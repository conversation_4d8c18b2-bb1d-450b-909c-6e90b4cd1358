import{u as E,h as K,G as g,H as Q,l as L,n as W,J as w,r as u,q as H,o as f,w as y,d as i,b as V,e as d,f as l,I as X,a as _,F as q,s as R,t as B,M as Y}from"./app-BAwPsakn.js";import{d as Z}from"./vuedraggable.umd-BRYqknf6.js";const x={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-2"},oe={class:"col-span-2 sm:col-span-1"},ae={class:"col-span-3"},se=["onClick"],te={key:0,class:"mt-4 grid grid-cols-4 gap-4"},le={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-1"},ne={key:0,class:"col-span-4 sm:col-span-1"},de={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},ue={class:"col-span-4 sm:col-span-1"},pe={class:"col-span-4 sm:col-span-1"},me={class:"col-span-4 sm:col-span-1"},ce={key:3,class:"col-span-4 sm:col-span-2"},fe={key:1,class:"mt-4 grid grid-cols-1 gap-4"},ye={class:"col"},Ve={class:"mt-4 space-x-2"},_e={class:"mt-4 grid grid-cols-1 gap-6"},$e={class:"col"},be={class:"col"},Ue={name:"formForm"},ge=Object.assign(Ue,{setup(C){const $=E();K();const c={name:"",dueDate:"",summary:"",description:"",studentAudienceType:"",employeeAudienceType:"",studentAudiences:[],employeeAudiences:[],fields:[],media:[],mediaUpdated:!1,mediaToken:g(),mediaHash:[]},A={uuid:g(),type:"",content:"",name:"",label:"",minLength:"",maxLength:"",minValue:"",maxValue:"",defaultValue:"",options:"",isRequired:!1},v="form/",t=Q(v),b=L({fieldTypes:[]}),n=L({...c}),h=L({studentAudiences:[],employeeAudiences:[],isLoaded:!$.params.uuid}),S=s=>{Object.assign(b,s)},j=()=>{n.mediaToken=g(),n.mediaHash=[]},D=()=>{n.fields.push({...A,uuid:g()})},I=()=>{n.fields.push({...A,uuid:g(),type:"paragraph"})},M=async s=>{await Y()&&(n.fields.length==1?n.fields=[A]:n.fields.splice(s,1))},O=s=>{var U,T,k;let a=s.audiences.filter(p=>p.type=="student").map(p=>p.uuid),m=s.audiences.filter(p=>p.type=="employee").map(p=>p.uuid),F=s.fields.map(p=>({...p,type:p.type.value}));Object.assign(c,{...s,dueDate:((U=s.dueDate)==null?void 0:U.value)||"",studentAudienceType:((T=s.studentAudienceType)==null?void 0:T.value)||"",employeeAudienceType:((k=s.employeeAudienceType)==null?void 0:k.value)||"",studentAudiences:a,employeeAudiences:m,fields:F}),Object.assign(n,w(c)),h.studentAudiences=a,h.employeeAudiences=m,h.isLoaded=!0};return W(async()=>{$.params.uuid||(D(),Object.assign(n,w(c)))}),(s,a)=>{const m=u("BaseInput"),F=u("DatePicker"),U=u("BaseTextarea"),T=u("AudienceInput"),k=u("BaseSelect"),p=u("BaseSwitch"),N=u("BaseEditor"),G=u("BaseFieldset"),P=u("BaseBadge"),J=u("MediaUpload"),z=u("FormAction");return f(),H(z,{"pre-requisites":!0,onSetPreRequisites:S,"init-url":v,"init-form":c,form:n,"set-form":O,redirect:"Form",onResetMediaFiles:j},{default:y(()=>[i("div",x,[i("div",ee,[d(m,{type:"text",modelValue:n.name,"onUpdate:modelValue":a[0]||(a[0]=e=>n.name=e),name:"name",label:s.$trans("form.props.name"),error:l(t).name,"onUpdate:error":a[1]||(a[1]=e=>l(t).name=e)},null,8,["modelValue","label","error"])]),i("div",oe,[d(F,{modelValue:n.dueDate,"onUpdate:modelValue":a[2]||(a[2]=e=>n.dueDate=e),name:"dueDate",label:s.$trans("form.props.due_date"),"no-clear":"",error:l(t).dueDate,"onUpdate:error":a[3]||(a[3]=e=>l(t).dueDate=e)},null,8,["modelValue","label","error"])]),i("div",ae,[d(U,{rows:1,modelValue:n.summary,"onUpdate:modelValue":a[4]||(a[4]=e=>n.summary=e),name:"summary",label:s.$trans("form.props.summary"),error:l(t).summary,"onUpdate:error":a[5]||(a[5]=e=>l(t).summary=e)},null,8,["modelValue","label","error"])])]),h.isLoaded?(f(),H(T,{key:0,"pre-requisites":b,studentAudienceType:n.studentAudienceType,"onUpdate:studentAudienceType":a[6]||(a[6]=e=>n.studentAudienceType=e),employeeAudienceType:n.employeeAudienceType,"onUpdate:employeeAudienceType":a[7]||(a[7]=e=>n.employeeAudienceType=e),studentAudiences:n.studentAudiences,"onUpdate:studentAudiences":a[8]||(a[8]=e=>n.studentAudiences=e),employeeAudiences:n.employeeAudiences,"onUpdate:employeeAudiences":a[9]||(a[9]=e=>n.employeeAudiences=e),formErrors:l(t),"onUpdate:formErrors":a[10]||(a[10]=e=>X(t)?t.value=e:null)},null,8,["pre-requisites","studentAudienceType","employeeAudienceType","studentAudiences","employeeAudiences","formErrors"])):V("",!0),d(l(Z),{list:n.fields,"item-key":"uuid",handle:".handle"},{item:y(({element:e,index:r})=>[d(G,{class:"mt-4"},{legend:y(()=>[a[16]||(a[16]=i("i",{class:"fas fa-arrows mr-2 cursor-pointer handle"},null,-1)),R(" "+B(s.$trans("form.props.field"))+" "+B(r+1)+". ",1),i("span",{class:"text-danger ml-2 cursor-pointer",onClick:o=>M(r)},a[15]||(a[15]=[i("i",{class:"fas fa-times-circle"},null,-1)]),8,se)]),default:y(()=>[!e.type||e.type!="paragraph"?(f(),_("div",te,[i("div",le,[d(k,{name:`fields.${r}.type`,label:s.$trans("global.select",{attribute:s.$trans("custom_field.props.type")}),modelValue:e.type,"onUpdate:modelValue":o=>e.type=o,error:l(t)[`fields.${r}.type`],"onUpdate:error":o=>l(t)[`fields.${r}.type`]=o,options:b.customFieldTypes},null,8,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","options"])]),i("div",re,[d(m,{type:"text",modelValue:e.label,"onUpdate:modelValue":o=>e.label=o,name:`fields.${r}.label`,label:s.$trans("custom_field.props.label"),error:l(t)[`fields.${r}.label`],"onUpdate:error":o=>l(t)[`fields.${r}.label`]=o},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),e.type=="camera_image"||e.type=="file_upload"?(f(),_("div",ne,[d(m,{type:"text",modelValue:e.name,"onUpdate:modelValue":o=>e.name=o,name:`fields.${r}.name`,label:s.$trans("custom_field.props.name"),error:l(t)[`fields.${r}.name`],"onUpdate:error":o=>l(t)[`fields.${r}.name`]=o},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])):V("",!0),i("div",de,[d(p,{vertical:"",name:`fields.${r}.isRequired`,modelValue:e.isRequired,"onUpdate:modelValue":o=>e.isRequired=o,label:s.$trans("custom_field.props.is_required"),error:l(t)[`fields.${r}.isRequired`],"onUpdate:error":o=>l(t)[`fields.${r}.isRequired`]=o},null,8,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error"])]),e.type=="text_input"||e.type=="multi_line_text_input"?(f(),_(q,{key:1},[i("div",ie,[d(m,{type:"text",modelValue:e.minLength,"onUpdate:modelValue":o=>e.minLength=o,name:`fields.${r}.minLength`,label:s.$trans("custom_field.props.min_length"),error:l(t)[`fields.${r}.minLength`],"onUpdate:error":o=>l(t)[`fields.${r}.minLength`]=o},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),i("div",ue,[d(m,{type:"text",modelValue:e.maxLength,"onUpdate:modelValue":o=>e.maxLength=o,name:`fields.${r}.maxLength`,label:s.$trans("custom_field.props.max_length"),error:l(t)[`fields.${r}.maxLength`],"onUpdate:error":o=>l(t)[`fields.${r}.maxLength`]=o},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])],64)):V("",!0),e.type=="number_input"||e.type=="currency_input"?(f(),_(q,{key:2},[i("div",pe,[d(m,{type:"number",modelValue:e.minValue,"onUpdate:modelValue":o=>e.minValue=o,name:`fields.${r}.minValue`,label:s.$trans("custom_field.props.min_value"),error:l(t)[`fields.${r}.minValue`],"onUpdate:error":o=>l(t)[`fields.${r}.minValue`]=o},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),i("div",me,[d(m,{type:"number",modelValue:e.maxValue,"onUpdate:modelValue":o=>e.maxValue=o,name:`fields.${r}.maxValue`,label:s.$trans("custom_field.props.max_value"),error:l(t)[`fields.${r}.maxValue`],"onUpdate:error":o=>l(t)[`fields.${r}.maxValue`]=o},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])],64)):V("",!0),e.type=="select_input"||e.type=="multi_select_input"||e.type=="checkbox_input"||e.type=="radio_input"?(f(),_("div",ce,[d(m,{type:"text",modelValue:e.options,"onUpdate:modelValue":o=>e.options=o,name:`fields.${r}.options`,label:s.$trans("custom_field.props.options"),error:l(t)[`fields.${r}.options`],"onUpdate:error":o=>l(t)[`fields.${r}.options`]=o,"label-hint":s.$trans("custom_field.option_info")},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","label-hint"])])):V("",!0)])):V("",!0),e.type=="paragraph"?(f(),_("div",fe,[i("div",ye,[d(N,{modelValue:e.content,"onUpdate:modelValue":o=>e.content=o,name:`fields.${r}.content`,edit:!!l($).params.uuid,placeholder:s.$trans("custom_field.props.content"),error:l(t)[`fields.${r}.content`],"onUpdate:error":o=>l(t)[`fields.${r}.content`]=o},null,8,["modelValue","onUpdate:modelValue","name","edit","placeholder","error","onUpdate:error"])])])):V("",!0)]),_:2},1024)]),_:1},8,["list"]),i("div",Ve,[d(P,{design:"primary",onClick:D,class:"cursor-pointer"},{default:y(()=>[R(B(s.$trans("global.add",{attribute:s.$trans("form.props.field")})),1)]),_:1}),d(P,{design:"primary",onClick:I,class:"cursor-pointer"},{default:y(()=>[R(B(s.$trans("global.add",{attribute:s.$trans("custom_field.types.paragraph")})),1)]),_:1})]),i("div",_e,[i("div",$e,[d(U,{rows:2,modelValue:n.description,"onUpdate:modelValue":a[11]||(a[11]=e=>n.description=e),name:"description",label:s.$trans("form.props.description"),error:l(t).description,"onUpdate:error":a[12]||(a[12]=e=>l(t).description=e)},null,8,["modelValue","label","error"])]),i("div",be,[d(J,{multiple:"",label:s.$trans("general.file"),module:"form",media:n.media,"media-token":n.mediaToken,onIsUpdated:a[13]||(a[13]=e=>n.mediaUpdated=!0),onSetHash:a[14]||(a[14]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),Ae={name:"FormAction"},Te=Object.assign(Ae,{setup(C){const $=E();return(c,A)=>{const v=u("PageHeaderAction"),t=u("PageHeader"),b=u("ParentTransition");return f(),_(q,null,[d(t,{title:c.$trans(l($).meta.trans,{attribute:c.$trans(l($).meta.label)}),navs:[{label:c.$trans("form.form"),path:"Form"}]},{default:y(()=>[d(v,{name:"Form",title:c.$trans("form.form"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(b,{appear:"",visibility:!0},{default:y(()=>[d(ge)]),_:1})],64)}}});export{Te as default};
