import{u as M,j as K,H as Q,g as p,l as P,n as W,J as X,r as d,a as k,o as m,q as I,b,e as l,f as r,w as L,d as s,s as T,t as z,I as Y,F as Z}from"./app-BAwPsakn.js";const x={class:"grid grid-cols-3 gap-6"},_={class:"col-span-3 sm:col-span-2"},ee={class:"flex"},ae={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Ue={key:0,class:"col-span-3 sm:col-span-1"},ve={key:1,class:"col-span-3 sm:col-span-1"},fe={name:"ContactEditBasic"},De=Object.assign(fe,{props:{guardian:{type:Object,default(){return{}}}},setup(N){const B=M(),F=K("emitter"),C=N,q={firstName:"",middleName:"",thirdName:"",lastName:"",firstName:"",middleName:"",gender:"",birthDate:"",anniversaryDate:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",occupation:"",annualIncome:"",organizationName:"",designation:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",maritalStatus:"",religion:"",category:"",caste:""},G="guardian/",n=Q(G),j=p("contact.uniqueIdNumber1Label"),E=p("contact.uniqueIdNumber2Label"),R=p("contact.uniqueIdNumber3Label"),O=p("contact.uniqueIdNumber4Label"),h=p("contact.uniqueIdNumber5Label"),w=p("contact.enableCategoryField"),H=p("contact.enableCasteField"),U=P({genders:[],bloodGroups:[],maritalStatuses:[]}),t=P({...q}),u=P({religion:"",category:"",caste:"",isLoaded:!B.params.uuid}),A=o=>{Object.assign(U,o)};W(async()=>{var e,v,g,f,i,$,y,V,c,D,S,a;let o=(e=C.guardian)==null?void 0:e.contact;Object.assign(q,{firstName:o.firstName,middleName:o.middleName,thirdName:o.thirdName,lastName:o.lastName,fatherName:o.fatherName,motherName:o.motherName,gender:(v=o.gender)==null?void 0:v.value,birthDate:(g=o.birthDate)==null?void 0:g.value,anniversaryDate:(f=o.anniversaryDate)==null?void 0:f.value,uniqueIdNumber1:o.uniqueIdNumber1,uniqueIdNumber2:o.uniqueIdNumber2,uniqueIdNumber3:o.uniqueIdNumber3,uniqueIdNumber4:o.uniqueIdNumber4,uniqueIdNumber5:o.uniqueIdNumber5,occupation:o.occupation,annualIncome:o.annualIncome,organizationName:o.organizationName,designation:o.designation,birthPlace:o.birthPlace,bloodGroup:((i=o.bloodGroup)==null?void 0:i.value)||"",maritalStatus:(($=o.maritalStatus)==null?void 0:$.value)||"",nationality:o.nationality,motherTongue:o.motherTongue,religion:((y=o.religion)==null?void 0:y.uuid)||"",category:((V=o.category)==null?void 0:V.uuid)||"",caste:((c=o.caste)==null?void 0:c.uuid)||""}),Object.assign(t,X(q)),u.religion=((D=o.religion)==null?void 0:D.name)||"",u.category=((S=o.category)==null?void 0:S.name)||"",u.caste=((a=o.caste)==null?void 0:a.name)||"",u.isLoaded=!0});const J=()=>{F.emit("guardianUpdated")};return(o,e)=>{const v=d("PageHeader"),g=d("BaseLabel"),f=d("NameInput"),i=d("BaseInput"),$=d("BaseRadioGroup"),y=d("DatePicker"),V=d("BaseSelect"),c=d("BaseSelectSearch"),D=d("FormAction"),S=d("ParentTransition");return m(),k(Z,null,[N.guardian.uuid?(m(),I(v,{key:0,title:o.$trans(r(B).meta.trans,{attribute:o.$trans(r(B).meta.label)}),navs:[{label:o.$trans("guardian.guardian"),path:"Guardian"},{label:N.guardian.contact.name,path:{name:"GuardianShow",params:{uuid:N.guardian.uuid}}}]},null,8,["title","navs"])):b("",!0),l(S,{appear:"",visibility:!0},{default:L(()=>[N.guardian.uuid?(m(),I(D,{key:0,"pre-requisites":!0,onSetPreRequisites:A,"init-url":G,"no-data-fetch":"","init-form":q,form:t,"stay-on":"","after-submit":J,redirect:{name:"GuardianShowBasic",params:{uuid:N.guardian.uuid}}},{default:L(()=>[s("div",x,[s("div",_,[l(g,null,{default:L(()=>[T(z(o.$trans("contact.props.name")),1)]),_:1}),s("div",ee,[l(f,{firstName:t.firstName,"onUpdate:firstName":e[0]||(e[0]=a=>t.firstName=a),middleName:t.middleName,"onUpdate:middleName":e[1]||(e[1]=a=>t.middleName=a),thirdName:t.thirdName,"onUpdate:thirdName":e[2]||(e[2]=a=>t.thirdName=a),lastName:t.lastName,"onUpdate:lastName":e[3]||(e[3]=a=>t.lastName=a),formErrors:r(n),"onUpdate:formErrors":e[4]||(e[4]=a=>Y(n)?n.value=a:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),s("div",ae,[l(i,{type:"text",modelValue:t.fatherName,"onUpdate:modelValue":e[5]||(e[5]=a=>t.fatherName=a),name:"fatherName",label:o.$trans("contact.props.father_name"),error:r(n).fatherName,"onUpdate:error":e[6]||(e[6]=a=>r(n).fatherName=a)},null,8,["modelValue","label","error"])]),s("div",oe,[l(i,{type:"text",modelValue:t.motherName,"onUpdate:modelValue":e[7]||(e[7]=a=>t.motherName=a),name:"motherName",label:o.$trans("contact.props.mother_name"),error:r(n).motherName,"onUpdate:error":e[8]||(e[8]=a=>r(n).motherName=a)},null,8,["modelValue","label","error"])]),s("div",te,[l(g,null,{default:L(()=>[T(z(o.$trans("contact.props.gender")),1)]),_:1}),l($,{"top-margin":"",options:U.genders,name:"gender",modelValue:t.gender,"onUpdate:modelValue":e[9]||(e[9]=a=>t.gender=a),error:r(n).gender,"onUpdate:error":e[10]||(e[10]=a=>r(n).gender=a),horizontal:""},null,8,["options","modelValue","error"])]),s("div",re,[l(y,{modelValue:t.birthDate,"onUpdate:modelValue":e[11]||(e[11]=a=>t.birthDate=a),name:"birthDate",label:o.$trans("contact.props.birth_date"),"no-clear":"",error:r(n).birthDate,"onUpdate:error":e[12]||(e[12]=a=>r(n).birthDate=a)},null,8,["modelValue","label","error"])]),s("div",ne,[l(y,{modelValue:t.anniversaryDate,"onUpdate:modelValue":e[13]||(e[13]=a=>t.anniversaryDate=a),name:"anniversaryDate",label:o.$trans("contact.props.anniversary_date"),"no-clear":"",error:r(n).anniversaryDate,"onUpdate:error":e[14]||(e[14]=a=>r(n).anniversaryDate=a)},null,8,["modelValue","label","error"])]),s("div",le,[l(i,{type:"text",modelValue:t.uniqueIdNumber1,"onUpdate:modelValue":e[15]||(e[15]=a=>t.uniqueIdNumber1=a),name:"uniqueIdNumber1",label:r(j),error:r(n).uniqueIdNumber1,"onUpdate:error":e[16]||(e[16]=a=>r(n).uniqueIdNumber1=a)},null,8,["modelValue","label","error"])]),s("div",se,[l(i,{type:"text",modelValue:t.uniqueIdNumber2,"onUpdate:modelValue":e[17]||(e[17]=a=>t.uniqueIdNumber2=a),name:"uniqueIdNumber2",label:r(E),error:r(n).uniqueIdNumber2,"onUpdate:error":e[18]||(e[18]=a=>r(n).uniqueIdNumber2=a)},null,8,["modelValue","label","error"])]),s("div",ie,[l(i,{type:"text",modelValue:t.uniqueIdNumber3,"onUpdate:modelValue":e[19]||(e[19]=a=>t.uniqueIdNumber3=a),name:"uniqueIdNumber3",label:r(R),error:r(n).uniqueIdNumber3,"onUpdate:error":e[20]||(e[20]=a=>r(n).uniqueIdNumber3=a)},null,8,["modelValue","label","error"])]),s("div",ue,[l(i,{type:"text",modelValue:t.uniqueIdNumber4,"onUpdate:modelValue":e[21]||(e[21]=a=>t.uniqueIdNumber4=a),name:"uniqueIdNumber4",label:r(O),error:r(n).uniqueIdNumber4,"onUpdate:error":e[22]||(e[22]=a=>r(n).uniqueIdNumber4=a)},null,8,["modelValue","label","error"])]),s("div",de,[l(i,{type:"text",modelValue:t.uniqueIdNumber5,"onUpdate:modelValue":e[23]||(e[23]=a=>t.uniqueIdNumber5=a),name:"uniqueIdNumber5",label:r(h),error:r(n).uniqueIdNumber5,"onUpdate:error":e[24]||(e[24]=a=>r(n).uniqueIdNumber5=a)},null,8,["modelValue","label","error"])]),s("div",me,[l(i,{type:"text",modelValue:t.occupation,"onUpdate:modelValue":e[25]||(e[25]=a=>t.occupation=a),name:"occupation",label:o.$trans("contact.props.occupation"),error:r(n).occupation,"onUpdate:error":e[26]||(e[26]=a=>r(n).occupation=a)},null,8,["modelValue","label","error"])]),s("div",pe,[l(i,{type:"text",modelValue:t.annualIncome,"onUpdate:modelValue":e[27]||(e[27]=a=>t.annualIncome=a),name:"annualIncome",label:o.$trans("contact.props.annual_income"),error:r(n).annualIncome,"onUpdate:error":e[28]||(e[28]=a=>r(n).annualIncome=a)},null,8,["modelValue","label","error"])]),s("div",be,[l(i,{type:"text",modelValue:t.organizationName,"onUpdate:modelValue":e[29]||(e[29]=a=>t.organizationName=a),name:"organizationName",label:o.$trans("contact.props.organization_name"),error:r(n).organizationName,"onUpdate:error":e[30]||(e[30]=a=>r(n).organizationName=a)},null,8,["modelValue","label","error"])]),s("div",Ne,[l(i,{type:"text",modelValue:t.designation,"onUpdate:modelValue":e[31]||(e[31]=a=>t.designation=a),name:"designation",label:o.$trans("contact.props.designation"),error:r(n).designation,"onUpdate:error":e[32]||(e[32]=a=>r(n).designation=a)},null,8,["modelValue","label","error"])]),s("div",ce,[l(i,{type:"text",modelValue:t.birthPlace,"onUpdate:modelValue":e[33]||(e[33]=a=>t.birthPlace=a),name:"birthPlace",label:o.$trans("contact.props.birth_place"),error:r(n).birthPlace,"onUpdate:error":e[34]||(e[34]=a=>r(n).birthPlace=a)},null,8,["modelValue","label","error"])]),s("div",ge,[l(i,{type:"text",modelValue:t.nationality,"onUpdate:modelValue":e[35]||(e[35]=a=>t.nationality=a),name:"nationality",label:o.$trans("contact.props.nationality"),error:r(n).nationality,"onUpdate:error":e[36]||(e[36]=a=>r(n).nationality=a)},null,8,["modelValue","label","error"])]),s("div",ye,[l(i,{type:"text",modelValue:t.motherTongue,"onUpdate:modelValue":e[37]||(e[37]=a=>t.motherTongue=a),name:"motherTongue",label:o.$trans("contact.props.mother_tongue"),error:r(n).motherTongue,"onUpdate:error":e[38]||(e[38]=a=>r(n).motherTongue=a)},null,8,["modelValue","label","error"])]),s("div",Ve,[l(V,{modelValue:t.bloodGroup,"onUpdate:modelValue":e[39]||(e[39]=a=>t.bloodGroup=a),name:"bloodGroup",label:o.$trans("contact.props.blood_group"),options:U.bloodGroups,error:r(n).bloodGroup,"onUpdate:error":e[40]||(e[40]=a=>r(n).bloodGroup=a)},null,8,["modelValue","label","options","error"])]),s("div",Ie,[l(V,{modelValue:t.maritalStatus,"onUpdate:modelValue":e[41]||(e[41]=a=>t.maritalStatus=a),name:"maritalStatus",label:o.$trans("contact.props.marital_status"),options:U.maritalStatuses,error:r(n).maritalStatus,"onUpdate:error":e[42]||(e[42]=a=>r(n).maritalStatus=a)},null,8,["modelValue","label","options","error"])]),s("div",qe,[u.isLoaded?(m(),I(c,{key:0,name:"religion",label:o.$trans("global.select",{attribute:o.$trans("contact.religion.religion")}),modelValue:t.religion,"onUpdate:modelValue":e[43]||(e[43]=a=>t.religion=a),error:r(n).religion,"onUpdate:error":e[44]||(e[44]=a=>r(n).religion=a),"label-prop":"name","value-prop":"uuid","init-search":u.religion,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"religion"}},null,8,["label","modelValue","error","init-search"])):b("",!0)]),r(w)?(m(),k("div",Ue,[u.isLoaded?(m(),I(c,{key:0,name:"category",label:o.$trans("global.select",{attribute:o.$trans("contact.category.category")}),modelValue:t.category,"onUpdate:modelValue":e[45]||(e[45]=a=>t.category=a),error:r(n).category,"onUpdate:error":e[46]||(e[46]=a=>r(n).category=a),"label-prop":"name","value-prop":"uuid","init-search":u.category,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"member_category"}},null,8,["label","modelValue","error","init-search"])):b("",!0)])):b("",!0),r(H)?(m(),k("div",ve,[u.isLoaded?(m(),I(c,{key:0,name:"caste",label:o.$trans("global.select",{attribute:o.$trans("contact.caste.caste")}),modelValue:t.caste,"onUpdate:modelValue":e[47]||(e[47]=a=>t.caste=a),error:r(n).caste,"onUpdate:error":e[48]||(e[48]=a=>r(n).caste=a),"label-prop":"name","value-prop":"uuid","init-search":u.caste,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"member_caste"}},null,8,["label","modelValue","error","init-search"])):b("",!0)])):b("",!0)])]),_:1},8,["form","redirect"])):b("",!0)]),_:1})],64)}}});export{De as default};
