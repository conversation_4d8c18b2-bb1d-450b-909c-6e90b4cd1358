import{u as w,G as q,H as z,l as I,n as K,r as d,q as T,o as $,w as c,d as i,a as H,b as Q,e as l,f as r,F as C,v as W,s as S,t as p,M as X,J as Y}from"./app-BAwPsakn.js";const Z={class:"grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-2 sm:col-span-1"},ne=["onClick"],oe={class:"mt-4 grid grid-cols-4 gap-4"},se={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-2"},ie={class:"mt-4"},le={class:"mt-4 grid grid-cols-1 gap-4"},de={class:"col"},me={class:"col"},ue={name:"InventoryStockAdjustmentForm"},ce=Object.assign(ue,{setup(O){const y=w(),u={date:"",place:"",items:[],description:"",media:[],mediaUpdated:!1,mediaToken:q(),mediaHash:[]},V={uuid:q(),item:"",quantity:1,description:""},U="inventory/stockAdjustment/",a=z(U),_=I({inventories:[],places:[]}),A=I({isLoaded:!y.params.uuid}),n=I({...u}),R=t=>{Object.assign(_,t)},M=()=>{n.mediaToken=q(),n.mediaHash=[]},P=()=>{n.items.push({...V,uuid:q()})},D=async t=>{await X()&&(n.items.length==1?n.items=[V]:n.items.splice(t,1))},N=t=>{var k,j,B,h,b;let o=[];t.items.forEach(v=>{o.push({uuid:v.uuid,item:v.item,quantity:v.quantity,description:v.description})}),Object.assign(u,{...t,items:o,date:(k=t.date)==null?void 0:k.value,inventory:((j=t.inventory)==null?void 0:j.uuid)||"",place:((B=t.place)==null?void 0:B.uuid)||""}),Object.assign(n,Y(u)),A.inventory=((h=t.inventory)==null?void 0:h.uuid)||"",A.place=((b=t.place)==null?void 0:b.uuid)||"",A.isLoaded=!0},E=t=>{t!=n.inventory&&(n.items=[V])};return K(async()=>{y.params.uuid||P()}),(t,o)=>{const k=d("BaseSelect"),j=d("DatePicker"),B=d("BaseSelectSearch"),h=d("BaseInput"),b=d("BaseTextarea"),v=d("BaseFieldset"),L=d("BaseBadge"),G=d("MediaUpload"),J=d("FormAction");return $(),T(J,{"pre-requisites":!0,onSetPreRequisites:R,"init-url":U,"init-form":u,form:n,"set-form":N,redirect:"InventoryStockAdjustment",onResetMediaFiles:M},{default:c(()=>[i("div",Z,[i("div",x,[l(k,{modelValue:n.inventory,"onUpdate:modelValue":o[0]||(o[0]=e=>n.inventory=e),name:"inventory",label:t.$trans("inventory.inventory"),options:_.inventories,"label-prop":"name","value-prop":"uuid",error:r(a).inventory,"onUpdate:error":o[1]||(o[1]=e=>r(a).inventory=e),onChange:E},null,8,["modelValue","label","options","error"])]),i("div",ee,[l(k,{modelValue:n.place,"onUpdate:modelValue":o[2]||(o[2]=e=>n.place=e),name:"place",label:t.$trans("inventory.place"),options:_.places,"label-prop":"fullName","value-prop":"uuid",error:r(a).place,"onUpdate:error":o[3]||(o[3]=e=>r(a).place=e)},null,8,["modelValue","label","options","error"])]),i("div",te,[l(j,{modelValue:n.date,"onUpdate:modelValue":o[4]||(o[4]=e=>n.date=e),name:"date",label:t.$trans("inventory.stock_adjustment.props.date"),"no-clear":"",error:r(a).date,"onUpdate:error":o[5]||(o[5]=e=>r(a).date=e)},null,8,["modelValue","label","error"])])]),n.inventory?($(),H(C,{key:0},[($(!0),H(C,null,W(n.items,(e,m)=>($(),T(v,{class:"mt-4",key:e.uuid},{legend:c(()=>[S(p(t.$trans("inventory.item"))+" "+p(m+1)+". ",1),i("span",{class:"text-danger ml-2 cursor-pointer",onClick:F=>D(m)},o[10]||(o[10]=[i("i",{class:"fas fa-times-circle"},null,-1)]),8,ne)]),default:c(()=>{var F;return[i("div",oe,[i("div",se,[l(B,{name:`items.${m}.item`,label:t.$trans("global.select",{attribute:t.$trans("inventory.stock_item.stock_item")}),modelValue:e.item,"onUpdate:modelValue":s=>e.item=s,error:r(a)[`items.${m}.item`],"onUpdate:error":s=>r(a)[`items.${m}.item`]=s,"value-prop":"uuid","object-prop":!0,"init-search":(F=e==null?void 0:e.item)==null?void 0:F.name,"init-search-key":"name","search-action":"inventory/stockItem/list","additional-search-query":{inventory:n.inventory}},{selectedOption:c(s=>{var g,f;return[S(p(s.value.name)+" "+p((f=(g=s.value)==null?void 0:g.category)==null?void 0:f.name),1)]}),listOption:c(s=>{var g,f;return[S(p(s.option.name)+" "+p((f=(g=s.option)==null?void 0:g.category)==null?void 0:f.name),1)]}),_:2},1032,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","init-search","additional-search-query"])]),i("div",ae,[l(h,{type:"number",step:.01,modelValue:e.quantity,"onUpdate:modelValue":s=>e.quantity=s,name:`items.${m}.quantity`,label:t.$trans("inventory.stock_adjustment.props.quantity"),error:r(a)[`items.${m}.quantity`],"onUpdate:error":s=>r(a)[`items.${m}.quantity`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),i("div",re,[l(b,{rows:1,modelValue:e.description,"onUpdate:modelValue":s=>e.description=s,name:`items.${m}.description`,label:t.$trans("inventory.stock_adjustment.props.description"),error:r(a)[`items.${m}.description`],"onUpdate:error":s=>r(a)[`items.${m}.description`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]}),_:2},1024))),128)),i("div",ie,[l(L,{design:"primary",onClick:P,class:"cursor-pointer"},{default:c(()=>[S(p(t.$trans("global.add",{attribute:t.$trans("inventory.stock_item.stock_item")})),1)]),_:1})])],64)):Q("",!0),i("div",le,[i("div",de,[l(b,{rows:1,modelValue:n.description,"onUpdate:modelValue":o[6]||(o[6]=e=>n.description=e),name:"description",label:t.$trans("inventory.stock_adjustment.props.description"),error:r(a).description,"onUpdate:error":o[7]||(o[7]=e=>r(a).description=e)},null,8,["modelValue","label","error"])]),i("div",me,[l(G,{multiple:"",label:t.$trans("general.file"),module:"stock_adjustment",media:n.media,"media-token":n.mediaToken,onIsUpdated:o[8]||(o[8]=e=>n.mediaUpdated=!0),onSetHash:o[9]||(o[9]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),pe={name:"InventoryStockAdjustmentAction"},ye=Object.assign(pe,{setup(O){const y=w();return(u,V)=>{const U=d("PageHeaderAction"),a=d("PageHeader"),_=d("ParentTransition");return $(),H(C,null,[l(a,{title:u.$trans(r(y).meta.trans,{attribute:u.$trans(r(y).meta.label)}),navs:[{label:u.$trans("inventory.inventory"),path:"Inventory"},{label:u.$trans("inventory.stock_adjustment.stock_adjustment"),path:"InventoryStockAdjustmentList"}]},{default:c(()=>[l(U,{name:"InventoryStockAdjustment",title:u.$trans("inventory.stock_adjustment.stock_adjustment"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(_,{appear:"",visibility:!0},{default:c(()=>[l(ce)]),_:1})],64)}}});export{ye as default};
