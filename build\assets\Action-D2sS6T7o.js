import{H as S,m as j,l as f,r as l,a as y,o as $,e as n,w as c,d as i,f as r,s as V,t as v,F as U,J as C,u as O}from"./app-BAwPsakn.js";import{_ as q}from"./Form-BYmZZDvP.js";const k={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3"},G={name:"AcademicProgramForm"},K=Object.assign(G,{setup(P){const d={type:"",department:"",name:"",code:"",shortcode:"",alias:"",enableRegistration:!1,description:""},p="academic/program/",s=S(p),m=j(!1),u=f({departments:[],types:[]}),t=f({...d}),_=o=>{Object.assign(u,o)},B=o=>{var e,g;Object.assign(d,{...o,department:(e=o.department)==null?void 0:e.uuid,type:(g=o.type)==null?void 0:g.uuid}),Object.assign(t,C(d))},A=o=>{u.types.push(o.programType),m.value=!1};return(o,e)=>{const g=l("BaseSelect"),R=l("HelperText"),b=l("BaseInput"),T=l("BaseSwitch"),F=l("BaseTextarea"),w=l("FormAction"),H=l("BaseModal");return $(),y(U,null,[n(w,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:_,"init-url":p,"init-form":d,form:t,"set-form":B,redirect:"AcademicProgram"},{default:c(()=>[i("div",k,[i("div",E,[n(g,{modelValue:t.type,"onUpdate:modelValue":e[0]||(e[0]=a=>t.type=a),name:"type",label:o.$trans("academic.program.props.type"),"label-prop":"name","value-prop":"uuid",options:u.types,error:r(s).type,"onUpdate:error":e[1]||(e[1]=a=>r(s).type=a)},null,8,["modelValue","label","options","error"]),n(R,{cursor:"","text-right":"",onClick:e[2]||(e[2]=a=>m.value=!0)},{default:c(()=>[V(v(o.$trans("global.add",{attribute:o.$trans("academic.program_type.program_type")})),1)]),_:1})]),i("div",N,[n(g,{modelValue:t.department,"onUpdate:modelValue":e[3]||(e[3]=a=>t.department=a),name:"department",label:o.$trans("academic.department.department"),"label-prop":"name","value-prop":"uuid",options:u.departments,error:r(s).department,"onUpdate:error":e[4]||(e[4]=a=>r(s).department=a)},null,8,["modelValue","label","options","error"])]),i("div",D,[n(b,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[5]||(e[5]=a=>t.name=a),name:"name",label:o.$trans("academic.program.props.name"),error:r(s).name,"onUpdate:error":e[6]||(e[6]=a=>r(s).name=a)},null,8,["modelValue","label","error"])]),i("div",I,[n(b,{type:"text",modelValue:t.code,"onUpdate:modelValue":e[7]||(e[7]=a=>t.code=a),name:"code",label:o.$trans("academic.program.props.code"),error:r(s).code,"onUpdate:error":e[8]||(e[8]=a=>r(s).code=a)},null,8,["modelValue","label","error"])]),i("div",M,[n(b,{type:"text",modelValue:t.shortcode,"onUpdate:modelValue":e[9]||(e[9]=a=>t.shortcode=a),name:"shortcode",label:o.$trans("academic.program.props.shortcode"),error:r(s).shortcode,"onUpdate:error":e[10]||(e[10]=a=>r(s).shortcode=a)},null,8,["modelValue","label","error"])]),i("div",z,[n(b,{type:"text",modelValue:t.alias,"onUpdate:modelValue":e[11]||(e[11]=a=>t.alias=a),name:"alias",label:o.$trans("academic.program.props.alias"),error:r(s).alias,"onUpdate:error":e[12]||(e[12]=a=>r(s).alias=a)},null,8,["modelValue","label","error"])]),i("div",J,[n(T,{vertical:"",modelValue:t.enableRegistration,"onUpdate:modelValue":e[13]||(e[13]=a=>t.enableRegistration=a),name:"enableRegistration",label:o.$trans("global.enable",{attribute:o.$trans("student.registration.registration")}),error:r(s).enableRegistration,"onUpdate:error":e[14]||(e[14]=a=>r(s).enableRegistration=a)},null,8,["modelValue","label","error"])]),i("div",L,[n(F,{modelValue:t.description,"onUpdate:modelValue":e[15]||(e[15]=a=>t.description=a),name:"description",label:o.$trans("academic.program.props.description"),error:r(s).description,"onUpdate:error":e[16]||(e[16]=a=>r(s).description=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"]),n(H,{show:m.value,onClose:e[18]||(e[18]=a=>m.value=!1)},{title:c(()=>[V(v(o.$trans("global.add",{attribute:o.$trans("academic.program_type.program_type")})),1)]),default:c(()=>[n(q,{"is-modal":!0,"after-submit":A,onCancelled:e[17]||(e[17]=a=>m.value=!1)})]),_:1},8,["show"])],64)}}}),Q={name:"AcademicProgramAction"},Y=Object.assign(Q,{setup(P){const d=O();return(p,s)=>{const m=l("PageHeaderAction"),u=l("PageHeader"),t=l("ParentTransition");return $(),y(U,null,[n(u,{title:p.$trans(r(d).meta.trans,{attribute:p.$trans(r(d).meta.label)}),navs:[{label:p.$trans("academic.academic"),path:"Academic"},{label:p.$trans("academic.program.program"),path:"AcademicProgramList"}]},{default:c(()=>[n(m,{name:"AcademicProgram",title:p.$trans("academic.program.program"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(t,{appear:"",visibility:!0},{default:c(()=>[n(K)]),_:1})],64)}}});export{Y as default};
