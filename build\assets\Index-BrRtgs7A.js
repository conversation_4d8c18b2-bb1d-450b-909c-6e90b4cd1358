import{u as z,h as R,i as T,l as M,n as G,r as o,q as _,o as u,w as e,d as j,e as n,s as i,t as l,j as J,m as D,a as F,f as I,F as P,v as K,b as C}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["refresh","hide"],setup(V,{emit:k}){const c=z(),B=R();T();const m=k,d={name:""},r=M({...d}),f=()=>{Object.assign(r,d),b(),m("hide")};G(async()=>{Object.assign(r,{search:c.query.search})});const b=async()=>{await <PERSON><PERSON>push({name:c.name,query:{...c.query,...r}}),m("refresh")};return(p,g)=>{const t=o("BaseInput"),s=o("BaseButton"),y=o("BaseCard");return u(),_(y,null,{footer:e(()=>[n(s,{design:"error",class:"mr-4",onClick:f},{default:e(()=>[i(l(p.$trans("general.cancel")),1)]),_:1}),n(s,{onClick:b},{default:e(()=>[i(l(p.$trans("general.filter")),1)]),_:1})]),default:e(()=>[j("div",Q,[j("div",W,[n(t,{type:"text",modelValue:r.name,"onUpdate:modelValue":g[0]||(g[0]=v=>r.name=v),name:"name",label:p.$trans("config.push_notification.template.props.name")},null,8,["modelValue","label"])])])]),_:1})}}},Y={key:0,class:"mb-4"},Z={name:"ConfigPushNotificationTemplateList"},x=Object.assign(Z,{setup(V){const k=R();T();const c=J("emitter"),B="config/pushNotificationTemplate/",m=D(!1),d=D(!1),r=M({}),f=M({selectedTemplate:null}),b=t=>{Object.assign(r,t)},p=t=>{f.selectedTemplate=t,d.value=!0},g=async t=>{c.emit("actionItem",{uuid:t.uuid,action:"toggleStatus",confirmation:!0})};return(t,s)=>{const y=o("PageHeaderAction"),v=o("ParentTransition"),q=o("BaseBadge"),h=o("DataCell"),A=o("TextMuted"),$=o("FloatingMenuItem"),L=o("FloatingMenu"),O=o("DataRow"),S=o("DataTable"),H=o("ListItem"),U=o("ConfigPage"),E=o("BaseModal");return u(),F(P,null,[n(U,{"no-background":""},{action:e(()=>[n(y,{name:"ConfigPushNotificationTemplate",title:t.$trans("config.push_notification.template.template"),actions:["filter"],onToggleFilter:s[0]||(s[0]=a=>m.value=!m.value)},null,8,["title"])]),default:e(()=>[n(H,{class:"sm:-mt-4","init-url":B,onSetItems:b},{filter:e(()=>[n(v,{appear:"",visibility:m.value},{default:e(()=>[n(X,{onRefresh:s[1]||(s[1]=a=>I(c).emit("listItems")),onHide:s[2]||(s[2]=a=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(v,{appear:"",visibility:!0},{default:e(()=>[n(S,{header:r.headers,meta:r.meta,module:"config.push_notification.template",onRefresh:s[3]||(s[3]=a=>I(c).emit("listItems"))},{actionButton:e(()=>s[5]||(s[5]=[])),default:e(()=>[(u(!0),F(P,null,K(r.data,a=>(u(),_(O,{key:a.uuid},{default:e(()=>[n(h,{name:"name"},{default:e(()=>[i(l(a.name)+" ",1),a.enabledAt.value?C("",!0):(u(),_(q,{key:0,design:"danger"},{default:e(()=>[i(l(t.$trans("config.template.statuses.disabled")),1)]),_:1}))]),_:2},1024),n(h,{name:"subject"},{default:e(()=>[i(l(a.subject)+" ",1),n(A,{block:""},{default:e(()=>[i(l(a.content),1)]),_:2},1024)]),_:2},1024),n(h,{name:"action"},{default:e(()=>[n(L,null,{default:e(()=>[a.enabledAt.value?(u(),_($,{key:0,icon:"far fa-times-circle",onClick:w=>g(a)},{default:e(()=>[i(l(t.$trans("global.disable",{attribute:t.$trans("config.template.template")})),1)]),_:2},1032,["onClick"])):C("",!0),a.enabledAt.value?C("",!0):(u(),_($,{key:1,icon:"far fa-check-circle",onClick:w=>g(a)},{default:e(()=>[i(l(t.$trans("global.enable",{attribute:t.$trans("config.template.template")})),1)]),_:2},1032,["onClick"])),n($,{icon:"fas fa-arrow-circle-right",onClick:w=>p(a)},{default:e(()=>[i(l(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),n($,{icon:"fas fa-edit",onClick:w=>I(k).push({name:"ConfigPushNotificationTemplateEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(l(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})]),_:1}),n(E,{show:d.value,onClose:s[4]||(s[4]=a=>d.value=!1)},{title:e(()=>[i(l(t.$trans("global.detail",{attribute:t.$trans("config.sms.template.template")})),1)]),default:e(()=>[f.selectedTemplate?(u(),F("div",Y,l(f.selectedTemplate.content),1)):C("",!0)]),_:1},8,["show"])],64)}}});export{x as default};
