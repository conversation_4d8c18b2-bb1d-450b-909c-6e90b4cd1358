import{i as W,u as q,h as U,j as G,c as S,l as J,r as i,a as f,o as d,e as t,w as e,f as n,q as b,b as g,d as p,s as u,t as a,F as v,v as k,x as C,y as K}from"./app-BAwPsakn.js";const M={class:"space-y-4"},Q={key:0,class:"px-4 py-2"},X={class:"text-success font-semibold"},Y={class:"text-danger font-semibold"},Z={class:"text-success text-lg font-semibold"},ee={class:"text-danger text-lg font-semibold"},te={class:"text-xl font-semibold"},ae={class:"text-success text-xl font-semibold"},le={class:"text-success"},oe={class:"text-success"},ne={class:"text-success font-semibold"},se={class:"text-xl font-semibold"},re={class:"text-danger text-xl font-semibold"},ue={key:2,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},ie={name:"EmployeePayrollSalaryStructureShow"},ye=Object.assign(ie,{setup(pe){W();const h=q(),x=U(),o=G("$trans"),B={},P="employee/payroll/salaryStructure/",w=[{key:"payHead",label:o("employee.payroll.pay_head.pay_head"),visibility:!0},{key:"payHeadCode",label:o("employee.payroll.pay_head.props.code"),visibility:!0},{key:"category",label:o("employee.payroll.salary_template.props.category"),visibility:!0},{key:"computation",label:o("employee.payroll.salary_template.props.computation"),visibility:!0},{key:"earning",label:o("employee.payroll.pay_head.categories.earning"),visibility:!0},{key:"deduction",label:o("employee.payroll.pay_head.categories.deduction"),visibility:!0}],E=S(()=>s.records.filter(c=>c.payHead.category.value!=="employer_contribution")),V=S(()=>s.records.filter(c=>c.payHead.category.value==="employer_contribution")),s=J({...B}),D=c=>{Object.assign(s,c)};return(c,m)=>{const L=i("PageHeaderAction"),R=i("PageHeader"),y=i("ListItemView"),A=i("ListContainerVertical"),H=i("BaseCard"),I=i("BaseAlert"),r=i("DataCell"),_=i("DataRow"),N=i("SimpleTable"),T=i("BaseDataView"),j=i("BaseButton"),$=i("ShowButton"),z=i("DetailLayoutVertical"),F=i("ShowItem"),O=i("ParentTransition");return d(),f(v,null,[t(R,{title:n(o)(n(h).meta.trans,{attribute:n(o)(n(h).meta.label)}),navs:[{label:n(o)("employee.employee"),path:"Employee"},{label:n(o)("employee.payroll.payroll"),path:"EmployeePayroll"},{label:n(o)("employee.payroll.salary_structure.salary_structure"),path:"EmployeePayrollSalaryStructureList"}]},{default:e(()=>[t(L,{name:"EmployeePayrollSalaryStructure",title:n(o)("employee.payroll.salary_structure.salary_structure"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(O,{appear:"",visibility:!0},{default:e(()=>[t(F,{"init-url":P,uuid:n(h).params.uuid,onSetItem:D,onRedirectTo:m[1]||(m[1]=l=>n(x).push({name:"EmployeePayrollSalaryStructure"}))},{default:e(()=>[s.uuid?(d(),b(z,{key:0},{detail:e(()=>[t(H,{"no-padding":"","no-content-padding":""},{title:e(()=>[u(a(n(o)("global.detail",{attribute:n(o)("employee.payroll.salary_structure.salary_structure")})),1)]),default:e(()=>[t(A,null,{default:e(()=>[t(y,{label:n(o)("employee.employee")},{default:e(()=>[u(a(s.employee.name),1)]),_:1},8,["label"]),t(y,{label:n(o)("employee.props.code_number")},{default:e(()=>[u(a(s.employee.codeNumber),1)]),_:1},8,["label"]),t(y,{label:n(o)("employee.department.department")},{default:e(()=>[u(a(s.employee.department),1)]),_:1},8,["label"]),t(y,{label:n(o)("employee.designation.designation")},{default:e(()=>[u(a(s.employee.designation),1)]),_:1},8,["label"]),t(y,{label:n(o)("employee.employment_status.employment_status")},{default:e(()=>[u(a(s.employee.employmentStatus),1)]),_:1},8,["label"]),t(y,{label:n(o)("general.created_at")},{default:e(()=>[u(a(s.createdAt.formatted),1)]),_:1},8,["label"]),t(y,{label:n(o)("general.updated_at")},{default:e(()=>[u(a(s.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[p("div",M,[t(H,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[u(a(n(o)("employee.payroll.salary_structure.salary_structure"))+" ("+a(s.template.name)+") ",1)]),footer:e(()=>[t($,null,{default:e(()=>[n(K)("salary-structure:edit")?(d(),b(j,{key:0,design:"primary",onClick:m[0]||(m[0]=l=>n(x).push({name:"EmployeePayrollSalaryStructureEdit",params:{uuid:s.uuid}}))},{default:e(()=>[u(a(n(o)("general.edit")),1)]),_:1})):g("",!0)]),_:1})]),default:e(()=>[s.template.hasHourlyPayroll?(d(),f("div",Q,[t(I,{design:"info",size:"xs"},{default:e(()=>[u(a(n(o)("employee.payroll.salary_structure.hourly_payroll_info",{attribute:s.hourlyPay.formatted})),1)]),_:1})])):g("",!0),s.records.length>0?(d(),b(N,{key:1,header:w},{default:e(()=>[(d(!0),f(v,null,k(E.value,l=>(d(),b(_,{key:l.uuid},{default:e(()=>[t(r,{name:"payHead"},{default:e(()=>[p("span",{class:C({"text-success":l.payHead.category.value=="earning"||l.payHead.category.value=="employer_contribution","text-danger":l.payHead.category.value=="deduction"||l.payHead.category.value=="employee_contribution"})},a(l.payHead.name),3)]),_:2},1024),t(r,{name:"payHeadCode"},{default:e(()=>[p("span",{class:C({"text-success":l.payHead.category.value=="earning"||l.payHead.category.value=="employer_contribution","text-danger":l.payHead.category.value=="deduction"||l.payHead.category.value=="employee_contribution"})},a(l.payHead.code),3)]),_:2},1024),t(r,{name:"type"},{default:e(()=>[u(a(l.type.label),1)]),_:2},1024),t(r,{name:"computation"},{default:e(()=>[u(a(l.computation||"-"),1)]),_:2},1024),t(r,{name:"earning"},{default:e(()=>[p("span",X,a(l.payHead.category.value=="earning"||l.payHead.category.value=="employer_contribution"?l.amount.formatted:""),1)]),_:2},1024),t(r,{name:"deduction"},{default:e(()=>[p("span",Y,a(l.payHead.category.value=="deduction"||l.payHead.category.value=="employee_contribution"?l.amount.formatted:""),1)]),_:2},1024)]),_:2},1024))),128)),t(_,null,{default:e(()=>[t(r,{colspan:4,name:"payHead"}),t(r,{name:"earning"},{default:e(()=>[p("span",Z,a(s.netEarning.formatted),1)]),_:1}),t(r,{name:"deduction"},{default:e(()=>[p("span",ee,a(s.calculatedNetDeduction.formatted),1)]),_:1})]),_:1}),t(_,null,{default:e(()=>[t(r,{colspan:4,name:"payHead"},{default:e(()=>[p("span",te,a(n(o)("employee.payroll.salary_structure.props.net_salary")),1)]),_:1}),t(r,{colspan:2,name:"earning"},{default:e(()=>[p("span",ae,a(s.netSalary.formatted),1)]),_:1})]),_:1}),(d(!0),f(v,null,k(V.value,l=>(d(),b(_,{key:l.uuid},{default:e(()=>[t(r,{name:"payHead"},{default:e(()=>[p("span",le,a(l.payHead.name),1)]),_:2},1024),t(r,{name:"payHeadCode"},{default:e(()=>[p("span",oe,a(l.payHead.code),1)]),_:2},1024),t(r,{name:"type"},{default:e(()=>[u(a(l.type.label),1)]),_:2},1024),t(r,{name:"computation"},{default:e(()=>[u(a(l.computation||"-"),1)]),_:2},1024),t(r,{name:"earning"},{default:e(()=>[p("span",ne,a(l.amount.formatted),1)]),_:2},1024),t(r,{name:"deduction"},{default:e(()=>m[2]||(m[2]=[p("span",{class:"text-danger font-semibold"},null,-1)])),_:1})]),_:2},1024))),128)),t(_,null,{default:e(()=>[t(r,{colspan:4,name:"payHead"},{default:e(()=>[p("div",se,a(n(o)("employee.payroll.pay_head.categories.employer_contribution")),1)]),_:1}),t(r,{name:"deduction"},{default:e(()=>[p("span",re,a(s.netEmployerContribution.formatted),1)]),_:1})]),_:1})]),_:1})):g("",!0),s.description?(d(),f("dl",ue,[t(T,{class:"col-span-1 sm:col-span-2",label:n(o)("employee.payroll.salary_structure.props.description")},{default:e(()=>[u(a(s.description),1)]),_:1},8,["label"])])):g("",!0)]),_:1})])]),_:1})):g("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{ye as default};
