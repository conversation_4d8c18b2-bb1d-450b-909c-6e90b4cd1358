import{u as _,H as T,l as V,r as a,q as f,o as g,w as c,d as s,e as i,f as r,t as d}from"./app-BAwPsakn.js";const B={class:"grid grid-cols-1 gap-6"},P={class:"col-span-1"},$={class:"col-span-1"},y={class:"col-span-1"},A={class:"text-sm"},F={name:"ConfigMailTemplateForm"},U=Object.assign(F,{setup(b){const m=_(),p={subject:"",content:"",variablesDisplay:""},l="config/mailTemplate/",n=T(l),t=V({...p});return(u,e)=>{const v=a("BaseInput"),j=a("BaseEditor"),C=a("FormAction");return g(),f(C,{"init-url":l,"init-form":p,form:t,redirect:"ConfigMailTemplate"},{default:c(()=>[s("div",B,[s("div",P,[i(v,{type:"text",modelValue:t.subject,"onUpdate:modelValue":e[0]||(e[0]=o=>t.subject=o),name:"subject",label:u.$trans("config.mail.template.props.subject"),error:r(n).subject,"onUpdate:error":e[1]||(e[1]=o=>r(n).subject=o),autofocus:""},null,8,["modelValue","label","error"])]),s("div",$,[i(j,{id:"Testing",modelValue:t.content,"onUpdate:modelValue":e[2]||(e[2]=o=>t.content=o),name:"content",edit:!!r(m).params.uuid,label:u.$trans("config.mail.template.props.content"),error:r(n).content,"onUpdate:error":e[3]||(e[3]=o=>r(n).content=o)},null,8,["modelValue","edit","label","error"])]),s("div",y,[s("p",A,d(u.$trans("config.mail.template.available_variables"))+": "+d(t.variablesDisplay),1)])])]),_:1},8,["form"])}}}),E={name:"ConfigMailTemplateAction"},k=Object.assign(E,{setup(b){return _(),(m,p)=>{const l=a("PageHeaderAction"),n=a("ParentTransition"),t=a("ConfigPage");return g(),f(t,{"no-background":""},{action:c(()=>[i(l,{name:"ConfigMailTemplate",title:m.$trans("config.mail.template.template"),actions:["list"]},null,8,["title"])]),default:c(()=>[i(n,{appear:"",visibility:!0},{default:c(()=>[i(U)]),_:1})]),_:1})}}});export{k as default};
