import{i as q,u as K,h as z,j as I,l as A,H as J,m as Q,K as W,r as l,a as v,o as r,q as p,b as u,e as c,w as a,f as t,y as b,s as n,t as s,F as f,d as k,v as $}from"./app-BAwPsakn.js";const X={key:0,class:"flex items-center justify-center mb-10"},Y=["src"],Z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-4"},tt={name:"TripShow"},at=Object.assign(tt,{setup(et){q();const d=K(),w=z(),i=I("$trans"),T=I("emitter");let S=["list"];const L={},D={},B="activity/trip/";A({}),J(B),A({...D});const g=Q(!1),e=A({...L}),H=_=>{Object.assign(e,_)},h=_=>e.audiences.filter(o=>o.type===_),M=()=>d.name=="ActivityTripParticipantList"?`activity/trips/${e.uuid}/participants/`:`activity/trips/${e.uuid}/`,V=()=>d.name=="ActivityTripParticipantList"?["print","pdf","excel"]:[];return W(()=>d.params.uuid,_=>{g.value=!0}),(_,o)=>{const P=l("BaseButton"),C=l("DropdownItem"),j=l("PageHeaderAction"),R=l("PageHeader"),E=l("BaseTab"),F=l("BaseBadge"),m=l("BaseDataView"),x=l("TextMuted"),N=l("BaseCard"),G=l("router-view"),O=l("ShowItem"),U=l("ParentTransition");return r(),v(f,null,[e.uuid?(r(),p(R,{key:0,title:t(i)(t(d).meta.trans,{attribute:t(i)(t(d).meta.label)}),navs:[{label:t(i)("activity.activity"),path:"Activity"},{label:t(i)("activity.trip.trip"),path:"ActivityTripList"},{label:e.titleExcerpt,path:{name:"ActivityTripShow",params:{uuid:e.uuid}}}]},{default:a(()=>[c(j,{url:M(),name:"ActivityTrip",title:t(i)("activity.trip.trip"),actions:t(S),"dropdown-actions":V()},{dropdown:a(()=>[t(b)("trip:manage")?(r(),p(C,{key:0,icon:"fas fa-pencil",onClick:o[2]||(o[2]=y=>t(w).push({name:"ActivityTripEdit",params:{uuid:e.uuid}}))},{default:a(()=>[n(s(t(i)("global.edit",{attribute:t(i)("activity.trip.trip")})),1)]),_:1})):u("",!0),t(b)("trip:manage")?(r(),p(C,{key:1,icon:"fas fa-trash",onClick:o[3]||(o[3]=y=>t(T).emit("showDeleteItem",{uuid:e.uuid}))},{default:a(()=>[n(s(t(i)("global.delete",{attribute:t(i)("activity.trip.trip")})),1)]),_:1})):u("",!0),t(d).name=="ActivityTripParticipantList"?(r(),v(f,{key:2},[],64)):u("",!0)]),default:a(()=>[t(d).name=="ActivityTripParticipantList"&&t(b)("trip:manage")?(r(),p(P,{key:0,design:"white",onClick:o[0]||(o[0]=y=>t(T).emit("addTripParticipant",{trip:e}))},{default:a(()=>[n(s(t(i)("global.add",{attribute:t(i)("activity.trip.participant.participants")})),1)]),_:1})):u("",!0),t(d).name=="ActivityTripMediaList"&&t(b)("trip:manage")?(r(),p(P,{key:1,design:"white",onClick:o[1]||(o[1]=y=>t(T).emit("addActivityTripMedia"))},{default:a(()=>[n(s(t(i)("global.add",{attribute:t(i)("activity.trip.media.media")})),1)]),_:1})):u("",!0)]),_:1},8,["url","title","actions","dropdown-actions"])]),_:1},8,["title","navs"])):u("",!0),c(U,{appear:"",visibility:!0},{default:a(()=>[c(O,{"init-url":B,uuid:t(d).params.uuid,onSetItem:H,onRedirectTo:o[5]||(o[5]=y=>t(w).push({name:"ActivityTrip"})),refresh:g.value,onRefreshed:o[6]||(o[6]=y=>g.value=!1)},{default:a(()=>[e?(r(),p(E,{key:0,tabs:[{name:"ActivityTripShowGeneral",icon:"fas fa-home",label:t(i)("general.detail"),path:"ActivityTripShowGeneral"},{name:"ActivityTripParticipant",icon:"fas fa-users",label:t(i)("activity.trip.participant.participants"),count:e.participantsCount,path:"ActivityTripParticipantList"},{name:"ActivityTripMedia",icon:"fas fa-paperclip",label:t(i)("activity.trip.media.media"),count:e.mediaCount,path:"ActivityTripMediaList"}]},null,8,["tabs"])):u("",!0),e.uuid&&t(d).name=="ActivityTripShowGeneral"?(r(),p(N,{key:1},{title:a(()=>[n(s(e.title)+" ",1),e.type?(r(),p(F,{key:0,design:e.type.color?"custom":"",color:e.type.color},{default:a(()=>[n(s(e.type.name),1)]),_:1},8,["design","color"])):u("",!0)]),action:a(()=>o[7]||(o[7]=[k("div",{class:"space-x-2"},null,-1)])),default:a(()=>[e.coverImage?(r(),v("div",X,[k("img",{class:"rounded-tl-lg rounded-tr-lg",src:e.coverImage},null,8,Y)])):u("",!0),k("dl",Z,[c(m,{label:t(i)("activity.trip.props.venue")},{default:a(()=>[n(s(e.venue),1)]),_:1},8,["label"]),c(m,{label:t(i)("activity.trip.props.audience")},{default:a(()=>[n(s(e.studentAudienceType.label)+" ",1),(r(!0),v(f,null,$(h("student"),y=>(r(),p(x,{block:""},{default:a(()=>[n(s(y.name),1)]),_:2},1024))),256))]),_:1},8,["label"]),c(m,{label:t(i)("activity.trip.props.audience")},{default:a(()=>[e.employeeAudienceType.value?(r(),v(f,{key:0},[n(s(e.employeeAudienceType.label)+" ",1),(r(!0),v(f,null,$(h("employee"),y=>(r(),p(x,{block:""},{default:a(()=>[n(s(y.name),1)]),_:2},1024))),256))],64)):(r(),v(f,{key:1},[n("-")],64))]),_:1},8,["label"]),c(m,{label:t(i)("activity.trip.props.fee")},{default:a(()=>[n(s(e.fee.formatted),1)]),_:1},8,["label"]),t(b)("trip:manage")?(r(),p(m,{key:0,label:t(i)("activity.trip.participants_count")},{default:a(()=>[n(s(e.participantsCount),1)]),_:1},8,["label"])):u("",!0),c(m,{label:t(i)("activity.trip.props.duration")},{default:a(()=>[n(s(e.duration),1)]),_:1},8,["label"]),c(m,{label:t(i)("activity.trip.props.period")},{default:a(()=>[n(s(e.durationInDetail),1)]),_:1},8,["label"]),c(m,{class:"col-span-1 sm:col-span-4"},{default:a(()=>[n(s(e.summary),1)]),_:1}),c(m,{label:t(i)("general.created_at")},{default:a(()=>[n(s(e.createdAt.formatted),1)]),_:1},8,["label"]),c(m,{label:t(i)("general.updated_at")},{default:a(()=>[n(s(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):u("",!0),e.uuid?(r(),p(G,{key:2,trip:e,onRefresh:o[4]||(o[4]=y=>g.value=!0)},null,8,["trip"])):u("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{at as default};
