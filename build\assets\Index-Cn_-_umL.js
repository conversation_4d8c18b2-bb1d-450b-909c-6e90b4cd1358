import{u,h as l,i as f,l as g,n as _,r as o,q as p,o as d,w as C,e as h}from"./app-BAwPsakn.js";const b={name:"TeamConfig"},k=Object.assign(b,{setup(v){const n=u(),t=l(),s=f(),i=[{name:"TeamConfigGeneral",icon:"fas fa-building",label:"team.config.general.general"},{name:"TeamConfigRole",icon:"fas fa-user-tag",label:"team.config.role.role"},{name:"TeamConfigPermission",icon:"fas fa-key",label:"team.config.permission.permission"}],a=g({}),c=async()=>{await s.dispatch("team/get",{uuid:n.params.uuid}).then(e=>{Object.assign(a,e)}).catch(e=>{t.push({name:"TeamList"})})};return _(async()=>{await c()}),(e,w)=>{const r=o("router-view"),m=o("ModuleConfig");return d(),p(m,{navigations:i},{default:C(()=>[h(r,{team:a},null,8,["team"])]),_:1})}}});export{k as default};
