import{u as I,i as Q,H as z,m as H,l as U,n as R,K as P,r as c,q as O,b as h,o as i,w as t,d as a,e as s,s as n,t as e,f as M,a as u,F as V,v as S,J,x as K,h as G,j as W,p as X}from"./app-BAwPsakn.js";const Y={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Z={class:"mt-4"},q={class:"mt-4 grid grid-cols-1 gap-6"},ee={key:0,class:"mb-4"},te=["innerHTML"],ae={class:"dark:text-gray-400 flex justify-between"},se={class:"flex items-center"},ne={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},oe=["innerHTML"],ie={class:"flex gap-2"},le={class:"font-semibold"},re={class:"dark:text-gray-300 font-semibold text-sm italic"},de={key:0},me={key:0,class:"text-success far fa-check-circle fa-lg"},ce={key:1,class:"text-danger far fa-times-circle fa-lg"},ue={key:1,class:"ml-1 text-success"},_e={class:"grid grid-cols-4 gap-6"},fe={class:"col-span-4 sm:col-span-1"},be={class:"col-span-4 sm:col-span-3"},pe={name:"ExamOnlineExamEvaluationForm"},ge=Object.assign(pe,{props:{visibility:{type:Boolean,default:!1},submission:{type:Object,default:()=>({})}},emits:["close","refresh"],setup(x,{emit:A}){const j=I(),k=Q(),C=A,y=x,g={questions:[]},v="exam/onlineExam/submission/",o=z(v),B=H(!1),_=U({}),b=U({...g}),p=async()=>{g.questions=[],B.value=!0,await k.dispatch(v+"getQuestions",{uuid:j.params.uuid,moduleUuid:y.submission.uuid}).then(l=>{Object.assign(_,l),l.questions.forEach($=>{g.questions.push({...$,obtainedMark:$.obtainedMark||0,comment:$.comment||""})}),Object.assign(b,J(g)),B.value=!1}).catch(l=>{B.value=!1})},m=()=>{C("close")},w=()=>{C("refresh"),m()};return R(()=>{}),P(y.submission,l=>{console.log("submission",l.uuid),p()},{deep:!0,immediate:!0}),(l,$)=>{const d=c("TextMuted"),E=c("BaseDataView"),F=c("BaseHeading"),D=c("BaseInput"),r=c("FormAction"),L=c("BaseModal");return _.uuid?(i(),O(L,{key:0,show:x.visibility,onClose:m},{title:t(()=>[n(e(_.studentName)+" ",1),s(d,null,{default:t(()=>[n(e(_.admissionNumber)+" - "+e(_.courseName+" "+_.batchName),1)]),_:1})]),default:t(()=>[a("dl",Y,[s(E,{label:l.$trans("exam.online_exam.submission.props.started_at")},{default:t(()=>[n(e(_.startedAt.formatted),1)]),_:1},8,["label"]),s(E,{label:l.$trans("exam.online_exam.submission.props.submitted_at")},{default:t(()=>[n(e(_.submittedAt.formatted),1)]),_:1},8,["label"]),s(E,{label:l.$trans("exam.online_exam.submission.props.obtained_mark")},{default:t(()=>[n(e(_.obtainedMark)+" / "+e(_.maxMark),1)]),_:1},8,["label"])]),a("div",Z,[s(F,null,{default:t(()=>[n(e(l.$trans("exam.online_exam.question.questions")),1)]),_:1}),s(r,{"no-card":"","no-data-fetch":"",action:"evaluate",uuid:M(j).params.uuid,"module-uuid":_.uuid,"keep-adding":!1,"init-url":v,"init-form":g,form:b,"after-submit":w},{default:t(()=>[a("div",q,[(i(!0),u(V,null,S(b.questions,(f,N)=>(i(),u("div",{class:"col-span-3 sm:col-span-1",key:f.uuid},[f.header?(i(),u("div",ee,[a("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:f.header},null,8,te)])):h("",!0),a("div",ae,[a("div",se,[a("span",ne,e(N+1)+".",1),a("div",{innerHTML:f.title},null,8,oe)]),a("div",ie,[a("span",le,"("+e(f.mark)+")",1)])]),a("div",re,[f.type.value=="mcq"?(i(),u("span",de,[f.obtainedMark>0?(i(),u("i",me)):(i(),u("i",ce))])):h("",!0),n(" "+e(f.answer)+" ",1),f.type.value=="mcq"&&f.obtainedMark<=0?(i(),u("span",ue,e(f.correctAnswer),1)):h("",!0)]),a("div",_e,[a("div",fe,[s(D,{type:"number",modelValue:f.obtainedMark,"onUpdate:modelValue":T=>f.obtainedMark=T,name:`questions.${N}.obtainedMark`,placeholder:l.$trans("exam.online_exam.obtained_mark"),error:M(o)[`questions.${N}.obtainedMark`],"onUpdate:error":T=>M(o)[`questions.${N}.obtainedMark`]=T},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),a("div",be,[s(D,{type:"text",modelValue:f.comment,"onUpdate:modelValue":T=>f.comment=T,name:`questions.${N}.comment`,placeholder:l.$trans("exam.online_exam.comment"),error:M(o)[`questions.${N}.comment`],"onUpdate:error":T=>M(o)[`questions.${N}.comment`]=T},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])])]))),128))])]),_:1},8,["uuid","module-uuid","form"])])]),_:1},8,["show"])):h("",!0)}}}),ve={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},he={class:"mt-4"},xe={class:"mt-4 grid grid-cols-1 gap-6"},ke={key:0,class:"mb-4"},ye=["innerHTML"],$e={class:"dark:text-gray-400 flex justify-between"},Me={class:"flex items-center"},we={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},Be=["innerHTML"],Ee={class:"flex gap-2"},Ne={class:"font-semibold"},Te={class:"dark:text-gray-300 font-semibold text-sm italic"},je={key:0},Ce={key:0,class:"text-success far fa-check-circle fa-lg"},Oe={key:1,class:"text-danger far fa-times-circle fa-lg"},Ve={key:1,class:"ml-1 text-success"},Ue={name:"ExamOnlineExamSubmissionDetail"},Ae=Object.assign(Ue,{props:{visibility:{type:Boolean,default:!1},submission:{type:Object,default:()=>{}}},emits:["close","refresh"],setup(x,{emit:A}){const j=I(),k=Q(),C=A,y=x,g="exam/onlineExam/submission/",v=H(!1),o=U({}),B=async()=>{v.value=!0,await k.dispatch(g+"getQuestions",{uuid:j.params.uuid,moduleUuid:y.submission.uuid}).then(b=>{Object.assign(o,b),v.value=!1}).catch(b=>{v.value=!1})},_=()=>{C("close")};return R(()=>{}),P(y.submission,b=>{B()},{deep:!0,immediate:!0}),(b,p)=>{const m=c("TextMuted"),w=c("BaseDataView"),l=c("BaseHeading"),$=c("BaseModal");return o.uuid?(i(),O($,{key:0,show:x.visibility,onClose:_},{title:t(()=>[n(e(o.studentName)+" ",1),s(m,null,{default:t(()=>[n(e(o.admissionNumber)+" - "+e(o.courseName+" "+o.batchName),1)]),_:1})]),default:t(()=>[a("dl",ve,[s(w,{label:b.$trans("exam.online_exam.submission.props.started_at")},{default:t(()=>[n(e(o.startedAt.formatted),1)]),_:1},8,["label"]),s(w,{label:b.$trans("exam.online_exam.submission.props.submitted_at")},{default:t(()=>[n(e(o.submittedAt.formatted),1)]),_:1},8,["label"]),s(w,{label:b.$trans("exam.online_exam.submission.props.obtained_mark")},{default:t(()=>[n(e(o.obtainedMark)+" / "+e(o.maxMark),1)]),_:1},8,["label"])]),a("div",he,[s(l,null,{default:t(()=>[n(e(b.$trans("exam.online_exam.question.questions")),1)]),_:1}),a("div",xe,[(i(!0),u(V,null,S(o.questions,(d,E)=>(i(),u("div",{class:"col-span-3 sm:col-span-1",key:d.uuid},[d.header?(i(),u("div",ke,[a("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:d.header},null,8,ye)])):h("",!0),a("div",$e,[a("div",Me,[a("span",we,e(E+1)+".",1),a("div",{innerHTML:d.title},null,8,Be)]),a("div",Ee,[a("span",{class:K(["font-semibold",{"text-success":d.obtainedMark>0,"text-danger":d.obtainedMark<0}])},e(d.obtainedMark)+" / ",3),a("span",Ne,e(d.mark),1)])]),a("div",Te,[d.type.value=="mcq"?(i(),u("span",je,[d.obtainedMark>0?(i(),u("i",Ce)):(i(),u("i",Oe))])):h("",!0),n(" "+e(d.answer)+" ",1),d.type.value=="mcq"&&d.obtainedMark<=0?(i(),u("span",Ve,e(d.correctAnswer),1)):h("",!0)])]))),128))])])]),_:1},8,["show"])):h("",!0)}}}),De={name:"OnlineExamSubmissionList"},He=Object.assign(De,{props:{onlineExam:{type:Object,default(){return{}}}},emits:["refresh"],setup(x,{emit:A}){const j=I();G();const k=W("emitter"),C="exam/onlineExam/submission/",y=H(!1),g=H(!1),v=U({}),o=U({}),B=p=>{Object.assign(v,p)},_=p=>{Object.assign(o,p),g.value=!0},b=async p=>{Object.assign(o,p),y.value=!0};return R(async()=>{k.on("actionPerformed",()=>{refreshOnlineExam()})}),X(()=>{k.all.delete("actionPerformed")}),(p,m)=>{const w=c("TextMuted"),l=c("DataCell"),$=c("FloatingMenuItem"),d=c("FloatingMenu"),E=c("DataRow"),F=c("DataTable"),D=c("ListItem");return i(),u(V,null,[s(D,{"init-url":C,uuid:M(j).params.uuid,onSetItems:B},{default:t(()=>[s(F,{header:v.headers,meta:v.meta,module:"exam.online_exam.submission",onRefresh:m[0]||(m[0]=r=>M(k).emit("listItems"))},{actionButton:t(()=>m[5]||(m[5]=[])),default:t(()=>[(i(!0),u(V,null,S(v.data,r=>(i(),O(E,{key:r.uuid},{default:t(()=>[s(l,{name:"studentName"},{default:t(()=>[n(e(r.studentName)+" ",1),s(w,{block:""},{default:t(()=>[n(e(r.admissionNumber),1)]),_:2},1024)]),_:2},1024),s(l,{name:"courseName"},{default:t(()=>[n(e(r.courseName+" "+r.batchName),1)]),_:2},1024),s(l,{name:"submittedAt"},{default:t(()=>[n(e(r.submittedAt.formatted)+" ",1),s(w,{block:""},{default:t(()=>[n(e(r.startedAt.formatted),1)]),_:2},1024)]),_:2},1024),s(l,{name:"evaluatedAt"},{default:t(()=>[n(e(r.evaluatedAt.formatted||"-"),1)]),_:2},1024),s(l,{name:"obtainedMark"},{default:t(()=>[n(e(r.obtainedMark),1)]),_:2},1024),s(l,{name:"action"},{default:t(()=>[s(d,null,{default:t(()=>[s($,{icon:"fas fa-arrow-circle-right",onClick:L=>_(r)},{default:t(()=>[n(e(p.$trans("global.view",{attribute:p.$trans("exam.online_exam.submission.submission")})),1)]),_:2},1032,["onClick"]),x.onlineExam.resultPublishedAt.value?h("",!0):(i(),u(V,{key:0},[x.onlineExam.canEvaluate?(i(),O($,{key:0,icon:"far fa-check-circle",onClick:L=>b(r)},{default:t(()=>[n(e(p.$trans("exam.online_exam.evaluate")),1)]),_:2},1032,["onClick"])):h("",!0),s($,{icon:"fas fa-trash",onClick:L=>M(k).emit("deleteItem",{uuid:x.onlineExam.uuid,moduleUuid:r.uuid})},{default:t(()=>[n(e(p.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64))]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["uuid"]),g.value&&o.uuid?(i(),O(Ae,{key:0,visibility:g.value,submission:o,onClose:m[1]||(m[1]=r=>g.value=!1),onRefresh:m[2]||(m[2]=r=>M(k).emit("listItems"))},null,8,["visibility","submission"])):h("",!0),y.value&&x.onlineExam.canEvaluate&&o.uuid?(i(),O(ge,{key:1,visibility:y.value,submission:o,onClose:m[3]||(m[3]=r=>y.value=!1),onRefresh:m[4]||(m[4]=r=>M(k).emit("listItems"))},null,8,["visibility","submission"])):h("",!0)],64)}}});export{He as default};
