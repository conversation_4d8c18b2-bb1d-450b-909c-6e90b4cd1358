import{u as U,G as V,H as B,l as _,r as i,q as F,o as f,w as b,d as s,e as a,f as n,J as P,a as H,F as L}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3"},w={class:"col-span-3"},G={class:"grid grid-cols-1"},J={class:"col"},z={name:"ReceptionCallLogForm"},K=Object.assign(z,{setup(k){U();const m={type:"",purpose:"",name:"",callAt:"",duration:"",incomingNumber:"",outgoingNumber:"",conversation:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:V(),mediaHash:[]},p="reception/callLog/",t=B(p),u=_({types:[],purposes:[]}),l=_({...m}),g=r=>{Object.assign(u,r)},$=()=>{l.mediaToken=V(),l.mediaHash=[]},y=r=>{var e,d;Object.assign(m,{...r,callAt:r.callAt.at,duration:r.duration.value,type:(e=r.type)==null?void 0:e.value,purpose:(d=r.purpose)==null?void 0:d.uuid}),Object.assign(l,P(m))};return(r,e)=>{const d=i("CustomCheckbox"),N=i("BaseSelect"),c=i("BaseInput"),A=i("DatePicker"),v=i("BaseTextarea"),C=i("MediaUpload"),R=i("FormAction");return f(),F(R,{"pre-requisites":!0,onSetPreRequisites:g,"init-url":p,"init-form":m,form:l,"set-form":y,redirect:"ReceptionCallLog",onResetMediaFiles:$},{default:b(()=>[s("div",T,[s("div",j,[a(d,{label:r.$trans("reception.call_log.props.type"),options:u.types,modelValue:l.type,"onUpdate:modelValue":e[0]||(e[0]=o=>l.type=o),error:n(t).type,"onUpdate:error":e[1]||(e[1]=o=>n(t).type=o)},null,8,["label","options","modelValue","error"])]),s("div",q,[a(N,{modelValue:l.purpose,"onUpdate:modelValue":e[2]||(e[2]=o=>l.purpose=o),name:"purpose",label:r.$trans("reception.call_log.purpose.purpose"),"label-prop":"name","value-prop":"uuid",options:u.purposes,error:n(t).purpose,"onUpdate:error":e[3]||(e[3]=o=>n(t).purpose=o)},null,8,["modelValue","label","options","error"])]),s("div",O,[a(c,{type:"text",modelValue:l.name,"onUpdate:modelValue":e[4]||(e[4]=o=>l.name=o),name:"name",label:r.$trans("reception.call_log.props.name"),error:n(t).name,"onUpdate:error":e[5]||(e[5]=o=>n(t).name=o)},null,8,["modelValue","label","error"])]),s("div",M,[a(A,{as:"datetime",modelValue:l.callAt,"onUpdate:modelValue":e[6]||(e[6]=o=>l.callAt=o),name:"callAt",label:r.$trans("reception.call_log.props.call_at"),"no-clear":"",error:n(t).callAt,"onUpdate:error":e[7]||(e[7]=o=>n(t).callAt=o)},null,8,["modelValue","label","error"])]),s("div",S,[a(c,{"trailing-text":r.$trans("list.durations.minutes"),type:"number",modelValue:l.duration,"onUpdate:modelValue":e[8]||(e[8]=o=>l.duration=o),name:"duration",label:r.$trans("reception.call_log.props.duration"),error:n(t).duration,"onUpdate:error":e[9]||(e[9]=o=>n(t).duration=o)},null,8,["trailing-text","modelValue","label","error"])]),s("div",D,[a(c,{type:"text",modelValue:l.incomingNumber,"onUpdate:modelValue":e[10]||(e[10]=o=>l.incomingNumber=o),name:"incomingNumber",label:r.$trans("reception.call_log.props.incoming_number"),error:n(t).incomingNumber,"onUpdate:error":e[11]||(e[11]=o=>n(t).incomingNumber=o)},null,8,["modelValue","label","error"])]),s("div",E,[a(c,{type:"text",modelValue:l.outgoingNumber,"onUpdate:modelValue":e[12]||(e[12]=o=>l.outgoingNumber=o),name:"outgoingNumber",label:r.$trans("reception.call_log.props.outgoing_number"),error:n(t).outgoingNumber,"onUpdate:error":e[13]||(e[13]=o=>n(t).outgoingNumber=o)},null,8,["modelValue","label","error"])]),s("div",I,[a(v,{modelValue:l.conversation,"onUpdate:modelValue":e[14]||(e[14]=o=>l.conversation=o),name:"conversation",label:r.$trans("reception.call_log.props.conversation"),error:n(t).conversation,"onUpdate:error":e[15]||(e[15]=o=>n(t).conversation=o)},null,8,["modelValue","label","error"])]),s("div",w,[a(v,{modelValue:l.remarks,"onUpdate:modelValue":e[16]||(e[16]=o=>l.remarks=o),name:"remarks",label:r.$trans("reception.call_log.props.remarks"),error:n(t).remarks,"onUpdate:error":e[17]||(e[17]=o=>n(t).remarks=o)},null,8,["modelValue","label","error"])])]),s("div",G,[s("div",J,[a(C,{multiple:"",label:r.$trans("general.file"),module:"call_log",media:l.media,"media-token":l.mediaToken,onIsUpdated:e[18]||(e[18]=o=>l.mediaUpdated=!0),onSetHash:e[19]||(e[19]=o=>l.mediaHash.push(o))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),Q={name:"ReceptionCallLogAction"},X=Object.assign(Q,{setup(k){const m=U();return(p,t)=>{const u=i("PageHeaderAction"),l=i("PageHeader"),g=i("ParentTransition");return f(),H(L,null,[a(l,{title:p.$trans(n(m).meta.trans,{attribute:p.$trans(n(m).meta.label)}),navs:[{label:p.$trans("reception.reception"),path:"Reception"},{label:p.$trans("reception.call_log.call_log"),path:"ReceptionCallLogList"}]},{default:b(()=>[a(u,{name:"ReceptionCallLog",title:p.$trans("reception.call_log.call_log"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(g,{appear:"",visibility:!0},{default:b(()=>[a(K)]),_:1})],64)}}});export{X as default};
