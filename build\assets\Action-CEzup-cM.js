import{H as B,l as f,r,q as k,o as c,w as p,d as u,e as l,f as o,u as A,a as V,F as $}from"./app-BAwPsakn.js";const v={class:"grid grid-cols-3 gap-6"},F={class:"col-span-3 sm:col-span-1"},U={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},H={name:"AssetBuildingBlockForm"},T=Object.assign(H,{setup(b){const i={name:"",alias:"",description:""},t="asset/building/block/",n=B(t),a=f({...i});return(d,e)=>{const m=r("BaseInput"),g=r("BaseTextarea"),_=r("FormAction");return c(),k(_,{"init-url":t,"init-form":i,form:a,redirect:"AssetBuildingBlock"},{default:p(()=>[u("div",v,[u("div",F,[l(m,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=s=>a.name=s),name:"name",label:d.$trans("asset.building.block.props.name"),error:o(n).name,"onUpdate:error":e[1]||(e[1]=s=>o(n).name=s),autofocus:""},null,8,["modelValue","label","error"])]),u("div",U,[l(m,{type:"text",modelValue:a.alias,"onUpdate:modelValue":e[2]||(e[2]=s=>a.alias=s),name:"alias",label:d.$trans("asset.building.block.props.alias"),error:o(n).alias,"onUpdate:error":e[3]||(e[3]=s=>o(n).alias=s)},null,8,["modelValue","label","error"])]),u("div",P,[l(g,{modelValue:a.description,"onUpdate:modelValue":e[4]||(e[4]=s=>a.description=s),name:"description",label:d.$trans("asset.building.block.props.description"),error:o(n).description,"onUpdate:error":e[5]||(e[5]=s=>o(n).description=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),y={name:"AssetBuildingBlockAction"},j=Object.assign(y,{setup(b){const i=A();return(t,n)=>{const a=r("PageHeaderAction"),d=r("PageHeader"),e=r("ParentTransition");return c(),V($,null,[l(d,{title:t.$trans(o(i).meta.trans,{attribute:t.$trans(o(i).meta.label)}),navs:[{label:t.$trans("asset.asset"),path:"Asset"},{label:t.$trans("asset.building.building"),path:"AssetBuilding"},{label:t.$trans("asset.building.block.block"),path:"AssetBuildingBlockList"}]},{default:p(()=>[l(a,{name:"AssetBuildingBlock",title:t.$trans("asset.building.block.block"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(e,{appear:"",visibility:!0},{default:p(()=>[l(T)]),_:1})],64)}}});export{j as default};
