import{u as O,h as H,j as T,H as D,l as q,n as J,J as M,r as i,a as G,o as U,q as g,b as v,e as o,f as d,w as m,d as a,I as C,s as F,t as w,F as K}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3"},_={class:"grid grid-cols-3 gap-6"},h={class:"col-span-3"},ee={class:"grid grid-cols-3 gap-6"},se={class:"col-span-3 sm:col-span-1"},te={class:"mt-4 grid grid-cols-3 gap-6"},re={name:"StudentEditContact"},de=Object.assign(re,{props:{student:{type:Object,default(){return{}}}},setup(p){const N=O(),j=H(),k=T("emitter"),V=p,A={contactNumber:"",email:"",alternateRecords:{},presentAddress:{},permanentAddress:{}},z="student/",n=D(z),t=q({...A});J(async()=>{var e,b,l,u,c,y,f,L,s,E,P,S,$,R,B;let r=(e=V.student)==null?void 0:e.contact;Object.assign(A,{contactNumber:r.contactNumber,email:r.email,alternateRecords:{contactNumber:(b=r.alternateRecords)==null?void 0:b.contactNumber,email:(l=r.alternateRecords)==null?void 0:l.email},presentAddress:{addressLine1:(u=r.presentAddress)==null?void 0:u.addressLine1,addressLine2:(c=r.presentAddress)==null?void 0:c.addressLine2,city:(y=r.presentAddress)==null?void 0:y.city,state:(f=r.presentAddress)==null?void 0:f.state,zipcode:(L=r.presentAddress)==null?void 0:L.zipcode,country:(s=r.presentAddress)==null?void 0:s.country},permanentAddress:{sameAsPresentAddress:r.sameAsPresentAddress,addressLine1:(E=r.permanentAddress)==null?void 0:E.addressLine1,addressLine2:(P=r.permanentAddress)==null?void 0:P.addressLine2,city:(S=r.permanentAddress)==null?void 0:S.city,state:($=r.permanentAddress)==null?void 0:$.state,zipcode:(R=r.permanentAddress)==null?void 0:R.zipcode,country:(B=r.permanentAddress)==null?void 0:B.country}}),Object.assign(t,M(A))});const I=()=>{k.emit("studentUpdated"),j.push({name:"StudentShowContact",params:{uuid:V.student.uuid}})};return(r,e)=>{const b=i("PageHeader"),l=i("BaseInput"),u=i("AddressInput"),c=i("BaseFieldset"),y=i("BaseSwitch"),f=i("FormAction"),L=i("ParentTransition");return U(),G(K,null,[p.student.uuid?(U(),g(b,{key:0,title:r.$trans(d(N).meta.trans,{attribute:r.$trans(d(N).meta.label)}),navs:[{label:r.$trans("student.student"),path:"Student"},{label:p.student.contact.name,path:{name:"StudentShow",params:{uuid:p.student.uuid}}}]},null,8,["title","navs"])):v("",!0),o(L,{appear:"",visibility:!0},{default:m(()=>[p.student.uuid?(U(),g(f,{key:0,"init-url":z,"no-data-fetch":"","init-form":A,form:t,"stay-on":"","after-submit":I,redirect:{name:"StudentShowContact",params:{uuid:p.student.uuid}}},{default:m(()=>[a("div",Q,[a("div",W,[o(l,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=s=>t.contactNumber=s),name:"contactNumber",label:r.$trans("contact.props.contact_number"),error:d(n).contactNumber,"onUpdate:error":e[1]||(e[1]=s=>d(n).contactNumber=s)},null,8,["modelValue","label","error"])]),a("div",X,[o(l,{type:"text",modelValue:t.alternateRecords.contactNumber,"onUpdate:modelValue":e[2]||(e[2]=s=>t.alternateRecords.contactNumber=s),name:"alternateContactNumber",label:r.$trans("global.alternate",{attribute:r.$trans("contact.props.contact_number")}),error:d(n).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=s=>d(n).alternateContactNumber=s)},null,8,["modelValue","label","error"])]),e[24]||(e[24]=a("div",{class:"col-span-3 sm:col-span-1"},null,-1)),a("div",Y,[o(l,{type:"text",modelValue:t.email,"onUpdate:modelValue":e[4]||(e[4]=s=>t.email=s),name:"email",label:r.$trans("contact.props.email"),error:d(n).email,"onUpdate:error":e[5]||(e[5]=s=>d(n).email=s)},null,8,["modelValue","label","error"])]),a("div",Z,[o(l,{type:"text",modelValue:t.alternateRecords.email,"onUpdate:modelValue":e[6]||(e[6]=s=>t.alternateRecords.email=s),name:"alternateEmail",label:r.$trans("global.alternate",{attribute:r.$trans("contact.props.email")}),error:d(n).alternateEmail,"onUpdate:error":e[7]||(e[7]=s=>d(n).alternateEmail=s)},null,8,["modelValue","label","error"])]),e[25]||(e[25]=a("div",{class:"col-span-3 sm:col-span-1"},null,-1)),a("div",x,[o(c,null,{legend:m(()=>[F(w(r.$trans("contact.props.present_address")),1)]),default:m(()=>[a("div",_,[o(u,{prefix:"presentAddress",addressLine1:t.presentAddress.addressLine1,"onUpdate:addressLine1":e[8]||(e[8]=s=>t.presentAddress.addressLine1=s),addressLine2:t.presentAddress.addressLine2,"onUpdate:addressLine2":e[9]||(e[9]=s=>t.presentAddress.addressLine2=s),city:t.presentAddress.city,"onUpdate:city":e[10]||(e[10]=s=>t.presentAddress.city=s),state:t.presentAddress.state,"onUpdate:state":e[11]||(e[11]=s=>t.presentAddress.state=s),zipcode:t.presentAddress.zipcode,"onUpdate:zipcode":e[12]||(e[12]=s=>t.presentAddress.zipcode=s),country:t.presentAddress.country,"onUpdate:country":e[13]||(e[13]=s=>t.presentAddress.country=s),formErrors:d(n),"onUpdate:formErrors":e[14]||(e[14]=s=>C(n)?n.value=s:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1})]),a("div",h,[o(c,null,{legend:m(()=>[F(w(r.$trans("contact.props.permanent_address")),1)]),default:m(()=>[a("div",ee,[a("div",se,[o(y,{modelValue:t.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[15]||(e[15]=s=>t.permanentAddress.sameAsPresentAddress=s),name:"sameAsPresentAddress",label:r.$trans("contact.props.same_as_present_address"),error:d(n).sameAsPresentAddress,"onUpdate:error":e[16]||(e[16]=s=>d(n).sameAsPresentAddress=s)},null,8,["modelValue","label","error"])])]),a("div",te,[t.permanentAddress.sameAsPresentAddress?v("",!0):(U(),g(u,{key:0,prefix:"permanentAddress",addressLine1:t.permanentAddress.addressLine1,"onUpdate:addressLine1":e[17]||(e[17]=s=>t.permanentAddress.addressLine1=s),addressLine2:t.permanentAddress.addressLine2,"onUpdate:addressLine2":e[18]||(e[18]=s=>t.permanentAddress.addressLine2=s),city:t.permanentAddress.city,"onUpdate:city":e[19]||(e[19]=s=>t.permanentAddress.city=s),state:t.permanentAddress.state,"onUpdate:state":e[20]||(e[20]=s=>t.permanentAddress.state=s),zipcode:t.permanentAddress.zipcode,"onUpdate:zipcode":e[21]||(e[21]=s=>t.permanentAddress.zipcode=s),country:t.permanentAddress.country,"onUpdate:country":e[22]||(e[22]=s=>t.permanentAddress.country=s),formErrors:d(n),"onUpdate:formErrors":e[23]||(e[23]=s=>C(n)?n.value=s:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1})])])]),_:1},8,["form","redirect"])):v("",!0)]),_:1})],64)}}});export{de as default};
