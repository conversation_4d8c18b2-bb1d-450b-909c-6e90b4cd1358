import{u as A,l as k,n as H,r as a,q as b,o as _,w as e,d as h,e as t,h as L,j as N,y as C,m as Q,f as c,a as S,F as U,v as x,s as l,t as d,b as O}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(R,{emit:g}){A();const f=g,v={search:"",startDate:"",endDate:""},i=k({...v}),D=k({isLoaded:!0});return H(async()=>{D.isLoaded=!0}),(m,o)=>{const $=a("BaseInput"),r=a("DatePicker"),s=a("FilterForm");return _(),b(s,{"init-form":v,form:i,multiple:[],onHide:o[3]||(o[3]=u=>f("hide"))},{default:e(()=>[h("div",q,[h("div",E,[t($,{type:"text",modelValue:i.search,"onUpdate:modelValue":o[0]||(o[0]=u=>i.search=u),name:"search",label:m.$trans("general.search")},null,8,["modelValue","label"])]),h("div",z,[t(r,{start:i.startDate,"onUpdate:start":o[1]||(o[1]=u=>i.startDate=u),end:i.endDate,"onUpdate:end":o[2]||(o[2]=u=>i.endDate=u),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={name:"ReceptionQueryList"},W=Object.assign(J,{setup(R){const g=L(),f=N("emitter");let v=["filter"],i=[];C("query:export")&&(i=["print","pdf","excel"]);const D="reception/query/",m=Q(!1),o=k({}),$=r=>{Object.assign(o,r)};return(r,s)=>{const u=a("PageHeaderAction"),y=a("PageHeader"),w=a("ParentTransition"),p=a("DataCell"),I=a("TextMuted"),P=a("BaseBadge"),F=a("FloatingMenuItem"),T=a("FloatingMenu"),M=a("DataRow"),V=a("DataTable"),j=a("ListItem");return _(),b(j,{"init-url":D,"additional-query":{},onSetItems:$},{header:e(()=>[t(y,{title:r.$trans("reception.query.query"),navs:[{label:r.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[t(u,{url:"reception/queries/",name:"ReceptionQuery",title:r.$trans("reception.query.query"),actions:c(v),"dropdown-actions":c(i),"config-path":"ReceptionConfig",onToggleFilter:s[0]||(s[0]=n=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(w,{appear:"",visibility:m.value},{default:e(()=>[t(G,{onRefresh:s[1]||(s[1]=n=>c(f).emit("listItems")),onHide:s[2]||(s[2]=n=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(w,{appear:"",visibility:!0},{default:e(()=>[t(V,{header:o.headers,meta:o.meta,module:"reception.query",onRefresh:s[3]||(s[3]=n=>c(f).emit("listItems"))},{actionButton:e(()=>s[4]||(s[4]=[])),default:e(()=>[(_(!0),S(U,null,x(o.data,n=>(_(),b(M,{key:n.uuid,onDoubleClick:B=>c(g).push({name:"ReceptionQueryShow",params:{uuid:n.uuid}})},{default:e(()=>[t(p,{name:"code_number"},{default:e(()=>[l(d(n.codeNumber),1)]),_:2},1024),t(p,{name:"name"},{default:e(()=>[l(d(n.name),1)]),_:2},1024),t(p,{name:"email"},{default:e(()=>[l(d(n.email)+" ",1),t(I,{block:""},{default:e(()=>[l(d(n.phone),1)]),_:2},1024)]),_:2},1024),t(p,{name:"status"},{default:e(()=>[t(P,{design:n.status.color},{default:e(()=>[l(d(n.status.label),1)]),_:2},1032,["design"])]),_:2},1024),t(p,{name:"subject"},{default:e(()=>[l(d(n.subject),1)]),_:2},1024),t(p,{name:"createdAt"},{default:e(()=>[l(d(n.createdAt.formatted),1)]),_:2},1024),t(p,{name:"action"},{default:e(()=>[t(T,null,{default:e(()=>[t(F,{icon:"fas fa-arrow-circle-right",onClick:B=>c(g).push({name:"ReceptionQueryShow",params:{uuid:n.uuid}})},{default:e(()=>[l(d(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),c(C)("query:delete")?(_(),b(F,{key:0,icon:"fas fa-trash",onClick:B=>c(f).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[l(d(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):O("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
