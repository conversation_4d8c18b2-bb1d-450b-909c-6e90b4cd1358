import{u as v,h as S,l as C,r as n,a as P,o as p,e as a,w as t,f as o,q as V,b as k,d as y,s as u,t as r,F as A}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},N={name:"MessMenuItemShow"},R=Object.assign(N,{setup(T){const l=v(),d=S(),c={},_="mess/menuItem/",s=C({...c}),f=e=>{Object.assign(s,e)};return(e,m)=>{const g=n("PageHeaderAction"),b=n("PageHeader"),i=n("BaseDataView"),B=n("BaseButton"),I=n("ShowButton"),M=n("BaseCard"),$=n("ShowItem"),h=n("ParentTransition");return p(),P(A,null,[a(b,{title:e.$trans(o(l).meta.trans,{attribute:e.$trans(o(l).meta.label)}),navs:[{label:e.$trans("mess.mess"),path:"Mess"},{label:e.$trans("mess.menu.item"),path:"MessMenuItem"}]},{default:t(()=>[a(g,{name:"MessMenuItem",title:e.$trans("mess.menu.item"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(h,{appear:"",visibility:!0},{default:t(()=>[a($,{"init-url":_,uuid:o(l).params.uuid,"module-uuid":o(l).params.muuid,onSetItem:f,onRedirectTo:m[1]||(m[1]=w=>o(d).push({name:"MessMenuItem",params:{uuid:s.uuid}}))},{default:t(()=>[s.uuid?(p(),V(M,{key:0},{title:t(()=>[u(r(s.name),1)]),footer:t(()=>[a(I,null,{default:t(()=>[a(B,{design:"primary",onClick:m[0]||(m[0]=w=>o(d).push({name:"MessMenuItemEdit",params:{uuid:s.uuid}}))},{default:t(()=>[u(r(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[y("dl",H,[a(i,{label:e.$trans("mess.menu.props.name")},{default:t(()=>[u(r(s.name),1)]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2",label:e.$trans("mess.menu.props.description")},{default:t(()=>[u(r(s.description),1)]),_:1},8,["label"]),a(i,{label:e.$trans("general.created_at")},{default:t(()=>[u(r(s.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:e.$trans("general.updated_at")},{default:t(()=>[u(r(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):k("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{R as default};
