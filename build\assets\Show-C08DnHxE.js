import{u as b,h as C,i as B,j,m as x,l as y,n as S,p as k,r as n,q as u,o as c,w as l,b as d,e as L,a as N,d as e,t as m,s as V}from"./app-BAwPsakn.js";const M={key:0,class:"mb-4"},U={class:"-mt-12 flex w-full justify-center sm:-mt-16"},O=["src"],R={class:"mt-4 text-center text-white"},q={class:"font-bold"},D={name:"ContactShow"},z=Object.assign(D,{setup(E){const f=b(),h=C(),_=B(),i=j("emitter"),g=[{name:"ContactShowBasic",icon:"fas fa-chevron-right",label:"general.basic"},{name:"ContactShowContact",icon:"fas fa-chevron-right",label:"contact.contact"},{name:"ContactShowLogin",icon:"fas fa-chevron-right",label:"contact.login.login"}],o=x(!1),t=y({}),r=async()=>{o.value=!0,await _.dispatch("contact/get",{uuid:f.params.uuid}).then(s=>{Object.assign(t,s),o.value=!1}).catch(s=>{o.value=!1,h.push({name:"ContactList"})})};return S(async()=>{i.on("contactUpdated",()=>{r()}),await r()}),k(()=>{i.all.delete("contactUpdated")}),(s,a)=>{const p=n("BaseLoader"),v=n("router-view"),w=n("ModuleConfig");return c(),u(w,{navigations:g,"use-uuid":""},{header:l(()=>[L(p,null,{default:l(()=>[t.uuid?(c(),N("div",M,[a[1]||(a[1]=e("img",{class:"h-32 w-full object-cover lg:h-48",src:"/images/user-cover.jpeg",alt:""},null,-1)),e("div",U,[e("img",{class:"h-24 w-24 rounded-full ring-4 ring-white sm:h-32 sm:w-32",src:t.photo,alt:""},null,8,O)]),e("div",R,[e("h1",q,m(t.name),1),e("p",null,[a[0]||(a[0]=e("i",{class:"fas fa-mobile-alt"},null,-1)),V(" "+m(t.contactNumber),1)])])])):d("",!0)]),_:1})]),default:l(()=>[t.uuid?(c(),u(v,{key:0,contact:t},null,8,["contact"])):d("",!0)]),_:1})}}});export{z as default};
