import{l as B,r as a,q as F,o as R,w as e,d as _,e as t,h as j,j as A,m as T,f as i,a as U,F as L,v as M,s as r,t as u}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(V,{emit:d}){const f=d,v=V,k={name:"",number:"",floor:"",block:""},m=B({...k}),b=B({floors:v.preRequisites.floors,blocks:v.preRequisites.blocks});return(p,s)=>{const $=a("BaseInput"),g=a("BaseSelect"),l=a("FilterForm");return R(),F(l,{"init-form":k,form:m,onHide:s[4]||(s[4]=o=>f("hide"))},{default:e(()=>[_("div",N,[_("div",O,[t($,{type:"text",modelValue:m.name,"onUpdate:modelValue":s[0]||(s[0]=o=>m.name=o),name:"name",label:p.$trans("hostel.room.props.name")},null,8,["modelValue","label"])]),_("div",E,[t($,{type:"text",modelValue:m.number,"onUpdate:modelValue":s[1]||(s[1]=o=>m.number=o),name:"number",label:p.$trans("hostel.room.props.number")},null,8,["modelValue","label"])]),_("div",W,[t(g,{modelValue:m.block,"onUpdate:modelValue":s[2]||(s[2]=o=>m.block=o),name:"block","label-prop":"name","value-prop":"uuid",label:p.$trans("hostel.block.block"),options:b.blocks},null,8,["modelValue","label","options"])]),_("div",z,[t(g,{modelValue:m.floor,"onUpdate:modelValue":s[3]||(s[3]=o=>m.floor=o),name:"floor","label-prop":"nameWithBlock","value-prop":"uuid",label:p.$trans("hostel.floor.floor"),options:b.floors},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},J={name:"HostelRoomList"},Q=Object.assign(J,{setup(V){const d=j(),f=A("emitter");let v=["create","filter"],k=["print","pdf","excel"];const m="hostel/room/",b=B({floors:[]}),p=T(!1),s=B({}),$=l=>{Object.assign(s,l)},g=l=>{Object.assign(b,l)};return(l,o)=>{const H=a("BaseButton"),y=a("PageHeaderAction"),I=a("PageHeader"),w=a("ParentTransition"),c=a("DataCell"),h=a("FloatingMenuItem"),q=a("FloatingMenu"),D=a("DataRow"),P=a("DataTable"),S=a("ListItem");return R(),F(S,{"init-url":m,"pre-requisites":!0,onSetPreRequisites:g,onSetItems:$},{header:e(()=>[t(I,{title:l.$trans("hostel.room.room"),navs:[{label:l.$trans("hostel.hostel"),path:"Hostel"}]},{default:e(()=>[t(y,{url:"hostel/rooms/",name:"HostelRoom",title:l.$trans("hostel.room.room"),actions:i(v),"dropdown-actions":i(k),onToggleFilter:o[2]||(o[2]=n=>p.value=!p.value)},{default:e(()=>[t(H,{design:"white",onClick:o[0]||(o[0]=n=>i(d).push({name:"HostelBlock"}))},{default:e(()=>[r(u(l.$trans("hostel.block.block")),1)]),_:1}),t(H,{design:"white",onClick:o[1]||(o[1]=n=>i(d).push({name:"HostelFloor"}))},{default:e(()=>[r(u(l.$trans("hostel.floor.floor")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(w,{appear:"",visibility:p.value},{default:e(()=>[t(G,{onRefresh:o[3]||(o[3]=n=>i(f).emit("listItems")),"pre-requisites":b,onHide:o[4]||(o[4]=n=>p.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(w,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:s.headers,meta:s.meta,module:"hostel.room",onRefresh:o[6]||(o[6]=n=>i(f).emit("listItems"))},{actionButton:e(()=>[t(H,{onClick:o[5]||(o[5]=n=>i(d).push({name:"HostelRoomCreate"}))},{default:e(()=>[r(u(l.$trans("global.add",{attribute:l.$trans("hostel.room.room")})),1)]),_:1})]),default:e(()=>[(R(!0),U(L,null,M(s.data,n=>(R(),F(D,{key:n.uuid,onDoubleClick:C=>i(d).push({name:"HostelRoomShow",params:{uuid:n.uuid}})},{default:e(()=>[t(c,{name:"name"},{default:e(()=>[r(u(n.name),1)]),_:2},1024),t(c,{name:"number"},{default:e(()=>[r(u(n.number),1)]),_:2},1024),t(c,{name:"floor"},{default:e(()=>[r(u(n.floorNameWithBlock),1)]),_:2},1024),t(c,{name:"capacity"},{default:e(()=>[r(u(n.capacity),1)]),_:2},1024),t(c,{name:"createdAt"},{default:e(()=>[r(u(n.createdAt.formatted),1)]),_:2},1024),t(c,{name:"action"},{default:e(()=>[t(q,null,{default:e(()=>[t(h,{icon:"fas fa-arrow-circle-right",onClick:C=>i(d).push({name:"HostelRoomShow",params:{uuid:n.uuid}})},{default:e(()=>[r(u(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-edit",onClick:C=>i(d).push({name:"HostelRoomEdit",params:{uuid:n.uuid}})},{default:e(()=>[r(u(l.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-copy",onClick:C=>i(d).push({name:"HostelRoomDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[r(u(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-trash",onClick:C=>i(f).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[r(u(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
