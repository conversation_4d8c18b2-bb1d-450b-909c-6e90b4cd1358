import{u as U,h as $,H as C,l as T,r as a,q as v,o as g,w as V,d as m,b as F,e as l,f as i,I as B,J as P,a as q,F as E}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-4 gap-6"},x={class:"col-span-4"},H={class:"col-span-4 sm:col-span-2"},I={class:"col-span-4 sm:col-span-2"},M={class:"mt-4 grid grid-cols-1 gap-6"},O={class:"col-span-4"},k={name:"CommunicationSMSForm"},L=Object.assign(k,{setup(_){const c=U();$();const u={subject:"",studentAudienceType:"",employeeAudienceType:"",studentAudiences:[],employeeAudiences:[],inclusion:"",exclusion:"",content:""},f="communication/sms/",s=C(f),p=T({studentAudienceTypes:[],employeeAudienceTypes:[]}),t=T({...u}),A=T({studentAudiences:[],employeeAudiences:[],isLoaded:!c.params.uuid}),j=o=>{Object.assign(p,o)},S=o=>{var d,b;let e=o.audiences.filter(r=>r.type=="student").map(r=>r.uuid),y=o.audiences.filter(r=>r.type=="employee").map(r=>r.uuid);Object.assign(u,{...o,studentAudienceType:((d=o.studentAudienceType)==null?void 0:d.value)||"",employeeAudienceType:((b=o.employeeAudienceType)==null?void 0:b.value)||"",studentAudiences:e,employeeAudiences:y}),Object.assign(t,P(u)),A.studentAudiences=e,A.employeeAudiences=y,A.isLoaded=!0};return(o,e)=>{const y=a("BaseInput"),d=a("BaseTextarea"),b=a("AudienceInput"),r=a("FormAction");return g(),v(r,{"pre-requisites":!0,onSetPreRequisites:j,"init-url":f,"init-form":u,form:t,setForm:S,redirect:"CommunicationSMS"},{default:V(()=>[m("div",R,[m("div",x,[l(y,{type:"text",modelValue:t.subject,"onUpdate:modelValue":e[0]||(e[0]=n=>t.subject=n),name:"subject",label:o.$trans("communication.sms.props.subject"),error:i(s).subject,"onUpdate:error":e[1]||(e[1]=n=>i(s).subject=n),autofocus:""},null,8,["modelValue","label","error"])]),m("div",H,[l(d,{modelValue:t.inclusion,"onUpdate:modelValue":e[2]||(e[2]=n=>t.inclusion=n),name:"inclusion",label:o.$trans("communication.sms.props.inclusion"),error:i(s).inclusion,"onUpdate:error":e[3]||(e[3]=n=>i(s).inclusion=n)},null,8,["modelValue","label","error"])]),m("div",I,[l(d,{modelValue:t.exclusion,"onUpdate:modelValue":e[4]||(e[4]=n=>t.exclusion=n),name:"exclusion",label:o.$trans("communication.sms.props.exclusion"),error:i(s).exclusion,"onUpdate:error":e[5]||(e[5]=n=>i(s).exclusion=n)},null,8,["modelValue","label","error"])])]),A.isLoaded?(g(),v(b,{key:0,"pre-requisites":p,studentAudienceType:t.studentAudienceType,"onUpdate:studentAudienceType":e[6]||(e[6]=n=>t.studentAudienceType=n),employeeAudienceType:t.employeeAudienceType,"onUpdate:employeeAudienceType":e[7]||(e[7]=n=>t.employeeAudienceType=n),studentAudiences:t.studentAudiences,"onUpdate:studentAudiences":e[8]||(e[8]=n=>t.studentAudiences=n),employeeAudiences:t.employeeAudiences,"onUpdate:employeeAudiences":e[9]||(e[9]=n=>t.employeeAudiences=n),formErrors:i(s),"onUpdate:formErrors":e[10]||(e[10]=n=>B(s)?s.value=n:null)},null,8,["pre-requisites","studentAudienceType","employeeAudienceType","studentAudiences","employeeAudiences","formErrors"])):F("",!0),m("div",M,[m("div",O,[l(d,{modelValue:t.content,"onUpdate:modelValue":e[11]||(e[11]=n=>t.content=n),name:"content",label:o.$trans("communication.sms.props.content"),error:i(s).content,"onUpdate:error":e[12]||(e[12]=n=>i(s).content=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),N={name:"CommunicationSMSAction"},D=Object.assign(N,{setup(_){const c=U();return(u,f)=>{const s=a("PageHeaderAction"),p=a("PageHeader"),t=a("ParentTransition");return g(),q(E,null,[l(p,{title:u.$trans(i(c).meta.trans,{attribute:u.$trans(i(c).meta.label)}),navs:[{label:u.$trans("communication.communication"),path:"Communication"},{label:u.$trans("communication.sms.sms"),path:"CommunicationSMSList"}]},{default:V(()=>[l(s,{name:"CommunicationSMS",title:u.$trans("communication.sms.sms"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(t,{appear:"",visibility:!0},{default:V(()=>[l(L)]),_:1})],64)}}});export{D as default};
