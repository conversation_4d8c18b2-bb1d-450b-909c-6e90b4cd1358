import{H as v,l as U,r as u,q as I,o as p,w as g,e as r,d as i,a as m,b,f as a}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-6"},B={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},O={key:0,class:"col-span-3"},G={key:1,class:"col-span-3"},J={key:2,class:"col-span-3"},_={name:"ConfigFeature"},E=Object.assign(_,{setup(L){const c="config/",t=v(c),f={enableTodo:!1,enableBackup:!1,enableActivityLog:!1,enableGuestPayment:!1,guestPaymentInstruction:"",enableOnlineRegistration:!1,onlineRegistrationInstruction:"",enableJobApplication:!1,jobApplicationInstruction:"",type:"feature"},o=U({...f});return(l,e)=>{const V=u("CardHeader"),s=u("BaseSwitch"),d=u("BaseEditor"),y=u("FormAction"),A=u("ConfigPage");return p(),I(A,null,{default:g(()=>[r(y,{"no-card":"","init-url":c,"data-fetch":"feature","init-form":f,form:o,action:"store","stay-on":"",redirect:"Config"},{default:g(()=>[r(V,{first:"",title:l.$trans("config.feature.feature_config"),description:l.$trans("config.feature.feature_info")},null,8,["title","description"]),i("div",P,[i("div",B,[r(s,{modelValue:o.enableTodo,"onUpdate:modelValue":e[0]||(e[0]=n=>o.enableTodo=n),name:"enableTodo",label:l.$trans("config.feature.props.todo"),error:a(t).enableTodo,"onUpdate:error":e[1]||(e[1]=n=>a(t).enableTodo=n)},null,8,["modelValue","label","error"])]),i("div",k,[r(s,{modelValue:o.enableBackup,"onUpdate:modelValue":e[2]||(e[2]=n=>o.enableBackup=n),name:"enableBackup",label:l.$trans("config.feature.props.backup"),error:a(t).enableBackup,"onUpdate:error":e[3]||(e[3]=n=>a(t).enableBackup=n)},null,8,["modelValue","label","error"])]),i("div",R,[r(s,{modelValue:o.enableActivityLog,"onUpdate:modelValue":e[4]||(e[4]=n=>o.enableActivityLog=n),name:"enableActivityLog",label:l.$trans("config.feature.props.activity_log"),error:a(t).enableActivityLog,"onUpdate:error":e[5]||(e[5]=n=>a(t).enableActivityLog=n)},null,8,["modelValue","label","error"])]),i("div",$,[r(s,{modelValue:o.enableGuestPayment,"onUpdate:modelValue":e[6]||(e[6]=n=>o.enableGuestPayment=n),name:"enableGuestPayment",label:l.$trans("config.feature.props.guest_payment"),error:a(t).enableGuestPayment,"onUpdate:error":e[7]||(e[7]=n=>a(t).enableGuestPayment=n)},null,8,["modelValue","label","error"])]),i("div",j,[r(s,{modelValue:o.enableOnlineRegistration,"onUpdate:modelValue":e[8]||(e[8]=n=>o.enableOnlineRegistration=n),name:"enableOnlineRegistration",label:l.$trans("config.feature.props.online_registration"),error:a(t).enableOnlineRegistration,"onUpdate:error":e[9]||(e[9]=n=>a(t).enableOnlineRegistration=n)},null,8,["modelValue","label","error"])]),i("div",C,[r(s,{modelValue:o.enableJobApplication,"onUpdate:modelValue":e[10]||(e[10]=n=>o.enableJobApplication=n),name:"enableJobApplication",label:l.$trans("config.feature.props.job_application"),error:a(t).enableJobApplication,"onUpdate:error":e[11]||(e[11]=n=>a(t).enableJobApplication=n)},null,8,["modelValue","label","error"])]),o.enableGuestPayment?(p(),m("div",O,[r(d,{modelValue:o.guestPaymentInstruction,"onUpdate:modelValue":e[12]||(e[12]=n=>o.guestPaymentInstruction=n),name:"guestPaymentInstruction",edit:!0,toolbar:"minimal",label:l.$trans("config.feature.props.guest_payment_instruction"),error:a(t).guestPaymentInstruction,"onUpdate:error":e[13]||(e[13]=n=>a(t).guestPaymentInstruction=n)},null,8,["modelValue","label","error"])])):b("",!0),o.enableOnlineRegistration?(p(),m("div",G,[r(d,{modelValue:o.onlineRegistrationInstruction,"onUpdate:modelValue":e[14]||(e[14]=n=>o.onlineRegistrationInstruction=n),name:"onlineRegistrationInstruction",edit:!0,toolbar:"minimal",label:l.$trans("config.feature.props.online_registration_instruction"),error:a(t).onlineRegistrationInstruction,"onUpdate:error":e[15]||(e[15]=n=>a(t).onlineRegistrationInstruction=n)},null,8,["modelValue","label","error"])])):b("",!0),o.enableJobApplication?(p(),m("div",J,[r(d,{modelValue:o.jobApplicationInstruction,"onUpdate:modelValue":e[16]||(e[16]=n=>o.jobApplicationInstruction=n),name:"jobApplicationInstruction",edit:!0,toolbar:"minimal",label:l.$trans("config.feature.props.job_application_instruction"),error:a(t).jobApplicationInstruction,"onUpdate:error":e[17]||(e[17]=n=>a(t).jobApplicationInstruction=n)},null,8,["modelValue","label","error"])])):b("",!0)])]),_:1},8,["form"])]),_:1})}}});export{E as default};
