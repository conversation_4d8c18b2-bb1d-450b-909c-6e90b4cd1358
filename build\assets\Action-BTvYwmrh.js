import{u as R,h as T,i as M,G as B,H as h,g as q,m as D,l as _,r as d,q as A,o as b,w as f,d as n,a as k,e as l,f as t,b as E,s as O,t as v,J as L,F as N}from"./app-BAwPsakn.js";const I={class:"grid grid-cols-4 gap-6"},G={class:"col-span-4"},J={class:"col-span-4 sm:col-span-1"},W={class:"col-span-4 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},K={class:"col-span-4 sm:col-span-1"},Q={class:"col-span-4 sm:col-span-1"},X={key:0,class:"col-span-4 sm:col-span-2"},Y={key:1,class:"col-span-4 sm:col-span-2"},Z={class:"col-span-4 sm:col-span-1"},x={class:"col-span-4"},ee={class:"mt-4 grid grid-cols-1"},se={class:"col"},oe={name:"ResourceOnlineClassForm"},te=Object.assign(oe,{setup(y){const p=R();T(),M();const i={topic:"",batches:[],subject:"",platform:"",meetingCode:"",url:"",password:"",description:"",media:[],mediaUpdated:!1,mediaToken:B(),mediaHash:[]},j="resource/onlineClass/",a=h(j),C=q("resource.onlineClassUseMeetingCode");D(!1);const m=_({subjects:[],platforms:[]});_({selectedBatch:null,subjects:[]});const o=_({...i}),V=_({batches:[],subject:"",isLoaded:!p.params.uuid}),F=r=>{Object.assign(m,r)},S=()=>{o.mediaToken=B(),o.mediaHash=[]},w=r=>{var u,g,c,U;let s=r.records.map($=>$.batch.uuid)||[];Object.assign(i,{...r,batches:s,subject:((g=(u=r.records[0])==null?void 0:u.subject)==null?void 0:g.uuid)||"",startAt:((c=r.startAt)==null?void 0:c.at)||"",platform:((U=r.platform)==null?void 0:U.value)||""}),Object.assign(o,L(i)),V.batches=s,V.isLoaded=!0};return(r,s)=>{const u=d("BaseInput"),g=d("BaseSelectSearch"),c=d("BaseSelect"),U=d("DatePicker"),$=d("BaseEditor"),H=d("MediaUpload"),P=d("FormAction");return b(),A(P,{"pre-requisites":!0,onSetPreRequisites:F,"init-url":j,"init-form":i,form:o,setForm:w,redirect:"ResourceOnlineClass",onResetMediaFiles:S},{default:f(()=>[n("div",I,[n("div",G,[l(u,{type:"text",modelValue:o.topic,"onUpdate:modelValue":s[0]||(s[0]=e=>o.topic=e),name:"topic",label:r.$trans("resource.online_class.props.topic"),error:t(a).topic,"onUpdate:error":s[1]||(s[1]=e=>t(a).topic=e),autofocus:""},null,8,["modelValue","label","error"])]),n("div",J,[V.isLoaded?(b(),A(g,{key:0,multiple:"",name:"batches",label:r.$trans("academic.batch.batch"),modelValue:o.batches,"onUpdate:modelValue":s[2]||(s[2]=e=>o.batches=e),error:t(a).batches,"onUpdate:error":s[3]||(s[3]=e=>t(a).batches=e),"value-prop":"uuid","init-search":V.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:f(e=>[O(v(e.value.course.name)+" - "+v(e.value.name),1)]),listOption:f(e=>[O(v(e.option.course.nameWithTerm)+" - "+v(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):E("",!0)]),n("div",W,[l(c,{modelValue:o.subject,"onUpdate:modelValue":s[4]||(s[4]=e=>o.subject=e),name:"subject",label:r.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:m.subjects,error:t(a).subject,"onUpdate:error":s[5]||(s[5]=e=>t(a).subject=e)},null,8,["modelValue","label","options","error"])]),n("div",z,[l(U,{as:"datetime",modelValue:o.startAt,"onUpdate:modelValue":s[6]||(s[6]=e=>o.startAt=e),name:"startAt",label:r.$trans("resource.online_class.props.start_at"),"no-clear":"",error:t(a).startAt,"onUpdate:error":s[7]||(s[7]=e=>t(a).startAt=e)},null,8,["modelValue","label","error"])]),n("div",K,[l(u,{type:"text",modelValue:o.duration,"onUpdate:modelValue":s[8]||(s[8]=e=>o.duration=e),name:"duration",label:r.$trans("resource.online_class.props.duration"),error:t(a).duration,"onUpdate:error":s[9]||(s[9]=e=>t(a).duration=e)},null,8,["modelValue","label","error"])]),n("div",Q,[l(c,{modelValue:o.platform,"onUpdate:modelValue":s[10]||(s[10]=e=>o.platform=e),name:"platform",label:r.$trans("resource.online_class.props.platform"),options:m.platforms,error:t(a).platform,"onUpdate:error":s[11]||(s[11]=e=>t(a).platform=e)},null,8,["modelValue","label","options","error"])]),t(C)?(b(),k("div",X,[l(u,{type:"text",modelValue:o.meetingCode,"onUpdate:modelValue":s[12]||(s[12]=e=>o.meetingCode=e),name:"meetingCode",label:r.$trans("resource.online_class.props.meeting_code"),error:t(a).meetingCode,"onUpdate:error":s[13]||(s[13]=e=>t(a).meetingCode=e)},null,8,["modelValue","label","error"])])):(b(),k("div",Y,[l(u,{type:"text",modelValue:o.url,"onUpdate:modelValue":s[14]||(s[14]=e=>o.url=e),name:"url",label:r.$trans("resource.online_class.props.url"),error:t(a).url,"onUpdate:error":s[15]||(s[15]=e=>t(a).url=e)},null,8,["modelValue","label","error"])])),n("div",Z,[l(u,{type:"text",modelValue:o.password,"onUpdate:modelValue":s[16]||(s[16]=e=>o.password=e),name:"password",label:r.$trans("resource.online_class.props.password"),error:t(a).password,"onUpdate:error":s[17]||(s[17]=e=>t(a).password=e)},null,8,["modelValue","label","error"])]),n("div",x,[l($,{modelValue:o.description,"onUpdate:modelValue":s[18]||(s[18]=e=>o.description=e),name:"description",edit:!!t(p).params.uuid,label:r.$trans("resource.online_class.props.description"),error:t(a).description,"onUpdate:error":s[19]||(s[19]=e=>t(a).description=e)},null,8,["modelValue","edit","label","error"])])]),n("div",ee,[n("div",se,[l(H,{multiple:"",label:r.$trans("general.file"),module:"online_class",media:o.media,"media-token":o.mediaToken,onIsUpdated:s[20]||(s[20]=e=>o.mediaUpdated=!0),onSetHash:s[21]||(s[21]=e=>o.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),ae={name:"ResourceOnlineClassAction"},le=Object.assign(ae,{setup(y){const p=R();return(i,j)=>{const a=d("PageHeaderAction"),C=d("PageHeader"),m=d("ParentTransition");return b(),k(N,null,[l(C,{title:i.$trans(t(p).meta.trans,{attribute:i.$trans(t(p).meta.label)}),navs:[{label:i.$trans("resource.resource"),path:"Resource"},{label:i.$trans("resource.online_class.online_class"),path:"ResourceOnlineClassList"}]},{default:f(()=>[l(a,{name:"ResourceOnlineClass",title:i.$trans("resource.online_class.online_class"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(m,{appear:"",visibility:!0},{default:f(()=>[l(te)]),_:1})],64)}}});export{le as default};
