import{u as A,j as B,y as C,r as u,a as P,o as r,q as m,b,e as n,w as s,f as e,d as w,s as l,t as o,F as D}from"./app-BAwPsakn.js";import{_ as N}from"./EditRequestInfo-8GzviTVS.js";const S={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},V={name:"ContactShowContact"},H=Object.assign(V,{props:{student:{type:Object,default(){return{}}}},setup(t){const p=A(),a=B("$trans"),f=t;let i=[];return C("student:edit")&&i.push({label:a("general.edit"),path:{name:"StudentEditContact",params:{uuid:f.student.uuid}}}),(k,x)=>{const g=u("PageHeaderAction"),y=u("PageHeader"),c=u("BaseDataView"),_=u("BaseCard"),h=u("ParentTransition");return r(),P(D,null,[t.student.uuid?(r(),m(y,{key:0,title:e(a)(e(p).meta.label),navs:[{label:e(a)("student.student"),path:"Student"},{label:t.student.contact.name,path:{name:"StudentShow",params:{uuid:t.student.uuid}}}]},{default:s(()=>[n(g,{"additional-actions":e(i)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):b("",!0),n(h,{appear:"",visibility:!0},{default:s(()=>[t.student.uuid?(r(),m(_,{key:0},{default:s(()=>[n(N,{student:t.student},null,8,["student"]),w("dl",S,[n(c,{label:e(a)("contact.props.contact_number")},{default:s(()=>[l(o(t.student.contact.contactNumber),1)]),_:1},8,["label"]),n(c,{label:e(a)("global.alternate",{attribute:e(a)("contact.props.contact_number")})},{default:s(()=>{var d;return[l(o((d=t.student.contact.alternateRecords)==null?void 0:d.contactNumber),1)]}),_:1},8,["label"]),n(c),n(c,{label:e(a)("contact.props.email")},{default:s(()=>[l(o(t.student.contact.email),1)]),_:1},8,["label"]),n(c,{label:e(a)("global.alternate",{attribute:e(a)("contact.props.email")})},{default:s(()=>{var d;return[l(o((d=t.student.contact.alternateRecords)==null?void 0:d.email),1)]}),_:1},8,["label"]),n(c),n(c,{class:"col-span-1 sm:col-span-3",label:e(a)("contact.props.present_address")},{default:s(()=>[l(o(t.student.contact.presentAddressDisplay),1)]),_:1},8,["label"]),n(c,{class:"col-span-1 sm:col-span-3",label:e(a)("contact.props.permanent_address")},{default:s(()=>[l(o(t.student.contact.sameAsPresentAddress?t.student.contact.presentAddressDisplay:t.student.contact.permanentAddressDisplay),1)]),_:1},8,["label"])])]),_:1})):b("",!0)]),_:1})],64)}}});export{H as default};
