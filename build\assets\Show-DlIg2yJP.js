import{i as K,u as Q,h as W,j as X,g as b,H as Y,l as V,m as Z,r,a as x,o as d,e as o,w as a,f as n,q as m,b as p,d as g,s as u,t as s,y as h,F as ee}from"./app-BAwPsakn.js";const ae={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},te={class:"text-danger"},ne={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3"},se={name:"EmployeeAccountShow"},ue=Object.assign(se,{props:{employee:{type:Object,default(){return{}}}},setup(f){K();const k=Q(),$=W();X("emitter");const A={},C={status:"",comment:""},B="employee/account/",w=b("finance.enableBankCode1"),E=b("finance.enableBankCode2"),j=b("finance.enableBankCode3"),L=b("finance.bankCode1Label"),U=b("finance.bankCode2Label"),F=b("finance.bankCode3Label"),v=Y(B),y=V({...C}),_=Z(!1),e=V({...A}),N=t=>{Object.assign(e,t)},P=()=>{_.value=!0};return(t,l)=>{const T=r("PageHeaderAction"),H=r("PageHeader"),I=r("BaseBadge"),i=r("BaseDataView"),R=r("ListMedia"),D=r("BaseButton"),O=r("ShowButton"),S=r("BaseCard"),M=r("BaseSelect"),q=r("BaseTextarea"),z=r("FormAction"),G=r("ShowItem"),J=r("ParentTransition");return d(),x(ee,null,[o(H,{title:t.$trans(n(k).meta.trans,{attribute:t.$trans(n(k).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:f.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:f.employee.uuid}}}]},{default:a(()=>[o(T,{name:"EmployeeAccount",title:t.$trans("finance.account.account"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(J,{appear:"",visibility:!0},{default:a(()=>[o(G,{"init-url":B,uuid:n(k).params.uuid,"module-uuid":n(k).params.muuid,onSetItem:N,onRedirectTo:l[5]||(l[5]=c=>n($).push({name:"EmployeeAccount",params:{uuid:f.employee.uuid}})),refresh:_.value,onRefreshed:l[6]||(l[6]=c=>_.value=!1)},{default:a(()=>[e.uuid?(d(),m(S,{key:0},{title:a(()=>[u(s(e.name),1)]),footer:a(()=>[o(O,null,{default:a(()=>[n(h)("employee:edit")?(d(),m(D,{key:0,design:"primary",onClick:l[0]||(l[0]=c=>n($).push({name:"EmployeeAccountEdit",params:{uuid:f.employee.uuid,muuid:e.uuid}}))},{default:a(()=>[u(s(t.$trans("general.edit")),1)]),_:1})):p("",!0)]),_:1})]),default:a(()=>[g("dl",ae,[o(i,{label:t.$trans("finance.account.props.alias")},{default:a(()=>[u(s(e.alias)+" ",1),e.selfUpload?(d(),m(I,{key:0,design:e.verificationStatus.color},{default:a(()=>[u(s(e.verificationStatus.label),1)]),_:1},8,["design"])):p("",!0)]),_:1},8,["label"]),o(i,{label:t.$trans("finance.account.props.number")},{default:a(()=>[u(s(e.number),1)]),_:1},8,["label"]),o(i,{label:t.$trans("finance.account.props.bank_name")},{default:a(()=>[u(s(e.bankName),1)]),_:1},8,["label"]),o(i,{label:t.$trans("finance.account.props.branch_name")},{default:a(()=>[u(s(e.branchName),1)]),_:1},8,["label"]),n(w)?(d(),m(i,{key:0,label:n(L)},{default:a(()=>[u(s(e.bankCode1),1)]),_:1},8,["label"])):p("",!0),n(E)?(d(),m(i,{key:1,label:n(U)},{default:a(()=>[u(s(e.bankCode2),1)]),_:1},8,["label"])):p("",!0),n(j)?(d(),m(i,{key:2,label:n(F)},{default:a(()=>[u(s(e.bankCode3),1)]),_:1},8,["label"])):p("",!0),e.selfUpload&&e.verificationStatus.value=="rejected"?(d(),m(i,{key:3,class:"col-span-1 sm:col-span-2",label:t.$trans("contact.verification.props.comment")},{default:a(()=>[g("span",te,s(e.comment),1)]),_:1},8,["label"])):p("",!0),o(i,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[o(R,{media:e.media,url:`/app/employees/${f.employee.uuid}/accounts/${e.uuid}/`},null,8,["media","url"])]),_:1}),o(i,{label:t.$trans("general.created_at")},{default:a(()=>[u(s(e.createdAt.formatted),1)]),_:1},8,["label"]),o(i,{label:t.$trans("general.updated_at")},{default:a(()=>[u(s(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):p("",!0),n(h)("employee:self-service-action")&&e.uuid&&e.selfUpload&&e.verificationStatus.value=="pending"?(d(),m(S,{key:1},{title:a(()=>[u(s(t.$trans("contact.verification.props.action")),1)]),default:a(()=>[o(z,{"no-card":"","keep-adding":!1,"init-url":B,uuid:f.employee.uuid,"module-uuid":e.uuid,"no-data-fetch":!0,action:"action","init-form":C,form:y,"after-submit":P},{default:a(()=>[g("div",ne,[g("div",oe,[o(M,{name:"status",label:t.$trans("global.select",{attribute:t.$trans("contact.verification.props.status")}),modelValue:y.status,"onUpdate:modelValue":l[1]||(l[1]=c=>y.status=c),options:[{value:"verify",label:t.$trans("contact.verification.action.verify")},{value:"reject",label:t.$trans("contact.verification.action.reject")}],error:n(v).status,"onUpdate:error":l[2]||(l[2]=c=>n(v).status=c)},null,8,["label","modelValue","options","error"])]),g("div",le,[o(q,{modelValue:y.comment,"onUpdate:modelValue":l[3]||(l[3]=c=>y.comment=c),name:"comment",label:t.$trans("contact.comment"),error:n(v).comment,"onUpdate:error":l[4]||(l[4]=c=>n(v).comment=c)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form"])]),_:1})):p("",!0)]),_:1},8,["uuid","module-uuid","refresh"])]),_:1})],64)}}});export{ue as default};
