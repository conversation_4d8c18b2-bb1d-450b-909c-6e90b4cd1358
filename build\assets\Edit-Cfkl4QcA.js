import{u as Y,h as Z,i as I,H as x,m as ee,l as N,n as ae,r as p,a as u,o as r,q as M,b as _,e as i,w as b,f as n,F as $,v as w,x as D,d,s as H,t as h,J as se}from"./app-BAwPsakn.js";const te={class:"col-span-1"},oe={class:"grid grid-cols-3 gap-2"},le={class:"col-span-3 sm:col-span-2 lg:col-span-1"},ne={class:"col-span-3 sm:col-span-2 lg:col-span-1"},re={key:0,class:"col-span-3 sm:col-span-2 lg:col-span-1"},ue={class:"flex-row space-y-2"},ie={key:0,class:"col-span-3 sm:col-span-2 lg:col-span-1"},ce={class:"flex-row space-y-2"},pe={key:0,class:"mt-4 grid grid-cols-3 gap-2"},de={class:"col-span-3 sm:col-span-2 lg:col-span-1"},me={key:0,class:"col-span-3 sm:col-span-2 lg:col-span-1"},be={class:"mt-4 grid grid-cols-3 gap-2"},_e={class:"col-span-3 sm:col-span-2 lg:col-span-1"},$e={class:"col-span-3 sm:col-span-2 lg:col-span-1"},he={class:"col-span-3 sm:col-span-2 lg:col-span-1"},fe={name:"StudentEditFee"},Ve=Object.assign(fe,{props:{student:{type:Object,default(){return{}}}},setup(F){const V=Y(),z=Z(),J=I(),R=F,C={feeGroups:[]},j="student/fee/",c=x(j),L=ee(!1),v=N({directions:[],transportCircles:[],feeConcessions:[],frequencies:[]}),P=N({...C}),K=[],Q=o=>{Object.assign(v,o)},W=async()=>{L.value=!0,await J.dispatch("student/fee/fetch",{uuid:V.params.uuid}).then(o=>{let T=[];o.feeGroups.forEach(g=>{let S=[];g.fees.forEach(t=>{var q,f,A,U;t.hasTransportFee=t.hasTransportFee,t.transportCircle=((q=t.transportCircle)==null?void 0:q.uuid)||"",t.concession=((f=t.concession)==null?void 0:f.uuid)||"",t.hasLateFee=!!t.lateFee.applicable,t.lateFeeType=t.lateFee.type||"amount",t.lateFeeFrequency=((A=t.lateFee.frequency)==null?void 0:A.value)||"",t.lateFeeValue=((U=t.lateFee.value)==null?void 0:U.value)||"",t.dueDate=t.dueDate.value,t.records.forEach(m=>{var y,B,E;m.isOptional?m.isApplicable?m.customAmount=((y=m.amount)==null?void 0:y.value)||0:m.customAmount=((B=m.installmentAmount)==null?void 0:B.value)||0:m.customAmount=((E=m.amount)==null?void 0:E.value)||0}),t.isEditable=t.status.value=="unpaid"||t.status.value=="not_applicable",S.push(t)}),T.push({uuid:g.uuid,name:g.name,fees:S})}),C.feeGroups=T,Object.assign(P,se(C)),L.value=!1}).catch(o=>{L.value=!1})};return ae(async()=>{if(!R.student.hasFeeStructureSet){z.push({name:"StudentShowFee",params:{uuid:R.student.uuid}});return}await W()}),(o,T)=>{const g=p("PageHeaderAction"),S=p("PageHeader"),t=p("BaseHeading"),q=p("DatePicker"),f=p("BaseSelect"),A=p("BaseLabel"),U=p("BaseCheckbox"),m=p("TextMuted"),y=p("BaseInput"),B=p("BaseFieldset"),E=p("FormAction"),X=p("ParentTransition");return r(),u($,null,[F.student.uuid?(r(),M(S,{key:0,title:o.$trans(n(V).meta.trans,{attribute:o.$trans(n(V).meta.label)}),navs:[{label:o.$trans("student.student"),path:"Student"},{label:F.student.contact.name,path:{name:"StudentShow",params:{uuid:F.student.uuid}}},{label:o.$trans("finance.fee.fee"),path:{name:"StudentShowFee",params:{uuid:F.student.uuid}}}]},{default:b(()=>[i(g,{"additional-actions":K})]),_:1},8,["title","navs"])):_("",!0),i(X,{appear:"",visibility:!0},{default:b(()=>[i(E,{"pre-requisites":{uuid:n(V).params.uuid},onSetPreRequisites:Q,"no-data-fetch":"","init-url":j,action:"update","init-form":C,form:P,"keep-adding":!1,redirect:{name:"StudentShowFee",params:{uuid:n(V).params.uuid}},confirmation:""},{default:b(()=>[(r(!0),u($,null,w(P.feeGroups,(O,s)=>(r(),u("div",{class:D(["grid grid-cols-1",{"mt-4":s>0}]),key:O.uuid},[d("div",te,[i(t,null,{default:b(()=>[H(h(O.name),1)]),_:2},1024),(r(!0),u($,null,w(O.fees,(a,l)=>(r(),M(B,{class:"mt-4",key:a.uuid},{legend:b(()=>[H(h(a.title)+" ",1),d("span",{class:D({"text-danger":a.status.color=="danger","text-success":a.status.color=="success","text-warning":a.status.color=="warning","text-primary":a.status.color=="primary"})},"("+h(a.status.label)+")",3)]),default:b(()=>[d("div",oe,[d("div",le,[i(q,{modelValue:a.dueDate,"onUpdate:modelValue":e=>a.dueDate=e,name:`feeGroups.${s}.fees.${l}.dueDate`,label:o.$trans("finance.fee_structure.props.due_date"),"no-clear":"",error:n(c)[`feeGroups.${s}.fees.${l}.dueDate`],"onUpdate:error":e=>n(c)[`feeGroups.${s}.fees.${l}.dueDate`]=e,disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","disabled"])]),d("div",ne,[i(f,{modelValue:a.concession,"onUpdate:modelValue":e=>a.concession=e,name:`feeGroups.${s}.fees.${l}.concession`,label:o.$trans("finance.fee_concession.fee_concession"),options:v.feeConcessions,"label-prop":"name","value-prop":"uuid",error:n(c)[`feeGroups.${s}.fees.${l}.concession`],"onUpdate:error":e=>n(c)[`feeGroups.${s}.fees.${l}.concession`]=e,disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error","disabled"])]),(r(!0),u($,null,w(a.records,(e,G)=>(r(),u($,null,[e.isOptional?(r(),u("div",re,[d("div",ue,[i(A,null,{default:b(()=>[H(h(e.head.name),1)]),_:2},1024),i(U,{modelValue:e.isApplicable,"onUpdate:modelValue":k=>e.isApplicable=k,name:`feeGroups.${s}.fees.${l}.hasLateFee.${G}.isApplicable`,label:o.$trans("global.is",{attribute:o.$trans("finance.fee_structure.props.applicable")}),disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","disabled"])])])):_("",!0)],64))),256)),(r(!0),u($,null,w(a.records,(e,G)=>(r(),u($,null,[!e.isOptional||e.isOptional&&e.isApplicable?(r(),u("div",ie,[d("div",ce,[i(y,{modelValue:e.customAmount,"onUpdate:modelValue":k=>e.customAmount=k,name:`feeGroups.${s}.fees.${G}.amount`,label:e.head.name,currency:"",error:n(c)[`feeGroups.${s}.fees.${G}.amount`],"onUpdate:error":k=>n(c)[`feeGroups.${s}.fees.${G}.amount`]=k},{"additional-label":b(()=>[i(m,null,{default:b(()=>[e.paid.value>0?(r(),u("span",{key:0,class:D({"text-success":e.paid.value>0})},h(o.$trans("finance.fee.paid"))+" "+h(e.paid.formatted),3)):_("",!0),e.concession.value>0?(r(),u("span",{key:1,class:D({"text-danger":e.concession.value>0})},h(o.$trans("finance.fee_concession.fee_concession"))+" "+h(e.concession.formatted),3)):_("",!0)]),_:2},1024)]),_:2},1032,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])):_("",!0)],64))),256))]),a.hasTransportFee?(r(),u("div",pe,[d("div",de,[i(f,{modelValue:a.transportCircle,"onUpdate:modelValue":e=>a.transportCircle=e,name:`feeGroups.${s}.fees.${l}.transportCircle`,label:o.$trans("transport.circle.circle"),options:v.transportCircles,"label-prop":"name","value-prop":"uuid",error:n(c)[`feeGroups.${s}.fees.${l}.transportCircle`],"onUpdate:error":e=>n(c)[`feeGroups.${s}.fees.${l}.transportCircle`]=e,disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error","disabled"])]),a.transportCircle?(r(),u("div",me,[i(f,{modelValue:a.direction,"onUpdate:modelValue":e=>a.direction=e,name:`feeGroups.${s}.fees.${l}.direction`,label:o.$trans("transport.circle.direction"),options:v.directions,error:n(c)[`feeGroups.${s}.fees.${l}.direction`],"onUpdate:error":e=>n(c)[`feeGroups.${s}.fees.${l}.direction`]=e,disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error","disabled"])])):_("",!0)])):_("",!0),d("div",be,[d("div",_e,[i(U,{modelValue:a.hasLateFee,"onUpdate:modelValue":e=>a.hasLateFee=e,name:`feeGroups.${s}.fees.${l}.hasLateFee`,label:o.$trans("finance.fee_structure.props.has_late_fee"),disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","disabled"])]),a.hasLateFee?(r(),u($,{key:0},[d("div",$e,[i(f,{modelValue:a.lateFeeFrequency,"onUpdate:modelValue":e=>a.lateFeeFrequency=e,name:`feeGroups.${s}.fees.${l}.lateFeeFrequency`,label:o.$trans("finance.fee_structure.props.late_fee_frequency"),options:v.frequencies,"label-prop":"label","value-prop":"value",error:n(c)[`feeGroups.${s}.fees.${l}.lateFeeFrequency`],"onUpdate:error":e=>n(c)[`feeGroups.${s}.fees.${l}.lateFeeFrequency`]=e,disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error","disabled"])]),d("div",he,[i(y,{modelValue:a.lateFeeValue,"onUpdate:modelValue":e=>a.lateFeeValue=e,name:`feeGroups.${s}.fees.${l}.lateFeeValue`,label:o.$trans("finance.fee_structure.props.late_fee_value"),currency:"",error:n(c)[`feeGroups.${s}.fees.${l}.lateFeeValue`],"onUpdate:error":e=>n(c)[`feeGroups.${s}.fees.${l}.lateFeeValue`]=e,disabled:!a.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","disabled"])])],64)):_("",!0)])]),_:2},1024))),128))])],2))),128))]),_:1},8,["pre-requisites","form","redirect"])]),_:1})],64)}}});export{Ve as default};
