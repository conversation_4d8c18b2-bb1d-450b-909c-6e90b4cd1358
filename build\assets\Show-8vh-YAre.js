import{i as v,u as k,h as C,l as P,r as o,a as V,o as i,e as a,w as t,f as l,q as c,b as _,d as A,s,t as r,y as H,F as I}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"EmployeeDepartmentShow"},F=Object.assign(T,{setup(R){v();const d=k(),u=C(),f={},y="employee/department/",n=P({...f}),b=e=>{Object.assign(n,e)};return(e,m)=>{const g=o("PageHeaderAction"),B=o("PageHeader"),p=o("BaseDataView"),$=o("BaseButton"),h=o("ShowButton"),w=o("BaseCard"),D=o("ShowItem"),E=o("ParentTransition");return i(),V(I,null,[a(B,{title:e.$trans(l(d).meta.trans,{attribute:e.$trans(l(d).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.department.department"),path:"EmployeeDepartmentList"}]},{default:t(()=>[a(g,{name:"EmployeeDepartment",title:e.$trans("employee.department.department"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(E,{appear:"",visibility:!0},{default:t(()=>[a(D,{"init-url":y,uuid:l(d).params.uuid,onSetItem:b,onRedirectTo:m[1]||(m[1]=S=>l(u).push({name:"EmployeeDepartment"}))},{default:t(()=>[n.uuid?(i(),c(w,{key:0},{title:t(()=>[s(r(n.name),1)]),footer:t(()=>[a(h,null,{default:t(()=>[l(H)("department:edit")?(i(),c($,{key:0,design:"primary",onClick:m[0]||(m[0]=S=>l(u).push({name:"EmployeeDepartmentEdit",params:{uuid:n.uuid}}))},{default:t(()=>[s(r(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:t(()=>[A("dl",N,[a(p,{label:e.$trans("employee.department.props.name")},{default:t(()=>[s(r(n.name),1)]),_:1},8,["label"]),a(p,{label:e.$trans("employee.department.props.alias")},{default:t(()=>[s(r(n.alias),1)]),_:1},8,["label"]),a(p,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.department.props.description")},{default:t(()=>[s(r(n.description),1)]),_:1},8,["label"]),a(p,{label:e.$trans("general.created_at")},{default:t(()=>[s(r(n.createdAt.formatted),1)]),_:1},8,["label"]),a(p,{label:e.$trans("general.updated_at")},{default:t(()=>[s(r(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
