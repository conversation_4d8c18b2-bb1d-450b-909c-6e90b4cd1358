import{u as T,l as y,n as M,r,q as m,o as u,w as e,d as B,e as o,h as j,j as E,y as _,m as N,f as n,a as S,F as O,v as U,s as c,t as d,b as k}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(D,{emit:l}){T();const F=l,v={name:""},f=y({...v}),b=y({isLoaded:!0});return M(async()=>{b.isLoaded=!0}),(p,i)=>{const C=r("BaseInput"),a=r("FilterForm");return u(),m(a,{"init-form":v,form:f,multiple:[],onHide:i[1]||(i[1]=s=>F("hide"))},{default:e(()=>[B("div",q,[B("div",z,[o(C,{type:"text",modelValue:f.name,"onUpdate:modelValue":i[0]||(i[0]=s=>f.name=s),name:"name",label:p.$trans("finance.fee_structure.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},J={name:"FinanceFeeStructureList"},Q=Object.assign(J,{setup(D){const l=j(),F=E("emitter");let v=["filter"];_("fee-structure:create")&&v.unshift("create");let f=[];_("fee-structure:export")&&(f=["print","pdf","excel"]);const b="finance/feeStructure/",p=N(!1),i=y({}),C=a=>{Object.assign(i,a)};return(a,s)=>{const I=r("PageHeaderAction"),V=r("PageHeader"),w=r("ParentTransition"),h=r("DataCell"),g=r("FloatingMenuItem"),A=r("FloatingMenu"),H=r("DataRow"),L=r("BaseButton"),P=r("DataTable"),R=r("ListItem");return u(),m(R,{"init-url":b,onSetItems:C},{header:e(()=>[o(V,{title:a.$trans("finance.fee_structure.fee_structure"),navs:[{label:a.$trans("finance.finance"),path:"Finance"}]},{default:e(()=>[o(I,{url:"finance/fee-structures/",name:"FinanceFeeStructure",title:a.$trans("finance.fee_structure.fee_structure"),actions:n(v),"dropdown-actions":n(f),onToggleFilter:s[0]||(s[0]=t=>p.value=!p.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(w,{appear:"",visibility:p.value},{default:e(()=>[o(G,{onRefresh:s[1]||(s[1]=t=>n(F).emit("listItems")),onHide:s[2]||(s[2]=t=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(w,{appear:"",visibility:!0},{default:e(()=>[o(P,{header:i.headers,meta:i.meta,module:"finance.fee_structure",onRefresh:s[4]||(s[4]=t=>n(F).emit("listItems"))},{actionButton:e(()=>[n(_)("fee-structure:create")?(u(),m(L,{key:0,onClick:s[3]||(s[3]=t=>n(l).push({name:"FinanceFeeStructureCreate"}))},{default:e(()=>[c(d(a.$trans("global.add",{attribute:a.$trans("finance.fee_structure.fee_structure")})),1)]),_:1})):k("",!0)]),default:e(()=>[(u(!0),S(O,null,U(i.data,t=>(u(),m(H,{key:t.uuid,onDoubleClick:$=>n(l).push({name:"FinanceFeeStructureShow",params:{uuid:t.uuid}})},{default:e(()=>[o(h,{name:"name"},{default:e(()=>[c(d(t.name),1)]),_:2},1024),o(h,{name:"createdAt"},{default:e(()=>[c(d(t.createdAt.formatted),1)]),_:2},1024),o(h,{name:"action"},{default:e(()=>[o(A,null,{default:e(()=>[o(g,{icon:"fas fa-arrow-circle-right",onClick:$=>n(l).push({name:"FinanceFeeStructureShow",params:{uuid:t.uuid}})},{default:e(()=>[c(d(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(_)("fee-structure:edit")&&t.isEditable?(u(),m(g,{key:0,icon:"fas fa-edit",onClick:$=>n(l).push({name:"FinanceFeeStructureEdit",params:{uuid:t.uuid}})},{default:e(()=>[c(d(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),n(_)("fee-structure:create")?(u(),m(g,{key:1,icon:"fas fa-copy",onClick:$=>n(l).push({name:"FinanceFeeStructureDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[c(d(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),n(_)("fee-structure:delete")&&t.isEditable?(u(),m(g,{key:2,icon:"fas fa-trash",onClick:$=>n(F).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[c(d(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
