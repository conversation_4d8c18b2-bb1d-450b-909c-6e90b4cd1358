import{u as ae,h as pe,i as ne,H as re,l as K,n as fe,r as D,q as C,o as l,w as n,d as m,b as y,e as r,f as e,I as ue,j as le,m as te,a as $,s as b,t as o,F as M,y as J,L as be,C as ge,v as X,G as oe,D as he,B as se,x as ce,c as de,K as ve,ag as ke,M as _e}from"./app-BAwPsakn.js";import{_ as $e}from"./OnlinePaymentForm-hfFN_8uD.js";import"./Billdesk-CH1h7WlK.js";const De={class:"mt-6 grid grid-cols-3 gap-4"},Ve={class:"col-span-3 sm:col-span-1"},Fe={class:"col-span-3 sm:col-span-1"},Ue={class:"col-span-3 sm:col-span-1"},Ce={class:"col-span-3 sm:col-span-1"},we={class:"col-span-3"},Pe={name:"StudentFeeEditPayment"},Be=Object.assign(Pe,{props:{transaction:{type:Object,default(){return{}}},preRequisites:{type:Object,default(){return{}}}},emits:["close","refresh"],setup(c,{emit:Y}){const x=ae();pe(),ne();const z=Y,I=c,F={date:"",codeNumber:"",ledger:"",paymentMethod:"",instrumentNumber:"",instrumentDate:"",clearingDate:"",bankDetail:"",referenceNumber:"",remarks:""},s="student/payment/",B=re(s),g=K({selectedPaymentMethod:{}}),p=K({...F}),d=P=>{p.paymentMethod=P?P.uuid:{},g.selectedPaymentMethod=P||{}},N=()=>{g.selectedPaymentMethod={},z("close"),z("refresh")};return fe(()=>{var A,R,V;let P=I.transaction,i=P.payments[0];F.codeNumber=P.codeNumber,F.date=(A=P.date)==null?void 0:A.value,F.ledger=i.ledgerUuid,F.paymentMethod=i.methodUuid,F.instrumentNumber=i.instrumentNumber||"",F.instrumentDate=((R=i.instrumentDate)==null?void 0:R.value)||"",F.clearingDate=((V=i.clearingDate)==null?void 0:V.value)||"",F.bankDetail=i.bankDetail||"",F.referenceNumber=i.referenceNumber||"",F.remarks=P.remarks,g.selectedPaymentMethod={uuid:i.methodUuid,name:i.methodName,hasBankDetail:i.hasBankDetail,hasInstrumentNumber:i.hasInstrumentNumber,hasInstrumentDate:i.hasInstrumentDate,hasClearingDate:i.hasClearingDate,hasReferenceNumber:i.hasReferenceNumber},Object.assign(p,F)}),(P,i)=>{const A=D("BaseInput"),R=D("DatePicker"),V=D("BaseSelect"),G=D("PaymentMethodInput"),w=D("BaseTextarea"),v=D("FormAction");return l(),C(v,{"no-card":"","no-data-fetch":"","is-modal":"",confirmation:"",uuid:e(x).params.uuid,"module-uuid":c.transaction.uuid,action:"updatePayment","init-url":s,"init-form":F,form:p,"keep-adding":!1,"cancel-action":!1,"submit-text":P.$trans("global.update",{attribute:P.$trans("student.payment.payment")}),onRedirectTo:N,onCancelled:N},{default:n(()=>[m("div",De,[m("div",Ve,[r(A,{modelValue:p.codeNumber,"onUpdate:modelValue":i[0]||(i[0]=t=>p.codeNumber=t),name:"codeNumber",label:P.$trans("finance.transaction.props.code_number"),error:e(B).codeNumber,"onUpdate:error":i[1]||(i[1]=t=>e(B).codeNumber=t)},null,8,["modelValue","label","error"])]),m("div",Fe,[r(R,{modelValue:p.date,"onUpdate:modelValue":i[2]||(i[2]=t=>p.date=t),name:"date","no-clear":"",label:P.$trans("student.fee.props.date"),error:e(B).date,"onUpdate:error":i[3]||(i[3]=t=>e(B).date=t)},null,8,["modelValue","label","error"])]),m("div",Ue,[r(V,{modelValue:p.ledger,"onUpdate:modelValue":i[4]||(i[4]=t=>p.ledger=t),name:"ledger",label:P.$trans("finance.ledger.ledger"),options:c.preRequisites.ledgers,"label-prop":"name","value-prop":"uuid",error:e(B).ledger,"onUpdate:error":i[5]||(i[5]=t=>e(B).ledger=t)},null,8,["modelValue","label","options","error"])]),m("div",Ce,[r(V,{modelValue:g.selectedPaymentMethod,"onUpdate:modelValue":i[6]||(i[6]=t=>g.selectedPaymentMethod=t),name:"paymentMethod",label:P.$trans("finance.payment_method.payment_method"),options:c.preRequisites.paymentMethods,"object-prop":!0,"label-prop":"name","value-prop":"uuid",error:e(B).paymentMethod,"onUpdate:error":i[7]||(i[7]=t=>e(B).paymentMethod=t),onChange:d},null,8,["modelValue","label","options","error"])]),g.selectedPaymentMethod?(l(),C(G,{key:0,"selected-payment-method":g.selectedPaymentMethod,instrumentNumber:p.instrumentNumber,"onUpdate:instrumentNumber":i[8]||(i[8]=t=>p.instrumentNumber=t),instrumentDate:p.instrumentDate,"onUpdate:instrumentDate":i[9]||(i[9]=t=>p.instrumentDate=t),clearingDate:p.clearingDate,"onUpdate:clearingDate":i[10]||(i[10]=t=>p.clearingDate=t),bankDetail:p.bankDetail,"onUpdate:bankDetail":i[11]||(i[11]=t=>p.bankDetail=t),referenceNumber:p.referenceNumber,"onUpdate:referenceNumber":i[12]||(i[12]=t=>p.referenceNumber=t),formErrors:e(B),"onUpdate:formErrors":i[13]||(i[13]=t=>ue(B)?B.value=t:null)},null,8,["selected-payment-method","instrumentNumber","instrumentDate","clearingDate","bankDetail","referenceNumber","formErrors"])):y("",!0),m("div",we,[r(w,{rows:1,modelValue:p.remarks,"onUpdate:modelValue":i[14]||(i[14]=t=>p.remarks=t),name:"remarks",label:P.$trans("student.payment.props.remarks"),error:e(B).remarks,"onUpdate:error":i[15]||(i[15]=t=>e(B).remarks=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form","submit-text"])}}}),je={key:0,class:"mb-4"},Se={class:"grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-8"},Me={class:"mt-8 grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-8"},Re={class:"mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4"},Ne={class:"col-span-3"},Ae={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},Te={class:"col-span-3"},Oe={key:1,class:"col-span-3"},Ee={key:3,class:"mt-8 flex justify-between"},Ge={class:"flex space-x-2 sm:space-x-4"},He={class:"flex space-x-2 sm:space-x-4"},Le={name:"StudentFeePaymentDetail"},xe=Object.assign(Le,{props:{visibility:{type:Boolean,default:!1},transaction:{type:Object,default(){return{}}},preRequisites:{type:Object,default(){return{}}}},emits:["close","refresh"],setup(c,{emit:Y}){const x=ae(),z=ne(),I=Y,F=c,s=le("$trans"),B={isRejected:!1,rejectedDate:"",rejectionCharge:"",customFeeHead:"",rejectionRemarks:"",cancellationRemarks:""},g="student/payment/",p=re(g),d=te(!1),N=te(!1),P=te(!1);K({});const i=K({isPaymentPreRequisiteSet:!1}),A=K({paymentMethods:[],ledgers:[]}),R=K({...B}),V=()=>{I("close")},G=async()=>{i.isPaymentPreRequisiteSet||await w(),N.value=!0},w=async()=>{d.value=!0,await z.dispatch("student/payment/preRequisite",{uuid:x.params.uuid}).then(a=>{Object.assign(A,a),i.isPaymentPreRequisiteSet=!0,d.value=!1}).catch(a=>{d.value=!1})},v=()=>{window.open(`/app/students/${x.params.uuid}/transactions/${F.transaction.uuid}/export?action=print`)},t=()=>{N.value=!1,P.value=!1,I("close"),I("refresh")};return(a,k)=>{const W=D("BaseBadge"),Q=D("BaseAlert"),O=D("BaseDataView"),ee=D("PaymentMethodDetail"),Z=D("BaseSwitch"),q=D("DatePicker"),T=D("BaseInput"),u=D("BaseSelect"),h=D("BaseTextarea"),j=D("FormAction"),f=D("BaseButton"),H=D("BaseModal");return c.transaction.uuid?(l(),C(H,{key:0,"is-loading":d.value,show:c.visibility,onClose:V},{title:n(()=>[b(o(e(s)("global.detail",{attribute:e(s)("student.payment.payment")}))+" ",1),c.transaction.isCancelled?(l(),C(W,{key:0,design:"danger"},{default:n(()=>[b(o(e(s)("general.cancelled")),1)]),_:1})):y("",!0),c.transaction.isRejected?(l(),C(W,{key:1,design:"warning"},{default:n(()=>[b(o(e(s)("general.rejected")),1)]),_:1})):y("",!0)]),default:n(()=>[c.transaction.records.length>1?(l(),$("div",je,[r(Q,{size:"xs",design:"info"},{default:n(()=>[b(o(e(s)("student.fee.multi_installment_payment_info")),1)]),_:1})])):y("",!0),m("dl",Se,[r(O,{label:e(s)("finance.transaction.props.code_number")},{default:n(()=>[b(o(c.transaction.codeNumber),1)]),_:1},8,["label"]),r(O,{label:e(s)("finance.transaction.props.date")},{default:n(()=>[b(o(c.transaction.date.formatted),1)]),_:1},8,["label"]),r(O,{label:e(s)("finance.transaction.props.amount")},{default:n(()=>[b(o(c.transaction.amount.formatted),1)]),_:1},8,["label"]),r(O,{label:e(s)("finance.payment_method.payment_method")},{default:n(()=>[r(ee,{payments:c.transaction.payments},null,8,["payments"])]),_:1},8,["label"])]),m("dl",Me,[c.transaction.remarks?(l(),C(O,{key:0,label:e(s)("student.payment.props.remarks"),class:"col-span-1 sm:col-span-3"},{default:n(()=>[b(o(c.transaction.remarks),1)]),_:1},8,["label"])):y("",!0),c.transaction.isRejected?(l(),$(M,{key:1},[r(O,{label:e(s)("finance.transaction.props.rejected_date")},{default:n(()=>[b(o(c.transaction.rejectedDate.formatted),1)]),_:1},8,["label"]),r(O,{label:e(s)("finance.transaction.props.rejection_charge")},{default:n(()=>[b(o(c.transaction.rejectionCharge.formatted),1)]),_:1},8,["label"]),r(O,{label:e(s)("finance.transaction.props.rejection_remarks"),class:"col-span-1 sm:col-span-3"},{default:n(()=>[b(o(c.transaction.rejectionRemarks),1)]),_:1},8,["label"])],64)):y("",!0),c.transaction.cancellationRemarks?(l(),C(O,{key:2,label:e(s)("finance.transaction.props.cancellation_remarks"),class:"col-span-1 sm:col-span-3"},{default:n(()=>[b(o(c.transaction.cancellationRemarks),1)]),_:1},8,["label"])):y("",!0),r(O,{label:e(s)("user.user")},{default:n(()=>[b(o(c.transaction.userName),1)]),_:1},8,["label"]),r(O,{label:e(s)("general.created_at")},{default:n(()=>[b(o(c.transaction.createdAt.formatted),1)]),_:1},8,["label"]),r(O,{label:e(s)("general.updated_at")},{default:n(()=>[b(o(c.transaction.updatedAt.formatted),1)]),_:1},8,["label"])]),P.value?(l(),C(j,{key:1,"no-card":"","no-data-fetch":"","is-modal":"",confirmation:"",uuid:e(x).params.uuid,"module-uuid":c.transaction.uuid,action:"cancelPayment","init-url":g,"init-form":B,form:R,"keep-adding":!1,"cancel-action":!1,"submit-text":e(s)("global.cancel",{attribute:e(s)("student.payment.payment")}),onRedirectTo:t,onCancelled:k[12]||(k[12]=U=>P.value=!1)},{default:n(()=>[m("div",Re,[m("div",Ne,[r(Z,{vertical:"",modelValue:R.isRejected,"onUpdate:modelValue":k[0]||(k[0]=U=>R.isRejected=U),name:"isRejected",label:e(s)("finance.transaction.props.payment_rejected"),error:e(p).isRejected,"onUpdate:error":k[1]||(k[1]=U=>e(p).isRejected=U)},null,8,["modelValue","label","error"])]),R.isRejected?(l(),$(M,{key:0},[m("div",Ae,[r(q,{modelValue:R.rejectedDate,"onUpdate:modelValue":k[2]||(k[2]=U=>R.rejectedDate=U),name:"rejectedDate",label:e(s)("finance.transaction.props.rejected_date"),"no-clear":"",error:e(p).rejectedDate,"onUpdate:error":k[3]||(k[3]=U=>e(p).rejectedDate=U)},null,8,["modelValue","label","error"])]),m("div",qe,[r(T,{currency:"",modelValue:R.rejectionCharge,"onUpdate:modelValue":k[4]||(k[4]=U=>R.rejectionCharge=U),name:"rejectionCharge",label:e(s)("finance.transaction.props.rejection_charge"),error:e(p).rejectionCharge,"onUpdate:error":k[5]||(k[5]=U=>e(p).rejectionCharge=U)},null,8,["modelValue","label","error"])]),m("div",Ie,[r(u,{modelValue:R.customFeeHead,"onUpdate:modelValue":k[6]||(k[6]=U=>R.customFeeHead=U),name:"customFeeHead",label:e(s)("student.fee.custom_fee"),"label-prop":"name","value-prop":"uuid",options:c.preRequisites.customFeeHeads,error:e(p).customFeeHead,"onUpdate:error":k[7]||(k[7]=U=>e(p).customFeeHead=U)},null,8,["modelValue","label","options","error"])]),m("div",Te,[r(h,{modelValue:R.rejectionRemarks,"onUpdate:modelValue":k[8]||(k[8]=U=>R.rejectionRemarks=U),name:"rejectionRemarks",label:e(s)("finance.transaction.props.rejection_remarks"),error:e(p).rejectionRemarks,"onUpdate:error":k[9]||(k[9]=U=>e(p).rejectionRemarks=U)},null,8,["modelValue","label","error"])])],64)):(l(),$("div",Oe,[r(h,{modelValue:R.cancellationRemarks,"onUpdate:modelValue":k[10]||(k[10]=U=>R.cancellationRemarks=U),name:"cancellationRemarks",label:e(s)("finance.transaction.props.cancellation_remarks"),error:e(p).cancellationRemarks,"onUpdate:error":k[11]||(k[11]=U=>e(p).cancellationRemarks=U)},null,8,["modelValue","label","error"])]))])]),_:1},8,["uuid","module-uuid","form","submit-text"])):y("",!0),N.value?(l(),C(Be,{key:2,transaction:c.transaction,"pre-requisites":A,onClose:k[13]||(k[13]=U=>N.value=!1),onRefresh:t},null,8,["transaction","pre-requisites"])):y("",!0),!P.value&&!N.value?(l(),$("div",Ee,[m("div",Ge,[e(J)("fee:cancel-payment")&&!c.transaction.isCancelled&&!c.transaction.isRejected&&!c.transaction.isOnline?(l(),C(f,{key:0,design:"danger",onClick:k[14]||(k[14]=U=>P.value=!0)},{default:n(()=>[b(o(e(s)("global.cancel",{attribute:e(s)("student.payment.payment")})),1)]),_:1})):y("",!0)]),m("div",He,[c.transaction.isEditable&&e(J)("fee:edit-payment")?(l(),C(f,{key:0,design:"primary",onClick:G},{default:n(()=>[b(o(e(s)("general.edit")),1)]),_:1})):y("",!0),r(f,{design:"success",onClick:v},{default:n(()=>[b(o(e(s)("general.print")),1)]),_:1})])])):y("",!0)]),_:1},8,["is-loading","show"])):y("",!0)}}}),ze={class:"grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-8 mb-4"},We={class:"grid grid-cols-3 gap-4"},Ke={class:"col-span-3 sm:col-span-1"},Je={key:0,class:"col-span-3 sm:col-span-1"},Qe={key:1,class:"col-span-3 sm:col-span-1"},Xe={class:"mt-4 grid grid-cols-3 gap-4"},Ye={class:"col-span-3 sm:col-span-1"},Ze={class:"col-span-3 sm:col-span-1"},et={class:"mt-4 grid grid-cols-3 gap-4"},tt={class:"col-span-3 sm:col-span-1"},at={class:"col-span-3 sm:col-span-1"},nt={class:"mt-4 grid grid-cols-3 gap-4"},st={class:"col-span-3 sm:col-span-1"},lt={class:"col-span-3 sm:col-span-1"},ot={class:"mt-4 grid grid-cols-3 gap-4"},rt={class:"col-span-3 sm:col-span-1"},it={class:"col-span-3 sm:col-span-1"},dt={class:"col-span-3 sm:col-span-1"},ut={class:"col-span-3"},mt={name:"StudentFeePaymentForm"},ct=Object.assign(mt,{props:{visibility:{type:Boolean,default:!1},date:{type:Object,default(){return{}}},fee:{type:Object,default(){return{}}},feeGroup:{type:Object,default(){return{}}},feeHead:{type:Object,default(){return{}}},preRequisites:{type:Object,default(){return{}}}},emits:["close","completed"],setup(c,{emit:Y}){const x=ae(),z=ne(),I=Y,F=c,s={codeNumber:"",feeHead:null,feeGroup:null,feeInstallment:null,additionalCharges:[],additionalDiscounts:[],date:"",lateFee:0,amount:"",ledger:"",paymentMethod:"",instrumentNumber:"",instrumentDate:"",clearingDate:"",bankDetail:"",referenceNumber:"",remarks:""},B="student/payment/",g=re(B),p=K({initialAmount:0,showAdditionalCharge:!1,showAdditionalDiscount:!1,selectedPaymentMethod:{}}),d=K({...s}),N=w=>{d.paymentMethod=w?w.uuid:{},p.selectedPaymentMethod=w||{}},P=()=>{A(),I("close")},i=()=>{A(),I("completed")},A=()=>{s.lateFee=0,s.date="",s.feeGroup=null,s.feeHead=null,s.feeInstallment=null,s.amount="",p.selectedPaymentMethod={},p.showAdditionalCharge=!1,p.showAdditionalDiscount=!1,Object.assign(d,s),z.dispatch(B+"resetFormErrors")},R=w=>{let v=parseFloat(p.initialAmount);d.lateFee&&(v+=parseFloat(d.lateFee)),d.additionalCharges.forEach(t=>{t.amount&&(v+=parseFloat(t.amount))}),d.additionalDiscounts.forEach(t=>{t.amount&&(v-=parseFloat(t.amount))}),d.amount=v},V=()=>{p.selectedPaymentMethod={},G(),Object.assign(d,s)},G=()=>{s.additionalCharges=[],s.additionalCharges.push({uuid:oe(),label:"",amount:0}),s.additionalDiscounts=[],s.additionalDiscounts.push({uuid:oe(),label:"",amount:0})};return be(()=>{var w,v,t;if(F.feeGroup.uuid){s.feeGroup=F.feeGroup.uuid;let a=F.feeGroup.lateFee.value;a>0&&(s.lateFee=a),s.amount=F.feeGroup.balance.value}else if(F.feeHead.uuid)s.feeHead=F.feeHead.uuid,s.amount=F.feeHead.balance.value;else if(F.fee.uuid){s.feeInstallment=F.fee.uuid;let a=((v=(w=F.fee.lateFee)==null?void 0:w.amount)==null?void 0:v.value)||0;a>0&&(s.lateFee=a),s.amount=F.fee.balance.value}p.initialAmount=s.amount-s.lateFee,s.date=(t=F.date)==null?void 0:t.value,G(),Object.assign(d,s)}),ge(()=>{A()}),(w,v)=>{const t=D("BaseDataView"),a=D("BaseInput"),k=D("DatePicker"),W=D("BaseSwitch"),Q=D("BaseFieldset"),O=D("BaseSelect"),ee=D("PaymentMethodInput"),Z=D("BaseTextarea"),q=D("FormAction"),T=D("BaseModal");return l(),C(T,{show:c.visibility,onClose:P},{title:n(()=>[b(o(w.$trans("global.pay",{attribute:w.$trans("student.fee.fee")})),1)]),default:n(()=>[m("dl",ze,[r(t,{label:w.$trans("general.date")},{default:n(()=>[b(o(c.date.formatted),1)]),_:1},8,["label"]),c.feeGroup.uuid?(l(),$(M,{key:0},[r(t,{label:w.$trans("finance.fee_group.fee_group")},{default:n(()=>[b(o(c.feeGroup.name),1)]),_:1},8,["label"]),r(t,{label:w.$trans("finance.fee.balance")},{default:n(()=>[b(o(c.feeGroup.balance.formatted),1)]),_:1},8,["label"])],64)):c.feeHead.uuid?(l(),$(M,{key:1},[r(t,{label:w.$trans("finance.fee_head.fee_head")},{default:n(()=>[b(o(c.feeHead.name),1)]),_:1},8,["label"]),r(t,{label:w.$trans("finance.fee.balance")},{default:n(()=>[b(o(c.feeHead.balance.formatted),1)]),_:1},8,["label"])],64)):c.fee.uuid?(l(),$(M,{key:2},[r(t,{label:w.$trans("finance.fee_structure.installment")},{default:n(()=>[b(o(c.fee.installment.title),1)]),_:1},8,["label"]),r(t,{label:w.$trans("finance.fee.balance")},{default:n(()=>[b(o(c.fee.balance.formatted),1)]),_:1},8,["label"])],64)):y("",!0)]),r(q,{"no-card":"","no-data-fetch":"","is-modal":"",uuid:e(x).params.uuid,action:"makePayment","init-url":B,"init-form":s,form:d,"keep-adding":!1,"cancel-action":!0,"after-reset":V,onRedirectTo:i,onCancelled:P},{default:n(()=>[m("div",We,[m("div",Ke,[r(a,{modelValue:d.codeNumber,"onUpdate:modelValue":v[0]||(v[0]=u=>d.codeNumber=u),name:"codeNumber",label:w.$trans("finance.transaction.props.code_number"),error:e(g).codeNumber,"onUpdate:error":v[1]||(v[1]=u=>e(g).codeNumber=u)},null,8,["modelValue","label","error"])]),e(J)("fee:change-payment-date")?(l(),$("div",Je,[r(k,{modelValue:d.date,"onUpdate:modelValue":v[2]||(v[2]=u=>d.date=u),name:"date","no-clear":"",label:w.$trans("student.fee.props.date"),error:e(g).date,"onUpdate:error":v[3]||(v[3]=u=>e(g).date=u)},null,8,["modelValue","label","error"])])):y("",!0),e(J)("fee:customize-late-fee")?(l(),$("div",Qe,[r(a,{modelValue:d.lateFee,"onUpdate:modelValue":v[4]||(v[4]=u=>d.lateFee=u),name:"lateFee",currency:"",label:w.$trans("finance.fee.default_fee_heads.late_fee"),error:e(g).lateFee,"onUpdate:error":v[5]||(v[5]=u=>e(g).lateFee=u),onChange:R},null,8,["modelValue","label","error"])])):y("",!0)]),m("div",Xe,[m("div",Ye,[r(W,{vertical:"",modelValue:p.showAdditionalCharge,"onUpdate:modelValue":v[6]||(v[6]=u=>p.showAdditionalCharge=u),name:"showAdditionalCharge",label:w.$trans("global.show",{attribute:w.$trans("student.fee.props.additional_charge")})},null,8,["modelValue","label"])]),m("div",Ze,[r(W,{vertical:"",modelValue:p.showAdditionalDiscount,"onUpdate:modelValue":v[7]||(v[7]=u=>p.showAdditionalDiscount=u),name:"showAdditionalDiscount",label:w.$trans("global.show",{attribute:w.$trans("student.fee.props.additional_discount")})},null,8,["modelValue","label"])])]),p.showAdditionalCharge?(l(!0),$(M,{key:0},X(d.additionalCharges,(u,h)=>(l(),C(Q,{class:"mt-4",key:u.uuid},{legend:n(()=>[b(o(w.$trans("student.fee.props.additional_charge")),1)]),default:n(()=>[m("div",et,[m("div",tt,[r(a,{modelValue:u.label,"onUpdate:modelValue":j=>u.label=j,name:`additionalCharges.${h}.label`,label:w.$trans("student.fee.props.additional_charge_label"),error:e(g)[`additionalCharges.${h}.label`],"onUpdate:error":j=>e(g)[`additionalCharges.${h}.label`]=j},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",at,[r(a,{modelValue:u.amount,"onUpdate:modelValue":j=>u.amount=j,name:`additionalCharges.${h}.amount`,label:w.$trans("student.fee.props.additional_charge_amount"),currency:"",onChange:R,error:e(g)[`additionalCharges.${h}.amount`],"onUpdate:error":j=>e(g)[`additionalCharges.${h}.amount`]=j},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)):y("",!0),p.showAdditionalDiscount?(l(!0),$(M,{key:1},X(d.additionalDiscounts,(u,h)=>(l(),C(Q,{class:"mt-4",key:u.uuid},{legend:n(()=>[b(o(w.$trans("student.fee.props.additional_discount")),1)]),default:n(()=>[m("div",nt,[m("div",st,[r(a,{modelValue:u.label,"onUpdate:modelValue":j=>u.label=j,name:`additionalDiscounts.${h}.label`,label:w.$trans("student.fee.props.additional_discount_label"),error:e(g)[`additionalDiscounts.${h}.label`],"onUpdate:error":j=>e(g)[`additionalDiscounts.${h}.label`]=j},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",lt,[r(a,{modelValue:u.amount,"onUpdate:modelValue":j=>u.amount=j,name:`additionalDiscounts.${h}.amount`,label:w.$trans("student.fee.props.additional_discount_amount"),currency:"",onChange:R,error:e(g)[`additionalDiscounts.${h}.amount`],"onUpdate:error":j=>e(g)[`additionalDiscounts.${h}.amount`]=j},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)):y("",!0),m("div",ot,[m("div",rt,[r(a,{disabled:!e(J)("fee:partial-payment"),modelValue:d.amount,"onUpdate:modelValue":v[8]||(v[8]=u=>d.amount=u),name:"amount",label:w.$trans("student.fee.props.amount"),currency:"",error:e(g).amount,"onUpdate:error":v[9]||(v[9]=u=>e(g).amount=u)},null,8,["disabled","modelValue","label","error"])]),m("div",it,[r(O,{modelValue:d.ledger,"onUpdate:modelValue":v[10]||(v[10]=u=>d.ledger=u),name:"ledger",label:w.$trans("finance.ledger.ledger"),options:c.preRequisites.ledgers,"label-prop":"name","value-prop":"uuid",error:e(g).ledger,"onUpdate:error":v[11]||(v[11]=u=>e(g).ledger=u)},null,8,["modelValue","label","options","error"])]),m("div",dt,[r(O,{modelValue:p.selectedPaymentMethod,"onUpdate:modelValue":v[12]||(v[12]=u=>p.selectedPaymentMethod=u),name:"paymentMethod",label:w.$trans("finance.payment_method.payment_method"),options:c.preRequisites.paymentMethods,"object-prop":!0,"label-prop":"name","value-prop":"uuid",error:e(g).paymentMethod,"onUpdate:error":v[13]||(v[13]=u=>e(g).paymentMethod=u),onChange:N},null,8,["modelValue","label","options","error"])]),p.selectedPaymentMethod?(l(),C(ee,{key:0,"selected-payment-method":p.selectedPaymentMethod,instrumentNumber:d.instrumentNumber,"onUpdate:instrumentNumber":v[14]||(v[14]=u=>d.instrumentNumber=u),instrumentDate:d.instrumentDate,"onUpdate:instrumentDate":v[15]||(v[15]=u=>d.instrumentDate=u),clearingDate:d.clearingDate,"onUpdate:clearingDate":v[16]||(v[16]=u=>d.clearingDate=u),bankDetail:d.bankDetail,"onUpdate:bankDetail":v[17]||(v[17]=u=>d.bankDetail=u),referenceNumber:d.referenceNumber,"onUpdate:referenceNumber":v[18]||(v[18]=u=>d.referenceNumber=u),formErrors:e(g),"onUpdate:formErrors":v[19]||(v[19]=u=>ue(g)?g.value=u:null)},null,8,["selected-payment-method","instrumentNumber","instrumentDate","clearingDate","bankDetail","referenceNumber","formErrors"])):y("",!0),m("div",ut,[r(Z,{rows:1,modelValue:d.remarks,"onUpdate:modelValue":v[20]||(v[20]=u=>d.remarks=u),name:"remarks",label:w.$trans("student.payment.props.remarks"),error:e(g).remarks,"onUpdate:error":v[21]||(v[21]=u=>e(g).remarks=u)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1},8,["show"])}}}),pt={class:"flex justify-start"},ft={key:0},bt={class:"text-right sm:text-left"},gt={key:0},yt={key:1,class:"text-xs"},ht={key:2,class:"text-xs"},vt={class:"mt-4 justify-end md:justify-start flex flex-wrap gap-1"},kt={class:"text-right sm:text-left"},_t={class:"ml-2"},$t={key:1},Dt={key:2,class:"text-xs"},Vt={key:0,class:"flex justify-between"},Ft={key:0,class:"line-through mr-2"},Ut={key:0,class:"flex justify-between"},Ct={key:0,class:"flex justify-between"},wt={class:"truncate"},Pt={key:0,class:"flex justify-between"},Bt={class:"truncate"},jt={class:"flex justify-start"},St={name:"StudentFeeGroupList"},Mt=Object.assign(St,{props:{state:{type:Object,default(){return{}}}},emits:["setIsLoading","refresh"],setup(c,{emit:Y}){const x=ae(),z=ne(),I=Y,F=c,s=le("$trans"),{screenSize:B}=he(),g=[{key:"title",label:s("finance.fee_structure.props.title"),visibility:!0},{key:"dueDate",label:s("finance.fee_structure.props.due_date"),visibility:!0},{key:"fee",label:s("student.fee.fee"),visibility:!0},{key:"total",label:s("finance.fee.total"),visibility:!0,align:"right"},{key:"paid",label:s("finance.fee.paid"),visibility:!0,align:"right"},{key:"balance",label:s("finance.fee.balance"),visibility:!0,align:"right"},{key:"action",label:"",visibility:!0}],p=q=>F.state.fees.filter(T=>T.installment.group.uuid==q.uuid),d=q=>F.state.transactions.filter(T=>T.records.filter(u=>u.studentFeeUuid==q.uuid).length>0);te(!1);const N=te(!1),P=te(!1),i=te(!1),A=K({isPaymentPreRequisiteSet:!1,isPreRequisiteSet:!1});K({directions:[],transportCircles:[],feeConcessions:[],frequencies:[]});const R=K({paymentMethods:[],ledgers:[]}),V=K({feeGroup:{},fee:{},transaction:{}}),G=async(q="payment")=>{I("setIsLoading",!0),await z.dispatch("student/payment/preRequisite",{uuid:x.params.uuid,type:q}).then(T=>{Object.assign(R,T),A.isPaymentPreRequisiteSet=!0,I("setIsLoading",!1)}).catch(T=>{I("setIsLoading",!1)})},w=q=>{let T=F.state.transactions.filter(h=>h.records.filter(j=>j.additionalCharges.length>0).length>0),u=[];return T.filter(h=>!h.isCancelled&&!h.isRejected).forEach(h=>{h.records.forEach(j=>{j.studentFeeUuid==q.uuid&&u.push(...j.additionalCharges)})}),u},v=q=>{let T=F.state.transactions.filter(h=>h.records.filter(j=>j.additionalDiscounts.length>0).length>0),u=[];return T.filter(h=>!h.isCancelled&&!h.isRejected).forEach(h=>{h.records.forEach(j=>{j.studentFeeUuid==q.uuid&&u.push(...j.additionalDiscounts)})}),u},t=async(q={})=>{A.isPaymentPreRequisiteSet||await G("custom_fee"),V.transaction=q,i.value=!0},a=async(q={})=>{A.isPaymentPreRequisiteSet||await G(),V.feeGroup={},V.fee=q,N.value=!0},k=async(q={})=>{A.isPaymentPreRequisiteSet||await G(),V.fee={},V.feeGroup=q,N.value=!0},W=async(q={})=>{A.isPaymentPreRequisiteSet||await G(),V.feeGroup={},V.fee=q,P.value=!0},Q=async(q={})=>{A.isPaymentPreRequisiteSet||await G(),V.fee={},V.feeGroup=q,P.value=!0},O=()=>{N.value=!1,P.value=!1,V.fee={},V.feeGroup={}},ee=()=>{V.transaction={},i.value=!1},Z=()=>{V.fee={},V.feeGroup={},O(),I("refresh")};return(q,T)=>{const u=D("DataCell"),h=D("BaseButton"),j=D("FloatingMenuItem"),f=D("FloatingMenu"),H=D("DataRow"),U=D("BaseBadge"),ye=D("SimpleTable");return l(),$(M,null,[c.state.fees.length>0?(l(),C(ye,{key:0,corner:"sharp",header:g},{default:n(()=>[(l(!0),$(M,null,X(c.state.feeGroups,E=>(l(),$(M,null,[r(H,{"is-heading":""},{default:n(()=>[r(u,{name:"title",colspan:3,"is-heading":""},{default:n(()=>[m("div",pt,[m("span",null,[E.status.value=="paid"?(l(),$("span",ft,T[1]||(T[1]=[m("i",{class:"fas fa-check-circle text-success mr-1"},null,-1)]))):y("",!0),b(" "+o(E.name),1)])])]),_:2},1024),r(u,{name:"total",align:"right","is-heading":""},{default:n(()=>[b(o(E.total.formatted),1)]),_:2},1024),r(u,{name:"paid",align:"right","is-heading":""},{default:n(()=>[b(o(E.paid.formatted),1)]),_:2},1024),r(u,{name:"balance",align:"right","is-heading":""},{default:n(()=>[b(o(E.balance.formatted),1)]),_:2},1024),r(u,{streched:""},{default:n(()=>[e(J)("fee:payment")?(l(),$(M,{key:0},[!e(se)(["student","guardian"],"any")&&e(B).small&&E.balance.value>0?(l(),C(h,{key:0,block:"",onClick:S=>k(E)},{default:n(()=>[b(o(e(s)("global.pay",{attribute:e(s)("finance.fee_group.fee_group")})),1)]),_:2},1032,["onClick"])):y("",!0),e(B).small&&E.balance.value>0?(l(),C(h,{key:1,block:"",onClick:S=>Q(E)},{default:n(()=>[b(o(e(s)("finance.payment.online")),1)]),_:2},1032,["onClick"])):y("",!0)],64)):y("",!0),e(B).small?y("",!0):(l(),C(f,{key:1,slim:""},{default:n(()=>[e(J)("fee:payment")?(l(),$(M,{key:0},[!e(se)(["student","guardian"],"any")&&E.balance.value>0?(l(),C(j,{key:0,onClick:S=>k(E)},{default:n(()=>[b(o(e(s)("global.pay",{attribute:e(s)("finance.fee_group.fee_group")})),1)]),_:2},1032,["onClick"])):y("",!0),E.balance.value>0?(l(),C(j,{key:1,onClick:S=>Q(E)},{default:n(()=>[b(o(e(s)("finance.payment.online")),1)]),_:2},1032,["onClick"])):y("",!0)],64)):y("",!0)]),_:2},1024))]),_:2},1024)]),_:2},1024),(l(!0),$(M,null,X(p(E),(S,Fa)=>(l(),C(H,{key:S.uuid},{default:n(()=>[r(u,{name:"title"},{default:n(()=>[m("div",bt,[S.status.value=="paid"?(l(),$("span",gt,T[2]||(T[2]=[m("i",{class:"fas fa-check-circle text-success mr-1"},null,-1)]))):y("",!0),b(" "+o(S.installment.title)+" ",1),S.transportCircle?(l(),$("p",yt,o(S.transportCircle.name),1)):y("",!0),S.concession?(l(),$("p",ht,o(S.concession.name),1)):y("",!0),m("div",vt,[(l(!0),$(M,null,X(d(S),L=>(l(),C(U,{key:L.uuid,size:"sm",clickable:"",design:L.design,onClick:ie=>t(L)},{default:n(()=>[b(o(L.codeNumber),1)]),_:2},1032,["design","onClick"]))),128))])])]),_:2},1024),r(u,{name:"dueDate"},{default:n(()=>[m("div",kt,[S.overdue?(l(),C(U,{key:0,design:"error"},{default:n(()=>[b(o(S.dueDate.formatted)+" ",1),m("span",_t,"("+o(S.overdue+" "+e(s)("list.durations.day"))+")",1)]),_:2},1024)):y("",!0),S.overdue===0?(l(),$("p",$t,o(S.dueDate.formatted),1)):y("",!0),S.lateFeeApplicable?(l(),$("p",Dt,o(e(s)("finance.fee_structure.props.late_fee"))+" "+o(S.lateFee.formatted),1)):y("",!0)])]),_:2},1024),r(u,{name:"fee",streched:"",table:""},{default:n(()=>[(l(!0),$(M,null,X(S.records,L=>{var ie,me;return l(),$(M,null,[L.amount.value>0?(l(),$("div",Vt,[b(o(((ie=L.head)==null?void 0:ie.name)||((me=L.defaultFeeHead)==null?void 0:me.label)||"-")+" ",1),m("div",null,[L.concession.value>0?(l(),$("span",Ft,o(L.amount.formatted),1)):y("",!0),m("span",null,o(L.amountWithConcession.formatted),1)])])):y("",!0)],64)}),256)),S.lateFee.amount.value>0?(l(),$("div",Ut,[b(o(e(s)("finance.fee_structure.late_fee"))+" ",1),m("div",null,[m("span",null,o(S.lateFee.amount.formatted),1)])])):y("",!0),(l(!0),$(M,null,X(w(S),L=>(l(),$(M,null,[L.amount.value>0?(l(),$("div",Ct,[m("span",wt,o(L.label),1),m("div",null,[m("span",null,o(L.amount.formatted),1)])])):y("",!0)],64))),256)),(l(!0),$(M,null,X(v(S),L=>(l(),$(M,null,[L.amount.value>0?(l(),$("div",Pt,[m("span",Bt,o(L.label),1),m("div",null,[m("span",null,"(-)"+o(L.amount.formatted),1)])])):y("",!0)],64))),256))]),_:2},1024),r(u,{align:"right",name:"total"},{default:n(()=>[b(o(S.total.formatted),1)]),_:2},1024),r(u,{align:"right",name:"paid"},{default:n(()=>[b(o(S.paid.formatted),1)]),_:2},1024),r(u,{align:"right",name:"balance"},{default:n(()=>[b(o(S.balance.formatted),1)]),_:2},1024),r(u,{name:"action",streched:""},{default:n(()=>[e(J)("fee:payment")?(l(),$(M,{key:0},[!e(se)(["student","guardian"],"any")&&e(B).small&&S.balance.value>0?(l(),C(h,{key:0,block:"",onClick:L=>a(S)},{default:n(()=>[b(o(e(s)("global.pay",{attribute:e(s)("finance.fee_structure.installment")})),1)]),_:2},1032,["onClick"])):y("",!0),e(B).small&&S.balance.value>0?(l(),C(h,{key:1,block:"",onClick:L=>W(S)},{default:n(()=>[b(o(e(s)("finance.payment.online")),1)]),_:2},1032,["onClick"])):y("",!0)],64)):y("",!0),e(B).small?y("",!0):(l(),C(f,{key:1},{default:n(()=>[e(J)("fee:payment")?(l(),$(M,{key:0},[!e(se)(["student","guardian"],"any")&&S.balance.value>0?(l(),C(j,{key:0,icon:"fas fa-credit-card",onClick:L=>a(S)},{default:n(()=>[b(o(e(s)("global.pay",{attribute:e(s)("finance.fee_structure.installment")})),1)]),_:2},1032,["onClick"])):y("",!0),S.balance.value>0?(l(),C(j,{key:1,icon:"fas fa-credit-card",onClick:L=>W(S)},{default:n(()=>[b(o(e(s)("finance.payment.online")),1)]),_:2},1032,["onClick"])):y("",!0)],64)):y("",!0)]),_:2},1024))]),_:2},1024)]),_:2},1024))),128))],64))),256)),c.state.summary?(l(),C(H,{key:0,"is-heading":""},{default:n(()=>[r(u,{name:"title",colspan:3,"is-heading":""},{default:n(()=>[m("div",jt,[m("span",null,o(e(s)("global.grand",{attribute:e(s)("finance.fee.total")})),1)])]),_:1}),r(u,{name:"total",align:"right","is-heading":""},{default:n(()=>{var E;return[b(o((E=c.state.summary.grandTotal)==null?void 0:E.formatted),1)]}),_:1}),r(u,{name:"paid",align:"right","is-heading":""},{default:n(()=>{var E;return[b(o((E=c.state.summary.grandTotalPaid)==null?void 0:E.formatted),1)]}),_:1}),r(u,{name:"balance",align:"right","is-heading":""},{default:n(()=>{var E;return[b(o((E=c.state.summary.grandTotalBalance)==null?void 0:E.formatted),1)]}),_:1}),r(u)]),_:1})):y("",!0)]),_:1})):y("",!0),e(se)(["student","guardian"],"any")?y("",!0):(l(),C(ct,{key:1,visibility:N.value,"pre-requisites":R,"fee-group":V.feeGroup,fee:V.fee,date:c.state.date,onClose:O,onCompleted:Z},null,8,["visibility","pre-requisites","fee-group","fee","date"])),r($e,{visibility:P.value,"pre-requisites":R,"fee-group":V.feeGroup,fee:V.fee,date:c.state.date,onClose:O,onCompleted:Z},null,8,["visibility","pre-requisites","fee-group","fee","date"]),r(xe,{visibility:i.value,transaction:V.transaction,"pre-requisites":R,onClose:ee,onRefresh:T[0]||(T[0]=E=>I("refresh"))},null,8,["visibility","transaction","pre-requisites"])],64)}}}),Rt={class:"grid grid-cols-3 gap-4"},Nt={class:"col-span-3 sm:col-span-1"},At={key:0,class:"col-span-3 sm:col-span-1"},qt={class:"col-span-3 sm:col-span-1"},It={key:0,class:"mt-4 grid grid-cols-3 gap-4"},Tt={class:"col-span-3 sm:col-span-1"},Ot={class:"col-span-3 sm:col-span-1"},Et={class:"col-span-3 sm:col-span-1"},Gt={key:1,class:"col-span-3"},Ht={class:"mt-4 grid grid-cols-3 gap-4"},Lt={class:"col-span-3 sm:col-span-1"},xt={class:"col-span-3 sm:col-span-1"},zt={class:"mt-4 grid grid-cols-3 gap-4"},Wt={class:"col-span-3 sm:col-span-1"},Kt={class:"col-span-3 sm:col-span-1"},Jt={class:"mt-4 grid grid-cols-3 gap-4"},Qt={class:"col-span-3 sm:col-span-1"},Xt={class:"col-span-3 sm:col-span-1"},Yt={class:"mt-4 grid grid-cols-3 gap-4"},Zt={class:"col-span-3 sm:col-span-1"},ea={class:"col-span-3 sm:col-span-1"},ta={class:"col-span-3 sm:col-span-1"},aa={class:"col-span-3"},na={name:"StudentFeePaymentForm"},sa=Object.assign(na,{props:{visibility:{type:Boolean,default:!1},date:{type:Object,default(){return{}}},feeHeads:{type:Array,default(){return[]}},preRequisites:{type:Object,default(){return{}}}},emits:["close","completed"],setup(c,{emit:Y}){const x=ae(),z=ne(),I=Y,F=c,s={feeGroup:"",codeNumber:"",date:"",amount:0,lateFee:0,transportFee:0,ledger:"",heads:[],additionalCharges:[],additionalDiscounts:[],paymentMethod:"",instrumentNumber:"",instrumentDate:"",clearingDate:"",bankDetail:"",referenceNumber:"",remarks:""},B="student/payment/",g=re(B),p=K({initialAmount:0,selectedFeeGroup:{},selectedPaymentMethod:{},showAdditionalCharge:!1,showAdditionalDiscount:!1}),d=K({...s}),N=t=>{d.paymentMethod=t?t.uuid:{},p.selectedPaymentMethod=t||{}},P=()=>{A(),I("close")},i=()=>{A(),I("completed")},A=()=>{Object.assign(d,s),p.selectedFeeGroup={},p.selectedPaymentMethod={},p.showAdditionalCharge=!1,p.showAdditionalDiscount=!1,v(),z.dispatch(B+"resetFormErrors")},R=t=>{let a=[];if(_.isEmpty(t)){d.feeGroup="",d.heads=a,d.amount=0,d.lateFee=0;return}d.feeGroup=t.uuid,t.heads.forEach(k=>{let W=F.feeHeads.find(Q=>k.uuid==Q.uuid);W!=null&&a.push({name:k.name,uuid:k.uuid,total:W.total,paid:W.paid,balance:W.balance,amount:0})}),d.amount=0,d.heads=a},V=()=>{let t=F.feeHeads.find(a=>a.defaultFeeHead=="transport_fee");return t?t.balance:{value:0,formatted:0}},G=t=>{let a=0;d.lateFee&&(a+=parseFloat(d.lateFee)),d.transportFee&&(a+=parseFloat(d.transportFee)),d.heads.forEach(k=>{k.amount&&(a+=parseFloat(k.amount))}),d.additionalCharges.forEach(k=>{k.amount&&(a+=parseFloat(k.amount))}),d.additionalDiscounts.forEach(k=>{k.amount&&(a-=parseFloat(k.amount))}),d.amount=a},w=()=>{A()},v=()=>{s.additionalCharges=[],s.additionalCharges.push({uuid:oe(),label:"",amount:0}),s.additionalDiscounts=[],s.additionalDiscounts.push({uuid:oe(),label:"",amount:0})};return be(()=>{var t;s.date=(t=F.date)==null?void 0:t.value,v(),Object.assign(d,s)}),ge(()=>{A()}),(t,a)=>{const k=D("BaseInput"),W=D("DatePicker"),Q=D("BaseSelect"),O=D("TextMuted"),ee=D("BaseAlert"),Z=D("BaseSwitch"),q=D("BaseFieldset"),T=D("PaymentMethodInput"),u=D("BaseTextarea"),h=D("FormAction"),j=D("BaseModal");return l(),C(j,{show:c.visibility,onClose:P},{title:n(()=>[b(o(t.$trans("global.pay",{attribute:t.$trans("student.fee.fee")})),1)]),default:n(()=>[r(h,{"no-card":"","no-data-fetch":"","is-modal":"",uuid:e(x).params.uuid,action:"makeHeadWisePayment","init-url":B,"init-form":s,form:d,"keep-adding":!1,"cancel-action":!0,"after-reset":w,onRedirectTo:i,onCancelled:P},{default:n(()=>[m("div",Rt,[m("div",Nt,[r(k,{modelValue:d.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=f=>d.codeNumber=f),name:"codeNumber",label:t.$trans("finance.transaction.props.code_number"),error:e(g).codeNumber,"onUpdate:error":a[1]||(a[1]=f=>e(g).codeNumber=f)},null,8,["modelValue","label","error"])]),e(J)("fee:change-payment-date")?(l(),$("div",At,[r(W,{modelValue:d.date,"onUpdate:modelValue":a[2]||(a[2]=f=>d.date=f),name:"date","no-clear":"",label:t.$trans("student.fee.props.date"),error:e(g).date,"onUpdate:error":a[3]||(a[3]=f=>e(g).date=f)},null,8,["modelValue","label","error"])])):y("",!0),m("div",qt,[r(Q,{modelValue:p.selectedFeeGroup,"onUpdate:modelValue":a[4]||(a[4]=f=>p.selectedFeeGroup=f),name:"feeGroup",label:t.$trans("finance.fee_group.fee_group"),options:c.preRequisites.feeGroups,"object-prop":!0,"label-prop":"name","value-prop":"uuid",onChange:R,error:e(g).feeGroup,"onUpdate:error":a[5]||(a[5]=f=>e(g).feeGroup=f)},null,8,["modelValue","label","options","error"])])]),d.feeGroup?(l(),$("div",It,[(l(!0),$(M,null,X(d.heads,(f,H)=>(l(),$("div",Tt,[r(k,{modelValue:f.amount,"onUpdate:modelValue":U=>f.amount=U,name:`heads.${H}.amount`,currency:"",label:f.name,error:e(g)[`heads.${H}.amount`],"onUpdate:error":U=>e(g)[`heads.${H}.amount`]=U,onChange:G},{"additional-label":n(()=>[r(O,null,{default:n(()=>[m("span",{class:ce({"text-danger":f.balance.value<=0,"text-success":f.balance.value>0})},o(t.$trans("finance.fee.balance"))+" "+o(f.balance.formatted),3)]),_:2},1024)]),_:2},1032,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]))),256)),p.selectedFeeGroup.isCustom?y("",!0):(l(),$(M,{key:0},[m("div",Ot,[r(k,{modelValue:d.transportFee,"onUpdate:modelValue":a[6]||(a[6]=f=>d.transportFee=f),name:"transportFee",currency:"",label:t.$trans("finance.fee.default_fee_heads.transport_fee"),error:e(g).transportFee,"onUpdate:error":a[7]||(a[7]=f=>e(g).transportFee=f),onChange:G},{"additional-label":n(()=>[r(O,null,{default:n(()=>[m("span",{class:ce({"text-danger":V().value<=0,"text-success":V().value>0})},o(t.$trans("finance.fee.balance"))+" "+o(V().formatted),3)]),_:1})]),_:1},8,["modelValue","label","error"])]),m("div",Et,[r(k,{modelValue:d.lateFee,"onUpdate:modelValue":a[8]||(a[8]=f=>d.lateFee=f),name:"lateFee",currency:"",label:t.$trans("finance.fee.default_fee_heads.late_fee"),error:e(g).lateFee,"onUpdate:error":a[9]||(a[9]=f=>e(g).lateFee=f),onChange:G},null,8,["modelValue","label","error"])])],64)),d.heads.length?y("",!0):(l(),$("div",Gt,[r(ee,{design:"info",size:"xs"},{default:n(()=>[b(o(t.$trans("global.could_not_find",{attribute:t.$trans("finance.fee_head.fee_head")})),1)]),_:1})]))])):y("",!0),d.heads.length?(l(),$(M,{key:1},[m("div",Ht,[m("div",Lt,[r(Z,{vertical:"",modelValue:p.showAdditionalCharge,"onUpdate:modelValue":a[10]||(a[10]=f=>p.showAdditionalCharge=f),name:"showAdditionalCharge",label:t.$trans("global.show",{attribute:t.$trans("student.fee.props.additional_charge")})},null,8,["modelValue","label"])]),m("div",xt,[r(Z,{vertical:"",modelValue:p.showAdditionalDiscount,"onUpdate:modelValue":a[11]||(a[11]=f=>p.showAdditionalDiscount=f),name:"showAdditionalDiscount",label:t.$trans("global.show",{attribute:t.$trans("student.fee.props.additional_discount")})},null,8,["modelValue","label"])])]),p.showAdditionalCharge?(l(!0),$(M,{key:0},X(d.additionalCharges,(f,H)=>(l(),C(q,{class:"mt-4",key:f.uuid},{legend:n(()=>[b(o(t.$trans("student.fee.props.additional_charge")),1)]),default:n(()=>[m("div",zt,[m("div",Wt,[r(k,{modelValue:f.label,"onUpdate:modelValue":U=>f.label=U,name:`additionalCharges.${H}.label`,label:t.$trans("student.fee.props.additional_charge_label"),error:e(g)[`additionalCharges.${H}.label`],"onUpdate:error":U=>e(g)[`additionalCharges.${H}.label`]=U},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",Kt,[r(k,{modelValue:f.amount,"onUpdate:modelValue":U=>f.amount=U,name:`additionalCharges.${H}.amount`,label:t.$trans("student.fee.props.additional_charge_amount"),currency:"",onChange:G,error:e(g)[`additionalCharges.${H}.amount`],"onUpdate:error":U=>e(g)[`additionalCharges.${H}.amount`]=U},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)):y("",!0),p.showAdditionalDiscount?(l(!0),$(M,{key:1},X(d.additionalDiscounts,(f,H)=>(l(),C(q,{class:"mt-4",key:f.uuid},{legend:n(()=>[b(o(t.$trans("student.fee.props.additional_discount")),1)]),default:n(()=>[m("div",Jt,[m("div",Qt,[r(k,{modelValue:f.label,"onUpdate:modelValue":U=>f.label=U,name:`additionalDiscounts.${H}.label`,label:t.$trans("student.fee.props.additional_discount_label"),error:e(g)[`additionalDiscounts.${H}.label`],"onUpdate:error":U=>e(g)[`additionalDiscounts.${H}.label`]=U},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",Xt,[r(k,{modelValue:f.amount,"onUpdate:modelValue":U=>f.amount=U,name:`additionalDiscounts.${H}.amount`,label:t.$trans("student.fee.props.additional_discount_amount"),currency:"",onChange:G,error:e(g)[`additionalDiscounts.${H}.amount`],"onUpdate:error":U=>e(g)[`additionalDiscounts.${H}.amount`]=U},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)):y("",!0)],64)):y("",!0),m("div",Yt,[m("div",Zt,[r(k,{disabled:!0,modelValue:d.amount,"onUpdate:modelValue":a[12]||(a[12]=f=>d.amount=f),name:"amount",currency:"",label:t.$trans("student.fee.props.amount"),error:e(g).amount,"onUpdate:error":a[13]||(a[13]=f=>e(g).amount=f)},null,8,["modelValue","label","error"])]),m("div",ea,[r(Q,{modelValue:d.ledger,"onUpdate:modelValue":a[14]||(a[14]=f=>d.ledger=f),name:"ledger",label:t.$trans("finance.ledger.ledger"),options:c.preRequisites.ledgers,"label-prop":"name","value-prop":"uuid",error:e(g).ledger,"onUpdate:error":a[15]||(a[15]=f=>e(g).ledger=f)},null,8,["modelValue","label","options","error"])]),m("div",ta,[r(Q,{modelValue:p.selectedPaymentMethod,"onUpdate:modelValue":a[16]||(a[16]=f=>p.selectedPaymentMethod=f),name:"paymentMethod",label:t.$trans("finance.payment_method.payment_method"),options:c.preRequisites.paymentMethods,"object-prop":!0,"label-prop":"name","value-prop":"uuid",error:e(g).paymentMethod,"onUpdate:error":a[17]||(a[17]=f=>e(g).paymentMethod=f),onChange:N},null,8,["modelValue","label","options","error"])]),p.selectedPaymentMethod?(l(),C(T,{key:0,"selected-payment-method":p.selectedPaymentMethod,instrumentNumber:d.instrumentNumber,"onUpdate:instrumentNumber":a[18]||(a[18]=f=>d.instrumentNumber=f),instrumentDate:d.instrumentDate,"onUpdate:instrumentDate":a[19]||(a[19]=f=>d.instrumentDate=f),clearingDate:d.clearingDate,"onUpdate:clearingDate":a[20]||(a[20]=f=>d.clearingDate=f),bankDetail:d.bankDetail,"onUpdate:bankDetail":a[21]||(a[21]=f=>d.bankDetail=f),referenceNumber:d.referenceNumber,"onUpdate:referenceNumber":a[22]||(a[22]=f=>d.referenceNumber=f),formErrors:e(g),"onUpdate:formErrors":a[23]||(a[23]=f=>ue(g)?g.value=f:null)},null,8,["selected-payment-method","instrumentNumber","instrumentDate","clearingDate","bankDetail","referenceNumber","formErrors"])):y("",!0),m("div",aa,[r(u,{rows:1,modelValue:d.remarks,"onUpdate:modelValue":a[24]||(a[24]=f=>d.remarks=f),name:"remarks",label:t.$trans("student.payment.props.remarks"),error:e(g).remarks,"onUpdate:error":a[25]||(a[25]=f=>e(g).remarks=f)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1},8,["show"])}}}),la={class:"flex justify-start"},oa={key:0,class:"flex justify-end p-4"},ra={name:"StudentFeeHeadList"},ia=Object.assign(ra,{props:{state:{type:Object,default(){return{}}}},emits:["setIsLoading","refresh"],setup(c,{emit:Y}){const x=ae(),z=ne(),I=Y,F=le("$trans"),s=[{key:"name",label:F("finance.fee_head.fee_head"),visibility:!0},{key:"amount",label:F("finance.transaction.props.amount"),visibility:!0,align:"right"},{key:"concession",label:F("finance.fee_concession.fee_concession"),visibility:!0,align:"right"},{key:"total",label:F("finance.fee.total"),visibility:!0,align:"right"},{key:"paid",label:F("finance.fee.paid"),visibility:!0,align:"right"},{key:"balance",label:F("finance.fee.balance"),visibility:!0,align:"right"},{key:"action",label:"",visibility:!0}],B=te(!1),g=K({isPaymentPreRequisiteSet:!1}),p=K({feeGroups:[],paymentMethods:[],ledgers:[]}),d=async()=>{I("setIsLoading",!0),await z.dispatch("student/payment/preRequisite",{uuid:x.params.uuid}).then(A=>{Object.assign(p,A),g.isPaymentPreRequisiteSet=!0,I("setIsLoading",!1)}).catch(A=>{I("setIsLoading",!1)})},N=async(A={})=>{g.isPaymentPreRequisiteSet||await d(),B.value=!0},P=()=>{B.value=!1},i=()=>{P(),I("refresh")};return(A,R)=>{const V=D("DataCell"),G=D("DataRow"),w=D("SimpleTable"),v=D("BaseButton");return l(),$(M,null,[r(w,{corner:"sharp",header:s},{default:n(()=>[(l(!0),$(M,null,X(c.state.feeHeads,t=>(l(),C(G,{key:t.uuid,"text-color":t.isDeduction?"danger":""},{default:n(()=>[r(V,{name:"name"},{default:n(()=>[b(o(t.name),1)]),_:2},1024),r(V,{align:"right",name:"amount"},{default:n(()=>[b(o(t.amount.formatted),1)]),_:2},1024),r(V,{align:"right",name:"concession"},{default:n(()=>[b(o(t.concession.formatted),1)]),_:2},1024),r(V,{align:"right",name:"total"},{default:n(()=>[b(o(t.total.formatted),1)]),_:2},1024),r(V,{align:"right",name:"paid"},{default:n(()=>[b(o(t.paid.formatted),1)]),_:2},1024),r(V,{align:"right",name:"balance"},{default:n(()=>[b(o(t.balance.formatted),1)]),_:2},1024),r(V,{name:"action"})]),_:2},1032,["text-color"]))),128)),c.state.summary?(l(),C(G,{key:0,"is-heading":""},{default:n(()=>[r(V,{name:"name","is-heading":""},{default:n(()=>[m("div",la,[m("span",null,o(e(F)("finance.fee.total")),1)])]),_:1}),r(V,{align:"right",name:"amount","is-heading":""},{default:n(()=>{var t;return[b(o((t=c.state.summary.grandTotalAmount)==null?void 0:t.formatted),1)]}),_:1}),r(V,{align:"right",name:"concession","is-heading":""},{default:n(()=>{var t;return[b(o((t=c.state.summary.grandTotalConcession)==null?void 0:t.formatted),1)]}),_:1}),r(V,{align:"right",name:"total","is-heading":""},{default:n(()=>{var t;return[b(o((t=c.state.summary.grandTotal)==null?void 0:t.formatted),1)]}),_:1}),r(V,{align:"right",name:"paid","is-heading":""},{default:n(()=>{var t;return[b(o((t=c.state.summary.grandTotalPaid)==null?void 0:t.formatted),1)]}),_:1}),r(V,{align:"right",name:"balance","is-heading":""},{default:n(()=>{var t;return[b(o((t=c.state.summary.grandTotalBalance)==null?void 0:t.formatted),1)]}),_:1}),r(V,{name:"action"})]),_:1})):y("",!0)]),_:1}),e(J)("fee:head-wise-payment")?(l(),$("div",oa,[r(v,{design:"primary",onClick:N},{default:n(()=>[b(o(e(F)("global.pay",{attribute:e(F)("student.fee.fee")})),1)]),_:1})])):y("",!0),r(sa,{visibility:B.value,"pre-requisites":p,"fee-heads":c.state.feeHeads,date:c.state.date,onClose:P,onCompleted:i},null,8,["visibility","pre-requisites","fee-heads","date"])],64)}}}),da={class:"flex flex-wrap gap-2"},ua=["onClick"],ma=["is-loading"],ca=["onClick"],pa={key:0},fa={key:1},ba={key:1,class:"text-sm"},ga={key:0,class:"pt-4 px-4 sm:px-6"},ya={class:"grid grid-cols-1 gap-4 sm:grid-cols-2"},ha=["onClick"],va={key:0,class:""},ka={class:"text-xs text-gray-500"},_a={class:"flex items-center gap-2 text-sm dark:text-gray-400"},$a={class:"flex items-center gap-2 text-sm dark:text-gray-400"},Da={class:"flex items-center gap-2 text-sm dark:text-gray-400"},Va={name:"StudentShowFee"},Pa=Object.assign(Va,{props:{student:{type:Object,default(){return{}}}},emits:["refresh"],setup(c,{emit:Y}){const x=ae(),z=pe(),I=ne(),F=le("emitter"),s=le("$trans"),B=c,g=de(()=>!(!J("fee:set")||i.fees.length)),p=de(()=>!J("fee:set")||!i.fees.length||i.type=="head"?!1:!i.fees.some(t=>{var a,k;return((a=t.status)==null?void 0:a.value)==="paid"||((k=t.status)==null?void 0:k.value)==="partially_paid"})),d=de(()=>!(!J("fee:set")||!i.fees.length)),N=te(!1),P=K({feeRecords:[],previousDues:[]}),i=K({type:"group",date:{},feeGroups:[],fees:[],transactions:[],feeHeads:{},summary:{},siblings:[]}),A=async()=>{await V()},R=t=>{i.type=t,V()},V=async()=>{var t;N.value=!0,await I.dispatch("student/fee/list",{uuid:x.params.uuid,date:((t=i.date)==null?void 0:t.value)||"",type:i.type}).then(a=>{i.date=a.date,i.fees=a.fees,i.type=="group"?(i.feeGroups=a.feeGroups,i.summary=a.summary,i.feeHeads=[],i.transactions=a.transactions):(i.feeHeads=a.feeHeads,i.summary=a.summary,i.feeGroups=[],i.transactions=[]),N.value=!1}).catch(a=>{N.value=!1})},G=async()=>{N.value=!0,await I.dispatch("student/fee/getStudentFees",{uuid:x.params.uuid}).then(t=>{P.feeRecords=t.feeRecords,P.previousDues=t.previousDues,N.value=!1}).catch(t=>{N.value=!1})},w=async()=>{await _e()&&(N.value=!0,await I.dispatch("student/fee/reset",{uuid:x.params.uuid}).then(t=>{i.feeGroups=[],i.fees=[],i.feeHeads={},i.summary={},F.emit("studentUpdated"),N.value=!1}).catch(t=>{N.value=!1}))},v=async()=>{se(["student"],"any")||await I.dispatch("student/fee/getSiblingFees",{uuid:x.params.uuid}).then(t=>{i.siblings=t.siblings}).catch(t=>{console.log(t)})};return fe(async()=>{i.date.value=new Date().toLocaleDateString("en-CA"),J("fee:change-payment-date")||await V(),await G(),setTimeout(async()=>{await v()},1e3)}),ve(()=>x.params.uuid,(t,a)=>{t!=null&&t!=a&&V()}),(t,a)=>{const k=D("BaseButton"),W=D("DropdownItem"),Q=D("PageHeaderAction"),O=D("PageHeader"),ee=D("BaseAlert"),Z=D("ParentTransition"),q=D("DatePicker"),T=D("BaseCard"),u=D("CardView");return l(),$(M,null,[c.student.uuid?(l(),C(O,{key:0,title:e(s)(e(x).meta.label),navs:[{label:e(s)("student.student"),path:"Student"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}}]},{default:n(()=>[r(Q,null,ke({default:n(()=>[g.value&&!c.student.hasFeeStructureSet?(l(),C(k,{key:0,design:"white",onClick:a[0]||(a[0]=h=>e(z).push({name:"StudentSetFee",params:{uuid:B.student.uuid}}))},{default:n(()=>[b(o(e(s)("global.set",{attribute:e(s)("student.fee.fee")})),1)]),_:1})):y("",!0),i.fees.length>0?(l(),$(M,{key:1},[i.type=="group"?(l(),C(k,{key:0,design:"white",onClick:a[1]||(a[1]=h=>R("head"))},{default:n(()=>[b(o(e(s)("student.fee.head_wise")),1)]),_:1})):y("",!0),i.type=="head"?(l(),C(k,{key:1,design:"white",onClick:a[2]||(a[2]=h=>R("group"))},{default:n(()=>[b(o(e(s)("student.fee.group_wise")),1)]),_:1})):y("",!0)],64)):y("",!0)]),_:2},[p.value||d.value?{name:"dropdown",fn:n(()=>[p.value?(l(),C(W,{key:0,icon:"fas fa-times",onClick:w},{default:n(()=>[b(o(e(s)("global.reset",{attribute:e(s)("student.fee.fee")})),1)]),_:1})):y("",!0),d.value?(l(),C(W,{key:1,icon:"fas fa-pencil-alt",onClick:a[3]||(a[3]=h=>e(z).push({name:"StudentEditFee",params:{uuid:B.student.uuid}}))},{default:n(()=>[b(o(e(s)("global.edit",{attribute:e(s)("student.fee.fee")})),1)]),_:1})):y("",!0),d.value?(l(),C(W,{key:2,icon:"fas fa-pen-to-square",onClick:a[4]||(a[4]=h=>e(z).push({name:"StudentCustomFee",params:{uuid:B.student.uuid}}))},{default:n(()=>[b(o(e(s)("finance.fee_head.custom_fee")),1)]),_:1})):y("",!0)]),key:"0"}:void 0]),1024)]),_:1},8,["title","navs"])):y("",!0),r(Z,{appear:"",visibility:!0},{default:n(()=>[m("div",da,[(l(!0),$(M,null,X(P.feeRecords,h=>(l(),$("span",{onClick:j=>e(z).push({name:"StudentShowFee",params:{uuid:h.uuid}}),class:"cursor-pointer px-4 py-2 bg-primary rounded-xl text-white text-xs"},o(h.course+" "+h.batch)+" ("+o(h.period)+") ",9,ua))),256))]),P.previousDues.length>0?(l(),$("div",{key:0,class:"mt-4","is-loading":N.value},[(l(!0),$(M,null,X(P.previousDues,h=>(l(),$("div",{class:"cursor-pointer",onClick:j=>e(z).push({name:"StudentShowFee",params:{uuid:h.uuid}})},[r(ee,{size:"xs",design:"error"},{default:n(()=>[b(o(e(s)("student.fee.previous_due_info",{period:h.period,amount:h.balance.formatted})),1)]),_:2},1024)],8,ca))),256))],8,ma)):y("",!0)]),_:1}),r(Z,{appear:"",visibility:!0},{default:n(()=>[c.student.uuid?(l(),C(T,{key:0,"no-padding":"","no-content-padding":"","bottom-content-padding":"","is-loading":N.value},{title:n(()=>[i.type=="group"?(l(),$("span",pa,o(e(s)("student.fee.group_wise")),1)):(l(),$("span",fa,o(e(s)("student.fee.head_wise")),1))]),action:n(()=>[e(J)("fee:change-payment-date")?(l(),C(q,{key:0,slim:"",modelValue:i.date.value,"onUpdate:modelValue":a[5]||(a[5]=h=>i.date.value=h),name:"date","no-clear":"",onChange:A},null,8,["modelValue"])):(l(),$("span",ba,o(i.date.formatted),1))]),default:n(()=>[i.fees.length===0?(l(),$("div",ga,[r(ee,{size:"xs",design:"error"},{default:n(()=>[b(o(e(s)("student.fee.set_fee_info")),1)]),_:1})])):(l(),$(M,{key:1},[i.type=="group"?(l(),C(Mt,{key:0,state:i,onSetIsLoading:a[6]||(a[6]=h=>N.value=h),onRefresh:V},null,8,["state"])):y("",!0),i.type=="head"?(l(),C(ia,{key:1,state:i,onSetIsLoading:a[7]||(a[7]=h=>N.value=h),onRefresh:V},null,8,["state"])):y("",!0)],64))]),_:1},8,["is-loading"])):y("",!0),i.siblings.length>0?(l(),C(T,{key:1,class:"mt-4"},{title:n(()=>[b(o(e(s)("student.sibling.sibling")),1)]),default:n(()=>[m("div",ya,[(l(!0),$(M,null,X(i.siblings,h=>(l(),$("div",{key:h.uuid,class:"cursor-pointer",onClick:j=>e(z).push({name:"StudentShowFee",params:{uuid:h.uuid}})},[r(u,{"img-src":h.photoUrl},{title:n(()=>[b(o(h.name)+" ",1),h.codeNumber?(l(),$("span",va,"("+o(h.codeNumber)+")",1)):y("",!0)]),default:n(()=>[m("p",ka,o(h.courseName+" "+h.batchName),1),m("div",_a,[m("span",null,o(e(s)("student.fee.total"))+": "+o(h.feeSummary.totalFee.formatted),1)]),m("div",$a,[m("span",null,o(e(s)("student.fee.paid"))+": "+o(h.feeSummary.paidFee.formatted),1)]),m("div",Da,[m("span",null,o(e(s)("student.fee.balance"))+": "+o(h.feeSummary.balanceFee.formatted),1)])]),_:2},1032,["img-src"])],8,ha))),128))])]),_:1})):y("",!0)]),_:1})],64)}}});export{Pa as default};
