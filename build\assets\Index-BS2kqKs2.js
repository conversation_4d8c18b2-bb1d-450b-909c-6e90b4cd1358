import{u as U,j as O,l as b,H as N,n as H,r,q as V,o as u,w as l,d as _,e as o,h as E,i as I,y as W,m as B,a as F,f as D,F as R,v as A,s as $,t as P}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(g,{emit:m}){U(),O("moment");const y=m,h=g,c={startDate:"",endDate:"",period:"",ledgers:[],pgAccount:""},e=b({...c});N(h.initUrl);const d=b({isLoaded:!0});return H(async()=>{d.isLoaded=!0}),(n,t)=>{const i=r("DatePicker"),v=r("BaseSelect"),f=r("BaseInput"),s=r("FilterForm");return u(),V(s,{"init-form":c,multiple:[],form:e,onHide:t[5]||(t[5]=a=>y("hide"))},{default:l(()=>[_("div",z,[_("div",G,[o(i,{start:e.startDate,"onUpdate:start":t[0]||(t[0]=a=>e.startDate=a),end:e.endDate,"onUpdate:end":t[1]||(t[1]=a=>e.endDate=a),name:"dateBetween",as:"range",label:n.$trans("general.date_between")},null,8,["start","end","label"])]),_("div",J,[o(v,{name:"period",label:n.$trans("global.select",{attribute:n.$trans("academic.period.period")}),modelValue:e.period,"onUpdate:modelValue":t[2]||(t[2]=a=>e.period=a),"label-prop":"name","value-prop":"uuid",options:g.preRequisites.periods},null,8,["label","modelValue","options"])]),_("div",K,[o(v,{multiple:"",name:"ledgers",label:n.$trans("global.select",{attribute:n.$trans("finance.ledger.ledger")}),modelValue:e.ledgers,"onUpdate:modelValue":t[3]||(t[3]=a=>e.ledgers=a),"label-prop":"name","value-prop":"uuid",options:g.preRequisites.ledgers},null,8,["label","modelValue","options"])]),_("div",Q,[o(f,{type:"text",modelValue:e.pgAccount,"onUpdate:modelValue":t[4]||(t[4]=a=>e.pgAccount=a),name:"pgAccount",label:n.$trans("finance.config.props.pg_account")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Y={name:"FinanceReportPaymentMethodWiseFeePayment"},x=Object.assign(Y,{setup(g){const m=U();E();const y=I();let h=["filter"],c=[];W("finance:export")&&(c=["print","pdf","excel"]);const e="finance/report/",d=B(!1),n=B(!1),t=b({periods:[],ledgers:[]}),i=b({headers:[],data:[],meta:{total:0}}),v=async()=>{n.value=!0,await y.dispatch(e+"preRequisite",{name:"payment-method-wise-fee-payment",params:m.query}).then(s=>{n.value=!1,Object.assign(t,s)}).catch(s=>{n.value=!1})},f=async()=>{n.value=!0,await y.dispatch(e+"fetchReport",{name:"payment-method-wise-fee-payment",params:m.query}).then(s=>{n.value=!1,Object.assign(i,s)}).catch(s=>{n.value=!1})};return H(async()=>{await v(),await f()}),(s,a)=>{const j=r("PageHeaderAction"),k=r("PageHeader"),q=r("ParentTransition"),w=r("DataCell"),C=r("DataRow"),T=r("DataTable"),S=r("BaseCard");return u(),F(R,null,[o(k,{title:s.$trans(D(m).meta.label),navs:[{label:s.$trans("finance.finance"),path:"Finance"},{label:s.$trans("finance.report.report"),path:"FinanceReport"}]},{default:l(()=>[o(j,{url:"finance/reports/payment-method-wise-fee-payment/",name:"FinanceReportPaymentMethodWiseFeePayment",title:s.$trans("finance.report.payment_method_wise_fee_payment.payment_method_wise_fee_payment"),actions:D(h),"dropdown-actions":D(c),headers:i.headers,onToggleFilter:a[0]||(a[0]=p=>d.value=!d.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),o(q,{appear:"",visibility:d.value},{default:l(()=>[o(X,{onAfterFilter:f,"init-url":e,"pre-requisites":t,onHide:a[1]||(a[1]=p=>d.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),o(q,{appear:"",visibility:!0},{default:l(()=>[o(S,{"no-padding":"","no-content-padding":"","is-loading":n.value},{default:l(()=>[o(T,{header:i.headers,footer:i.footers,meta:i.meta,module:"finance.report.payment_method_wise_fee_payment",onRefresh:f},{default:l(()=>[(u(!0),F(R,null,A(i.data,p=>(u(),V(C,{key:p.uuid},{default:l(()=>[o(w,{name:"date"},{default:l(()=>[$(P(p.date.formatted),1)]),_:2},1024),(u(!0),F(R,null,A(p.paymentMethods,(L,M)=>(u(),V(w,{name:M},{default:l(()=>[$(P(L.formatted),1)]),_:2},1032,["name"]))),256)),o(w,{name:"total"},{default:l(()=>[$(P(p.total.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{x as default};
