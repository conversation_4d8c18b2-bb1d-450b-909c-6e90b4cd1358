import{u as O,l as B,n as E,r as l,q as y,o as p,w as t,d as I,e as n,h as z,j as G,y as f,m as P,f as i,a as J,F as K,v as Q,s as u,t as m,b as q}from"./app-BAwPsakn.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(R,{emit:c}){O();const _=c,k=R,g={name:"",code:"",inventory:"",place:"",stockCategories:[]},r=B({...g}),b=B({inventories:k.preRequisites.inventories,stockCategories:k.preRequisites.stockCategories,places:k.preRequisites.places}),$=B({isLoaded:!0});return E(async()=>{$.isLoaded=!0}),(d,a)=>{const F=l("BaseInput"),V=l("BaseSelect"),s=l("FilterForm");return p(),y(s,{"init-form":g,form:r,multiple:["stockCategories"],onHide:a[5]||(a[5]=e=>_("hide"))},{default:t(()=>[I("div",W,[I("div",X,[n(F,{type:"text",modelValue:r.name,"onUpdate:modelValue":a[0]||(a[0]=e=>r.name=e),name:"name",label:d.$trans("inventory.stock_item.props.name")},null,8,["modelValue","label"])]),I("div",Y,[n(F,{type:"text",modelValue:r.code,"onUpdate:modelValue":a[1]||(a[1]=e=>r.code=e),name:"code",label:d.$trans("inventory.stock_item.props.code")},null,8,["modelValue","label"])]),I("div",Z,[n(V,{modelValue:r.inventory,"onUpdate:modelValue":a[2]||(a[2]=e=>r.inventory=e),name:"inventory",label:d.$trans("inventory.inventory"),"label-prop":"name","value-prop":"uuid",options:b.inventories},null,8,["modelValue","label","options"])]),I("div",x,[n(V,{multiple:"",modelValue:r.stockCategories,"onUpdate:modelValue":a[3]||(a[3]=e=>r.stockCategories=e),name:"stockCategories",label:d.$trans("inventory.stock_category.stock_category"),"label-prop":"name","value-prop":"uuid",options:b.categories},null,8,["modelValue","label","options"])]),I("div",ee,[n(V,{modelValue:r.place,"onUpdate:modelValue":a[4]||(a[4]=e=>r.place=e),name:"place",label:d.$trans("inventory.stock_item.props.place"),"label-prop":"fullName","value-prop":"uuid",options:b.places},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},ne={name:"InventoryStockItemList"},ae=Object.assign(ne,{setup(R){const c=z(),_=G("emitter");let k=["filter"];f("stock-item:create")&&k.unshift("create");let g=[];f("stock-item:export")&&(g=["print","pdf","excel"]),f("stock-item:create")&&g.unshift("import");const r="inventory/stockItem/",b=B({categories:[],places:[]}),$=P(!1),d=P(!1),a=B({}),F=s=>{Object.assign(b,s)},V=s=>{Object.assign(a,s)};return(s,e)=>{const H=l("PageHeaderAction"),M=l("PageHeader"),w=l("ParentTransition"),j=l("BaseImport"),D=l("TextMuted"),C=l("DataCell"),S=l("FloatingMenuItem"),A=l("FloatingMenu"),L=l("DataRow"),U=l("BaseButton"),h=l("DataTable"),N=l("ListItem");return p(),y(N,{"init-url":r,"pre-requisites":!0,onSetPreRequisites:F,onSetItems:V},{header:t(()=>[n(M,{title:s.$trans("inventory.stock_item.stock_item"),navs:[{label:s.$trans("inventory.inventory"),path:"Inventory"}]},{default:t(()=>[n(H,{url:"inventory/stock-items/",name:"InventoryStockItem",title:s.$trans("inventory.stock_item.stock_item"),actions:i(k),"dropdown-actions":i(g),onToggleFilter:e[0]||(e[0]=o=>$.value=!$.value),onToggleImport:e[1]||(e[1]=o=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[n(w,{appear:"",visibility:$.value},{default:t(()=>[n(te,{onRefresh:e[2]||(e[2]=o=>i(_).emit("listItems")),"pre-requisites":b,onHide:e[3]||(e[3]=o=>$.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),import:t(()=>[n(w,{appear:"",visibility:d.value},{default:t(()=>[n(j,{path:"inventory/stock-items/import",onCancelled:e[4]||(e[4]=o=>d.value=!1),onHide:e[5]||(e[5]=o=>d.value=!1),onCompleted:e[6]||(e[6]=o=>i(_).emit("listItems"))})]),_:1},8,["visibility"])]),default:t(()=>[n(w,{appear:"",visibility:!0},{default:t(()=>[n(h,{header:a.headers,meta:a.meta,module:"inventory.stock_item",onRefresh:e[8]||(e[8]=o=>i(_).emit("listItems"))},{actionButton:t(()=>[i(f)("stock-item:create")?(p(),y(U,{key:0,onClick:e[7]||(e[7]=o=>i(c).push({name:"InventoryStockItemCreate"}))},{default:t(()=>[u(m(s.$trans("global.add",{attribute:s.$trans("inventory.stock_item.stock_item")})),1)]),_:1})):q("",!0)]),default:t(()=>[(p(!0),J(K,null,Q(a.data,o=>(p(),y(L,{key:o.uuid,onDoubleClick:v=>i(c).push({name:"InventoryStockItemShow",params:{uuid:o.uuid}})},{default:t(()=>[n(C,{name:"name"},{default:t(()=>[u(m(o.name)+" ",1),n(D,{block:""},{default:t(()=>[u(m(o.code),1)]),_:2},1024)]),_:2},1024),n(C,{name:"category"},{default:t(()=>[u(m(o.category.name)+" ",1),n(D,{block:""},{default:t(()=>{var v,T;return[u(m((T=(v=o.category)==null?void 0:v.inventory)==null?void 0:T.name),1)]}),_:2},1024)]),_:2},1024),n(C,{name:"quantity"},{default:t(()=>[u(m(o.quantity),1)]),_:2},1024),n(C,{name:"unit"},{default:t(()=>[u(m(o.unit),1)]),_:2},1024),n(C,{name:"createdAt"},{default:t(()=>[u(m(o.createdAt.formatted),1)]),_:2},1024),n(C,{name:"action"},{default:t(()=>[n(A,null,{default:t(()=>[n(S,{icon:"fas fa-arrow-circle-right",onClick:v=>i(c).push({name:"InventoryStockItemShow",params:{uuid:o.uuid}})},{default:t(()=>[u(m(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(f)("stock-item:edit")?(p(),y(S,{key:0,icon:"fas fa-edit",onClick:v=>i(c).push({name:"InventoryStockItemEdit",params:{uuid:o.uuid}})},{default:t(()=>[u(m(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):q("",!0),i(f)("stock-item:create")?(p(),y(S,{key:1,icon:"fas fa-copy",onClick:v=>i(c).push({name:"InventoryStockItemDuplicate",params:{uuid:o.uuid}})},{default:t(()=>[u(m(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):q("",!0),i(f)("stock-item:delete")?(p(),y(S,{key:2,icon:"fas fa-trash",onClick:v=>i(_).emit("deleteItem",{uuid:o.uuid})},{default:t(()=>[u(m(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):q("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ae as default};
