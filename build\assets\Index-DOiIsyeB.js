import{r as e,q as t,o as c,w as i,e as s}from"./app-BAwPsakn.js";const f={name:"FinanceConfig"},p=Object.assign(f,{setup(r){const n=[{name:"FinanceConfigGeneral",icon:"fas fa-cogs",label:"config.config"},{name:"FinanceConfigFeeConcessionType",icon:"fas fa-chevron-right",label:"finance.fee_concession.type.type"},{name:"FinanceConfigPaymentGateway",icon:"fas fa-wallet",label:"finance.config.payment_gateway"}];return(_,l)=>{const a=e("router-view"),o=e("ModuleConfig");return c(),t(o,{navigations:n},{default:i(()=>[s(a)]),_:1})}}});export{p as default};
