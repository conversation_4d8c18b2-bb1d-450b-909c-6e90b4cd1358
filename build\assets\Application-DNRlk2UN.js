import{u as ne,h as le,i as ie,G as de,g as A,H as me,c as z,l as D,m as pe,n as ue,r as p,a as V,o as h,d as n,e as o,f as l,s as m,t as d,w as i,b as ce,I as F,F as be}from"./app-BAwPsakn.js";const fe={class:"flex justify-end"},ge={class:"flex justify-center"},ve={href:"/",class:"mb-6"},ye=["src"],$e={class:"text-primary text-center text-xl"},Ne={class:"text-dark-primary text-center"},Ue={key:0,class:"mt-8 w-full rounded-lg"},Ve={key:0,class:"mb-6"},he=["innerHTML"],Le={class:"grid grid-cols-3 gap-4"},ke={class:"col-span-3 sm:col-span-1"},Se={class:"mt-4 grid grid-cols-3 gap-4"},Ae={class:"col-span-3 sm:col-span-2"},De={class:"flex"},we={class:"col-span-3 sm:col-span-1"},_e={class:"col-span-3 sm:col-span-1"},Be={class:"col-span-3 sm:col-span-1"},ze={class:"col-span-3 sm:col-span-1"},Fe={class:"grid grid-cols-3 gap-4"},He={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},Pe={class:"col-span-3"},Te={class:"grid grid-cols-3 gap-6"},Ee={class:"grid grid-cols-3 gap-4"},qe={class:"col-span-3 sm:col-span-2"},Re={class:"col-span-3 sm:col-span-1"},je={class:"col-span-3"},Ce={class:"grid grid-cols-1 gap-4"},Me={class:"col"},Ge={class:"col"},We={class:"col"},Je={class:"col"},Oe={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-4"},Ke={class:"mt-4 grid grid-cols-1"},Qe={class:"text-sm"},Xe={key:1,class:"mt-8"},Ye={name:"JobVacancy"},et=Object.assign(Ye,{setup(Ze){const L=ne(),k=le(),N=ie(),H={designation:"",firstName:"",middleName:"",lastName:"",birthDate:"",gender:"",fatherName:"",motherName:"",contactNumber:"",email:"",presentAddress:{},availabilityDate:"",qualificationSummary:"",coverLetter:"",declaration:!1,resume:[],marksheet:[],idProof:[],addressProof:[],mediaUpdated:!1,mediaToken:de(),mediaHash:[]},f="recruitment/",I=A("layout.display").value=="dark"?A("assets.iconLight"):A("assets.icon"),r=me(f),P=z(()=>`${a.firstName} ${a.middleName} ${a.lastName}`.replace(/\s+/g," ")),T=z(()=>`${a.presentAddress.addressLine1}, ${a.presentAddress.addressLine2}, ${a.presentAddress.city}, ${a.presentAddress.state}, ${a.presentAddress.zipcode}, ${a.presentAddress.country}`.replace(/\s+/g," ")),b=pe(!1),a=D({...H}),u=D({status:!1,vacancy:{},response:"",designations:[],selectedDesignation:null}),g=D({genders:[],instruction:""}),E=()=>{},q=()=>{},R=()=>{},j=()=>{},C=()=>v("basic"),M=()=>v("contact"),G=()=>v("cover_letter"),W=()=>v("upload"),J=()=>v("finish"),O=s=>{a.designation=s.uuid},K=()=>{a.designation=""},Q=async()=>{await N.dispatch(f+"vacancyPreRequisite").then(s=>{g.genders=s.genders,g.instruction=s.instruction}).catch(s=>{k.push({name:"Dashboard"})})},X=async()=>{await N.dispatch(f+"getVacancy",{slug:L.params.slug}).then(s=>{u.vacancy=s,u.designations=s.records.map(e=>({name:e.designation,uuid:e.designationUuid}))}).catch(s=>{k.push({name:"Dashboard"})})},v=s=>(b.value=!0,N.dispatch(f+"validateApplication",{slug:L.params.slug,option:s,form:a}).then(e=>(b.value=!1,!0)).catch(e=>(b.value=!1,!1))),Y=async()=>{b.value=!0,await N.dispatch(f+"finishApplication",{slug:L.params.slug,form:a}).then(s=>{u.status=!0,u.response=s.message,b.value=!1}).catch(s=>{b.value=!1})};return ue(async()=>{await X(),await Q()}),(s,e)=>{const S=p("BaseAlert"),Z=p("BaseSelect"),w=p("BaseLabel"),x=p("NameInput"),ee=p("BaseRadioGroup"),_=p("DatePicker"),y=p("BaseInput"),$=p("TabContent"),te=p("AddressInput"),B=p("BaseFieldset"),ae=p("BaseEditor"),U=p("MediaUpload"),c=p("BaseDataView"),se=p("BaseSwitch"),oe=p("FormWizard"),re=p("BaseLoader");return h(),V(be,null,[n("div",fe,[n("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:e[0]||(e[0]=t=>l(k).push({name:"Dashboard"}))},[e[47]||(e[47]=n("i",{class:"fas fa-home mr-1"},null,-1)),m(" "+d(s.$trans("global.go_to",{attribute:s.$trans("dashboard.home")})),1)])]),n("div",ge,[n("a",ve,[n("img",{class:"h-16 w-auto",src:l(I),alt:""},null,8,ye)])]),o(re,{"is-loading":b.value},{default:i(()=>[n("div",null,[n("p",$e,d(u.vacancy.team),1),n("p",Ne,d(u.vacancy.title),1)]),u.status?(h(),V("div",Xe,[o(S,{design:"success",size:"xs"},{default:i(()=>[m(d(u.response),1)]),_:1})])):(h(),V("div",Ue,[g.instruction?(h(),V("div",Ve,[o(S,{size:"xs",design:"info"},{default:i(()=>[m(d(s.$trans("recruitment.vacancy.application_instruction_alert")),1)]),_:1}),n("div",{class:"mt-4 text-sm",innerHTML:g.instruction},null,8,he)])):ce("",!0),o(oe,{onComplete:Y,"next-button-text":s.$trans("pagination.next"),"previous-button-text":s.$trans("pagination.previous"),"finish-button-text":s.$trans("pagination.finish")},{default:i(()=>[o($,{title:s.$trans("global.step",{attribute:1}),description:s.$trans("recruitment.vacancy.wizard.first_step"),"before-change":C},{default:i(()=>[n("div",Le,[n("div",ke,[o(Z,{modelValue:u.selectedDesignation,"onUpdate:modelValue":e[1]||(e[1]=t=>u.selectedDesignation=t),name:"designation",label:s.$trans("employee.designation.designation"),"label-prop":"name","value-prop":"uuid","object-prop":!0,options:u.designations,error:l(r).designation,"onUpdate:error":e[2]||(e[2]=t=>l(r).designation=t),onSelected:O,onRemoved:K},null,8,["modelValue","label","options","error"])])]),n("div",Se,[n("div",Ae,[o(w,null,{default:i(()=>[m(d(s.$trans("contact.props.name")),1)]),_:1}),n("div",De,[o(x,{firstName:a.firstName,"onUpdate:firstName":e[3]||(e[3]=t=>a.firstName=t),middleName:a.middleName,"onUpdate:middleName":e[4]||(e[4]=t=>a.middleName=t),thirdName:a.thirdName,"onUpdate:thirdName":e[5]||(e[5]=t=>a.thirdName=t),lastName:a.lastName,"onUpdate:lastName":e[6]||(e[6]=t=>a.lastName=t),formErrors:l(r),"onUpdate:formErrors":e[7]||(e[7]=t=>F(r)?r.value=t:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),n("div",we,[o(w,null,{default:i(()=>[m(d(s.$trans("contact.props.gender")),1)]),_:1}),o(ee,{"top-margin":"",options:g.genders,name:"gender",modelValue:a.gender,"onUpdate:modelValue":e[8]||(e[8]=t=>a.gender=t),error:l(r).gender,"onUpdate:error":e[9]||(e[9]=t=>l(r).gender=t),horizontal:""},null,8,["options","modelValue","error"])]),n("div",_e,[o(_,{modelValue:a.birthDate,"onUpdate:modelValue":e[10]||(e[10]=t=>a.birthDate=t),name:"birthDate",label:s.$trans("contact.props.birth_date"),"no-clear":"",error:l(r).birthDate,"onUpdate:error":e[11]||(e[11]=t=>l(r).birthDate=t)},null,8,["modelValue","label","error"])]),n("div",Be,[o(y,{type:"text",modelValue:a.fatherName,"onUpdate:modelValue":e[12]||(e[12]=t=>a.fatherName=t),name:"fatherName",label:s.$trans("contact.props.father_name"),error:l(r).fatherName,"onUpdate:error":e[13]||(e[13]=t=>l(r).fatherName=t)},null,8,["modelValue","label","error"])]),n("div",ze,[o(y,{type:"text",modelValue:a.motherName,"onUpdate:modelValue":e[14]||(e[14]=t=>a.motherName=t),name:"motherName",label:s.$trans("contact.props.mother_name"),error:l(r).motherName,"onUpdate:error":e[15]||(e[15]=t=>l(r).motherName=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["title","description"]),o($,{title:s.$trans("global.step",{attribute:2}),description:s.$trans("recruitment.vacancy.wizard.second_step"),"before-change":M,"after-load":E},{default:i(()=>[n("div",Fe,[n("div",He,[o(y,{type:"text",modelValue:a.contactNumber,"onUpdate:modelValue":e[16]||(e[16]=t=>a.contactNumber=t),name:"contactNumber",label:s.$trans("contact.props.contact_number"),error:l(r).contactNumber,"onUpdate:error":e[17]||(e[17]=t=>l(r).contactNumber=t)},null,8,["modelValue","label","error"])]),n("div",Ie,[o(y,{type:"text",modelValue:a.email,"onUpdate:modelValue":e[18]||(e[18]=t=>a.email=t),name:"email",label:s.$trans("contact.props.email"),error:l(r).email,"onUpdate:error":e[19]||(e[19]=t=>l(r).email=t)},null,8,["modelValue","label","error"])]),n("div",Pe,[o(B,null,{legend:i(()=>[m(d(s.$trans("contact.props.present_address")),1)]),default:i(()=>[n("div",Te,[o(te,{prefix:"presentAddress",addressLine1:a.presentAddress.addressLine1,"onUpdate:addressLine1":e[20]||(e[20]=t=>a.presentAddress.addressLine1=t),addressLine2:a.presentAddress.addressLine2,"onUpdate:addressLine2":e[21]||(e[21]=t=>a.presentAddress.addressLine2=t),city:a.presentAddress.city,"onUpdate:city":e[22]||(e[22]=t=>a.presentAddress.city=t),state:a.presentAddress.state,"onUpdate:state":e[23]||(e[23]=t=>a.presentAddress.state=t),zipcode:a.presentAddress.zipcode,"onUpdate:zipcode":e[24]||(e[24]=t=>a.presentAddress.zipcode=t),country:a.presentAddress.country,"onUpdate:country":e[25]||(e[25]=t=>a.presentAddress.country=t),formErrors:l(r),"onUpdate:formErrors":e[26]||(e[26]=t=>F(r)?r.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1})])])]),_:1},8,["title","description"]),o($,{title:s.$trans("global.step",{attribute:3}),description:s.$trans("recruitment.vacancy.wizard.third_step"),"before-change":G,"after-load":q},{default:i(()=>[n("div",Ee,[n("div",qe,[o(y,{type:"text",modelValue:a.qualificationSummary,"onUpdate:modelValue":e[27]||(e[27]=t=>a.qualificationSummary=t),name:"qualificationSummary",label:s.$trans("recruitment.application.props.qualification_summary"),error:l(r).qualificationSummary,"onUpdate:error":e[28]||(e[28]=t=>l(r).qualificationSummary=t)},null,8,["modelValue","label","error"])]),n("div",Re,[o(_,{modelValue:a.availabilityDate,"onUpdate:modelValue":e[29]||(e[29]=t=>a.availabilityDate=t),name:"availabilityDate",label:s.$trans("recruitment.application.props.availability_date"),"no-clear":"",error:l(r).availabilityDate,"onUpdate:error":e[30]||(e[30]=t=>l(r).availabilityDate=t)},null,8,["modelValue","label","error"])]),n("div",je,[o(ae,{modelValue:a.coverLetter,"onUpdate:modelValue":e[31]||(e[31]=t=>a.coverLetter=t),toolbar:"minimal",name:"coverLetter",label:s.$trans("recruitment.application.props.cover_letter"),error:l(r).coverLetter,"onUpdate:error":e[32]||(e[32]=t=>l(r).coverLetter=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["title","description"]),o($,{title:s.$trans("global.step",{attribute:4}),description:s.$trans("recruitment.vacancy.wizard.fourth_step"),"before-change":W,"after-load":R},{default:i(()=>[n("div",Ce,[n("div",Me,[o(U,{multiple:"",guest:"",label:s.$trans("global.select",{attribute:s.$trans("recruitment.vacancy.wizard.resume")}),module:"job_application",section:"resume",media:a.resume,"media-token":a.mediaToken,onIsUpdated:e[33]||(e[33]=t=>a.mediaUpdated=!0),onSetHash:e[34]||(e[34]=t=>a.mediaHash.push(t)),error:l(r).resume,"onUpdate:error":e[35]||(e[35]=t=>l(r).resume=t)},null,8,["label","media","media-token","error"])]),n("div",Ge,[o(U,{multiple:"",guest:"",label:s.$trans("global.select",{attribute:s.$trans("recruitment.vacancy.wizard.marksheet")}),module:"job_application",section:"marksheet",media:a.marksheet,"media-token":a.mediaToken,onIsUpdated:e[36]||(e[36]=t=>a.mediaUpdated=!0),onSetHash:e[37]||(e[37]=t=>a.mediaHash.push(t)),error:l(r).marksheet,"onUpdate:error":e[38]||(e[38]=t=>l(r).marksheet=t)},null,8,["label","media","media-token","error"])]),n("div",We,[o(U,{multiple:"",guest:"",label:s.$trans("global.select",{attribute:s.$trans("contact.props.id_proof")}),module:"job_application",section:"id_proof",media:a.idProof,"media-token":a.mediaToken,onIsUpdated:e[39]||(e[39]=t=>a.mediaUpdated=!0),onSetHash:e[40]||(e[40]=t=>a.mediaHash.push(t)),error:l(r).idProof,"onUpdate:error":e[41]||(e[41]=t=>l(r).idProof=t)},null,8,["label","media","media-token","error"])]),n("div",Je,[o(U,{multiple:"",guest:"",label:s.$trans("global.select",{attribute:s.$trans("contact.props.address_proof")}),module:"job_application",section:"address_proof",media:a.addressProof,"media-token":a.mediaToken,onIsUpdated:e[42]||(e[42]=t=>a.mediaUpdated=!0),onSetHash:e[43]||(e[43]=t=>a.mediaHash.push(t)),error:l(r).addressProof,"onUpdate:error":e[44]||(e[44]=t=>l(r).addressProof=t)},null,8,["label","media","media-token","error"])])])]),_:1},8,["title","description"]),o($,{title:s.$trans("global.step",{attribute:4}),description:s.$trans("recruitment.vacancy.wizard.final_step"),"before-change":J,"after-load":j},{default:i(()=>[o(S,{design:"info",size:"xs"},{default:i(()=>[m(d(s.$trans("recruitment.vacancy.wizard.review_content")),1)]),_:1}),o(B,{class:"mt-4"},{legend:i(()=>[m(d(s.$trans("recruitment.vacancy.wizard.review")),1)]),default:i(()=>[n("dl",Oe,[o(c,{label:s.$trans("employee.designation.designation")},{default:i(()=>{var t;return[m(d(((t=u.selectedDesignation)==null?void 0:t.name)||"-"),1)]}),_:1},8,["label"]),o(c,{label:s.$trans("student.props.name")},{default:i(()=>[m(d(P.value),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.gender")},{default:i(()=>[m(d(a.gender),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.birth_date")},{default:i(()=>[m(d(a.birthDate),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.father_name")},{default:i(()=>[m(d(a.fatherName),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.mother_name")},{default:i(()=>[m(d(a.motherName),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.contact_number")},{default:i(()=>[m(d(a.contactNumber),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.email")},{default:i(()=>[m(d(a.email),1)]),_:1},8,["label"]),o(c,{label:s.$trans("contact.props.address.address")},{default:i(()=>[m(d(T.value),1)]),_:1},8,["label"])])]),_:1}),n("div",Ke,[n("div",Qe,d(s.$trans("recruitment.vacancy.wizard.declaration_content")),1),o(se,{reverse:"",vertical:"",modelValue:a.declaration,"onUpdate:modelValue":e[45]||(e[45]=t=>a.declaration=t),name:"declaration",error:l(r).declaration,"onUpdate:error":e[46]||(e[46]=t=>l(r).declaration=t)},null,8,["modelValue","error"])])]),_:1},8,["title","description"])]),_:1},8,["next-button-text","previous-button-text","finish-button-text"])]))]),_:1},8,["is-loading"])],64)}}});export{et as default};
