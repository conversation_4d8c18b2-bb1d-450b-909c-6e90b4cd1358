import{u as R,j as B,l as w,H as S,n as U,r as n,q as F,o as V,w as d,d as p,e as r,b as H,s as k,t as f,f as l,i as q,m as C,a as D,F as L,aN as P}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},E={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(y,{emit:m}){const c=R();B("moment");const v=m,_=y,i={date:"",batches:[],status:"all",output:"print"},o=w({...i}),u=S(_.initUrl),s=w({isLoaded:!c.query.batches});return U(async()=>{s.batches=c.query.batches?c.query.batches.split(","):[],s.isLoaded=!0}),(t,a)=>{const h=n("DatePicker"),b=n("BaseSelectSearch"),g=n("CustomCheckbox"),$=n("FilterForm");return V(),F($,{"init-form":i,multiple:["batches"],form:o,onHide:a[6]||(a[6]=e=>v("hide"))},{default:d(()=>[p("div",T,[p("div",A,[r(h,{modelValue:o.date,"onUpdate:modelValue":a[0]||(a[0]=e=>o.date=e),name:"date",as:"date",label:t.$trans("general.date")},null,8,["modelValue","label"])]),p("div",N,[s.isLoaded?(V(),F(b,{key:0,multiple:"",name:"batches",label:t.$trans("global.select",{attribute:t.$trans("academic.batch.batch")}),modelValue:o.batches,"onUpdate:modelValue":a[1]||(a[1]=e=>o.batches=e),"value-prop":"uuid","init-search":s.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:d(e=>[k(f(e.value.course.nameWithTerm)+" "+f(e.value.name),1)]),listOption:d(e=>[k(f(e.option.course.nameWithTerm)+" "+f(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):H("",!0)]),p("div",W,[r(g,{label:t.$trans("resource.status"),options:[{label:t.$trans("general.all"),value:"all"},{label:t.$trans("resource.submitted"),value:"submitted"},{label:t.$trans("resource.not_submitted"),value:"not_submitted"}],modelValue:o.status,"onUpdate:modelValue":a[2]||(a[2]=e=>o.status=e),error:l(u).status,"onUpdate:error":a[3]||(a[3]=e=>l(u).status=e)},null,8,["label","options","modelValue","error"])]),p("div",j,[r(g,{label:t.$trans("general.action"),options:[{label:t.$trans("general.print"),value:"print"},{label:t.$trans("general.pdf"),value:"pdf"}],modelValue:o.output,"onUpdate:modelValue":a[4]||(a[4]=e=>o.output=e),error:l(u).output,"onUpdate:error":a[5]||(a[5]=e=>l(u).output=e)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},M={name:"ResourceReportDateWiseLearningMaterial"},Q=Object.assign(M,{setup(y){const m=R();q();let c=["filter"],v=[];const _="resource/report/",i=C(!0),o=C(!1),u=async()=>{let s="/app/resource/reports/date-wise-learning-material/export",t=m.query;window.open(P(s,t),"_blank").focus()};return U(async()=>{}),(s,t)=>{const a=n("PageHeaderAction"),h=n("PageHeader"),b=n("ParentTransition"),g=n("BaseCard");return V(),D(L,null,[r(h,{title:s.$trans(l(m).meta.label),navs:[{label:s.$trans("resource.resource"),path:"Resource"},{label:s.$trans("resource.report.report"),path:"ResourceReport"}]},{default:d(()=>[r(a,{url:"resource/reports/date-wise-learning-material/",name:"ResourceReportDateWiseLearningMaterial",title:s.$trans("resource.report.date_wise_learning_material.date_wise_learning_material"),actions:l(c),"dropdown-actions":l(v),onToggleFilter:t[0]||(t[0]=$=>i.value=!0)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),r(b,{appear:"",visibility:i.value},{default:d(()=>[r(E,{onAfterFilter:u,"init-url":_,onHide:t[1]||(t[1]=$=>i.value=!1)})]),_:1},8,["visibility"]),r(b,{appear:"",visibility:!0},{default:d(()=>[r(g,{"no-padding":"","no-content-padding":"","is-loading":o.value},null,8,["is-loading"])]),_:1})],64)}}});export{Q as default};
