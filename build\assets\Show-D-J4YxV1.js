import{i as D,u as k,h as S,l as v,r,a as C,o as p,e as t,w as a,f as c,q as P,b as T,d as V,s as o,t as s,F as N}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"AcademicDepartmentInchargeShow"},F=Object.assign(R,{setup(j){D();const d=k(),m=S(),u={},_="academic/departmentIncharge/",n=v({...u}),g=e=>{Object.assign(n,e)};return(e,i)=>{const h=r("PageHeaderAction"),b=r("PageHeader"),f=r("TextMuted"),l=r("BaseDataView"),B=r("BaseButton"),$=r("ShowButton"),y=r("BaseCard"),A=r("ShowItem"),I=r("ParentTransition");return p(),C(N,null,[t(b,{title:e.$trans(c(d).meta.trans,{attribute:e.$trans(c(d).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.department.department"),path:"AcademicDepartment"},{label:e.$trans("academic.department_incharge.department_incharge"),path:"AcademicDepartmentInchargeList"}]},{default:a(()=>[t(h,{name:"AcademicDepartmentIncharge",title:e.$trans("academic.department_incharge.department_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(I,{appear:"",visibility:!0},{default:a(()=>[t(A,{"init-url":_,uuid:c(d).params.uuid,onSetItem:g,onRedirectTo:i[1]||(i[1]=w=>c(m).push({name:"AcademicDepartmentIncharge"}))},{default:a(()=>[n.uuid?(p(),P(y,{key:0},{title:a(()=>[o(s(n.department.name),1)]),footer:a(()=>[t($,null,{default:a(()=>[t(B,{design:"primary",onClick:i[0]||(i[0]=w=>c(m).push({name:"AcademicDepartmentInchargeEdit",params:{uuid:n.uuid}}))},{default:a(()=>[o(s(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[V("dl",H,[t(l,{label:e.$trans("employee.employee")},{default:a(()=>[o(s(n.employee.name)+" ",1),t(f,{block:""},{default:a(()=>[o(s(n.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),t(l,{label:e.$trans("employee.incharge.props.period")},{default:a(()=>[o(s(n.period),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.incharge.props.remarks")},{default:a(()=>[o(s(n.remarks),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.created_at")},{default:a(()=>[o(s(n.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.updated_at")},{default:a(()=>[o(s(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):T("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
