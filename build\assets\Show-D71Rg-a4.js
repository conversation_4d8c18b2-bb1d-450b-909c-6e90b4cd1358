import{u as x,i as ee,H as te,c as se,l as H,m as ae,r as p,q as k,o as b,w as s,d,e as n,f as a,y as K,I as re,s as m,t as i,M as le,j as Y,z as ie,a as U,b as v,F as S,v as Z,A as de,aP as ue,h as me}from"./app-BAwPsakn.js";const pe={class:"grid grid-cols-3 gap-6"},ce={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},fe={class:"mt-4 grid grid-cols-3 gap-6"},_e={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3"},ye={name:"RegistrationPaymentForm"},ke=Object.assign(ye,{props:{registration:{type:Object,default(){return{}}}},emits:["completed"],setup(T,{emit:I}){const z=x(),O=ee(),l=I,u={amount:T.registration.fee.value,date:"",ledger:{},paymentMethod:"",instrumentNumber:"",instrumentDate:"",clearingDate:"",bankDetail:"",referenceNumber:"",remarks:""},w="student/registration/",g=te(w),e=se(()=>f.paymentMethod?M.paymentMethods.find(r=>r.uuid===f.paymentMethod):{}),L=ae(!1),M=H({paymentMethods:[],ledgers:[]}),f=H({...u}),j=r=>{Object.assign(M,r)},V=r=>{l("completed")},E=async()=>{await le()&&(L.value=!0,await O.dispatch(w+"skipPayment",{uuid:z.params.uuid}).then(r=>{L.value=!1,l("completed")}).catch(r=>{L.value=!1}))};return(r,t)=>{const _=p("BaseInput"),R=p("DatePicker"),D=p("BaseSelect"),P=p("LedgerBalance"),B=p("PaymentMethodInput"),F=p("BaseTextarea"),N=p("BaseButton"),A=p("FormAction");return b(),k(A,{"pre-requisites":!0,"pre-requisite-custom-url":"paymentPreRequisite",onSetPreRequisites:j,"init-url":w,uuid:T.registration.uuid,"no-data-fetch":"",action:"payment","init-form":u,form:f,"keep-adding":!1,"after-submit":V},{additionalButton:s(()=>[n(N,{design:"success",onClick:E},{default:s(()=>[m(i(r.$trans("student.registration.proceed_without_payment")),1)]),_:1})]),default:s(()=>[d("div",pe,[d("div",ce,[n(_,{disabled:!a(K)("registration:action"),currency:"",modelValue:f.amount,"onUpdate:modelValue":t[0]||(t[0]=c=>f.amount=c),name:"amount",label:r.$trans("academic.course.props.registration_fee"),error:a(g).amount,"onUpdate:error":t[1]||(t[1]=c=>a(g).amount=c)},null,8,["disabled","modelValue","label","error"])]),d("div",be,[n(R,{modelValue:f.date,"onUpdate:modelValue":t[2]||(t[2]=c=>f.date=c),name:"date",label:r.$trans("student.registration.props.payment_date"),"no-clear":"",error:a(g).date,"onUpdate:error":t[3]||(t[3]=c=>a(g).date=c)},null,8,["modelValue","label","error"])]),d("div",ge,[n(D,{modelValue:f.ledger,"onUpdate:modelValue":t[4]||(t[4]=c=>f.ledger=c),name:"ledger",label:r.$trans("finance.ledger.ledger"),options:M.ledgers,"object-prop":!0,"label-prop":"name","value-prop":"uuid",error:a(g)["ledger.uuid"],"onUpdate:error":t[5]||(t[5]=c=>a(g)["ledger.uuid"]=c)},null,8,["modelValue","label","options","error"]),n(P,{ledger:f.ledger},null,8,["ledger"])])]),d("div",fe,[d("div",_e,[n(D,{modelValue:f.paymentMethod,"onUpdate:modelValue":t[6]||(t[6]=c=>f.paymentMethod=c),name:"paymentMethod",label:r.$trans("finance.payment_method.payment_method"),"label-prop":"name","value-prop":"uuid",options:M.paymentMethods,error:a(g).paymentMethod,"onUpdate:error":t[7]||(t[7]=c=>a(g).paymentMethod=c)},null,8,["modelValue","label","options","error"])]),n(B,{"selected-payment-method":e.value,instrumentNumber:f.instrumentNumber,"onUpdate:instrumentNumber":t[8]||(t[8]=c=>f.instrumentNumber=c),instrumentDate:f.instrumentDate,"onUpdate:instrumentDate":t[9]||(t[9]=c=>f.instrumentDate=c),clearingDate:f.clearingDate,"onUpdate:clearingDate":t[10]||(t[10]=c=>f.clearingDate=c),bankDetail:f.bankDetail,"onUpdate:bankDetail":t[11]||(t[11]=c=>f.bankDetail=c),referenceNumber:f.referenceNumber,"onUpdate:referenceNumber":t[12]||(t[12]=c=>f.referenceNumber=c),formErrors:a(g),"onUpdate:formErrors":t[13]||(t[13]=c=>re(g)?g.value=c:null)},null,8,["selected-payment-method","instrumentNumber","instrumentDate","clearingDate","bankDetail","referenceNumber","formErrors"]),d("div",ve,[n(F,{modelValue:f.remarks,"onUpdate:modelValue":t[14]||(t[14]=c=>f.remarks=c),name:"remarks",label:r.$trans("finance.transaction.props.remarks"),error:a(g).remarks,"onUpdate:error":t[15]||(t[15]=c=>a(g).remarks=c)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),Ve={class:"grid grid-cols-3 gap-6"},$e={class:"col-span-3 sm:col-span-3"},Ue={class:"col-span-3 sm:col-span-1"},we={class:"col-span-3 sm:col-span-1"},Be={class:"col-span-3 sm:col-span-1"},Ce={class:"col-span-3 sm:col-span-1"},je={class:"col-span-3 sm:col-span-1"},De={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3"},Se={class:"col-span-3 sm:col-span-1"},Re={class:"col-span-3 sm:col-span-1"},Pe={key:0,class:"col-span-3 sm:col-span-2"},Fe={class:"col-span-3 space-y-2"},Ae={class:"flex flex-wrap gap-y-2"},Me={class:"col-span-3"},Te={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},Le={class:"col-span-3 sm:col-span-1"},qe={class:"flex space-x-2"},ze={class:"col-span-3 sm:col-span-1"},Oe={class:"col-span-3 sm:col-span-3"},Ee={key:1,class:"col-span-3 sm:col-span-3"},He={name:"RegistrationActionForm"},he=Object.assign(He,{props:{registration:{type:Object,default(){return{}}}},emits:["completed"],setup(T,{emit:I}){var V,E;const z=I;Y("emitter");const O=T,l={status:((E=(V=O.registration)==null?void 0:V.status)==null?void 0:E.value)||"",rejectionRemarks:"",date:"",codeNumber:"",batch:"",enrollmentType:"",electiveSubjects:[],groups:[],assignFee:!1,feeConcession:"",transportCircle:"",direction:"",optedFeeHeads:[],createUserAccount:!1,email:"",username:"",password:"",passwordConfirmation:"",remarks:""},h="student/registration/",u=te(h),w=H({selectedBatch:null,hidePassword:!0}),g=H({statuses:[],batches:[],feeHeads:[],enrollmentTypes:[],electiveSubjects:[],groups:[],directions:[],transportCircles:[],feeConcessions:[],codeNumber:""}),e=H({...l}),L=r=>{Object.assign(g,r),e.codeNumber=r.codeNumber,e.username=r.codeNumber,e.email=O.registration.contact.email},M=r=>{e.batch=(r==null?void 0:r.uuid)||""},f=()=>{var r=ue(12);e.password=r,e.passwordConfirmation=r},j=r=>{z("completed")};return(r,t)=>{const _=p("BaseLabel"),R=p("BaseRadioGroup"),D=p("BaseInput"),P=p("DatePicker"),B=p("BaseSelect"),F=p("FormInfo"),N=p("BaseSwitch"),A=p("BaseArrayCheckbox"),c=p("BaseTextarea"),W=p("FormAction"),Q=ie("tooltip");return b(),k(W,{"has-setup-wizard":!0,"pre-requisites":!0,"pre-requisite-custom-url":"actionPreRequisite",onSetPreRequisites:L,"init-url":h,uuid:T.registration.uuid,"no-data-fetch":"",action:"action","no-action-button":e.status=="pending","init-form":l,form:e,"keep-adding":!1,"after-submit":j},{default:s(()=>{var J;return[d("div",Ve,[d("div",$e,[n(_,null,{default:s(()=>[m(i(r.$trans("student.registration.props.status")),1)]),_:1}),n(R,{"top-margin":"",options:g.statuses,name:"status",modelValue:e.status,"onUpdate:modelValue":t[0]||(t[0]=o=>e.status=o),error:a(u).status,"onUpdate:error":t[1]||(t[1]=o=>a(u).status=o),horizontal:""},null,8,["options","modelValue","error"])]),e.status=="approved"?(b(),U(S,{key:0},[d("div",Ue,[n(D,{readonly:"",type:"text",modelValue:e.codeNumber,"onUpdate:modelValue":t[2]||(t[2]=o=>e.codeNumber=o),name:"codeNumber",label:r.$trans("student.admission.props.code_number"),error:a(u).codeNumber,"onUpdate:error":t[3]||(t[3]=o=>a(u).codeNumber=o)},null,8,["modelValue","label","error"])]),d("div",we,[n(P,{modelValue:e.date,"onUpdate:modelValue":t[4]||(t[4]=o=>e.date=o),name:"date",label:r.$trans("student.admission.props.date"),"no-clear":"",error:a(u).date,"onUpdate:error":t[5]||(t[5]=o=>a(u).date=o)},null,8,["modelValue","label","error"])]),d("div",Be,[n(B,{name:"batch",label:((J=T.registration.course)==null?void 0:J.name)+" "+r.$trans("global.select",{attribute:r.$trans("academic.batch.batch")}),modelValue:w.selectedBatch,"onUpdate:modelValue":t[6]||(t[6]=o=>w.selectedBatch=o),error:a(u).batch,"onUpdate:error":t[7]||(t[7]=o=>a(u).batch=o),options:g.batches,"object-prop":!0,"label-prop":"name","value-prop":"uuid",onChange:M},null,8,["label","modelValue","error","options"]),w.selectedBatch?(b(),k(F,{key:0},{default:s(()=>[d("span",null,i(r.$trans("academic.batch.current_strength",{attribute:w.selectedBatch.maxStrength})),1)]),_:1})):v("",!0)]),d("div",Ce,[n(B,{modelValue:e.enrollmentType,"onUpdate:modelValue":t[8]||(t[8]=o=>e.enrollmentType=o),name:"enrollmentType",label:r.$trans("student.enrollment_type.enrollment_type"),options:g.enrollmentTypes,"label-prop":"name","value-prop":"uuid",error:a(u).enrollmentType,"onUpdate:error":t[9]||(t[9]=o=>a(u).enrollmentType=o)},null,8,["modelValue","label","options","error"])]),d("div",je,[n(B,{multiple:"",modelValue:e.electiveSubjects,"onUpdate:modelValue":t[10]||(t[10]=o=>e.electiveSubjects=o),name:"electiveSubjects",label:r.$trans("academic.subject.elective_subject"),options:g.electiveSubjects,"label-prop":"name","value-prop":"uuid",error:a(u).electiveSubjects,"onUpdate:error":t[11]||(t[11]=o=>a(u).electiveSubjects=o)},null,8,["modelValue","label","options","error"])]),d("div",De,[n(B,{multiple:"",modelValue:e.groups,"onUpdate:modelValue":t[12]||(t[12]=o=>e.groups=o),name:"groups",label:r.$trans("student.group.group"),options:g.groups,"label-prop":"name","value-prop":"uuid",error:a(u).groups,"onUpdate:error":t[13]||(t[13]=o=>a(u).groups=o)},null,8,["modelValue","label","options","error"])]),d("div",Ne,[n(N,{vertical:"",modelValue:e.assignFee,"onUpdate:modelValue":t[14]||(t[14]=o=>e.assignFee=o),name:"assignFee",label:r.$trans("global.assign",{attribute:r.$trans("student.fee.fee")}),error:a(u).assignFee,"onUpdate:error":t[15]||(t[15]=o=>a(u).assignFee=o)},null,8,["modelValue","label","error"])]),e.assignFee?(b(),U(S,{key:0},[d("div",Se,[n(B,{modelValue:e.feeConcession,"onUpdate:modelValue":t[16]||(t[16]=o=>e.feeConcession=o),name:"feeConcession",label:r.$trans("finance.fee_concession.fee_concession"),options:g.feeConcessions,"label-prop":"name","value-prop":"uuid",error:a(u).feeConcession,"onUpdate:error":t[17]||(t[17]=o=>a(u).feeConcession=o)},null,8,["modelValue","label","options","error"])]),d("div",Re,[n(B,{modelValue:e.transportCircle,"onUpdate:modelValue":t[18]||(t[18]=o=>e.transportCircle=o),name:"transportCircle",label:r.$trans("transport.circle.circle"),options:g.transportCircles,"label-prop":"name","value-prop":"uuid",error:a(u).transportCircle,"onUpdate:error":t[19]||(t[19]=o=>a(u).transportCircle=o)},null,8,["modelValue","label","options","error"])]),e.transportCircle?(b(),U("div",Pe,[n(_,null,{default:s(()=>[m(i(r.$trans("transport.circle.direction")),1)]),_:1}),n(R,{"top-margin":"",options:g.directions,name:"direction",modelValue:e.direction,"onUpdate:modelValue":t[20]||(t[20]=o=>e.direction=o),error:a(u).direction,"onUpdate:error":t[21]||(t[21]=o=>a(u).direction=o),horizontal:""},null,8,["options","modelValue","error"])])):v("",!0),d("div",Fe,[n(_,null,{default:s(()=>[m(i(r.$trans("student.fee.opted_fee")),1)]),_:1}),d("div",Ae,[(b(!0),U(S,null,Z(g.feeHeads,o=>(b(),U("div",{class:"w-1/3",key:o.uuid},[n(A,{items:e.optedFeeHeads,"onUpdate:items":t[22]||(t[22]=X=>e.optedFeeHeads=X),value:o.uuid,label:o.name},null,8,["items","value","label"])]))),128))])])],64)):v("",!0),d("div",Me,[n(N,{vertical:"",modelValue:e.createUserAccount,"onUpdate:modelValue":t[23]||(t[23]=o=>e.createUserAccount=o),name:"createUserAccount",label:r.$trans("global.create",{attribute:r.$trans("contact.user_account")}),error:a(u).createUserAccount,"onUpdate:error":t[24]||(t[24]=o=>a(u).createUserAccount=o)},null,8,["modelValue","label","error"])]),e.createUserAccount?(b(),U(S,{key:1},[d("div",Te,[n(D,{type:"text",modelValue:e.email,"onUpdate:modelValue":t[25]||(t[25]=o=>e.email=o),name:"email",label:r.$trans("contact.login.props.email"),error:a(u).email,"onUpdate:error":t[26]||(t[26]=o=>a(u).email=o)},null,8,["modelValue","label","error"])]),d("div",Ie,[n(D,{type:"text",modelValue:e.username,"onUpdate:modelValue":t[27]||(t[27]=o=>e.username=o),name:"username",label:r.$trans("contact.login.props.username"),error:a(u).username,"onUpdate:error":t[28]||(t[28]=o=>a(u).username=o)},null,8,["modelValue","label","error"])]),d("div",Le,[n(D,{type:w.hidePassword?"password":"text",modelValue:e.password,"onUpdate:modelValue":t[30]||(t[30]=o=>e.password=o),name:"password",label:r.$trans("contact.login.props.password"),error:a(u).password,"onUpdate:error":t[31]||(t[31]=o=>a(u).password=o)},{"additional-label":s(()=>[d("div",qe,[de(d("i",{class:"fas fa-key cursor-pointer",onClick:f},null,512),[[Q,r.$trans("global.generate",{attribute:r.$trans("auth.login.props.password")})]]),e.password?(b(),U("i",{key:0,class:"fas fa-eye cursor-pointer",onClick:t[29]||(t[29]=o=>w.hidePassword=!w.hidePassword)})):v("",!0)])]),_:1},8,["type","modelValue","label","error"])]),d("div",ze,[n(D,{type:"password",modelValue:e.passwordConfirmation,"onUpdate:modelValue":t[32]||(t[32]=o=>e.passwordConfirmation=o),name:"passwordConfirmation",label:r.$trans("contact.login.props.password_confirmation"),error:a(u).passwordConfirmation,"onUpdate:error":t[33]||(t[33]=o=>a(u).passwordConfirmation=o)},null,8,["modelValue","label","error"])])],64)):v("",!0),d("div",Oe,[n(c,{modelValue:e.remarks,"onUpdate:modelValue":t[34]||(t[34]=o=>e.remarks=o),name:"remarks",label:r.$trans("student.admission.props.remarks"),error:a(u).remarks,"onUpdate:error":t[35]||(t[35]=o=>a(u).remarks=o)},null,8,["modelValue","label","error"])])],64)):v("",!0),e.status=="rejected"?(b(),U("div",Ee,[n(c,{modelValue:e.rejectionRemarks,"onUpdate:modelValue":t[36]||(t[36]=o=>e.rejectionRemarks=o),name:"rejectionRemarks",label:r.$trans("student.registration.props.rejection_remarks"),error:a(u).rejectionRemarks,"onUpdate:error":t[37]||(t[37]=o=>a(u).rejectionRemarks=o)},null,8,["modelValue","label","error"])])):v("",!0)])]}),_:1},8,["uuid","no-action-button","form"])}}}),Ge={class:"space-y-2"},We={key:0},Je={class:"space-x-2"},Ke={class:"font-bold"},Qe={class:"mt-4 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Xe={class:"text-sm space-y-2"},Ye={name:"StudentRegistrationShow"},xe=Object.assign(Ye,{setup(T){ee();const I=x(),z=me(),O=Y("emitter"),l=Y("$trans"),h={},u="student/registration/",w=[{key:"codeNumber",label:l("finance.transaction.props.code_number"),visibility:!0},{key:"ledger",label:l("finance.ledger.ledger"),visibility:!0},{key:"amount",label:l("finance.transaction.props.amount"),visibility:!0},{key:"date",label:l("finance.transaction.props.date"),visibility:!0},{key:"action",label:"",visibility:!0}],g=ae(!1),e=H({...h}),L=j=>{Object.assign(e,j)},M=j=>{window.open(`/app/student/registrations/${e.uuid}/fee/${j}/export?action=print`)},f=async j=>{O.emit("showActionItem",{uuid:e.uuid,moduleUuid:j,action:"cancelPayment",confirmation:!0})};return(j,V)=>{const E=p("BaseButton"),r=p("PageHeaderAction"),t=p("PageHeader"),_=p("ListItemView"),R=p("BaseBadge"),D=p("ListContainerVertical"),P=p("BaseCard"),B=p("BaseAlert"),F=p("BaseDataView"),N=p("ListMedia"),A=p("DataCell"),c=p("TextMuted"),W=p("FloatingMenuItem"),Q=p("FloatingMenu"),J=p("DataRow"),o=p("SimpleTable"),X=p("DetailLayoutVertical"),oe=p("ShowItem"),ne=p("ParentTransition");return b(),U(S,null,[n(t,{title:a(l)(a(I).meta.trans,{attribute:a(l)(a(I).meta.label)}),navs:[{label:a(l)("student.student"),path:"Student"},{label:a(l)("student.registration.registration"),path:"StudentRegistrationList"}]},{default:s(()=>[n(r,{name:"StudentRegistration",title:a(l)("student.registration.registration"),actions:["list"]},{default:s(()=>[a(K)("registration:edit")&&e.isEditable?(b(),k(E,{key:0,design:"white",onClick:V[0]||(V[0]=y=>a(z).push({name:"StudentRegistrationEdit",params:{uuid:e.uuid}}))},{default:s(()=>[m(i(a(l)("general.edit")),1)]),_:1})):v("",!0)]),_:1},8,["title"])]),_:1},8,["title","navs"]),n(ne,{appear:"",visibility:!0},{default:s(()=>[n(oe,{"init-url":u,uuid:a(I).params.uuid,refresh:g.value,onRefreshed:V[3]||(V[3]=y=>g.value=!1),onSetItem:L,onRedirectTo:V[4]||(V[4]=y=>a(z).push({name:"Registration"}))},{default:s(()=>[e.uuid?(b(),k(X,{key:0},{detail:s(()=>[d("div",Ge,[n(P,{"no-padding":"","no-content-padding":""},{title:s(()=>[m(i(a(l)("student.registration.registration"))+" #"+i(e.codeNumber),1)]),action:s(()=>V[5]||(V[5]=[])),default:s(()=>[n(D,null,{default:s(()=>[e.isOnline?(b(),k(_,{key:0,label:a(l)("student.online_registration.props.number")},{default:s(()=>[m(i(e.applicationNumber),1)]),_:1},8,["label"])):v("",!0),n(_,{label:a(l)("student.registration.props.date")},{default:s(()=>[m(i(e.date.formatted),1)]),_:1},8,["label"]),n(_,{label:a(l)("student.props.name")},{default:s(()=>[m(i(e.contact.name),1)]),_:1},8,["label"]),n(_,{label:a(l)("contact.props.gender")},{default:s(()=>[m(i(e.contact.gender.label),1)]),_:1},8,["label"]),n(_,{label:a(l)("contact.props.birth_date")},{default:s(()=>[m(i(e.contact.birthDate.formatted),1)]),_:1},8,["label"]),n(_,{label:a(l)("contact.props.contact_number")},{default:s(()=>[m(i(e.contact.contactNumber),1)]),_:1},8,["label"]),n(_,{label:a(l)("contact.props.email")},{default:s(()=>[m(i(e.contact.email),1)]),_:1},8,["label"]),n(_,{label:a(l)("academic.course.course")},{default:s(()=>{var y,C;return[m(i(((y=e.course)==null?void 0:y.name)||"-")+" ",1),(C=e.course)!=null&&C.uuid?(b(),U("span",We,"("+i(e.course.division.name)+")",1)):v("",!0)]}),_:1},8,["label"]),n(_,{label:a(l)("student.registration.props.status")},{default:s(()=>[d("div",Je,[n(R,{design:e.status.color},{default:s(()=>[m(i(e.status.label),1)]),_:1},8,["design"]),e.isOnline?(b(),k(R,{key:0},{default:s(()=>[m(i(a(l)("student.registration.online")),1)]),_:1})):v("",!0)])]),_:1},8,["label"]),n(_,{label:a(l)("academic.course.props.registration_fee")},{default:s(()=>[m(i(e.fee.formatted),1)]),_:1},8,["label"]),n(_,{label:a(l)("student.registration.props.payment_status")},{default:s(()=>[n(R,{design:e.paymentStatus.color},{default:s(()=>[m(i(e.paymentStatus.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),n(_,{label:a(l)("guardian.guardian")},{default:s(()=>{var y,C;return[m(i((C=(y=e.contact.guardian)==null?void 0:y.contact)==null?void 0:C.name),1)]}),_:1},8,["label"]),n(_,{label:a(l)("contact.props.contact_number")},{default:s(()=>{var y,C;return[m(i((C=(y=e.contact.guardian)==null?void 0:y.contact)==null?void 0:C.contactNumber),1)]}),_:1},8,["label"]),(b(!0),U(S,null,Z(e.customFields||[],y=>(b(),k(_,{key:y.uuid,label:y.label},{default:s(()=>[m(i(y.formattedValue),1)]),_:2},1032,["label"]))),128)),n(_,{label:a(l)("general.created_at")},{default:s(()=>[m(i(e.createdAt.formatted),1)]),_:1},8,["label"]),n(_,{label:a(l)("general.updated_at")},{default:s(()=>[m(i(e.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:s(()=>{var y,C;return[e.status.value=="initiated"?(b(),k(P,{key:0},{default:s(()=>[n(B,{size:"xs",design:"info"},{default:s(()=>[m(i(a(l)("student.registration.initiated_info")),1)]),_:1})]),_:1})):(b(),U(S,{key:1},[e.status.value=="pending"&&e.fee.value>0&&e.paymentStatus.value!="paid"?(b(),k(ke,{key:0,registration:e,onCompleted:V[1]||(V[1]=$=>g.value=!0)},null,8,["registration"])):e.status.value!="pending"?(b(),k(P,{key:1},{default:s(()=>[e.status.value=="rejected"?(b(),k(B,{key:0,size:"xs",design:"error"},{default:s(()=>[m(i(a(l)("student.registration.rejected_info",{attribute:e.rejectedAt.formatted}))+" ",1),d("p",Ke,i(e.rejectionRemarks),1)]),_:1})):v("",!0),e.status.value=="approved"?(b(),U(S,{key:1},[n(B,{size:"xs",design:"success"},{default:s(()=>[m(i(a(l)("student.registration.approved_info")),1)]),_:1}),d("dl",Qe,[n(F,{label:a(l)("student.admission.props.code_number")},{default:s(()=>[m(i(e.admission.codeNumber),1)]),_:1},8,["label"]),n(F,{label:a(l)("student.admission.props.date")},{default:s(()=>[m(i(e.admission.joiningDate.formatted),1)]),_:1},8,["label"]),n(F,{class:"col-span-1 sm:col-span-2",label:a(l)("academic.batch.batch")},{default:s(()=>{var $,q,G;return[m(i((($=e.course)==null?void 0:$.name)||"-")+" "+i(((G=(q=e.admission)==null?void 0:q.batch)==null?void 0:G.name)||"-"),1)]}),_:1},8,["label"]),n(F,{label:a(l)("general.created_at")},{default:s(()=>[m(i(e.admission.createdAt.formatted),1)]),_:1},8,["label"]),n(F,{label:a(l)("general.updated_at")},{default:s(()=>[m(i(e.admission.updatedAt.formatted),1)]),_:1},8,["label"])])],64)):v("",!0)]),_:1})):(b(),U(S,{key:2},[a(K)("registration:action")?(b(),k(he,{key:0,registration:e,onCompleted:V[2]||(V[2]=$=>g.value=!0)},null,8,["registration"])):v("",!0)],64)),((y=e.media)==null?void 0:y.length)>0?(b(),k(P,{key:3},{title:s(()=>[m(i(a(l)("general.media")),1)]),default:s(()=>[d("div",Xe,[d("div",null,[d("h2",null,i(a(l)("student.online_registration.wizard.marksheet")),1),n(N,{section:"marksheet",media:e.media,url:`/app/student/registrations/${e.uuid}/`},null,8,["media","url"])]),d("div",null,[d("h2",null,i(a(l)("student.online_registration.wizard.transfer_certificate")),1),n(N,{section:"transfer_certificate",media:e.media,url:`/app/student/registrations/${e.uuid}/`},null,8,["media","url"])]),d("div",null,[d("h2",null,i(a(l)("contact.props.id_proof")),1),n(N,{section:"id_proof",media:e.media,url:`/app/student/registrations/${e.uuid}/`},null,8,["media","url"])]),d("div",null,[d("h2",null,i(a(l)("contact.props.address_proof")),1),n(N,{section:"address_proof",media:e.media,url:`/app/student/registrations/${e.uuid}/`},null,8,["media","url"])]),d("div",null,[d("h2",null,i(a(l)("contact.props.signature")),1),n(N,{section:"signature",media:e.media,url:`/app/student/registrations/${e.uuid}/`},null,8,["media","url"])])])]),_:1})):v("",!0),((C=e.transactions)==null?void 0:C.length)>0?(b(),k(P,{key:4,"no-padding":"","no-content-padding":"","bottom-content-padding":"",class:"mt-4"},{title:s(()=>[m(i(a(l)("finance.transaction.transaction")),1)]),default:s(()=>[n(o,{header:w},{default:s(()=>[(b(!0),U(S,null,Z(e.transactions,$=>(b(),k(J,{key:$.uuid},{default:s(()=>[n(A,{name:"codeNumber"},{default:s(()=>[m(i($.codeNumber)+" ",1),$.isCancelled?(b(),k(R,{key:0,design:"danger",size:"sm"},{default:s(()=>[m(i(a(l)("general.cancelled")),1)]),_:1})):v("",!0)]),_:2},1024),n(A,{name:"ledger"},{default:s(()=>{var q;return[m(i((q=$==null?void 0:$.payment)==null?void 0:q.ledger.name)+" ",1),n(c,null,{default:s(()=>{var G;return[m("("+i((G=$.payment)==null?void 0:G.methodName)+")",1)]}),_:2},1024)]}),_:2},1024),n(A,{name:"amount"},{default:s(()=>[m(i($.amount.formatted),1)]),_:2},1024),n(A,{name:"date"},{default:s(()=>[m(i($.date.formatted),1)]),_:2},1024),n(A,{name:"action"},{default:s(()=>[n(Q,null,{default:s(()=>[n(W,{icon:"fas fa-print",onClick:q=>M($.uuid)},{default:s(()=>[m(i(a(l)("general.print")),1)]),_:2},1032,["onClick"]),e.status.value=="pending"&&a(K)("registration:action")&&!$.isCancelled?(b(),k(W,{key:0,icon:"fas fa-trash",onClick:q=>f($.uuid)},{default:s(()=>[m(i(a(l)("global.cancel",{attribute:a(l)("finance.transaction.transaction")})),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})):v("",!0)],64))]}),_:1})):v("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{xe as default};
