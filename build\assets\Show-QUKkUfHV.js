import{i as j,u as N,h as $,j as E,l as F,r as n,a as b,o as r,e as i,w as e,f as t,q as u,b as _,d as O,F as T,v as q,s,t as o,y as U}from"./app-BAwPsakn.js";const z={class:"space-y-4"},G={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},J={name:"AcademicClassTimingShow"},Q=Object.assign(J,{setup(K){j();const p=N(),f=$(),a=E("$trans"),B={},C="academic/classTiming/",v=[{key:"name",label:a("academic.class_timing.session"),visibility:!0},{key:"break",label:a("academic.class_timing.break"),visibility:!0},{key:"start_time",label:a("academic.class_timing.props.start_time"),visibility:!0},{key:"end_time",label:a("academic.class_timing.props.end_time"),visibility:!0},{key:"action",label:"",visibility:!0}],l=F({...B}),w=y=>{Object.assign(l,y)};return(y,m)=>{const V=n("PageHeaderAction"),h=n("PageHeader"),g=n("ListItemView"),S=n("ListContainerVertical"),k=n("BaseCard"),d=n("DataCell"),A=n("DataRow"),D=n("SimpleTable"),L=n("BaseDataView"),x=n("BaseButton"),I=n("ShowButton"),P=n("DetailLayoutVertical"),H=n("ShowItem"),R=n("ParentTransition");return r(),b(T,null,[i(h,{title:t(a)(t(p).meta.trans,{attribute:t(a)(t(p).meta.label)}),navs:[{label:t(a)("academic.academic"),path:"Academic"}]},{default:e(()=>[i(V,{name:"AcademicClassTiming",title:t(a)("academic.class_timing.class_timing"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(R,{appear:"",visibility:!0},{default:e(()=>[i(H,{"init-url":C,uuid:t(p).params.uuid,onSetItem:w,onRedirectTo:m[1]||(m[1]=c=>t(f).push({name:"AcademicClassTiming"}))},{default:e(()=>[l.uuid?(r(),u(P,{key:0},{detail:e(()=>[i(k,{"no-padding":"","no-content-padding":""},{title:e(()=>[s(o(t(a)("global.detail",{attribute:t(a)("academic.class_timing.props.session")})),1)]),default:e(()=>[i(S,null,{default:e(()=>[i(g,{label:t(a)("academic.class_timing.props.name")},{default:e(()=>[s(o(l.name),1)]),_:1},8,["label"]),i(g,{label:t(a)("general.created_at")},{default:e(()=>[s(o(l.createdAt.formatted),1)]),_:1},8,["label"]),i(g,{label:t(a)("general.updated_at")},{default:e(()=>[s(o(l.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[O("div",z,[i(k,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[s(o(t(a)("academic.class_timing.props.session")),1)]),footer:e(()=>[i(I,null,{default:e(()=>[t(U)("class-timing:edit")?(r(),u(x,{key:0,design:"primary",onClick:m[0]||(m[0]=c=>t(f).push({name:"AcademicClassTimingEdit",params:{uuid:l.uuid}}))},{default:e(()=>[s(o(t(a)("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[l.sessions.length>0?(r(),u(D,{key:0,header:v},{default:e(()=>[(r(!0),b(T,null,q(l.sessions,c=>(r(),u(A,{key:c.uuid},{default:e(()=>[i(d,{name:"name"},{default:e(()=>[s(o(c.name),1)]),_:2},1024),i(d,{name:"break"},{default:e(()=>[s(o(c.isBreak?t(a)("general.yes"):t(a)("general.no")),1)]),_:2},1024),i(d,{name:"startTime"},{default:e(()=>[s(o(c.startTime.formatted),1)]),_:2},1024),i(d,{name:"endtime"},{default:e(()=>[s(o(c.endTime.formatted),1)]),_:2},1024),i(d,{name:"action"})]),_:2},1024))),128))]),_:1})):_("",!0),l.description?(r(),b("dl",G,[i(L,{class:"col-span-1 sm:col-span-2",label:t(a)("academic.class_timing.props.description")},{default:e(()=>[s(o(l.description),1)]),_:1},8,["label"])])):_("",!0)]),_:1})])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Q as default};
