import{u as O,l as q,n as z,r as o,q as f,o as u,w as t,d as v,e as s,h as G,i as J,j as K,y as D,m as Q,f as m,a as F,F as I,v as S,b as y,s as p,t as c}from"./app-BAwPsakn.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(j,{emit:g}){const k=O(),w=g,V=j,h={name:"",email:"",username:"",status:"",roles:[],withDirectPermission:!1},l=q({...h}),B=q({statuses:V.preRequisites.statuses,roles:V.preRequisites.roles}),$=q({isLoaded:!k.query.roles});return z(async()=>{$.roles=k.query.roles?k.query.roles.split(","):[]}),(d,a)=>{const C=o("BaseInput"),P=o("BaseSelect"),n=o("BaseSwitch"),i=o("FilterForm");return u(),f(i,{"init-form":h,form:l,multiple:["roles"],onHide:a[6]||(a[6]=r=>w("hide"))},{default:t(()=>[v("div",W,[v("div",X,[s(C,{type:"text",modelValue:l.name,"onUpdate:modelValue":a[0]||(a[0]=r=>l.name=r),name:"name",label:d.$trans("user.props.name")},null,8,["modelValue","label"])]),v("div",Y,[s(C,{type:"text",modelValue:l.email,"onUpdate:modelValue":a[1]||(a[1]=r=>l.email=r),name:"email",label:d.$trans("user.props.email")},null,8,["modelValue","label"])]),v("div",Z,[s(C,{type:"text",modelValue:l.username,"onUpdate:modelValue":a[2]||(a[2]=r=>l.username=r),name:"username",label:d.$trans("user.props.username")},null,8,["modelValue","label"])]),v("div",x,[s(P,{modelValue:l.status,"onUpdate:modelValue":a[3]||(a[3]=r=>l.status=r),name:"status",label:d.$trans("user.status"),options:B.statuses},null,8,["modelValue","label","options"])]),v("div",ee,[s(P,{multiple:"",modelValue:l.roles,"onUpdate:modelValue":a[4]||(a[4]=r=>l.roles=r),name:"role",label:d.$trans("team.config.role.role"),"label-prop":"name","value-prop":"uuid",options:B.roles},null,8,["modelValue","label","options"])]),v("div",te,[s(n,{vertical:"",modelValue:l.withDirectPermission,"onUpdate:modelValue":a[5]||(a[5]=r=>l.withDirectPermission=r),name:"withDirectPermission",label:d.$trans("user.with_direct_permission")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ae={name:"UserList"},le=Object.assign(ae,{setup(j){const g=G(),k=J(),w=K("emitter");let V=["filter"];D("user:create")&&V.unshift("create");let h=[];D("user:export")&&(h=["print","pdf","excel"]);const l="user/",B=q({}),$=Q(!1),d=q({}),a=n=>{Object.assign(d,n)},C=n=>{Object.assign(B,n)},P=async n=>{await k.dispatch("auth/user/impersonate",{uuid:n}).then(i=>{window.location.href="/app"}).catch(i=>{})};return(n,i)=>{const r=o("PageHeaderAction"),T=o("PageHeader"),H=o("ParentTransition"),_=o("DataCell"),R=o("BaseBadge"),U=o("FloatingMenuItem"),A=o("FloatingMenu"),L=o("DataRow"),M=o("BaseButton"),E=o("DataTable"),N=o("ListItem");return u(),f(N,{"init-url":l,"pre-requisites":!0,onSetPreRequisites:C,onSetItems:a},{header:t(()=>[s(T,{title:n.$trans("user.user")},{default:t(()=>[s(r,{url:"users/",name:"User",title:n.$trans("user.user"),actions:m(V),"dropdown-actions":m(h),onToggleFilter:i[0]||(i[0]=e=>$.value=!$.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title"])]),filter:t(()=>[s(H,{appear:"",visibility:$.value},{default:t(()=>[s(se,{onRefresh:i[1]||(i[1]=e=>m(w).emit("listItems")),"pre-requisites":B,onHide:i[2]||(i[2]=e=>$.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:t(()=>[s(H,{appear:"",visibility:!0},{default:t(()=>[s(E,{header:d.headers,meta:d.meta,module:"user",onRefresh:i[4]||(i[4]=e=>m(w).emit("listItems"))},{actionButton:t(()=>[m(D)("user:create")?(u(),f(M,{key:0,onClick:i[3]||(i[3]=e=>m(g).push({name:"UserCreate"}))},{default:t(()=>[p(c(n.$trans("global.add",{attribute:n.$trans("user.user")})),1)]),_:1})):y("",!0)]),default:t(()=>[(u(!0),F(I,null,S(d.data,e=>(u(),f(L,{key:e.uuid,onDoubleClick:b=>m(g).push({name:"UserShow",params:{uuid:e.uuid}})},{default:t(()=>[s(_,{name:"name"},{default:t(()=>[p(c(e.profile.name),1)]),_:2},1024),s(_,{name:"email"},{default:t(()=>[p(c(e.email),1)]),_:2},1024),s(_,{name:"username"},{default:t(()=>[p(c(e.username),1)]),_:2},1024),s(_,{name:"roles"},{default:t(()=>[(u(!0),F(I,null,S(e.roles,b=>(u(),F("div",null,[s(R,{label:b.label},null,8,["label"])]))),256))]),_:2},1024),e.showPermissions?(u(),f(_,{key:0,name:"permissions"},{default:t(()=>[(u(!0),F(I,null,S(e.permissions,b=>(u(),F("div",null,[s(R,{label:b},null,8,["label"])]))),256))]),_:2},1024)):y("",!0),s(_,{name:"status"},{default:t(()=>[s(R,{label:e.status.label,design:e.status.color},null,8,["label","design"])]),_:2},1024),s(_,{name:"createdAt"},{default:t(()=>[p(c(e.createdAt.formatted),1)]),_:2},1024),s(_,{name:"action"},{default:t(()=>[e.isEditable||e.isDeletable||m(D)("user:impersonate")?(u(),f(A,{key:0},{default:t(()=>[m(D)("user:impersonate")?(u(),f(U,{key:0,icon:"fas fa-key",onClick:b=>P(e.uuid)},{default:t(()=>[p(c(n.$trans("user.impersonate")),1)]),_:2},1032,["onClick"])):y("",!0),s(U,{icon:"fas fa-eye",onClick:b=>m(g).push({name:"UserShow",params:{uuid:e.uuid}})},{default:t(()=>[p(c(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),e.isEditable?(u(),f(U,{key:1,icon:"fas fa-edit",onClick:b=>m(g).push({name:"UserEdit",params:{uuid:e.uuid}})},{default:t(()=>[p(c(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):y("",!0),e.isDeletable?(u(),f(U,{key:2,icon:"fas fa-trash",onClick:b=>m(w).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[p(c(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):y("",!0)]),_:2},1024)):y("",!0)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{le as default};
