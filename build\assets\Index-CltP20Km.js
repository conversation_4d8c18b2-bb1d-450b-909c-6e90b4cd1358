import{r as o,q as i,o as e,w as t,e as c}from"./app-BAwPsakn.js";const b={name:"LibraryConfig"},g=Object.assign(b,{setup(l){const a=[{name:"LibraryConfigBookAuthor",icon:"fas fa-chevron-right",label:"library.book_author.book_author"},{name:"LibraryConfigBookPublisher",icon:"fas fa-chevron-right",label:"library.book_publisher.book_publisher"},{name:"LibraryConfigBookLanguage",icon:"fas fa-chevron-right",label:"library.book_language.book_language"},{name:"LibraryConfigBookTopic",icon:"fas fa-chevron-right",label:"library.book_topic.book_topic"},{name:"LibraryConfigBookCondition",icon:"fas fa-chevron-right",label:"library.book_condition.book_condition"}];return(s,_)=>{const r=o("router-view"),n=o("ModuleConfig");return e(),i(n,{navigations:a},{default:t(()=>[c(r)]),_:1})}}});export{g as default};
