import{r as n,q as a,o as c,w as i,e as r}from"./app-BAwPsakn.js";const s={name:"CommunicationConfig"},l=Object.assign(s,{setup(m){const o=[{name:"CommunicationConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"CommunicationConfigAnnouncementType",icon:"fas fa-chevron-right",label:"communication.announcement.type.type"}];return(f,u)=>{const e=n("router-view"),t=n("ModuleConfig");return c(),a(t,{navigations:o},{default:i(()=>[r(e)]),_:1})}}});export{l as default};
