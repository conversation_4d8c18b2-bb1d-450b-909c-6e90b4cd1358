import{u as L,l as N,n as R,r as s,q as _,o as d,w as e,d as V,e as t,h as j,j as S,y as f,m as O,f as o,a as U,F as E,v as q,s as i,t as l,b as C}from"./app-BAwPsakn.js";import{_ as x}from"./ModuleDropdown-sYr-p8uC.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(F,{emit:c}){L();const v=c,g={registrationNumber:"",modelNumber:""},m=N({...g}),T=N({isLoaded:!0});return R(async()=>{T.isLoaded=!0}),(p,u)=>{const h=s("BaseInput"),r=s("FilterForm");return d(),_(r,{"init-form":g,form:m,multiple:[],onHide:u[2]||(u[2]=n=>v("hide"))},{default:e(()=>[V("div",z,[V("div",G,[t(h,{type:"text",modelValue:m.registrationNumber,"onUpdate:modelValue":u[0]||(u[0]=n=>m.registrationNumber=n),name:"registrationNumber",label:p.$trans("transport.vehicle.props.registration_number")},null,8,["modelValue","label"])]),V("div",J,[t(h,{type:"text",modelValue:m.modelNumber,"onUpdate:modelValue":u[1]||(u[1]=n=>m.modelNumber=n),name:"modelNumber",label:p.$trans("transport.vehicle.props.model_number")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Q={name:"TransportVehicleList"},Y=Object.assign(Q,{setup(F){const c=j(),v=S("emitter");let g=["filter"];f("vehicle:config")&&g.push("config"),f("vehicle:create")&&g.unshift("create");let m=[];f("vehicle:export")&&(m=["print","pdf","excel"]);const T="transport/vehicle/",p=O(!1),u=N({}),h=r=>{Object.assign(u,r)};return(r,n)=>{const w=s("PageHeaderAction"),B=s("PageHeader"),y=s("ParentTransition"),b=s("DataCell"),D=s("TextMuted"),$=s("FloatingMenuItem"),I=s("FloatingMenu"),M=s("DataRow"),P=s("BaseButton"),A=s("DataTable"),H=s("ListItem");return d(),_(H,{"init-url":T,onSetItems:h},{header:e(()=>[t(B,{title:r.$trans("transport.vehicle.vehicle"),navs:[{label:r.$trans("transport.transport"),path:"Transport"}]},{default:e(()=>[t(w,{url:"transport/vehicles/",name:"TransportVehicle",title:r.$trans("transport.vehicle.vehicle"),actions:o(g),"dropdown-actions":o(m),"config-path":"TransportVehicleConfigDocumentType",onToggleFilter:n[0]||(n[0]=a=>p.value=!p.value)},{moduleOption:e(()=>[t(x)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(y,{appear:"",visibility:p.value},{default:e(()=>[t(K,{onRefresh:n[1]||(n[1]=a=>o(v).emit("listItems")),onHide:n[2]||(n[2]=a=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(y,{appear:"",visibility:!0},{default:e(()=>[t(A,{header:u.headers,meta:u.meta,module:"transport.vehicle",onRefresh:n[4]||(n[4]=a=>o(v).emit("listItems"))},{actionButton:e(()=>[o(f)("vehicle:create")?(d(),_(P,{key:0,onClick:n[3]||(n[3]=a=>o(c).push({name:"TransportVehicleCreate"}))},{default:e(()=>[i(l(r.$trans("global.add",{attribute:r.$trans("transport.vehicle.vehicle")})),1)]),_:1})):C("",!0)]),default:e(()=>[(d(!0),U(E,null,q(u.data,a=>(d(),_(M,{key:a.uuid,onDoubleClick:k=>o(c).push({name:"TransportVehicleShow",params:{uuid:a.uuid}})},{default:e(()=>[t(b,{name:"name"},{default:e(()=>[i(l(a.name),1)]),_:2},1024),t(b,{name:"registrationNumber"},{default:e(()=>[i(l(a.registrationNumber)+" ",1),t(D,{block:""},{default:e(()=>[i(l(a.registrationPlace),1)]),_:2},1024)]),_:2},1024),t(b,{name:"registrationDate"},{default:e(()=>[i(l(a.registrationDate.formatted),1)]),_:2},1024),t(b,{name:"modelNumber"},{default:e(()=>[i(l(a.modelNumber)+" ",1),t(D,{block:""},{default:e(()=>[i(l(a.make),1)]),_:2},1024)]),_:2},1024),t(b,{name:"createdAt"},{default:e(()=>[i(l(a.createdAt.formatted),1)]),_:2},1024),t(b,{name:"action"},{default:e(()=>[t(I,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:k=>o(c).push({name:"TransportVehicleShow",params:{uuid:a.uuid}})},{default:e(()=>[i(l(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(f)("vehicle:edit")?(d(),_($,{key:0,icon:"fas fa-edit",onClick:k=>o(c).push({name:"TransportVehicleEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(l(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):C("",!0),o(f)("vehicle:create")?(d(),_($,{key:1,icon:"fas fa-copy",onClick:k=>o(c).push({name:"TransportVehicleDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[i(l(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):C("",!0),o(f)("vehicle:delete")?(d(),_($,{key:2,icon:"fas fa-trash",onClick:k=>o(v).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(l(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):C("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
