import{u as P,l as h,H as x,r as n,q,o as f,w as t,d as m,e as o,f as e,h as L,j as C,m as Q,a as T,b as w,t as r,s as i,y as U,F as D}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-3 gap-6"},N={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3"},E={name:"ReceptionQueryActionForm"},z=Object.assign(E,{props:{query:{type:Object,required:!0}},emits:["completed"],setup(j,{emit:_}){const V=_;P();const s={status:"",remarks:""},b="reception/query/",y=h({statuses:[]}),p=x(b),a=h({...s}),k=l=>{Object.assign(y,l)},g=()=>{V("completed")};return(l,u)=>{const B=n("BaseSelect"),v=n("BaseTextarea"),d=n("FormAction");return f(),q(d,{"no-card":"","pre-requisites":!0,onSetPreRequisites:k,"keep-adding":!1,"init-url":b,uuid:j.query.uuid,"no-data-fetch":!0,action:"action","init-form":s,form:a,"after-submit":g},{default:t(()=>[m("div",H,[m("div",N,[o(B,{name:"status",label:l.$trans("global.select",{attribute:l.$trans("reception.query.props.status")}),modelValue:a.status,"onUpdate:modelValue":u[0]||(u[0]=c=>a.status=c),options:y.statuses,error:e(p).status,"onUpdate:error":u[1]||(u[1]=c=>e(p).status=c)},null,8,["label","modelValue","options","error"])]),m("div",O,[o(v,{rows:1,modelValue:a.remarks,"onUpdate:modelValue":u[2]||(u[2]=c=>a.remarks=c),name:"remarks",label:l.$trans("reception.query.props.remarks"),error:e(p).remarks,"onUpdate:error":u[3]||(u[3]=c=>e(p).remarks=c)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),G={class:"space-y-4"},J={class:"px-4 py-2 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},K={class:"font-semibold"},M={name:"ReceptionQueryShow"},X=Object.assign(M,{setup(j){const _=P(),V=L();C("emitter");const s=C("$trans"),b={},y="reception/query/",p=Q(!1),a=h({...b}),k=g=>{Object.assign(a,g)};return(g,l)=>{const u=n("PageHeaderAction"),B=n("PageHeader"),v=n("BaseBadge"),d=n("ListItemView"),c=n("ListContainerVertical"),R=n("BaseCard"),$=n("BaseDataView"),A=n("DetailLayoutVertical"),F=n("ShowItem"),I=n("ParentTransition");return f(),T(D,null,[o(B,{title:e(s)(e(_).meta.trans,{attribute:e(s)(e(_).meta.label)}),navs:[{label:e(s)("reception.reception"),path:"Reception"},{label:e(s)("reception.query.query"),path:"ReceptionQuery"}]},{default:t(()=>[o(u,{name:"ReceptionQuery",title:e(s)("reception.query.query"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(I,{appear:"",visibility:!0},{default:t(()=>[o(F,{"init-url":y,uuid:e(_).params.uuid,"module-uuid":e(_).params.muuid,onSetItem:k,onRedirectTo:l[1]||(l[1]=S=>e(V).push({name:"ReceptionQuery",params:{uuid:a.uuid}})),refresh:p.value,onRefreshed:l[2]||(l[2]=S=>p.value=!1)},{default:t(()=>[a.uuid?(f(),q(A,{key:0},{detail:t(()=>[o(R,{"no-padding":"","no-content-padding":""},{title:t(()=>[i(r(e(s)("global.detail",{attribute:e(s)("reception.query.query")}))+" ",1),o(v,{design:a.status.color},{default:t(()=>[i(r(a.status.label),1)]),_:1},8,["design"])]),default:t(()=>[o(c,null,{default:t(()=>[o(d,{label:e(s)("reception.query.props.code_number")},{default:t(()=>[i(r(a.codeNumber),1)]),_:1},8,["label"]),o(d,{label:e(s)("reception.query.props.name")},{default:t(()=>[i(r(a.name),1)]),_:1},8,["label"]),o(d,{label:e(s)("reception.query.props.email")},{default:t(()=>[i(r(a.email),1)]),_:1},8,["label"]),o(d,{label:e(s)("reception.query.props.phone")},{default:t(()=>[i(r(a.phone),1)]),_:1},8,["label"]),m("template",null,[o(d,{label:e(s)("general.created_at")},{default:t(()=>[i(r(a.createdAt.formatted),1)]),_:1},8,["label"]),o(d,{label:e(s)("general.updated_at")},{default:t(()=>[i(r(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})]),_:1})]),default:t(()=>[m("div",G,[o(R,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:t(()=>[i(r(e(s)("reception.query.query")),1)]),default:t(()=>[m("dl",J,[o($,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[m("span",K,r(e(s)("reception.query.props.subject"))+": "+r(a.subject),1)]),_:1}),o($,{label:e(s)("reception.query.props.message"),class:"col-span-1 sm:col-span-2"},{default:t(()=>[i(r(a.message),1)]),_:1},8,["label"]),a.remarks?(f(),q($,{key:0,class:"col-span-1 sm:col-span-2",label:e(s)("reception.query.props.remarks")},{default:t(()=>[i(r(a.remarks),1)]),_:1},8,["label"])):w("",!0)])]),_:1}),e(U)("query:action")?(f(),q(R,{key:0},{title:t(()=>[i(r(e(s)("reception.query.props.action")),1)]),default:t(()=>[o(z,{query:a,onCompleted:l[0]||(l[0]=S=>p.value=!0)},null,8,["query"])]),_:1})):w("",!0)])]),_:1})):w("",!0)]),_:1},8,["uuid","module-uuid","refresh"])]),_:1})],64)}}});export{X as default};
