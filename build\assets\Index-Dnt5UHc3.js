import{u as U,l as x,n as E,r as i,q as m,o as s,w as t,d as l,e as n,b,h as O,j as z,y as $,m as G,f as o,B as J,a as T,F as I,v as V,t as a,ai as K,s as r}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={__name:"Filter",emits:["hide"],setup(P,{emit:g}){const k=U(),C=g,D={codeNumber:"",title:"",types:[],startDate:"",endDate:""},_=x({...D}),v=x({types:[],isLoaded:!k.query.types});return E(async()=>{v.types=k.query.types?k.query.types.split(","):[],v.isLoaded=!0}),(c,p)=>{const d=i("BaseInput"),u=i("BaseSelectSearch"),B=i("DatePicker"),F=i("FilterForm");return s(),m(F,{"init-form":D,form:_,multiple:["types"],onHide:p[4]||(p[4]=f=>C("hide"))},{default:t(()=>[l("div",Q,[l("div",W,[n(d,{type:"text",modelValue:_.title,"onUpdate:modelValue":p[0]||(p[0]=f=>_.title=f),name:"title",label:c.$trans("activity.trip.props.title")},null,8,["modelValue","label"])]),l("div",X,[v.isLoaded?(s(),m(u,{key:0,multiple:"",name:"types",label:c.$trans("global.select",{attribute:c.$trans("activity.trip.type.type")}),modelValue:_.types,"onUpdate:modelValue":p[1]||(p[1]=f=>_.types=f),"value-prop":"uuid","init-search":v.types,"search-action":"option/list","additional-search-query":{type:"trip_type"}},null,8,["label","modelValue","init-search"])):b("",!0)]),l("div",Y,[n(B,{start:_.startDate,"onUpdate:start":p[2]||(p[2]=f=>_.startDate=f),end:_.endDate,"onUpdate:end":p[3]||(p[3]=f=>_.endDate=f),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ee={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},te=["onClick"],ae=["src"],ne={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},se={class:"flex items-center justify-between"},ie={class:"font-medium"},oe={class:"text-sm"},le={class:"mt-2 flex items-center"},re={class:"mt-2 text-xs"},de={class:"text-xl font-semibold"},ue={name:"ActivityTripList"},me=Object.assign(ue,{setup(P){const g=O(),k=z("emitter");let C=["filter"];$("activity:config")&&C.push("config"),$("trip:manage")&&C.unshift("create");let D=[];$("trip:manage")&&(D=["print","pdf","excel"]);const _="activity/trip/",v=G(!1),c=x({}),p=d=>{Object.assign(c,d)};return(d,u)=>{const B=i("PageHeaderAction"),F=i("PageHeader"),f=i("ParentTransition"),w=i("TextMuted"),S=i("CardView"),L=i("Pagination"),M=i("CardList"),h=i("DataCell"),A=i("FloatingMenuItem"),R=i("FloatingMenu"),H=i("DataRow"),j=i("BaseButton"),q=i("DataTable"),N=i("ListItem");return s(),m(N,{"init-url":_,onSetItems:p},{header:t(()=>[n(F,{title:d.$trans("activity.trip.trip"),navs:[{label:d.$trans("activity.activity"),path:"Activity"}]},{default:t(()=>[n(B,{url:"activity/trips/",name:"ActivityTrip",title:d.$trans("activity.trip.trip"),actions:o(C),"dropdown-actions":o(D),"config-path":"ActivityConfig",onToggleFilter:u[0]||(u[0]=e=>v.value=!v.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[n(f,{appear:"",visibility:v.value},{default:t(()=>[n(Z,{onRefresh:u[1]||(u[1]=e=>o(k).emit("listItems")),onHide:u[2]||(u[2]=e=>v.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[o(J)(["student","guardian"],"any")?(s(),m(f,{key:0,appear:"",visibility:!0},{default:t(()=>[n(M,{header:c.headers,meta:c.meta},{content:t(()=>[l("div",de,a(d.$trans("dashboard.nothing_to_show")),1)]),default:t(()=>[l("div",ee,[(s(!0),T(I,null,V(c.data,e=>(s(),T("div",{key:e.uuid,class:"cursor-pointer",onClick:y=>o(g).push({name:"ActivityTripShow",params:{uuid:e.uuid}})},[l("div",null,[l("img",{class:"rounded-tl-lg rounded-tr-lg",src:e.coverImage},null,8,ae)]),n(S,{"no-padding":"","rounded-bottom":""},{default:t(()=>[l("div",ne,[l("div",se,[l("span",ie,a(e.title),1),l("span",oe,a(e.duration),1)]),l("div",le,[l("span",{class:"px-2 py-1 text-gray-50 text-xs rounded-lg mr-2",style:K(`background-color: ${e.type.color}`)},a(e.type.name),5),e.venue?(s(),m(w,{key:0,block:""},{default:t(()=>[r(" @ "+a(e.venue),1)]),_:2},1024)):b("",!0)]),l("p",re,a(e.durationInDetail),1)])]),_:2},1024)],8,te))),128))]),l("div",null,[n(L,{"card-view":"",meta:c.meta,onRefresh:u[3]||(u[3]=e=>o(k).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(s(),m(f,{key:1,appear:"",visibility:!0},{default:t(()=>[n(q,{header:c.headers,meta:c.meta,module:"activity.trip",onRefresh:u[5]||(u[5]=e=>o(k).emit("listItems"))},{actionButton:t(()=>[o($)("trip:manage")?(s(),m(j,{key:0,onClick:u[4]||(u[4]=e=>o(g).push({name:"ActivityTripCreate"}))},{default:t(()=>[r(a(d.$trans("global.add",{attribute:d.$trans("activity.trip.trip")})),1)]),_:1})):b("",!0)]),default:t(()=>[(s(!0),T(I,null,V(c.data,e=>(s(),m(H,{key:e.uuid,onDoubleClick:y=>o(g).push({name:"ActivityTripShow",params:{uuid:e.uuid}})},{default:t(()=>[n(h,{name:"type"},{default:t(()=>[r(a(e.type.name),1)]),_:2},1024),n(h,{name:"title"},{default:t(()=>[r(a(e.title)+" ",1),n(w,{block:""},{default:t(()=>[r(a(e.venue),1)]),_:2},1024)]),_:2},1024),n(h,{name:"startDate"},{default:t(()=>[r(a(e.startDate.formatted)+" ",1),e.startTime.value?(s(),m(w,{key:0,block:""},{default:t(()=>[r(a(e.startTime.formatted),1)]),_:2},1024)):b("",!0)]),_:2},1024),n(h,{name:"endDate"},{default:t(()=>{var y;return[r(a(((y=e.endDate)==null?void 0:y.formatted)||"-")+" ",1),e.endTime.value?(s(),m(w,{key:0,block:""},{default:t(()=>[r(a(e.endTime.formatted),1)]),_:2},1024)):b("",!0)]}),_:2},1024),n(h,{name:"fee"},{default:t(()=>[r(a(e.fee.formatted),1)]),_:2},1024),n(h,{name:"audience"},{default:t(()=>[(s(!0),T(I,null,V(e.audienceTypes,y=>(s(),T("div",null,a(y),1))),256))]),_:2},1024),n(h,{name:"createdAt"},{default:t(()=>[r(a(e.createdAt.formatted),1)]),_:2},1024),n(h,{name:"action"},{default:t(()=>[n(R,null,{default:t(()=>[n(A,{icon:"fas fa-arrow-circle-right",onClick:y=>o(g).push({name:"ActivityTripShow",params:{uuid:e.uuid}})},{default:t(()=>[r(a(d.$trans("general.show")),1)]),_:2},1032,["onClick"]),o($)("trip:manage")?(s(),m(A,{key:0,icon:"fas fa-edit",onClick:y=>o(g).push({name:"ActivityTripEdit",params:{uuid:e.uuid}})},{default:t(()=>[r(a(d.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),o($)("trip:manage")?(s(),m(A,{key:1,icon:"fas fa-copy",onClick:y=>o(g).push({name:"ActivityTripDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[r(a(d.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),o($)("trip:manage")?(s(),m(A,{key:2,icon:"fas fa-trash",onClick:y=>o(k).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[r(a(d.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{me as default};
