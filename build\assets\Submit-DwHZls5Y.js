import{i as W,u as f,h as J,H as K,m as Q,G as X,l as F,n as Y,r as n,a as o,o as t,e as c,w as d,q as m,b as s,d as b,F as _,v as V,f as u,s as g,t as v,J as Z}from"./app-BAwPsakn.js";const ee={class:"grid grid-cols-1 gap-6"},ae={class:"col-span-3 sm:col-span-1"},te={key:0},oe=["innerHTML"],ne={class:"flex space-x-4"},re=["src"],se=["src"],le={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ue={key:0},me={key:1},pe={class:"flex space-x-4"},ie=["src"],ce={key:2},de={key:3},_e={name:"FormSubmit"},ve=Object.assign(_e,{setup(ye){const H=W(),M=f(),S=J(),h="form/",l=K(h),k=Q(!1),U={fields:[],mediaUpdated:!1,mediaToken:X(),mediaHash:[]},y=F({...U}),p=F({form:{}}),$=async()=>{await H.dispatch("form/detail",{uuid:M.params.uuid}).then(i=>{p.form=i,!p.form.isSubmitted&&p.form.isExpired&&S.push({name:"Form"}),i.isSubmitted||(U.fields=i.fields.map(r=>(r.isDateType?r.value=r.value.value:r.type=="file_upload"||r.type=="camera_image"?r.value=r.value||[]:r.value=r.value||"",r)),Object.assign(y,Z(U))),k.value=!1}).catch(i=>{k.value=!1})},D=i=>{$()};return Y(async()=>{await $()}),(i,r)=>{const A=n("PageHeaderAction"),L=n("PageHeader"),B=n("BaseInput"),P=n("DatePicker"),w=n("BaseTextarea"),R=n("BaseSelect"),E=n("BaseMultiCheckbox"),N=n("BaseRadioGroup"),x=n("BaseLabel"),G=n("WebcamCapture"),I=n("MediaUpload"),j=n("FormAction"),T=n("BaseCard"),q=n("ListMedia"),C=n("BaseDataView"),z=n("ParentTransition");return t(),o(_,null,[c(L,{title:p.form.name,navs:[{label:i.$trans("form.form"),path:"Form"}]},{default:d(()=>[c(A,{name:"Form",title:i.$trans("form.form"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),c(z,{appear:"",visibility:!0},{default:d(()=>[p.form.isSubmitted?(t(),m(T,{key:1,"is-loading":k.value},{default:d(()=>[b("dl",le,[(t(!0),o(_,null,V(p.form.fields,(e,O)=>(t(),o(_,null,[e.showType?(t(),m(C,{key:0,class:"col-span-1 sm:col-span-2",label:e.label},{default:d(()=>[e.isDateType?(t(),o("div",ue,v(e.value.formatted),1)):e.type=="camera_image"?(t(),o("div",me,[b("div",pe,[(t(!0),o(_,null,V(e.value,a=>(t(),o("div",{key:a.id},[a.url?(t(),o("img",{key:0,src:a.url},null,8,ie)):s("",!0)]))),128))])])):e.type=="file_upload"?(t(),o("div",ce,[c(q,{media:p.form.submissionMedia,section:e.slug,url:`/app/forms/${p.form.uuid}/submissions/${p.form.submissionUuid}/`},null,8,["media","section","url"])])):(t(),o("div",de,v(e.value),1))]),_:2},1032,["label"])):s("",!0)],64))),256)),c(C,{label:i.$trans("form.props.submitted_at")},{default:d(()=>[g(v(p.form.submittedAt.formatted),1)]),_:1},8,["label"])])]),_:1},8,["is-loading"])):(t(),m(T,{key:0,"is-loading":k.value},{default:d(()=>[y.fields.length?(t(),m(j,{key:0,"no-card":"","no-data-fetch":"",action:"submit","keep-adding":!1,"init-url":h,"init-form":U,form:y,"after-submit":D},{default:d(()=>[b("div",ee,[(t(!0),o(_,null,V(y.fields,(e,O)=>(t(),o("div",ae,[e.type=="paragraph"?(t(),o("div",te,[b("div",{class:"text-sm",innerHTML:e.content},null,8,oe)])):s("",!0),e.type=="text_input"||e.type=="email_input"||e.type=="currency_input"?(t(),m(B,{key:1,type:"text",currency:e.type=="currency_input",modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,name:`fields.${e.name}`,label:e.label,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a},null,8,["currency","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):s("",!0),e.type=="number_input"?(t(),m(B,{key:2,type:"number",modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,name:`fields.${e.name}`,label:e.label,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):s("",!0),e.type=="date_picker"||e.type=="time_picker"||e.type=="date_time_picker"?(t(),m(P,{key:3,as:e.type=="date_picker"?"date":e.type=="time_picker"?"time":"datetime",modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,name:`fields.${e.name}`,label:e.label,"no-clear":!!e.isRequired,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a},null,8,["as","modelValue","onUpdate:modelValue","name","label","no-clear","error","onUpdate:error"])):s("",!0),e.type=="multi_line_text_input"?(t(),m(w,{key:4,modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,name:`fields.${e.name}`,label:e.label,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):s("",!0),e.type=="select_input"||e.type=="multi_select_input"?(t(),m(R,{key:5,multiple:e.type=="multi_select_input",modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,options:e.optionArray,name:`fields.${e.name}`,label:e.label,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a},null,8,["multiple","modelValue","onUpdate:modelValue","options","name","label","error","onUpdate:error"])):s("",!0),e.type=="checkbox_input"?(t(),m(E,{key:6,horizontal:"",options:e.optionArray,modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,name:`fields.${e.name}`,label:e.label,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a},null,8,["options","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):s("",!0),e.type=="radio_input"?(t(),m(N,{key:7,"top-margin":"",options:e.optionArray,modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,name:`fields.${e.name}`,label:e.label,error:u(l)[`fields.${e.name}`],"onUpdate:error":a=>u(l)[`fields.${e.name}`]=a,horizontal:""},null,8,["options","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):s("",!0),e.type=="camera_image"?(t(),o(_,{key:8},[c(x,{class:"mb-4"},{default:d(()=>[g(v(e.label),1)]),_:2},1024),c(G,{onCompleted:a=>e.value=a},null,8,["onCompleted"]),b("div",ne,[(t(!0),o(_,null,V(e.value,a=>(t(),o("div",{key:a.id},[a.image?(t(),o("img",{key:0,src:a.image},null,8,re)):s("",!0),a.url?(t(),o("img",{key:1,src:a.url},null,8,se)):s("",!0)]))),128))])],64)):s("",!0),e.type=="file_upload"?(t(),o(_,{key:9},[c(x,{class:"mb-4"},{default:d(()=>[g(v(e.label),1)]),_:2},1024),c(I,{multiple:"",label:e.name,module:"form_submission",section:e.slug,media:e.value,"media-token":y.mediaToken,onIsUpdated:r[0]||(r[0]=a=>y.mediaUpdated=!0),onSetHash:r[1]||(r[1]=a=>y.mediaHash.push(a))},null,8,["label","section","media","media-token"])],64)):s("",!0)]))),256))])]),_:1},8,["form"])):s("",!0)]),_:1},8,["is-loading"]))]),_:1})],64)}}});export{ve as default};
