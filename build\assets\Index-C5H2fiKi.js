import{h as j,i as y,j as L,c as _,m as N,l as P,r as t,q as f,o as u,w as e,e as a,f as c,a as g,F as R,v as V,s as l,b as x,t as m,d as A}from"./app-BAwPsakn.js";const F={key:0},H={name:"TodoList"},q=Object.assign(H,{setup(M){const d=j(),p=y(),D=L("emitter"),k=_(()=>p.getters["auth/user/currentTeamId"]);_(()=>p.getters["auth/user/isSuperAdmin"]);const C="team/";N(!1);const s=P({}),T=r=>{Object.assign(s,r)};return(r,o)=>{const h=t("PageHeader"),i=t("DataCell"),w=t("DropdownItem"),I=t("DropdownMenu"),b=t("DataRow"),v=t("DataTable"),B=t("ParentTransition"),S=t("ListItem");return u(),f(S,{"init-url":C,onSetItems:T},{header:e(()=>[a(h,{title:r.$trans("team.team")},null,8,["title"])]),default:e(()=>[a(B,{appear:"",visibility:!0},{default:e(()=>[a(v,{header:s.headers,meta:s.meta,module:"team",onRefresh:o[0]||(o[0]=n=>c(D).emit("listItems"))},{actionButton:e(()=>o[2]||(o[2]=[])),default:e(()=>[(u(!0),g(R,null,V(s.data,n=>(u(),f(b,{key:n.uuid,onDoubleClick:$=>c(d).push({name:"TeamShow",params:{uuid:n.uuid}})},{default:e(()=>[a(i,{name:"name"},{default:e(()=>[l(m(n.name)+" ",1),n.id==k.value?(u(),g("span",F,o[1]||(o[1]=[A("i",{class:"fas fa-check-circle fa-lg text-success cursor-pointer"},null,-1)]))):x("",!0)]),_:2},1024),a(i,{name:"createdAt"},{default:e(()=>[l(m(n.createdAt.formatted),1)]),_:2},1024),a(i,{name:"action"},{default:e(()=>[a(I,null,{default:e(()=>[a(w,{icon:"fas fa-cog",onClick:$=>c(d).push({name:"TeamConfig",params:{uuid:n.uuid}})},{default:e(()=>[l(m(r.$trans("team.config.config")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{q as default};
