import{u as j,j as B,H as C,l as _,n as $,J as k,r as a,a as P,o as s,q as r,b as c,e as l,f as i,w as m,d as p,F}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-1"},w={class:"col-span-1"},E={name:"ContactEditPhoto"},V=Object.assign(E,{props:{student:{type:Object,default(){return{}}}},setup(t){const o=j(),d=B("emitter"),h=t,n={photo:""};C("student/");const u=_({...n}),b=async()=>{d.emit("studentUpdated")},g=async()=>{d.emit("studentUpdated")};return $(async()=>{Object.assign(n,{photo:h.student.contact.photo}),Object.assign(u,k(n))}),(e,N)=>{const v=a("PageHeader"),f=a("ImageUpload"),y=a("BaseCard"),U=a("ParentTransition");return s(),P(F,null,[t.student.uuid?(s(),r(v,{key:0,title:e.$trans(i(o).meta.trans,{attribute:e.$trans(i(o).meta.label)}),navs:[{label:e.$trans("student.student"),path:"Student"},{label:t.student.contact.name,path:{name:"StudentShow",params:{uuid:t.student.uuid}}}]},null,8,["title","navs"])):c("",!0),l(U,{appear:"",visibility:!0},{default:m(()=>[t.student.uuid?(s(),r(y,{key:0},{default:m(()=>[p("div",O,[p("div",w,[l(f,{label:e.$trans("contact.props.photo"),src:u.photo,"upload-path":`students/${t.student.uuid}/photo`,"remove-path":`students/${t.student.uuid}/photo`,onUploaded:b,onRemoved:g},null,8,["label","src","upload-path","remove-path"])])])]),_:1})):c("",!0)]),_:1})],64)}}});export{V as default};
