import{H as _,l as c,r as p,q as U,o as B,w as v,d as n,e as d,f as a,J as F}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},T={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3"},O={name:"AcademicProgramTypeForm"},$=Object.assign(O,{props:{isModal:{type:Boolean,default:!1}},setup(u){const l={name:"",code:"",shortcode:"",description:""},m="academic/programType/",s=_(m),f=c({}),r=c({...l}),g=t=>{Object.assign(f,t)},V=t=>{Object.assign(l,{...t}),Object.assign(r,F(l))};return(t,e)=>{const i=p("BaseInput"),b=p("BaseTextarea"),y=p("FormAction");return B(),U(y,{"no-card":!!u.isModal,"has-setup-wizard":!0,"pre-requisites":!1,onSetPreRequisites:g,"init-url":m,"init-form":l,form:r,"set-form":V,redirect:"AcademicProgramType"},{default:v(()=>[n("div",q,[n("div",T,[d(i,{type:"text",modelValue:r.name,"onUpdate:modelValue":e[0]||(e[0]=o=>r.name=o),name:"name",label:t.$trans("academic.program_type.props.name"),error:a(s).name,"onUpdate:error":e[1]||(e[1]=o=>a(s).name=o)},null,8,["modelValue","label","error"])]),n("div",j,[d(i,{type:"text",modelValue:r.code,"onUpdate:modelValue":e[2]||(e[2]=o=>r.code=o),name:"code",label:t.$trans("academic.program_type.props.code"),error:a(s).code,"onUpdate:error":e[3]||(e[3]=o=>a(s).code=o)},null,8,["modelValue","label","error"])]),n("div",x,[d(i,{type:"text",modelValue:r.shortcode,"onUpdate:modelValue":e[4]||(e[4]=o=>r.shortcode=o),name:"shortcode",label:t.$trans("academic.program_type.props.shortcode"),error:a(s).shortcode,"onUpdate:error":e[5]||(e[5]=o=>a(s).shortcode=o)},null,8,["modelValue","label","error"])]),n("div",A,[d(b,{modelValue:r.description,"onUpdate:modelValue":e[6]||(e[6]=o=>r.description=o),name:"description",label:t.$trans("academic.program_type.props.description"),error:a(s).description,"onUpdate:error":e[7]||(e[7]=o=>a(s).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["no-card","form"])}}});export{$ as _};
