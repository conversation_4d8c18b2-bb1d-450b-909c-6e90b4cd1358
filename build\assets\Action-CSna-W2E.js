import{u as v,h as P,H as S,l as D,r as l,q as V,o as _,w as p,d,e as m,f as o,b as O,s as f,t as g,J as I,a as N,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},R={class:"col-span-2 sm:col-span-1"},T={class:"col-span-2 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},C={class:"col-span-3"},E={name:"AcademicDepartmentInchargeForm"},w=Object.assign(E,{setup(k){const c=v();P();const s={department:"",employee:"",startDate:"",endDate:"",remarks:""},b="academic/departmentIncharge/",n=S(b),u=D({departments:[]}),r=D({...s}),i=D({employee:"",isLoaded:!c.params.uuid}),$=a=>{Object.assign(u,a)},h=a=>{Object.assign(s,{...a,startDate:a.startDate.value,endDate:a.endDate.value,department:a.department.uuid,employee:a.employee.uuid}),Object.assign(r,I(s)),i.department=a.department.uuid,i.employee=a.employee.uuid,i.isLoaded=!0};return(a,t)=>{const A=l("BaseSelect"),U=l("BaseSelectSearch"),y=l("DatePicker"),B=l("BaseTextarea"),F=l("FormAction");return _(),V(F,{"pre-requisites":!0,onSetPreRequisites:$,"init-url":b,"init-form":s,form:r,setForm:h,redirect:"AcademicDepartmentIncharge"},{default:p(()=>[d("div",q,[d("div",H,[m(A,{name:"department",label:a.$trans("academic.department.department"),modelValue:r.department,"onUpdate:modelValue":t[0]||(t[0]=e=>r.department=e),error:o(n).department,"onUpdate:error":t[1]||(t[1]=e=>o(n).department=e),"label-prop":"name","value-prop":"uuid",options:u.departments},null,8,["label","modelValue","error","options"])]),d("div",R,[i.isLoaded?(_(),V(U,{key:0,name:"employee",label:a.$trans("global.select",{attribute:a.$trans("employee.employee")}),modelValue:r.employee,"onUpdate:modelValue":t[2]||(t[2]=e=>r.employee=e),error:o(n).employee,"onUpdate:error":t[3]||(t[3]=e=>o(n).employee=e),"value-prop":"uuid","init-search":i.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:p(e=>[f(g(e.value.name)+" ("+g(e.value.codeNumber)+") ",1)]),listOption:p(e=>[f(g(e.option.name)+" ("+g(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):O("",!0)]),d("div",T,[m(y,{modelValue:r.startDate,"onUpdate:modelValue":t[4]||(t[4]=e=>r.startDate=e),name:"startDate",label:a.$trans("employee.incharge.props.start_date"),"no-clear":"",error:o(n).startDate,"onUpdate:error":t[5]||(t[5]=e=>o(n).startDate=e)},null,8,["modelValue","label","error"])]),d("div",L,[m(y,{modelValue:r.endDate,"onUpdate:modelValue":t[6]||(t[6]=e=>r.endDate=e),name:"endDate",label:a.$trans("employee.incharge.props.end_date"),"no-clear":"",error:o(n).endDate,"onUpdate:error":t[7]||(t[7]=e=>o(n).endDate=e)},null,8,["modelValue","label","error"])]),d("div",C,[m(B,{modelValue:r.remarks,"onUpdate:modelValue":t[8]||(t[8]=e=>r.remarks=e),name:"remarks",label:a.$trans("employee.incharge.props.remarks"),error:o(n).remarks,"onUpdate:error":t[9]||(t[9]=e=>o(n).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),J={name:"AcademicDepartmentInchargeAction"},G=Object.assign(J,{setup(k){const c=v();return(s,b)=>{const n=l("PageHeaderAction"),u=l("PageHeader"),r=l("ParentTransition");return _(),N(j,null,[m(u,{title:s.$trans(o(c).meta.trans,{attribute:s.$trans(o(c).meta.label)}),navs:[{label:s.$trans("academic.academic"),path:"Academic"},{label:s.$trans("academic.department.department"),path:"AcademicDepartment"},{label:s.$trans("academic.department_incharge.department_incharge"),path:"AcademicDepartmentInchargeList"}]},{default:p(()=>[m(n,{name:"AcademicDepartmentIncharge",title:s.$trans("academic.department_incharge.department_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),m(r,{appear:"",visibility:!0},{default:p(()=>[m(w)]),_:1})],64)}}});export{G as default};
