import{u as N,l as k,n as S,r as o,q as g,o as m,w as e,d as h,e as n,h as V,j as U,y as D,m as q,f as r,a as w,F as B,v as E,s as p,t as u,b as I}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(P,{emit:c}){N();const v=c,f={startDate:"",endDate:""},l=k({...f}),b=k({isLoaded:!0});return S(async()=>{b.isLoaded=!0}),(d,i)=>{const $=o("DatePicker"),s=o("FilterForm");return m(),g(s,{"init-form":f,form:l,multiple:[],onHide:i[2]||(i[2]=t=>v("hide"))},{default:e(()=>[h("div",O,[h("div",z,[n($,{start:l.startDate,"onUpdate:start":i[0]||(i[0]=t=>l.startDate=t),end:l.endDate,"onUpdate:end":i[1]||(i[1]=t=>l.endDate=t),name:"dateBetween",as:"range",label:d.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={name:"CommunicationEmailList"},Q=Object.assign(J,{setup(P){const c=V(),v=U("emitter");let f=["filter"];D("email:send")&&f.unshift("new");let l=[];D("email:read")&&(l=["print","pdf","excel"]);const b="communication/email/",d=q(!1),i=k({}),$=s=>{Object.assign(i,s)};return(s,t)=>{const T=o("PageHeaderAction"),j=o("PageHeader"),F=o("ParentTransition"),_=o("DataCell"),y=o("FloatingMenuItem"),A=o("FloatingMenu"),H=o("DataRow"),L=o("BaseButton"),R=o("DataTable"),M=o("ListItem");return m(),g(M,{"init-url":b,"additional-query":{},onSetItems:$},{header:e(()=>[n(j,{title:s.$trans("communication.email.email"),navs:[{label:s.$trans("communication.communication"),path:"Communication"}]},{default:e(()=>[n(T,{url:"communication/emails/",name:"CommunicationEmail",title:s.$trans("communication.email.email"),actions:r(f),"dropdown-actions":r(l),"config-path":"CommunicationConfig",onToggleFilter:t[0]||(t[0]=a=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(F,{appear:"",visibility:d.value},{default:e(()=>[n(G,{onRefresh:t[1]||(t[1]=a=>r(v).emit("listItems")),onHide:t[2]||(t[2]=a=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(R,{header:i.headers,meta:i.meta,module:"communication.email",onRefresh:t[4]||(t[4]=a=>r(v).emit("listItems"))},{actionButton:e(()=>[r(D)("email:send")?(m(),g(L,{key:0,onClick:t[3]||(t[3]=a=>r(c).push({name:"CommunicationEmailNew"}))},{default:e(()=>[p(u(s.$trans("global.new",{attribute:s.$trans("communication.email.email")})),1)]),_:1})):I("",!0)]),default:e(()=>[(m(!0),w(B,null,E(i.data,a=>(m(),g(H,{key:a.uuid,onDoubleClick:C=>r(c).push({name:"CommunicationEmailShow",params:{uuid:a.uuid}})},{default:e(()=>[n(_,{name:"subject"},{default:e(()=>[p(u(a.subjectExcerpt),1)]),_:2},1024),n(_,{name:"audience"},{default:e(()=>[(m(!0),w(B,null,E(a.audienceTypes,C=>(m(),w("div",null,u(C),1))),256))]),_:2},1024),n(_,{name:"recipientCount"},{default:e(()=>[p(u(a.recipientCount),1)]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[p(u(a.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(A,null,{default:e(()=>[n(y,{icon:"fas fa-arrow-circle-right",onClick:C=>r(c).push({name:"CommunicationEmailShow",params:{uuid:a.uuid}})},{default:e(()=>[p(u(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),r(D)("email:send")?(m(),g(y,{key:0,icon:"fas fa-copy",onClick:C=>r(c).push({name:"CommunicationEmailDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[p(u(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):I("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
