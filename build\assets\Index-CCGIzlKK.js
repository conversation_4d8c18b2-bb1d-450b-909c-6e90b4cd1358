import{l as h,r as l,q as C,o as D,w as t,d as _,e,h as U,j as H,y as M,m as R,f as m,a as j,F as L,v as N,s as d,t as r}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(w,{emit:u}){const g=u,v={name:"",code:"",shortcode:"",alias:"",startDate:"",endDate:""},s=h({...v});return(p,n)=>{const c=l("BaseInput"),k=l("DatePicker"),i=l("FilterForm");return D(),C(i,{"init-form":v,form:s,onHide:n[6]||(n[6]=a=>g("hide"))},{default:t(()=>[_("div",E,[_("div",O,[e(c,{type:"text",modelValue:s.name,"onUpdate:modelValue":n[0]||(n[0]=a=>s.name=a),name:"name",label:p.$trans("academic.session.props.name")},null,8,["modelValue","label"])]),_("div",q,[e(c,{type:"text",modelValue:s.code,"onUpdate:modelValue":n[1]||(n[1]=a=>s.code=a),name:"code",label:p.$trans("academic.session.props.code")},null,8,["modelValue","label"])]),_("div",z,[e(c,{type:"text",modelValue:s.shortcode,"onUpdate:modelValue":n[2]||(n[2]=a=>s.shortcode=a),name:"shortcode",label:p.$trans("academic.session.props.shortcode")},null,8,["modelValue","label"])]),_("div",G,[e(c,{type:"text",modelValue:s.alias,"onUpdate:modelValue":n[3]||(n[3]=a=>s.alias=a),name:"alias",label:p.$trans("academic.session.props.alias")},null,8,["modelValue","label"])]),_("div",J,[e(k,{start:s.startDate,"onUpdate:start":n[4]||(n[4]=a=>s.startDate=a),end:s.endDate,"onUpdate:end":n[5]||(n[5]=a=>s.endDate=a),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"AcademicSessionList"},X=Object.assign(Q,{setup(w){const u=U(),g=H("emitter");let v=["create","filter"];M("academic:config")&&v.push("config");let s=["print","pdf","excel"];const p="academic/session/",n=R(!1),c=h({}),k=i=>{Object.assign(c,i)};return(i,a)=>{const F=l("PageHeaderAction"),B=l("PageHeader"),A=l("ParentTransition"),V=l("TextMuted"),f=l("DataCell"),$=l("FloatingMenuItem"),y=l("FloatingMenu"),I=l("DataRow"),S=l("BaseButton"),P=l("DataTable"),T=l("ListItem");return D(),C(T,{"init-url":p,onSetItems:k},{header:t(()=>[e(B,{title:i.$trans("academic.session.session"),navs:[{label:i.$trans("academic.academic"),path:"Academic"}]},{default:t(()=>[e(F,{url:"academic/sessions/",name:"AcademicSession",title:i.$trans("academic.session.session"),actions:m(v),"dropdown-actions":m(s),onToggleFilter:a[0]||(a[0]=o=>n.value=!n.value),"config-path":"AcademicConfig"},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[e(A,{appear:"",visibility:n.value},{default:t(()=>[e(K,{onRefresh:a[1]||(a[1]=o=>m(g).emit("listItems")),onHide:a[2]||(a[2]=o=>n.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[e(A,{appear:"",visibility:!0},{default:t(()=>[e(P,{header:c.headers,meta:c.meta,module:"academic.session",onRefresh:a[4]||(a[4]=o=>m(g).emit("listItems"))},{actionButton:t(()=>[e(S,{onClick:a[3]||(a[3]=o=>m(u).push({name:"AcademicSessionCreate"}))},{default:t(()=>[d(r(i.$trans("global.add",{attribute:i.$trans("academic.session.session")})),1)]),_:1})]),default:t(()=>[(D(!0),j(L,null,N(c.data,o=>(D(),C(I,{key:o.uuid,onDoubleClick:b=>m(u).push({name:"AcademicSessionShow",params:{uuid:o.uuid}})},{default:t(()=>[e(f,{name:"name"},{default:t(()=>[d(r(o.name)+" ",1),e(V,{block:""},{default:t(()=>[d(r(o.alias),1)]),_:2},1024)]),_:2},1024),e(f,{name:"code"},{default:t(()=>[d(r(o.code)+" ",1),e(V,{block:""},{default:t(()=>[d(r(o.shortcode),1)]),_:2},1024)]),_:2},1024),e(f,{name:"startDate"},{default:t(()=>[d(r(o.startDate.formatted),1)]),_:2},1024),e(f,{name:"endDate"},{default:t(()=>[d(r(o.endDate.formatted),1)]),_:2},1024),e(f,{name:"createdAt"},{default:t(()=>[d(r(o.createdAt.formatted),1)]),_:2},1024),e(f,{name:"action"},{default:t(()=>[e(y,null,{default:t(()=>[e($,{icon:"fas fa-arrow-circle-right",onClick:b=>m(u).push({name:"AcademicSessionShow",params:{uuid:o.uuid}})},{default:t(()=>[d(r(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),e($,{icon:"fas fa-edit",onClick:b=>m(u).push({name:"AcademicSessionEdit",params:{uuid:o.uuid}})},{default:t(()=>[d(r(i.$trans("general.edit")),1)]),_:2},1032,["onClick"]),e($,{icon:"fas fa-copy",onClick:b=>m(u).push({name:"AcademicSessionDuplicate",params:{uuid:o.uuid}})},{default:t(()=>[d(r(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),e($,{icon:"fas fa-trash",onClick:b=>m(g).emit("deleteItem",{uuid:o.uuid})},{default:t(()=>[d(r(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
