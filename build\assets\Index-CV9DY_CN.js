import{u as L,l as g,n as T,r as n,q as V,o as b,w as e,d as h,e as t,h as M,j as N,m as O,f as i,a as U,F as E,v as z,s as r,t as u,b as G}from"./app-BAwPsakn.js";import{u as J}from"./useColumnVisibility-BaCp0EnB.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(R,{emit:p}){L();const f=p,v=R,C={label:"",form:"",type:""},d=g({...C}),c=g({forms:v.preRequisites.forms,types:v.preRequisites.types}),y=g({isLoaded:!0});return T(async()=>{y.isLoaded=!0}),(m,l)=>{const F=n("BaseInput"),$=n("BaseSelect"),s=n("FilterForm");return b(),V(s,{"init-form":C,form:d,multiple:[],onHide:l[3]||(l[3]=o=>f("hide"))},{default:e(()=>[h("div",K,[h("div",Q,[t(F,{type:"text",modelValue:d.label,"onUpdate:modelValue":l[0]||(l[0]=o=>d.label=o),name:"label",label:m.$trans("custom_field.props.label")},null,8,["modelValue","label"])]),h("div",W,[t($,{modelValue:d.form,"onUpdate:modelValue":l[1]||(l[1]=o=>d.form=o),name:"form",label:m.$trans("custom_field.props.form"),options:c.forms},null,8,["modelValue","label","options"])]),h("div",X,[t($,{modelValue:d.type,"onUpdate:modelValue":l[2]||(l[2]=o=>d.type=o),name:"type",label:m.$trans("custom_field.props.type"),options:c.types},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},Z={name:"CustomFieldList"},te=Object.assign(Z,{setup(R){const p=M(),f=N("emitter");let v=["create","filter"],C=["print","pdf","excel"];const d="customField/",c=O(!1),y=g({forms:[],types:[]}),m=g({}),{applyVisibility:l}=J("CustomField"),F=s=>{s.headers=l(s.headers),Object.assign(m,s)},$=s=>{Object.assign(y,s)};return(s,o)=>{const w=n("PageHeaderAction"),D=n("PageHeader"),q=n("ParentTransition"),_=n("DataCell"),I=n("BaseBadge"),B=n("FloatingMenuItem"),P=n("FloatingMenu"),S=n("DataRow"),j=n("BaseButton"),A=n("DataTable"),H=n("ListItem");return b(),V(H,{"pre-requisites":!0,onSetPreRequisites:$,"init-url":d,onSetItems:F},{header:e(()=>[t(D,{title:s.$trans("custom_field.custom_field"),navs:[]},{default:e(()=>[t(w,{url:"custom-fields/",name:"CustomField",title:s.$trans("custom_field.custom_field"),actions:i(v),"dropdown-actions":i(C),headers:m.headers,onToggleFilter:o[0]||(o[0]=a=>c.value=!c.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title"])]),filter:e(()=>[t(q,{appear:"",visibility:c.value},{default:e(()=>[t(Y,{"pre-requisites":y,onRefresh:o[1]||(o[1]=a=>i(f).emit("listItems")),onHide:o[2]||(o[2]=a=>c.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(q,{appear:"",visibility:!0},{default:e(()=>[t(A,{header:m.headers,meta:m.meta,module:"custom_field",onRefresh:o[4]||(o[4]=a=>i(f).emit("listItems"))},{actionButton:e(()=>[t(j,{onClick:o[3]||(o[3]=a=>i(p).push({name:"CustomFieldCreate"}))},{default:e(()=>[r(u(s.$trans("global.add",{attribute:s.$trans("custom_field.custom_field")})),1)]),_:1})]),default:e(()=>[(b(!0),U(E,null,z(m.data,a=>(b(),V(S,{key:a.uuid,onDoubleClick:k=>i(p).push({name:"CustomFieldShow",params:{uuid:a.uuid}})},{default:e(()=>[t(_,{name:"form"},{default:e(()=>[r(u(a.form.label),1)]),_:2},1024),t(_,{name:"type"},{default:e(()=>[r(u(a.type.label),1)]),_:2},1024),t(_,{name:"label"},{default:e(()=>[r(u(a.label)+" ",1),a.isRequired?(b(),V(I,{key:0,design:"primary"},{default:e(()=>[r(u(s.$trans("custom_field.props.required")),1)]),_:1})):G("",!0)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[r(u(a.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(P,null,{default:e(()=>[t(B,{icon:"fas fa-arrow-circle-right",onClick:k=>i(p).push({name:"CustomFieldShow",params:{uuid:a.uuid}})},{default:e(()=>[r(u(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-edit",onClick:k=>i(p).push({name:"CustomFieldEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(u(s.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-copy",onClick:k=>i(p).push({name:"CustomFieldDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(u(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-trash",onClick:k=>i(f).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(u(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{te as default};
