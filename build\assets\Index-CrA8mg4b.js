import{r as e,q as a,o as r,w as s,e as c}from"./app-BAwPsakn.js";const i={name:"StudentConfig"},_=Object.assign(i,{setup(f){const n=[{name:"StudentConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"StudentConfigEnrollmentType",icon:"fas fa-chevron-right",label:"student.enrollment_type.enrollment_type"},{name:"StudentConfigDocumentType",icon:"fas fa-chevron-right",label:"student.document_type.document_type"},{name:"StudentConfigLeaveCategory",icon:"fas fa-chevron-right",label:"student.leave_category.leave_category"},{name:"StudentConfigTransferReason",icon:"fas fa-chevron-right",label:"student.transfer_reason.transfer_reason"},{name:"StudentConfigGroup",icon:"fas fa-chevron-right",label:"student.group.group"}];return(l,u)=>{const t=e("router-view"),o=e("ModuleConfig");return r(),a(o,{navigations:n},{default:s(()=>[c(t)]),_:1})}}});export{_ as default};
