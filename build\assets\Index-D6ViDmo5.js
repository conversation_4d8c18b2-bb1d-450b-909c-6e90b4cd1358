import{u as P,l as $,H as z,n as x,r as c,q as k,o as m,w as t,d as g,b as B,f as b,s as i,t as r,e as a,h as Z,i as ee,j as te,m as ae,K as oe,a as A,F as H,v as M,M as se,J as ne}from"./app-BAwPsakn.js";import{i as le,t as re,a as ie}from"./table-FwhM-Z75.js";const ce={class:"grid grid-cols-3 gap-6"},ue={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},me={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Array,default:[]}},emits:["hide"],setup(q,{emit:V}){const y=P(),S=V,v=q,d={name:"",batch:"",groups:[],showDetail:!0},p=$({...d}),_=z(v.initUrl),f=$({batch:"",isLoaded:!(y.query.batch||y.query.groups)});return x(async()=>{f.batch=y.query.batch,f.groups=y.query.groups?y.query.groups.split(","):[],f.isLoaded=!0}),(n,o)=>{const w=c("BaseSelectSearch"),T=c("BaseInput"),N=c("BaseSelect"),R=c("FilterForm");return m(),k(R,{"init-form":d,form:p,multiple:["groups"],onHide:o[4]||(o[4]=u=>S("hide"))},{default:t(()=>[g("div",ce,[g("div",ue,[f.isLoaded?(m(),k(w,{key:0,name:"batch",label:n.$trans("global.select",{attribute:n.$trans("academic.batch.batch")}),modelValue:p.batch,"onUpdate:modelValue":o[0]||(o[0]=u=>p.batch=u),error:b(_).batch,"onUpdate:error":o[1]||(o[1]=u=>b(_).batch=u),"value-prop":"uuid","init-search":f.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(u=>[i(r(u.value.course.name)+" "+r(u.value.name),1)]),listOption:t(u=>[i(r(u.option.course.nameWithTerm)+" "+r(u.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):B("",!0)]),g("div",de,[a(T,{type:"text",modelValue:p.name,"onUpdate:modelValue":o[2]||(o[2]=u=>p.name=u),name:"name",label:n.$trans("student.props.name")},null,8,["modelValue","label"])]),g("div",pe,[a(N,{multiple:"",modelValue:p.groups,"onUpdate:modelValue":o[3]||(o[3]=u=>p.groups=u),name:"groups","label-prop":"name","value-prop":"uuid",label:n.$trans("student.group.group"),options:q.preRequisites.groups},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},fe={class:"p-2"},be={key:0,class:"fas fa-check-circle text-success"},ge={class:"grid grid-cols-3 gap-6 px-4 py-2"},_e={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},ve={key:0,class:"col-span-3 sm:col-span-2"},Ce={class:"col-span-3 space-y-2"},he={class:"flex flex-wrap gap-y-2"},Be={class:"grid grid-cols-3 gap-6 px-4 py-2"},ke={class:"col-span-3 sm:col-span-1"},Ve={name:"StudentFeeAllocation"},Te=Object.assign(Ve,{setup(q){const V=P();Z();const y=ee();te("emitter");const S={batch:"",name:"",selectAll:!1,students:[],feeConcession:"",feeConcessionType:"",transportCircle:"",direction:"",optedFeeHeads:[]},v="student/feeAllocation/",d=ae(!1),p=$({feeConcessions:[],feeConcessionTypes:[],transportCircles:[],directions:[],feeHeads:[],groups:[]}),_=z(v),f=$({data:[]}),n=$({...S});$({});const o=$({...le}),w=async()=>{d.value=!0,await y.dispatch(v+"preRequisite").then(l=>{d.value=!1,Object.assign(p,l)}).catch(l=>{d.value=!1})},T=async()=>{d.value=!0,await y.dispatch(v+"fetch",{params:V.query}).then(l=>{d.value=!1,n.batch=V.query.batch,n.name=V.query.name,Object.assign(f,l),o.pageItems=l.data.map(s=>s.uuid)}).catch(l=>{d.value=!1})},N=()=>{Object.assign(n,ne(S))},R=()=>{o.global=!o.global,n.selectAll=o.global},u=async()=>{await se()&&(d.value=!0,await y.dispatch(v+"remove",{form:n}).then(l=>{d.value=!1,D()}).catch(l=>{d.value=!1}))},D=async()=>{o.items=[],o.pageItems=[],o.all=!1,n.selectAll=!1,N(),await T()};return oe(()=>[o.items,o.pageItems],([l,s],[L,U])=>{n.students=l,o.all=ie(o)}),x(async()=>{await w(),V.query.batch&&await T()}),(l,s)=>{const L=c("PageHeader"),U=c("ParentTransition"),J=c("BaseAlert"),I=c("BaseCard"),O=c("BaseArrayCheckbox"),K=c("BaseCheckbox"),C=c("DataCell"),F=c("TextMuted"),W=c("DataRow"),Q=c("DataTable"),X=c("BaseButton"),j=c("BaseSelect"),E=c("BaseLabel"),Y=c("BaseRadioGroup"),G=c("FormAction");return m(),A(H,null,[a(L,{title:l.$trans(b(V).meta.label),navs:[{label:l.$trans("student.student"),path:"Student"}]},null,8,["title","navs"]),a(U,{appear:"",visibility:!0},{default:t(()=>[a(me,{onAfterFilter:T,"init-url":v,"pre-requisites":p},null,8,["pre-requisites"])]),_:1}),f.data.length==0?(m(),k(I,{key:0,"no-padding":"","no-content-padding":"","is-loading":d.value},{title:t(()=>[i(r(l.$trans("student.fee_allocation.fee_allocation")),1)]),default:t(()=>[g("div",fe,[a(J,{size:"xs",design:"error"},{default:t(()=>[i(r(l.$trans("general.errors.record_not_found")),1)]),_:1})])]),_:1},8,["is-loading"])):B("",!0),f.data.length>0?(m(),k(U,{key:1,appear:"",visibility:!0},{default:t(()=>[a(Q,{onToggleSelectAll:s[2]||(s[2]=e=>o.items=b(re)(e,o)),onToggleGlobalSelect:R,selected:o,header:f.headers,meta:f.meta,module:"student.fee_allocation",onRefresh:T},{actionButton:t(()=>s[12]||(s[12]=[])),default:t(()=>[(m(!0),A(H,null,M(f.data,e=>(m(),k(W,{key:e.uuid},{default:t(()=>[a(C,{name:"selectAll"},{default:t(()=>[o.global?B("",!0):(m(),k(O,{key:0,items:o.items,"onUpdate:items":s[0]||(s[0]=h=>o.items=h),value:e.uuid},null,8,["items","value"])),o.global?(m(),k(K,{key:1,modelValue:o.global,"onUpdate:modelValue":s[1]||(s[1]=h=>o.global=h)},null,8,["modelValue"])):B("",!0)]),_:2},1024),a(C,{name:"codeNumber"},{default:t(()=>[i(r(e.codeNumber)+" ",1),e.feesCount?(m(),A("i",be)):B("",!0),a(F,{block:""},{default:t(()=>[i(r(e.joiningDate.formatted),1)]),_:2},1024)]),_:2},1024),a(C,{name:"name"},{default:t(()=>[i(r(e.name)+" ",1),a(F,{block:""},{default:t(()=>[i(r(e.gender.label),1)]),_:2},1024)]),_:2},1024),a(C,{name:"course"},{default:t(()=>[i(r(e.courseName)+" ",1),a(F,{block:""},{default:t(()=>[i(r(e.batchName),1)]),_:2},1024)]),_:2},1024),a(C,{name:"installmentCount"},{default:t(()=>[i(r(e.feesCount==0?"-":e.feesCount)+" ",1),a(F,{block:""},{default:t(()=>{var h;return[i(r(((h=e.feeConcessionType)==null?void 0:h.name)||""),1)]}),_:2},1024)]),_:2},1024),a(C,{name:"contactNumber"},{default:t(()=>[i(r(e.contactNumber)+" ",1),a(F,{block:""},{default:t(()=>[i(r(e.email),1)]),_:2},1024)]),_:2},1024),a(C,{name:"parent"},{default:t(()=>[i(r(e.fatherName)+" ",1),a(F,{block:""},{default:t(()=>[i(r(e.motherName),1)]),_:2},1024)]),_:2},1024),a(C,{name:"address"},{default:t(()=>[i(r(e.shortAddress),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["selected","header","meta"])]),_:1})):B("",!0),o.items.length>0?(m(),k(U,{key:2,appear:"",visibility:!0},{default:t(()=>[a(I,{"is-loading":d.value},{title:t(()=>[i(r(l.$trans("student.fee_allocation.fee_allocation")),1)]),action:t(()=>[a(X,{onClick:u,size:"xs",design:"error"},{default:t(()=>[i(r(l.$trans("global.remove",{attribute:l.$trans("student.fee.fee")})),1)]),_:1})]),default:t(()=>[a(G,{"no-card":"","keep-adding":!1,"init-url":v,action:"allocate","init-form":S,form:n,"after-submit":D},{default:t(()=>[g("div",ge,[g("div",_e,[a(j,{modelValue:n.feeConcession,"onUpdate:modelValue":s[3]||(s[3]=e=>n.feeConcession=e),name:"feeConcession",label:l.$trans("finance.fee_concession.fee_concession"),options:p.feeConcessions,"label-prop":"name","value-prop":"uuid",error:b(_).feeConcession,"onUpdate:error":s[4]||(s[4]=e=>b(_).feeConcession=e)},null,8,["modelValue","label","options","error"])]),g("div",ye,[a(j,{modelValue:n.transportCircle,"onUpdate:modelValue":s[5]||(s[5]=e=>n.transportCircle=e),name:"transportCircle",label:l.$trans("transport.circle.circle"),options:p.transportCircles,"label-prop":"name","value-prop":"uuid",error:b(_).transportCircle,"onUpdate:error":s[6]||(s[6]=e=>b(_).transportCircle=e)},null,8,["modelValue","label","options","error"])]),n.transportCircle?(m(),A("div",ve,[a(E,null,{default:t(()=>[i(r(l.$trans("transport.circle.direction")),1)]),_:1}),a(Y,{"top-margin":"",options:p.directions,name:"direction",modelValue:n.direction,"onUpdate:modelValue":s[7]||(s[7]=e=>n.direction=e),error:b(_).direction,"onUpdate:error":s[8]||(s[8]=e=>b(_).direction=e),horizontal:""},null,8,["options","modelValue","error"])])):B("",!0),g("div",Ce,[a(E,null,{default:t(()=>[i(r(l.$trans("student.fee.opted_fee")),1)]),_:1}),g("div",he,[(m(!0),A(H,null,M(p.feeHeads,e=>(m(),A("div",{class:"w-1/3",key:e.uuid},[a(O,{items:n.optedFeeHeads,"onUpdate:items":s[9]||(s[9]=h=>n.optedFeeHeads=h),value:e.uuid,label:e.name},null,8,["items","value","label"])]))),128))])])])]),_:1},8,["form"])]),_:1},8,["is-loading"]),a(I,{class:"mt-4","is-loading":d.value},{title:t(()=>[i(r(l.$trans("finance.fee_concession.type.type")),1)]),action:t(()=>s[13]||(s[13]=[])),default:t(()=>[a(G,{"no-card":"","keep-adding":!1,"init-url":v,action:"allocateFeeConcessionType","init-form":S,form:n,"after-submit":D},{default:t(()=>[g("div",Be,[g("div",ke,[a(j,{modelValue:n.feeConcessionType,"onUpdate:modelValue":s[10]||(s[10]=e=>n.feeConcessionType=e),name:"feeConcessionType",label:l.$trans("finance.fee_concession.type.type"),options:p.feeConcessionTypes,"label-prop":"name","value-prop":"uuid",error:b(_).feeConcessionType,"onUpdate:error":s[11]||(s[11]=e=>b(_).feeConcessionType=e)},null,8,["modelValue","label","options","error"])])])]),_:1},8,["form"])]),_:1},8,["is-loading"])]),_:1})):B("",!0)],64)}}});export{Te as default};
