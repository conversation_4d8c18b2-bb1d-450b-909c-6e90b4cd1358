import{i as j,u as D,h as O,g as c,l as R,r as d,a as E,o as r,e as n,w as e,f as o,q as b,b as m,d as F,s as u,t as s,y as M,F as q}from"./app-BAwPsakn.js";const U={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},z={name:"StudentAccountShow"},J=Object.assign(z,{props:{student:{type:Object,default(){return{}}}},setup(i){j();const f=D(),k=O(),_={},C="student/account/",g=c("finance.enableBankCode1"),B=c("finance.enableBankCode2"),S=c("finance.enableBankCode3"),$=c("finance.bankCode1Label"),h=c("finance.bankCode2Label"),y=c("finance.bankCode3Label"),a=R({..._}),w=t=>{Object.assign(a,t)};return(t,p)=>{const A=d("PageHeaderAction"),L=d("PageHeader"),l=d("BaseDataView"),v=d("ListMedia"),N=d("BaseButton"),P=d("ShowButton"),V=d("BaseCard"),H=d("ShowItem"),I=d("ParentTransition");return r(),E(q,null,[n(L,{title:t.$trans(o(f).meta.trans,{attribute:t.$trans(o(f).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:i.student.contact.name,path:{name:"StudentShow",params:{uuid:i.student.uuid}}}]},{default:e(()=>[n(A,{name:"StudentAccount",title:t.$trans("finance.account.account"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(I,{appear:"",visibility:!0},{default:e(()=>[n(H,{"init-url":C,uuid:o(f).params.uuid,"module-uuid":o(f).params.muuid,onSetItem:w,onRedirectTo:p[1]||(p[1]=T=>o(k).push({name:"StudentAccount",params:{uuid:i.student.uuid}}))},{default:e(()=>[a.uuid?(r(),b(V,{key:0},{title:e(()=>[u(s(a.name),1)]),footer:e(()=>[n(P,null,{default:e(()=>[o(M)("student:edit")?(r(),b(N,{key:0,design:"primary",onClick:p[0]||(p[0]=T=>o(k).push({name:"StudentAccountEdit",params:{uuid:i.student.uuid,muuid:a.uuid}}))},{default:e(()=>[u(s(t.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:e(()=>[F("dl",U,[n(l,{label:t.$trans("finance.account.props.alias")},{default:e(()=>[u(s(a.alias),1)]),_:1},8,["label"]),n(l,{label:t.$trans("finance.account.props.number")},{default:e(()=>[u(s(a.number),1)]),_:1},8,["label"]),n(l,{label:t.$trans("finance.account.props.bank_name")},{default:e(()=>[u(s(a.bankName),1)]),_:1},8,["label"]),n(l,{label:t.$trans("finance.account.props.branch_name")},{default:e(()=>[u(s(a.branchName),1)]),_:1},8,["label"]),o(g)?(r(),b(l,{key:0,label:o($)},{default:e(()=>[u(s(a.bankCode1),1)]),_:1},8,["label"])):m("",!0),o(B)?(r(),b(l,{key:1,label:o(h)},{default:e(()=>[u(s(a.bankCode2),1)]),_:1},8,["label"])):m("",!0),o(S)?(r(),b(l,{key:2,label:o(y)},{default:e(()=>[u(s(a.bankCode3),1)]),_:1},8,["label"])):m("",!0),n(l,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[n(v,{media:a.media,url:`/app/students/${i.student.uuid}/accounts/${a.uuid}/`},null,8,["media","url"])]),_:1}),n(l,{label:t.$trans("general.created_at")},{default:e(()=>[u(s(a.createdAt.formatted),1)]),_:1},8,["label"]),n(l,{label:t.$trans("general.updated_at")},{default:e(()=>[u(s(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{J as default};
