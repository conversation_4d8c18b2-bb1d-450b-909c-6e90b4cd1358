import{u as N,h as U,i as I,m as S,l as B,K as E,n as J,r as o,a,o as t,q as h,b as _,e as l,w as i,d as n,F as m,v as y,f,s as C,t as p,J as P}from"./app-BAwPsakn.js";const K={class:"space-x-4"},W={class:"scroller-thin-y scroller-hidden overflow-x-hidden h-96"},z={class:"scroller-thin-x scroller-hidden scrollbar-track-transparent scrollbar-thumb-body dark:scrollbar-thumb-dm-body border border-gray-200 dark:border-gray-700 sm:rounded-lg"},G={key:0,class:"table min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Q={class:"bg-gray-50 dark:bg-neutral-700"},X={class:"sticky-column bg-gray-50 dark:bg-neutral-700 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"},Y={class:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"},Z={class:"dark:bg-dark-body divide-y divide-gray-200 bg-white dark:divide-gray-700"},ee={class:"sticky-column bg-white dark:bg-dark-body py-2 pl-6 text-sm text-gray-500 dark:text-gray-400"},te={class:"py-2 pl-6 text-sm text-gray-500 dark:text-gray-400"},se={name:"TeamConfigPermissionAssign"},oe=Object.assign(se,{props:{team:{type:Object,default(){return{name:""}}}},setup($){const d=N(),k=U(),A=I(),g=S(!1),v="team/permission/",c=B({modules:[]}),r={selectedModule:"",assignedPermissions:[]},u=B({...r}),M=e=>{k.push({name:"TeamConfigPermissionAssignModule",params:{module:e}})},x=async()=>{g.value=!0,await A.dispatch(v+"preRequisite",{uuid:d.params.uuid,data:d.params.module||"general"}).then(e=>{g.value=!1,Object.assign(c,{modules:e.modules,selectedModule:e.selectedModule,roles:e.roles}),r.selectedModule=e.selectedModule,r.assignedPermissions=e.assignedPermissions,Object.assign(u,P(r))}).catch(e=>{g.value=!1})};return E(()=>d.params.module,e=>{e&&(r.selectedModule=e,Object.assign(u,P(r)),x())}),J(()=>{x()}),(e,w)=>{const V=o("DropdownItem"),D=o("DropdownButton"),T=o("BaseButton"),j=o("PageHeader"),F=o("CardHeader"),O=o("BaseCheckbox"),R=o("BaseLoader"),q=o("FormAction"),H=o("ParentTransition");return t(),a(m,null,[$.team.uuid?(t(),h(j,{key:0,title:e.$trans(f(d).meta.label),navs:[]},{default:i(()=>[n("div",K,[c.modules.length?(t(),h(D,{key:0,direction:"down",label:e.$trans("module."+c.selectedModule)},{default:i(()=>[n("div",W,[(t(!0),a(m,null,y(c.modules,s=>(t(),a("div",{key:s.value},[s.value!=f(d).params.module?(t(),h(V,{key:0,as:"span",onClick:b=>M(s.value)},{default:i(()=>[C(p(s.label),1)]),_:2},1032,["onClick"])):_("",!0)]))),128))])]),_:1},8,["label"])):_("",!0),l(T,{onClick:w[0]||(w[0]=s=>f(k).push({name:"TeamConfigUserPermission"}))},{default:i(()=>[C(p(e.$trans("team.config.permission.user_permission")),1)]),_:1})])]),_:1},8,["title"])):_("",!0),l(H,{appear:"",visibility:!0},{default:i(()=>[l(q,{"no-data-fetch":"","init-url":v,uuid:f(d).params.uuid,action:"roleWiseAssign","init-form":r,form:u,"stay-on":""},{default:i(()=>[l(F,{first:"",title:e.$trans("team.config.permission.permission_config"),description:e.$trans("team.config.permission.permission_info")},null,8,["title","description"]),l(R,{"is-loading":g.value},{default:i(()=>[n("div",z,[u.assignedPermissions.length?(t(),a("table",G,[n("thead",Q,[n("tr",null,[n("th",X,p(e.$trans("team.config.permission.permission")),1),(t(!0),a(m,null,y(c.roles,s=>(t(),a("th",Y,p(s.label),1))),256))])]),n("tbody",Z,[(t(!0),a(m,null,y(u.assignedPermissions,s=>(t(),a("tr",{key:s.name},[n("td",ee,p(s.name),1),(t(!0),a(m,null,y(s.roles,b=>(t(),a("td",te,[l(O,{modelValue:b.isAssigned,"onUpdate:modelValue":L=>b.isAssigned=L},null,8,["modelValue","onUpdate:modelValue"])]))),256))]))),128))])])):_("",!0)])]),_:1},8,["is-loading"])]),_:1},8,["uuid","form"])]),_:1})],64)}}});export{oe as default};
