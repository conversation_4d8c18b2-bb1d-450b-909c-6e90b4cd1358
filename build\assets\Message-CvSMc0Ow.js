import{u as T,h as L,i as E,k as v,l as $,m as S,n as U,a7 as j,K as A,r as B,z as F,a as n,o,d as a,t as l,q as V,b as d,w as D,F as K,v as N,x as w,A as c,s as q,a4 as z,a5 as O,f as b}from"./app-BAwPsakn.js";import{l as R}from"./lodash-CyHJH6Xs.js";const W={class:"flex flex-col h-full"},H={class:"bg-white dark:bg-dark-header p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},G={class:"flex items-center"},I=["src","alt"],J={class:"text-xl font-semibold dark:text-white"},P={class:"flex-1 p-4 bg-gray-100 dark:bg-dark-body flex flex-col-reverse scroller-thin-y scroller-hidden h-0"},Q={class:"space-y-4"},X={class:"flex justify-center items-center"},Y={class:"text-center text-xs border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2 cursor-pointer"},Z={key:1,class:"text-center"},ee={key:0,class:"mr-2"},te=["src","alt"],se={class:"text-xs text-gray-500"},ae={key:1,class:"ml-2"},re=["src","alt"],oe={class:"bg-white dark:bg-dark-header p-4 border-t border-gray-200 dark:border-gray-700"},ne={key:0,class:"text-xs text-gray-500 italic mb-2"},ie=["placeholder"],le={name:"ChatMessage"},ce=Object.assign(le,{props:{chat:{type:Object,required:!0}},emits:["onCloseChat"],setup(u,{emit:_}){T(),L();const f=E(),k=_,i=u,m=v("uuid"),e=$({messages:[],isTyping:!1,typingUser:null,newMessage:"",nextCursor:null,isLoading:!1}),h=S(null),g=async(r=null)=>{e.isLoading||(e.isLoading=!0,await f.dispatch("chat/getMessages",{uuid:i.chat.uuid,cursor:r}).then(s=>{e.messages=[...e.messages,...s.data],e.nextCursor=s.meta.nextCursor}).catch(s=>{console.error("Error loading messages:",s)}).finally(()=>{e.isLoading=!1}))},C=async()=>{e.newMessage.trim()&&(await f.dispatch("chat/sendMessage",{uuid:i.chat.uuid,message:{content:e.newMessage,type:"text"}}).then(r=>{e.messages.unshift(r)}).catch(r=>{console.error("Failed to send message:",r)}),e.newMessage="")},M=()=>{k("onCloseChat")},x=R.debounce(()=>{window.Echo&&(window.Echo.private(`chats.${i.chat.uuid}`).whisper("typing",{name:v("profile.name").value}),h.value&&clearTimeout(h.value),h.value=setTimeout(()=>{window.Echo.private(`chats.${i.chat.uuid}`).whisper("stopTyping",{})},2e3))},300);return U(async()=>{await g(),window.Echo&&window.Echo.private(`chats.${i.chat.uuid}`).listen(".message.sent",r=>{let s=j(r.message);if(s.isSent=s.user.uuid==m.value,s.user.uuid!=m.value){var p=new Audio("/notification.mp3");p.play(),e.messages.unshift(s)}}).listenForWhisper("typing",r=>{e.isTyping=!0,e.typingUser=r.name}).listenForWhisper("stopTyping",()=>{e.isTyping=!1,e.typingUser=null})}),A(()=>i.chat.uuid,()=>{e.messages=[],g()}),(r,s)=>{const p=B("BaseLoader"),y=F("tooltip");return o(),n("div",W,[a("div",H,[a("div",G,[a("img",{src:u.chat.avatar,alt:u.chat.name,class:"w-10 h-10 rounded-full mr-3"},null,8,I),a("h2",J,l(u.chat.name),1)]),a("div",{onClick:M},s[3]||(s[3]=[a("i",{class:"fas fa-lg fa-times cursor-pointer text-gray-500"},null,-1)]))]),a("div",P,[a("div",Q,[e.isLoading?(o(),V(p,{key:0},{default:D(()=>[a("div",X,[a("span",Y,l(r.$trans("chat.loading")),1)])]),_:1})):d("",!0),e.nextCursor&&!e.isLoading?(o(),n("div",Z,[a("span",{class:"text-xs border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2 cursor-pointer",onClick:s[0]||(s[0]=t=>g(e.nextCursor))},l(r.$trans("chat.load_more")),1)])):d("",!0),(o(!0),n(K,null,N([...e.messages].reverse(),t=>(o(),n("div",{key:t.uuid,class:w(["flex",t.isSent?"justify-end":"justify-start"])},[t.user&&!t.isSent?(o(),n("div",ee,[c(a("img",{src:t.user.avatar,alt:t.user.name,class:"w-8 h-8 rounded-full"},null,8,te),[[y,t.user.name]])])):d("",!0),a("div",null,[a("div",{class:w(["max-w-xs rounded-lg py-1 px-3 text-sm",t.isSent?"bg-primary text-white":"bg-gray-200 dark:bg-gray-700 dark:text-white"])},l(t.content),3),c((o(),n("span",se,[q(l(t.createdAt.diffForHuman),1)])),[[y,t.createdAt.formatted]])]),t.user&&t.isSent?(o(),n("div",ae,[c(a("img",{src:t.user.avatar,alt:t.user.name,class:"w-8 h-8 rounded-full"},null,8,re),[[y,t.user.name]])])):d("",!0)],2))),128))])]),a("div",oe,[e.isTyping?(o(),n("div",ne,l(r.$trans("chat.is_typing",{attribute:e.typingUser})),1)):d("",!0),c(a("input",{"onUpdate:modelValue":s[1]||(s[1]=t=>e.newMessage=t),onKeyup:[O(C,["enter"]),s[2]||(s[2]=(...t)=>b(x)&&b(x)(...t))],type:"text",placeholder:r.$trans("chat.type_message"),class:"w-full border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-body dark:text-white"},null,40,ie),[[z,e.newMessage]])])])}}});export{ce as default};
