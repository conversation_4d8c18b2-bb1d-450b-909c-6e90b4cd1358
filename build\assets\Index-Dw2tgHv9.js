import{u as P,l as F,n as R,r as i,q as c,o as m,w as e,d as A,e as o,h as T,j as M,y as _,m as j,f as t,a as N,F as S,v as O,s as u,t as d,b as x}from"./app-BAwPsakn.js";const U={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(w,{emit:l}){P();const v=l,g={name:""},p=F({...g}),C=F({isLoaded:!0});return R(async()=>{C.isLoaded=!0}),(f,r)=>{const b=i("BaseInput"),a=i("FilterForm");return m(),c(a,{"init-form":g,form:p,multiple:[],onHide:r[1]||(r[1]=s=>v("hide"))},{default:e(()=>[A("div",U,[A("div",q,[o(b,{type:"text",modelValue:p.name,"onUpdate:modelValue":r[0]||(r[0]=s=>p.name=s),name:"name",label:f.$trans("exam.assessment.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={name:"ExamAssessmentList"},K=Object.assign(G,{setup(w){const l=T(),v=M("emitter");let g=["filter"];_("assessment:create")&&g.unshift("create");let p=[];_("assessment:export")&&(p=["print","pdf","excel"]);const C="exam/assessment/",f=j(!1),r=F({}),b=a=>{Object.assign(r,a)};return(a,s)=>{const B=i("PageHeaderAction"),D=i("PageHeader"),y=i("ParentTransition"),h=i("DataCell"),$=i("FloatingMenuItem"),I=i("FloatingMenu"),E=i("DataRow"),V=i("BaseButton"),H=i("DataTable"),L=i("ListItem");return m(),c(L,{"init-url":C,onSetItems:b},{header:e(()=>[o(D,{title:a.$trans("exam.assessment.assessment"),navs:[{label:a.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[o(B,{url:"exam/assessments/",name:"ExamAssessment",title:a.$trans("exam.assessment.assessment"),actions:t(g),"dropdown-actions":t(p),onToggleFilter:s[0]||(s[0]=n=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(y,{appear:"",visibility:f.value},{default:e(()=>[o(z,{onRefresh:s[1]||(s[1]=n=>t(v).emit("listItems")),onHide:s[2]||(s[2]=n=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(y,{appear:"",visibility:!0},{default:e(()=>[o(H,{header:r.headers,meta:r.meta,module:"exam.assessment",onRefresh:s[4]||(s[4]=n=>t(v).emit("listItems"))},{actionButton:e(()=>[t(_)("exam-assessment:create")?(m(),c(V,{key:0,onClick:s[3]||(s[3]=n=>t(l).push({name:"ExamAssessmentCreate"}))},{default:e(()=>[u(d(a.$trans("global.add",{attribute:a.$trans("exam.assessment.assessment")})),1)]),_:1})):x("",!0)]),default:e(()=>[(m(!0),N(S,null,O(r.data,n=>(m(),c(E,{key:n.uuid,onDoubleClick:k=>t(l).push({name:"ExamAssessmentShow",params:{uuid:n.uuid}})},{default:e(()=>[o(h,{name:"name"},{default:e(()=>[u(d(n.name),1)]),_:2},1024),o(h,{name:"createdAt"},{default:e(()=>[u(d(n.createdAt.formatted),1)]),_:2},1024),o(h,{name:"action"},{default:e(()=>[o(I,null,{default:e(()=>[o($,{icon:"fas fa-arrow-circle-right",onClick:k=>t(l).push({name:"ExamAssessmentShow",params:{uuid:n.uuid}})},{default:e(()=>[u(d(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(_)("exam-assessment:edit")?(m(),c($,{key:0,icon:"fas fa-edit",onClick:k=>t(l).push({name:"ExamAssessmentEdit",params:{uuid:n.uuid}})},{default:e(()=>[u(d(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):x("",!0),t(_)("exam-assessment:create")?(m(),c($,{key:1,icon:"fas fa-copy",onClick:k=>t(l).push({name:"ExamAssessmentDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[u(d(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):x("",!0),t(_)("exam-assessment:delete")?(m(),c($,{key:2,icon:"fas fa-trash",onClick:k=>t(v).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[u(d(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):x("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
