import{i as z,u as G,h as J,l as q,H as K,m as Q,r as o,a as W,o as b,e as a,w as t,f as u,q as $,b as B,d as c,s as l,t as n,y as S,F as X}from"./app-BAwPsakn.js";const Y={class:"space-y-4"},Z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},x={class:"grid grid-cols-2 gap-6"},ee={class:"col-span-2 sm:col-span-1"},te={class:"col-span-2 sm:col-span-1"},ae={name:"EmployeeLeaveRequestShow"},le=Object.assign(ae,{setup(se){z();const y=G(),V=J(),w={},L={status:"",comment:""},_="employee/leave/request/",R=q({statuses:[]}),f=K(_),p=q({...L}),v=Q(!1),s=q({...w}),E=e=>{Object.assign(R,e)},h=e=>{Object.assign(s,e)},k=()=>{v.value=!0};return(e,r)=>{const C=o("PageHeaderAction"),P=o("PageHeader"),d=o("ListItemView"),I=o("ListContainerVertical"),g=o("BaseCard"),A=o("BaseBadge"),m=o("BaseDataView"),F=o("ListMedia"),T=o("BaseButton"),D=o("ShowButton"),H=o("BaseSelect"),N=o("BaseTextarea"),U=o("FormAction"),j=o("DetailLayoutVertical"),O=o("ShowItem"),M=o("ParentTransition");return b(),W(X,null,[a(P,{title:e.$trans(u(y).meta.trans,{attribute:e.$trans(u(y).meta.label)}),navs:[{label:e.$trans("employee.leave.leave"),path:"EmployeeLeave"},{label:e.$trans("employee.leave.request.request"),path:"EmployeeLeaveRequestList"}]},{default:t(()=>[a(C,{name:"EmployeeLeaveRequest",title:e.$trans("employee.leave.request.request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(M,{appear:"",visibility:!0},{default:t(()=>[a(O,{"init-url":_,uuid:u(y).params.uuid,onSetItem:h,onRedirectTo:r[5]||(r[5]=i=>u(V).push({name:"EmployeeLeaveRequest"})),refresh:v.value,onRefreshed:r[6]||(r[6]=i=>v.value=!1)},{default:t(()=>[s.uuid?(b(),$(j,{key:0},{detail:t(()=>[a(g,{"no-padding":"","no-content-padding":""},{title:t(()=>[l(n(e.$trans("global.detail",{attribute:e.$trans("employee.employee")})),1)]),default:t(()=>[a(I,null,{default:t(()=>[a(d,{label:e.$trans("employee.props.name")},{default:t(()=>[l(n(s.employee.name),1)]),_:1},8,["label"]),a(d,{label:e.$trans("employee.props.code_number")},{default:t(()=>[l(n(s.employee.codeNumber),1)]),_:1},8,["label"]),a(d,{label:e.$trans("employee.department.department")},{default:t(()=>[l(n(s.employee.department),1)]),_:1},8,["label"]),a(d,{label:e.$trans("employee.designation.designation")},{default:t(()=>[l(n(s.employee.designation),1)]),_:1},8,["label"]),a(d,{label:e.$trans("employee.employment_status.employment_status")},{default:t(()=>[l(n(s.employee.employmentStatus),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:t(()=>[c("div",Y,[a(g,null,{title:t(()=>[l(n(e.$trans("employee.leave.request.request"))+" ",1),a(A,{label:s.status.label,design:s.status.color},null,8,["label","design"])]),footer:t(()=>[a(D,null,{default:t(()=>[u(S)("leave-request:edit")&&s.status.value=="requested"?(b(),$(T,{key:0,design:"primary",onClick:r[0]||(r[0]=i=>u(V).push({name:"EmployeeLeaveRequestEdit",params:{uuid:s.uuid}}))},{default:t(()=>[l(n(e.$trans("general.edit")),1)]),_:1})):B("",!0)]),_:1})]),default:t(()=>[c("dl",Z,[a(m,{label:e.$trans("employee.leave.request.props.period")},{default:t(()=>[l(n(s.period),1)]),_:1},8,["label"]),a(m,{label:e.$trans("employee.leave.request.props.duration")},{default:t(()=>[l(n(s.duration),1)]),_:1},8,["label"]),a(m,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.leave.request.props.reason")},{default:t(()=>[l(n(s.reason),1)]),_:1},8,["label"]),a(m,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.leave.request.props.comment")},{default:t(()=>[l(n(s.comment),1)]),_:1},8,["label"]),a(m,{label:e.$trans("general.created_at")},{default:t(()=>[l(n(s.createdAt.formatted),1)]),_:1},8,["label"]),a(m,{label:e.$trans("general.updated_at")},{default:t(()=>[l(n(s.updatedAt.formatted),1)]),_:1},8,["label"]),a(m,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[a(F,{media:s.media,url:`/app/employee/leave/requests/${s.uuid}/`},null,8,["media","url"])]),_:1})])]),_:1}),u(S)("leave-request:action")?(b(),$(g,{key:0},{title:t(()=>[l(n(e.$trans("employee.leave.request.action")),1)]),default:t(()=>[a(U,{"no-card":"","keep-adding":!1,"init-url":_,"pre-requisites":!0,onSetPreRequisites:E,uuid:s.uuid,"no-data-fetch":!0,action:"status","init-form":L,form:p,"after-submit":k},{default:t(()=>[c("div",x,[c("div",ee,[a(H,{name:"status",label:e.$trans("global.select",{attribute:e.$trans("employee.leave.request.props.status")}),modelValue:p.status,"onUpdate:modelValue":r[1]||(r[1]=i=>p.status=i),options:R.statuses,error:u(f).status,"onUpdate:error":r[2]||(r[2]=i=>u(f).status=i)},null,8,["label","modelValue","options","error"])]),c("div",te,[a(N,{modelValue:p.comment,"onUpdate:modelValue":r[3]||(r[3]=i=>p.comment=i),name:"comment",label:e.$trans("employee.leave.request.props.comment"),error:u(f).comment,"onUpdate:error":r[4]||(r[4]=i=>u(f).comment=i)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})):B("",!0)])]),_:1})):B("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{le as default};
