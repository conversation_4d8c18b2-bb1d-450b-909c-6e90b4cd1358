import{u as D,h as H,j as L,l as v,H as T,c as I,n as h,J,r as d,a as c,o as i,q as y,b as f,e as l,f as a,w as V,F as C,d as m,s as M,t as z,A as G,af as K,y as Q}from"./app-BAwPsakn.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},_={key:0,class:"col-span-3 sm:col-span-1"},ee={key:1,class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},te={key:2,class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},re={name:"StudentEditLogin"},le=Object.assign(re,{props:{student:{type:Object,default(){return{}}}},setup(p){const S=D(),k=H(),j=L("emitter"),B=p,g={username:"",email:"",forceChangePassword:!1,password:"",passwordConfirmation:"",roles:[]},U="student/",q="user/",P=v({}),r=T(U),b=I(()=>{var t,e;return(e=(t=B.student)==null?void 0:t.contact)==null?void 0:e.user}),n=v({isValidated:!1,existingUser:null}),o=v({...g}),F=t=>{Object.assign(P,t)},R=t=>{n.isValidated=!0,n.existingUser=t||null,o.username=t==null?void 0:t.username},O=()=>{j.emit("studentUpdated"),k.push({name:"StudentShowLogin",params:{uuid:B.student.uuid}})};return h(async()=>{var t,e,w;n.isValidated=!!b.value,n.existingUser=b.value,Object.assign(g,{username:(t=b.value)==null?void 0:t.username,email:(e=b.value)==null?void 0:e.email,roles:((w=b.value)==null?void 0:w.roles.map(u=>u.uuid))||[]}),Object.assign(o,J(g))}),(t,e)=>{const w=d("PageHeader"),u=d("BaseInput"),E=d("BaseButton"),$=d("FormAction"),N=d("BaseSelect"),x=d("BaseSwitch"),A=d("ParentTransition");return i(),c(C,null,[p.student.uuid?(i(),y(w,{key:0,title:t.$trans(a(S).meta.trans,{attribute:t.$trans(a(S).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:p.student.contact.name,path:{name:"StudentShow",params:{uuid:p.student.uuid}}}]},null,8,["title","navs"])):f("",!0),l(A,{appear:"",visibility:!0},{default:V(()=>[p.student.uuid?(i(),c(C,{key:0},[n.isValidated?(i(),y($,{key:1,"no-data-fetch":"","pre-requisites":!0,"pre-requisite-url":q,onSetPreRequisites:F,"init-url":U,action:n.existingUser?"updateUser":"createUser","init-form":g,form:o,"after-submit":O},{default:V(()=>[m("div",Y,[m("div",Z,[l(u,{readonly:"",disabled:"",type:"text",modelValue:o.email,"onUpdate:modelValue":e[2]||(e[2]=s=>o.email=s),name:"email",label:t.$trans("contact.login.props.email"),error:a(r).email,"onUpdate:error":e[3]||(e[3]=s=>a(r).email=s)},null,8,["modelValue","label","error"])]),n.existingUser?(i(),c("div",_,[l(u,{readonly:"",disabled:"",type:"text",modelValue:o.username,"onUpdate:modelValue":e[4]||(e[4]=s=>o.username=s),name:"username",label:t.$trans("contact.login.props.username"),error:a(r).username,"onUpdate:error":e[5]||(e[5]=s=>a(r).username=s)},null,8,["modelValue","label","error"])])):f("",!0),n.existingUser?f("",!0):(i(),c("div",ee,[l(u,{type:"text",modelValue:o.username,"onUpdate:modelValue":e[6]||(e[6]=s=>o.username=s),name:"username",label:t.$trans("contact.login.props.username"),error:a(r).username,"onUpdate:error":e[7]||(e[7]=s=>a(r).username=s)},null,8,["modelValue","label","error"])])),G(m("div",se,[l(N,{modelValue:o.roles,"onUpdate:modelValue":e[8]||(e[8]=s=>o.roles=s),name:"roles",label:t.$trans("contact.login.props.role"),options:P.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:a(r).roles,"onUpdate:error":e[9]||(e[9]=s=>a(r).roles=s)},null,8,["modelValue","label","options","error"])],512),[[K,!1]]),n.existingUser&&a(Q)("user:edit")?(i(),c("div",te,[l(x,{vertical:"",modelValue:o.forceChangePassword,"onUpdate:modelValue":e[10]||(e[10]=s=>o.forceChangePassword=s),name:"forceChangePassword",label:t.$trans("global.change",{attribute:t.$trans("contact.login.props.password")}),error:a(r).forceChangePassword,"onUpdate:error":e[11]||(e[11]=s=>a(r).forceChangePassword=s)},null,8,["modelValue","label","error"])])):f("",!0),!n.existingUser||o.forceChangePassword?(i(),c(C,{key:3},[m("div",oe,[l(u,{type:"password",modelValue:o.password,"onUpdate:modelValue":e[12]||(e[12]=s=>o.password=s),name:"password",label:t.$trans("contact.login.props.password"),error:a(r).password,"onUpdate:error":e[13]||(e[13]=s=>a(r).password=s)},null,8,["modelValue","label","error"])]),m("div",ae,[l(u,{type:"password",modelValue:o.passwordConfirmation,"onUpdate:modelValue":e[14]||(e[14]=s=>o.passwordConfirmation=s),name:"passwordConfirmation",label:t.$trans("contact.login.props.password_confirmation"),error:a(r).passwordConfirmation,"onUpdate:error":e[15]||(e[15]=s=>a(r).passwordConfirmation=s)},null,8,["modelValue","label","error"])])],64)):f("",!0)])]),_:1},8,["action","form"])):(i(),y($,{key:0,"no-action-button":"","no-data-fetch":"","init-url":U,action:"confirmUser","init-form":g,form:o,"after-submit":R,"stay-on":"",redirect:{name:"StudentShowLogin",params:{uuid:p.student.uuid}}},{default:V(()=>[m("div",W,[m("div",X,[l(u,{type:"text",modelValue:o.email,"onUpdate:modelValue":e[0]||(e[0]=s=>o.email=s),name:"email",label:t.$trans("contact.login.props.email"),error:a(r).email,"onUpdate:error":e[1]||(e[1]=s=>a(r).email=s)},null,8,["modelValue","label","error"])])]),l(E,{class:"mt-4",type:"submit"},{default:V(()=>[M(z(t.$trans("general.validate")),1)]),_:1})]),_:1},8,["form","redirect"]))],64)):f("",!0)]),_:1})],64)}}});export{le as default};
