import{u as V,G as g,H,l as f,r as l,q as T,o as U,w as _,d as u,e as d,f as a,J as j,a as O,F as q}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},w={class:"col-span-2 sm:col-span-1"},E={class:"col-span-2 sm:col-span-1"},I={class:"col-span-3 sm:col-span-2"},L={class:"grid grid-cols-1"},C={class:"col"},N={name:"StudentDocumentForm"},G=Object.assign(N,{setup(m){const i=V(),r={type:"",title:"",description:"",startDate:"",endDate:"",media:[],mediaUpdated:!1,mediaToken:g(),mediaHash:[]},D="student/document/",s=H(D),p=f({types:[]}),n=f({...r}),v=f({type:"",isLoaded:!i.params.muuid}),$=o=>{Object.assign(p,o)},S=()=>{n.mediaToken=g(),n.mediaHash=[]},k=o=>{var e,c,b;Object.assign(r,{...o,type:(e=o.type)==null?void 0:e.uuid,startDate:((c=o.startDate)==null?void 0:c.value)||"",endDate:((b=o.endDate)==null?void 0:b.value)||""}),Object.assign(n,j(r)),v.type=o.type.name,v.isLoaded=!0};return(o,e)=>{const c=l("BaseSelect"),b=l("BaseInput"),y=l("DatePicker"),B=l("BaseTextarea"),F=l("MediaUpload"),P=l("FormAction");return U(),T(P,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:$,"init-url":D,uuid:a(i).params.uuid,"module-uuid":a(i).params.muuid,"init-form":r,form:n,"set-form":k,redirect:{name:"StudentDocument",params:{uuid:a(i).params.uuid}},onResetMediaFiles:S},{default:_(()=>[u("div",A,[u("div",R,[d(c,{modelValue:n.type,"onUpdate:modelValue":e[0]||(e[0]=t=>n.type=t),name:"type",label:o.$trans("student.document_type.document_type"),"label-prop":"name","value-prop":"uuid",options:p.types,error:a(s).type,"onUpdate:error":e[1]||(e[1]=t=>a(s).type=t)},null,8,["modelValue","label","options","error"])]),u("div",M,[d(b,{type:"text",modelValue:n.title,"onUpdate:modelValue":e[2]||(e[2]=t=>n.title=t),name:"title",label:o.$trans("student.document.props.title"),error:a(s).title,"onUpdate:error":e[3]||(e[3]=t=>a(s).title=t),autofocus:""},null,8,["modelValue","label","error"])]),u("div",w,[d(y,{modelValue:n.startDate,"onUpdate:modelValue":e[4]||(e[4]=t=>n.startDate=t),name:"startDate",label:o.$trans("student.document.props.start_date"),error:a(s).startDate,"onUpdate:error":e[5]||(e[5]=t=>a(s).startDate=t)},null,8,["modelValue","label","error"])]),u("div",E,[d(y,{modelValue:n.endDate,"onUpdate:modelValue":e[6]||(e[6]=t=>n.endDate=t),name:"endDate",label:o.$trans("student.document.props.end_date"),error:a(s).endDate,"onUpdate:error":e[7]||(e[7]=t=>a(s).endDate=t)},null,8,["modelValue","label","error"])]),u("div",I,[d(B,{modelValue:n.description,"onUpdate:modelValue":e[8]||(e[8]=t=>n.description=t),name:"description",label:o.$trans("student.document.props.description"),error:a(s).description,"onUpdate:error":e[9]||(e[9]=t=>a(s).description=t)},null,8,["modelValue","label","error"])])]),u("div",L,[u("div",C,[d(F,{multiple:"",label:o.$trans("general.file"),module:"document",media:n.media,"media-token":n.mediaToken,onIsUpdated:e[10]||(e[10]=t=>n.mediaUpdated=!0),onSetHash:e[11]||(e[11]=t=>n.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),J={name:"StudentDocumentAction"},K=Object.assign(J,{props:{student:{type:Object,default(){return{}}}},setup(m){const i=V();return(r,D)=>{const s=l("PageHeaderAction"),p=l("PageHeader"),n=l("ParentTransition");return U(),O(q,null,[d(p,{title:r.$trans(a(i).meta.trans,{attribute:r.$trans(a(i).meta.label)}),navs:[{label:r.$trans("student.student"),path:"StudentList"},{label:m.student.contact.name,path:{name:"StudentShow",params:{uuid:m.student.uuid}}},{label:r.$trans("student.document.document"),path:{name:"StudentDocument",params:{uuid:m.student.uuid}}}]},{default:_(()=>[d(s,{name:"StudentDocument",title:r.$trans("student.document.document"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(n,{appear:"",visibility:!0},{default:_(()=>[d(G)]),_:1})],64)}}});export{K as default};
