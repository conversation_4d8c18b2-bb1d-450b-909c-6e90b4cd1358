import{j as pe,i as me,u as be,h as ve,g,H as fe,l as G,m as $e,r as s,a as J,o as b,e as a,w as n,f as u,q,b as h,d as N,s as r,t as o,F as we}from"./app-BAwPsakn.js";const _e={key:0,class:"mb-4"},ge={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},he={class:"grid grid-cols-3 gap-6"},Ae={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3"},Ne={name:"StudentEditRequestShow"},Ie=Object.assign(Ne,{setup(ye){pe("emitter"),me();const y=be(),K=ve(),Q={},S={status:"",comment:""},L="student/editRequest/",W=g("contact.uniqueIdNumber1Label"),X=g("contact.uniqueIdNumber2Label"),Y=g("contact.uniqueIdNumber3Label"),Z=g("contact.uniqueIdNumber4Label"),x=g("contact.uniqueIdNumber5Label"),A=fe(L),v=G({...S}),I=$e(!1),e=G({...Q}),ee=t=>{Object.assign(e,t)},ae=()=>{I.value=!0};return(t,d)=>{const te=s("PageHeaderAction"),le=s("PageHeader"),ne=s("BaseAlert"),se=s("BaseBadge"),c=s("BaseDataView"),l=s("HorizontalListItem"),B=s("HorizontalList"),R=s("BaseFieldset"),re=s("ListMedia"),V=s("BaseCard"),oe=s("BaseSelect"),ue=s("BaseTextarea"),de=s("FormAction"),ie=s("ShowItem"),ce=s("ParentTransition");return b(),J(we,null,[a(le,{title:t.$trans(u(y).meta.trans,{attribute:t.$trans(u(y).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:t.$trans("student.edit_request.edit_request"),path:"StudentEditRequestList"}]},{default:n(()=>[a(te,{name:"StudentEditRequest",title:t.$trans("student.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(ce,{appear:"",visibility:!0},{default:n(()=>[a(ie,{"init-url":L,uuid:u(y).params.uuid,onSetItem:ee,onRedirectTo:d[4]||(d[4]=i=>u(K).push({name:"StudentEditRequest"})),refresh:I.value,onRefreshed:d[5]||(d[5]=i=>I.value=!1)},{default:n(()=>[e.uuid?(b(),q(V,{key:0},{title:n(()=>[r(o(e.student.name),1)]),default:n(()=>[e.isRejected&&e.comment?(b(),J("div",_e,[a(ne,{design:"error",size:"xs"},{default:n(()=>[r(o(e.comment),1)]),_:1})])):h("",!0),N("dl",ge,[a(c,{label:t.$trans("student.admission.props.code_number")},{default:n(()=>[r(o(e.student.codeNumber)+" ",1),a(se,{design:e.status.color},{default:n(()=>[r(o(e.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),a(c,{label:t.$trans("contact.props.birth_date")},{default:n(()=>[r(o(e.student.birthDate.formatted),1)]),_:1},8,["label"]),a(c,{label:t.$trans("contact.props.father_name")},{default:n(()=>[r(o(e.student.fatherName),1)]),_:1},8,["label"]),a(c,{label:t.$trans("contact.props.mother_name")},{default:n(()=>[r(o(e.student.motherName),1)]),_:1},8,["label"]),a(c,{label:t.$trans("academic.course.course")},{default:n(()=>[r(o(e.student.courseName+" "+e.student.batchName),1)]),_:1},8,["label"]),a(c,{label:t.$trans("student.edit_request.request_by")},{default:n(()=>[r(o(e.user.profile.name),1)]),_:1},8,["label"]),a(c,{class:"col-span-1 sm:col-span-2"},{default:n(()=>{var i,z,E,k,C,H,P,F,j,T,D,U,O;return[a(B,null,{default:n(()=>{var p,m;return[a(l,{label:t.$trans("contact.props.contact_number"),value:e.data.new.contactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.alternate_contact_number"),value:e.data.new.alternateContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.email"),value:e.data.new.email},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_contact_number"),value:e.data.new.fatherContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_email"),value:e.data.new.fatherEmail},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_birth_date"),value:(p=e.data.new.fatherBirthDate)==null?void 0:p.formatted},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_occupation"),value:e.data.new.fatherOccupation},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_annual_income"),value:e.data.new.fatherAnnualIncome},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_contact_number"),value:e.data.new.motherContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_email"),value:e.data.new.motherEmail},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_birth_date"),value:(m=e.data.new.motherBirthDate)==null?void 0:m.formatted},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_occupation"),value:e.data.new.motherOccupation},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_annual_income"),value:e.data.new.motherAnnualIncome},null,8,["label","value"]),a(l,{label:u(W),value:e.data.new.uniqueIdNumber1},null,8,["label","value"]),a(l,{label:u(X),value:e.data.new.uniqueIdNumber2},null,8,["label","value"]),a(l,{label:u(Y),value:e.data.new.uniqueIdNumber3},null,8,["label","value"]),a(l,{label:u(Z),value:e.data.new.uniqueIdNumber4},null,8,["label","value"]),a(l,{label:u(x),value:e.data.new.uniqueIdNumber5},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.birth_place"),value:e.data.new.birthPlace},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.nationality"),value:e.data.new.nationality},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_tongue"),value:e.data.new.motherTongue},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.blood_group"),value:t.$trans("list.blood_groups."+e.data.new.bloodGroup)},null,8,["label","value"]),a(l,{label:t.$trans("contact.religion.religion"),value:e.data.new.religion},null,8,["label","value"]),a(l,{label:t.$trans("contact.category.category"),value:e.data.new.category},null,8,["label","value"]),a(l,{label:t.$trans("contact.caste.caste"),value:e.data.new.caste},null,8,["label","value"])]}),_:1}),(i=e.data.new.presentAddress)!=null&&i.addressLine1||(z=e.data.new.presentAddress)!=null&&z.addressLine2||(E=e.data.new.presentAddress)!=null&&E.city||(k=e.data.new.presentAddress)!=null&&k.state||(C=e.data.new.presentAddress)!=null&&C.zipcode||(H=e.data.new.presentAddress)!=null&&H.country?(b(),q(R,{key:0,class:"mt-4"},{legend:n(()=>[r(o(t.$trans("contact.props.present_address")),1)]),default:n(()=>[a(B,null,{default:n(()=>{var p,m,f,$,w,_;return[a(l,{label:t.$trans("contact.props.address.address_line1"),value:(p=e.data.new.presentAddress)==null?void 0:p.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(m=e.data.new.presentAddress)==null?void 0:m.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:(f=e.data.new.presentAddress)==null?void 0:f.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:($=e.data.new.presentAddress)==null?void 0:$.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(w=e.data.new.presentAddress)==null?void 0:w.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(_=e.data.new.presentAddress)==null?void 0:_.country},null,8,["label","value"])]}),_:1})]),_:1})):h("",!0),(P=e.data.new.permanentAddress)!=null&&P.sameAsPresentAddress||(F=e.data.new.permanentAddress)!=null&&F.addressLine1||(j=e.data.new.permanentAddress)!=null&&j.addressLine2||(T=e.data.new.permanentAddress)!=null&&T.city||(D=e.data.new.permanentAddress)!=null&&D.state||(U=e.data.new.permanentAddress)!=null&&U.zipcode||(O=e.data.new.permanentAddress)!=null&&O.country?(b(),q(R,{key:1,class:"mt-4"},{legend:n(()=>[r(o(t.$trans("contact.props.permanent_address")),1)]),default:n(()=>[a(B,null,{default:n(()=>{var p,m,f,$,w,_,M;return[a(l,{label:t.$trans("contact.props.same_as_present_address"),value:(p=e.data.new.permanentAddress)!=null&&p.sameAsPresentAddress?t.$trans("general.yes"):t.$trans("general.no")},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line1"),value:(m=e.data.new.permanentAddress)==null?void 0:m.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(f=e.data.new.permanentAddress)==null?void 0:f.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:($=e.data.new.permanentAddress)==null?void 0:$.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(w=e.data.new.permanentAddress)==null?void 0:w.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(_=e.data.new.permanentAddress)==null?void 0:_.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(M=e.data.new.permanentAddress)==null?void 0:M.country},null,8,["label","value"])]}),_:1})]),_:1})):h("",!0)]}),_:1}),a(c,{class:"col-span-1 sm:col-span-2"},{default:n(()=>[a(re,{media:e.media,url:`/app/student/edit-requests/${e.uuid}/`},null,8,["media","url"])]),_:1}),a(c,{label:t.$trans("general.created_at")},{default:n(()=>[r(o(e.createdAt.formatted),1)]),_:1},8,["label"]),a(c,{label:t.$trans("general.updated_at")},{default:n(()=>[r(o(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):h("",!0),e.uuid&&e.status.value=="pending"?(b(),q(V,{key:1},{title:n(()=>[r(o(t.$trans("student.edit_request.props.action")),1)]),default:n(()=>[a(de,{"no-card":"","keep-adding":!1,"init-url":L,uuid:e.uuid,"no-data-fetch":!0,action:"action","init-form":S,form:v,"after-submit":ae},{default:n(()=>[N("div",he,[N("div",Ae,[a(oe,{name:"status",label:t.$trans("global.select",{attribute:t.$trans("reception.complaint.props.status")}),modelValue:v.status,"onUpdate:modelValue":d[0]||(d[0]=i=>v.status=i),options:[{value:"approve",label:t.$trans("student.edit_request.statuses.approve")},{value:"reject",label:t.$trans("student.edit_request.statuses.reject")}],error:u(A).status,"onUpdate:error":d[1]||(d[1]=i=>u(A).status=i)},null,8,["label","modelValue","options","error"])]),N("div",qe,[a(ue,{modelValue:v.comment,"onUpdate:modelValue":d[2]||(d[2]=i=>v.comment=i),name:"comment",label:t.$trans("student.edit_request.props.comment"),error:u(A).comment,"onUpdate:error":d[3]||(d[3]=i=>u(A).comment=i)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})):h("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{Ie as default};
