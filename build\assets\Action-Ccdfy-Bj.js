import{u as T,G as N,H as X,l as C,n as Y,r as d,q as D,o as f,w as c,d as l,a as j,e as i,f as t,b as H,s as V,t as p,F as w,v as Z,I as x,M as ee,J as S}from"./app-BAwPsakn.js";import{u as oe}from"./useCustomFields-C7JPVoj8.js";const re={class:"grid grid-cols-4 gap-6"},se={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},te={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},le={class:"mt-4 grid grid-cols-3 gap-6"},ie={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe=["onClick"],ce={class:"mt-4 grid grid-cols-4 gap-4"},be={class:"col-span-4 sm:col-span-1"},ye={class:"col-span-4 sm:col-span-1"},ge={class:"col-span-4 sm:col-span-1"},Ve={class:"col-span-4 sm:col-span-1"},ve={class:"mt-4"},Ue={class:"mt-4 grid grid-cols-1"},$e={class:"col"},fe={class:"col"},_e={name:"ReceptionEnquiryForm"},Fe=Object.assign(_e,{setup(A){const v=T(),m={period:"",date:"",type:"",source:"",employee:"",name:"",email:"",contactNumber:"",records:[],remarks:"",media:[],mediaUpdated:!1,mediaToken:N(),mediaHash:[],customFields:[]},h={uuid:N(),name:"",birthDate:"",gender:"",course:""},k="reception/enquiry/",a=X(k),b=C({types:[],sources:[],genders:[],periods:[],customFields:[]}),r=C({...m}),y=C({isLoaded:!v.params.uuid}),{customFields:E,setCustomFields:L}=oe(),M=s=>{Object.assign(b,s),L(b.customFields),m.customFields=E.value,Object.assign(r,S(m))},P=()=>{r.mediaToken=N(),r.mediaHash=[]},O=()=>{r.records.push({...h,uuid:N()}),y.isLoaded=!0},I=async s=>{await ee()&&(r.records.length==1?r.records=[h]:r.records.splice(s,1))},G=s=>{var U,_,g,F;let o=s.records.map($=>{var q,B,R;return{...$,birthDate:((q=$.birthDate)==null?void 0:q.value)||"",gender:((B=$.gender)==null?void 0:B.value)||"",course:((R=$.course)==null?void 0:R.uuid)||""}});Object.assign(m,{...s,date:s.date.value,period:s.period.uuid,type:((U=s.type)==null?void 0:U.uuid)||"",source:((_=s.source)==null?void 0:_.uuid)||"",employee:((g=s.employee)==null?void 0:g.uuid)||"",records:o}),L(b.customFields,s.customFields),m.customFields=E.value,Object.assign(r,S(m)),y.employee=(F=s.employee)==null?void 0:F.uuid,y.isLoaded=!0},z=()=>{y.isLoaded};return Y(async()=>{v.params.uuid||O()}),(s,o)=>{const U=d("BaseSelect"),_=d("DatePicker"),g=d("BaseInput"),F=d("BaseSelectSearch"),$=d("BaseLabel"),q=d("BaseRadioGroup"),B=d("BaseFieldset"),R=d("BaseBadge"),J=d("BaseTextarea"),K=d("MediaUpload"),Q=d("CustomField"),W=d("FormAction");return f(),D(W,{"pre-requisites":!0,onSetPreRequisites:M,"init-url":k,"init-form":m,form:r,"set-form":G,redirect:"ReceptionEnquiry",onResetMediaFiles:P},{default:c(()=>[l("div",re,[l("div",se,[i(U,{modelValue:r.period,"onUpdate:modelValue":o[0]||(o[0]=e=>r.period=e),name:"period",label:s.$trans("global.select",{attribute:s.$trans("academic.period.period")}),options:b.periods,"label-prop":"name","value-prop":"uuid",error:t(a).period,"onUpdate:error":o[1]||(o[1]=e=>t(a).period=e),onChange:z},null,8,["modelValue","label","options","error"])]),l("div",ae,[i(_,{modelValue:r.date,"onUpdate:modelValue":o[2]||(o[2]=e=>r.date=e),name:"date",label:s.$trans("reception.enquiry.props.date"),"no-clear":"",error:t(a).date,"onUpdate:error":o[3]||(o[3]=e=>t(a).date=e)},null,8,["modelValue","label","error"])]),l("div",te,[i(U,{modelValue:r.type,"onUpdate:modelValue":o[4]||(o[4]=e=>r.type=e),name:"type",label:s.$trans("reception.enquiry.props.type"),options:b.types,"label-prop":"name","value-prop":"uuid",error:t(a).type,"onUpdate:error":o[5]||(o[5]=e=>t(a).type=e)},null,8,["modelValue","label","options","error"])]),l("div",ne,[i(U,{modelValue:r.source,"onUpdate:modelValue":o[6]||(o[6]=e=>r.source=e),name:"source",label:s.$trans("reception.enquiry.props.source"),options:b.sources,"label-prop":"name","value-prop":"uuid",error:t(a).source,"onUpdate:error":o[7]||(o[7]=e=>t(a).source=e)},null,8,["modelValue","label","options","error"])])]),l("div",le,[l("div",ie,[i(g,{type:"text",modelValue:r.name,"onUpdate:modelValue":o[8]||(o[8]=e=>r.name=e),name:"name",label:s.$trans("reception.enquiry.props.name"),error:t(a).name,"onUpdate:error":o[9]||(o[9]=e=>t(a).name=e)},null,8,["modelValue","label","error"])]),l("div",de,[i(g,{type:"text",modelValue:r.email,"onUpdate:modelValue":o[10]||(o[10]=e=>r.email=e),name:"email",label:s.$trans("reception.enquiry.props.email"),error:t(a).email,"onUpdate:error":o[11]||(o[11]=e=>t(a).email=e)},null,8,["modelValue","label","error"])]),l("div",ue,[i(g,{type:"text",modelValue:r.contactNumber,"onUpdate:modelValue":o[12]||(o[12]=e=>r.contactNumber=e),name:"contactNumber",label:s.$trans("reception.enquiry.props.contact_number"),error:t(a).contactNumber,"onUpdate:error":o[13]||(o[13]=e=>t(a).contactNumber=e)},null,8,["modelValue","label","error"])]),l("div",me,[y.isLoaded?(f(),D(F,{key:0,name:"employee",label:s.$trans("employee.employee"),placeholder:s.$trans("global.select",{attribute:s.$trans("employee.employee")}),modelValue:r.employee,"onUpdate:modelValue":o[14]||(o[14]=e=>r.employee=e),error:t(a).employee,"onUpdate:error":o[15]||(o[15]=e=>t(a).employee=e),"value-prop":"uuid","init-search":y.employee,"search-key":"name","search-action":"employee/summary"},{selectedOption:c(e=>[V(p(e.value.name)+" ("+p(e.value.designation)+") ",1)]),listOption:c(e=>[V(p(e.option.name)+" ("+p(e.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):H("",!0)])]),(f(!0),j(w,null,Z(r.records,(e,u)=>(f(),D(B,{class:"mt-4",key:e.uuid},{legend:c(()=>[V(p(u+1)+". ",1),l("span",{class:"text-danger ml-2 cursor-pointer",onClick:n=>I(u)},o[22]||(o[22]=[l("i",{class:"fas fa-times-circle"},null,-1)]),8,pe)]),default:c(()=>[l("div",ce,[l("div",be,[i(g,{type:"text",modelValue:e.studentName,"onUpdate:modelValue":n=>e.studentName=n,name:`records.${u}.studentName`,label:s.$trans("reception.enquiry.props.student_name"),error:t(a)[`records.${u}.studentName`],"onUpdate:error":n=>t(a)[`records.${u}.studentName`]=n},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),l("div",ye,[i(_,{modelValue:e.birthDate,"onUpdate:modelValue":n=>e.birthDate=n,name:`records.${u}.birthDate`,label:s.$trans("contact.props.birth_date"),"no-clear":"",error:t(a)[`records.${u}.birthDate`],"onUpdate:error":n=>t(a)[`records.${u}.birthDate`]=n},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),l("div",ge,[i($,null,{default:c(()=>[V(p(s.$trans("contact.props.gender")),1)]),_:1}),i(q,{"top-margin":"",options:b.genders,name:`records.${u}.gender`,modelValue:e.gender,"onUpdate:modelValue":n=>e.gender=n,error:t(a)[`records.${u}.gender`],"onUpdate:error":n=>t(a)[`records.${u}.gender`]=n,horizontal:""},null,8,["options","name","modelValue","onUpdate:modelValue","error","onUpdate:error"])]),l("div",Ve,[y.isLoaded?(f(),D(F,{key:0,name:`records.${u}.course`,label:s.$trans("global.select",{attribute:s.$trans("academic.course.course")}),modelValue:e.course,"onUpdate:modelValue":n=>e.course=n,error:t(a)[`records.${u}.course`],"onUpdate:error":n=>t(a)[`records.${u}.course`]=n,"value-prop":"uuid","init-search":e.course,"init-search-key":t(v).params.uuid?"uuid":"name","additional-search-query":{period:r.period},"search-action":"academic/course/list"},{selectedOption:c(n=>[V(p(n.value.division.name)+" - "+p(n.value.name),1)]),listOption:c(n=>[V(p(n.option.division.name)+" - "+p(n.option.name),1)]),_:2},1032,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","init-search","init-search-key","additional-search-query"])):H("",!0)])])]),_:2},1024))),128)),l("div",ve,[i(R,{design:"primary",onClick:O,class:"cursor-pointer"},{default:c(()=>[V(p(s.$trans("global.add",{attribute:s.$trans("general.record")})),1)]),_:1})]),l("div",Ue,[l("div",$e,[i(J,{rows:1,modelValue:r.remarks,"onUpdate:modelValue":o[16]||(o[16]=e=>r.remarks=e),name:"remarks",label:s.$trans("reception.enquiry.props.remarks"),error:t(a).remarks,"onUpdate:error":o[17]||(o[17]=e=>t(a).remarks=e)},null,8,["modelValue","label","error"])]),l("div",fe,[i(K,{multiple:"",label:s.$trans("general.file"),module:"enquiry",media:r.media,"media-token":r.mediaToken,onIsUpdated:o[18]||(o[18]=e=>r.mediaUpdated=!0),onSetHash:o[19]||(o[19]=e=>r.mediaHash.push(e))},null,8,["label","media","media-token"])])]),i(Q,{customFields:r.customFields,"onUpdate:customFields":o[20]||(o[20]=e=>r.customFields=e),formErrors:t(a),"onUpdate:formErrors":o[21]||(o[21]=e=>x(a)?a.value=e:null)},null,8,["customFields","formErrors"])]),_:1},8,["form"])}}}),ke={name:"ReceptionEnquiryAction"},Re=Object.assign(ke,{setup(A){const v=T();return(m,h)=>{const k=d("PageHeaderAction"),a=d("PageHeader"),b=d("ParentTransition");return f(),j(w,null,[i(a,{title:m.$trans(t(v).meta.trans,{attribute:m.$trans(t(v).meta.label)}),navs:[{label:m.$trans("reception.reception"),path:"Reception"},{label:m.$trans("reception.enquiry.enquiry"),path:"ReceptionEnquiryList"}]},{default:c(()=>[i(k,{name:"ReceptionEnquiry",title:m.$trans("reception.enquiry.enquiry"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(b,{appear:"",visibility:!0},{default:c(()=>[i(Fe)]),_:1})],64)}}});export{Re as default};
