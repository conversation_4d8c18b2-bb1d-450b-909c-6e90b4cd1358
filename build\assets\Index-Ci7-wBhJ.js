import{u as U,l as T,n as j,r as l,q as _,o as m,w as e,d as V,e as a,h as S,j as q,y as g,m as O,f as o,a as E,F as R,v as z,s,t as i,b}from"./app-BAwPsakn.js";import{_ as G}from"./ModuleDropdown-sYr-p8uC.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(B,{emit:p}){U();const h=p,$={startDate:"",endDate:""},d=T({...$}),D=T({isLoaded:!0});return j(async()=>{D.isLoaded=!0}),(v,u)=>{const C=l("DatePicker"),r=l("FilterForm");return m(),_(r,{"init-form":$,form:d,multiple:[],onHide:u[2]||(u[2]=n=>h("hide"))},{default:e(()=>[V("div",J,[V("div",K,[a(C,{start:d.startDate,"onUpdate:start":u[0]||(u[0]=n=>d.startDate=n),end:d.endDate,"onUpdate:end":u[1]||(u[1]=n=>d.endDate=n),name:"dateBetween",as:"range",label:v.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={name:"TransportVehicleFuelRecordList"},Z=Object.assign(W,{setup(B){const p=S(),h=q("emitter");let $=["filter"];g("vehicle-fuel-record:create")&&$.unshift("create");let d=[];g("vehicle-fuel-record:export")&&(d=["print","pdf","excel"]);const D="transport/vehicle/fuelRecord/",v=O(!1),u=T({}),C=r=>{Object.assign(u,r)};return(r,n)=>{const P=l("PageHeaderAction"),F=l("PageHeader"),w=l("ParentTransition"),I=l("TextMuted"),c=l("DataCell"),k=l("FloatingMenuItem"),M=l("FloatingMenu"),A=l("DataRow"),H=l("BaseButton"),L=l("DataTable"),N=l("ListItem");return m(),_(N,{"init-url":D,onSetItems:C},{header:e(()=>[a(F,{title:r.$trans("transport.vehicle.fuel_record.fuel_record"),navs:[{label:r.$trans("transport.transport"),path:"Transport"},{label:r.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"}]},{default:e(()=>[a(P,{url:"transport/vehicle/fuel-records/",name:"TransportVehicleFuelRecord",title:r.$trans("transport.vehicle.fuel_record.fuel_record"),actions:o($),"dropdown-actions":o(d),onToggleFilter:n[0]||(n[0]=t=>v.value=!v.value)},{moduleOption:e(()=>[a(G)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(w,{appear:"",visibility:v.value},{default:e(()=>[a(Q,{onRefresh:n[1]||(n[1]=t=>o(h).emit("listItems")),onHide:n[2]||(n[2]=t=>v.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(w,{appear:"",visibility:!0},{default:e(()=>[a(L,{header:u.headers,meta:u.meta,module:"transport.vehicle.fuel_record",onRefresh:n[4]||(n[4]=t=>o(h).emit("listItems"))},{actionButton:e(()=>[o(g)("vehicle-fuel-record:create")?(m(),_(H,{key:0,onClick:n[3]||(n[3]=t=>o(p).push({name:"TransportVehicleFuelRecordCreate"}))},{default:e(()=>[s(i(r.$trans("global.add",{attribute:r.$trans("transport.vehicle.fuel_record.fuel_record")})),1)]),_:1})):b("",!0)]),default:e(()=>[(m(!0),E(R,null,z(u.data,t=>(m(),_(A,{key:t.uuid,onDoubleClick:f=>o(p).push({name:"TransportVehicleFuelRecordShow",params:{uuid:t.uuid}})},{default:e(()=>[a(c,{name:"vehicle"},{default:e(()=>{var f;return[s(i((f=t.vehicle)==null?void 0:f.name)+" ",1),a(I,{block:""},{default:e(()=>{var y;return[s(i((y=t.vehicle)==null?void 0:y.registrationNumber),1)]}),_:2},1024)]}),_:2},1024),a(c,{name:"date"},{default:e(()=>[s(i(t.date.formatted),1)]),_:2},1024),a(c,{name:"quantity"},{default:e(()=>[s(i(t.quantity),1)]),_:2},1024),a(c,{name:"pricePerUnit"},{default:e(()=>[s(i(t.pricePerUnit.formatted),1)]),_:2},1024),a(c,{name:"cost"},{default:e(()=>[s(i(t.cost.formatted),1)]),_:2},1024),a(c,{name:"log"},{default:e(()=>[s(i(t.log),1)]),_:2},1024),a(c,{name:"createdAt"},{default:e(()=>[s(i(t.createdAt.formatted),1)]),_:2},1024),a(c,{name:"action"},{default:e(()=>[a(M,null,{default:e(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:f=>o(p).push({name:"TransportVehicleFuelRecordShow",params:{uuid:t.uuid}})},{default:e(()=>[s(i(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(g)("vehicle-fuel-record:edit")?(m(),_(k,{key:0,icon:"fas fa-edit",onClick:f=>o(p).push({name:"TransportVehicleFuelRecordEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(i(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),o(g)("vehicle-fuel-record:create")?(m(),_(k,{key:1,icon:"fas fa-copy",onClick:f=>o(p).push({name:"TransportVehicleFuelRecordDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[s(i(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),o(g)("vehicle-fuel-record:delete")?(m(),_(k,{key:2,icon:"fas fa-trash",onClick:f=>o(h).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(i(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Z as default};
