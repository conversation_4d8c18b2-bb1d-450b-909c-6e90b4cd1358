import{l as y,r as o,q as m,o as l,w as e,d as w,e as r,h as j,j as L,y as _,m as M,f as n,a as N,F as S,v as E,s as c,t as d,b as k}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",emits:["hide"],setup(B,{emit:u}){const v=u,C={name:""},p=y({...C});return(b,i)=>{const f=o("BaseInput"),F=o("FilterForm");return l(),m(F,{"init-form":C,form:p,onHide:i[1]||(i[1]=t=>v("hide"))},{default:e(()=>[w("div",O,[w("div",U,[r(f,{type:"text",modelValue:p.name,"onUpdate:modelValue":i[0]||(i[0]=t=>p.name=t),name:"name",label:b.$trans("transport.circle.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"TransportCircleList"},J=Object.assign(z,{setup(B){const u=j(),v=L("emitter");let C=["filter"];_("transport-circle:create")&&C.unshift("create");let p=[];_("transport-circle:export")&&(p=["print","pdf","excel"]);const b="transport/circle/",i=M(!1),f=y({}),F=t=>{Object.assign(f,t)};return(t,s)=>{const I=o("PageHeaderAction"),D=o("PageHeader"),h=o("ParentTransition"),T=o("DataCell"),g=o("FloatingMenuItem"),V=o("FloatingMenu"),A=o("DataRow"),H=o("BaseButton"),P=o("DataTable"),R=o("ListItem");return l(),m(R,{"init-url":b,onSetItems:F},{header:e(()=>[r(D,{title:t.$trans("transport.circle.circle"),navs:[{label:t.$trans("transport.transport"),path:"Transport"}]},{default:e(()=>[r(I,{url:"transport/circles/",name:"TransportCircle",title:t.$trans("transport.circle.circle"),actions:n(C),"dropdown-actions":n(p),onToggleFilter:s[0]||(s[0]=a=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[r(h,{appear:"",visibility:i.value},{default:e(()=>[r(q,{onRefresh:s[1]||(s[1]=a=>n(v).emit("listItems")),onHide:s[2]||(s[2]=a=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[r(h,{appear:"",visibility:!0},{default:e(()=>[r(P,{header:f.headers,meta:f.meta,module:"transport.circle",onRefresh:s[4]||(s[4]=a=>n(v).emit("listItems"))},{actionButton:e(()=>[n(_)("transport-circle:create")?(l(),m(H,{key:0,onClick:s[3]||(s[3]=a=>n(u).push({name:"TransportCircleCreate"}))},{default:e(()=>[c(d(t.$trans("global.add",{attribute:t.$trans("transport.circle.circle")})),1)]),_:1})):k("",!0)]),default:e(()=>[(l(!0),N(S,null,E(f.data,a=>(l(),m(A,{key:a.uuid,onDoubleClick:$=>n(u).push({name:"TransportCircleShow",params:{uuid:a.uuid}})},{default:e(()=>[r(T,{name:"name"},{default:e(()=>[c(d(a.name),1)]),_:2},1024),r(T,{name:"createdAt"},{default:e(()=>[c(d(a.createdAt.formatted),1)]),_:2},1024),r(T,{name:"action"},{default:e(()=>[r(V,null,{default:e(()=>[r(g,{icon:"fas fa-arrow-circle-right",onClick:$=>n(u).push({name:"TransportCircleShow",params:{uuid:a.uuid}})},{default:e(()=>[c(d(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(_)("transport-circle:edit")?(l(),m(g,{key:0,icon:"fas fa-edit",onClick:$=>n(u).push({name:"TransportCircleEdit",params:{uuid:a.uuid}})},{default:e(()=>[c(d(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),n(_)("transport-circle:create")?(l(),m(g,{key:1,icon:"fas fa-copy",onClick:$=>n(u).push({name:"TransportCircleDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[c(d(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),n(_)("transport-circle:delete")?(l(),m(g,{key:2,icon:"fas fa-trash",onClick:$=>n(v).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[c(d(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{J as default};
