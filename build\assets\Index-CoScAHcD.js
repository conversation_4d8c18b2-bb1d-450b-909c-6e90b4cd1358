import{l as y,r,q as p,o as l,w as e,d as w,e as o,h as j,j as L,y as _,m as M,f as n,a as N,F as S,v as E,s as d,t as m,b as k}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",emits:["hide"],setup(B,{emit:u}){const v=u,g={name:""},c=y({...g});return(C,i)=>{const f=r("BaseInput"),b=r("FilterForm");return l(),p(b,{"init-form":g,form:c,onHide:i[1]||(i[1]=t=>v("hide"))},{default:e(()=>[w("div",O,[w("div",U,[o(f,{type:"text",modelValue:c.name,"onUpdate:modelValue":i[0]||(i[0]=t=>c.name=t),name:"name",label:C.$trans("transport.fee.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"TransportFeeList"},J=Object.assign(z,{setup(B){const u=j(),v=L("emitter");let g=["filter"];_("transport-fee:create")&&g.unshift("create");let c=[];_("transport-fee:export")&&(c=["print","pdf","excel"]);const C="transport/fee/",i=M(!1),f=y({}),b=t=>{Object.assign(f,t)};return(t,s)=>{const I=r("PageHeaderAction"),D=r("PageHeader"),h=r("ParentTransition"),T=r("DataCell"),$=r("FloatingMenuItem"),V=r("FloatingMenu"),A=r("DataRow"),H=r("BaseButton"),P=r("DataTable"),R=r("ListItem");return l(),p(R,{"init-url":C,onSetItems:b},{header:e(()=>[o(D,{title:t.$trans("transport.fee.fee"),navs:[{label:t.$trans("transport.transport"),path:"Transport"}]},{default:e(()=>[o(I,{url:"transport/fees/",name:"TransportFee",title:t.$trans("transport.fee.fee"),actions:n(g),"dropdown-actions":n(c),onToggleFilter:s[0]||(s[0]=a=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(h,{appear:"",visibility:i.value},{default:e(()=>[o(q,{onRefresh:s[1]||(s[1]=a=>n(v).emit("listItems")),onHide:s[2]||(s[2]=a=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(h,{appear:"",visibility:!0},{default:e(()=>[o(P,{header:f.headers,meta:f.meta,module:"transport.fee",onRefresh:s[4]||(s[4]=a=>n(v).emit("listItems"))},{actionButton:e(()=>[n(_)("transport-fee:create")?(l(),p(H,{key:0,onClick:s[3]||(s[3]=a=>n(u).push({name:"TransportFeeCreate"}))},{default:e(()=>[d(m(t.$trans("global.add",{attribute:t.$trans("transport.fee.fee")})),1)]),_:1})):k("",!0)]),default:e(()=>[(l(!0),N(S,null,E(f.data,a=>(l(),p(A,{key:a.uuid,onDoubleClick:F=>n(u).push({name:"TransportFeeShow",params:{uuid:a.uuid}})},{default:e(()=>[o(T,{name:"name"},{default:e(()=>[d(m(a.name),1)]),_:2},1024),o(T,{name:"createdAt"},{default:e(()=>[d(m(a.createdAt.formatted),1)]),_:2},1024),o(T,{name:"action"},{default:e(()=>[o(V,null,{default:e(()=>[o($,{icon:"fas fa-arrow-circle-right",onClick:F=>n(u).push({name:"TransportFeeShow",params:{uuid:a.uuid}})},{default:e(()=>[d(m(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(_)("transport-fee:edit")?(l(),p($,{key:0,icon:"fas fa-edit",onClick:F=>n(u).push({name:"TransportFeeEdit",params:{uuid:a.uuid}})},{default:e(()=>[d(m(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),n(_)("transport-fee:create")?(l(),p($,{key:1,icon:"fas fa-copy",onClick:F=>n(u).push({name:"TransportFeeDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[d(m(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),n(_)("transport-fee:delete")?(l(),p($,{key:2,icon:"fas fa-trash",onClick:F=>n(v).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[d(m(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{J as default};
