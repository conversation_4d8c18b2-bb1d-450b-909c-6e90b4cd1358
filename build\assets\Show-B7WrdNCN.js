import{j as me,i as ce,u as be,h as ve,g,H as $e,l as G,m as we,r as n,a as J,o as m,e as a,w as s,f as r,q,b as _,d as L,s as u,t as i,F as ye}from"./app-BAwPsakn.js";const fe={key:0,class:"mb-4"},ge={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},_e={class:"grid grid-cols-3 gap-6"},Ae={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3"},Le={name:"EmployeeEditRequestShow"},Be=Object.assign(Le,{setup(Ne){me("emitter"),ce();const N=be(),K=ve(),Q={},E={status:"",comment:""},I="employee/editRequest/",W=g("contact.uniqueIdNumber1Label"),X=g("contact.uniqueIdNumber2Label"),Y=g("contact.uniqueIdNumber3Label"),Z=g("contact.uniqueIdNumber4Label"),x=g("contact.uniqueIdNumber5Label"),A=$e(I),c=G({...E}),B=we(!1),e=G({...Q}),ee=t=>{Object.assign(e,t)},ae=()=>{B.value=!0};return(t,o)=>{const te=n("PageHeaderAction"),le=n("PageHeader"),se=n("BaseAlert"),ne=n("BaseBadge"),p=n("BaseDataView"),l=n("HorizontalListItem"),h=n("HorizontalList"),R=n("BaseFieldset"),re=n("ListMedia"),V=n("BaseCard"),oe=n("BaseSelect"),de=n("BaseTextarea"),ue=n("FormAction"),ie=n("ShowItem"),pe=n("ParentTransition");return m(),J(ye,null,[a(le,{title:t.$trans(r(N).meta.trans,{attribute:t.$trans(r(N).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:t.$trans("employee.edit_request.edit_request"),path:"EmployeeEditRequestList"}]},{default:s(()=>[a(te,{name:"EmployeeEditRequest",title:t.$trans("employee.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(pe,{appear:"",visibility:!0},{default:s(()=>[a(ie,{"init-url":I,uuid:r(N).params.uuid,onSetItem:ee,onRedirectTo:o[4]||(o[4]=d=>r(K).push({name:"EmployeeEditRequest"})),refresh:B.value,onRefreshed:o[5]||(o[5]=d=>B.value=!1)},{default:s(()=>[e.uuid?(m(),q(V,{key:0},{title:s(()=>[u(i(e.employee.name),1)]),default:s(()=>[e.isRejected&&e.comment?(m(),J("div",fe,[a(se,{design:"error",size:"xs"},{default:s(()=>[u(i(e.comment),1)]),_:1})])):_("",!0),L("dl",ge,[a(p,{label:t.$trans("employee.admission.props.code_number")},{default:s(()=>[u(i(e.employee.codeNumber)+" ",1),a(ne,{design:e.status.color},{default:s(()=>[u(i(e.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),a(p,{label:t.$trans("contact.props.birth_date")},{default:s(()=>[u(i(e.employee.birthDate.formatted),1)]),_:1},8,["label"]),a(p,{label:t.$trans("employee.designation.designation")},{default:s(()=>[u(i(e.employee.designation+" "+e.employee.department),1)]),_:1},8,["label"]),a(p,{label:t.$trans("employee.edit_request.request_by")},{default:s(()=>[u(i(e.user.profile.name),1)]),_:1},8,["label"]),a(p,{class:"col-span-1 sm:col-span-2"},{default:s(()=>{var d,z,S,k,H,P,F,j,C,T,U,D,M;return[a(h,null,{default:s(()=>[a(l,{label:t.$trans("contact.props.contact_number"),value:e.data.new.contactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.alternate_contact_number"),value:e.data.new.alternateContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.email"),value:e.data.new.email},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_name"),value:e.data.new.fatherName},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_name"),value:e.data.new.motherName},null,8,["label","value"]),a(l,{label:r(W),value:e.data.new.uniqueIdNumber1},null,8,["label","value"]),a(l,{label:r(X),value:e.data.new.uniqueIdNumber2},null,8,["label","value"]),a(l,{label:r(Y),value:e.data.new.uniqueIdNumber3},null,8,["label","value"]),a(l,{label:r(Z),value:e.data.new.uniqueIdNumber4},null,8,["label","value"]),a(l,{label:r(x),value:e.data.new.uniqueIdNumber5},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.birth_place"),value:e.data.new.birthPlace},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.nationality"),value:e.data.new.nationality},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_tongue"),value:e.data.new.motherTongue},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.blood_group"),value:t.$trans("list.blood_groups."+e.data.new.bloodGroup)},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.marital_status"),value:t.$trans("list.marital_statuses."+e.data.new.maritalStatus)},null,8,["label","value"]),a(l,{label:t.$trans("contact.religion.religion"),value:e.data.new.religion},null,8,["label","value"]),a(l,{label:t.$trans("contact.category.category"),value:e.data.new.category},null,8,["label","value"]),a(l,{label:t.$trans("contact.caste.caste"),value:e.data.new.caste},null,8,["label","value"])]),_:1}),(d=e.data.new.presentAddress)!=null&&d.addressLine1||(z=e.data.new.presentAddress)!=null&&z.addressLine2||(S=e.data.new.presentAddress)!=null&&S.city||(k=e.data.new.presentAddress)!=null&&k.state||(H=e.data.new.presentAddress)!=null&&H.zipcode||(P=e.data.new.presentAddress)!=null&&P.country?(m(),q(R,{key:0,class:"mt-4"},{legend:s(()=>[u(i(t.$trans("contact.props.present_address")),1)]),default:s(()=>[a(h,null,{default:s(()=>{var b,v,$,w,y,f;return[a(l,{label:t.$trans("contact.props.address.address_line1"),value:(b=e.data.new.presentAddress)==null?void 0:b.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(v=e.data.new.presentAddress)==null?void 0:v.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:($=e.data.new.presentAddress)==null?void 0:$.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(w=e.data.new.presentAddress)==null?void 0:w.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(y=e.data.new.presentAddress)==null?void 0:y.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(f=e.data.new.presentAddress)==null?void 0:f.country},null,8,["label","value"])]}),_:1})]),_:1})):_("",!0),(F=e.data.new.permanentAddress)!=null&&F.sameAsPresentAddress||(j=e.data.new.permanentAddress)!=null&&j.addressLine1||(C=e.data.new.permanentAddress)!=null&&C.addressLine2||(T=e.data.new.permanentAddress)!=null&&T.city||(U=e.data.new.permanentAddress)!=null&&U.state||(D=e.data.new.permanentAddress)!=null&&D.zipcode||(M=e.data.new.permanentAddress)!=null&&M.country?(m(),q(R,{key:1,class:"mt-4"},{legend:s(()=>[u(i(t.$trans("contact.props.permanent_address")),1)]),default:s(()=>[a(h,null,{default:s(()=>{var b,v,$,w,y,f,O;return[a(l,{label:t.$trans("contact.props.same_as_present_address"),value:(b=e.data.new.permanentAddress)!=null&&b.sameAsPresentAddress?t.$trans("general.yes"):t.$trans("general.no")},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line1"),value:(v=e.data.new.permanentAddress)==null?void 0:v.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:($=e.data.new.permanentAddress)==null?void 0:$.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:(w=e.data.new.permanentAddress)==null?void 0:w.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(y=e.data.new.permanentAddress)==null?void 0:y.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(f=e.data.new.permanentAddress)==null?void 0:f.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(O=e.data.new.permanentAddress)==null?void 0:O.country},null,8,["label","value"])]}),_:1})]),_:1})):_("",!0)]}),_:1}),a(p,{class:"col-span-1 sm:col-span-2"},{default:s(()=>[a(re,{media:e.media,url:`/app/employee/edit-requests/${e.uuid}/`},null,8,["media","url"])]),_:1}),a(p,{label:t.$trans("general.created_at")},{default:s(()=>[u(i(e.createdAt.formatted),1)]),_:1},8,["label"]),a(p,{label:t.$trans("general.updated_at")},{default:s(()=>[u(i(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0),e.uuid&&e.status.value=="pending"?(m(),q(V,{key:1},{title:s(()=>[u(i(t.$trans("employee.edit_request.props.action")),1)]),default:s(()=>[a(ue,{"no-card":"","keep-adding":!1,"init-url":I,uuid:e.uuid,"no-data-fetch":!0,action:"action","init-form":E,form:c,"after-submit":ae},{default:s(()=>[L("div",_e,[L("div",Ae,[a(oe,{name:"status",label:t.$trans("global.select",{attribute:t.$trans("employee.status")}),modelValue:c.status,"onUpdate:modelValue":o[0]||(o[0]=d=>c.status=d),options:[{value:"approve",label:t.$trans("employee.edit_request.statuses.approve")},{value:"reject",label:t.$trans("employee.edit_request.statuses.reject")}],error:r(A).status,"onUpdate:error":o[1]||(o[1]=d=>r(A).status=d)},null,8,["label","modelValue","options","error"])]),L("div",qe,[a(de,{modelValue:c.comment,"onUpdate:modelValue":o[2]||(o[2]=d=>c.comment=d),name:"comment",label:t.$trans("employee.edit_request.props.comment"),error:r(A).comment,"onUpdate:error":o[3]||(o[3]=d=>r(A).comment=d)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})):_("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{Be as default};
