import{u as A,H as q,g as D,l as y,r as d,z as H,q as T,o as m,w as g,d as i,a as p,b,f as o,e as s,s as B,t as U,F as $,A as E,aP as L,aQ as z,J as I}from"./app-BAwPsakn.js";import{_ as J}from"./ParentDetail-QambUQlq.js";const Q={class:"grid grid-cols-3 gap-6"},K={key:0,class:"col-span-3"},M={key:1,class:"col-span-3 sm:col-span-2"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3"},x={class:"col-span-3 sm:col-span-1"},h={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},ae={class:"flex space-x-2"},te={class:"col-span-3 sm:col-span-1"},oe={name:"StudentGuardianForm"},re=Object.assign(oe,{setup(f){const l=A(),u={type:"new",guardian:null,name:"",relation:"",contactNumber:"",createUserAccount:!1,email:"",username:"",password:"",passwordConfirmation:""},S="student/guardian/",n=q(S),P=D("system.enableStrongPassword"),w=y({relations:[],guardianTypes:[]}),t=y({...u}),C=y({isLoaded:!l.params.muuid}),N=y({hidePassword:!0}),k=r=>{Object.assign(w,r)},F=()=>{var r=P.value?L(12):z(6);t.password=r,t.passwordConfirmation=r},O=r=>{var a,V,v;Object.assign(u,{...r,name:((a=r.contact)==null?void 0:a.name)||"",relation:(V=r.relation)==null?void 0:V.value,contactNumber:((v=r.contact)==null?void 0:v.contactNumber)||""}),Object.assign(t,I(u)),C.isLoaded=!0};return(r,a)=>{const V=d("BaseRadioGroup"),v=d("BaseSelectSearch"),c=d("BaseInput"),G=d("BaseSelect"),_=d("BaseSwitch"),j=d("FormAction"),R=H("tooltip");return m(),T(j,{"pre-requisites":!0,onSetPreRequisites:k,"no-data-fetch":"","init-url":S,uuid:o(l).params.uuid,"module-uuid":o(l).params.muuid,"init-form":u,form:t,"set-form":O,redirect:{name:"StudentGuardian",params:{uuid:o(l).params.uuid}}},{default:g(()=>[i("div",Q,[o(l).params.muuid?b("",!0):(m(),p("div",K,[s(V,{"top-margin":"",options:w.guardianTypes,name:"type",modelValue:t.type,"onUpdate:modelValue":a[0]||(a[0]=e=>t.type=e),error:o(n).type,"onUpdate:error":a[1]||(a[1]=e=>o(n).type=e),horizontal:""},null,8,["options","modelValue","error"])])),t.type=="existing"?(m(),p("div",M,[s(v,{name:"guardian",label:r.$trans("global.select",{attribute:r.$trans("guardian.guardian")}),modelValue:t.guardian,"onUpdate:modelValue":a[2]||(a[2]=e=>t.guardian=e),error:o(n).guardian,"onUpdate:error":a[3]||(a[3]=e=>o(n).guardian=e),"value-prop":"uuid","search-action":"guardian/list"},{selectedOption:g(e=>[B(U(e.value.name)+" ("+U(e.value.contactNumber)+") ",1)]),listOption:g(e=>[B(U(e.option.name)+" ("+U(e.option.contactNumber)+") ",1)]),_:1},8,["label","modelValue","error"])])):b("",!0),t.type=="new"?(m(),p($,{key:2},[i("div",W,[s(c,{disabled:o(l).params.muuid,type:"text",modelValue:t.name,"onUpdate:modelValue":a[4]||(a[4]=e=>t.name=e),name:"name",label:r.$trans("contact.props.name"),error:o(n).name,"onUpdate:error":a[5]||(a[5]=e=>o(n).name=e),autofocus:""},null,8,["disabled","modelValue","label","error"])]),i("div",X,[s(c,{disabled:o(l).params.muuid,type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":a[6]||(a[6]=e=>t.contactNumber=e),name:"contactNumber",label:r.$trans("contact.props.contact_number"),error:o(n).contactNumber,"onUpdate:error":a[7]||(a[7]=e=>o(n).contactNumber=e)},null,8,["disabled","modelValue","label","error"])])],64)):b("",!0),i("div",Y,[s(G,{modelValue:t.relation,"onUpdate:modelValue":a[8]||(a[8]=e=>t.relation=e),name:"relation",label:r.$trans("contact.props.relation"),options:w.relations,"label-prop":"label","value-prop":"value",error:o(n).relation,"onUpdate:error":a[9]||(a[9]=e=>o(n).relation=e)},null,8,["modelValue","label","options","error"])]),t.type=="new"&&!o(l).params.muuid?(m(),p($,{key:3},[i("div",Z,[s(_,{vertical:"",modelValue:t.createUserAccount,"onUpdate:modelValue":a[10]||(a[10]=e=>t.createUserAccount=e),name:"createUserAccount",label:r.$trans("global.create",{attribute:r.$trans("contact.user_account")}),error:o(n).createUserAccount,"onUpdate:error":a[11]||(a[11]=e=>o(n).createUserAccount=e)},null,8,["modelValue","label","error"])]),t.createUserAccount?(m(),p($,{key:0},[i("div",x,[s(c,{type:"text",modelValue:t.email,"onUpdate:modelValue":a[12]||(a[12]=e=>t.email=e),name:"email",label:r.$trans("contact.login.props.email"),error:o(n).email,"onUpdate:error":a[13]||(a[13]=e=>o(n).email=e)},null,8,["modelValue","label","error"])]),i("div",h,[s(c,{type:"text",modelValue:t.username,"onUpdate:modelValue":a[14]||(a[14]=e=>t.username=e),name:"username",label:r.$trans("contact.login.props.username"),error:o(n).username,"onUpdate:error":a[15]||(a[15]=e=>o(n).username=e)},null,8,["modelValue","label","error"])]),i("div",ee,[s(c,{type:N.hidePassword?"password":"text",modelValue:t.password,"onUpdate:modelValue":a[17]||(a[17]=e=>t.password=e),name:"password",label:r.$trans("contact.login.props.password"),error:o(n).password,"onUpdate:error":a[18]||(a[18]=e=>o(n).password=e)},{"additional-label":g(()=>[i("div",ae,[E(i("i",{class:"fas fa-key cursor-pointer",onClick:F},null,512),[[R,r.$trans("global.generate",{attribute:r.$trans("auth.login.props.password")})]]),t.password?(m(),p("i",{key:0,class:"fas fa-eye cursor-pointer",onClick:a[16]||(a[16]=e=>N.hidePassword=!N.hidePassword)})):b("",!0)])]),_:1},8,["type","modelValue","label","error"])]),i("div",te,[s(c,{type:"password",modelValue:t.passwordConfirmation,"onUpdate:modelValue":a[19]||(a[19]=e=>t.passwordConfirmation=e),name:"passwordConfirmation",label:r.$trans("contact.login.props.password_confirmation"),error:o(n).passwordConfirmation,"onUpdate:error":a[20]||(a[20]=e=>o(n).passwordConfirmation=e)},null,8,["modelValue","label","error"])])],64)):b("",!0)],64)):b("",!0)])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),ne={name:"StudentGuardianAction"},ie=Object.assign(ne,{props:{student:{type:Object,default(){return{}}}},setup(f){const l=A();return(u,S)=>{const n=d("PageHeaderAction"),P=d("PageHeader"),w=d("ParentTransition");return m(),p($,null,[s(P,{title:u.$trans(o(l).meta.trans,{attribute:u.$trans(o(l).meta.label)}),navs:[{label:u.$trans("student.student"),path:"StudentList"},{label:f.student.contact.name,path:{name:"StudentShow",params:{uuid:f.student.uuid}}},{label:u.$trans("guardian.guardian"),path:{name:"StudentGuardian",params:{uuid:f.student.uuid}}}]},{default:g(()=>[s(n,{name:"StudentGuardian",title:u.$trans("guardian.guardian"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(J,{student:f.student},null,8,["student"]),s(w,{appear:"",visibility:!0},{default:g(()=>[s(re)]),_:1})],64)}}});export{ie as default};
