import{u as W,j as O,H as N,l as H,L as R,J as q,r as f,q as F,b as M,o as y,w as i,d as n,e as o,f as s,s as u,t as b,i as J,h as z,a as G,y as I,F as K}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-4 gap-6"},X={class:"col-span-4 sm:col-span-1"},Y={class:"col-span-4 sm:col-span-1"},Z={class:"col-span-4 sm:col-span-1"},_={class:"grid grid-cols-4 gap-6"},tt={class:"col-span-4 sm:col-span-1"},et={class:"col-span-4 sm:col-span-1"},lt={class:"col-span-4 sm:col-span-1"},at={class:"mt-4 grid grid-cols-4 gap-6"},st={class:"col-span-4 sm:col-span-1"},rt={class:"col-span-4 sm:col-span-1"},ot={class:"col-span-4 sm:col-span-1"},nt={class:"col-span-4 sm:col-span-1"},it={class:"grid grid-cols-4 gap-6"},mt={class:"col-span-4 sm:col-span-1"},dt={class:"col-span-4 sm:col-span-1"},pt={class:"col-span-4 sm:col-span-1"},ut={class:"grid grid-cols-4 gap-6"},bt={class:"col-span-4 sm:col-span-1"},ft={class:"col-span-4 sm:col-span-1"},ht={class:"col-span-4 sm:col-span-1"},gt={class:"grid grid-cols-4 gap-6"},At={class:"col-span-4 sm:col-span-1"},Vt={class:"col-span-4 sm:col-span-1"},kt={class:"col-span-4 sm:col-span-1"},Tt={class:"grid grid-cols-4 gap-6"},yt={class:"col-span-4 sm:col-span-1"},$t={class:"col-span-4 sm:col-span-1"},Ut={class:"col-span-4 sm:col-span-1"},vt={class:"grid grid-cols-4 gap-6"},Ft={class:"col-span-4 sm:col-span-1"},Mt={class:"col-span-4 sm:col-span-1"},wt={class:"col-span-4 sm:col-span-1"},St={name:"ExamConfigForm"},xt=Object.assign(St,{props:{exam:{type:Object,default:()=>({})}},emits:["completed"],setup(w,{emit:T}){const $=W(),S=T,x=O("emitter"),U=w,p={examFormFee:"",examFormLastDate:"",examFormLateFee:"",showSno:!1,showPrintDateTime:!1,showWatermark:!1,signatory1:"",signatory2:"",signatory3:"",signatory4:"",firstAttempt:{title:"",subTitle:"",publishMarksheet:!1},secondAttempt:{title:"",subTitle:"",publishMarksheet:!1},thirdAttempt:{title:"",subTitle:"",publishMarksheet:!1},fourthAttempt:{title:"",subTitle:"",publishMarksheet:!1},fifthAttempt:{title:"",subTitle:"",publishMarksheet:!1}},v="exam/",l=N(v),a=H({...p}),P=()=>{S("completed"),x.emit("listItems")};return R(()=>{var r,t,m,V,h,g,k,e,A,D,B,L,c,E,C,j;if(U.exam.uuid){let d=((r=U.exam)==null?void 0:r.configDetail)||{};Object.assign(p,{examFormFee:d.examFormFee.value,examFormLastDate:d.examFormLastDate.value,examFormLateFee:d.examFormLateFee.value,showSno:d.showSno,showPrintDateTime:d.showPrintDateTime,showWatermark:d.showWatermark,signatory1:d.signatory1,signatory2:d.signatory2,signatory3:d.signatory3,signatory4:d.signatory4,firstAttempt:{title:(t=d.firstAttempt)==null?void 0:t.title,subTitle:(m=d.firstAttempt)==null?void 0:m.subTitle,publishMarksheet:((V=d.firstAttempt)==null?void 0:V.publishMarksheet)||!1},secondAttempt:{title:(h=d.secondAttempt)==null?void 0:h.title,subTitle:(g=d.secondAttempt)==null?void 0:g.subTitle,publishMarksheet:((k=d.secondAttempt)==null?void 0:k.publishMarksheet)||!1},thirdAttempt:{title:(e=d.thirdAttempt)==null?void 0:e.title,subTitle:(A=d.thirdAttempt)==null?void 0:A.subTitle,publishMarksheet:((D=d.thirdAttempt)==null?void 0:D.publishMarksheet)||!1},fourthAttempt:{title:(B=d.fourthAttempt)==null?void 0:B.title,subTitle:(L=d.fourthAttempt)==null?void 0:L.subTitle,publishMarksheet:((c=d.fourthAttempt)==null?void 0:c.publishMarksheet)||!1},fifthAttempt:{title:(E=d.fifthAttempt)==null?void 0:E.title,subTitle:(C=d.fifthAttempt)==null?void 0:C.subTitle,publishMarksheet:((j=d.fifthAttempt)==null?void 0:j.publishMarksheet)||!1}}),Object.assign(a,q(p))}}),(r,t)=>{const m=f("BaseInput"),V=f("DatePicker"),h=f("BaseSwitch"),g=f("BaseFieldset"),k=f("FormAction");return w.exam.uuid?(y(),F(k,{key:0,"init-url":v,uuid:s($).params.uuid,"no-data-fetch":"",action:"storeConfig","init-form":p,form:a,"keep-adding":!1,"stay-on":!0,"after-submit":P},{default:i(()=>[n("div",Q,[n("div",X,[o(m,{currency:"",modelValue:a.examFormFee,"onUpdate:modelValue":t[0]||(t[0]=e=>a.examFormFee=e),name:"examFormFee",label:r.$trans("exam.form.props.fee"),error:s(l).examFormFee,"onUpdate:error":t[1]||(t[1]=e=>s(l).examFormFee=e)},null,8,["modelValue","label","error"])]),n("div",Y,[o(V,{modelValue:a.examFormLastDate,"onUpdate:modelValue":t[2]||(t[2]=e=>a.examFormLastDate=e),name:"examFormLastDate",label:r.$trans("exam.form.props.last_date"),"no-clear":"",error:s(l).examFormLastDate,"onUpdate:error":t[3]||(t[3]=e=>s(l).examFormLastDate=e)},null,8,["modelValue","label","error"])]),n("div",Z,[o(m,{currency:"",modelValue:a.examFormLateFee,"onUpdate:modelValue":t[4]||(t[4]=e=>a.examFormLateFee=e),name:"examFormLateFee",label:r.$trans("exam.form.props.late_fee"),error:s(l).examFormLateFee,"onUpdate:error":t[5]||(t[5]=e=>s(l).examFormLateFee=e)},null,8,["modelValue","label","error"])])]),o(g,{class:"mt-4"},{legend:i(()=>[u(b(r.$trans("print.config")),1)]),default:i(()=>[n("div",_,[n("div",tt,[o(h,{vertical:"",modelValue:a.showSno,"onUpdate:modelValue":t[6]||(t[6]=e=>a.showSno=e),name:"showSno",label:r.$trans("global.show",{attribute:r.$trans("general.sno")}),error:s(l).showSno,"onUpdate:error":t[7]||(t[7]=e=>s(l).showSno=e)},null,8,["modelValue","label","error"])]),n("div",et,[o(h,{vertical:"",modelValue:a.showPrintDateTime,"onUpdate:modelValue":t[8]||(t[8]=e=>a.showPrintDateTime=e),name:"showPrintDateTime",label:r.$trans("global.show",{attribute:r.$trans("general.print_date_time")}),error:s(l).showPrintDateTime,"onUpdate:error":t[9]||(t[9]=e=>s(l).showPrintDateTime=e)},null,8,["modelValue","label","error"])]),n("div",lt,[o(h,{vertical:"",modelValue:a.showWatermark,"onUpdate:modelValue":t[10]||(t[10]=e=>a.showWatermark=e),name:"showWatermark",label:r.$trans("global.show",{attribute:r.$trans("print.watermark")}),error:s(l).showWatermark,"onUpdate:error":t[11]||(t[11]=e=>s(l).showWatermark=e)},null,8,["modelValue","label","error"])])]),n("div",at,[n("div",st,[o(m,{type:"text",modelValue:a.signatory1,"onUpdate:modelValue":t[12]||(t[12]=e=>a.signatory1=e),name:"signatory1",label:r.$trans("print.signatory1"),error:s(l).signatory1,"onUpdate:error":t[13]||(t[13]=e=>s(l).signatory1=e)},null,8,["modelValue","label","error"])]),n("div",rt,[o(m,{type:"text",modelValue:a.signatory2,"onUpdate:modelValue":t[14]||(t[14]=e=>a.signatory2=e),name:"signatory2",label:r.$trans("print.signatory2"),error:s(l).signatory2,"onUpdate:error":t[15]||(t[15]=e=>s(l).signatory2=e)},null,8,["modelValue","label","error"])]),n("div",ot,[o(m,{type:"text",modelValue:a.signatory3,"onUpdate:modelValue":t[16]||(t[16]=e=>a.signatory3=e),name:"signatory3",label:r.$trans("print.signatory3"),error:s(l).signatory3,"onUpdate:error":t[17]||(t[17]=e=>s(l).signatory3=e)},null,8,["modelValue","label","error"])]),n("div",nt,[o(m,{type:"text",modelValue:a.signatory4,"onUpdate:modelValue":t[18]||(t[18]=e=>a.signatory4=e),name:"signatory4",label:r.$trans("print.signatory4"),error:s(l).signatory4,"onUpdate:error":t[19]||(t[19]=e=>s(l).signatory4=e)},null,8,["modelValue","label","error"])])])]),_:1}),o(g,{class:"mt-4"},{legend:i(()=>[u(b(r.$trans("exam.schedule.attempt_number",{attribute:r.$trans("exam.schedule.attempts.first")})),1)]),default:i(()=>[n("div",it,[n("div",mt,[o(m,{type:"text",modelValue:a.firstAttempt.title,"onUpdate:modelValue":t[20]||(t[20]=e=>a.firstAttempt.title=e),name:"firstAttemptTitle",label:r.$trans("print.title"),error:s(l).firstAttemptTitle,"onUpdate:error":t[21]||(t[21]=e=>s(l).firstAttemptTitle=e)},null,8,["modelValue","label","error"])]),n("div",dt,[o(m,{type:"text",modelValue:a.firstAttempt.subTitle,"onUpdate:modelValue":t[22]||(t[22]=e=>a.firstAttempt.subTitle=e),name:"firstAttemptSubTitle",label:r.$trans("print.sub_title"),error:s(l).firstAttemptSubTitle,"onUpdate:error":t[23]||(t[23]=e=>s(l).firstAttemptSubTitle=e)},null,8,["modelValue","label","error"])]),n("div",pt,[o(h,{vertical:"",modelValue:a.firstAttempt.publishMarksheet,"onUpdate:modelValue":t[24]||(t[24]=e=>a.firstAttempt.publishMarksheet=e),name:"firstAttemptPublishMarksheet",label:r.$trans("global.publish",{attribute:r.$trans("exam.marksheet.marksheet")}),error:s(l).firstAttemptPublishMarksheet,"onUpdate:error":t[25]||(t[25]=e=>s(l).firstAttemptPublishMarksheet=e)},null,8,["modelValue","label","error"])])])]),_:1}),o(g,{class:"mt-4"},{legend:i(()=>[u(b(r.$trans("exam.schedule.attempt_number",{attribute:r.$trans("exam.schedule.attempts.second")})),1)]),default:i(()=>[n("div",ut,[n("div",bt,[o(m,{type:"text",modelValue:a.secondAttempt.title,"onUpdate:modelValue":t[26]||(t[26]=e=>a.secondAttempt.title=e),name:"secondAttemptTitle",label:r.$trans("print.title"),error:s(l).secondAttemptTitle,"onUpdate:error":t[27]||(t[27]=e=>s(l).secondAttemptTitle=e)},null,8,["modelValue","label","error"])]),n("div",ft,[o(m,{type:"text",modelValue:a.secondAttempt.subTitle,"onUpdate:modelValue":t[28]||(t[28]=e=>a.secondAttempt.subTitle=e),name:"secondAttemptSubTitle",label:r.$trans("print.sub_title"),error:s(l).secondAttemptSubTitle,"onUpdate:error":t[29]||(t[29]=e=>s(l).secondAttemptSubTitle=e)},null,8,["modelValue","label","error"])]),n("div",ht,[o(h,{vertical:"",modelValue:a.secondAttempt.publishMarksheet,"onUpdate:modelValue":t[30]||(t[30]=e=>a.secondAttempt.publishMarksheet=e),name:"secondAttemptPublishMarksheet",label:r.$trans("global.publish",{attribute:r.$trans("exam.marksheet.marksheet")}),error:s(l).secondAttemptPublishMarksheet,"onUpdate:error":t[31]||(t[31]=e=>s(l).secondAttemptPublishMarksheet=e)},null,8,["modelValue","label","error"])])])]),_:1}),o(g,{class:"mt-4"},{legend:i(()=>[u(b(r.$trans("exam.schedule.attempt_number",{attribute:r.$trans("exam.schedule.attempts.third")})),1)]),default:i(()=>[n("div",gt,[n("div",At,[o(m,{type:"text",modelValue:a.thirdAttempt.title,"onUpdate:modelValue":t[32]||(t[32]=e=>a.thirdAttempt.title=e),name:"thirdAttemptTitle",label:r.$trans("print.title"),error:s(l).thirdAttemptTitle,"onUpdate:error":t[33]||(t[33]=e=>s(l).thirdAttemptTitle=e)},null,8,["modelValue","label","error"])]),n("div",Vt,[o(m,{type:"text",modelValue:a.thirdAttempt.subTitle,"onUpdate:modelValue":t[34]||(t[34]=e=>a.thirdAttempt.subTitle=e),name:"thirdAttemptSubTitle",label:r.$trans("print.sub_title"),error:s(l).thirdAttemptSubTitle,"onUpdate:error":t[35]||(t[35]=e=>s(l).thirdAttemptSubTitle=e)},null,8,["modelValue","label","error"])]),n("div",kt,[o(h,{vertical:"",modelValue:a.thirdAttempt.publishMarksheet,"onUpdate:modelValue":t[36]||(t[36]=e=>a.thirdAttempt.publishMarksheet=e),name:"thirdAttemptPublishMarksheet",label:r.$trans("global.publish",{attribute:r.$trans("exam.marksheet.marksheet")}),error:s(l).thirdAttemptPublishMarksheet,"onUpdate:error":t[37]||(t[37]=e=>s(l).thirdAttemptPublishMarksheet=e)},null,8,["modelValue","label","error"])])])]),_:1}),o(g,{class:"mt-4"},{legend:i(()=>[u(b(r.$trans("exam.schedule.attempt_number",{attribute:r.$trans("exam.schedule.attempts.fourth")})),1)]),default:i(()=>[n("div",Tt,[n("div",yt,[o(m,{type:"text",modelValue:a.fourthAttempt.title,"onUpdate:modelValue":t[38]||(t[38]=e=>a.fourthAttempt.title=e),name:"fourthAttemptTitle",label:r.$trans("print.title"),error:s(l).fourthAttemptTitle,"onUpdate:error":t[39]||(t[39]=e=>s(l).fourthAttemptTitle=e)},null,8,["modelValue","label","error"])]),n("div",$t,[o(m,{type:"text",modelValue:a.fourthAttempt.subTitle,"onUpdate:modelValue":t[40]||(t[40]=e=>a.fourthAttempt.subTitle=e),name:"fourthAttemptSubTitle",label:r.$trans("print.sub_title"),error:s(l).fourthAttemptSubTitle,"onUpdate:error":t[41]||(t[41]=e=>s(l).fourthAttemptSubTitle=e)},null,8,["modelValue","label","error"])]),n("div",Ut,[o(h,{vertical:"",modelValue:a.fourthAttempt.publishMarksheet,"onUpdate:modelValue":t[42]||(t[42]=e=>a.fourthAttempt.publishMarksheet=e),name:"fourthAttemptPublishMarksheet",label:r.$trans("global.publish",{attribute:r.$trans("exam.marksheet.marksheet")}),error:s(l).fourthAttemptPublishMarksheet,"onUpdate:error":t[43]||(t[43]=e=>s(l).fourthAttemptPublishMarksheet=e)},null,8,["modelValue","label","error"])])])]),_:1}),o(g,{class:"mt-4"},{legend:i(()=>[u(b(r.$trans("exam.schedule.attempt_number",{attribute:r.$trans("exam.schedule.attempts.fifth")})),1)]),default:i(()=>[n("div",vt,[n("div",Ft,[o(m,{type:"text",modelValue:a.fifthAttempt.title,"onUpdate:modelValue":t[44]||(t[44]=e=>a.fifthAttempt.title=e),name:"fifthAttemptTitle",label:r.$trans("print.title"),error:s(l).fifthAttemptTitle,"onUpdate:error":t[45]||(t[45]=e=>s(l).fifthAttemptTitle=e)},null,8,["modelValue","label","error"])]),n("div",Mt,[o(m,{type:"text",modelValue:a.fifthAttempt.subTitle,"onUpdate:modelValue":t[46]||(t[46]=e=>a.fifthAttempt.subTitle=e),name:"fifthAttemptSubTitle",label:r.$trans("print.sub_title"),error:s(l).fifthAttemptSubTitle,"onUpdate:error":t[47]||(t[47]=e=>s(l).fifthAttemptSubTitle=e)},null,8,["modelValue","label","error"])]),n("div",wt,[o(h,{vertical:"",modelValue:a.fifthAttempt.publishMarksheet,"onUpdate:modelValue":t[48]||(t[48]=e=>a.fifthAttempt.publishMarksheet=e),name:"fifthAttemptPublishMarksheet",label:r.$trans("global.publish",{attribute:r.$trans("exam.marksheet.marksheet")}),error:s(l).fifthAttemptPublishMarksheet,"onUpdate:error":t[49]||(t[49]=e=>s(l).fifthAttemptPublishMarksheet=e)},null,8,["modelValue","label","error"])])])]),_:1})]),_:1},8,["uuid","form"])):M("",!0)}}}),Pt={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Dt={name:"ExamShow"},Lt=Object.assign(Dt,{setup(w){J();const T=W(),$=z(),S=O("emitter"),x={},U="exam/",p=H({...x}),v=l=>{Object.assign(p,l)};return(l,a)=>{const P=f("PageHeaderAction"),r=f("PageHeader"),t=f("TextMuted"),m=f("BaseDataView"),V=f("BaseButton"),h=f("ShowButton"),g=f("BaseCard"),k=f("ShowItem"),e=f("ParentTransition");return y(),G(K,null,[o(r,{title:l.$trans(s(T).meta.trans,{attribute:l.$trans(s(T).meta.label)}),navs:[{label:l.$trans("exam.exam"),path:"Exam"}]},{default:i(()=>[o(P,{name:"Exam",title:l.$trans("exam.exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(e,{appear:"",visibility:!0},{default:i(()=>[o(k,{"init-url":U,uuid:s(T).params.uuid,onSetItem:v,onRedirectTo:a[2]||(a[2]=A=>s($).push({name:"Exam"}))},{default:i(()=>[p.uuid?(y(),F(g,{key:0},{title:i(()=>[u(b(p.name),1)]),footer:i(()=>[o(h,null,{default:i(()=>[s(I)("exam:manage")?(y(),F(V,{key:0,design:"primary",onClick:a[0]||(a[0]=A=>s($).push({name:"ExamEdit",params:{uuid:p.uuid}}))},{default:i(()=>[u(b(l.$trans("general.edit")),1)]),_:1})):M("",!0)]),_:1})]),default:i(()=>[n("dl",Pt,[o(m,{label:l.$trans("exam.props.name")},{default:i(()=>[u(b(p.name)+" ",1),o(t,{block:""},{default:i(()=>[u(b(p.code),1)]),_:1})]),_:1},8,["label"]),o(m,{label:l.$trans("academic.term.term")},{default:i(()=>{var A;return[u(b(((A=p.term)==null?void 0:A.name)||""),1)]}),_:1},8,["label"]),o(m,{label:l.$trans("exam.props.display_name")},{default:i(()=>[u(b(p.displayName||"-"),1)]),_:1},8,["label"]),o(m,{class:"col-span-1 sm:col-span-2",label:l.$trans("exam.props.description")},{default:i(()=>[u(b(p.description),1)]),_:1},8,["label"]),o(m,{label:l.$trans("general.created_at")},{default:i(()=>[u(b(p.createdAt.formatted),1)]),_:1},8,["label"]),o(m,{label:l.$trans("general.updated_at")},{default:i(()=>[u(b(p.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):M("",!0),s(I)("exam:manage")?(y(),F(g,{key:1,"no-padding":"","no-content-padding":""},{title:i(()=>[u(b(l.$trans("exam.config.config")),1)]),default:i(()=>[o(xt,{exam:p,onCompleted:a[1]||(a[1]=A=>s(S).emit("refreshItem"))},null,8,["exam"])]),_:1})):M("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Lt as default};
