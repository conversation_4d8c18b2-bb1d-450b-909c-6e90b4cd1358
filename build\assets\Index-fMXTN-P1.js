import{u as L,i as D,m as T,H as z,l as F,n as J,r as i,q as w,o as s,w as t,d as V,b as y,f as d,s as h,a as x,t as n,h as Y,j as Z,e as b,F as E,v as j,J as H,M as ee}from"./app-BAwPsakn.js";const ae={class:"grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},ne={key:0,class:"ml-1"},oe={key:0,class:"ml-1"},se={class:"col-span-3 sm:col-span-1"},re={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide","cancel"],setup(M,{emit:$}){const c=L();D();const C=$,r=M,g={exam:"",batch:""};T(!1);const o=z(r.initUrl),m=F({...g});F({exams:r.preRequisites.exams});const p=F({exam:"",batch:"",isLoaded:!(c.query.exam&&c.query.batch)}),k=()=>{C("cancel")},U=async f=>{if(!f){m.batch="";return}m.batch=f||""};return J(async()=>{p.exam=c.query.exam,m.exam=c.query.exam,p.batch=c.query.batch,m.batch=c.query.batch,c.query.batch&&await U(c.query.batch),p.isLoaded=!0}),(f,u)=>{const A=i("BaseSelect"),S=i("BaseSelectSearch"),a=i("FilterForm");return s(),w(a,{"init-form":g,form:m,onCancel:k,onHide:u[4]||(u[4]=e=>C("hide"))},{default:t(()=>[V("div",ae,[V("div",te,[p.isLoaded?(s(),w(A,{key:0,modelValue:m.exam,"onUpdate:modelValue":u[0]||(u[0]=e=>m.exam=e),name:"exam",label:f.$trans("exam.exam"),"value-prop":"uuid",options:M.preRequisites.exams,error:d(o).exam,"onUpdate:error":u[1]||(u[1]=e=>d(o).exam=e)},{selectedOption:t(e=>{var v,q;return[h(n(e.value.name)+" ",1),e.value.term?(s(),x("span",ne,"("+n(((q=(v=e.value.term)==null?void 0:v.division)==null?void 0:q.name)||f.$trans("general.all"))+")",1)):y("",!0)]}),listOption:t(e=>{var v,q;return[h(n(e.option.name)+" ",1),e.option.term?(s(),x("span",oe,"("+n(((q=(v=e.option.term)==null?void 0:v.division)==null?void 0:q.name)||f.$trans("general.all"))+")",1)):y("",!0)]}),_:1},8,["modelValue","label","options","error"])):y("",!0)]),V("div",se,[p.isLoaded?(s(),w(S,{key:0,name:"batch",label:f.$trans("global.select",{attribute:f.$trans("academic.batch.batch")}),modelValue:m.batch,"onUpdate:modelValue":u[2]||(u[2]=e=>m.batch=e),error:d(o).batch,"onUpdate:error":u[3]||(u[3]=e=>d(o).batch=e),"value-prop":"uuid","init-search":p.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:U},{selectedOption:t(e=>[h(n(e.value.course.name)+" "+n(e.value.name),1)]),listOption:t(e=>[h(n(e.option.course.nameWithTerm)+" "+n(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):y("",!0)])])]),_:1},8,["form"])}}},le={class:"p-2"},ie={class:"divide-y divide-gray-200 dark:divide-gray-700"},ce={class:"col-span-4 sm:col-span-1"},me={class:"mt-1"},ue={key:0,class:"col-span-4 sm:col-span-3"},de={class:"flex flex-col sm:flex-row gap-2"},pe={key:0,class:"mt-2"},be={name:"ExamObservationMark"},fe=Object.assign(be,{setup(M){const $=L(),c=Y(),C=D();Z("emitter");const r={exam:"",batch:"",students:[]},g="exam/observationMark/",o=T(!1),m=F({exams:[]}),p=z(g),k=F({...r}),U=F({meta:{},showComment:!0}),f=async()=>{o.value=!0,await C.dispatch(g+"preRequisite").then(a=>{o.value=!1,Object.assign(m,a)}).catch(a=>{o.value=!1})},u=()=>{r.exam="",r.batch="",r.students=[],Object.assign(k,H(r))},A=async()=>{$.query.batch&&(o.value=!0,await C.dispatch(g+"fetch",{params:$.query}).then(a=>{o.value=!1,r.exam=$.query.exam,r.batch=$.query.batch,r.students=a.data,U.meta=a.meta,Object.assign(k,H(r))}).catch(a=>{o.value=!1}))},S=async()=>{await ee()&&(o.value=!0,await C.dispatch(g+"remove",{form:$.query}).then(a=>{o.value=!1,A()}).catch(a=>{o.value=!1}))};return J(async()=>{await f(),await A()}),(a,e)=>{const v=i("BaseButton"),q=i("PageHeaderAction"),W=i("PageHeader"),G=i("ParentTransition"),I=i("BaseAlert"),K=i("BaseCheckbox"),P=i("BaseDataView"),R=i("BaseInput"),Q=i("FormAction"),X=i("BaseCard");return s(),x(E,null,[b(W,{title:a.$trans(d($).meta.label),navs:[{label:a.$trans("exam.exam"),path:"Exam"}]},{default:t(()=>[b(q,null,{default:t(()=>[b(v,{design:"white",onClick:e[0]||(e[0]=l=>d(c).push({name:"ExamMark"}))},{default:t(()=>[h(n(a.$trans("exam.mark")),1)]),_:1}),b(v,{design:"white",onClick:e[1]||(e[1]=l=>d(c).push({name:"ExamComment"}))},{default:t(()=>[h(n(a.$trans("exam.comment")),1)]),_:1}),b(v,{design:"white",onClick:e[2]||(e[2]=l=>d(c).push({name:"ExamAttendance"}))},{default:t(()=>[h(n(a.$trans("student.attendance.attendance")),1)]),_:1})]),_:1})]),_:1},8,["title","navs"]),b(G,{appear:"",visibility:!0},{default:t(()=>[b(re,{onAfterFilter:A,onCancel:u,"init-url":g,"pre-requisites":m},null,8,["pre-requisites"])]),_:1}),b(X,{"no-padding":"","no-content-padding":"","is-loading":o.value},{title:t(()=>[h(n(a.$trans("exam.record")),1)]),action:t(()=>[U.meta.observationMarkRecorded?(s(),w(v,{key:0,design:"error",onClick:S},{default:t(()=>[h(n(a.$trans("global.remove",{attribute:a.$trans("exam.observation_mark")})),1)]),_:1})):y("",!0)]),default:t(()=>[V("div",le,[k.students.length==0?(s(),w(I,{key:0,size:"xs",design:"error"},{default:t(()=>[h(n(a.$trans("general.errors.record_not_found")),1)]),_:1})):y("",!0)]),k.students.length?(s(),w(Q,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":g,action:"store","init-form":r,form:k},{default:t(()=>[V("div",ie,[(s(!0),x(E,null,j(k.students,(l,B)=>(s(),x("div",{class:"grid grid-cols-4 gap-6 px-4 py-2",key:l.uuid},[V("div",ce,[b(P,null,{default:t(()=>[h(n(l.name)+" ("+n(l.rollNumber||l.codeNumber)+") ",1),V("div",me,[b(K,{modelValue:l.isNotApplicable,"onUpdate:modelValue":_=>l.isNotApplicable=_,name:`students.${B}.isNotApplicable`,label:a.$trans("global.is_not",{attribute:a.$trans("exam.schedule.props.applicable")})},null,8,["modelValue","onUpdate:modelValue","name","label"])])]),_:2},1024)]),l.isNotApplicable?y("",!0):(s(),x("div",ue,[V("div",de,[(s(!0),x(E,null,j(l.marks,(_,N)=>(s(),w(R,{type:"text",modelValue:_.obtainedMark,"onUpdate:modelValue":O=>_.obtainedMark=O,name:`students.${B}.marks.${N}.obtainedMark`,label:_.name+" ("+_.maxMark+")",error:d(p)[`students.${B}.marks.${N}.obtainedMark`],"onUpdate:error":O=>d(p)[`students.${B}.marks.${N}.obtainedMark`]=O},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"]))),256))]),U.showComment?(s(),x("div",pe,[b(R,{type:"text",modelValue:l.comment,"onUpdate:modelValue":_=>l.comment=_,name:`students.${B}.comment`,placeholder:a.$trans("exam.comment"),error:d(p)[`students.${B}.comment`],"onUpdate:error":_=>d(p)[`students.${B}.comment`]=_},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])):y("",!0)]))]))),128))])]),_:1},8,["form"])):y("",!0)]),_:1},8,["is-loading"])],64)}}});export{fe as default};
