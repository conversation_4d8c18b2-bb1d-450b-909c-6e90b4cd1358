import{i as y,u as k,h as P,l as V,r as n,a as A,o as u,e as a,w as t,f as s,q as m,b as _,d as H,s as o,t as l,y as I,F as N}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"TransportCircleShow"},F=Object.assign(R,{setup(j){y();const p=k(),d=P(),f={},g="transport/circle/",r=V({...f}),b=e=>{Object.assign(r,e)};return(e,i)=>{const B=n("PageHeaderAction"),C=n("PageHeader"),c=n("BaseDataView"),$=n("BaseButton"),T=n("ShowButton"),h=n("BaseCard"),w=n("ShowItem"),S=n("ParentTransition");return u(),A(N,null,[a(C,{title:e.$trans(s(p).meta.trans,{attribute:e.$trans(s(p).meta.label)}),navs:[{label:e.$trans("transport.transport"),path:"Transport"},{label:e.$trans("transport.circle.circle"),path:"TransportCircleList"}]},{default:t(()=>[a(B,{name:"TransportCircle",title:e.$trans("transport.circle.circle"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(S,{appear:"",visibility:!0},{default:t(()=>[a(w,{"init-url":g,uuid:s(p).params.uuid,onSetItem:b,onRedirectTo:i[1]||(i[1]=v=>s(d).push({name:"TransportCircle"}))},{default:t(()=>[r.uuid?(u(),m(h,{key:0},{title:t(()=>[o(l(r.name),1)]),footer:t(()=>[a(T,null,{default:t(()=>[s(I)("transport-circle:edit")?(u(),m($,{key:0,design:"primary",onClick:i[0]||(i[0]=v=>s(d).push({name:"TransportCircleEdit",params:{uuid:r.uuid}}))},{default:t(()=>[o(l(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:t(()=>[H("dl",D,[a(c,{label:e.$trans("transport.circle.props.name")},{default:t(()=>[o(l(r.name),1)]),_:1},8,["label"]),a(c,{class:"col-span-1 sm:col-span-2",label:e.$trans("transport.circle.props.description")},{default:t(()=>[o(l(r.description),1)]),_:1},8,["label"]),a(c,{label:e.$trans("general.created_at")},{default:t(()=>[o(l(r.createdAt.formatted),1)]),_:1},8,["label"]),a(c,{label:e.$trans("general.updated_at")},{default:t(()=>[o(l(r.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
