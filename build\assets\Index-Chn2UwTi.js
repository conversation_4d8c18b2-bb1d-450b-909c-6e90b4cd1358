import{H as w,l as h,r as g,q as T,o as d,w as f,e as n,d as i,a as b,b as p,f as a,s as O,t as v,F as m}from"./app-BAwPsakn.js";const S={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},y={key:0,class:"col-span-3 sm:col-span-1"},G={class:"mt-4 grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},F={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3"},H={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3"},D={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},_={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3"},le={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={name:"ConfigFeature"},ie=Object.assign(ae,{setup(re){const c="config/",t=w(c),C={sessionLifetime:"",loginThrottleMaxAttempts:"",loginThrottleLockTimeout:"",resetPasswordTokenLifetime:"",enableResetPassword:!1,enableRegistration:!1,enableRegistrationTerms:!1,enableEmailVerification:!1,enableAccountApproval:!1,enableOauthLogin:!1,enableGoogleOauthLogin:!1,googleClientId:"",googleClientSecret:"",googleCallbackUrl:"",enableFacebookOauthLogin:!1,facebookClientId:"",facebookClientSecret:"",facebookCallbackUrl:"",enableTwitterOauthLogin:!1,twitterClientId:"",twitterClientSecret:"",twitterCallbackUrl:"",enableGithubOauthLogin:!1,githubClientId:"",githubClientSecret:"",githubCallbackUrl:"",enableMicrosoftOauthLogin:!1,microsoftClientId:"",microsoftClientSecret:"",microsoftCallbackUrl:"",type:"auth"},o=h({...C});return(r,e)=>{const V=g("CardHeader"),s=g("BaseInput"),u=g("BaseSwitch"),k=g("HelperText"),U=g("FormAction"),L=g("ConfigPage");return d(),T(L,null,{default:f(()=>[n(U,{"no-card":"","init-url":c,"data-fetch":"auth","init-form":C,form:o,action:"store","stay-on":"",redirect:"Config"},{default:f(()=>[n(V,{first:"",title:r.$trans("config.auth.auth_config"),description:r.$trans("config.auth.auth_info")},null,8,["title","description"]),i("div",S,[i("div",I,[n(s,{type:"text",modelValue:o.loginThrottleMaxAttempts,"onUpdate:modelValue":e[0]||(e[0]=l=>o.loginThrottleMaxAttempts=l),name:"loginThrottleMaxAttempts",label:r.$trans("config.auth.props.login_throttle_max_attempts"),error:a(t).loginThrottleMaxAttempts,"onUpdate:error":e[1]||(e[1]=l=>a(t).loginThrottleMaxAttempts=l)},null,8,["modelValue","label","error"])]),i("div",$,[n(s,{type:"text",modelValue:o.loginThrottleLockTimeout,"onUpdate:modelValue":e[2]||(e[2]=l=>o.loginThrottleLockTimeout=l),name:"loginThrottleLockTimeout",label:r.$trans("config.auth.props.login_throttle_lock_timeout"),error:a(t).loginThrottleLockTimeout,"onUpdate:error":e[3]||(e[3]=l=>a(t).loginThrottleLockTimeout=l)},null,8,["modelValue","label","error"])]),o.enableResetPassword?(d(),b("div",y,[n(s,{type:"text",modelValue:o.resetPasswordTokenLifetime,"onUpdate:modelValue":e[4]||(e[4]=l=>o.resetPasswordTokenLifetime=l),name:"resetPasswordTokenLifetime",label:r.$trans("config.auth.props.reset_password_token_lifetime"),error:a(t).resetPasswordTokenLifetime,"onUpdate:error":e[5]||(e[5]=l=>a(t).resetPasswordTokenLifetime=l)},null,8,["modelValue","label","error"])])):p("",!0)]),i("div",G,[i("div",A,[n(u,{modelValue:o.enableResetPassword,"onUpdate:modelValue":e[6]||(e[6]=l=>o.enableResetPassword=l),name:"enableResetPassword",label:r.$trans("config.auth.props.enable_reset_password"),error:a(t).enableResetPassword,"onUpdate:error":e[7]||(e[7]=l=>a(t).enableResetPassword=l)},null,8,["modelValue","label","error"])]),i("div",R,[n(u,{modelValue:o.enableRegistration,"onUpdate:modelValue":e[8]||(e[8]=l=>o.enableRegistration=l),name:"enableRegistration",label:r.$trans("config.auth.props.enable_registration"),error:a(t).enableRegistration,"onUpdate:error":e[9]||(e[9]=l=>a(t).enableRegistration=l)},null,8,["modelValue","label","error"]),n(k,null,{default:f(()=>[O(v(r.$trans("config.auth.registration_tip")),1)]),_:1})]),o.enableRegistration?(d(),b(m,{key:0},[i("div",F,[n(u,{modelValue:o.enableRegistrationTerms,"onUpdate:modelValue":e[10]||(e[10]=l=>o.enableRegistrationTerms=l),name:"enableRegistrationTerms",label:r.$trans("config.auth.props.enable_registration_terms"),error:a(t).enableRegistrationTerms,"onUpdate:error":e[11]||(e[11]=l=>a(t).enableRegistrationTerms=l)},null,8,["modelValue","label","error"])]),i("div",M,[n(u,{modelValue:o.enableEmailVerification,"onUpdate:modelValue":e[12]||(e[12]=l=>o.enableEmailVerification=l),name:"enableEmailVerification",label:r.$trans("config.auth.props.enable_email_verification"),error:a(t).enableEmailVerification,"onUpdate:error":e[13]||(e[13]=l=>a(t).enableEmailVerification=l)},null,8,["modelValue","label","error"])]),i("div",P,[n(u,{modelValue:o.enableAccountApproval,"onUpdate:modelValue":e[14]||(e[14]=l=>o.enableAccountApproval=l),name:"enableAccountApproval",label:r.$trans("config.auth.props.enable_account_approval"),error:a(t).enableAccountApproval,"onUpdate:error":e[15]||(e[15]=l=>a(t).enableAccountApproval=l)},null,8,["modelValue","label","error"])])],64)):p("",!0),i("div",E,[n(u,{modelValue:o.enableOauthLogin,"onUpdate:modelValue":e[16]||(e[16]=l=>o.enableOauthLogin=l),name:"enableOauthLogin",label:r.$trans("config.auth.props.enable_oauth_login"),error:a(t).enableOauthLogin,"onUpdate:error":e[17]||(e[17]=l=>a(t).enableOauthLogin=l)},null,8,["modelValue","label","error"])]),o.enableOauthLogin?(d(),b(m,{key:1},[i("div",B,[n(u,{modelValue:o.enableGoogleOauthLogin,"onUpdate:modelValue":e[18]||(e[18]=l=>o.enableGoogleOauthLogin=l),name:"enableGoogleOauthLogin",label:r.$trans("config.auth.props.enable_oauth",{attribute:"Google"}),error:a(t).enableGoogleOauthLogin,"onUpdate:error":e[19]||(e[19]=l=>a(t).enableGoogleOauthLogin=l)},null,8,["modelValue","label","error"])]),o.enableGoogleOauthLogin?(d(),b(m,{key:0},[i("div",H,[n(s,{type:"text",modelValue:o.googleClientId,"onUpdate:modelValue":e[20]||(e[20]=l=>o.googleClientId=l),name:"googleClientId",label:r.$trans("config.auth.props.client_id",{attribute:"Google"}),error:a(t).googleClientId,"onUpdate:error":e[21]||(e[21]=l=>a(t).googleClientId=l)},null,8,["modelValue","label","error"])]),i("div",N,[n(s,{type:"text",modelValue:o.googleClientSecret,"onUpdate:modelValue":e[22]||(e[22]=l=>o.googleClientSecret=l),name:"googleClientSecret",label:r.$trans("config.auth.props.client_secret",{attribute:"Google"}),error:a(t).googleClientSecret,"onUpdate:error":e[23]||(e[23]=l=>a(t).googleClientSecret=l)},null,8,["modelValue","label","error"])]),i("div",j,[n(s,{type:"text",modelValue:o.googleCallbackUrl,"onUpdate:modelValue":e[24]||(e[24]=l=>o.googleCallbackUrl=l),name:"googleCallbackUrl",label:r.$trans("config.auth.props.callback_url",{attribute:"Google"}),error:a(t).googleCallbackUrl,"onUpdate:error":e[25]||(e[25]=l=>a(t).googleCallbackUrl=l)},null,8,["modelValue","label","error"])])],64)):p("",!0),i("div",q,[n(u,{modelValue:o.enableFacebookOauthLogin,"onUpdate:modelValue":e[26]||(e[26]=l=>o.enableFacebookOauthLogin=l),name:"enableFacebookOauthLogin",label:r.$trans("config.auth.props.enable_oauth",{attribute:"Facebook"}),error:a(t).enableFacebookOauthLogin,"onUpdate:error":e[27]||(e[27]=l=>a(t).enableFacebookOauthLogin=l)},null,8,["modelValue","label","error"])]),o.enableFacebookOauthLogin?(d(),b(m,{key:1},[i("div",D,[n(s,{type:"text",modelValue:o.facebookClientId,"onUpdate:modelValue":e[28]||(e[28]=l=>o.facebookClientId=l),name:"facebookClientId",label:r.$trans("config.auth.props.client_id",{attribute:"Facebook"}),error:a(t).facebookClientId,"onUpdate:error":e[29]||(e[29]=l=>a(t).facebookClientId=l)},null,8,["modelValue","label","error"])]),i("div",z,[n(s,{type:"text",modelValue:o.facebookClientSecret,"onUpdate:modelValue":e[30]||(e[30]=l=>o.facebookClientSecret=l),name:"facebookClientSecret",label:r.$trans("config.auth.props.client_secret",{attribute:"Facebook"}),error:a(t).facebookClientSecret,"onUpdate:error":e[31]||(e[31]=l=>a(t).facebookClientSecret=l)},null,8,["modelValue","label","error"])]),i("div",J,[n(s,{type:"text",modelValue:o.facebookCallbackUrl,"onUpdate:modelValue":e[32]||(e[32]=l=>o.facebookCallbackUrl=l),name:"facebookCallbackUrl",label:r.$trans("config.auth.props.callback_url",{attribute:"Facebook"}),error:a(t).facebookCallbackUrl,"onUpdate:error":e[33]||(e[33]=l=>a(t).facebookCallbackUrl=l)},null,8,["modelValue","label","error"])])],64)):p("",!0),i("div",K,[n(u,{modelValue:o.enableTwitterOauthLogin,"onUpdate:modelValue":e[34]||(e[34]=l=>o.enableTwitterOauthLogin=l),name:"enableTwitterOauthLogin",label:r.$trans("config.auth.props.enable_oauth",{attribute:"Twitter"}),error:a(t).enableTwitterOauthLogin,"onUpdate:error":e[35]||(e[35]=l=>a(t).enableTwitterOauthLogin=l)},null,8,["modelValue","label","error"])]),o.enableTwitterOauthLogin?(d(),b(m,{key:2},[i("div",Q,[n(s,{type:"text",modelValue:o.twitterClientId,"onUpdate:modelValue":e[36]||(e[36]=l=>o.twitterClientId=l),name:"twitterClientId",label:r.$trans("config.auth.props.client_id",{attribute:"Twitter"}),error:a(t).twitterClientId,"onUpdate:error":e[37]||(e[37]=l=>a(t).twitterClientId=l)},null,8,["modelValue","label","error"])]),i("div",W,[n(s,{type:"text",modelValue:o.twitterClientSecret,"onUpdate:modelValue":e[38]||(e[38]=l=>o.twitterClientSecret=l),name:"twitterClientSecret",label:r.$trans("config.auth.props.client_secret",{attribute:"Twitter"}),error:a(t).twitterClientSecret,"onUpdate:error":e[39]||(e[39]=l=>a(t).twitterClientSecret=l)},null,8,["modelValue","label","error"])]),i("div",X,[n(s,{type:"text",modelValue:o.twitterCallbackUrl,"onUpdate:modelValue":e[40]||(e[40]=l=>o.twitterCallbackUrl=l),name:"twitterCallbackUrl",label:r.$trans("config.auth.props.callback_url",{attribute:"Twitter"}),error:a(t).twitterCallbackUrl,"onUpdate:error":e[41]||(e[41]=l=>a(t).twitterCallbackUrl=l)},null,8,["modelValue","label","error"])])],64)):p("",!0),i("div",Y,[n(u,{modelValue:o.enableGithubOauthLogin,"onUpdate:modelValue":e[42]||(e[42]=l=>o.enableGithubOauthLogin=l),name:"enableGithubOauthLogin",label:r.$trans("config.auth.props.enable_oauth",{attribute:"Github"}),error:a(t).enableGithubOauthLogin,"onUpdate:error":e[43]||(e[43]=l=>a(t).enableGithubOauthLogin=l)},null,8,["modelValue","label","error"])]),o.enableGithubOauthLogin?(d(),b(m,{key:3},[i("div",Z,[n(s,{type:"text",modelValue:o.githubClientId,"onUpdate:modelValue":e[44]||(e[44]=l=>o.githubClientId=l),name:"githubClientId",label:r.$trans("config.auth.props.client_id",{attribute:"Github"}),error:a(t).githubClientId,"onUpdate:error":e[45]||(e[45]=l=>a(t).githubClientId=l)},null,8,["modelValue","label","error"])]),i("div",x,[n(s,{type:"text",modelValue:o.githubClientSecret,"onUpdate:modelValue":e[46]||(e[46]=l=>o.githubClientSecret=l),name:"githubClientSecret",label:r.$trans("config.auth.props.client_secret",{attribute:"Github"}),error:a(t).githubClientSecret,"onUpdate:error":e[47]||(e[47]=l=>a(t).githubClientSecret=l)},null,8,["modelValue","label","error"])]),i("div",_,[n(s,{type:"text",modelValue:o.githubCallbackUrl,"onUpdate:modelValue":e[48]||(e[48]=l=>o.githubCallbackUrl=l),name:"githubCallbackUrl",label:r.$trans("config.auth.props.callback_url",{attribute:"Github"}),error:a(t).githubCallbackUrl,"onUpdate:error":e[49]||(e[49]=l=>a(t).githubCallbackUrl=l)},null,8,["modelValue","label","error"])])],64)):p("",!0),i("div",ee,[n(u,{modelValue:o.enableMicrosoftOauthLogin,"onUpdate:modelValue":e[50]||(e[50]=l=>o.enableMicrosoftOauthLogin=l),name:"enableMicrosoftOauthLogin",label:r.$trans("config.auth.props.enable_oauth",{attribute:"Microsoft"}),error:a(t).enableMicrosoftOauthLogin,"onUpdate:error":e[51]||(e[51]=l=>a(t).enableMicrosoftOauthLogin=l)},null,8,["modelValue","label","error"])]),o.enableMicrosoftOauthLogin?(d(),b(m,{key:4},[i("div",le,[n(s,{type:"text",modelValue:o.microsoftClientId,"onUpdate:modelValue":e[52]||(e[52]=l=>o.microsoftClientId=l),name:"microsoftClientId",label:r.$trans("config.auth.props.client_id",{attribute:"Microsoft"}),error:a(t).microsoftClientId,"onUpdate:error":e[53]||(e[53]=l=>a(t).microsoftClientId=l)},null,8,["modelValue","label","error"])]),i("div",oe,[n(s,{type:"text",modelValue:o.microsoftClientSecret,"onUpdate:modelValue":e[54]||(e[54]=l=>o.microsoftClientSecret=l),name:"microsoftClientSecret",label:r.$trans("config.auth.props.client_secret",{attribute:"Microsoft"}),error:a(t).microsoftClientSecret,"onUpdate:error":e[55]||(e[55]=l=>a(t).microsoftClientSecret=l)},null,8,["modelValue","label","error"])]),i("div",te,[n(s,{type:"text",modelValue:o.microsoftCallbackUrl,"onUpdate:modelValue":e[56]||(e[56]=l=>o.microsoftCallbackUrl=l),name:"microsoftCallbackUrl",label:r.$trans("config.auth.props.callback_url",{attribute:"Microsoft"}),error:a(t).microsoftCallbackUrl,"onUpdate:error":e[57]||(e[57]=l=>a(t).microsoftCallbackUrl=l)},null,8,["modelValue","label","error"])])],64)):p("",!0)],64)):p("",!0)])]),_:1},8,["form"])]),_:1})}}});export{ie as default};
