import{H as x,l as g,r as m,z as U,q as S,o as V,w as p,d as i,e as n,f as a,s as f,t as _,A as F,J as N,u as O,a as T,F as q}from"./app-BAwPsakn.js";const y={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},k={class:"fas fa-question-circle"},E={class:"col-span-3"},z={name:"AcademicBatchForm"},C=Object.assign(z,{setup(v){const c={name:"",course:"",maxStrength:"",rollNumberPrefix:"",position:"",pgAccount:"",description:""},l="academic/batch/",r=x(l),d=g({courses:[]}),o=g({...c}),b=s=>{Object.assign(d,s)},A=s=>{Object.assign(c,{...s,course:s.course.uuid}),Object.assign(o,N(c))};return(s,t)=>{const u=m("BaseInput"),B=m("BaseSelect"),h=m("BaseTextarea"),P=m("FormAction"),$=U("tooltip");return V(),S(P,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:b,"init-url":l,"init-form":c,form:o,setForm:A,redirect:"AcademicBatch"},{default:p(()=>[i("div",y,[i("div",j,[n(u,{type:"text",modelValue:o.name,"onUpdate:modelValue":t[0]||(t[0]=e=>o.name=e),name:"name",label:s.$trans("academic.batch.props.name"),error:a(r).name,"onUpdate:error":t[1]||(t[1]=e=>a(r).name=e),autofocus:""},null,8,["modelValue","label","error"])]),i("div",H,[n(B,{modelValue:o.course,"onUpdate:modelValue":t[2]||(t[2]=e=>o.course=e),name:"course",label:s.$trans("academic.course.course"),"value-prop":"uuid",options:d.courses,error:a(r).course,"onUpdate:error":t[3]||(t[3]=e=>a(r).course=e)},{selectedOption:p(e=>[f(_(e.value.nameWithTerm),1)]),listOption:p(e=>[f(_(e.option.nameWithTerm),1)]),_:1},8,["modelValue","label","options","error"])]),i("div",w,[n(u,{type:"text",modelValue:o.maxStrength,"onUpdate:modelValue":t[4]||(t[4]=e=>o.maxStrength=e),name:"maxStrength",label:s.$trans("academic.batch.props.max_strength"),error:a(r).maxStrength,"onUpdate:error":t[5]||(t[5]=e=>a(r).maxStrength=e)},null,8,["modelValue","label","error"])]),i("div",D,[n(u,{type:"text",modelValue:o.rollNumberPrefix,"onUpdate:modelValue":t[6]||(t[6]=e=>o.rollNumberPrefix=e),name:"rollNumberPrefix",label:s.$trans("academic.batch.props.roll_number_prefix"),error:a(r).rollNumberPrefix,"onUpdate:error":t[7]||(t[7]=e=>a(r).rollNumberPrefix=e)},null,8,["modelValue","label","error"])]),i("div",R,[n(u,{type:"text",modelValue:o.pgAccount,"onUpdate:modelValue":t[8]||(t[8]=e=>o.pgAccount=e),name:"pgAccount",label:s.$trans("finance.config.props.pg_account"),error:a(r).pgAccount,"onUpdate:error":t[9]||(t[9]=e=>a(r).pgAccount=e)},{"additional-label":p(()=>[F(i("i",k,null,512),[[$,s.$trans("finance.config.props.pg_account_info")]])]),_:1},8,["modelValue","label","error"])]),i("div",E,[n(h,{modelValue:o.description,"onUpdate:modelValue":t[10]||(t[10]=e=>o.description=e),name:"description",label:s.$trans("academic.batch.props.description"),error:a(r).description,"onUpdate:error":t[11]||(t[11]=e=>a(r).description=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),I={name:"AcademicBatchAction"},J=Object.assign(I,{setup(v){const c=O();return(l,r)=>{const d=m("PageHeaderAction"),o=m("PageHeader"),b=m("ParentTransition");return V(),T(q,null,[n(o,{title:l.$trans(a(c).meta.trans,{attribute:l.$trans(a(c).meta.label)}),navs:[{label:l.$trans("academic.academic"),path:"Academic"},{label:l.$trans("academic.batch.batch"),path:"AcademicBatchList"}]},{default:p(()=>[n(d,{name:"AcademicBatch",title:l.$trans("academic.batch.batch"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(b,{appear:"",visibility:!0},{default:p(()=>[n(C)]),_:1})],64)}}});export{J as default};
