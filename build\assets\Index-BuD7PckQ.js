import{l as T,r as s,q as x,o as d,w as e,d as w,e as a,u as P,i as S,H as U,m as A,n as O,a as y,b as R,f as u,s as l,t as r,h as q,j as z,z as G,F as H,v as J,A as K}from"./app-BAwPsakn.js";import{d as Q}from"./vuedraggable.umd-BRYqknf6.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={__name:"Filter",emits:["hide"],setup(D,{emit:c}){const p=c,f={name:""},g=T({...f});return(B,o)=>{const m=s("BaseInput"),_=s("FilterForm");return d(),x(_,{"init-form":f,form:g,onHide:o[1]||(o[1]=b=>p("hide"))},{default:e(()=>[w("div",W,[w("div",X,[a(m,{type:"text",modelValue:g.name,"onUpdate:modelValue":o[0]||(o[0]=b=>g.name=b),name:"name",label:B.$trans("exam.term.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Z={key:0},ee={class:"flex border rounded-xl px-4 py-2"},te={key:0,class:"ml-1"},ae={key:1},ne={key:2,class:"mt-4 flex justify-end"},se={name:"ExamTermReorder"},oe=Object.assign(se,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(D,{emit:c}){P();const p=S(),f=c,g={terms:[]};U("exam/term/");const o=A(!1),m=T({terms:[]});T({...g});const _=async()=>{o.value=!0,await p.dispatch("exam/term/list",{params:{all:!0}}).then(t=>{o.value=!1,m.terms=t}).catch(t=>{o.value=!1})},b=async()=>{o.value=!0,await p.dispatch("exam/term/reorder",{data:{terms:m.terms}}).then(t=>{o.value=!1,f("refresh"),f("close")}).catch(t=>{o.value=!1})},i=()=>{f("close")};return O(()=>{_()}),(t,h)=>{const E=s("BaseLabel"),I=s("BaseAlert"),C=s("BaseButton"),M=s("BaseModal");return d(),x(M,{show:D.visibility,onClose:i},{title:e(()=>[l(r(t.$trans("global.reorder",{attribute:t.$trans("exam.term.term")})),1)]),default:e(()=>[m.terms.length?(d(),y("div",Z,[a(u(Q),{class:"space-y-2",list:m.terms,"item-key":"uuid"},{item:e(({element:v,index:k})=>[w("div",ee,[h[0]||(h[0]=w("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),a(E,null,{default:e(()=>{var F;return[l(r(v.name)+" ",1),v.division?(d(),y("span",te,"("+r((F=v.division)==null?void 0:F.name)+")",1)):R("",!0)]}),_:2},1024)])]),_:1},8,["list"])])):(d(),y("div",ae,[a(I,{design:"info",size:"xs"},{default:e(()=>[l(r(t.$trans("general.errors.record_not_found")),1)]),_:1})])),m.terms.length?(d(),y("div",ne,[a(C,{onClick:b},{default:e(()=>[l(r(t.$trans("general.reorder")),1)]),_:1})])):R("",!0)]),_:1},8,["show"])}}}),ie={name:"ExamTermList"},me=Object.assign(ie,{setup(D){const c=q(),p=z("emitter");let f=["create","filter"],g=["print","pdf","excel"];const B="exam/term/",o=A(!1),m=A(!1),_=T({}),b=i=>{Object.assign(_,i)};return(i,t)=>{const h=s("BaseButton"),E=s("PageHeaderAction"),I=s("PageHeader"),C=s("ParentTransition"),M=s("TextMuted"),v=s("DataCell"),k=s("FloatingMenuItem"),F=s("FloatingMenu"),L=s("DataRow"),V=s("DataTable"),j=s("ListItem"),N=G("tooltip");return d(),y(H,null,[a(j,{"init-url":B,"additional-query":{details:!0},onSetItems:b},{header:e(()=>[a(I,{title:i.$trans("exam.term.term"),navs:[{label:i.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[a(E,{url:"exam/terms/",name:"ExamTerm",title:i.$trans("exam.term.term"),actions:u(f),"dropdown-actions":u(g),onToggleFilter:t[1]||(t[1]=n=>o.value=!o.value)},{default:e(()=>[K((d(),x(h,{design:"white",onClick:t[0]||(t[0]=n=>m.value=!m.value)},{default:e(()=>t[8]||(t[8]=[w("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[N,i.$trans("global.reorder",{attribute:i.$trans("exam.term.term")})]])]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(C,{appear:"",visibility:o.value},{default:e(()=>[a(Y,{onRefresh:t[2]||(t[2]=n=>u(p).emit("listItems")),onHide:t[3]||(t[3]=n=>o.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(C,{appear:"",visibility:!0},{default:e(()=>[a(V,{header:_.headers,meta:_.meta,module:"exam.term",onRefresh:t[5]||(t[5]=n=>u(p).emit("listItems"))},{actionButton:e(()=>[a(h,{onClick:t[4]||(t[4]=n=>u(c).push({name:"ExamTermCreate"}))},{default:e(()=>[l(r(i.$trans("global.add",{attribute:i.$trans("exam.term.term")})),1)]),_:1})]),default:e(()=>[(d(!0),y(H,null,J(_.data,n=>(d(),x(L,{key:n.uuid,onDoubleClick:$=>u(c).push({name:"ExamTermShow",params:{uuid:n.uuid}})},{default:e(()=>[a(v,{name:"name"},{default:e(()=>[l(r(n.name)+" ",1),n.displayName?(d(),x(M,{key:0,block:""},{default:e(()=>[l(r(n.displayName),1)]),_:2},1024)):R("",!0)]),_:2},1024),a(v,{name:"division"},{default:e(()=>{var $;return[l(r((($=n.division)==null?void 0:$.name)||"-"),1)]}),_:2},1024),a(v,{name:"createdAt"},{default:e(()=>[l(r(n.createdAt.formatted),1)]),_:2},1024),a(v,{name:"action"},{default:e(()=>[a(F,null,{default:e(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:$=>u(c).push({name:"ExamTermShow",params:{uuid:n.uuid}})},{default:e(()=>[l(r(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-edit",onClick:$=>u(c).push({name:"ExamTermEdit",params:{uuid:n.uuid}})},{default:e(()=>[l(r(i.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-copy",onClick:$=>u(c).push({name:"ExamTermDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[l(r(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-trash",onClick:$=>u(p).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[l(r(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),a(oe,{visibility:m.value,onClose:t[6]||(t[6]=n=>m.value=!1),onRefresh:t[7]||(t[7]=n=>u(p).emit("listItems"))},null,8,["visibility"])],64)}}});export{me as default};
