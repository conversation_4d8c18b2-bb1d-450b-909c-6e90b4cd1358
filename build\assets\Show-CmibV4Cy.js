import{i as v,u as y,h as C,l as N,r as l,a as P,o as m,e as o,w as t,f as r,q as p,b as _,d as T,s as n,t as s,y as V,F as I}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"HostelRoomAllocationShow"},M=Object.assign(j,{setup(E){v();const d=y(),c=C(),f={},b="hostel/roomAllocation/",a=N({...f}),g=e=>{Object.assign(a,e)};return(e,u)=>{const h=l("PageHeaderAction"),B=l("PageHeader"),$=l("TextMuted"),i=l("BaseDataView"),A=l("BaseButton"),H=l("ShowButton"),k=l("BaseCard"),w=l("ShowItem"),R=l("ParentTransition");return m(),P(I,null,[o(B,{title:e.$trans(r(d).meta.trans,{attribute:e.$trans(r(d).meta.label)}),navs:[{label:e.$trans("hostel.hostel"),path:"Hostel"},{label:e.$trans("hostel.room_allocation.room_allocation"),path:"HostelRoomAllocationList"}]},{default:t(()=>[o(h,{name:"HostelRoomAllocation",title:e.$trans("hostel.room_allocation.room_allocation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(R,{appear:"",visibility:!0},{default:t(()=>[o(w,{"init-url":b,uuid:r(d).params.uuid,onSetItem:g,onRedirectTo:u[1]||(u[1]=S=>r(c).push({name:"HostelRoomAllocation"}))},{default:t(()=>[a.uuid?(m(),p(k,{key:0},{title:t(()=>[n(s(a.room.fullName),1)]),footer:t(()=>[o(H,null,{default:t(()=>[r(V)("hostel-room-allocation:edit")?(m(),p(A,{key:0,design:"primary",onClick:u[0]||(u[0]=S=>r(c).push({name:"HostelRoomAllocationEdit",params:{uuid:a.uuid}}))},{default:t(()=>[n(s(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:t(()=>[T("dl",D,[o(i,{label:e.$trans("student.student")},{default:t(()=>[n(s(a.student.name)+" ",1),o($,{block:""},{default:t(()=>[n(s(a.student.codeNumber),1)]),_:1})]),_:1},8,["label"]),o(i,{label:e.$trans("hostel.room_allocation.props.period")},{default:t(()=>[n(s(a.period),1)]),_:1},8,["label"]),o(i,{class:"col-span-1 sm:col-span-2",label:e.$trans("hostel.room_allocation.props.remarks")},{default:t(()=>[n(s(a.remarks),1)]),_:1},8,["label"]),o(i,{label:e.$trans("general.created_at")},{default:t(()=>[n(s(a.createdAt.formatted),1)]),_:1},8,["label"]),o(i,{label:e.$trans("general.updated_at")},{default:t(()=>[n(s(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
