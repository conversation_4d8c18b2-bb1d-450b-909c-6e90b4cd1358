import{i as N,u as R,h as j,j as $,l as F,r as s,a as b,o as d,e as l,w as e,f as t,q as u,b as _,d as O,F as L,v as q,s as n,t as i,y as U}from"./app-BAwPsakn.js";const z={class:"space-y-4"},G={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},J={name:"EmployeeLeaveAllocationShow"},Q=Object.assign(J,{setup(K){N();const y=R(),f=j(),a=$("$trans"),B={},k="employee/leave/allocation/",w=[{key:"type",label:a("employee.leave.type.type"),visibility:!0},{key:"allotted",label:a("employee.leave.allocation.props.allotted"),visibility:!0},{key:"used",label:a("employee.leave.allocation.props.used"),visibility:!0},{key:"action",label:"",visibility:!0}],o=F({...B}),h=v=>{Object.assign(o,v)};return(v,c)=>{const V=s("PageHeaderAction"),A=s("PageHeader"),p=s("ListItemView"),D=s("ListContainerVertical"),g=s("BaseCard"),m=s("DataCell"),S=s("DataRow"),C=s("SimpleTable"),E=s("BaseDataView"),T=s("BaseButton"),x=s("ShowButton"),I=s("DetailLayoutVertical"),P=s("ShowItem"),H=s("ParentTransition");return d(),b(L,null,[l(A,{title:t(a)(t(y).meta.trans,{attribute:t(a)(t(y).meta.label)}),navs:[{label:t(a)("employee.leave.leave"),path:"EmployeeLeave"},{label:t(a)("employee.leave.allocation.allocation"),path:"EmployeeLeaveAllocationList"}]},{default:e(()=>[l(V,{name:"EmployeeLeaveAllocation",title:t(a)("employee.leave.allocation.allocation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(H,{appear:"",visibility:!0},{default:e(()=>[l(P,{"init-url":k,uuid:t(y).params.uuid,onSetItem:h,onRedirectTo:c[1]||(c[1]=r=>t(f).push({name:"EmployeeLeaveAllocation"}))},{default:e(()=>[o.uuid?(d(),u(I,{key:0},{detail:e(()=>[l(g,{"no-padding":"","no-content-padding":""},{title:e(()=>[n(i(t(a)("global.detail",{attribute:t(a)("employee.leave.allocation.allocation")})),1)]),default:e(()=>[l(D,null,{default:e(()=>[l(p,{label:t(a)("employee.props.name")},{default:e(()=>[n(i(o.employee.name),1)]),_:1},8,["label"]),l(p,{label:t(a)("employee.props.code_number")},{default:e(()=>[n(i(o.employee.codeNumber),1)]),_:1},8,["label"]),l(p,{label:t(a)("employee.company.department.department")},{default:e(()=>[n(i(o.employee.department),1)]),_:1},8,["label"]),l(p,{label:t(a)("employee.designation.designation")},{default:e(()=>[n(i(o.employee.designation),1)]),_:1},8,["label"]),l(p,{label:t(a)("employee.employment_status.employment_status")},{default:e(()=>[n(i(o.employee.employmentStatus),1)]),_:1},8,["label"]),l(p,{label:t(a)("employee.leave.allocation.props.start_date")},{default:e(()=>[n(i(o.startDate.formatted),1)]),_:1},8,["label"]),l(p,{label:t(a)("employee.leave.allocation.props.end_date")},{default:e(()=>[n(i(o.endDate.formatted),1)]),_:1},8,["label"]),l(p,{label:t(a)("general.created_at")},{default:e(()=>[n(i(o.createdAt.formatted),1)]),_:1},8,["label"]),l(p,{label:t(a)("general.updated_at")},{default:e(()=>[n(i(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[O("div",z,[l(g,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[n(i(t(a)("employee.leave.type.type")),1)]),footer:e(()=>[l(x,null,{default:e(()=>[t(U)("leave-allocation:edit")?(d(),u(T,{key:0,design:"primary",onClick:c[0]||(c[0]=r=>t(f).push({name:"EmployeeLeaveAllocationEdit",params:{uuid:o.uuid}}))},{default:e(()=>[n(i(t(a)("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[o.records.length>0?(d(),u(C,{key:0,header:w},{default:e(()=>[(d(!0),b(L,null,q(o.records,r=>(d(),u(S,{key:r.uuid},{default:e(()=>[l(m,{name:"type"},{default:e(()=>[n(i(r.leaveType.name),1)]),_:2},1024),l(m,{name:"allotted"},{default:e(()=>[n(i(r.allotted),1)]),_:2},1024),l(m,{name:"used"},{default:e(()=>[n(i(r.used),1)]),_:2},1024),l(m,{name:"action"})]),_:2},1024))),128))]),_:1})):_("",!0),o.description?(d(),b("dl",G,[l(E,{class:"col-span-1 sm:col-span-2",label:t(a)("employee.leave.allocation.props.description")},{default:e(()=>[n(i(o.description),1)]),_:1},8,["label"])])):_("",!0)]),_:1})])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Q as default};
