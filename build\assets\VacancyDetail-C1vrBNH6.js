import{u as L,h as V,i as C,g as u,m as T,l as j,n as M,r as y,a as r,o as c,d as t,e as m,f as l,s as d,t as s,w as p,b as _,F as g,v as N}from"./app-BAwPsakn.js";const D={class:"flex justify-between"},H={class:"flex justify-center"},J={href:"/",class:"mb-6"},A=["src"],F={key:0,class:"text-sm text-gray-700 dark:text-gray-400"},O={class:"text-xl text-center font-semibold text-gray-700 dark:text-gray-400"},R={class:"mt-4 flex justify-between"},S={class:"text-xl font-semibold text-gray-700 dark:text-gray-400"},E={class:"mt-2 flex flex-col sm:flex-row justify-between"},I={class:"text-sm text-gray-500 dark:text-gray-400"},P={class:"font-semibold"},U={class:"mt-4 p-2 space-y-1 border-2 rounded-xl border-gray-200 dark:border-gray-700"},q={class:"w-1/2 sm:w-1/4 text-sm text-gray-500 dark:text-gray-400"},z=["innerHTML"],G={key:0,class:"mt-4"},K={class:"text-lg font-semibold text-gray-700 dark:text-gray-400"},Q=["innerHTML"],W={class:"mt-4"},X={name:"JobVacancy"},tt=Object.assign(X,{setup(Y){const v=L(),i=V(),f=C(),x=u("layout.display").value=="dark"?u("assets.iconLight"):u("assets.icon"),b="recruitment/",h=T(!1),a=j({vacancy:{}}),k=async()=>{await f.dispatch(b+"getVacancy",{slug:v.params.slug}).then(e=>{a.vacancy=e}).catch(e=>{i.push({name:"Dashboard"})})};return M(()=>{k()}),(e,n)=>{const w=y("BaseButton"),$=y("BaseLoader");return c(),r(g,null,[t("div",D,[t("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:n[0]||(n[0]=o=>l(i).push({name:"JobVacancies"}))},[n[3]||(n[3]=t("i",{class:"fas fa-arrow-left mr-1"},null,-1)),d(" "+s(e.$trans("global.list_all",{attribute:e.$trans("recruitment.vacancy.vacancy")})),1)]),t("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:n[1]||(n[1]=o=>l(i).push({name:"Dashboard"}))},[n[4]||(n[4]=t("i",{class:"fas fa-home mr-1"},null,-1)),d(" "+s(e.$trans("global.go_to",{attribute:e.$trans("dashboard.home")})),1)])]),t("div",H,[t("a",J,[t("img",{class:"h-16 w-auto",src:l(x),alt:""},null,8,A)])]),m($,{"is-loading":h.value},{default:p(()=>[a.vacancy.uuid?(c(),r("div",F,[t("h1",O,s(a.vacancy.team),1),t("div",R,[t("h2",S,s(a.vacancy.title),1)]),t("div",E,[t("span",I,s(a.vacancy.codeNumber),1),t("span",null,[d(s(e.$trans("recruitment.vacancy.props.last_application_date"))+" : ",1),t("span",P,s(a.vacancy.lastApplicationDate.formatted),1)])]),t("div",U,[(c(!0),r(g,null,N(a.vacancy.records,(o,B)=>(c(),r("div",{class:"flex",key:o.id},[t("span",q,s(B+1)+". "+s(o.designation)+" ("+s(o.employmentType)+") ",1),t("span",null,s(o.numberOfPositions),1)]))),128))]),t("div",{class:"mt-4",innerHTML:a.vacancy.description},null,8,z),a.vacancy.responsibility?(c(),r("div",G,[t("h3",K,s(e.$trans("recruitment.vacancy.props.responsibility")),1),t("div",{innerHTML:a.vacancy.responsibility},null,8,Q)])):_("",!0)])):_("",!0),t("div",W,[m(w,{block:"",onClick:n[2]||(n[2]=o=>l(i).push({name:"JobVacancyApplication",params:{slug:a.vacancy.slug}}))},{default:p(()=>[d(s(e.$trans("recruitment.apply_now")),1)]),_:1})])]),_:1},8,["is-loading"])],64)}}});export{tt as default};
