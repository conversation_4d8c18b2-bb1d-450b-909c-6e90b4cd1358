import{h as p,g as t,r as s,a as c,o as l,e as o,d as e,f as n,b as y,w as v,s as x,t as b,F as k}from"./app-BAwPsakn.js";const B={class:"dark:bg-dark-body scroller-thin-y scroller-hidden flex min-h-screen bg-gray-50"},w={class:"relative hidden flex-1 md:block"},C=["src"],N={class:"flex-1 p-4 sm:p-16 lg:flex-none lg:p-32"},j={key:0,class:"flex justify-end"},G={class:"flex h-full flex-col justify-center"},F={class:"flex justify-center"},V={href:"/",class:"mb-6"},P=["src"],$={class:"dark:bg-dark-header mx-auto w-full rounded-xl bg-white px-8 py-10 shadow-md sm:w-96"},z={},L=Object.assign(z,{__name:"Guest",setup(D){const r=p(),i=t("assets.guestBackground"),d=t("layout.display").value=="dark"?t("assets.iconLight"):t("assets.icon"),u=t("feature.enableGuestPayment").value;return(_,a)=>{const f=s("NotificationBar"),m=s("BaseButton"),h=s("router-view"),g=s("FooterCredit");return l(),c(k,null,[o(f,{type:"guest"}),e("div",B,[e("div",w,[e("img",{class:"absolute inset-0 h-full w-full object-cover",src:n(i),alt:""},null,8,C)]),e("div",N,[n(u)?(l(),c("div",j,[o(m,{size:"xs",onClick:a[0]||(a[0]=E=>n(r).push({name:"GuestPayment"}))},{default:v(()=>[x(b(_.$trans("student.payment.online")),1)]),_:1})])):y("",!0),e("div",G,[e("div",F,[e("a",V,[e("img",{class:"h-16 w-auto",src:n(d),alt:""},null,8,P)])]),e("div",$,[o(h)]),o(g)])])])],64)}}});export{L as default};
