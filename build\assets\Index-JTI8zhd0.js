import{l as M,r as o,q as y,o as m,w as e,d as k,e as a,u as U,i as L,H as O,m as I,n as q,a as w,b as B,f as d,s as r,t as l,h as z,j as G,z as J,F as H,v as K,A as Q,y as W}from"./app-BAwPsakn.js";import{d as X}from"./vuedraggable.umd-BRYqknf6.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={__name:"Filter",emits:["hide"],setup(V,{emit:c}){const p=c,g={name:"",code:""},_=M({...g});return(C,s)=>{const u=o("BaseInput"),$=o("FilterForm");return m(),y($,{"init-form":g,form:_,onHide:s[2]||(s[2]=b=>p("hide"))},{default:e(()=>[k("div",Y,[k("div",Z,[a(u,{type:"text",modelValue:_.name,"onUpdate:modelValue":s[0]||(s[0]=b=>_.name=b),name:"name",label:C.$trans("exam.props.name")},null,8,["modelValue","label"])]),k("div",ee,[a(u,{type:"text",modelValue:_.code,"onUpdate:modelValue":s[1]||(s[1]=b=>_.code=b),name:"code",label:C.$trans("exam.props.code")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ae={key:0},ne={class:"flex border rounded-xl px-4 py-2"},se={key:0,class:"ml-1"},oe={key:1},le={key:2,class:"mt-4 flex justify-end"},ie={name:"ExamTermReorder"},re=Object.assign(ie,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(V,{emit:c}){U();const p=L(),g=c,_={exams:[]};O("exam/");const s=I(!1),u=M({exams:[]});M({..._});const $=async()=>{s.value=!0,await p.dispatch("exam/list",{params:{all:!0}}).then(t=>{s.value=!1,u.exams=t}).catch(t=>{s.value=!1})},b=async()=>{s.value=!0,await p.dispatch("exam/reorder",{data:{exams:u.exams}}).then(t=>{s.value=!1,g("refresh"),g("close")}).catch(t=>{s.value=!1})},i=()=>{g("close")};return q(()=>{$()}),(t,x)=>{const A=o("BaseLabel"),R=o("BaseAlert"),F=o("BaseButton"),D=o("BaseModal");return m(),y(D,{show:V.visibility,onClose:i},{title:e(()=>[r(l(t.$trans("global.reorder",{attribute:t.$trans("exam.exam")})),1)]),default:e(()=>[u.exams.length?(m(),w("div",ae,[a(d(X),{class:"space-y-2",list:u.exams,"item-key":"uuid"},{item:e(({element:v,index:h})=>[k("div",ne,[x[0]||(x[0]=k("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),a(A,null,{default:e(()=>{var E;return[r(l(v.name)+" ",1),v.term?(m(),w("span",se,"("+l((E=v.term)==null?void 0:E.name)+")",1)):B("",!0)]}),_:2},1024)])]),_:1},8,["list"])])):(m(),w("div",oe,[a(R,{design:"info",size:"xs"},{default:e(()=>[r(l(t.$trans("general.errors.record_not_found")),1)]),_:1})])),u.exams.length?(m(),w("div",le,[a(F,{onClick:b},{default:e(()=>[r(l(t.$trans("general.reorder")),1)]),_:1})])):B("",!0)]),_:1},8,["show"])}}}),de={name:"ExamList"},ce=Object.assign(de,{setup(V){const c=z();L();const p=G("emitter");let g=["create","filter"],_=["print","pdf","excel"];const C="exam/";I(!1);const s=I(!1),u=I(!1),$=M({}),b=i=>{Object.assign($,i)};return(i,t)=>{const x=o("BaseButton"),A=o("PageHeaderAction"),R=o("PageHeader"),F=o("ParentTransition"),D=o("TextMuted"),v=o("DataCell"),h=o("FloatingMenuItem"),E=o("FloatingMenu"),j=o("DataRow"),N=o("DataTable"),P=o("ListItem"),S=J("tooltip");return m(),w(H,null,[a(P,{"init-url":C,"additional-query":{details:!0},onSetItems:b},{header:e(()=>[a(R,{title:i.$trans("exam.exam"),navs:[{label:i.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[a(A,{url:"exams/",name:"Exam",title:i.$trans("exam.exam"),actions:d(g),"dropdown-actions":d(_),onToggleFilter:t[2]||(t[2]=n=>s.value=!s.value)},{after:e(()=>[d(W)("exam:config")?(m(),y(x,{key:0,design:"white",onClick:t[1]||(t[1]=n=>d(c).push({name:"ExamConfig"}))},{default:e(()=>t[10]||(t[10]=[k("i",{class:"fas fa-cog"},null,-1)])),_:1})):B("",!0)]),default:e(()=>[Q((m(),y(x,{design:"white",onClick:t[0]||(t[0]=n=>u.value=!u.value)},{default:e(()=>t[9]||(t[9]=[k("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[S,i.$trans("global.reorder",{attribute:i.$trans("exam.exam")})]])]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(F,{appear:"",visibility:s.value},{default:e(()=>[a(te,{onRefresh:t[3]||(t[3]=n=>d(p).emit("listItems")),onHide:t[4]||(t[4]=n=>s.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a(N,{header:$.headers,meta:$.meta,module:"exam",onRefresh:t[6]||(t[6]=n=>d(p).emit("listItems"))},{actionButton:e(()=>[a(x,{onClick:t[5]||(t[5]=n=>d(c).push({name:"ExamCreate"}))},{default:e(()=>[r(l(i.$trans("global.add",{attribute:i.$trans("exam.exam")})),1)]),_:1})]),default:e(()=>[(m(!0),w(H,null,K($.data,n=>(m(),y(j,{key:n.uuid,onDoubleClick:f=>d(c).push({name:"ExamShow",params:{uuid:n.uuid}})},{default:e(()=>[a(v,{name:"name"},{default:e(()=>[r(l(n.name)+" ",1),n.code?(m(),y(D,{key:0,block:""},{default:e(()=>[r(l(n.code),1)]),_:2},1024)):B("",!0),n.displayName?(m(),y(D,{key:1,block:""},{default:e(()=>[r(l(n.displayName),1)]),_:2},1024)):B("",!0)]),_:2},1024),a(v,{name:"division"},{default:e(()=>{var f,T;return[r(l(((T=(f=n.term)==null?void 0:f.division)==null?void 0:T.name)||"-"),1)]}),_:2},1024),a(v,{name:"term"},{default:e(()=>{var f;return[r(l(((f=n.term)==null?void 0:f.name)||"-"),1)]}),_:2},1024),a(v,{name:"createdAt"},{default:e(()=>[r(l(n.createdAt.formatted),1)]),_:2},1024),a(v,{name:"action"},{default:e(()=>[a(E,null,{default:e(()=>[a(h,{icon:"fas fa-arrow-circle-right",onClick:f=>d(c).push({name:"ExamShow",params:{uuid:n.uuid}})},{default:e(()=>[r(l(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-edit",onClick:f=>d(c).push({name:"ExamEdit",params:{uuid:n.uuid}})},{default:e(()=>[r(l(i.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-copy",onClick:f=>d(c).push({name:"ExamDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[r(l(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-trash",onClick:f=>d(p).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[r(l(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),a(re,{visibility:u.value,onClose:t[7]||(t[7]=n=>u.value=!1),onRefresh:t[8]||(t[8]=n=>d(p).emit("listItems"))},null,8,["visibility"])],64)}}});export{ce as default};
