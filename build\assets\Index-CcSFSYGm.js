import{l as M,r as s,q as f,o as r,w as e,d as I,e as n,u as E,h as q,j as x,y as F,g as z,m as J,f as u,a as D,F as H,v as K,s as l,b,t as i}from"./app-BAwPsakn.js";import{_ as Q}from"./ParentDetail-QambUQlq.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={__name:"Filter",emits:["hide"],setup(d,{emit:N}){const $=N,_={firstName:"",lastName:""},m=M({..._});return(C,c)=>{const p=s("BaseInput"),v=s("FilterForm");return r(),f(v,{"init-form":_,form:m,onHide:c[2]||(c[2]=g=>$("hide"))},{default:e(()=>[I("div",W,[I("div",X,[n(p,{type:"text",modelValue:m.firstName,"onUpdate:modelValue":c[0]||(c[0]=g=>m.firstName=g),name:"name",label:C.$trans("contact.props.first_name")},null,8,["modelValue","label"])]),I("div",Y,[n(p,{type:"text",modelValue:m.lastName,"onUpdate:modelValue":c[1]||(c[1]=g=>m.lastName=g),name:"name",label:C.$trans("contact.props.last_name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ee={name:"StudentGuardianList"},ae=Object.assign(ee,{props:{student:{type:Object,default(){return{}}}},setup(d){const N=E(),$=q(),_=x("emitter");let m=["filter"];F("student:edit")&&m.unshift("create");const C=z("periods"),c="student/guardian/",p=J(!1),v=M({}),g=o=>{var a;return(a=C.value.find(h=>h.id==o))==null?void 0:a.name},R=o=>{Object.assign(v,o)};return(o,a)=>{const h=s("PageHeaderAction"),U=s("PageHeader"),S=s("ParentTransition"),B=s("TextMuted"),k=s("DataCell"),w=s("FloatingMenuItem"),j=s("FloatingMenu"),A=s("DataRow"),G=s("BaseButton"),L=s("DataTable"),O=s("ListItem");return r(),f(O,{"init-url":c,uuid:u(N).params.uuid,onSetItems:R},{header:e(()=>[d.student.uuid?(r(),f(U,{key:0,title:o.$trans("guardian.guardian"),navs:[{label:o.$trans("student.student"),path:"Student"},{label:d.student.contact.name,path:{name:"StudentShow",params:{uuid:d.student.uuid}}}]},{default:e(()=>[n(h,{url:`students/${d.student.uuid}/guardians/`,name:"StudentGuardian",title:o.$trans("guardian.guardian"),actions:u(m),"dropdown-actions":["print","pdf","excel"],onToggleFilter:a[0]||(a[0]=t=>p.value=!p.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):b("",!0)]),filter:e(()=>[n(S,{appear:"",visibility:p.value},{default:e(()=>[n(Z,{onRefresh:a[1]||(a[1]=t=>u(_).emit("listItems")),onHide:a[2]||(a[2]=t=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(Q,{student:d.student},null,8,["student"]),n(S,{appear:"",visibility:!0},{default:e(()=>[n(L,{header:v.headers,meta:v.meta,module:"guardian",onRefresh:a[4]||(a[4]=t=>u(_).emit("listItems"))},{actionButton:e(()=>[u(F)("student:edit")&&u(F)("guardian:create")?(r(),f(G,{key:0,onClick:a[3]||(a[3]=t=>u($).push({name:"StudentGuardianCreate"}))},{default:e(()=>[l(i(o.$trans("global.add",{attribute:o.$trans("guardian.guardian")})),1)]),_:1})):b("",!0)]),default:e(()=>[(r(!0),D(H,null,K(v.data,t=>(r(),f(A,{key:t.uuid},{default:e(()=>[n(k,{name:"name"},{default:e(()=>{var y,V;return[l(i(t.contact.name)+" ",1),t.contact.hasUser?(r(),f(B,{key:0,block:""},{default:e(()=>[a[5]||(a[5]=I("i",{class:"fas fa-user-circle"},null,-1)),l(" "+i(t.contact.user.username),1)]),_:2},1024)):b("",!0),(V=(y=t.contact)==null?void 0:y.user)!=null&&V.currentPeriodId?(r(),f(B,{key:1,block:""},{default:e(()=>{var P,T;return[l(i(g((T=(P=t.contact)==null?void 0:P.user)==null?void 0:T.currentPeriodId)),1)]}),_:2},1024)):b("",!0)]}),_:2},1024),n(k,{name:"relation"},{default:e(()=>[l(i(t.relation.label),1)]),_:2},1024),n(k,{name:"contactNumber"},{default:e(()=>[l(i(t.contact.contactNumber)+" ",1),t.contact.hasUser?(r(),f(B,{key:0,block:""},{default:e(()=>[l(i(t.contact.user.email),1)]),_:2},1024)):b("",!0)]),_:2},1024),n(k,{name:"createdAt"},{default:e(()=>[l(i(t.createdAt.formatted),1)]),_:2},1024),n(k,{name:"action"},{default:e(()=>[n(j,null,{default:e(()=>[n(w,{icon:"fas fa-arrow-circle-right",onClick:y=>u($).push({name:"GuardianShow",params:{uuid:t.uuid}})},{default:e(()=>[l(i(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),u(F)("student:edit")?(r(),D(H,{key:0},[n(w,{icon:"fas fa-edit",onClick:y=>u($).push({name:"StudentGuardianEdit",params:{uuid:d.student.uuid,muuid:t.uuid}})},{default:e(()=>[l(i(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),n(w,{icon:"fas fa-trash",onClick:y=>u(_).emit("deleteItem",{uuid:d.student.uuid,moduleUuid:t.uuid})},{default:e(()=>[l(i(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{ae as default};
