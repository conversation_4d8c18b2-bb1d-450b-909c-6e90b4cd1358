import{u as C,j as N,l as k,H as O,n as H,r as o,q as g,o as c,w as a,d as F,b as E,f as u,s as _,t as i,e as n,i as M,y as W,m as B,a as j,F as $,v as A}from"./app-BAwPsakn.js";const x={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},I={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(D,{emit:m}){const h=C();N("moment");const y=m,v=D,f={batch:"",date:null},s=k({...f}),l=O(v.initUrl),r=k({isLoaded:!h.query.batch});return H(async()=>{r.batch=h.query.batch,r.isLoaded=!0}),(d,e)=>{const p=o("BaseSelectSearch"),w=o("DatePicker"),S=o("FilterForm");return c(),g(S,{"init-form":f,multiple:[],form:s,onHide:e[4]||(e[4]=t=>y("hide"))},{default:a(()=>[F("div",x,[F("div",z,[r.isLoaded?(c(),g(p,{key:0,name:"batch",label:d.$trans("global.select",{attribute:d.$trans("academic.batch.batch")}),modelValue:s.batch,"onUpdate:modelValue":e[0]||(e[0]=t=>s.batch=t),"value-prop":"uuid","init-search":r.batch,"search-key":"course_batch","search-action":"academic/batch/list",error:u(l).batch,"onUpdate:error":e[1]||(e[1]=t=>u(l).batch=t)},{selectedOption:a(t=>[_(i(t.value.course.name)+" "+i(t.value.name),1)]),listOption:a(t=>[_(i(t.option.course.nameWithTerm)+" "+i(t.option.name),1)]),_:1},8,["label","modelValue","init-search","error"])):E("",!0)]),F("div",G,[n(w,{as:"month",modelValue:s.date,"onUpdate:modelValue":e[2]||(e[2]=t=>s.date=t),name:"date",label:d.$trans("student.attendance.props.date"),"no-clear":"",error:u(l).date,"onUpdate:error":e[3]||(e[3]=t=>u(l).date=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}},J={name:"StudentReportSubjectWiseAttendance"},Q=Object.assign(J,{setup(D){const m=C(),h=M();let y=["filter"],v=[];W("student:list-attendance")&&(v=["print","pdf","excel"]);const f="student/report/",s=B(!0),l=B(!1),r=k({headers:[],data:[],meta:{total:0}}),d=async()=>{l.value=!0,await h.dispatch(f+"fetchReport",{name:"subject-wise-attendance",params:m.query}).then(e=>{l.value=!1,Object.assign(r,e)}).catch(e=>{l.value=!1})};return H(async()=>{m.query.batch&&m.query.date&&await d()}),(e,p)=>{const w=o("PageHeaderAction"),S=o("PageHeader"),t=o("ParentTransition"),V=o("TextMuted"),R=o("DataCell"),U=o("DataRow"),q=o("DataTable"),P=o("BaseCard");return c(),j($,null,[n(S,{title:e.$trans(u(m).meta.label),navs:[{label:e.$trans("student.student"),path:"Student"},{label:e.$trans("student.report.report"),path:"StudentReport"}]},{default:a(()=>[n(w,{url:"student/reports/subject-wise-attendance/",name:"StudentReportSubjectWiseAttendance",title:e.$trans("student.report.subject_wise_attendance.subject_wise_attendance"),actions:u(y),"dropdown-actions":u(v),headers:r.headers,onToggleFilter:p[0]||(p[0]=b=>s.value=!s.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),n(t,{appear:"",visibility:s.value},{default:a(()=>[n(I,{onAfterFilter:d,"init-url":f,onHide:p[1]||(p[1]=b=>s.value=!1)})]),_:1},8,["visibility"]),n(t,{appear:"",visibility:!0},{default:a(()=>[n(P,{"no-padding":"","no-content-padding":"","is-loading":l.value},{default:a(()=>[n(q,{header:r.headers,meta:r.meta,module:"student.report.subject_wise_attendance",onRefresh:d},{default:a(()=>[(c(!0),j($,null,A(r.data,b=>(c(),g(U,{key:b.uuid},{default:a(()=>[n(R,{name:"student"},{default:a(()=>[_(i(b.name)+" ",1),n(V,{block:""},{default:a(()=>[_(i(b.codeNumber),1)]),_:2},1024)]),_:2},1024),(c(!0),j($,null,A(b.subjects,(T,L)=>(c(),g(R,{name:L},{default:a(()=>[_(i(T.present)+" ",1),n(V,{block:""},{default:a(()=>[_(i(T.percentage),1)]),_:2},1024)]),_:2},1032,["name"]))),256))]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{Q as default};
