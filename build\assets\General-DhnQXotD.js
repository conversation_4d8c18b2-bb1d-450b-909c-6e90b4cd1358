import{u as B,j as V,H as L,c as h,l as _,r as s,a as F,o as R,e as i,f as e,w as r,d as c,s as W,t as A,F as U}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-4"},x={class:"col-span-3 sm:col-span-1"},y={class:"col-span-3 sm:col-span-1"},C={name:"AcademicConfigGeneral"},H=Object.assign(C,{setup(E){const g=B(),o=V("$trans"),d="config/",l=L(d);h(()=>o("global.placeholder_info",{attribute:p.datePlaceholders}));const p=_({datePlaceholders:""}),u={periodSelection:"period_wise",allowListingSubjectWiseStudent:!1,type:"academic"},n=_({...u}),S=m=>{Object.assign(p,{datePlaceholders:m.datePlaceholders.map(t=>t.value).join(", ")})};return(m,t)=>{const f=s("PageHeader"),b=s("BaseLabel"),w=s("BaseRadioGroup"),j=s("BaseSwitch"),v=s("FormAction"),P=s("ParentTransition");return R(),F(U,null,[i(f,{title:e(o)(e(g).meta.label),navs:[{label:e(o)("academic.academic"),path:"Academic"}]},null,8,["title","navs"]),i(P,{appear:"",visibility:!0},{default:r(()=>[i(v,{"pre-requisites":!1,onSetPreRequisites:S,"init-url":d,"data-fetch":"academic","init-form":u,form:n,action:"store","stay-on":"",redirect:"Academic"},{default:r(()=>[c("div",q,[c("div",x,[i(b,null,{default:r(()=>[W(A(e(o)("academic.period_selection")),1)]),_:1}),i(w,{"top-margin":"",options:[{label:e(o)("academic.period_selection_options.period_wise"),value:"period_wise"},{label:e(o)("academic.period_selection_options.session_wise"),value:"session_wise"}],name:"periodSelection",modelValue:n.periodSelection,"onUpdate:modelValue":t[0]||(t[0]=a=>n.periodSelection=a),error:e(l).periodSelection,"onUpdate:error":t[1]||(t[1]=a=>e(l).periodSelection=a),horizontal:""},null,8,["options","modelValue","error"])]),c("div",y,[i(j,{vertical:"",modelValue:n.allowListingSubjectWiseStudent,"onUpdate:modelValue":t[2]||(t[2]=a=>n.allowListingSubjectWiseStudent=a),name:"allowListingSubjectWiseStudent",label:e(o)("academic.config.props.allow_listing_subject_wise_student"),error:e(l).allowListingSubjectWiseStudent,"onUpdate:error":t[3]||(t[3]=a=>e(l).allowListingSubjectWiseStudent=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{H as default};
