import{i as y,u as C,h as D,l as P,r as n,a as N,o as p,e as a,w as e,f as i,q as _,b as v,d as A,s as o,t as s,y as H,F as I}from"./app-BAwPsakn.js";const M={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},L={name:"TransportVehicleServiceRecordShow"},F=Object.assign(L,{setup(j){y();const c=C(),m=D(),h={},f="transport/vehicle/serviceRecord/",t=P({...h}),b=r=>{Object.assign(t,r)};return(r,d)=>{const g=n("PageHeaderAction"),$=n("PageHeader"),u=n("TextMuted"),l=n("BaseDataView"),S=n("ListMedia"),B=n("BaseButton"),T=n("ShowButton"),V=n("BaseCard"),k=n("ShowItem"),R=n("ParentTransition");return p(),N(I,null,[a($,{title:r.$trans(i(c).meta.trans,{attribute:r.$trans(i(c).meta.label)}),navs:[{label:r.$trans("transport.transport"),path:"Transport"},{label:r.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:r.$trans("transport.vehicle.service_record.service_record"),path:"TransportVehicleServiceRecord"}]},{default:e(()=>[a(g,{name:"TransportVehicleServiceRecord",title:r.$trans("transport.vehicle.service_record.service_record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(R,{appear:"",visibility:!0},{default:e(()=>[a(k,{"init-url":f,uuid:i(c).params.uuid,"module-uuid":i(c).params.muuid,onSetItem:b,onRedirectTo:d[1]||(d[1]=w=>i(m).push({name:"TransportVehicleServiceRecord",params:{uuid:t.uuid}}))},{default:e(()=>[t.uuid?(p(),_(V,{key:0},{title:e(()=>[o(s(t.vehicle.name)+" ",1),a(u,{block:""},{default:e(()=>[o(s(t.vehicle.registrationNumber),1)]),_:1})]),footer:e(()=>[a(T,null,{default:e(()=>[i(H)("vehicle-service-record:edit")?(p(),_(B,{key:0,design:"primary",onClick:d[0]||(d[0]=w=>i(m).push({name:"TransportVehicleServiceRecordEdit",params:{uuid:t.uuid}}))},{default:e(()=>[o(s(r.$trans("general.edit")),1)]),_:1})):v("",!0)]),_:1})]),default:e(()=>[A("dl",M,[a(l,{label:r.$trans("transport.vehicle.service_record.props.date")},{default:e(()=>[o(s(t.date.formatted)+" ",1),a(u,{block:""},{default:e(()=>[o(s(t.nextDueDate.formatted),1)]),_:1})]),_:1},8,["label"]),a(l,{label:r.$trans("transport.vehicle.service_record.props.log")},{default:e(()=>[o(s(t.log||"-")+" ",1),a(u,{block:""},{default:e(()=>[o(s(t.nextDueLog),1)]),_:1})]),_:1},8,["label"]),a(l,{label:r.$trans("transport.vehicle.service_record.props.amount")},{default:e(()=>[o(s(t.amount.formatted||"-"),1)]),_:1},8,["label"]),a(l,{class:"col-span-1 sm:col-span-2",label:r.$trans("transport.vehicle.service_record.props.remarks")},{default:e(()=>[o(s(t.remarks),1)]),_:1},8,["label"]),a(l,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[a(S,{media:t.media,url:`/app/transport/vehicle/service-records/${t.uuid}/`},null,8,["media","url"])]),_:1}),a(l,{label:r.$trans("general.created_at")},{default:e(()=>[o(s(t.createdAt.formatted),1)]),_:1},8,["label"]),a(l,{label:r.$trans("general.updated_at")},{default:e(()=>[o(s(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):v("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{F as default};
