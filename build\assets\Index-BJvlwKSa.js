import{r as e,q as i,o as a,w as c,e as r}from"./app-BAwPsakn.js";const s={name:"ActivityConfig"},g=Object.assign(s,{setup(f){const t=[{name:"ActivityConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"ActivityConfigTripType",icon:"fas fa-chevron-right",label:"activity.trip.type.type"}];return(_,p)=>{const o=e("router-view"),n=e("ModuleConfig");return a(),i(n,{navigations:t},{default:c(()=>[r(o)]),_:1})}}});export{g as default};
