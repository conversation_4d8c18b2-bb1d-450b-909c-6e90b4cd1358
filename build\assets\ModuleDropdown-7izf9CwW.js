import{u as f,h as k,r as P,a as _,o as l,q as y,b as n,f as e,y as p,w as m,s as u,t as d,F as E}from"./app-BAwPsakn.js";const c={name:"EmployeePayrollModuleDropdown"},S=Object.assign(c,{setup($){const r=f(),o=k();return(t,a)=>{const s=P("DropdownItem");return l(),_(E,null,[e(r).name!="EmployeePayrollPayHeadList"&&e(p)("payroll:config")?(l(),y(s,{key:0,onClick:a[0]||(a[0]=i=>e(o).push({name:"EmployeePayrollPayHead"}))},{default:m(()=>[u(d(t.$trans("employee.payroll.pay_head.pay_head")),1)]),_:1})):n("",!0),e(r).name!="EmployeePayrollSalaryTemplateList"&&e(p)("salary-template:read")?(l(),y(s,{key:1,onClick:a[1]||(a[1]=i=>e(o).push({name:"EmployeePayrollSalaryTemplate"}))},{default:m(()=>[u(d(t.$trans("employee.payroll.salary_template.salary_template")),1)]),_:1})):n("",!0),e(r).name!="EmployeePayrollSalaryStructureList"&&e(p)("salary-structure:read")?(l(),y(s,{key:2,onClick:a[2]||(a[2]=i=>e(o).push({name:"EmployeePayrollSalaryStructure"}))},{default:m(()=>[u(d(t.$trans("employee.payroll.salary_structure.salary_structure")),1)]),_:1})):n("",!0),e(r).name!="EmployeePayrollList"&&e(p)("payroll:read")?(l(),y(s,{key:3,onClick:a[3]||(a[3]=i=>e(o).push({name:"EmployeePayroll"}))},{default:m(()=>[u(d(t.$trans("employee.payroll.payroll")),1)]),_:1})):n("",!0)],64)}}});export{S as _};
