import{i as C,u as P,h as T,l as V,r as o,a as N,o as d,e as n,w as e,f as l,q as p,b,d as H,s as c,t as s,y as D,F as R}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},F={name:"AcademicSubjectInchargeShow"},q=Object.assign(F,{setup(M){C();const m=P(),_=T(),j={},$="academic/subjectIncharge/",t=V({...j}),y=a=>{Object.assign(t,a)};return(a,i)=>{const B=o("PageHeaderAction"),S=o("PageHeader"),g=o("TextMuted"),r=o("BaseDataView"),k=o("BaseButton"),A=o("ShowButton"),I=o("BaseCard"),w=o("ShowItem"),v=o("ParentTransition");return d(),N(R,null,[n(S,{title:a.$trans(l(m).meta.trans,{attribute:a.$trans(l(m).meta.label)}),navs:[{label:a.$trans("academic.academic"),path:"Academic"},{label:a.$trans("academic.subject.subject"),path:"AcademicSubject"},{label:a.$trans("academic.subject_incharge.subject_incharge"),path:"AcademicSubjectInchargeList"}]},{default:e(()=>[n(B,{name:"AcademicSubjectIncharge",title:a.$trans("academic.subject_incharge.subject_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(v,{appear:"",visibility:!0},{default:e(()=>[n(w,{"init-url":$,uuid:l(m).params.uuid,onSetItem:y,onRedirectTo:i[1]||(i[1]=u=>l(_).push({name:"AcademicSubjectIncharge"}))},{default:e(()=>[t.uuid?(d(),p(I,{key:0},{title:e(()=>[c(s(t.subject.name)+" - "+s(t.subject.code),1)]),footer:e(()=>[n(A,null,{default:e(()=>[l(D)("subject-incharge:edit")?(d(),p(k,{key:0,design:"primary",onClick:i[0]||(i[0]=u=>l(_).push({name:"AcademicSubjectInchargeEdit",params:{uuid:t.uuid}}))},{default:e(()=>[c(s(a.$trans("general.edit")),1)]),_:1})):b("",!0)]),_:1})]),default:e(()=>[H("dl",E,[n(r,{label:a.$trans("academic.batch.batch")},{default:e(()=>{var u,h,f;return[c(s(((h=(u=t.batch)==null?void 0:u.course)==null?void 0:h.name)||"-")+" ",1),(f=t.batch)!=null&&f.name?(d(),p(g,{key:0,block:""},{default:e(()=>[c(s(t.batch.name),1)]),_:1})):b("",!0)]}),_:1},8,["label"]),n(r,{label:a.$trans("employee.employee")},{default:e(()=>[c(s(t.employee.name)+" ",1),n(g,{block:""},{default:e(()=>[c(s(t.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),n(r,{label:a.$trans("employee.incharge.props.period")},{default:e(()=>[c(s(t.period),1)]),_:1},8,["label"]),n(r,{class:"col-span-1 sm:col-span-2",label:a.$trans("employee.incharge.props.remarks")},{default:e(()=>[c(s(t.remarks),1)]),_:1},8,["label"]),n(r,{label:a.$trans("general.created_at")},{default:e(()=>[c(s(t.createdAt.formatted),1)]),_:1},8,["label"]),n(r,{label:a.$trans("general.updated_at")},{default:e(()=>[c(s(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):b("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{q as default};
