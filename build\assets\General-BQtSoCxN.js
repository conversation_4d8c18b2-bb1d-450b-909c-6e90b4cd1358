import{u as h,j as v,H as b,c as F,l,r as s,a as j,o as q,e as a,f as i,w as d,d as p,F as R}from"./app-BAwPsakn.js";const x={name:"DisciplineConfigGeneral"},C=Object.assign(x,{setup(y){const u=h(),t=v("$trans"),n="config/";b(n),F(()=>t("global.placeholder_info",{attribute:o.datePlaceholders}));const o=l({datePlaceholders:""}),r={type:"discipline"},m=l({...r}),_=c=>{Object.assign(o,{datePlaceholders:c.datePlaceholders.map(e=>e.value).join(", ")})};return(c,e)=>{const f=s("PageHeader"),g=s("FormAction"),P=s("ParentTransition");return q(),j(R,null,[a(f,{title:i(t)(i(u).meta.label),navs:[{label:i(t)("discipline.discipline"),path:"Discipline"}]},null,8,["title","navs"]),a(P,{appear:"",visibility:!0},{default:d(()=>[a(g,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:_,"init-url":n,"data-fetch":"discipline","init-form":r,form:m,action:"store","stay-on":"",redirect:"Discipline"},{default:d(()=>e[0]||(e[0]=[p("div",{class:"grid grid-cols-3 gap-4"},[p("div",{class:"col-span-3 sm:col-span-1"})],-1)])),_:1},8,["form"])]),_:1})],64)}}});export{C as default};
