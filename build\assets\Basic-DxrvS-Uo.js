import{i as T,H as J,m as K,l as x,K as z,n as Q,r as b,q as m,o as d,w as a,a as S,b as f,F as I,v as j,s,t as l,f as e,y as D,J as W,u as X,h as Y,j as A,g as p,e as o,d as O,M as Z}from"./app-BAwPsakn.js";import{_ as tt}from"./EditRequestInfo-8GzviTVS.js";const et={key:0,class:"space-x-1"},at={name:"StudentTagList"},nt=Object.assign(at,{props:{student:{type:Object,default(){return{}}}},emits:["refresh"],setup(t,{emit:w}){T();const C=w,_=t,n={tags:[]},q="student/",y=J(q),h=K(!1),B=x({...n}),N=x({tags:[],isLoaded:!1}),L=i=>{Object.assign(n,{tags:i.map(r=>r.uuid)}),Object.assign(B,W(n)),N.tags=i.map(r=>r.uuid),N.isLoaded=!0},F=()=>{h.value=!1,C("refresh")};return z(()=>_.student.tags,i=>{L(i)}),Q(()=>{var i;L(((i=_.student)==null?void 0:i.tags)||[])}),(i,r)=>{const k=b("BaseBadge"),V=b("BaseSelectSearch"),$=b("FormAction"),v=b("BaseDataView");return d(),m(v,{class:"col-span-1 sm:col-span-4"},{label:a(()=>[s(l(i.$trans("general.tags"))+" ",1),e(D)("student:edit")?(d(),S("i",{key:0,class:"fas fa-edit cursor-pointer",onClick:r[0]||(r[0]=g=>h.value=!0)})):f("",!0)]),default:a(()=>[h.value?f("",!0):(d(),S("div",et,[(d(!0),S(I,null,j(t.student.tags||[],g=>(d(),m(k,{design:"primary"},{default:a(()=>[s(l(g.name),1)]),_:2},1024))),256))])),h.value?(d(),m($,{key:1,"no-card":"","no-data-fetch":"","cancel-action":"",action:"updateTags","keep-adding":!1,"init-url":q,"init-form":n,form:B,"after-submit":F,onCancelled:r[3]||(r[3]=g=>h.value=!1)},{default:a(()=>[N.isLoaded?(d(),m(V,{key:0,tags:"",name:"tags",placeholder:i.$trans("global.select",{attribute:i.$trans("general.tag")}),modelValue:B.tags,"onUpdate:modelValue":r[1]||(r[1]=g=>B.tags=g),error:e(y).tags,"onUpdate:error":r[2]||(r[2]=g=>e(y).tags=g),"init-search":N.tags,"search-action":"tag/list"},null,8,["placeholder","modelValue","error","init-search"])):f("",!0)]),_:1},8,["form"])):f("",!0)]),_:1})}}}),st={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},lt={class:"flex flex-wrap gap-2"},ut={name:"StudentShowBasic"},dt=Object.assign(ut,{props:{student:{type:Object,default(){return{contact:{}}}}},setup(t){const w=X(),C=Y(),_=T(),n=A("$trans"),q=A("emitter"),y=t,h=p("contact.uniqueIdNumber1Label"),B=p("contact.uniqueIdNumber2Label"),N=p("contact.uniqueIdNumber3Label"),L=p("contact.uniqueIdNumber4Label"),F=p("contact.uniqueIdNumber5Label"),i=p("contact.enableCategoryField"),r=p("contact.enableCasteField");let k=[];D("student:edit")&&(k.push({label:n("general.edit"),path:{name:"StudentEditBasic",params:{uuid:y.student.uuid}}}),k.push({label:n("global.edit",{attribute:n("contact.props.photo")}),path:{name:"StudentEditPhoto",params:{uuid:y.student.uuid}}}));const V=()=>{q.emit("studentUpdated")},$=async()=>{await Z()&&await _.dispatch("student/delete",{uuid:y.student.uuid}).then(v=>{C.push({name:"StudentList"})}).catch(v=>{})};return(v,g)=>{const E=b("BaseButton"),H=b("PageHeaderAction"),U=b("PageHeader"),c=b("BaseDataView"),R=b("BaseBadge"),M=b("BaseCard"),G=b("ParentTransition");return d(),S(I,null,[t.student.uuid?(d(),m(U,{key:0,title:e(n)(e(w).meta.label),navs:[{label:e(n)("student.student"),path:"Student"},{label:t.student.contact.name,path:{name:"StudentShow",params:{uuid:t.student.uuid}}}]},{default:a(()=>[o(H,{"additional-actions":e(k)},{after:a(()=>[e(D)("student:delete")&&t.student.records.length==0?(d(),m(E,{key:0,design:"danger",onClick:$},{default:a(()=>[s(l(e(n)("global.delete",{attribute:e(n)("student.student")})),1)]),_:1})):f("",!0)]),_:1},8,["additional-actions"])]),_:1},8,["title","navs"])):f("",!0),o(G,{appear:"",visibility:!0},{default:a(()=>[t.student.uuid?(d(),m(M,{key:0},{default:a(()=>{var P;return[o(tt,{student:t.student},null,8,["student"]),O("dl",st,[o(c,{label:e(n)("contact.props.father_name")},{default:a(()=>[s(l(t.student.contact.fatherName),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.mother_name")},{default:a(()=>[s(l(t.student.contact.motherName),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.birth_date")},{default:a(()=>[s(l(t.student.contact.birthDate.formatted),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.gender")},{default:a(()=>[s(l(t.student.contact.gender.label),1)]),_:1},8,["label"]),o(c,{label:e(h)},{default:a(()=>[s(l(t.student.contact.uniqueIdNumber1),1)]),_:1},8,["label"]),o(c,{label:e(B)},{default:a(()=>[s(l(t.student.contact.uniqueIdNumber2),1)]),_:1},8,["label"]),o(c,{label:e(N)},{default:a(()=>[s(l(t.student.contact.uniqueIdNumber3),1)]),_:1},8,["label"]),o(c,{label:e(L)},{default:a(()=>[s(l(t.student.contact.uniqueIdNumber4),1)]),_:1},8,["label"]),o(c,{label:e(F)},{default:a(()=>[s(l(t.student.contact.uniqueIdNumber5),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.birth_place")},{default:a(()=>[s(l(t.student.contact.birthPlace),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.nationality")},{default:a(()=>[s(l(t.student.contact.nationality),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.mother_tongue")},{default:a(()=>[s(l(t.student.contact.motherTongue),1)]),_:1},8,["label"]),o(c,{label:e(n)("contact.props.blood_group")},{default:a(()=>{var u;return[s(l(((u=t.student.contact.bloodGroup)==null?void 0:u.label)||"-"),1)]}),_:1},8,["label"]),o(c,{label:e(n)("contact.props.marital_status")},{default:a(()=>{var u;return[s(l(((u=t.student.contact.maritalStatus)==null?void 0:u.label)||"-"),1)]}),_:1},8,["label"]),o(c,{label:e(n)("contact.religion.religion")},{default:a(()=>{var u;return[s(l(((u=t.student.contact.religion)==null?void 0:u.name)||"-"),1)]}),_:1},8,["label"]),e(r)?(d(),m(c,{key:0,label:e(n)("contact.caste.caste")},{default:a(()=>{var u;return[s(l(((u=t.student.contact.caste)==null?void 0:u.name)||"-"),1)]}),_:1},8,["label"])):f("",!0),e(i)?(d(),m(c,{key:1,label:e(n)("contact.category.category")},{default:a(()=>{var u;return[s(l(((u=t.student.contact.category)==null?void 0:u.name)||"-"),1)]}),_:1},8,["label"])):f("",!0),(d(!0),S(I,null,j(((P=t.student.contact)==null?void 0:P.customFields)||[],u=>(d(),m(c,{key:u.uuid,label:u.label},{default:a(()=>[s(l(u.formattedValue),1)]),_:2},1032,["label"]))),128)),o(nt,{student:t.student,onRefresh:V},null,8,["student"]),o(c,{label:e(n)("student.group.group")},{default:a(()=>[O("div",lt,[(d(!0),S(I,null,j(t.student.groups,u=>(d(),m(R,{design:"primary",key:u.uuid},{default:a(()=>[s(l(u.name),1)]),_:2},1024))),128))])]),_:1},8,["label"])])]}),_:1})):f("",!0)]),_:1})],64)}}});export{dt as default};
