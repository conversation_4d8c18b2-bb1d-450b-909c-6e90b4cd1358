import{u as j,j as K,l as U,H as Q,n as L,r as d,q as g,o as p,w as t,d as h,e as a,b as v,s as i,t as o,h as X,i as Y,y as Z,m as H,a as D,f as S,F as M,v as x}from"./app-BAwPsakn.js";const ee={class:"grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},ue={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(N,{emit:$}){const F=j();K("moment");const w=$,q=N,V={referenceNumber:"",pgAccount:"",name:"",batches:[],startDate:"",endDate:"",period:"",status:""},l=U({...V});Q(q.initUrl);const b=U({isLoaded:!F.query.batches});return L(async()=>{b.batches=F.query.batches?F.query.batches.split(","):[],b.isLoaded=!0}),(r,u)=>{const m=d("BaseInput"),B=d("BaseSelectSearch"),k=d("DatePicker"),R=d("BaseSelect"),O=d("FilterForm");return p(),g(O,{"init-form":V,multiple:["batches"],form:l,onHide:u[8]||(u[8]=n=>w("hide"))},{default:t(()=>[h("div",ee,[h("div",te,[a(m,{type:"text",modelValue:l.referenceNumber,"onUpdate:modelValue":u[0]||(u[0]=n=>l.referenceNumber=n),name:"referenceNumber",label:r.$trans("finance.transaction.props.reference_number")},null,8,["modelValue","label"])]),h("div",ae,[a(m,{type:"text",modelValue:l.pgAccount,"onUpdate:modelValue":u[1]||(u[1]=n=>l.pgAccount=n),name:"pgAccount",label:r.$trans("finance.config.props.pg_account")},null,8,["modelValue","label"])]),h("div",ne,[a(m,{type:"text",modelValue:l.name,"onUpdate:modelValue":u[2]||(u[2]=n=>l.name=n),name:"name",label:r.$trans("contact.props.name")},null,8,["modelValue","label"])]),h("div",se,[b.isLoaded?(p(),g(B,{key:0,multiple:"",name:"batches",label:r.$trans("global.select",{attribute:r.$trans("academic.batch.batch")}),modelValue:l.batches,"onUpdate:modelValue":u[3]||(u[3]=n=>l.batches=n),"value-prop":"uuid","init-search":b.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(n=>[i(o(n.value.course.name)+" "+o(n.value.name),1)]),listOption:t(n=>[i(o(n.option.course.nameWithTerm)+" "+o(n.option.name),1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),h("div",oe,[a(k,{start:l.startDate,"onUpdate:start":u[4]||(u[4]=n=>l.startDate=n),end:l.endDate,"onUpdate:end":u[5]||(u[5]=n=>l.endDate=n),name:"dateBetween",as:"range",label:r.$trans("general.date_between")},null,8,["start","end","label"])]),h("div",le,[a(R,{name:"period",label:r.$trans("global.select",{attribute:r.$trans("academic.period.period")}),modelValue:l.period,"onUpdate:modelValue":u[6]||(u[6]=n=>l.period=n),"label-prop":"name","value-prop":"uuid",options:N.preRequisites.periods},null,8,["label","modelValue","options"])]),h("div",re,[a(R,{name:"status",label:r.$trans("global.select",{attribute:r.$trans("finance.transaction.props.status")}),modelValue:l.status,"onUpdate:modelValue":u[7]||(u[7]=n=>l.status=n),options:N.preRequisites.statuses},null,8,["label","modelValue","options"])])])]),_:1},8,["form"])}}},ie={key:0},de={key:2,class:"text-danger text-xs"},ce={name:"FinanceReportOnlineFeePayment"},me=Object.assign(ce,{setup(N){const $=j(),F=X(),w=Y();let q=["filter"],V=[];Z("finance:export")&&(V=["print","pdf","excel"]);const l="finance/report/",b=H(!1),r=H(!1),u=U({periods:[],statuses:[]}),m=U({headers:[],data:[],meta:{total:0}}),B=async()=>{r.value=!0,await w.dispatch(l+"preRequisite",{name:"online-fee-payment",params:$.query}).then(s=>{r.value=!1,Object.assign(u,s)}).catch(s=>{r.value=!1})},k=async()=>{r.value=!0,await w.dispatch(l+"fetchReport",{name:"online-fee-payment",params:$.query}).then(s=>{r.value=!1,Object.assign(m,s)}).catch(s=>{r.value=!1})},R=async s=>{r.value=!0,await w.dispatch("student/payment/updatePaymentStatus",{uuid:s.studentUuid,moduleUuid:s.uuid}).then(f=>{r.value=!1,k()}).catch(f=>{r.value=!1})},O=s=>{window.open(s.statusCheckUrl)},n=s=>{window.open(`/app/students/${s.studentUuid}/transactions/${s.uuid}/export?action=print`)};return L(async()=>{await B(),await k()}),(s,f)=>{const I=d("PageHeaderAction"),E=d("PageHeader"),P=d("ParentTransition"),y=d("TextMuted"),_=d("DataCell"),A=d("FloatingMenuItem"),W=d("FloatingMenu"),z=d("DataRow"),G=d("DataTable"),J=d("BaseCard");return p(),D(M,null,[a(E,{title:s.$trans(S($).meta.label),navs:[{label:s.$trans("finance.finance"),path:"Finance"},{label:s.$trans("finance.report.report"),path:"FinanceReport"}]},{default:t(()=>[a(I,{url:"finance/reports/online-fee-payment/",name:"FinanceReportOnlineFeePayment",title:s.$trans("finance.report.online_fee_payment.online_fee_payment"),actions:S(q),"dropdown-actions":S(V),headers:m.headers,onToggleFilter:f[0]||(f[0]=e=>b.value=!b.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),a(P,{appear:"",visibility:b.value},{default:t(()=>[a(ue,{onAfterFilter:k,"init-url":l,"pre-requisites":u,onHide:f[1]||(f[1]=e=>b.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),a(P,{appear:"",visibility:!0},{default:t(()=>[a(J,{"no-padding":"","no-content-padding":"","is-loading":r.value},{default:t(()=>[a(G,{header:m.headers,footer:m.footers,meta:m.meta,module:"finance.report.online_fee_payment",onRefresh:k},{default:t(()=>[(p(!0),D(M,null,x(m.data,e=>(p(),g(z,{key:e.uuid},{default:t(()=>[a(_,{name:"voucherNumber"},{default:t(()=>[i(o(e.voucherNumber)+" ",1),e.processedAt.value?(p(),D("span",ie,f[2]||(f[2]=[h("i",{class:"far fa-lg fa-check-circle text-success"},null,-1)]))):v("",!0),e.isOnline?(p(),g(y,{key:1,block:""},{default:t(()=>[i(o(e.referenceNumber),1)]),_:2},1024)):v("",!0),!e.processedAt.value&&e.errorCode?(p(),D("span",de,o(e.errorCode),1)):v("",!0)]),_:2},1024),a(_,{name:"name"},{default:t(()=>[i(o(e.name)+" ",1),a(y,{block:""},{default:t(()=>[i(o(e.rollNumber||e.codeNumber),1)]),_:2},1024)]),_:2},1024),a(_,{name:"fatherName"},{default:t(()=>[i(o(e.fatherName)+" ",1),a(y,{block:""},{default:t(()=>[i(o(e.contactNumber),1)]),_:2},1024)]),_:2},1024),a(_,{name:"course"},{default:t(()=>[i(o(e.courseName)+" ",1),a(y,{block:""},{default:t(()=>[i(o(e.batchName),1)]),_:2},1024)]),_:2},1024),a(_,{name:"amount"},{default:t(()=>[i(o(e.amount.formatted),1)]),_:2},1024),a(_,{name:"date"},{default:t(()=>[i(o(e.date.formatted)+" ",1),a(y,{block:""},{default:t(()=>[i(o(e.createdAt.formatted),1)]),_:2},1024),e.processedAt.value?(p(),g(y,{key:0,block:"",class:"text-success"},{default:t(()=>{var c;return[i(o((c=e.processedAt)==null?void 0:c.formatted),1)]}),_:2},1024)):v("",!0)]),_:2},1024),a(_,{name:"ledger"},{default:t(()=>{var c,C;return[i(o((C=(c=e.payment)==null?void 0:c.ledger)==null?void 0:C.name)+" ",1),a(y,{block:""},{default:t(()=>{var T;return[i(o((T=e.payment)==null?void 0:T.methodName),1)]}),_:2},1024)]}),_:2},1024),a(_,{name:"user"},{default:t(()=>{var c,C;return[i(o(((C=(c=e.user)==null?void 0:c.profile)==null?void 0:C.name)||"-"),1)]}),_:2},1024),a(_,{name:"action"},{default:t(()=>[a(W,null,{default:t(()=>[e.processedAt.value?(p(),g(A,{key:0,icon:"fas fa-print",onClick:c=>n(e)},{default:t(()=>[i(o(s.$trans("global.print",{attribute:s.$trans("student.fee.receipt")})),1)]),_:2},1032,["onClick"])):v("",!0),e.processedAt.value?v("",!0):(p(),g(A,{key:1,icon:"fas fa-sync",onClick:c=>R(e)},{default:t(()=>[i(o(s.$trans("global.refresh",{attribute:s.$trans("finance.transaction.props.status")})),1)]),_:2},1032,["onClick"])),a(A,{icon:"fas fa-arrow-circle-right",onClick:c=>S(F).push({name:"StudentShowFee",params:{uuid:e.studentUuid}})},{default:t(()=>[i(o(s.$trans("global.show",{attribute:s.$trans("student.fee.fee")})),1)]),_:2},1032,["onClick"]),e.hasStatusCheck?(p(),g(A,{key:2,icon:"fas fa-print",onClick:c=>O(e)},{default:t(()=>f[3]||(f[3]=[i("Check Status")])),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{me as default};
