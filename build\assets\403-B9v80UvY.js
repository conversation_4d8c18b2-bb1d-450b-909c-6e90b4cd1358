import{h as g,i as h,N as _,r,q as m,o as f,w as o,e as n,f as B,s as i,t as u}from"./app-BAwPsakn.js";const k={__name:"403",setup($){const s=g(),d=h(),l=_.useLoading({}),p=async()=>{let t=l.show();await d.dispatch("auth/user/logout",{}).then(()=>{t.hide(),s.push({name:"Login"})}).catch(e=>{t.hide()})};return(t,e)=>{const a=r("BaseButton"),c=r("ErrorPage");return f(),m(c,{type:"403",title:t.$trans("general.errors.403_title"),description:t.$trans("general.errors.403_description")},{default:o(()=>[n(a,{type:"button",onClick:e[0]||(e[0]=b=>B(s).push({name:"Dashboard"}))},{default:o(()=>[i(u(t.$trans("dashboard.dashboard")),1)]),_:1}),n(a,{design:"danger",type:"button",onClick:p},{default:o(()=>[i(u(t.$trans("auth.login.logout")),1)]),_:1})]),_:1},8,["title","description"])}}};export{k as default};
