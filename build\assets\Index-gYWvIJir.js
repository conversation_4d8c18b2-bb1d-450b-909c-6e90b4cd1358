import{r as e,q as t,o as r,w as c,e as i}from"./app-BAwPsakn.js";const s={name:"CalendarConfig"},p=Object.assign(s,{setup(l){const n=[{name:"CalendarConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"CalendarConfigEventType",icon:"fas fa-chevron-right",label:"calendar.event.type.type"}];return(f,_)=>{const o=e("router-view"),a=e("ModuleConfig");return r(),t(a,{navigations:n},{default:c(()=>[i(o)]),_:1})}}});export{p as default};
