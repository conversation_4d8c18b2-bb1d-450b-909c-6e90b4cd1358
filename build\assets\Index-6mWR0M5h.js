import{u as H,l as M,n as Q,r as d,q as c,o as i,w as t,d as w,b as p,s,t as a,e as o,h as W,i as X,j as Y,y,m as E,f as l,a as A,F as q,v as Z,x as ee,M as te}from"./app-BAwPsakn.js";import{_ as ae}from"./ModuleDropdown-BgZfvduI.js";const ne={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},le={__name:"Filter",emits:["hide"],setup(N,{emit:g}){const b=H(),T=g,_={employees:[],startDate:"",endDate:""},f=M({..._}),v=M({employees:[],isLoaded:!b.query.employees});return Q(async()=>{v.employees=b.query.employees?b.query.employees.split(","):[],v.isLoaded=!0}),(h,m)=>{const D=d("BaseSelectSearch"),$=d("DatePicker"),B=d("FilterForm");return i(),c(B,{"init-form":_,form:f,multiple:["employees"],onHide:m[3]||(m[3]=u=>T("hide"))},{default:t(()=>[w("div",ne,[w("div",oe,[v.isLoaded?(i(),c(D,{key:0,multiple:"",name:"employees",label:h.$trans("global.select",{attribute:h.$trans("employee.employee")}),modelValue:f.employees,"onUpdate:modelValue":m[0]||(m[0]=u=>f.employees=u),"value-prop":"uuid","init-search":v.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:t(u=>[s(a(u.value.name)+" ("+a(u.value.codeNumber)+") ",1)]),listOption:t(u=>[s(a(u.option.name)+" ("+a(u.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):p("",!0)]),w("div",se,[o($,{start:f.startDate,"onUpdate:start":m[1]||(m[1]=u=>f.startDate=u),end:f.endDate,"onUpdate:end":m[2]||(m[2]=u=>f.endDate=u),name:"dateBetween",as:"range",label:h.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ie={class:"mr-1"},re={key:0,class:"fas fa-moon fa-xs"},de={key:1,class:"text-danger text-xs"},me={key:0},ue={name:"EmployeeAttendanceTimesheetList"},fe=Object.assign(ue,{setup(N){const g=H(),b=W(),T=X(),_=Y("emitter");let f=["filter"];y("timesheet:create")&&f.unshift("create");let v=[];y("timesheet:export")&&(v=["print","pdf","excel"]),y("timesheet:import")&&v.unshift("import");const h="employee/attendance/timesheet/",m=E(!1),D=E(!1),$=E(!1),B=M({}),u=r=>{Object.assign(B,r)},V=async()=>{m.value||await te()&&(m.value=!0,await T.dispatch(h+"sync",{params:g.query}).then(r=>{m.value=!1,_.emit("listItems")}).catch(r=>{m.value=!1}))};return(r,n)=>{const L=d("BaseButton"),P=d("PageHeaderAction"),R=d("PageHeader"),j=d("BaseImport"),I=d("ParentTransition"),U=d("BaseAlert"),k=d("DataCell"),z=d("BaseBadge"),F=d("TextMuted"),S=d("FloatingMenuItem"),x=d("FloatingMenu"),G=d("DataRow"),J=d("DataTable"),K=d("ListItem");return i(),c(K,{"init-url":h,onSetItems:u},{header:t(()=>[o(R,{title:r.$trans("employee.attendance.timesheet.timesheet"),navs:[{label:r.$trans("employee.employee"),path:"Employee"},{label:r.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"}]},{default:t(()=>[o(P,{url:"employee/attendance/timesheets/",name:"EmployeeAttendanceTimesheet",title:r.$trans("employee.attendance.timesheet.timesheet"),actions:l(f),"dropdown-actions":l(v),onToggleFilter:n[0]||(n[0]=e=>D.value=!D.value),onToggleImport:n[1]||(n[1]=e=>$.value=!$.value)},{moduleOption:t(()=>[o(ae)]),default:t(()=>[l(y)("timesheet:sync")&&l(g).query.startDate&&l(g).query.endDate?(i(),c(L,{key:0,design:"white",onClick:V,disabled:m.value},{default:t(()=>[w("i",{class:ee(["fas fa-rotate mr-2",{"fa-spin":m.value}])},null,2),s(" "+a(r.$trans("global.sync",{attribute:r.$trans("employee.attendance.timesheet.timesheet")})),1)]),_:1},8,["disabled"])):p("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:t(()=>[o(I,{appear:"",visibility:$.value},{default:t(()=>[o(j,{path:"employee/attendance/timesheets/import",onCancelled:n[2]||(n[2]=e=>$.value=!1),onHide:n[3]||(n[3]=e=>$.value=!1),onCompleted:n[4]||(n[4]=e=>l(_).emit("listItems"))})]),_:1},8,["visibility"])]),filter:t(()=>[o(I,{appear:"",visibility:D.value},{default:t(()=>[o(le,{onRefresh:n[5]||(n[5]=e=>l(_).emit("listItems")),onHide:n[6]||(n[6]=e=>D.value=!1)})]),_:1},8,["visibility"])]),"after-filter":t(()=>[l(y)("timesheet:sync")&&(!l(g).query.startDate||!l(g).query.endDate)?(i(),c(U,{key:0,design:"info",size:"xs"},{default:t(()=>[s(a(r.$trans("employee.attendance.timesheet.choose_date_range_to_sync")),1)]),_:1})):p("",!0)]),default:t(()=>[o(I,{appear:"",visibility:!0},{default:t(()=>[o(J,{header:B.headers,meta:B.meta,module:"employee.attendance.timesheet",onRefresh:n[8]||(n[8]=e=>l(_).emit("listItems"))},{actionButton:t(()=>[l(y)("timesheet:create")?(i(),c(L,{key:0,onClick:n[7]||(n[7]=e=>l(b).push({name:"EmployeeAttendanceTimesheetCreate"}))},{default:t(()=>[s(a(r.$trans("global.add",{attribute:r.$trans("employee.attendance.timesheet.timesheet")})),1)]),_:1})):p("",!0)]),default:t(()=>[(i(!0),A(q,null,Z(B.data,e=>(i(),c(G,{key:e.uuid},{default:t(()=>[o(k,{name:"employee"},{default:t(()=>[s(a(e.employee.name)+" ("+a(e.employee.codeNumber)+") ",1)]),_:2},1024),o(k,{name:"designation"},{default:t(()=>[s(a(e.employee.designation),1)]),_:2},1024),o(k,{name:"workShift"},{default:t(()=>{var C,O;return[e.workShift?(i(),A(q,{key:0},[w("span",ie,[e.isOvernight?(i(),A("i",re)):p("",!0)]),s(" "+a((C=e.workShift)==null?void 0:C.name)+" ("+a((O=e.workShift)==null?void 0:O.code)+") ",1)],64)):(i(),A(q,{key:1},[s("-")],64))]}),_:2},1024),o(k,{name:"date"},{default:t(()=>[s(a(e.date.formatted)+" ",1),e.isManual?(i(),c(z,{key:0,type:"info"},{default:t(()=>[s(a(r.$trans("employee.attendance.timesheet.props.manual")),1)]),_:1})):p("",!0),e.status&&e.status!="ok"?(i(),A("div",de,a(e.status.label),1)):p("",!0),o(F,{block:""},{default:t(()=>[s(a(e.day),1)]),_:2},1024)]),_:2},1024),o(k,{name:"inAt"},{default:t(()=>[s(a(e.inAtTime.formatted)+" ",1),e.isOvernight?(i(),c(F,{key:0,block:""},{default:t(()=>[s(a(e.inAtDate.formatted),1)]),_:2},1024)):p("",!0)]),_:2},1024),o(k,{name:"outAt"},{default:t(()=>[s(a(e.outAtTime.formatted)+" ",1),e.isOvernight?(i(),c(F,{key:0,block:""},{default:t(()=>[s(a(e.outAtDate.formatted),1)]),_:2},1024)):p("",!0)]),_:2},1024),o(k,{name:"duration"},{default:t(()=>[s(a(e.duration)+" ",1),e.status=="ok"?(i(),A("span",me,n[9]||(n[9]=[w("i",{class:"fas fa-check-circle text-success"},null,-1)]))):p("",!0)]),_:2},1024),o(k,{name:"action"},{default:t(()=>[o(x,null,{default:t(()=>[l(y)("timesheet:edit")&&e.isEditable?(i(),c(S,{key:0,icon:"fas fa-edit",onClick:C=>l(b).push({name:"EmployeeAttendanceTimesheetEdit",params:{uuid:e.uuid}})},{default:t(()=>[s(a(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):p("",!0),l(y)("timesheet:create")?(i(),c(S,{key:1,icon:"fas fa-copy",onClick:C=>l(b).push({name:"EmployeeAttendanceTimesheetDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[s(a(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):p("",!0),l(y)("timesheet:delete")&&e.isDeletable?(i(),c(S,{key:2,icon:"fas fa-trash",onClick:C=>l(_).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[s(a(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):p("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{fe as default};
