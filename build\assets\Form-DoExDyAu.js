import{u as G,H as E,g as z,l as O,n as H,J as M,r as w,q as _,o as $,w as b,d as m,e as u,s as U,t as y,f as i,b as V,I as Q,G as K,K as Z,a as j,j as x,i as X,m as W,L as ee,F as J,M as te,h as ae}from"./app-BAwPsakn.js";import{u as se}from"./useCustomFields-C7JPVoj8.js";import{_ as re,a as oe,b as ne,c as ie,d as le,e as de}from"./Billdesk-CH1h7WlK.js";const ue={class:"grid grid-cols-3 gap-6"},me={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},$e={class:"col-span-3 sm:col-span-1"},ke={class:"col-span-3 sm:col-span-1"},Ue={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},Ae={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},_e={class:"col-span-3 sm:col-span-1"},we={class:"grid grid-cols-3 gap-6"},Ie={class:"col-span-3 sm:col-span-1"},Le={class:"col-span-3 sm:col-span-1"},Re={name:"OnlineRegistrationBasicForm"},he=Object.assign(Re,{props:{registration:{type:Object,required:!0},preRequisites:{type:Object,required:!0}},emits:["completed"],setup(L,{emit:S}){const R=G(),{customFields:T,setCustomFields:B}=se(),q=S,I=L,g={birthDate:"",birthPlace:"",gender:"",fatherName:"",motherName:"",nationality:"",motherTongue:"",bloodGroup:"",maritalStatus:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",category:"",caste:"",religion:"",customFields:[]},e="onlineRegistration/",a=E(e),v=z("contact.uniqueIdNumber1Label"),p=z("contact.uniqueIdNumber2Label"),N=z("contact.uniqueIdNumber3Label"),c=z("contact.uniqueIdNumber4Label"),o=z("contact.uniqueIdNumber5Label"),s=O({...g}),h=O({isLoaded:!R.params.uuid}),l=r=>{q("completed")};return H(()=>{var t,A,d,P,k,F,f;let r=I.registration.contact;Object.assign(g,{birthDate:((t=r.birthDate)==null?void 0:t.value)||"",birthPlace:r.birthPlace,gender:((A=r.gender)==null?void 0:A.value)||"",fatherName:r.fatherName,motherName:r.motherName,nationality:r.nationality,motherTongue:r.motherTongue,bloodGroup:((d=r.bloodGroup)==null?void 0:d.value)||"",maritalStatus:((P=r.maritalStatus)==null?void 0:P.value)||"",uniqueIdNumber1:r.uniqueIdNumber1,uniqueIdNumber2:r.uniqueIdNumber2,uniqueIdNumber3:r.uniqueIdNumber3,uniqueIdNumber4:r.uniqueIdNumber4,uniqueIdNumber5:r.uniqueIdNumber5,category:((k=r.category)==null?void 0:k.uuid)||"",caste:((F=r.caste)==null?void 0:F.uuid)||"",religion:((f=r.religion)==null?void 0:f.uuid)||""}),B(I.preRequisites.customFields,I.registration.customFields),g.customFields=T.value,Object.assign(s,M(g)),h.isLoaded=!0}),(r,t)=>{const A=w("BaseLabel"),d=w("BaseRadioGroup"),P=w("DatePicker"),k=w("BaseInput"),F=w("BaseSelect"),f=w("CustomField"),D=w("BaseFieldset"),C=w("FormAction");return $(),_(C,{"no-card":"","init-url":e,"no-data-fetch":"",uuid:i(R).params.number,action:"updateBasic","init-form":g,form:s,"stay-on":"","validate-unchanged":!1,"submit-text":r.$trans("pagination.next"),"after-submit":l},{default:b(()=>[m("div",ue,[m("div",me,[u(A,null,{default:b(()=>[U(y(r.$trans("contact.props.gender")),1)]),_:1}),u(d,{"top-margin":"",options:L.preRequisites.genders,name:"gender",modelValue:s.gender,"onUpdate:modelValue":t[0]||(t[0]=n=>s.gender=n),error:i(a).gender,"onUpdate:error":t[1]||(t[1]=n=>i(a).gender=n),horizontal:""},null,8,["options","modelValue","error"])]),m("div",pe,[u(P,{modelValue:s.birthDate,"onUpdate:modelValue":t[2]||(t[2]=n=>s.birthDate=n),name:"birthDate",label:r.$trans("contact.props.birth_date"),"no-clear":"",error:i(a).birthDate,"onUpdate:error":t[3]||(t[3]=n=>i(a).birthDate=n)},null,8,["modelValue","label","error"])]),m("div",ce,[u(P,{modelValue:s.anniversaryDate,"onUpdate:modelValue":t[4]||(t[4]=n=>s.anniversaryDate=n),name:"anniversaryDate",label:r.$trans("contact.props.anniversary_date"),"no-clear":"",error:i(a).anniversaryDate,"onUpdate:error":t[5]||(t[5]=n=>i(a).anniversaryDate=n)},null,8,["modelValue","label","error"])]),m("div",be,[u(k,{type:"text",modelValue:s.birthPlace,"onUpdate:modelValue":t[6]||(t[6]=n=>s.birthPlace=n),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:i(a).birthPlace,"onUpdate:error":t[7]||(t[7]=n=>i(a).birthPlace=n)},null,8,["modelValue","label","error"])]),m("div",ge,[u(k,{type:"text",modelValue:s.nationality,"onUpdate:modelValue":t[8]||(t[8]=n=>s.nationality=n),name:"nationality",label:r.$trans("contact.props.nationality"),error:i(a).nationality,"onUpdate:error":t[9]||(t[9]=n=>i(a).nationality=n)},null,8,["modelValue","label","error"])]),m("div",fe,[u(k,{type:"text",modelValue:s.motherTongue,"onUpdate:modelValue":t[10]||(t[10]=n=>s.motherTongue=n),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:i(a).motherTongue,"onUpdate:error":t[11]||(t[11]=n=>i(a).motherTongue=n)},null,8,["modelValue","label","error"])]),m("div",ye,[u(k,{type:"text",modelValue:s.uniqueIdNumber1,"onUpdate:modelValue":t[12]||(t[12]=n=>s.uniqueIdNumber1=n),name:"uniqueIdNumber1",label:i(v),error:i(a).uniqueIdNumber1,"onUpdate:error":t[13]||(t[13]=n=>i(a).uniqueIdNumber1=n)},null,8,["modelValue","label","error"])]),m("div",ve,[u(k,{type:"text",modelValue:s.uniqueIdNumber2,"onUpdate:modelValue":t[14]||(t[14]=n=>s.uniqueIdNumber2=n),name:"uniqueIdNumber2",label:i(p),error:i(a).uniqueIdNumber2,"onUpdate:error":t[15]||(t[15]=n=>i(a).uniqueIdNumber2=n)},null,8,["modelValue","label","error"])]),m("div",$e,[u(k,{type:"text",modelValue:s.uniqueIdNumber3,"onUpdate:modelValue":t[16]||(t[16]=n=>s.uniqueIdNumber3=n),name:"uniqueIdNumber3",label:i(N),error:i(a).uniqueIdNumber3,"onUpdate:error":t[17]||(t[17]=n=>i(a).uniqueIdNumber3=n)},null,8,["modelValue","label","error"])]),m("div",ke,[u(k,{type:"text",modelValue:s.uniqueIdNumber4,"onUpdate:modelValue":t[18]||(t[18]=n=>s.uniqueIdNumber4=n),name:"uniqueIdNumber4",label:i(c),error:i(a).uniqueIdNumber4,"onUpdate:error":t[19]||(t[19]=n=>i(a).uniqueIdNumber4=n)},null,8,["modelValue","label","error"])]),m("div",Ue,[u(k,{type:"text",modelValue:s.uniqueIdNumber5,"onUpdate:modelValue":t[20]||(t[20]=n=>s.uniqueIdNumber5=n),name:"uniqueIdNumber5",label:i(o),error:i(a).uniqueIdNumber5,"onUpdate:error":t[21]||(t[21]=n=>i(a).uniqueIdNumber5=n)},null,8,["modelValue","label","error"])]),m("div",qe,[h.isLoaded?($(),_(F,{key:0,modelValue:s.bloodGroup,"onUpdate:modelValue":t[22]||(t[22]=n=>s.bloodGroup=n),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:L.preRequisites.bloodGroups,error:i(a).bloodGroup,"onUpdate:error":t[23]||(t[23]=n=>i(a).bloodGroup=n)},null,8,["modelValue","label","options","error"])):V("",!0)]),m("div",Ne,[h.isLoaded?($(),_(F,{key:0,modelValue:s.maritalStatus,"onUpdate:modelValue":t[24]||(t[24]=n=>s.maritalStatus=n),name:"maritalStatus",label:r.$trans("contact.props.marital_status"),options:L.preRequisites.maritalStatuses,error:i(a).maritalStatus,"onUpdate:error":t[25]||(t[25]=n=>i(a).maritalStatus=n)},null,8,["modelValue","label","options","error"])):V("",!0)]),m("div",Ae,[h.isLoaded?($(),_(F,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:s.religion,"onUpdate:modelValue":t[26]||(t[26]=n=>s.religion=n),error:i(a).religion,"onUpdate:error":t[27]||(t[27]=n=>i(a).religion=n),options:L.preRequisites.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):V("",!0)]),m("div",Ve,[h.isLoaded?($(),_(F,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:s.category,"onUpdate:modelValue":t[28]||(t[28]=n=>s.category=n),error:i(a).category,"onUpdate:error":t[29]||(t[29]=n=>i(a).category=n),options:L.preRequisites.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):V("",!0)]),m("div",_e,[h.isLoaded?($(),_(F,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:s.caste,"onUpdate:modelValue":t[30]||(t[30]=n=>s.caste=n),error:i(a).caste,"onUpdate:error":t[31]||(t[31]=n=>i(a).caste=n),options:L.preRequisites.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):V("",!0)])]),u(f,{customFields:s.customFields,"onUpdate:customFields":t[32]||(t[32]=n=>s.customFields=n),formErrors:i(a),"onUpdate:formErrors":t[33]||(t[33]=n=>Q(a)?a.value=n:null)},null,8,["customFields","formErrors"]),u(D,{class:"mt-4"},{legend:b(()=>[U(y(r.$trans("student.props.parent")),1)]),default:b(()=>[m("div",we,[m("div",Ie,[u(k,{type:"text",modelValue:s.fatherName,"onUpdate:modelValue":t[34]||(t[34]=n=>s.fatherName=n),name:"fatherName",label:r.$trans("contact.props.father_name"),error:i(a).fatherName,"onUpdate:error":t[35]||(t[35]=n=>i(a).fatherName=n)},null,8,["modelValue","label","error"])]),m("div",Le,[u(k,{type:"text",modelValue:s.motherName,"onUpdate:modelValue":t[36]||(t[36]=n=>s.motherName=n),name:"motherName",label:r.$trans("contact.props.mother_name"),error:i(a).motherName,"onUpdate:error":t[37]||(t[37]=n=>i(a).motherName=n)},null,8,["modelValue","label","error"])])])]),_:1})]),_:1},8,["uuid","form","submit-text"])}}}),Te={class:"grid grid-cols-3 gap-6"},Be={class:"col-span-3 sm:col-span-1"},Oe={class:"col-span-3 sm:col-span-1"},Pe={class:"col-span-3"},Fe={class:"grid grid-cols-3 gap-6"},Se={class:"col-span-3"},Ce={class:"grid grid-cols-3 gap-6"},je={class:"col-span-3 sm:col-span-1"},ze={class:"mt-4 grid grid-cols-3 gap-6"},De={class:"grid grid-cols-1"},Ge={class:"col"},Ee={class:"col"},He={name:"OnlineRegistrationContactForm"},Me=Object.assign(He,{props:{registration:{type:Object,required:!0}},emits:["completed"],setup(L,{emit:S}){const R=G(),T=S,B=L,q={alternateRecords:{contactNumber:"",email:""},presentAddress:{addressLine1:"",addressLine2:"",city:"",state:"",zipcode:"",country:""},permanentAddress:{sameAsPresentAddress:!0,addressLine1:"",addressLine2:"",city:"",state:"",zipcode:"",country:""},idProof:[],addressProof:[],mediaUpdated:!1,mediaToken:K(),mediaHash:[]},I="onlineRegistration/",g=E(I),e=O({}),a=O({...q}),v=O({isLoaded:!R.params.uuid}),p=c=>{Object.assign(e,c)},N=c=>{T("completed")};return H(()=>{var o,s,h,l,r,t,A,d,P,k,F,f,D,C;let c=B.registration.contact;Object.assign(q,{alternateRecords:{contactNumber:(o=c.alternateRecords)==null?void 0:o.contactNumber,email:(s=c.alternateRecords)==null?void 0:s.email},presentAddress:{addressLine1:(h=c.presentAddress)==null?void 0:h.addressLine1,addressLine2:(l=c.presentAddress)==null?void 0:l.addressLine2,city:(r=c.presentAddress)==null?void 0:r.city,state:(t=c.presentAddress)==null?void 0:t.state,zipcode:(A=c.presentAddress)==null?void 0:A.zipcode,country:(d=c.presentAddress)==null?void 0:d.country},permanentAddress:{sameAsPresentAddress:c.sameAsPresentAddress,addressLine1:(P=c.permanentAddress)==null?void 0:P.addressLine1,addressLine2:(k=c.permanentAddress)==null?void 0:k.addressLine2,city:(F=c.permanentAddress)==null?void 0:F.city,state:(f=c.permanentAddress)==null?void 0:f.state,zipcode:(D=c.permanentAddress)==null?void 0:D.zipcode,country:(C=c.permanentAddress)==null?void 0:C.country},idProof:B.registration.media.filter(n=>n.section=="id_proof"),addressProof:B.registration.media.filter(n=>n.section=="address_proof"),mediaToken:B.registration.mediaToken}),Object.assign(a,M(q)),v.isLoaded=!0}),(c,o)=>{const s=w("BaseInput"),h=w("AddressInput"),l=w("BaseFieldset"),r=w("BaseSwitch"),t=w("MediaUpload"),A=w("FormAction");return v.isLoaded?($(),_(A,{key:0,"no-card":"","pre-requisites":!1,onSetPreRequisites:p,"init-url":I,"no-data-fetch":"",uuid:i(R).params.number,action:"updateContact","init-form":q,form:a,"stay-on":"","validate-unchanged":!1,"submit-text":c.$trans("pagination.next"),"after-submit":N},{default:b(()=>[m("div",Te,[m("div",Be,[u(s,{type:"text",modelValue:a.alternateRecords.contactNumber,"onUpdate:modelValue":o[0]||(o[0]=d=>a.alternateRecords.contactNumber=d),name:"alternateContactNumber",label:c.$trans("global.alternate",{attribute:c.$trans("contact.props.contact_number")}),error:i(g).alternateContactNumber,"onUpdate:error":o[1]||(o[1]=d=>i(g).alternateContactNumber=d)},null,8,["modelValue","label","error"])]),m("div",Oe,[u(s,{type:"text",modelValue:a.alternateRecords.email,"onUpdate:modelValue":o[2]||(o[2]=d=>a.alternateRecords.email=d),name:"alternateEmail",label:c.$trans("global.alternate",{attribute:c.$trans("contact.props.email")}),error:i(g).alternateEmail,"onUpdate:error":o[3]||(o[3]=d=>i(g).alternateEmail=d)},null,8,["modelValue","label","error"])]),m("div",Pe,[u(l,null,{legend:b(()=>[U(y(c.$trans("contact.props.present_address")),1)]),default:b(()=>[m("div",Fe,[u(h,{prefix:"presentAddress",addressLine1:a.presentAddress.addressLine1,"onUpdate:addressLine1":o[4]||(o[4]=d=>a.presentAddress.addressLine1=d),addressLine2:a.presentAddress.addressLine2,"onUpdate:addressLine2":o[5]||(o[5]=d=>a.presentAddress.addressLine2=d),city:a.presentAddress.city,"onUpdate:city":o[6]||(o[6]=d=>a.presentAddress.city=d),state:a.presentAddress.state,"onUpdate:state":o[7]||(o[7]=d=>a.presentAddress.state=d),zipcode:a.presentAddress.zipcode,"onUpdate:zipcode":o[8]||(o[8]=d=>a.presentAddress.zipcode=d),country:a.presentAddress.country,"onUpdate:country":o[9]||(o[9]=d=>a.presentAddress.country=d),formErrors:i(g),"onUpdate:formErrors":o[10]||(o[10]=d=>Q(g)?g.value=d:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1})]),m("div",Se,[u(l,null,{legend:b(()=>[U(y(c.$trans("contact.props.permanent_address")),1)]),default:b(()=>[m("div",Ce,[m("div",je,[u(r,{modelValue:a.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":o[11]||(o[11]=d=>a.permanentAddress.sameAsPresentAddress=d),name:"sameAsPresentAddress",label:c.$trans("contact.props.same_as_present_address"),error:i(g).sameAsPresentAddress,"onUpdate:error":o[12]||(o[12]=d=>i(g).sameAsPresentAddress=d)},null,8,["modelValue","label","error"])])]),m("div",ze,[a.permanentAddress.sameAsPresentAddress?V("",!0):($(),_(h,{key:0,prefix:"permanentAddress",addressLine1:a.permanentAddress.addressLine1,"onUpdate:addressLine1":o[13]||(o[13]=d=>a.permanentAddress.addressLine1=d),addressLine2:a.permanentAddress.addressLine2,"onUpdate:addressLine2":o[14]||(o[14]=d=>a.permanentAddress.addressLine2=d),city:a.permanentAddress.city,"onUpdate:city":o[15]||(o[15]=d=>a.permanentAddress.city=d),state:a.permanentAddress.state,"onUpdate:state":o[16]||(o[16]=d=>a.permanentAddress.state=d),zipcode:a.permanentAddress.zipcode,"onUpdate:zipcode":o[17]||(o[17]=d=>a.permanentAddress.zipcode=d),country:a.permanentAddress.country,"onUpdate:country":o[18]||(o[18]=d=>a.permanentAddress.country=d),formErrors:i(g),"onUpdate:formErrors":o[19]||(o[19]=d=>Q(g)?g.value=d:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1})])]),m("div",De,[m("div",Ge,[u(t,{multiple:"",guest:"",label:c.$trans("contact.props.id_proof"),module:"registration",section:"id_proof",media:a.idProof,"media-token":a.mediaToken,onIsUpdated:o[20]||(o[20]=d=>a.mediaUpdated=!0),onSetHash:o[21]||(o[21]=d=>a.mediaHash.push(d)),error:i(g).idProof,"onUpdate:error":o[22]||(o[22]=d=>i(g).idProof=d)},null,8,["label","media","media-token","error"])]),m("div",Ee,[u(t,{multiple:"",guest:"",label:c.$trans("contact.props.address_proof"),module:"registration",section:"address_proof",media:a.addressProof,"media-token":a.mediaToken,onIsUpdated:o[23]||(o[23]=d=>a.mediaUpdated=!0),onSetHash:o[24]||(o[24]=d=>a.mediaHash.push(d)),error:i(g).addressProof,"onUpdate:error":o[25]||(o[25]=d=>i(g).addressProof=d)},null,8,["label","media","media-token","error"])])])]),_:1},8,["uuid","form","submit-text"])):V("",!0)}}}),Je={class:"grid grid-cols-1"},Ke={class:"col-span-1"},Ye={name:"OnlineRegistrationAvatarForm"},Qe=Object.assign(Ye,{props:{registration:{type:Object,required:!0}},emits:["completed"],setup(L,{emit:S}){const R=S,T=L,B=G(),q={photo:"",idProof:[],addressProof:[],mediaUpdated:!1,mediaToken:K(),mediaHash:[]};E("onlineRegistration/");const g=O({...q}),e=O({headers:{"auth-token":localStorage.getItem("authToken")}}),a=O({isLoaded:!B.params.uuid}),v=N=>{R("completed");let c=T.registration.contact;g.photo=c==null?void 0:c.photo},p=()=>{R("completed");let N=T.registration.contact;g.photo=N==null?void 0:N.photo};return H(()=>{let N=T.registration.contact;Object.assign(q,{photo:N.photo}),Object.assign(g,M(q)),a.isLoaded=!0}),Z(()=>T.registration.contact,N=>{N&&(g.photo=N.photo)},{deep:!0}),(N,c)=>{const o=w("ImageUpload");return $(),j("div",Je,[m("div",Ke,[u(o,{label:N.$trans("contact.props.photo"),src:g.photo,"upload-path":`online-registrations/${L.registration.applicationNumber}/photo`,"remove-path":`online-registrations/${L.registration.applicationNumber}/photo`,headers:e.headers,onUploaded:v,onRemoved:p},null,8,["label","src","upload-path","remove-path","headers"])])])}}}),We={class:"grid grid-cols-1"},Xe={class:"col"},Ze={class:"col"},xe={name:"OnlineRegistrationUploadForm"},et=Object.assign(xe,{props:{registration:{type:Object,default(){return{}}}},emits:["close","completed"],setup(L,{emit:S}){const R=G(),T=S,B=L,q={marksheet:[],transferCertificate:[],mediaUpdated:!1,mediaToken:K(),mediaHash:[]},I="onlineRegistration/",g=E(I),e=O({...q}),a=O({isLoaded:!R.params.number}),v=p=>{T("completed")};return H(()=>{Object.assign(q,{marksheet:B.registration.media.filter(p=>p.section=="marksheet"),transferCertificate:B.registration.media.filter(p=>p.section=="transfer_certificate"),mediaToken:B.registration.mediaToken}),Object.assign(e,M(q)),a.isLoaded=!0}),(p,N)=>{const c=w("MediaUpload"),o=w("FormAction");return a.isLoaded?($(),_(o,{key:0,"no-card":"","no-data-fetch":"","cancel-action":"",uuid:i(R).params.number,action:"uploadFile","init-url":I,"init-form":q,form:e,"validate-unchanged":!1,"keep-adding":!1,"after-submit":v},{default:b(()=>[m("div",We,[m("div",Xe,[u(c,{multiple:"",guest:"",label:p.$trans("student.online_registration.wizard.marksheet"),module:"registration",section:"marksheet",media:e.marksheet,"media-token":e.mediaToken,onIsUpdated:N[0]||(N[0]=s=>e.mediaUpdated=!0),onSetHash:N[1]||(N[1]=s=>e.mediaHash.push(s)),error:i(g).marksheet,"onUpdate:error":N[2]||(N[2]=s=>i(g).marksheet=s)},null,8,["label","media","media-token","error"])]),m("div",Ze,[u(c,{multiple:"",guest:"",label:p.$trans("student.online_registration.wizard.transfer_certificate"),module:"registration",section:"transfer_certificate",media:e.transferCertificate,"media-token":e.mediaToken,onIsUpdated:N[3]||(N[3]=s=>e.mediaUpdated=!0),onSetHash:N[4]||(N[4]=s=>e.mediaHash.push(s)),error:i(g).transferCertificate,"onUpdate:error":N[5]||(N[5]=s=>i(g).transferCertificate=s)},null,8,["label","media","media-token","error"])])])]),_:1},8,["uuid","form"])):V("",!0)}}}),tt={class:"grid grid-cols-3 gap-6"},at={class:"col-span-3"},st={class:"col-span-3 sm:col-span-1"},rt={class:"col-span-3 sm:col-span-1"},ot={class:"grid grid-cols-1"},nt={class:"col"},it={name:"OnlineRegistrationReviewForm"},lt=Object.assign(it,{props:{registration:{type:Object,required:!0}},emits:["completed"],setup(L,{emit:S}){const R=G(),T=x("moment"),B=S,q=L,I={declaration:!1,dateOfSubmission:T().format("D MMM Y"),placeOfSubmission:"",signature:[],mediaUpdated:!1,mediaToken:K(),mediaHash:[]},g="onlineRegistration/",e=E(g),a=O({}),v=O({...I}),p=O({isLoaded:!R.params.uuid}),N=o=>{Object.assign(a,o)},c=o=>{B("completed",o)};return H(()=>{Object.assign(I,{signature:q.registration.media.filter(o=>o.section=="signature"),mediaToken:q.registration.mediaToken}),Object.assign(v,M(I)),p.isLoaded=!0}),(o,s)=>{const h=w("BaseSwitch"),l=w("BaseInput"),r=w("MediaUpload"),t=w("FormAction");return p.isLoaded?($(),_(t,{key:0,"no-card":"","pre-requisites":!1,onSetPreRequisites:N,"init-url":g,"no-data-fetch":"",uuid:i(R).params.number,action:"updateReview","keep-adding":!1,"init-form":I,form:v,"submit-text":o.$trans("general.submit"),"after-submit":c},{default:b(()=>[m("div",tt,[m("div",at,[u(h,{reverse:"",modelValue:v.declaration,"onUpdate:modelValue":s[0]||(s[0]=A=>v.declaration=A),name:"declaration",label:o.$trans("student.online_registration.declaration"),error:i(e).declaration,"onUpdate:error":s[1]||(s[1]=A=>i(e).declaration=A)},null,8,["modelValue","label","error"])]),m("div",st,[u(l,{readonly:"",type:"text",modelValue:v.dateOfSubmission,"onUpdate:modelValue":s[2]||(s[2]=A=>v.dateOfSubmission=A),name:"dateOfSubmission",label:o.$trans("student.online_registration.props.date_of_submission"),error:i(e).dateOfSubmission,"onUpdate:error":s[3]||(s[3]=A=>i(e).dateOfSubmission=A)},null,8,["modelValue","label","error"])]),m("div",rt,[u(l,{type:"text",modelValue:v.placeOfSubmission,"onUpdate:modelValue":s[4]||(s[4]=A=>v.placeOfSubmission=A),name:"placeOfSubmission",label:o.$trans("student.online_registration.props.place_of_submission"),error:i(e).placeOfSubmission,"onUpdate:error":s[5]||(s[5]=A=>i(e).placeOfSubmission=A)},null,8,["modelValue","label","error"])])]),m("div",ot,[m("div",nt,[u(r,{multiple:"",guest:"",label:o.$trans("contact.props.signature"),module:"registration",section:"signature",media:v.signature,"media-token":v.mediaToken,onIsUpdated:s[6]||(s[6]=A=>v.mediaUpdated=!0),onSetHash:s[7]||(s[7]=A=>v.mediaHash.push(A)),error:i(e).signature,"onUpdate:error":s[8]||(s[8]=A=>i(e).signature=A)},null,8,["label","media","media-token","error"])])])]),_:1},8,["uuid","form","submit-text"])):V("",!0)}}}),dt={class:"grid grid-cols-3 gap-4"},ut={class:"col-span-3 sm:col-span-1"},mt={key:0,class:"col-span-3"},pt={class:"mt-4"},ct={name:"OnlineApplicationPayment"},bt=Object.assign(ct,{props:{visibility:{type:Boolean,default:!1},registration:{type:Object,default:()=>({})},preRequisites:{type:Object,default:()=>({})}},emits:["close","completed"],setup(L,{emit:S}){const R=G(),T=X(),B=S,q=L,I={uuid:R.params.number,amount:"",gateway:""},g="onlineRegistration/",e=E(g),a=W(!1),v=O({initiated:!1,payment:null}),p=O({...I}),N=()=>{h(),T.dispatch(g+"resetFormErrors"),B("close")},c=()=>{v.initiated=!1,B("completed")},o=r=>{a.value=r},s=r=>!!q.preRequisites.paymentGateways.find(t=>t.value==r),h=()=>{v.initiated=!1,v.payment=null,p.gateway=""},l=async()=>{await te()&&(a.value=!0,await T.dispatch(g+"initiatePayment",p).then(r=>{if(r.hasOwnProperty("payment")&&r.payment.amount<=0){location.reload();return}v.initiated=!0,v.payment=r,a.value=!1}).catch(r=>{a.value=!1}))};return ee(async()=>{var r,t;((t=(r=q.preRequisites)==null?void 0:r.paymentGateways)==null?void 0:t.length)==1&&(I.gateway=q.preRequisites.paymentGateways[0].value),I.amount=q.registration.fee.value,Object.assign(p,I)}),(r,t)=>{const A=w("BaseInput"),d=w("BaseRadioGroup"),P=w("BaseButton"),k=w("BaseModal");return $(),_(k,{show:L.visibility,onClose:N},{title:b(()=>[U(y(r.$trans("global.pay",{attribute:r.$trans("student.fee.fee")})),1)]),default:b(()=>{var F,f,D;return[m("div",dt,[m("div",ut,[u(A,{disabled:!0,modelValue:p.amount,"onUpdate:modelValue":t[0]||(t[0]=C=>p.amount=C),name:"amount",label:r.$trans("student.fee.props.amount"),currency:"",error:i(e).amount,"onUpdate:error":t[1]||(t[1]=C=>i(e).amount=C)},null,8,["modelValue","label","error"])]),L.preRequisites.paymentGateways.length>1?($(),j("div",mt,[u(d,{disabled:v.initiated,options:L.preRequisites.paymentGateways,name:"gateway",modelValue:p.gateway,"onUpdate:modelValue":t[2]||(t[2]=C=>p.gateway=C),error:i(e).gateway,"onUpdate:error":t[3]||(t[3]=C=>i(e).gateway=C),horizontal:""},null,8,["disabled","options","modelValue","error"])])):V("",!0)]),s("razorpay")&&p.gateway=="razorpay"?($(),_(re,{key:0,"init-url":g,form:p,payment:v.payment,onLoading:o,onRefresh:c},null,8,["form","payment"])):V("",!0),s("paystack")&&p.gateway=="paystack"?($(),_(oe,{key:1,"init-url":g,form:p,payment:v.payment,onLoading:o,onRefresh:c},null,8,["form","payment"])):V("",!0),s("stripe")&&p.gateway=="stripe"?($(),_(ne,{key:2,"init-url":g,form:p,payment:v.payment,onCancel:h,onLoading:o,onRefresh:c},null,8,["form","payment"])):V("",!0),s("paypal")&&p.gateway=="paypal"&&((F=v.payment)!=null&&F.token)?($(),_(ie,{key:3,"init-url":g,form:p,payment:v.payment,onCancel:h,onLoading:o,onRefresh:c},null,8,["form","payment"])):V("",!0),s("ccavenue")&&p.gateway=="ccavenue"&&((f=v.payment)!=null&&f.token)?($(),_(le,{key:4,payment:v.payment},null,8,["payment"])):V("",!0),s("billdesk")&&p.gateway=="billdesk"&&((D=v.payment)!=null&&D.token)?($(),_(de,{key:5,payment:v.payment},null,8,["payment"])):V("",!0),m("div",pt,[p.gateway?($(),j(J,{key:0},[p.gateway!="stripe"&&p.gateway!="paypal"&&p.gateway!="ccavenue"&&p.gateway!="billdesk"?($(),_(P,{key:0,design:"primary",onClick:l},{default:b(()=>[U(y(r.$trans("general.proceed")),1)]),_:1})):V("",!0),p.gateway=="stripe"&&!v.payment?($(),_(P,{key:1,design:"primary",onClick:l},{default:b(()=>[U(y(r.$trans("general.proceed")),1)]),_:1})):V("",!0),p.gateway=="paypal"&&!v.payment?($(),_(P,{key:2,design:"primary",onClick:l},{default:b(()=>[U(y(r.$trans("general.proceed")),1)]),_:1})):V("",!0),p.gateway=="ccavenue"&&!v.payment?($(),_(P,{key:3,design:"primary",onClick:l},{default:b(()=>[U(y(r.$trans("general.proceed")),1)]),_:1})):V("",!0),p.gateway=="billdesk"&&!v.payment?($(),_(P,{key:4,design:"primary",onClick:l},{default:b(()=>[U(y(r.$trans("general.proceed")),1)]),_:1})):V("",!0)],64)):V("",!0)])]}),_:1},8,["show"])}}}),gt={class:"flex justify-end"},ft={class:"flex justify-center"},yt={href:"/",class:"mb-6"},vt=["src"],$t={class:"text-primary dark:text-darken-secondary text-center text-xl"},kt={class:"text-dark-primary dark:text-darken-secondary text-center"},Ut={class:"text-lg text-dark-primary dark:text-darken-secondary font-semibold text-center"},qt={key:0,class:"mt-8"},Nt={key:0},At={key:1},Vt={class:"font-bold"},_t={key:2},wt={class:"mt-8 border-t border-gray-200 dark:border-gray-800"},It={class:"mt-8 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},Lt={key:1,class:"mt-8"},Rt={key:3,class:"mt-8"},Y="onlineRegistration/",Ot={__name:"Form",setup(L){const S=X(),R=G(),T=ae(),B=z("layout.display").value=="dark"?z("assets.iconLight"):z("assets.icon"),q=W(null),I=W(!1),g=O({genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[],paymentGateways:[]}),e=O({registration:{},activeTab:"basic"}),a=()=>{I.value=!1,p()},v=async()=>{q.value=!0,await S.dispatch(Y+"preRequisite",{number:R.params.number}).then(l=>{e.preRequisites=l,q.value=!1}).catch(l=>{q.value=!1,T.push({name:"OnlineRegistration"})})},p=async()=>{q.value=!0,await S.dispatch(Y+"getRegistration",{number:R.params.number}).then(l=>{e.registration=l,e.registration.status.value=="pending"&&(e.activeTab="review"),e.registration.status.value=="pending"&&e.registration.fee.value>0&&N(),q.value=!1}).catch(l=>{q.value=!1,T.push({name:"OnlineRegistration"})})},N=async()=>{q.value=!0,await S.dispatch(Y+"paymentPreRequisite",{uuid:R.params.number}).then(l=>{g.paymentGateways=l.paymentGateways,q.value=!1}).catch(l=>{q.value=!1})},c=async()=>{let l=localStorage.getItem("authToken"),r=`/online-registrations/${R.params.number}/download?auth-token=${l}`;window.open(r)},o=()=>{e.activeTab="contact",p()},s=()=>{e.activeTab="upload",p()},h=()=>{e.activeTab="review",p()};return H(async()=>{await v(),await p()}),(l,r)=>{const t=w("BaseButton"),A=w("BaseAlert"),d=w("TabItem"),P=w("TabGroup"),k=w("BaseDataView"),F=w("BaseLoader");return $(),j(J,null,[m("div",gt,[m("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:r[0]||(r[0]=f=>i(T).push({name:"Dashboard"}))},[r[7]||(r[7]=m("i",{class:"fas fa-home mr-1"},null,-1)),U(" "+y(l.$trans("global.go_to",{attribute:l.$trans("dashboard.home")})),1)])]),m("div",ft,[m("a",yt,[m("img",{class:"h-16 w-auto",src:i(B),alt:""},null,8,vt)])]),u(F,{"is-loading":q.value},{default:b(()=>[m("div",null,[m("p",$t,y(l.$trans("student.online_registration.title")),1),m("p",kt,y(l.$trans("student.online_registration.subtitle")),1),m("div",Ut,y(l.$trans("student.online_registration.application_number"))+" : "+y(e.registration.applicationNumber),1)]),e.registration.uuid?($(),j("div",qt,[e.registration.status.value=="pending"?($(),j("div",Nt,[e.registration.fee.value>0?($(),j(J,{key:0},[e.registration.paymentStatus.value=="unpaid"?($(),_(t,{key:0,block:"",onClick:r[1]||(r[1]=f=>I.value=!0)},{default:b(()=>[U(y(l.$trans("student.online_registration.proceed_with_payment")),1)]),_:1})):($(),_(A,{key:1,size:"xs",design:"success"},{default:b(()=>[U(y(l.$trans("student.online_registration.pending_info")),1)]),_:1}))],64)):($(),_(A,{key:1,size:"xs",design:"success"},{default:b(()=>[U(y(l.$trans("student.online_registration.pending_info")),1)]),_:1}))])):e.registration.status.value=="rejected"?($(),j("div",At,[u(A,{size:"xs",design:"error"},{default:b(()=>[U(y(l.$trans("student.online_registration.rejected_info",{attribute:e.registration.rejectedAt.formatted}))+" ",1),m("p",Vt,y(e.registration.rejectionRemarks),1)]),_:1})])):e.registration.status.value=="approved"?($(),j("div",_t,[u(A,{size:"xs",design:"success"},{default:b(()=>[U(y(l.$trans("student.online_registration.approved_info")),1)]),_:1})])):V("",!0),m("div",wt,[e.registration.status.value!="pending"?($(),_(P,{key:0},{default:b(()=>[u(d,{first:"",label:l.$trans("student.online_registration.wizard.first_step"),onClick:r[2]||(r[2]=f=>e.activeTab="basic"),"is-active":e.activeTab=="basic"},null,8,["label","is-active"]),u(d,{label:l.$trans("student.online_registration.wizard.second_step"),onClick:r[3]||(r[3]=f=>e.activeTab="contact"),"is-active":e.activeTab=="contact"},null,8,["label","is-active"]),u(d,{label:l.$trans("student.online_registration.wizard.third_step"),onClick:r[4]||(r[4]=f=>e.activeTab="upload"),"is-active":e.activeTab=="upload"},null,8,["label","is-active"]),u(d,{last:"",label:l.$trans("student.online_registration.wizard.final_step"),onClick:r[5]||(r[5]=f=>e.activeTab="review"),"is-active":e.activeTab=="review"},null,8,["label","is-active"])]),_:1})):V("",!0),m("dl",It,[u(k,{label:l.$trans("contact.props.name")},{default:b(()=>[U(y(e.registration.contact.name),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.contact_number")},{default:b(()=>[U(y(e.registration.contact.contactNumber),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.email")},{default:b(()=>[U(y(e.registration.contact.email),1)]),_:1},8,["label"]),u(k,{label:l.$trans("academic.program.program")},{default:b(()=>{var f;return[U(y((f=e.registration.course.division.program)==null?void 0:f.name),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("academic.period.period")},{default:b(()=>[U(y(e.registration.period.name),1)]),_:1},8,["label"]),u(k,{label:l.$trans("academic.course.course")},{default:b(()=>[U(y(e.registration.course.name),1)]),_:1},8,["label"]),e.registration.status.value=="initiated"&&e.activeTab=="review"||e.registration.status.value=="pending"||e.registration.status.value=="approved"?($(),j(J,{key:0},[u(k,{label:l.$trans("contact.props.father_name")},{default:b(()=>[U(y(e.registration.contact.fatherName),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.mother_name")},{default:b(()=>[U(y(e.registration.contact.motherName),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.gender")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.gender)==null?void 0:f.label)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.birth_date")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.birthDate)==null?void 0:f.formatted)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.birth_place")},{default:b(()=>[U(y(e.registration.contact.birthPlace),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.nationality")},{default:b(()=>[U(y(e.registration.contact.nationality),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.mother_tongue")},{default:b(()=>[U(y(e.registration.contact.motherTongue),1)]),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.blood_group")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.bloodGroup)==null?void 0:f.label)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("contact.props.marital_status")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.maritalStatus)==null?void 0:f.label)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("contact.category.category")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.category)==null?void 0:f.name)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("contact.caste.caste")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.caste)==null?void 0:f.name)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("contact.religion.religion")},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.religion)==null?void 0:f.name)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("global.alternate",{attribute:l.$trans("contact.props.contact_number")})},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.alternateRecords)==null?void 0:f.contactNumber)||"-"),1)]}),_:1},8,["label"]),u(k,{label:l.$trans("global.alternate",{attribute:l.$trans("contact.props.email")})},{default:b(()=>{var f;return[U(y(((f=e.registration.contact.alternateRecords)==null?void 0:f.email)||"-"),1)]}),_:1},8,["label"]),u(k,{class:"col-span-1 sm:col-span-3",label:l.$trans("contact.props.present_address")},{default:b(()=>[U(y(e.registration.contact.presentAddressDisplay),1)]),_:1},8,["label"]),u(k,{class:"col-span-1 sm:col-span-3",label:l.$trans("contact.props.permanent_address")},{default:b(()=>[U(y(e.registration.contact.sameAsPresentAddress?e.registration.contact.presentAddressDisplay:e.registration.contact.permanentAddressDisplay),1)]),_:1},8,["label"])],64)):V("",!0)]),e.registration.status.value!="pending"&&e.registration.status.value!="rejected"&&e.registration.status.value!="approved"?($(),j("div",Lt,[e.activeTab=="basic"&&e.registration?($(),_(he,{key:0,registration:e.registration,"pre-requisites":e.preRequisites,onCompleted:o},null,8,["registration","pre-requisites"])):V("",!0),e.activeTab=="contact"&&e.registration?($(),_(Me,{key:1,registration:e.registration,onCompleted:s},null,8,["registration"])):V("",!0),e.activeTab=="upload"&&e.registration?($(),_(Qe,{key:2,registration:e.registration,onCompleted:p},null,8,["registration"])):V("",!0),e.activeTab=="upload"&&e.registration?($(),_(et,{key:3,registration:e.registration,onCompleted:h},null,8,["registration"])):V("",!0),e.activeTab=="review"&&e.registration?($(),_(lt,{key:4,registration:e.registration,onCompleted:p},null,8,["registration"])):V("",!0)])):V("",!0)]),e.registration.status.value!="initiated"?($(),j("div",Rt,[u(t,{block:"",onClick:c},{default:b(()=>[U(y(l.$trans("general.download")),1)]),_:1})])):V("",!0)])):V("",!0)]),_:1},8,["is-loading"]),u(bt,{visibility:I.value,"pre-requisites":g,registration:e.registration,onClose:r[6]||(r[6]=f=>I.value=!1),onCompleted:a},null,8,["visibility","pre-requisites","registration"])],64)}}};export{Ot as default};
