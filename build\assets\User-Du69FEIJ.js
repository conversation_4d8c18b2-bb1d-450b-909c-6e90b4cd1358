import{u as U,i as P,c as n,j,r as d,a as p,o as _,e as l,f as a,w as g,d as e,t as s,F as x,v as N,x as T,s as A}from"./app-BAwPsakn.js";const B={class:"overflow-hidden rounded-t-lg bg-black"},C={class:"mx-auto max-w-full px-4 pb-4 sm:px-6 lg:px-8"},V={class:"-mt-12 sm:-mt-16 sm:flex sm:items-end sm:space-x-5"},E={class:"flex items-center space-x-4"},F=["src"],H={class:"mt-16"},S={class:"text-secondary truncate text-2xl font-bold"},$={class:"text-md text-light-secondary"},z={class:"bg-black pt-6 sm:pt-2 2xl:pt-5"},D={class:"border-b border-gray-200 dark:border-gray-700"},L={class:"px-4 sm:px-6 lg:px-8"},O={class:"-mb-px flex flex-wrap","aria-label":"Tabs"},R={class:"dark:bg-dark-header flex bg-white sm:rounded-bl-lg sm:rounded-br-lg"},q={class:"mx-auto hidden w-1/3 border-r border-gray-200 px-4 py-6 dark:border-gray-700 sm:px-6 md:block lg:px-8"},G={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},I={class:"sm:col-span-1"},J={class:"text-sm font-medium text-gray-500"},K={class:"mt-1 truncate text-sm text-gray-900 dark:text-gray-400"},M={class:"sm:col-span-1"},Q={class:"text-sm font-medium text-gray-500"},W={class:"mt-1 truncate text-sm text-gray-900 dark:text-gray-400"},X={class:"sm:col-span-1"},Y={class:"text-sm font-medium text-gray-500"},Z={class:"mt-1 truncate text-sm text-gray-900 dark:text-gray-400"},ee={class:"mx-auto w-full px-4 py-6 sm:px-6 md:w-2/3 lg:px-8"},te={},oe=Object.assign(te,{__name:"User",setup(se){const c=U(),o=P(),h=n(()=>o.getters["auth/user/user"]("avatar")),i=n(()=>o.getters["auth/user/user"]("profile.name")),m=n(()=>o.getters["auth/user/user"]("email")),v=n(()=>o.getters["auth/user/user"]("username")),t=j("$trans"),f=[{name:t("user.profile.profile"),path:"UserProfile"},{name:t("user.profile.account"),path:"UserAccount"},{name:t("user.avatar"),path:"UserAvatar"},{name:t("user.preference.preference"),path:"UserPreference"},{name:t("auth.password.change_password"),path:"UserPassword"}];return(ae,u)=>{const b=d("PageHeader"),y=d("router-link"),k=d("router-view"),w=d("ParentTransition");return _(),p(x,null,[l(b,{title:a(t)(a(c).meta.label),navs:[{label:a(t)("user.user"),path:"User"}]},null,8,["title","navs"]),l(w,{appear:"",visibility:!0},{default:g(()=>[e("article",null,[e("div",B,[u[0]||(u[0]=e("div",null,[e("img",{class:"h-32 w-full object-cover lg:h-48",src:"/images/user-cover.jpeg",alt:""})],-1)),e("div",C,[e("div",V,[e("div",E,[e("img",{class:"h-24 w-24 rounded-full ring-4 ring-white sm:h-32 sm:w-32",src:h.value,alt:""},null,8,F),e("div",H,[e("h1",S,s(i.value),1),e("span",$,s(m.value),1)])])])])]),e("div",z,[e("div",D,[e("div",L,[e("nav",O,[(_(),p(x,null,N(f,r=>l(y,{key:r.name,to:{name:r.path},class:T([a(c).name===r.path?"dark:bg-dark-header rounded-t-lg bg-white text-black dark:border dark:border-b-0 dark:border-gray-700 dark:text-gray-400":"hover:text-secondary border-transparent text-white hover:border-gray-300 dark:text-gray-400 dark:hover:border-gray-700 dark:hover:text-gray-500","whitespace-nowrap px-4 py-2 text-sm font-medium text-gray-500"]),"aria-current":r.current?"page":void 0},{default:g(()=>[A(s(r.name),1)]),_:2},1032,["to","class","aria-current"])),64))])])])]),e("div",R,[e("div",q,[e("dl",G,[e("div",I,[e("dt",J,s(a(t)("user.profile.props.name")),1),e("dd",K,s(i.value),1)]),e("div",M,[e("dt",Q,s(a(t)("user.profile.props.email")),1),e("dd",W,s(m.value),1)]),e("div",X,[e("dt",Y,s(a(t)("user.profile.props.username")),1),e("dd",Z,s(v.value),1)])])]),e("div",ee,[l(k)])])])]),_:1})],64)}}});export{oe as default};
