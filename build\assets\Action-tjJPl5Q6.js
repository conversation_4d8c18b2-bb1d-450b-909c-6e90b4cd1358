import{H as F,l as f,r as i,q as N,o as v,w as u,d,e as r,f as s,s as g,t as _,J as E,u as O,a as P,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3"},k={name:"ExamForm"},w=Object.assign(k,{setup(x){const l={name:"",code:"",term:"",displayName:"",description:""},m="exam/",n=F(m),c=f({terms:[]}),t=f({...l}),b=o=>{Object.assign(c,o)},$=o=>{Object.assign(l,{...o,term:o.term.uuid}),Object.assign(t,E(l))};return(o,a)=>{const V=i("BaseInput"),y=i("BaseSelect"),U=i("BaseTextarea"),B=i("FormAction");return v(),N(B,{"pre-requisites":!0,onSetPreRequisites:b,"init-url":m,"init-form":l,form:t,setForm:$,redirect:"Exam"},{default:u(()=>[d("div",q,[d("div",A,[r(V,{type:"text",modelValue:t.name,"onUpdate:modelValue":a[0]||(a[0]=e=>t.name=e),name:"name",label:o.$trans("exam.props.name"),error:s(n).name,"onUpdate:error":a[1]||(a[1]=e=>s(n).name=e),autofocus:""},null,8,["modelValue","label","error"])]),d("div",H,[r(V,{type:"text",modelValue:t.code,"onUpdate:modelValue":a[2]||(a[2]=e=>t.code=e),name:"code",label:o.$trans("exam.props.code"),error:s(n).code,"onUpdate:error":a[3]||(a[3]=e=>s(n).code=e)},null,8,["modelValue","label","error"])]),d("div",T,[r(y,{modelValue:t.term,"onUpdate:modelValue":a[4]||(a[4]=e=>t.term=e),name:"term",label:o.$trans("exam.term.term"),"value-prop":"uuid",options:c.terms,error:s(n).term,"onUpdate:error":a[5]||(a[5]=e=>s(n).term=e)},{selectedOption:u(e=>{var p;return[g(_(e.value.name)+" ("+_(((p=e.value.division)==null?void 0:p.name)||o.$trans("general.all"))+") ",1)]}),listOption:u(e=>{var p;return[g(_(e.option.name)+" ("+_(((p=e.option.division)==null?void 0:p.name)||o.$trans("general.all"))+") ",1)]}),_:1},8,["modelValue","label","options","error"])]),d("div",R,[r(V,{type:"text",modelValue:t.displayName,"onUpdate:modelValue":a[6]||(a[6]=e=>t.displayName=e),name:"displayName",label:o.$trans("exam.props.display_name"),error:s(n).displayName,"onUpdate:error":a[7]||(a[7]=e=>s(n).displayName=e)},null,8,["modelValue","label","error"])]),d("div",S,[r(U,{modelValue:t.description,"onUpdate:modelValue":a[8]||(a[8]=e=>t.description=e),name:"description",label:o.$trans("exam.props.description"),error:s(n).description,"onUpdate:error":a[9]||(a[9]=e=>s(n).description=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),C={name:"ExamAction"},I=Object.assign(C,{setup(x){const l=O();return(m,n)=>{const c=i("PageHeaderAction"),t=i("PageHeader"),b=i("ParentTransition");return v(),P(j,null,[r(t,{title:m.$trans(s(l).meta.trans,{attribute:m.$trans(s(l).meta.label)}),navs:[{label:m.$trans("exam.exam"),path:"Exam"}]},{default:u(()=>[r(c,{name:"Exam",title:m.$trans("exam.exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(b,{appear:"",visibility:!0},{default:u(()=>[r(w)]),_:1})],64)}}});export{I as default};
