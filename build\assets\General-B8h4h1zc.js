import{u as D,j as C,H as w,c as S,l as f,r as l,a as B,o as I,e as n,f as o,w as u,d as i,s as U,t as y,F}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-4"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3"},T={name:"CalendarConfigGeneral"},$=Object.assign(T,{setup(k){const v=D(),s=C("$trans"),m="config/",a=w(m),_=S(()=>s("global.placeholder_info",{attribute:c.datePlaceholders})),c=f({datePlaceholders:""}),p={eventNumberPrefix:"",eventNumberSuffix:"",eventNumberDigit:0,showCelebrationInDashboard:!1,type:"calendar"},r=f({...p}),g=b=>{Object.assign(c,{datePlaceholders:b.datePlaceholders.map(e=>e.value).join(", ")})};return(b,e)=>{const x=l("PageHeader"),d=l("BaseInput"),N=l("BaseSwitch"),P=l("BaseAlert"),h=l("FormAction"),V=l("ParentTransition");return I(),B(F,null,[n(x,{title:o(s)(o(v).meta.label),navs:[{label:o(s)("calendar.calendar"),path:"Calendar"}]},null,8,["title","navs"]),n(V,{appear:"",visibility:!0},{default:u(()=>[n(h,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:g,"init-url":m,"data-fetch":"calendar","init-form":p,form:r,action:"store","stay-on":"",redirect:"Calendar"},{default:u(()=>[i("div",j,[i("div",q,[n(d,{type:"text",modelValue:r.eventNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=t=>r.eventNumberPrefix=t),name:"eventNumberPrefix",label:o(s)("calendar.event.config.props.number_prefix"),error:o(a).eventNumberPrefix,"onUpdate:error":e[1]||(e[1]=t=>o(a).eventNumberPrefix=t)},null,8,["modelValue","label","error"])]),i("div",A,[n(d,{type:"number",modelValue:r.eventNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=t=>r.eventNumberDigit=t),name:"eventNumberDigit",label:o(s)("calendar.event.config.props.number_digit"),error:o(a).eventNumberDigit,"onUpdate:error":e[3]||(e[3]=t=>o(a).eventNumberDigit=t)},null,8,["modelValue","label","error"])]),i("div",R,[n(d,{type:"text",modelValue:r.eventNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=t=>r.eventNumberSuffix=t),name:"eventNumberSuffix",label:o(s)("calendar.event.config.props.number_suffix"),error:o(a).eventNumberSuffix,"onUpdate:error":e[5]||(e[5]=t=>o(a).eventNumberSuffix=t)},null,8,["modelValue","label","error"])]),i("div",E,[n(N,{vertical:"",modelValue:r.showCelebrationInDashboard,"onUpdate:modelValue":e[6]||(e[6]=t=>r.showCelebrationInDashboard=t),name:"showCelebrationInDashboard",label:o(s)("calendar.config.props.show_celebration_in_dashboard"),error:o(a).showCelebrationInDashboard,"onUpdate:error":e[7]||(e[7]=t=>o(a).showCelebrationInDashboard=t)},null,8,["modelValue","label","error"])]),i("div",H,[n(P,{size:"xs",design:"info"},{default:u(()=>[U(y(_.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{$ as default};
