import{u as M,g as T,l as k,n as z,r as y,q,o as V,w as r,d as l,e as a,b as _,s as v,t as c,a as F,f as p,h as ae,j as O,m as H,H as se,K as W,y as R,z as fe,F as ee,v as te,A as ye,V as Ve}from"./app-BAwPsakn.js";import{u as ve}from"./useColumnVisibility-BaCp0EnB.js";import{i as $e,t as qe,a as Ne}from"./table-FwhM-Z75.js";const _e={class:"grid grid-cols-3 gap-6"},Se={class:"col-span-3 sm:col-span-1"},he={class:"col-span-3 sm:col-span-1"},Ie={class:"mt-4 grid grid-cols-3 gap-6"},Ue={class:"col-span-3 sm:col-span-1"},De={class:"col-span-3 sm:col-span-1"},ke={class:"col-span-3 sm:col-span-1"},Be={class:"col-span-3 sm:col-span-1"},Ae={class:"col-span-3 sm:col-span-1"},we={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},Le={class:"col-span-3 sm:col-span-1"},Ce={class:"col-span-3 sm:col-span-1"},Ge={class:"col-span-3 sm:col-span-1"},Te={class:"col-span-3 sm:col-span-1"},Fe={class:"col-span-3 sm:col-span-1"},Re={class:"col-span-3 sm:col-span-1"},Pe={class:"col-span-3 sm:col-span-1"},je={key:0,class:"col-span-3 sm:col-span-1"},Oe={class:"col-span-3 sm:col-span-1"},He={class:"col-span-3 sm:col-span-1"},Me={class:"col-span-3 sm:col-span-1"},ze={class:"col-span-3 sm:col-span-1"},We={class:"col-span-3 sm:col-span-1"},Ke={class:"col-span-3 sm:col-span-1"},xe={class:"col-span-3 sm:col-span-1"},Je={class:"col-span-3 sm:col-span-1"},Qe={class:"col-span-3 sm:col-span-1"},Xe={class:"col-span-3 sm:col-span-1"},Ye={class:"col-span-3 sm:col-span-1"},Ze={class:"col-span-3 sm:col-span-1"},et={class:"col-span-3 sm:col-span-1"},tt={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(C,{emit:I}){const m=M(),B=I,N=C,A={codeNumber:"",firstName:"",lastName:"",fatherName:"",motherName:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",gender:"",contactNumber:"",email:"",birthStartDate:"",birthEndDate:"",admissionStartDate:"",admissionEndDate:"",startDate:"",endDate:"",status:"studying",batches:[],bloodGroups:[],maritalStatuses:[],enrollmentType:"",religions:[],categories:[],castes:[],tagsIncluded:[],tagsExcluded:[],groups:[],address:""},i=T("contact.uniqueIdNumber1Label"),w=T("contact.uniqueIdNumber2Label"),U=T("contact.uniqueIdNumber3Label"),g=T("contact.uniqueIdNumber4Label"),b=T("contact.uniqueIdNumber5Label"),e=k({...A}),f=k({genders:N.preRequisites.genders,bloodGroups:N.preRequisites.bloodGroups,maritalStatuses:N.preRequisites.maritalStatuses,statuses:N.preRequisites.statuses,enrollmentTypes:N.preRequisites.enrollmentTypes,groups:N.preRequisites.groups}),$=T("periods").value.map(u=>({name:u.name,uuid:u.uuid})),d=k({isLoaded:!(m.query.batches||m.query.religions||m.query.castes||m.query.categories||m.query.tagsIncluded||m.query.tagsExcluded||m.query.groups)});return z(async()=>{d.batches=m.query.batches?m.query.batches.split(","):[],d.bloodGroups=m.query.bloodGroups?m.query.bloodGroups.split(","):[],d.maritalStatuses=m.query.maritalStatuses?m.query.maritalStatuses.split(","):[],d.religions=m.query.religions?m.query.religions.split(","):[],d.categories=m.query.categories?m.query.categories.split(","):[],d.castes=m.query.castes?m.query.castes.split(","):[],d.tagsIncluded=m.query.tagsIncluded?m.query.tagsIncluded.split(","):[],d.tagsExcluded=m.query.tagsExcluded?m.query.tagsExcluded.split(","):[],d.groups=m.query.groups?m.query.groups.split(","):[],d.isLoaded=!0}),(u,s)=>{const o=y("BaseInput"),E=y("BaseSelectSearch"),S=y("BaseSelect"),G=y("DatePicker"),L=y("FilterForm");return V(),q(L,{"init-form":A,form:e,multiple:["batches","bloodGroups","maritalStatuses","religions","castes","categories","tagsIncluded","tagsExcluded","groups"],onHide:s[34]||(s[34]=t=>B("hide"))},{default:r(()=>[l("div",_e,[l("div",Se,[a(o,{type:"text",modelValue:e.codeNumber,"onUpdate:modelValue":s[0]||(s[0]=t=>e.codeNumber=t),name:"codeNumber",label:u.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),l("div",he,[d.isLoaded?(V(),q(E,{key:0,multiple:"",name:"batches",label:u.$trans("global.select",{attribute:u.$trans("academic.batch.batch")}),modelValue:e.batches,"onUpdate:modelValue":s[1]||(s[1]=t=>e.batches=t),"value-prop":"uuid","init-search":d.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:r(t=>[v(c(t.value.course.nameWithTerm)+" "+c(t.value.name),1)]),listOption:r(t=>[v(c(t.option.course.nameWithTerm)+" "+c(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):_("",!0)])]),l("div",Ie,[l("div",Ue,[a(o,{type:"text",modelValue:e.firstName,"onUpdate:modelValue":s[2]||(s[2]=t=>e.firstName=t),name:"firstName",label:u.$trans("contact.props.first_name")},null,8,["modelValue","label"])]),l("div",De,[a(o,{type:"text",modelValue:e.lastName,"onUpdate:modelValue":s[3]||(s[3]=t=>e.lastName=t),name:"lastName",label:u.$trans("contact.props.last_name")},null,8,["modelValue","label"])]),l("div",ke,[a(o,{type:"text",modelValue:e.fatherName,"onUpdate:modelValue":s[4]||(s[4]=t=>e.fatherName=t),name:"fatherName",label:u.$trans("contact.props.father_name")},null,8,["modelValue","label"])]),l("div",Be,[a(o,{type:"text",modelValue:e.motherName,"onUpdate:modelValue":s[5]||(s[5]=t=>e.motherName=t),name:"motherName",label:u.$trans("contact.props.mother_name")},null,8,["modelValue","label"])]),l("div",Ae,[a(o,{type:"text",modelValue:e.uniqueIdNumber1,"onUpdate:modelValue":s[6]||(s[6]=t=>e.uniqueIdNumber1=t),name:"uniqueIdNumber1",label:p(i)},null,8,["modelValue","label"])]),l("div",we,[a(o,{type:"text",modelValue:e.uniqueIdNumber2,"onUpdate:modelValue":s[7]||(s[7]=t=>e.uniqueIdNumber2=t),name:"uniqueIdNumber2",label:p(w)},null,8,["modelValue","label"])]),l("div",Ee,[a(o,{type:"text",modelValue:e.uniqueIdNumber3,"onUpdate:modelValue":s[8]||(s[8]=t=>e.uniqueIdNumber3=t),name:"uniqueIdNumber3",label:p(U)},null,8,["modelValue","label"])]),l("div",Le,[a(o,{type:"text",modelValue:e.uniqueIdNumber4,"onUpdate:modelValue":s[9]||(s[9]=t=>e.uniqueIdNumber4=t),name:"uniqueIdNumber4",label:p(g)},null,8,["modelValue","label"])]),l("div",Ce,[a(o,{type:"text",modelValue:e.uniqueIdNumber5,"onUpdate:modelValue":s[10]||(s[10]=t=>e.uniqueIdNumber5=t),name:"uniqueIdNumber5",label:p(b)},null,8,["modelValue","label"])]),l("div",Ge,[a(o,{type:"text",modelValue:e.contactNumber,"onUpdate:modelValue":s[11]||(s[11]=t=>e.contactNumber=t),name:"contactNumber",label:u.$trans("contact.props.contact_number")},null,8,["modelValue","label"])]),l("div",Te,[a(S,{modelValue:e.gender,"onUpdate:modelValue":s[12]||(s[12]=t=>e.gender=t),name:"gender",label:u.$trans("contact.props.gender"),options:f.genders},null,8,["modelValue","label","options"])]),l("div",Fe,[a(S,{multiple:"",modelValue:e.bloodGroups,"onUpdate:modelValue":s[13]||(s[13]=t=>e.bloodGroups=t),name:"bloodGroups",label:u.$trans("contact.props.blood_group"),options:f.bloodGroups},null,8,["modelValue","label","options"])]),l("div",Re,[a(S,{multiple:"",modelValue:e.maritalStatuses,"onUpdate:modelValue":s[14]||(s[14]=t=>e.maritalStatuses=t),name:"maritalStatuses",label:u.$trans("contact.props.marital_status"),options:f.maritalStatuses},null,8,["modelValue","label","options"])]),l("div",Pe,[a(S,{modelValue:e.status,"onUpdate:modelValue":s[15]||(s[15]=t=>e.status=t),name:"status",label:u.$trans("student.props.status"),options:f.statuses},null,8,["modelValue","label","options"])]),e.status=="alumni"?(V(),F("div",je,[a(S,{modelValue:e.alumniPeriod,"onUpdate:modelValue":s[16]||(s[16]=t=>e.alumniPeriod=t),name:"alumniPeriod",label:u.$trans("student.alumni.period"),"label-prop":"name","value-prop":"uuid",options:p($)},null,8,["modelValue","label","options"])])):_("",!0),l("div",Oe,[d.isLoaded?(V(),q(E,{key:0,multiple:"",name:"religions",label:u.$trans("global.select",{attribute:u.$trans("contact.religion.religion")}),modelValue:e.religions,"onUpdate:modelValue":s[17]||(s[17]=t=>e.religions=t),"label-props":"name","value-prop":"uuid","init-search":d.religions,"search-action":"option/list","additional-search-query":{type:"religion"}},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",He,[d.isLoaded?(V(),q(E,{key:0,multiple:"",name:"castes",label:u.$trans("global.select",{attribute:u.$trans("contact.caste.caste")}),modelValue:e.castes,"onUpdate:modelValue":s[18]||(s[18]=t=>e.castes=t),"label-props":"name","value-prop":"uuid","init-search":d.castes,"search-action":"option/list","additional-search-query":{type:"member_caste"}},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",Me,[d.isLoaded?(V(),q(E,{key:0,multiple:"",name:"categories",label:u.$trans("global.select",{attribute:u.$trans("contact.category.category")}),modelValue:e.categories,"onUpdate:modelValue":s[19]||(s[19]=t=>e.categories=t),"label-props":"name","value-prop":"uuid","init-search":d.categories,"search-action":"option/list","additional-search-query":{type:"member_category"}},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",ze,[a(S,{modelValue:e.enrollmentType,"onUpdate:modelValue":s[20]||(s[20]=t=>e.enrollmentType=t),name:"enrollmentType",label:u.$trans("student.enrollment_type.enrollment_type"),"label-prop":"name","value-prop":"uuid",options:f.enrollmentTypes},null,8,["modelValue","label","options"])]),l("div",We,[a(o,{type:"text",modelValue:e.email,"onUpdate:modelValue":s[21]||(s[21]=t=>e.email=t),name:"email",label:u.$trans("contact.props.email")},null,8,["modelValue","label"])]),l("div",Ke,[a(G,{start:e.birthStartDate,"onUpdate:start":s[22]||(s[22]=t=>e.birthStartDate=t),end:e.birthEndDate,"onUpdate:end":s[23]||(s[23]=t=>e.birthEndDate=t),name:"birthDateBetween",as:"range",label:u.$trans("global.date_between",{attribute:u.$trans("contact.props.birth_date")})},null,8,["start","end","label"])]),l("div",xe,[a(G,{start:e.admissionStartDate,"onUpdate:start":s[24]||(s[24]=t=>e.admissionStartDate=t),end:e.admissionEndDate,"onUpdate:end":s[25]||(s[25]=t=>e.admissionEndDate=t),name:"admissionDateBetween",as:"range",label:u.$trans("global.date_between",{attribute:u.$trans("student.admission.props.date")})},null,8,["start","end","label"])]),l("div",Je,[a(G,{start:e.leavingStartDate,"onUpdate:start":s[26]||(s[26]=t=>e.leavingStartDate=t),end:e.leavingEndDate,"onUpdate:end":s[27]||(s[27]=t=>e.leavingEndDate=t),name:"leavingDateBetween",as:"range",label:u.$trans("global.date_between",{attribute:u.$trans("student.transfer.props.date")})},null,8,["start","end","label"])]),l("div",Qe,[a(G,{start:e.startDate,"onUpdate:start":s[28]||(s[28]=t=>e.startDate=t),end:e.endDate,"onUpdate:end":s[29]||(s[29]=t=>e.endDate=t),name:"dateBetween",as:"range",label:u.$trans("global.date_between",{attribute:u.$trans("general.created_at")})},null,8,["start","end","label"])]),l("div",Xe,[d.isLoaded?(V(),q(E,{key:0,tags:"",name:"tagsIncluded",label:u.$trans("general.tags_included"),modelValue:e.tagsIncluded,"onUpdate:modelValue":s[30]||(s[30]=t=>e.tagsIncluded=t),"init-search":d.tagsIncluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",Ye,[d.isLoaded?(V(),q(E,{key:0,tags:"",name:"tagsExcluded",label:u.$trans("general.tags_excluded"),modelValue:e.tagsExcluded,"onUpdate:modelValue":s[31]||(s[31]=t=>e.tagsExcluded=t),"init-search":d.tagsExcluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",Ze,[a(S,{multiple:"",modelValue:e.groups,"onUpdate:modelValue":s[32]||(s[32]=t=>e.groups=t),name:"groups","label-prop":"name","value-prop":"uuid",label:u.$trans("student.group.group"),options:f.groups},null,8,["modelValue","label","options"])]),l("div",et,[a(o,{type:"text",modelValue:e.address,"onUpdate:modelValue":s[33]||(s[33]=t=>e.address=t),name:"address",label:u.$trans("contact.props.address.address")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},at={class:"grid grid-cols-3 gap-6"},st={class:"col-span-3 sm:col-span-1"},lt={class:"mt-4 grid grid-cols-3 gap-6"},ot={class:"col-span-3 sm:col-span-1"},nt={class:"col-span-3 sm:col-span-1"},rt={class:"col-span-3 sm:col-span-1"},it={class:"col-span-3 sm:col-span-1"},dt={class:"col-span-3 sm:col-span-1"},ut={class:"col-span-3 sm:col-span-1"},mt={class:"col-span-3 sm:col-span-1"},pt={class:"col-span-3 sm:col-span-1"},ct={class:"col-span-3 sm:col-span-1"},bt={class:"col-span-3 sm:col-span-1"},gt={class:"col-span-3 sm:col-span-1"},ft={class:"col-span-3 sm:col-span-1"},yt={class:"col-span-3 sm:col-span-1"},Vt={class:"col-span-3 sm:col-span-1"},vt={class:"col-span-3 sm:col-span-1"},$t={class:"col-span-3 sm:col-span-1"},qt={class:"col-span-3 sm:col-span-1"},Nt={class:"col-span-3 sm:col-span-1"},_t={__name:"AlumniFilter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(C,{emit:I}){const m=M();ae();const B=I,N=C,A={codeNumber:"",firstName:"",lastName:"",fatherName:"",motherName:"",gender:"",contactNumber:"",email:"",birthStartDate:"",birthEndDate:"",admissionStartDate:"",admissionEndDate:"",startDate:"",endDate:"",status:"alumni",bloodGroups:[],maritalStatuses:[],religions:[],categories:[],castes:[],tagsIncluded:[],tagsExcluded:[]},i=k({...A}),w=k({genders:N.preRequisites.genders,bloodGroups:N.preRequisites.bloodGroups,maritalStatuses:N.preRequisites.maritalStatuses,statuses:N.preRequisites.statuses}),U=T("periods").value.map(b=>({name:b.name,uuid:b.uuid})),g=k({isLoaded:!(m.query.religions||m.query.castes||m.query.categories||m.query.tagsIncluded||m.query.tagsExcluded)});return z(async()=>{g.bloodGroups=m.query.bloodGroups?m.query.bloodGroups.split(","):[],g.maritalStatuses=m.query.maritalStatuses?m.query.maritalStatuses.split(","):[],g.religions=m.query.religions?m.query.religions.split(","):[],g.categories=m.query.categories?m.query.categories.split(","):[],g.castes=m.query.castes?m.query.castes.split(","):[],g.tagsIncluded=m.query.tagsIncluded?m.query.tagsIncluded.split(","):[],g.tagsExcluded=m.query.tagsExcluded?m.query.tagsExcluded.split(","):[],g.isLoaded=!0}),(b,e)=>{const f=y("BaseInput"),$=y("BaseSelect"),d=y("BaseSelectSearch"),u=y("DatePicker"),s=y("FilterForm");return V(),q(s,{"init-form":A,form:i,multiple:["bloodGroups","maritalStatuses","religions","castes","categories","tagsIncluded","tagsExcluded"],"default-query":{status:"alumni"},onHide:e[22]||(e[22]=o=>B("hide"))},{default:r(()=>[l("div",at,[l("div",st,[a(f,{type:"text",modelValue:i.codeNumber,"onUpdate:modelValue":e[0]||(e[0]=o=>i.codeNumber=o),name:"codeNumber",label:b.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])])]),l("div",lt,[l("div",ot,[a(f,{type:"text",modelValue:i.firstName,"onUpdate:modelValue":e[1]||(e[1]=o=>i.firstName=o),name:"firstName",label:b.$trans("contact.props.first_name")},null,8,["modelValue","label"])]),l("div",nt,[a(f,{type:"text",modelValue:i.lastName,"onUpdate:modelValue":e[2]||(e[2]=o=>i.lastName=o),name:"lastName",label:b.$trans("contact.props.last_name")},null,8,["modelValue","label"])]),l("div",rt,[a(f,{type:"text",modelValue:i.fatherName,"onUpdate:modelValue":e[3]||(e[3]=o=>i.fatherName=o),name:"fatherName",label:b.$trans("contact.props.father_name")},null,8,["modelValue","label"])]),l("div",it,[a(f,{type:"text",modelValue:i.motherName,"onUpdate:modelValue":e[4]||(e[4]=o=>i.motherName=o),name:"motherName",label:b.$trans("contact.props.mother_name")},null,8,["modelValue","label"])]),l("div",dt,[a(f,{type:"text",modelValue:i.contactNumber,"onUpdate:modelValue":e[5]||(e[5]=o=>i.contactNumber=o),name:"contactNumber",label:b.$trans("contact.props.contact_number")},null,8,["modelValue","label"])]),l("div",ut,[a($,{modelValue:i.gender,"onUpdate:modelValue":e[6]||(e[6]=o=>i.gender=o),name:"gender",label:b.$trans("contact.props.gender"),options:w.genders},null,8,["modelValue","label","options"])]),l("div",mt,[a($,{multiple:"",modelValue:i.bloodGroups,"onUpdate:modelValue":e[7]||(e[7]=o=>i.bloodGroups=o),name:"bloodGroups",label:b.$trans("contact.props.blood_group"),options:w.bloodGroups},null,8,["modelValue","label","options"])]),l("div",pt,[a($,{multiple:"",modelValue:i.maritalStatuses,"onUpdate:modelValue":e[8]||(e[8]=o=>i.maritalStatuses=o),name:"maritalStatuses",label:b.$trans("contact.props.marital_status"),options:w.maritalStatuses},null,8,["modelValue","label","options"])]),l("div",ct,[a($,{modelValue:i.alumniPeriod,"onUpdate:modelValue":e[9]||(e[9]=o=>i.alumniPeriod=o),name:"alumniPeriod",label:b.$trans("student.alumni.period"),"label-prop":"name","value-prop":"uuid",options:p(U)},null,8,["modelValue","label","options"])]),l("div",bt,[g.isLoaded?(V(),q(d,{key:0,multiple:"",name:"religions",label:b.$trans("global.select",{attribute:b.$trans("contact.religion.religion")}),modelValue:i.religions,"onUpdate:modelValue":e[10]||(e[10]=o=>i.religions=o),"label-props":"name","value-prop":"uuid","init-search":g.religions,"search-action":"option/list","additional-search-query":{type:"religion"}},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",gt,[g.isLoaded?(V(),q(d,{key:0,multiple:"",name:"castes",label:b.$trans("global.select",{attribute:b.$trans("contact.caste.caste")}),modelValue:i.castes,"onUpdate:modelValue":e[11]||(e[11]=o=>i.castes=o),"label-props":"name","value-prop":"uuid","init-search":g.castes,"search-action":"option/list","additional-search-query":{type:"member_caste"}},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",ft,[g.isLoaded?(V(),q(d,{key:0,multiple:"",name:"categories",label:b.$trans("global.select",{attribute:b.$trans("contact.category.category")}),modelValue:i.categories,"onUpdate:modelValue":e[12]||(e[12]=o=>i.categories=o),"label-props":"name","value-prop":"uuid","init-search":g.categories,"search-action":"option/list","additional-search-query":{type:"member_category"}},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",yt,[a(f,{type:"text",modelValue:i.email,"onUpdate:modelValue":e[13]||(e[13]=o=>i.email=o),name:"email",label:b.$trans("contact.props.email")},null,8,["modelValue","label"])]),l("div",Vt,[a(u,{start:i.birthStartDate,"onUpdate:start":e[14]||(e[14]=o=>i.birthStartDate=o),end:i.birthEndDate,"onUpdate:end":e[15]||(e[15]=o=>i.birthEndDate=o),name:"birthDateBetween",as:"range",label:b.$trans("global.date_between",{attribute:b.$trans("contact.props.birth_date")})},null,8,["start","end","label"])]),l("div",vt,[a(u,{start:i.admissionStartDate,"onUpdate:start":e[16]||(e[16]=o=>i.admissionStartDate=o),end:i.admissionEndDate,"onUpdate:end":e[17]||(e[17]=o=>i.admissionEndDate=o),name:"admissionDateBetween",as:"range",label:b.$trans("global.date_between",{attribute:b.$trans("student.admission.props.date")})},null,8,["start","end","label"])]),l("div",$t,[a(u,{start:i.startDate,"onUpdate:start":e[18]||(e[18]=o=>i.startDate=o),end:i.endDate,"onUpdate:end":e[19]||(e[19]=o=>i.endDate=o),name:"dateBetween",as:"range",label:b.$trans("global.date_between",{attribute:b.$trans("general.created_at")})},null,8,["start","end","label"])]),l("div",qt,[g.isLoaded?(V(),q(d,{key:0,tags:"",name:"tagsIncluded",label:b.$trans("general.tags_included"),modelValue:i.tagsIncluded,"onUpdate:modelValue":e[20]||(e[20]=o=>i.tagsIncluded=o),"init-search":g.tagsIncluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),l("div",Nt,[g.isLoaded?(V(),q(d,{key:0,tags:"",name:"tagsExcluded",label:b.$trans("general.tags_excluded"),modelValue:i.tagsExcluded,"onUpdate:modelValue":e[21]||(e[21]=o=>i.tagsExcluded=o),"init-search":g.tagsExcluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):_("",!0)])])]),_:1},8,["form"])}}},St={class:"grid grid-cols-3 gap-6"},ht={class:"col-span-3 sm:col-span-1"},It={class:"mt-4 grid grid-cols-3 gap-6"},Ut={class:"col-span-3 sm:col-span-1"},Dt={name:"StudentUpdateBulkTag"},kt=Object.assign(Dt,{props:{selected:{type:Object,required:!0},selectAll:{type:Boolean,required:!0},selectedStudents:{type:Array,required:!0}},emits:["completed"],setup(C,{emit:I}){const m=O("emitter"),B=I,N=C,A={action:"assign",tags:[],selectAll:!1,students:[]},i="student/",w=H(!1),U=se(i),g=k({...A}),b=k({isLoaded:!0,tags:[]}),e=async()=>{g.selectAll=!1,m.emit("listItems"),B("completed")};return W([()=>N.selectAll,()=>N.selectedStudents],([f,$])=>{g.selectAll=f,g.students=$},{immediate:!0}),(f,$)=>{const d=y("BaseRadioGroup"),u=y("BaseSelectSearch"),s=y("FormAction"),o=y("BaseCard"),E=y("ParentTransition");return C.selected.items.length>0&&p(R)("student:edit")?(V(),q(E,{key:0,appear:"",visibility:!0},{default:r(()=>[a(o,{class:"mt-4","is-loading":w.value},{title:r(()=>[v(c(f.$trans("global.update",{attribute:f.$trans("general.tag")})),1)]),default:r(()=>[a(s,{"no-card":"","keep-adding":!1,"init-url":i,action:"updateBulkTag","init-form":A,form:g,"after-submit":e},{default:r(()=>[l("div",St,[l("div",ht,[a(d,{options:[{label:f.$trans("general.assign"),value:"assign"},{label:f.$trans("general.remove"),value:"remove"}],name:"action",modelValue:g.action,"onUpdate:modelValue":$[0]||($[0]=S=>g.action=S),error:p(U).action,"onUpdate:error":$[1]||($[1]=S=>p(U).action=S),horizontal:""},null,8,["options","modelValue","error"])])]),l("div",It,[l("div",Ut,[b.isLoaded?(V(),q(u,{key:0,tags:"",name:"tags",placeholder:f.$trans("global.select",{attribute:f.$trans("general.tag")}),modelValue:g.tags,"onUpdate:modelValue":$[2]||($[2]=S=>g.tags=S),error:p(U).tags,"onUpdate:error":$[3]||($[3]=S=>p(U).tags=S),"init-search":b.tags,"search-action":"tag/list"},null,8,["placeholder","modelValue","error","init-search"])):_("",!0)])])]),_:1},8,["form"])]),_:1},8,["is-loading"])]),_:1})):_("",!0)}}}),Bt={class:"grid grid-cols-3 gap-6"},At={class:"col-span-3 sm:col-span-1"},wt={class:"mt-4 grid grid-cols-3 gap-6"},Et={class:"col-span-3 sm:col-span-1"},Lt={name:"StudentUpdateBulkGroup"},Ct=Object.assign(Lt,{props:{selected:{type:Object,required:!0},selectAll:{type:Boolean,required:!0},selectedStudents:{type:Array,required:!0}},emits:["completed"],setup(C,{emit:I}){const m=O("emitter"),B=I,N=C,A={action:"assign",groups:[],selectAll:!1,students:[]},i="student/",w=H(!1),U=se(i),g=k({...A}),b=k({isLoaded:!0,groups:[]}),e=async()=>{g.selectAll=!1,m.emit("listItems"),B("completed")};return W([()=>N.selectAll,()=>N.selectedStudents],([f,$])=>{g.selectAll=f,g.students=$},{immediate:!0}),(f,$)=>{const d=y("BaseRadioGroup"),u=y("BaseSelectSearch"),s=y("FormAction"),o=y("BaseCard"),E=y("ParentTransition");return C.selected.items.length>0&&p(R)("student:edit")?(V(),q(E,{key:0,appear:"",visibility:!0},{default:r(()=>[a(o,{class:"mt-4","is-loading":w.value},{title:r(()=>[v(c(f.$trans("global.update",{attribute:f.$trans("student.group.group")})),1)]),default:r(()=>[a(s,{"no-card":"","keep-adding":!1,"init-url":i,action:"updateBulkGroup","init-form":A,form:g,"after-submit":e},{default:r(()=>[l("div",Bt,[l("div",At,[a(d,{options:[{label:f.$trans("general.assign"),value:"assign"},{label:f.$trans("general.remove"),value:"remove"}],name:"action",modelValue:g.action,"onUpdate:modelValue":$[0]||($[0]=S=>g.action=S),error:p(U).action,"onUpdate:error":$[1]||($[1]=S=>p(U).action=S),horizontal:""},null,8,["options","modelValue","error"])])]),l("div",wt,[l("div",Et,[b.isLoaded?(V(),q(u,{key:0,multiple:"",name:"groups",placeholder:f.$trans("global.select",{attribute:f.$trans("student.group.group")}),modelValue:g.groups,"onUpdate:modelValue":$[2]||($[2]=S=>g.groups=S),error:p(U).groups,"onUpdate:error":$[3]||($[3]=S=>p(U).groups=S),"init-search":b.groups,"search-action":"option/list","additional-search-query":{type:"student_group"}},null,8,["placeholder","modelValue","error","init-search"])):_("",!0)])])]),_:1},8,["form"])]),_:1},8,["is-loading"])]),_:1})):_("",!0)}}}),Gt={class:"grid grid-cols-1 gap-4 px-4 pt-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},Tt={key:0},Ft={key:1,class:""},Rt=["onClick"],Pt={class:"text-xs text-gray-500"},jt={class:"truncate text-sm text-gray-500"},Ot={class:"text-xs text-gray-500"},Ht={key:0},Mt={key:1},zt={class:"text-sm text-gray-500"},Wt=["onClick"],Kt={name:"StudentList"},Xt=Object.assign(Kt,{setup(C){const I=M(),m=ae(),B=O("emitter"),N=O("$trans"),{applyVisibility:A}=ve("Student");let i=["filter","view"];R("student:config")&&i.push("config");let w=[];R("student:export")&&(w=["print","pdf","excel"]),R("student:create")&&w.unshift("import");let U=[];I.name=="StudentAlumni"&&(U=[{path:"StudentList",label:N("student.student")}]);const g="student/",b=H(!1),e=H(!1),f=k({selectAll:!1,selectedStudents:[],createUserAccount:!1}),$=k({genders:[]}),d=k({...$e}),u=k({}),s=L=>{L.headers=A(L.headers),Object.assign(u,L),d.pageItems=u.data.map(t=>t.uuid)},o=()=>{d.items=[],d.pageItems=[],d.all=!1},E=L=>{Object.assign($,L)},S=()=>{d.global=!d.global,f.selectAll=d.global},G=L=>{window.open(`/app/students/${L}`,"_blank")};return W(()=>[d.items,d.pageItems],([L,t],[K,x])=>{f.selectedStudents=L,d.all=Ne(d)}),z(()=>{I.name=="StudentAlumni"&&m.push({name:I.name,query:{status:"alumni"}})}),(L,t)=>{const K=y("PageHeaderAction"),x=y("PageHeader"),le=y("BaseAlert"),oe=y("BaseImport"),P=y("ParentTransition"),ne=y("CardView"),re=y("Pagination"),ie=y("CardList"),de=y("BaseArrayCheckbox"),ue=y("BaseCheckbox"),h=y("DataCell"),j=y("TextMuted"),J=y("BaseBadge"),Q=y("FloatingMenuItem"),me=y("FloatingMenu"),pe=y("DataRow"),ce=y("DataTable"),be=y("ListItem"),ge=fe("tooltip");return V(),q(be,{"pre-requisites":!0,onSetPreRequisites:E,"init-url":g,onSetItems:s},{header:r(()=>[a(x,{title:p(N)(p(I).meta.label),navs:p(U)},{default:r(()=>[a(K,{url:"students/",name:"Student",title:p(N)(p(I).meta.label),actions:p(i),"dropdown-actions":p(w),headers:u.headers,onToggleFilter:t[0]||(t[0]=n=>b.value=!b.value),onToggleImport:t[1]||(t[1]=n=>e.value=!e.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"])]),import:r(()=>[p(I).name!="StudentAlumni"?(V(),q(P,{key:0,appear:"",visibility:e.value},{default:r(()=>[a(oe,{path:"students/import","history-path":"students/import/history","additional-option":{createUserAccount:f.createUserAccount},onCancelled:t[2]||(t[2]=n=>e.value=!1),onHide:t[3]||(t[3]=n=>e.value=!1),onCompleted:t[4]||(t[4]=n=>p(B).emit("listItems"))},{header:r(()=>[a(le,{size:"xs",design:"info"},{default:r(()=>[v(c(p(N)("general.import_info")),1)]),_:1})]),option:r(()=>t[14]||(t[14]=[])),_:1},8,["additional-option"])]),_:1},8,["visibility"])):_("",!0)]),filter:r(()=>[a(P,{appear:"",visibility:b.value},{default:r(()=>[p(I).name!="StudentAlumni"?(V(),q(tt,{key:0,"pre-requisites":$,onRefresh:t[5]||(t[5]=n=>p(B).emit("listItems")),onHide:t[6]||(t[6]=n=>b.value=!1)},null,8,["pre-requisites"])):_("",!0),p(I).name=="StudentAlumni"?(V(),q(_t,{key:1,"pre-requisites":$,onRefresh:t[7]||(t[7]=n=>p(B).emit("listItems")),onHide:t[8]||(t[8]=n=>b.value=!1)},null,8,["pre-requisites"])):_("",!0)]),_:1},8,["visibility"])]),default:r(()=>[p(I).query.view!="list"?(V(),q(P,{key:0,appear:"",visibility:!0},{default:r(()=>[a(ie,{header:u.headers,meta:u.meta,module:"student"},{actionButton:r(()=>t[16]||(t[16]=[])),default:r(()=>[l("div",Gt,[(V(!0),F(ee,null,te(u.data,n=>(V(),q(ne,{key:n.uuid,"img-src":n.photo,path:{name:"StudentShow",params:{uuid:n.uuid}}},{title:r(()=>[n.isAlumni?ye((V(),F("span",Tt,t[15]||(t[15]=[l("i",{class:"fas fa-graduation-cap"},null,-1)]))),[[ge,p(N)("student.alumni.alumni")]]):_("",!0),v(" "+c(n.name)+" ",1),n.codeNumber?(V(),F("span",Ft,"("+c(n.codeNumber)+")",1)):_("",!0),l("i",{class:"fas fa-up-right-from-square ml-2 cursor-pointer",onClick:Ve(D=>G(n.uuid),["stop"])},null,8,Rt)]),default:r(()=>{var D;return[l("p",Pt,c((D=n.age)==null?void 0:D.short),1),l("p",jt,c(n.courseName+" "+n.batchName),1),l("p",Ot,[n.fatherName?(V(),F("span",Ht,c(n.fatherName),1)):n.motherName?(V(),F("span",Mt,c(n.motherName),1)):_("",!0)]),l("p",zt,c(n.contactNumber),1)]}),_:2},1032,["img-src","path"]))),128))]),l("div",null,[a(re,{"card-view":"",meta:u.meta,onRefresh:t[9]||(t[9]=n=>p(B).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):_("",!0),p(I).query.view=="list"?(V(),q(P,{key:1,appear:"",visibility:!0},{default:r(()=>[a(ce,{onToggleSelectAll:t[12]||(t[12]=n=>d.items=p(qe)(n,d)),onToggleGlobalSelect:S,selected:d,header:u.headers,meta:u.meta,module:"student",onRefresh:t[13]||(t[13]=n=>p(B).emit("listItems"))},{actionButton:r(()=>t[17]||(t[17]=[])),default:r(()=>[(V(!0),F(ee,null,te(u.data,n=>(V(),q(pe,{key:n.uuid,onDoubleClick:D=>p(m).push({name:"StudentShow",params:{uuid:n.uuid}})},{default:r(()=>[a(h,{name:"selectAll"},{default:r(()=>[d.global?_("",!0):(V(),q(de,{key:0,items:d.items,"onUpdate:items":t[10]||(t[10]=D=>d.items=D),value:n.uuid},null,8,["items","value"])),d.global?(V(),q(ue,{key:1,modelValue:d.global,"onUpdate:modelValue":t[11]||(t[11]=D=>d.global=D)},null,8,["modelValue"])):_("",!0)]),_:2},1024),a(h,{name:"codeNumber"},{default:r(()=>[v(c(n.codeNumber)+" ",1),a(j,{block:""},{default:r(()=>[v(c(n.joiningDate.formatted),1)]),_:2},1024)]),_:2},1024),a(h,{name:"name"},{default:r(()=>[l("span",{class:"cursor-pointer",onClick:D=>G(n.uuid)},c(n.name),9,Wt),n.isTransferred?(V(),q(J,{key:0,design:"danger"},{default:r(()=>[v(c(p(N)("student.statuses.transferred")),1)]),_:1})):_("",!0),n.isAlumni?(V(),q(J,{key:1},{default:r(()=>[v(c(p(N)("student.alumni.alumni")),1)]),_:1})):_("",!0)]),_:2},1024),a(h,{name:"course"},{default:r(()=>[v(c(n.courseName)+" ",1),a(j,{block:""},{default:r(()=>[v(c(n.batchName),1)]),_:2},1024),a(j,{block:""},{default:r(()=>[v(c(n.enrollmentTypeName),1)]),_:2},1024)]),_:2},1024),a(h,{name:"gender"},{default:r(()=>[v(c(n.gender.label),1)]),_:2},1024),a(h,{name:"birthDate"},{default:r(()=>[v(c(n.birthDate.formatted),1)]),_:2},1024),a(h,{name:"contactNumber"},{default:r(()=>[v(c(n.contactNumber),1)]),_:2},1024),a(h,{name:"rollNumber"},{default:r(()=>[v(c(n.rollNumber),1)]),_:2},1024),a(h,{name:"fatherName"},{default:r(()=>[v(c(n.fatherName||"-"),1)]),_:2},1024),a(h,{name:"motherName"},{default:r(()=>[v(c(n.motherName||"-"),1)]),_:2},1024),a(h,{name:"guardian"},{default:r(()=>{var D,X;return[v(c(((X=(D=n.guardian)==null?void 0:D.contact)==null?void 0:X.name)||"-")+" ",1),a(j,{block:""},{default:r(()=>{var Y,Z;return[v(c(((Z=(Y=n.guardian)==null?void 0:Y.contact)==null?void 0:Z.contactNumber)||"-"),1)]}),_:2},1024)]}),_:2},1024),a(h,{name:"bloodGroup"},{default:r(()=>[v(c(n.bloodGroup.label),1)]),_:2},1024),a(h,{name:"religion"},{default:r(()=>[v(c(n.religionName),1)]),_:2},1024),a(h,{name:"category"},{default:r(()=>[v(c(n.categoryName),1)]),_:2},1024),a(h,{name:"caste"},{default:r(()=>[v(c(n.casteName),1)]),_:2},1024),a(h,{name:"uniqueIdNumber1"},{default:r(()=>[v(c(n.uniqueIdNumber1),1)]),_:2},1024),a(h,{name:"uniqueIdNumber2"},{default:r(()=>[v(c(n.uniqueIdNumber2),1)]),_:2},1024),a(h,{name:"uniqueIdNumber3"},{default:r(()=>[v(c(n.uniqueIdNumber3),1)]),_:2},1024),a(h,{name:"uniqueIdNumber4"},{default:r(()=>[v(c(n.uniqueIdNumber4),1)]),_:2},1024),a(h,{name:"uniqueIdNumber5"},{default:r(()=>[v(c(n.uniqueIdNumber5),1)]),_:2},1024),a(h,{name:"leavingDate"},{default:r(()=>[v(c(n.leavingDate.formatted),1)]),_:2},1024),a(h,{name:"address"},{default:r(()=>[v(c(n.address),1)]),_:2},1024),a(h,{name:"action"},{default:r(()=>[a(me,null,{default:r(()=>[a(Q,{icon:"fas fa-arrow-circle-right",onClick:D=>p(m).push({name:"StudentShow",params:{uuid:n.uuid}})},{default:r(()=>[v(c(p(N)("general.show")),1)]),_:2},1032,["onClick"]),a(Q,{icon:"fas fa-file-lines",onClick:D=>p(m).push({name:"StudentShowFee",params:{uuid:n.uuid}})},{default:r(()=>[v(c(p(N)("global.view",{attribute:p(N)("student.fee.fee")})),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["selected","header","meta"])]),_:1})):_("",!0),a(kt,{selected:d,"select-all":f.selectAll,"selected-students":f.selectedStudents,onCompleted:o},null,8,["selected","select-all","selected-students"]),a(Ct,{selected:d,"select-all":f.selectAll,"selected-students":f.selectedStudents,onCompleted:o},null,8,["selected","select-all","selected-students"])]),_:1})}}});export{Xt as default};
