import{i as I,u as j,h as J,G as T,H as K,m as Q,l as q,r as m,q as D,o as l,w as i,d as _,a as f,b,f as r,B as U,s as p,t as n,F as B,e as c,x as W,J as R}from"./app-BAwPsakn.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={key:0},x={key:2,class:"mt-2 text-sm"},ee={class:"dark:text-gray-400"},te={key:0,class:"dark:text-gray-400"},ae={key:0,class:"col-span-3 sm:col-span-1"},se={class:"col-span-3"},ne={key:0,class:"mt-4 grid grid-cols-1"},re={class:"col"},oe={name:"StudentTransferForm"},ue=Object.assign(oe,{setup(O){const g=I(),d=j();J();const S={student:"",requestDate:null,reason:"",application:[],mediaUpdated:!1,mediaToken:T(),mediaHash:[]},y="student/transferRequest/",u=K(y),v=Q(!1),N=q({students:[]}),s=q({selectedStudent:null,feeSummary:{}}),o=q({...S}),$=q({student:"",isLoaded:!d.params.uuid}),H=a=>{Object.assign(N,a),Object.assign(o,R(S))},A=()=>{o.mediaToken=T(),o.mediaHash=[]},M=a=>{let t=a.student;s.selectedStudent={uuid:t.uuid,name:t.name,courseName:t.courseName,batchName:t.batchName,codeNumber:t.codeNumber,joiningDate:t.joiningDate},Object.assign(S,{...a,student:t.uuid,requestDate:a.requestDate.value,application:a.media}),Object.assign(o,R(S)),$.isLoaded=!0},w=async a=>{v.value=!0,await g.dispatch("student/getFeeSummary",{uuid:a}).then(t=>{v.value=!1,s.feeSummary=t}).catch(t=>{v.value=!1})},F=a=>{o.student=a?a.uuid:"",a&&w(a.uuid)},V=()=>{s.selectedStudent=null,o.student="",s.feeSummary={}};return(a,t)=>{const C=m("BaseSelect"),k=m("TextMuted"),E=m("BaseSelectSearch"),L=m("DatePicker"),P=m("BaseTextarea"),z=m("MediaUpload"),G=m("FormAction");return l(),D(G,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":y,"init-form":S,form:o,"set-form":M,redirect:"StudentTransferRequest",onResetMediaFiles:A},{default:i(()=>{var h;return[_("div",X,[_("div",Y,[r(U)(["student","guardian"],"any")?(l(),D(C,{key:0,name:"student",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:s.selectedStudent,"onUpdate:modelValue":t[0]||(t[0]=e=>s.selectedStudent=e),error:r(u).student,"onUpdate:error":t[1]||(t[1]=e=>r(u).student=e),options:N.students,"value-prop":"uuid","object-prop":!0,onSelected:F,onRemoved:V},{selectedOption:i(e=>[p(n(e.value.name)+" ("+n(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:i(e=>[p(n(e.option.name)+" ("+n(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","options"])):(l(),f(B,{key:1},[r(d).params.uuid&&s.selectedStudent?(l(),f("div",Z,[p(n(s.selectedStudent.name)+" ",1),c(k,{block:""},{default:i(()=>[p(n(s.selectedStudent.codeNumber),1)]),_:1}),c(k,{block:""},{default:i(()=>[p(n(a.$trans("student.admission.props.date"))+": "+n(s.selectedStudent.joiningDate.formatted),1)]),_:1})])):b("",!0),r(d).params.uuid?b("",!0):(l(),D(E,{key:1,name:"student",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:s.selectedStudent,"onUpdate:modelValue":t[2]||(t[2]=e=>s.selectedStudent=e),error:r(u).student,"onUpdate:error":t[3]||(t[3]=e=>r(u).student=e),"value-prop":"uuid","object-prop":!0,"init-search":$.student,"search-key":"name","search-action":"student/summary",onSelected:F,onRemoved:V},{selectedOption:i(e=>[p(n(e.value.name)+" ("+n(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:i(e=>[p(n(e.option.name)+" ("+n(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","init-search"]))],64)),!r(d).params.uuid&&s.selectedStudent?(l(),f("div",x,[c(k,{block:""},{default:i(()=>[p(n(s.selectedStudent.codeNumber),1)]),_:1}),_("div",ee,n(a.$trans("student.admission.props.date"))+": "+n(s.selectedStudent.joiningDate.formatted),1),s.selectedStudent.leavingDate.value?(l(),f("div",te,n(a.$trans("student.transfer.props.date"))+": "+n(s.selectedStudent.leavingDate.formatted),1)):b("",!0),s.feeSummary.balanceFee?(l(),f("div",{key:1,class:W(["font-semibold",{"text-red-500":s.feeSummary.balanceFee.value>0,"text-green-500":s.feeSummary.balanceFee.value<0}])},n(a.$trans("finance.fee.balance")+": "+s.feeSummary.balanceFee.formatted),3)):b("",!0)])):b("",!0)]),r(U)(["student","guardian"],"any")?b("",!0):(l(),f("div",ae,[c(L,{modelValue:o.requestDate,"onUpdate:modelValue":t[4]||(t[4]=e=>o.requestDate=e),name:"requestDate",label:a.$trans("student.transfer_request.props.request_date"),"no-clear":"",error:r(u).requestDate,"onUpdate:error":t[5]||(t[5]=e=>r(u).requestDate=e)},null,8,["modelValue","label","error"])])),_("div",se,[c(P,{rows:1,modelValue:o.reason,"onUpdate:modelValue":t[6]||(t[6]=e=>o.reason=e),name:"reason",label:a.$trans("student.transfer_request.props.reason"),error:r(u).reason,"onUpdate:error":t[7]||(t[7]=e=>r(u).reason=e)},null,8,["modelValue","label","error"])])]),s.selectedStudent&&(((h=s.feeSummary.balanceFee)==null?void 0:h.value)==0||r(d).params.uuid)?(l(),f("div",ne,[_("div",re,[c(z,{multiple:"",label:a.$trans("general.file"),module:"transfer_request",section:"application",media:o.application,"media-token":o.mediaToken,onIsUpdated:t[8]||(t[8]=e=>o.mediaUpdated=!0),onSetHash:t[9]||(t[9]=e=>o.mediaHash.push(e)),error:r(u).application,"onUpdate:error":t[10]||(t[10]=e=>r(u).application=e)},null,8,["label","media","media-token","error"])])])):b("",!0)]}),_:1},8,["form"])}}}),de={name:"StudentTransferRequestAction"},ie=Object.assign(de,{setup(O){const g=j();return(d,S)=>{const y=m("PageHeaderAction"),u=m("PageHeader"),v=m("ParentTransition");return l(),f(B,null,[c(u,{title:d.$trans(r(g).meta.trans,{attribute:d.$trans(r(g).meta.label)}),navs:[{label:d.$trans("student.student"),path:"Student"},{label:d.$trans("student.transfer_request.transfer_request"),path:"StudentTransferRequest"}]},{default:i(()=>[c(y,{name:"StudentTransferRequest",title:d.$trans("student.transfer_request.transfer_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),c(v,{appear:"",visibility:!0},{default:i(()=>[c(ue)]),_:1})],64)}}});export{ie as default};
