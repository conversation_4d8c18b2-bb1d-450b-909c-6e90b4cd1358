import{u as v,l as g,H as C,r as n,q as T,o as c,w as F,d as u,b as j,e as a,f as i,I as k,J as V,a as O,F as q}from"./app-BAwPsakn.js";import{u as A}from"./useCustomFields-C7JPVoj8.js";const H={class:"grid grid-cols-2 gap-6"},R={class:"col-span-2"},I={class:"col-span-2 sm:col-span-1"},N={class:"col-span-2 sm:col-span-1"},w={class:"col-span-2"},J={name:"TodoForm"},L=Object.assign(J,{setup(U){const m=v(),r={title:"",dueDate:null,dueTime:null,description:"",customFields:[]},p="utility/todo/",d=g({customFields:[]}),s=C(p),o=g({...r}),{customFields:f,setCustomFields:_}=A(),y=l=>{Object.assign(d,l),_(d.customFields),r.customFields=f.value,Object.assign(o,V(r))},D=l=>{var e;Object.assign(r,{...l,dueDate:l.dueDate.value,dueTime:(e=l.dueTime)==null?void 0:e.at}),_(d.customFields,l.customFields),r.customFields=f.value,Object.assign(o,V(r))};return(l,e)=>{const P=n("BaseInput"),b=n("DatePicker"),$=n("BaseEditor"),B=n("CustomField"),E=n("FormAction");return c(),T(E,{"pre-requisites":!0,onSetPreRequisites:y,"init-url":p,"init-form":r,form:o,"set-form":D,redirect:"UtilityTodo"},{default:F(()=>[u("div",H,[u("div",R,[a(P,{type:"text",modelValue:o.title,"onUpdate:modelValue":e[0]||(e[0]=t=>o.title=t),name:"title",label:l.$trans("utility.todo.props.title"),error:i(s).title,"onUpdate:error":e[1]||(e[1]=t=>i(s).title=t),autofocus:""},null,8,["modelValue","label","error"])]),u("div",I,[a(b,{modelValue:o.dueDate,"onUpdate:modelValue":e[2]||(e[2]=t=>o.dueDate=t),name:"dueDate",label:l.$trans("utility.todo.props.due_date"),"no-clear":"",error:i(s).dueDate,"onUpdate:error":e[3]||(e[3]=t=>i(s).dueDate=t)},null,8,["modelValue","label","error"])]),u("div",N,[a(b,{modelValue:o.dueTime,"onUpdate:modelValue":e[4]||(e[4]=t=>o.dueTime=t),name:"dueTime",label:l.$trans("utility.todo.props.due_time"),as:"time",error:i(s).dueTime,"onUpdate:error":e[5]||(e[5]=t=>i(s).dueTime=t)},null,8,["modelValue","label","error"])]),u("div",w,[a($,{id:"Testing",modelValue:o.description,"onUpdate:modelValue":e[6]||(e[6]=t=>o.description=t),name:"description",edit:!!i(m).params.uuid,label:l.$trans("utility.todo.props.description"),error:i(s).description,"onUpdate:error":e[7]||(e[7]=t=>i(s).description=t)},null,8,["modelValue","edit","label","error"])])]),d.customFields.length>0?(c(),T(B,{key:0,customFields:o.customFields,"onUpdate:customFields":e[8]||(e[8]=t=>o.customFields=t),formErrors:i(s),"onUpdate:formErrors":e[9]||(e[9]=t=>k(s)?s.value=t:null)},null,8,["customFields","formErrors"])):j("",!0)]),_:1},8,["form"])}}}),S={name:"TodoAction"},K=Object.assign(S,{setup(U){const m=v();return(r,p)=>{const d=n("PageHeaderAction"),s=n("PageHeader"),o=n("ParentTransition");return c(),O(q,null,[a(s,{title:r.$trans(i(m).meta.trans,{attribute:r.$trans(i(m).meta.label)}),navs:[{label:r.$trans("utility.todo.todo"),path:"UtilityTodoList"}]},{default:F(()=>[a(d,{name:"UtilityTodo",title:r.$trans("utility.todo.todo"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(o,{appear:"",visibility:!0},{default:F(()=>[a(L)]),_:1})],64)}}});export{K as default};
