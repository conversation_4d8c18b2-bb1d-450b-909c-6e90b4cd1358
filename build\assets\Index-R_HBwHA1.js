import{u as w,G as O,H as D,l as H,r as o,q as g,o as n,w as l,e as _,d as t,b as y,t as u,h as N,i as z,j as E,m as M,c as I,n as L,p as R,a as B,F,v as q,x as G,f as J,y as K,s as h,aO as P}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-1"},W={class:"col"},X={name:"ActivityTripMediaForm"},Y=Object.assign(X,{props:{trip:{type:Object,default(){return{}}},visibility:{type:Boolean,default:!1}},emits:["close","completed"],setup(i,{emit:$}){w();const r=$,m={media:[],mediaUpdated:!1,mediaToken:O(),mediaHash:[]},p="activity/trip/media/";D(p);const a=H({...m});return(f,e)=>{const v=o("MediaUpload"),b=o("FormAction"),C=o("BaseSideover");return n(),g(C,{visibility:i.visibility,onClose:e[6]||(e[6]=s=>r("close"))},{title:l(()=>[t("span",null,u(f.$trans("global.add",{attribute:f.$trans("activity.trip.media.media")})),1)]),default:l(()=>[_(b,{sideover:"","no-card":"","no-data-fetch":"","cancel-action":"",action:"create","init-url":p,"init-form":m,form:a,onEnd:e[3]||(e[3]=s=>r("close")),onCompleted:e[4]||(e[4]=s=>r("completed")),onCancelled:e[5]||(e[5]=s=>r("close"))},{default:l(()=>[t("div",Q,[t("div",W,[i.trip.uuid?(n(),g(v,{key:0,multiple:"",label:f.$trans("general.file"),module:"trip",media:a.media,onIsUpdated:e[0]||(e[0]=s=>a.mediaUpdated=!0),onSetHash:e[1]||(e[1]=s=>a.mediaHash=s),onSetToken:e[2]||(e[2]=s=>a.mediaToken=s)},null,8,["label","media"])):y("",!0)])])]),_:1},8,["form"])]),_:1},8,["visibility"])}}}),Z={class:"grid grid-cols-1"},ee={class:"grid grid-cols-4 gap-6"},te=["onClick"],se={class:"flex items-start justify-between"},ie={class:"flex cursor-pointer"},ae={class:"mr-4"},ne={class:"flex flex-col"},oe={name:"ActivityTripMediaList"},re=Object.assign(oe,{props:{trip:{type:Object,default(){return{}}}},emits:["refresh"],setup(i,{emit:$}){w(),N();const r=z(),m=E("emitter"),p=$,a=i,f="activity/trip/media/",e=M(!1),v=M(!1),b=()=>{v.value=!1},C=I(()=>r.getters["config/config"]("system.url")),s=c=>{window.open(C.value+`/app/activity/trips/${a.trip.uuid}/media/`+c)},U=async c=>{await P()&&(e.value=!0,await r.dispatch(f+"delete",{uuid:a.trip.uuid,moduleUuid:c.uuid}).then(()=>{e.value=!1,p("refresh")}).catch(k=>{e.value=!1}))};return L(async()=>{m.on("addActivityTripMedia",()=>{v.value=!0})}),R(()=>{m.all.delete("addActivityTripMedia")}),(c,k)=>{const x=o("FloatingMenuItem"),A=o("FloatingMenu"),T=o("BaseAlert"),j=o("BaseDataView"),S=o("BaseCard");return n(),B(F,null,[i.trip.uuid?(n(),g(S,{key:0,"is-loading":e.value},{default:l(()=>[t("div",Z,[_(j,{class:"col-span-1"},{default:l(()=>[t("div",ee,[(n(!0),B(F,null,q(i.trip.media,d=>(n(),B("div",{onClick:V=>s(d.uuid),class:"col-span-4 sm:col-span-1"},[t("div",se,[t("div",ie,[t("span",ae,[t("i",{class:G(["fas text-4xl",d.icon])},null,2)]),t("div",ne,[t("span",null,u(d.name),1),t("span",null,u(d.size),1),t("span",null,u(d.uuid),1)])]),t("div",null,[J(K)("trip:manage")?(n(),g(A,{key:0},{default:l(()=>[_(x,{icon:"fas fa-trash",onClick:V=>U(d)},{default:l(()=>[h(u(c.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)):y("",!0)])])],8,te))),256))]),i.trip.media.length===0?(n(),g(T,{key:0,design:"info",size:"xs"},{default:l(()=>[h(u(c.$trans("general.errors.attachment_not_found")),1)]),_:1})):y("",!0)]),_:1})])]),_:1},8,["is-loading"])):y("",!0),_(Y,{trip:i.trip,visibility:v.value,onCompleted:k[0]||(k[0]=d=>p("refresh")),onClose:b},null,8,["trip","visibility"])],64)}}});export{re as default};
