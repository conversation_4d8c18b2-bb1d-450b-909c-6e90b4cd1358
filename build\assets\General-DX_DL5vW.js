import{u as V,H as v,l as G,r as s,a as b,o as w,e as t,f as o,w as k,d as i,b as P,F as c}from"./app-BAwPsakn.js";const h={class:"grid grid-cols-3 gap-4"},U={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},B={name:"GalleryConfigGeneral"},F=Object.assign(B,{setup(W){const y=V(),m="config/",l=v(m),d={showGalleryInDashboard:!0,enableWatermark:!0,watermarkPosition:"top-left",watermarkSize:10,type:"gallery"},r=G({...d});return(n,e)=>{const g=s("PageHeader"),p=s("BaseSwitch"),u=s("BaseInput"),f=s("FormAction"),_=s("ParentTransition");return w(),b(c,null,[t(g,{title:n.$trans(o(y).meta.label),navs:[{label:n.$trans("gallery.gallery"),path:"Gallery"}]},null,8,["title","navs"]),t(_,{appear:"",visibility:!0},{default:k(()=>[t(f,{"init-url":m,"data-fetch":"gallery","init-form":d,form:r,action:"store","stay-on":"",redirect:"Gallery"},{default:k(()=>[i("div",h,[i("div",U,[t(p,{vertical:"",modelValue:r.showGalleryInDashboard,"onUpdate:modelValue":e[0]||(e[0]=a=>r.showGalleryInDashboard=a),name:"showGalleryInDashboard",label:n.$trans("gallery.config.props.show_gallery_in_dashboard"),error:o(l).showGalleryInDashboard,"onUpdate:error":e[1]||(e[1]=a=>o(l).showGalleryInDashboard=a)},null,8,["modelValue","label","error"])]),i("div",I,[t(p,{vertical:"",modelValue:r.enableWatermark,"onUpdate:modelValue":e[2]||(e[2]=a=>r.enableWatermark=a),name:"enableWatermark",label:n.$trans("gallery.config.props.enable_watermark"),error:o(l).enableWatermark,"onUpdate:error":e[3]||(e[3]=a=>o(l).enableWatermark=a)},null,8,["modelValue","label","error"])]),r.enableWatermark?(w(),b(c,{key:0},[i("div",S,[t(u,{type:"text",modelValue:r.watermarkPosition,"onUpdate:modelValue":e[4]||(e[4]=a=>r.watermarkPosition=a),name:"watermarkPosition",label:n.$trans("gallery.config.props.watermark_position"),error:o(l).watermarkPosition,"onUpdate:error":e[5]||(e[5]=a=>o(l).watermarkPosition=a)},null,8,["modelValue","label","error"])]),i("div",z,[t(u,{type:"text",modelValue:r.watermarkSize,"onUpdate:modelValue":e[6]||(e[6]=a=>r.watermarkSize=a),name:"watermarkSize",label:n.$trans("gallery.config.props.watermark_size"),error:o(l).watermarkSize,"onUpdate:error":e[7]||(e[7]=a=>o(l).watermarkSize=a)},null,8,["modelValue","label","error"])])],64)):P("",!0)])]),_:1},8,["form"])]),_:1})],64)}}});export{F as default};
