import{l as B,r as o,q as _,o as d,w as e,d as D,e as n,h as N,j as S,y as v,m as T,z as U,f as s,a as w,F as E,v as O,s as r,t as u,A as q,b as F}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(I,{emit:m}){const y=m,$={name:"",alias:""},c=B({...$});return(k,i)=>{const p=o("BaseInput"),C=o("FilterForm");return d(),_(C,{"init-form":$,form:c,onHide:i[2]||(i[2]=a=>y("hide"))},{default:e(()=>[D("div",z,[D("div",G,[n(p,{type:"text",modelValue:c.name,"onUpdate:modelValue":i[0]||(i[0]=a=>c.name=a),name:"name",label:k.$trans("finance.ledger_type.props.name")},null,8,["modelValue","label"])]),D("div",J,[n(p,{type:"text",modelValue:c.alias,"onUpdate:modelValue":i[1]||(i[1]=a=>c.alias=a),name:"alias",label:k.$trans("finance.ledger_type.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Q={key:0,class:"far fa-check-circle fa-xl text-success"},W={name:"FinanceLedgerTypeList"},Y=Object.assign(W,{setup(I){const m=N(),y=S("emitter");let $=["filter"];v("ledger-type:create")&&$.unshift("create");let c=[];v("ledger-type:export")&&(c=["print","pdf","excel"]);const k="finance/ledgerType/",i=T(!1),p=B({}),C=a=>{Object.assign(p,a)};return(a,l)=>{const L=o("PageHeaderAction"),V=o("PageHeader"),h=o("ParentTransition"),g=o("DataCell"),b=o("FloatingMenuItem"),A=o("FloatingMenu"),H=o("DataRow"),P=o("BaseButton"),R=o("DataTable"),j=o("ListItem"),M=U("tooltip");return d(),_(j,{"init-url":k,onSetItems:C},{header:e(()=>[n(V,{title:a.$trans("finance.ledger_type.ledger_type"),navs:[{label:a.$trans("finance.finance"),path:"Finance"}]},{default:e(()=>[n(L,{url:"finance/ledger-types/",name:"FinanceLedgerType",title:a.$trans("finance.ledger_type.ledger_type"),actions:s($),"dropdown-actions":s(c),onToggleFilter:l[0]||(l[0]=t=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(h,{appear:"",visibility:i.value},{default:e(()=>[n(K,{onRefresh:l[1]||(l[1]=t=>s(y).emit("listItems")),onHide:l[2]||(l[2]=t=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(h,{appear:"",visibility:!0},{default:e(()=>[n(R,{header:p.headers,meta:p.meta,module:"finance.ledger_type",onRefresh:l[4]||(l[4]=t=>s(y).emit("listItems"))},{actionButton:e(()=>[s(v)("ledger-type:create")?(d(),_(P,{key:0,onClick:l[3]||(l[3]=t=>s(m).push({name:"FinanceLedgerTypeCreate"}))},{default:e(()=>[r(u(a.$trans("global.add",{attribute:a.$trans("finance.ledger_type.ledger_type")})),1)]),_:1})):F("",!0)]),default:e(()=>[(d(!0),w(E,null,O(p.data,t=>(d(),_(H,{key:t.uuid,onDoubleClick:f=>s(m).push({name:"FinanceLedgerTypeShow",params:{uuid:t.uuid}})},{default:e(()=>[n(g,{name:"name"},{default:e(()=>[r(u(t.name),1)]),_:2},1024),n(g,{name:"isDefault"},{default:e(()=>[t.isDefault?q((d(),w("i",Q,null,512)),[[M,a.$trans("general.default")]]):F("",!0)]),_:2},1024),n(g,{name:"alias"},{default:e(()=>[r(u(t.alias||"-"),1)]),_:2},1024),n(g,{name:"parent"},{default:e(()=>{var f;return[r(u(((f=t.parent)==null?void 0:f.name)||"-"),1)]}),_:2},1024),n(g,{name:"createdAt"},{default:e(()=>[r(u(t.createdAt.formatted),1)]),_:2},1024),n(g,{name:"action"},{default:e(()=>[n(A,null,{default:e(()=>[n(b,{icon:"fas fa-arrow-circle-right",onClick:f=>s(m).push({name:"FinanceLedgerTypeShow",params:{uuid:t.uuid}})},{default:e(()=>[r(u(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),s(v)("ledger-type:edit")&&!t.isDefault?(d(),_(b,{key:0,icon:"fas fa-edit",onClick:f=>s(m).push({name:"FinanceLedgerTypeEdit",params:{uuid:t.uuid}})},{default:e(()=>[r(u(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):F("",!0),s(v)("ledger-type:create")?(d(),_(b,{key:1,icon:"fas fa-copy",onClick:f=>s(m).push({name:"FinanceLedgerTypeDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[r(u(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):F("",!0),s(v)("ledger-type:delete")&&!t.isDefault?(d(),_(b,{key:2,icon:"fas fa-trash",onClick:f=>s(y).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[r(u(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):F("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
