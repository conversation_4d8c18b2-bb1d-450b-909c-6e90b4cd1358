import{u as q,j as z,H as Y,l as E,n as J,K as Z,r as m,q as k,b as g,o as u,w as n,e as r,f as t,d as _,a as D,F as P,s as f,t as d,J as ee,_ as te,h as ae,m as L,p as oe,v as ne,x as se,y as x}from"./app-BAwPsakn.js";const re={class:"grid grid-cols-3 gap-4"},le={class:"col-span-3"},de={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-2 sm:col-span-1"},me={key:0,class:"col-span-2 sm:col-span-1"},ce={class:"col-span-3"},pe={key:1,class:"col-span-3 sm:col-span-1"},be={class:"col-span-3"},fe={name:"StudentEditRecord"},ge=Object.assign(fe,{props:{visibility:{type:Boolean,default:!1},student:{type:Object,default(){return{}}},record:{type:Object,default(){return{}}}},emits:["close","completed"],setup(b,{emit:T}){const R=q(),C=T,c=b,p=z("$trans"),y={editCodeNumber:!1,codeNumberFormat:"",codeNumber:"",joiningDate:"",startDate:"",editBatch:!1,batch:"",remarks:""},N="student/record/",i=Y(N),s=E({...y}),B=()=>{C("close")},M=()=>{C("close"),C("completed")},j=()=>{var V,a;y.codeNumberFormat=c.record.numberFormat,y.codeNumber=c.record.codeNumber,y.joiningDate=(V=c.record.joiningDate)==null?void 0:V.value,y.startDate=(a=c.record.startDate)==null?void 0:a.value,Object.assign(s,ee(y))};return J(()=>{j()}),Z(()=>c.record.uuid,V=>{j()},{deep:!0}),(V,a)=>{const w=m("BaseSwitch"),S=m("BaseInput"),I=m("DatePicker"),l=m("BaseSelectSearch"),v=m("BaseTextarea"),A=m("FormAction"),O=m("BaseModal");return b.record.uuid?(u(),k(O,{key:0,show:b.visibility,onClose:B},{title:n(()=>[f(d(t(p)("global.detail",{attribute:t(p)("student.record.record")})),1)]),default:n(()=>[r(A,{"no-card":"","no-data-fetch":"","is-modal":"",confirmation:"",uuid:t(R).params.uuid,"module-uuid":b.record.uuid,action:"update","init-url":N,"init-form":y,form:s,"keep-adding":!1,"after-submit":M,onCancelled:B},{default:n(()=>[_("div",re,[_("div",le,[r(w,{vertical:"",modelValue:s.editCodeNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>s.editCodeNumber=e),name:"editCodeNumber",label:t(p)("global.edit",{attribute:t(p)("student.admission.props.code_number")}),error:t(i).editCodeNumber,"onUpdate:error":a[1]||(a[1]=e=>t(i).editCodeNumber=e)},null,8,["modelValue","label","error"])]),s.editCodeNumber?(u(),D(P,{key:0},[_("div",de,[r(S,{type:"text",modelValue:s.codeNumberFormat,"onUpdate:modelValue":a[2]||(a[2]=e=>s.codeNumberFormat=e),name:"codeNumberFormat",label:t(p)("student.admission.props.code_number_format"),error:t(i).codeNumberFormat,"onUpdate:error":a[3]||(a[3]=e=>t(i).codeNumberFormat=e)},null,8,["modelValue","label","error"])]),_("div",ie,[r(S,{type:"text",modelValue:s.codeNumber,"onUpdate:modelValue":a[4]||(a[4]=e=>s.codeNumber=e),name:"codeNumber",label:t(p)("student.admission.props.code_number"),error:t(i).codeNumber,"onUpdate:error":a[5]||(a[5]=e=>t(i).codeNumber=e)},null,8,["modelValue","label","error"])]),_("div",ue,[r(I,{modelValue:s.joiningDate,"onUpdate:modelValue":a[6]||(a[6]=e=>s.joiningDate=e),name:"joiningDate",label:t(p)("student.admission.props.date"),"no-clear":"",error:t(i).joiningDate,"onUpdate:error":a[7]||(a[7]=e=>t(i).joiningDate=e)},null,8,["modelValue","label","error"])]),b.record.isPromoted?(u(),D("div",me,[r(I,{modelValue:s.startDate,"onUpdate:modelValue":a[8]||(a[8]=e=>s.startDate=e),name:"startDate",label:t(p)("student.record.props.promotion_date"),"no-clear":"",error:t(i).startDate,"onUpdate:error":a[9]||(a[9]=e=>t(i).startDate=e)},null,8,["modelValue","label","error"])])):g("",!0)],64)):g("",!0),_("div",ce,[r(w,{vertical:"",modelValue:s.editBatch,"onUpdate:modelValue":a[10]||(a[10]=e=>s.editBatch=e),name:"editBatch",label:t(p)("global.edit",{attribute:t(p)("academic.batch.batch")}),error:t(i).editBatch,"onUpdate:error":a[11]||(a[11]=e=>t(i).editBatch=e)},null,8,["modelValue","label","error"])]),s.editBatch?(u(),D("div",pe,[r(l,{name:"batch",label:t(p)("global.change",{attribute:t(p)("academic.batch.batch")}),modelValue:s.batch,"onUpdate:modelValue":a[12]||(a[12]=e=>s.batch=e),error:t(i).batch,"onUpdate:error":a[13]||(a[13]=e=>t(i).batch=e),"search-key":"course_batch","value-prop":"uuid","search-action":"academic/batch/list"},{selectedOption:n(e=>[f(d(e.value.course.name)+" - "+d(e.value.name),1)]),listOption:n(e=>[f(d(e.option.course.nameWithTerm)+" - "+d(e.option.name),1)]),_:1},8,["label","modelValue","error"])])):g("",!0),_("div",be,[r(v,{rows:1,modelValue:s.remarks,"onUpdate:modelValue":a[14]||(a[14]=e=>s.remarks=e),name:"remarks",label:t(p)("student.record.props.remarks"),error:t(i).remarks,"onUpdate:error":a[15]||(a[15]=e=>t(i).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form"])]),_:1},8,["show"])):g("",!0)}}}),ve={name:"TemplateName"};function ke(b,T,R,C,c,p){return null}const _e=te(ve,[["render",ke]]),ye={key:0,class:"far fa-check-circle fa-xl text-success"},Ne={class:"text-danger"},$e={class:"text-danger"},De={name:"StudentRecordList"},Be=Object.assign(De,{props:{student:{type:Object,default(){return{}}}},emits:["close","refresh"],setup(b,{emit:T}){const R=q(),C=ae(),c=z("emitter");let p=[];const y="student/record/",N=L(!1),i=L(!1),s=E({}),B=E({selectedRecord:{}}),M=l=>{B.selectedRecord=l,i.value=!0},j=()=>{i.value=!1},V=()=>{c.emit("studentUpdated"),c.emit("listItems")},a=l=>{Object.assign(s,l)},w=l=>{c.emit("actionItem",{uuid:l.uuid,action:"cancelAdmission",confirmation:!0})},S=l=>{c.emit("actionItem",{uuid:l.uuid,action:"cancelPromotion",confirmation:!0})},I=l=>{c.emit("actionItem",{uuid:l.uuid,action:"setDefaultPeriod",confirmation:!0})};return J(()=>{c.on("actionPerformed",()=>{c.emit("studentUpdated")})}),oe(()=>{c.all.delete("actionPerformed")}),(l,v)=>{const A=m("PageHeaderAction"),O=m("PageHeader"),e=m("ParentTransition"),h=m("TextMuted"),K=m("BaseBadge"),F=m("DataCell"),U=m("FloatingMenuItem"),W=m("FloatingMenu"),G=m("DataRow"),Q=m("DataTable"),X=m("ListItem");return u(),D(P,null,[r(X,{"init-url":y,uuid:t(R).params.uuid,onSetItems:a},{header:n(()=>[b.student.uuid?(u(),k(O,{key:0,title:l.$trans("student.record.record"),navs:[{label:l.$trans("student.student"),path:"Student"},{label:b.student.contact.name,path:{name:"StudentShow",params:{uuid:b.student.uuid}}}]},{default:n(()=>[r(A,{url:`students/${b.student.uuid}/records/`,name:"StudentRecord",title:l.$trans("student.record.record"),actions:t(p),"dropdown-actions":["print","pdf","excel"],onToggleFilter:v[0]||(v[0]=o=>N.value=!N.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):g("",!0)]),filter:n(()=>[r(e,{appear:"",visibility:N.value},{default:n(()=>[r(_e,{onRefresh:v[1]||(v[1]=o=>t(c).emit("listItems")),onHide:v[2]||(v[2]=o=>N.value=!1)})]),_:1},8,["visibility"])]),default:n(()=>[r(e,{appear:"",visibility:!0},{default:n(()=>[r(Q,{header:s.headers,meta:s.meta,module:"student.record",onRefresh:v[3]||(v[3]=o=>t(c).emit("listItems"))},{actionButton:n(()=>v[4]||(v[4]=[])),default:n(()=>[(u(!0),D(P,null,ne(s.data,o=>(u(),k(G,{key:o.uuid},{default:n(()=>[r(F,{name:"period"},{default:n(()=>{var $,H;return[_("span",{class:se({"text-danger":o.period.id==((H=($=b.student.contact)==null?void 0:$.user)==null?void 0:H.currentPeriodId)})},d(o.period.name),3),o.uuid==b.student.uuid?(u(),D("i",ye)):g("",!0),r(h,{block:""},{default:n(()=>[f(d(o.codeNumber)+" ("+d(o.joiningDate.formatted)+")",1)]),_:2},1024),o.cancelledAt.value?(u(),k(K,{key:1,design:"danger"},{default:n(()=>[f(d(l.$trans("student.statuses.cancelled")),1)]),_:1})):g("",!0)]}),_:2},1024),r(F,{name:"course"},{default:n(()=>[f(d(o.courseName)+" ",1),r(h,{block:""},{default:n(()=>[f(d(o.batchName),1)]),_:2},1024),o.enrollmentTypeName?(u(),k(h,{key:0,block:""},{default:n(()=>[f(d(o.enrollmentTypeName),1)]),_:2},1024)):g("",!0)]),_:2},1024),r(F,{name:"startDate"},{default:n(()=>[f(d(o.startDate.formatted),1)]),_:2},1024),r(F,{name:"endDate"},{default:n(()=>[f(d(o.endDate.formatted||"-")+" ",1),o.transferReason?(u(),k(h,{key:0,block:""},{default:n(()=>[_("span",Ne,d(o.transferReason),1)]),_:2},1024)):g("",!0),o.transferCertificateNumber?(u(),k(h,{key:1,block:""},{default:n(()=>[_("span",$e,d(o.transferCertificateNumber),1)]),_:2},1024)):g("",!0)]),_:2},1024),r(F,{name:"action"},{default:n(()=>[r(W,null,{default:n(()=>[o.uuid!=b.student.uuid?(u(),k(U,{key:0,icon:"fas fa-arrow-circle-right",onClick:$=>t(C).push({name:"StudentRecord",params:{uuid:o.uuid}})},{default:n(()=>[f(d(l.$trans("general.show")),1)]),_:2},1032,["onClick"])):g("",!0),t(x)("student:edit")?(u(),D(P,{key:1},[r(U,{icon:"fas fa-clock",onClick:$=>I(o)},{default:n(()=>[f(d(l.$trans("global.set_default",{attribute:l.$trans("academic.period.period")})),1)]),_:2},1032,["onClick"]),r(U,{icon:"fas fa-edit",onClick:$=>M(o)},{default:n(()=>[f(d(l.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(x)("student:manage-record")&&o.isAdmitted&&!o.cancelledAt.value?(u(),k(U,{key:0,icon:"fas fa-times",onClick:$=>w(o)},{default:n(()=>[f(d(l.$trans("global.cancel",{attribute:l.$trans("student.admission.admission")})),1)]),_:2},1032,["onClick"])):g("",!0),t(x)("student:manage-record")&&o.isPromoted&&!o.cancelledAt.value?(u(),k(U,{key:1,icon:"fas fa-times",onClick:$=>S(o)},{default:n(()=>[f(d(l.$trans("global.cancel",{attribute:l.$trans("student.promotion.promotion")})),1)]),_:2},1032,["onClick"])):g("",!0)],64)):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"]),B.selectedRecord.uuid?(u(),k(ge,{key:0,visibility:i.value,student:b.student,record:B.selectedRecord,onClose:j,onCompleted:V},null,8,["visibility","student","record"])):g("",!0)],64)}}});export{Be as default};
