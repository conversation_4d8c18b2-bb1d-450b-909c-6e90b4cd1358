import{u as S,h as I,H as M,l as h,n as O,r as m,q as y,o as c,w as b,d as u,e as p,f as l,G as j,b as $,J as P,j as F,m as R,z as C,a as K,A as L,s as x,t as N,F as z}from"./app-BAwPsakn.js";import{_ as q,a as G}from"./Og-CAJZ9P6B.js";const J={class:"grid w-full grid-cols-1 gap-y-2"},Q={class:"col-span-1"},W={class:"col-span-1"},X={class:"col-span-1"},Y={name:"BlogEditContent"},Z=Object.assign(Y,{props:{blog:{type:Object,default(){return{}}}},emits:["refresh"],setup(B,{emit:w}){S();const T=I(),_=w,n=B,v={title:"",subTitle:"",content:""},f="blog/",t=M(f),o=h({...v}),g=()=>{_("refresh")};return O(()=>{Object.assign(v,{title:n.blog.title,subTitle:n.blog.subTitle,content:n.blog.content}),Object.assign(o,v)}),(a,i)=>{const k=m("BaseInput"),d=m("MdEditor"),e=m("FormAction");return c(),y(e,{"no-card":"","no-data-fetch":"","button-padding":"",action:"update","keep-adding":!1,"stay-on":!0,"init-url":f,"init-form":v,form:o,"after-submit":g,onCancelled:i[6]||(i[6]=r=>l(T).push({name:"BlogShow",params:{uuid:B.blog.uuid}}))},{default:b(()=>[u("div",J,[u("div",Q,[p(k,{invisible:"","text-size":"2xl",type:"text",modelValue:o.title,"onUpdate:modelValue":i[0]||(i[0]=r=>o.title=r),name:"title",placeholder:a.$trans("blog.props.title_placeholder"),error:l(t).title,"onUpdate:error":i[1]||(i[1]=r=>l(t).title=r)},null,8,["modelValue","placeholder","error"])]),u("div",W,[p(k,{invisible:"","text-size":"lg",type:"text",modelValue:o.subTitle,"onUpdate:modelValue":i[2]||(i[2]=r=>o.subTitle=r),name:"subTitle",placeholder:a.$trans("blog.props.sub_title_placeholder"),error:l(t).subTitle,"onUpdate:error":i[3]||(i[3]=r=>l(t).subTitle=r)},null,8,["modelValue","placeholder","error"])]),u("div",X,[p(d,{placeholder:a.$trans("blog.props.content_placeholder"),modelValue:o.content,"onUpdate:modelValue":i[4]||(i[4]=r=>o.content=r),error:l(t).content,"onUpdate:error":i[5]||(i[5]=r=>l(t).content=r)},null,8,["placeholder","modelValue","error"])])])]),_:1},8,["form"])}}}),ee={class:"grid w-full grid-cols-1 gap-y-4"},te={class:"col-span-1"},oe={class:"col-span-1"},se={class:"col-span-1"},le={class:"col-span-1"},ae={class:"col-span-1"},re={class:"col-span-1"},ne={class:"col-span-1"},ie={class:"col-span-1"},de={class:"col-span-1"},ue={name:"BlogEditMeta"},me=Object.assign(ue,{props:{blog:{type:Object,default(){return{}}}},emits:["refresh"],setup(B,{emit:w}){const T=S();I();const _=w,n=B,v={publishedAt:"",category:"",slug:"",tags:[],seo:{robots:"",metaTitle:"",metaDescription:"",metaKeywords:""},media:[],mediaUpdated:!1,mediaToken:j(),mediaHash:[]},f="blog/",t=M(f),o=h({...v}),g=h({category:"",tags:[],isLoaded:!T.params.uuid}),a=()=>{_("refresh")},i=()=>{o.mediaToken=j(),o.mediaHash=[]},k=()=>{var d,e,r;Object.assign(v,{publishedAt:((d=n.blog.publishedAt)==null?void 0:d.at)||"",category:(e=n.blog.category)==null?void 0:e.uuid,slug:n.blog.slug,tags:n.blog.tags.map(U=>U.uuid),seo:{robots:n.blog.seo.robots,metaTitle:n.blog.seo.metaTitle,metaDescription:n.blog.seo.metaDescription,metaKeywords:n.blog.seo.metaKeywords},media:n.blog.media,mediaToken:n.blog.mediaToken}),Object.assign(o,P(v)),g.category=(r=n.blog.category)==null?void 0:r.name,g.tags=n.blog.tags.map(U=>U.uuid),g.isLoaded=!0};return O(()=>{k()}),(d,e)=>{const r=m("DatePicker"),U=m("BaseSelectSearch"),D=m("BaseInput"),A=m("BaseSwitch"),V=m("BaseTextarea"),E=m("MediaUpload"),H=m("FormAction");return c(),y(H,{"no-card":"","no-data-fetch":"",action:"updateMeta","keep-adding":!1,"stay-on":!0,"init-url":f,"init-form":v,form:o,"after-submit":a,onResetMediaFiles:i},{default:b(()=>[u("div",ee,[u("div",te,[p(r,{as:"datetime",modelValue:o.publishedAt,"onUpdate:modelValue":e[0]||(e[0]=s=>o.publishedAt=s),name:"publishedAt",label:d.$trans("blog.props.published_at"),error:l(t).publishedAt,"onUpdate:error":e[1]||(e[1]=s=>l(t).publishedAt=s)},null,8,["modelValue","label","error"])]),u("div",oe,[g.isLoaded?(c(),y(U,{key:0,name:"category",label:d.$trans("global.select",{attribute:d.$trans("blog.category.category")}),modelValue:o.category,"onUpdate:modelValue":e[2]||(e[2]=s=>o.category=s),error:l(t).category,"onUpdate:error":e[3]||(e[3]=s=>l(t).category=s),"label-prop":"name","value-prop":"uuid","init-search":g.category,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"blog_category"}},null,8,["label","modelValue","error","init-search"])):$("",!0)]),u("div",se,[p(D,{type:"text","leading-text":B.blog.slugPrefix,modelValue:o.slug,"onUpdate:modelValue":e[4]||(e[4]=s=>o.slug=s),name:"slug",label:d.$trans("blog.props.slug_placeholder"),error:l(t).slug,"onUpdate:error":e[5]||(e[5]=s=>l(t).slug=s)},null,8,["leading-text","modelValue","label","error"])]),u("div",le,[p(A,{modelValue:o.seo.robots,"onUpdate:modelValue":e[6]||(e[6]=s=>o.seo.robots=s),name:"robots",label:d.$trans("blog.props.seo.robots"),error:l(t)["seo.robots"],"onUpdate:error":e[7]||(e[7]=s=>l(t)["seo.robots"]=s)},null,8,["modelValue","label","error"])]),u("div",ae,[p(D,{type:"text",modelValue:o.seo.metaTitle,"onUpdate:modelValue":e[8]||(e[8]=s=>o.seo.metaTitle=s),name:"seoMetaTitle",label:d.$trans("blog.props.seo.meta_title"),error:l(t)["seo.metaTitle"],"onUpdate:error":e[9]||(e[9]=s=>l(t)["seo.metaTitle"]=s)},null,8,["modelValue","label","error"])]),u("div",re,[p(V,{rows:1,modelValue:o.seo.metaDescription,"onUpdate:modelValue":e[10]||(e[10]=s=>o.seo.metaDescription=s),name:"seoMetaDescription",label:d.$trans("blog.props.seo.meta_description"),error:l(t)["seo.metaDescription"],"onUpdate:error":e[11]||(e[11]=s=>l(t)["seo.metaDescription"]=s)},null,8,["modelValue","label","error"])]),u("div",ne,[p(V,{rows:1,modelValue:o.seo.metaKeywords,"onUpdate:modelValue":e[12]||(e[12]=s=>o.seo.metaKeywords=s),name:"seoMetaKeywords",label:d.$trans("blog.props.seo.meta_keywords"),error:l(t)["seo.metaKeywords"],"onUpdate:error":e[13]||(e[13]=s=>l(t)["seo.metaKeywords"]=s)},null,8,["modelValue","label","error"])]),u("div",ie,[g.isLoaded?(c(),y(U,{key:0,tags:"",name:"tags",label:d.$trans("global.select",{attribute:d.$trans("general.tag")}),modelValue:o.tags,"onUpdate:modelValue":e[14]||(e[14]=s=>o.tags=s),error:l(t).tags,"onUpdate:error":e[15]||(e[15]=s=>l(t).tags=s),"init-search":g.tags,"search-action":"tag/list"},null,8,["label","modelValue","error","init-search"])):$("",!0)]),u("div",de,[g.isLoaded?(c(),y(E,{key:0,multiple:"",label:d.$trans("general.file"),module:"blog",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[16]||(e[16]=s=>o.mediaUpdated=!0),onSetHash:e[17]||(e[17]=s=>o.mediaHash.push(s))},null,8,["label","media","media-token"])):$("",!0)])])]),_:1},8,["form"])}}}),pe={class:"space-y-2"},ge={name:"BlogEdit"},fe=Object.assign(ge,{setup(B){const w=S(),T=I(),_=F("$trans");F("emitter");const n={uuid:""},v="blog/",f=R(!1),t=h({...n}),o=g=>{Object.assign(t,g)};return(g,a)=>{const i=m("BaseButton"),k=m("PageHeaderAction"),d=m("PageHeader"),e=m("BaseCard"),r=m("DetailLayoutVertical"),U=m("ShowItem"),D=m("ParentTransition"),A=C("tooltip");return c(),K(z,null,[t.uuid?(c(),y(d,{key:0,title:t.title,navs:[{label:l(_)("blog.blog"),path:"BlogList"}]},{default:b(()=>[p(k,{name:"Blog",title:l(_)("blog.blog"),actions:["list"]},{default:b(()=>[L((c(),y(i,{design:"white",onClick:a[0]||(a[0]=V=>l(T).push({name:"BlogShow",params:{uuid:t.uuid}}))},{default:b(()=>a[7]||(a[7]=[u("i",{class:"fas fa-eye"},null,-1)])),_:1})),[[A,l(_)("general.preview")]])]),_:1},8,["title"])]),_:1},8,["title","navs"])):$("",!0),p(D,{appear:"",visibility:!0},{default:b(()=>[p(U,{"init-url":v,uuid:l(w).params.uuid,onSetItem:o,onRedirectTo:a[5]||(a[5]=V=>l(T).push({name:"Blog"})),refresh:f.value,onRefreshed:a[6]||(a[6]=V=>f.value=!1)},{default:b(()=>[t.uuid?(c(),y(r,{key:0},{detail:b(()=>[u("div",pe,[p(e,null,{default:b(()=>[t.uuid?(c(),y(me,{key:0,blog:t,onRefreshItem:a[1]||(a[1]=V=>f.value=!0)},null,8,["blog"])):$("",!0)]),_:1}),p(e,{"no-padding":""},{title:b(()=>[x(N(l(_)("blog.assets.custom_og")),1)]),default:b(()=>[t.uuid?(c(),y(G,{key:0,blog:t,onRefreshItem:a[2]||(a[2]=V=>f.value=!0)},null,8,["blog"])):$("",!0)]),_:1})])]),default:b(()=>[u("div",null,[p(e,{"no-padding":"","no-content-padding":""},{default:b(()=>[t.uuid?(c(),y(q,{key:0,blog:t,onRefreshItem:a[3]||(a[3]=V=>f.value=!0)},null,8,["blog"])):$("",!0),t.uuid?(c(),y(Z,{key:1,blog:t,onRefreshItem:a[4]||(a[4]=V=>f.value=!0)},null,8,["blog"])):$("",!0)]),_:1})])]),_:1})):$("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{fe as default};
