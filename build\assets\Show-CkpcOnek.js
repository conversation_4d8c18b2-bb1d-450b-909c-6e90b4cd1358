import{i as v,u as L,h as k,l as C,r as s,a as P,o as c,e as n,w as a,f as l,q as g,b as _,d as V,s as r,t as o,y as A,F as D}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},I={name:"FinanceLedgerTypeShow"},j=Object.assign(I,{setup(N){v();const u=L(),m=k(),f={},y="finance/ledgerType/",t=C({...f}),b=e=>{Object.assign(t,e)};return(e,d)=>{const B=s("PageHeaderAction"),$=s("PageHeader"),i=s("BaseDataView"),T=s("BaseButton"),h=s("ShowButton"),w=s("BaseCard"),F=s("ShowItem"),S=s("ParentTransition");return c(),P(D,null,[n($,{title:e.$trans(l(u).meta.trans,{attribute:e.$trans(l(u).meta.label)}),navs:[{label:e.$trans("finance.finance"),path:"Finance"},{label:e.$trans("finance.ledger_type.ledger_type"),path:"FinanceLedgerTypeList"}]},{default:a(()=>[n(B,{name:"FinanceLedgerType",title:e.$trans("finance.ledger_type.ledger_type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(S,{appear:"",visibility:!0},{default:a(()=>[n(F,{"init-url":y,uuid:l(u).params.uuid,onSetItem:b,onRedirectTo:d[1]||(d[1]=p=>l(m).push({name:"FinanceLedgerType"}))},{default:a(()=>[t.uuid?(c(),g(w,{key:0},{title:a(()=>[r(o(t.name),1)]),footer:a(()=>[n(h,null,{default:a(()=>[l(A)("ledger-type:edit")&&!t.isDefault?(c(),g(T,{key:0,design:"primary",onClick:d[0]||(d[0]=p=>l(m).push({name:"FinanceLedgerTypeEdit",params:{uuid:t.uuid}}))},{default:a(()=>[r(o(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:a(()=>[V("dl",H,[n(i,{label:e.$trans("finance.ledger_type.props.alias")},{default:a(()=>[r(o(t.alias||"-"),1)]),_:1},8,["label"]),n(i,{label:e.$trans("finance.ledger_type.props.parent")},{default:a(()=>{var p;return[r(o(((p=t.parent)==null?void 0:p.name)||"-"),1)]}),_:1},8,["label"]),n(i,{class:"col-span-1 sm:col-span-2",label:e.$trans("finance.ledger_type.props.description")},{default:a(()=>[r(o(t.description),1)]),_:1},8,["label"]),n(i,{label:e.$trans("general.created_at")},{default:a(()=>[r(o(t.createdAt.formatted),1)]),_:1},8,["label"]),n(i,{label:e.$trans("general.updated_at")},{default:a(()=>[r(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
