import{u as H,l as w,n as X,r as c,q as p,o,w as n,d as y,e as u,b,h as Y,i as Z,j as L,y as $,m as ee,K as te,z as ae,f as a,a as x,F as ne,v as se,s as k,A as P,t as v}from"./app-BAwPsakn.js";import{i as le,t as oe,a as ie}from"./table-FwhM-Z75.js";const re={class:"grid grid-cols-3 gap-6"},ue={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},me={class:"col-span-2 sm:col-span-1"},ge={class:"col-span-2 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},fe={__name:"Filter",emits:["hide"],setup(R,{emit:A}){const i=H(),d=A,C={search:"",categories:[],startDate:"",endDate:"",tagsIncluded:[],tagsExcluded:[],isArchived:!1},r=w({...C}),h=w({categories:[],tagsIncluded:[],tagsExcluded:[],isLoaded:!(i.query.categories||i.query.tagsIncluded||i.query.tagsExcluded)});return X(async()=>{h.categories=i.query.categories?i.query.categories.split(","):[],h.tagsIncluded=i.query.tagsIncluded?i.query.tagsIncluded.split(","):[],h.tagsExcluded=i.query.tagsExcluded?i.query.tagsExcluded.split(","):[],h.isLoaded=!0}),(_,s)=>{const t=c("BaseInput"),V=c("BaseSelectSearch"),D=c("DatePicker"),S=c("BaseSwitch"),E=c("FilterForm");return o(),p(E,{"init-form":C,form:r,multiple:["categories","tagsIncluded","tagsExcluded"],onHide:s[7]||(s[7]=m=>d("hide"))},{default:n(()=>[y("div",re,[y("div",ue,[u(t,{type:"text",modelValue:r.search,"onUpdate:modelValue":s[0]||(s[0]=m=>r.search=m),name:"search",label:_.$trans("general.search")},null,8,["modelValue","label"])]),y("div",de,[h.isLoaded?(o(),p(V,{key:0,multiple:"",name:"categories",label:_.$trans("global.select",{attribute:_.$trans("blog.category.category")}),modelValue:r.categories,"onUpdate:modelValue":s[1]||(s[1]=m=>r.categories=m),"value-prop":"uuid","init-search":h.categories,"search-action":"option/list","additional-search-query":{type:"blog_category"}},null,8,["label","modelValue","init-search"])):b("",!0)]),y("div",ce,[u(D,{start:r.startDate,"onUpdate:start":s[2]||(s[2]=m=>r.startDate=m),end:r.endDate,"onUpdate:end":s[3]||(s[3]=m=>r.endDate=m),name:"publishedAtBetween",as:"range",label:_.$trans("global.date_between",{attribute:_.$trans("blog.props.published_at")})},null,8,["start","end","label"])]),y("div",me,[h.isLoaded?(o(),p(V,{key:0,tags:"",name:"tagsIncluded",label:_.$trans("general.tags_included"),modelValue:r.tagsIncluded,"onUpdate:modelValue":s[4]||(s[4]=m=>r.tagsIncluded=m),"init-search":h.tagsIncluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):b("",!0)]),y("div",ge,[h.isLoaded?(o(),p(V,{key:0,tags:"",name:"tagsExcluded",label:_.$trans("general.tags_excluded"),modelValue:r.tagsExcluded,"onUpdate:modelValue":s[5]||(s[5]=m=>r.tagsExcluded=m),"init-search":h.tagsExcluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):b("",!0)]),y("div",pe,[u(S,{vertical:"",modelValue:r.isArchived,"onUpdate:modelValue":s[6]||(s[6]=m=>r.isArchived=m),name:"isArchived",label:_.$trans("general.archived")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},be=["onDblclick"],_e={key:0,class:"ml-2"},he={key:1,class:"text-xs text-gray-400"},ve={key:0},ke={key:1},ye={key:0,class:"far fa-check-circle fa-xl text-success"},Ie={key:1,class:"far fa-pen-to-square fa-xl text-danger"},Be={name:"BlogList"},Ae=Object.assign(Be,{setup(R){H();const A=Y();Z();const i=L("emitter"),d=L("$trans");let C=["filter"];$("blog:create")&&C.unshift("create"),$("blog:config")&&C.unshift("config");let r=[];$("blog:edit")&&(r.unshift({name:"unarchive",label:d("general.unarchive"),icon:"fas fa-box-open"}),r.unshift({name:"archive",label:d("general.archive"),icon:"fas fa-box-archive"})),$("blog:delete")&&r.unshift({name:"delete",label:d("general.delete"),icon:"fas fa-trash"});const h="blog/",_=ee(!1),s=w({}),t=w({...le}),V=g=>{Object.assign(s,g),t.items=[],t.pageItems=s.data.map(l=>l.uuid)},D=async g=>{i.emit("actionItem",{uuid:g.uuid,action:"archive",confirmation:!0})},S=async g=>{i.emit("actionItem",{uuid:g.uuid,action:"unarchive",confirmation:!0})},E=async g=>{i.emit("actionItem",{uuid:g.uuid,action:"pin",confirmation:!0})},m=async g=>{i.emit("actionItem",{uuid:g.uuid,action:"unpin",confirmation:!0})},M=g=>{g=="delete"?i.emit("deleteItems",{global:t.global,uuids:t.items}):g=="archive"?i.emit("actionItems",{action:"archiveBlogs",global:t.global,uuids:t.items,confirmation:!0}):g=="unarchive"&&i.emit("actionItems",{action:"unarchiveBlogs",global:t.global,uuids:t.items,confirmation:!0})};return te(()=>[t.items,t.pageItems],([g,l],[q,T])=>{t.all=ie(t)}),(g,l)=>{const q=c("PageHeaderAction"),T=c("PageHeader"),U=c("ParentTransition"),j=c("BaseArrayCheckbox"),N=c("BaseCheckbox"),I=c("DataCell"),O=c("BaseBadge"),z=c("TagSummary"),B=c("FloatingMenuItem"),G=c("FloatingMenu"),K=c("DataRow"),J=c("BaseButton"),Q=c("DataTable"),W=c("ListItem"),F=ae("tooltip");return o(),p(W,{"init-url":h,onSetItems:V},{header:n(()=>[u(T,{title:a(d)("blog.blog")},{default:n(()=>[u(q,{url:"blogs/",name:"Blog",title:a(d)("blog.blog"),actions:a(C),"bulk-actions":a(r),"show-bulk-action":t.items.length>0||t.global,"dropdown-actions":["print","pdf","excel"],headers:s.headers,onToggleFilter:l[0]||(l[0]=e=>_.value=!_.value),onOnBulkAction:M},null,8,["title","actions","bulk-actions","show-bulk-action","headers"])]),_:1},8,["title"])]),filter:n(()=>[u(U,{appear:"",visibility:_.value},{default:n(()=>[u(fe,{onRefresh:l[1]||(l[1]=e=>a(i).emit("listItems")),onHide:l[2]||(l[2]=e=>_.value=!1)})]),_:1},8,["visibility"])]),default:n(()=>[u(U,{appear:"",visibility:!0},{default:n(()=>[u(Q,{onToggleSelectAll:l[6]||(l[6]=e=>t.items=a(oe)(e,t)),onToggleGlobalSelect:l[7]||(l[7]=e=>t.global=!t.global),selected:t,header:s.headers,meta:s.meta,module:"blog",onRefresh:l[8]||(l[8]=e=>a(i).emit("listItems"))},{actionButton:n(()=>[a($)("blog:create")?(o(),p(J,{key:0,onClick:l[5]||(l[5]=e=>a(A).push({name:"BlogCreate"}))},{default:n(()=>[k(v(a(d)("global.add",{attribute:a(d)("blog.blog")})),1)]),_:1})):b("",!0)]),default:n(()=>[(o(!0),x(ne,null,se(s.data,e=>(o(),p(K,{key:e.uuid},{default:n(()=>[u(I,{name:"selectAll"},{default:n(()=>[t.global?b("",!0):(o(),p(j,{key:0,items:t.items,"onUpdate:items":l[3]||(l[3]=f=>t.items=f),value:e.uuid},null,8,["items","value"])),t.global?(o(),p(N,{key:1,modelValue:t.global,"onUpdate:modelValue":l[4]||(l[4]=f=>t.global=f)},null,8,["modelValue"])):b("",!0)]),_:2},1024),u(I,{name:"title",clickable:""},{default:n(()=>[y("div",{onDblclick:f=>a(A).push({name:"BlogShow",params:{uuid:e.uuid}})},[k(v(e.titleExcerpt)+" ",1),e.pinnedAt.value?P((o(),x("span",_e,l[9]||(l[9]=[y("i",{class:"fas fa-bookmark"},null,-1)]))),[[F,a(d)("general.pin")]]):b("",!0),e.subTitleExcerpt?(o(),x("p",he,v(e.subTitleExcerpt),1)):b("",!0)],40,be)]),_:2},1024),u(I,{name:"name"},{default:n(()=>[e.category?(o(),x("span",ve,[u(O,{design:"custom",color:e.category.color},{default:n(()=>[k(v(e.category.name),1)]),_:2},1032,["color"])])):(o(),x("span",ke,"-"))]),_:2},1024),u(I,{name:"publishedAt"},{default:n(()=>{var f;return[k(v(((f=e.publishedAt)==null?void 0:f.formatted)||"-"),1)]}),_:2},1024),u(I,{name:"isPublished"},{default:n(()=>[e.isPublished?P((o(),x("i",ye,null,512)),[[F,a(d)("blog.props.published")]]):P((o(),x("i",Ie,null,512)),[[F,a(d)("blog.props.draft")]])]),_:2},1024),u(I,{name:"tag"},{default:n(()=>[u(z,{summary:e.tagSummary},null,8,["summary"])]),_:2},1024),u(I,{name:"action"},{default:n(()=>[u(G,null,{default:n(()=>[u(B,{icon:"fas fa-arrow-circle-right",onClick:f=>a(A).push({name:"BlogShow",params:{uuid:e.uuid}})},{default:n(()=>[k(v(a(d)("general.show")),1)]),_:2},1032,["onClick"]),e.archivedAt.value?b("",!0):(o(),p(B,{key:0,icon:"fas fa-box-archive",onClick:f=>D(e)},{default:n(()=>[k(v(a(d)("general.archive")),1)]),_:2},1032,["onClick"])),e.archivedAt.value?(o(),p(B,{key:1,icon:"fas fa-box-open",onClick:f=>S(e)},{default:n(()=>[k(v(a(d)("general.unarchive")),1)]),_:2},1032,["onClick"])):b("",!0),e.pinnedAt.value?b("",!0):(o(),p(B,{key:2,icon:"fas fa-bookmark",onClick:f=>E(e)},{default:n(()=>[k(v(a(d)("general.pin")),1)]),_:2},1032,["onClick"])),e.pinnedAt.value?(o(),p(B,{key:3,icon:"far fa-bookmark",onClick:f=>m(e)},{default:n(()=>[k(v(a(d)("general.unpin")),1)]),_:2},1032,["onClick"])):b("",!0),a($)("blog:edit")?(o(),p(B,{key:4,icon:"fas fa-edit",onClick:f=>a(A).push({name:"BlogEdit",params:{uuid:e.uuid}})},{default:n(()=>[k(v(a(d)("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),a($)("blog:delete")?(o(),p(B,{key:5,icon:"fas fa-trash",onClick:f=>a(i).emit("deleteItem",{uuid:e.uuid})},{default:n(()=>[k(v(a(d)("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["selected","header","meta"])]),_:1})]),_:1})}}});export{Ae as default};
