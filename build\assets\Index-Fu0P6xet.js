import{u as T,l as w,n as j,r as l,q as f,o as m,w as e,d as h,e as s,h as N,j as S,y as g,m as V,f as n,a as U,F as q,v as E,s as r,t as u,b as D}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(y,{emit:c}){T();const _=c,v={name:"",startDate:"",endDate:""},d=w({...v}),C=w({isLoaded:!0});return j(async()=>{C.isLoaded=!0}),(p,i)=>{const b=l("DatePicker"),o=l("FilterForm");return m(),f(o,{"init-form":v,form:d,multiple:[],onHide:i[2]||(i[2]=t=>_("hide"))},{default:e(()=>[h("div",O,[h("div",z,[s(b,{start:d.startDate,"onUpdate:start":i[0]||(i[0]=t=>d.startDate=t),end:d.endDate,"onUpdate:end":i[1]||(i[1]=t=>d.endDate=t),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={name:"MessMealLogList"},Q=Object.assign(J,{setup(y){const c=N(),_=S("emitter");let v=["filter","config"];g("meal-log:create")&&v.unshift("create");let d=[];g("meal-log:export")&&(d=["print","pdf","excel"]);const C="mess/mealLog/",p=V(!1),i=w({}),b=o=>{Object.assign(i,o)};return(o,t)=>{const I=l("PageHeaderAction"),B=l("PageHeader"),F=l("ParentTransition"),$=l("DataCell"),k=l("FloatingMenuItem"),P=l("FloatingMenu"),A=l("DataRow"),H=l("BaseButton"),L=l("DataTable"),R=l("ListItem");return m(),f(R,{"init-url":C,"additional-query":{},onSetItems:b},{header:e(()=>[s(B,{title:o.$trans("mess.meal.log.log"),navs:[{label:o.$trans("mess.mess"),path:"Mess"}]},{default:e(()=>[s(I,{url:"mess/meal-logs/",name:"MessMealLog",title:o.$trans("mess.meal.log.log"),actions:n(v),"dropdown-actions":n(d),"config-path":"MessConfig",onToggleFilter:t[0]||(t[0]=a=>p.value=!p.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[s(F,{appear:"",visibility:p.value},{default:e(()=>[s(G,{onRefresh:t[1]||(t[1]=a=>n(_).emit("listItems")),onHide:t[2]||(t[2]=a=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[s(F,{appear:"",visibility:!0},{default:e(()=>[s(L,{header:i.headers,meta:i.meta,module:"mess.meal.log",onRefresh:t[4]||(t[4]=a=>n(_).emit("listItems"))},{actionButton:e(()=>[n(g)("meal-log:create")?(m(),f(H,{key:0,onClick:t[3]||(t[3]=a=>n(c).push({name:"MessMealLogCreate"}))},{default:e(()=>[r(u(o.$trans("global.add",{attribute:o.$trans("mess.meal.log.log")})),1)]),_:1})):D("",!0)]),default:e(()=>[(m(!0),U(q,null,E(i.data,a=>(m(),f(A,{key:a.uuid,onDoubleClick:M=>n(c).push({name:"MessMealLogShow",params:{uuid:a.uuid}})},{default:e(()=>[s($,{name:"date"},{default:e(()=>[r(u(a.date.formatted),1)]),_:2},1024),s($,{name:"meal"},{default:e(()=>[r(u(a.meal.name),1)]),_:2},1024),s($,{name:"menuItems"},{default:e(()=>[r(u(a.menuItems),1)]),_:2},1024),s($,{name:"createdAt"},{default:e(()=>[r(u(a.createdAt.formatted),1)]),_:2},1024),s($,{name:"action"},{default:e(()=>[s(P,null,{default:e(()=>[s(k,{icon:"fas fa-arrow-circle-right",onClick:M=>n(c).push({name:"MessMealLogShow",params:{uuid:a.uuid}})},{default:e(()=>[r(u(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(g)("meal-log:edit")?(m(),f(k,{key:0,icon:"fas fa-edit",onClick:M=>n(c).push({name:"MessMealLogEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(u(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):D("",!0),n(g)("meal-log:create")?(m(),f(k,{key:1,icon:"fas fa-copy",onClick:M=>n(c).push({name:"MessMealLogDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(u(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):D("",!0),n(g)("meal-log:delete")?(m(),f(k,{key:2,icon:"fas fa-trash",onClick:M=>n(_).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(u(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
