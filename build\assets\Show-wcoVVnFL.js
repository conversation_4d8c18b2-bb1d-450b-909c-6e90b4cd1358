import{i as T,u as j,h as H,l as I,r,a as _,o as d,e as t,w as e,f as i,q as b,b as m,d as N,F as f,v as D,s,t as l,y as B,B as E}from"./app-BAwPsakn.js";const F={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},O={name:"ResourceLearningMaterialShow"},z=Object.assign(O,{setup(q){T();const p=j(),w=H(),$={},h="resource/learningMaterial/",a=I({...$}),v=n=>{Object.assign(a,n)};return(n,g)=>{const L=r("PageHeaderAction"),M=r("PageHeader"),y=r("TextMuted"),u=r("BaseDataView"),k=r("ListMedia"),R=r("ViewLog"),S=r("BaseButton"),V=r("ShowButton"),A=r("BaseCard"),C=r("ShowItem"),P=r("ParentTransition");return d(),_(f,null,[t(M,{title:n.$trans(i(p).meta.trans,{attribute:n.$trans(i(p).meta.label)}),navs:[{label:n.$trans("resource.resource"),path:"Resource"},{label:n.$trans("resource.learning_material.learning_material"),path:"ResourceLearningMaterial"}]},{default:e(()=>[t(L,{name:"ResourceLearningMaterial",title:n.$trans("resource.learning_material.learning_material"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(P,{appear:"",visibility:!0},{default:e(()=>[t(C,{"init-url":h,uuid:i(p).params.uuid,"module-uuid":i(p).params.muuid,onSetItem:v,onRedirectTo:g[1]||(g[1]=o=>i(w).push({name:"ResourceLearningMaterial",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(d(),b(A,{key:0},{title:e(()=>[s(l(a.title),1)]),footer:e(()=>[t(V,null,{default:e(()=>[i(B)("learning-material:edit")&&a.isEditable?(d(),b(S,{key:0,design:"primary",onClick:g[0]||(g[0]=o=>i(w).push({name:"ResourceLearningMaterialEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(l(n.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:e(()=>[N("dl",F,[t(u,{label:n.$trans("academic.course.course")},{default:e(()=>[(d(!0),_(f,null,D(a.records,o=>{var c;return d(),_("div",null,[s(l(((c=o.batch.course)==null?void 0:c.name)+" "+o.batch.name)+" ",1),o.subject?(d(),b(y,{key:0},{default:e(()=>[s(l(o.subject.name),1)]),_:2},1024)):m("",!0)])}),256))]),_:1},8,["label"]),t(u,{label:n.$trans("employee.employee")},{default:e(()=>{var o;return[s(l(((o=a.employee)==null?void 0:o.name)||"-")+" ",1),t(y,{block:""},{default:e(()=>{var c;return[s(l(((c=a.employee)==null?void 0:c.designation)||""),1)]}),_:1})]}),_:1},8,["label"]),t(u,{label:n.$trans("resource.learning_material.props.published_at")},{default:e(()=>[s(l(a.publishedAt.formatted),1)]),_:1},8,["label"]),t(u,{label:n.$trans("resource.learning_material.props.description"),class:"col-span-1 sm:col-span-2",html:""},{default:e(()=>[s(l(a.description),1)]),_:1},8,["label"]),t(u,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(k,{media:a.media,url:`/app/resource/learning-materials/${a.uuid}/`},null,8,["media","url"])]),_:1}),i(B)("learning-material:view-log")?(d(),b(u,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(R,{"view-logs":a.viewLogs},null,8,["view-logs"])]),_:1})):m("",!0),i(E)(["student","guardian"],"any")?m("",!0):(d(),_(f,{key:1},[t(u,{label:n.$trans("general.created_at")},{default:e(()=>[s(l(a.createdAt.formatted),1)]),_:1},8,["label"]),t(u,{label:n.$trans("general.updated_at")},{default:e(()=>[s(l(a.updatedAt.formatted),1)]),_:1},8,["label"])],64))])]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{z as default};
