import{H as q,l as v,r as u,q as A,o as b,w as V,d as c,a as y,b as k,e as l,f as s,s as U,t as _,J as F,u as P,F as L}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},T={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},S={key:0,class:"col-span-3 sm:col-span-1"},C={key:1,class:"col-span-3 sm:col-span-1"},E={key:2,class:"col-span-3 sm:col-span-1"},w={key:3,class:"col-span-3 sm:col-span-1"},D={class:"col-span-3"},I={name:"AcademicBookListForm"},W=Object.assign(I,{setup(f){const p={type:"",course:"",subject:"",title:"",author:"",publisher:"",quantity:"",pages:"",description:""},n="academic/bookList/",a=q(n),d=v({types:[],courses:[],subjects:[]}),t=v({...p}),g=r=>{Object.assign(d,r)},j=r=>{var e,m,i;Object.assign(p,{...r,type:((e=r.type)==null?void 0:e.value)||"",course:((m=r.course)==null?void 0:m.uuid)||"",subject:((i=r.subject)==null?void 0:i.uuid)||""}),Object.assign(t,F(p))};return(r,e)=>{const m=u("BaseSelect"),i=u("BaseInput"),$=u("BaseTextarea"),B=u("FormAction");return b(),A(B,{"pre-requisites":!0,onSetPreRequisites:g,"init-url":n,"init-form":p,form:t,"set-form":j,redirect:"AcademicBookList"},{default:V(()=>[c("div",O,[c("div",T,[l(m,{modelValue:t.type,"onUpdate:modelValue":e[0]||(e[0]=o=>t.type=o),name:"type",label:r.$trans("academic.book_list.props.type"),options:d.types,error:s(a).type,"onUpdate:error":e[1]||(e[1]=o=>s(a).type=o)},null,8,["modelValue","label","options","error"])]),c("div",H,[l(m,{modelValue:t.course,"onUpdate:modelValue":e[2]||(e[2]=o=>t.course=o),name:"course",label:r.$trans("academic.course.course"),"value-prop":"uuid",options:d.courses,error:s(a).course,"onUpdate:error":e[3]||(e[3]=o=>s(a).course=o)},{selectedOption:V(o=>[U(_(o.value.nameWithTerm),1)]),listOption:V(o=>[U(_(o.option.nameWithTerm),1)]),_:1},8,["modelValue","label","options","error"])]),c("div",N,[l(m,{modelValue:t.subject,"onUpdate:modelValue":e[4]||(e[4]=o=>t.subject=o),name:"subject",label:r.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:d.subjects,error:s(a).subject,"onUpdate:error":e[5]||(e[5]=o=>s(a).subject=o)},null,8,["modelValue","label","options","error"])]),c("div",R,[l(i,{type:"text",modelValue:t.title,"onUpdate:modelValue":e[6]||(e[6]=o=>t.title=o),name:"title",label:r.$trans("academic.book_list.props.title"),error:s(a).title,"onUpdate:error":e[7]||(e[7]=o=>s(a).title=o)},null,8,["modelValue","label","error"])]),t.type!="notebook"?(b(),y("div",S,[l(i,{type:"text",modelValue:t.author,"onUpdate:modelValue":e[8]||(e[8]=o=>t.author=o),name:"author",label:r.$trans("academic.book_list.props.author"),error:s(a).author,"onUpdate:error":e[9]||(e[9]=o=>s(a).author=o)},null,8,["modelValue","label","error"])])):k("",!0),t.type!="notebook"?(b(),y("div",C,[l(i,{type:"text",modelValue:t.publisher,"onUpdate:modelValue":e[10]||(e[10]=o=>t.publisher=o),name:"publisher",label:r.$trans("academic.book_list.props.publisher"),error:s(a).publisher,"onUpdate:error":e[11]||(e[11]=o=>s(a).publisher=o)},null,8,["modelValue","label","error"])])):k("",!0),t.type=="notebook"?(b(),y("div",E,[l(i,{type:"number",modelValue:t.quantity,"onUpdate:modelValue":e[12]||(e[12]=o=>t.quantity=o),name:"quantity",label:r.$trans("academic.book_list.props.quantity"),error:s(a).quantity,"onUpdate:error":e[13]||(e[13]=o=>s(a).quantity=o)},null,8,["modelValue","label","error"])])):k("",!0),t.type=="notebook"?(b(),y("div",w,[l(i,{type:"number",modelValue:t.pages,"onUpdate:modelValue":e[14]||(e[14]=o=>t.pages=o),name:"pages",label:r.$trans("academic.book_list.props.pages"),error:s(a).pages,"onUpdate:error":e[15]||(e[15]=o=>s(a).pages=o)},null,8,["modelValue","label","error"])])):k("",!0),c("div",D,[l($,{modelValue:t.description,"onUpdate:modelValue":e[16]||(e[16]=o=>t.description=o),name:"description",label:r.$trans("academic.book_list.props.description"),error:s(a).description,"onUpdate:error":e[17]||(e[17]=o=>s(a).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),J={name:"AcademicBookListAction"},G=Object.assign(J,{setup(f){const p=P();return(n,a)=>{const d=u("PageHeaderAction"),t=u("PageHeader"),g=u("ParentTransition");return b(),y(L,null,[l(t,{title:n.$trans(s(p).meta.trans,{attribute:n.$trans(s(p).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.book_list.book_list"),path:"AcademicBookListList"}]},{default:V(()=>[l(d,{name:"AcademicBookList",title:n.$trans("academic.book_list.book_list"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(g,{appear:"",visibility:!0},{default:V(()=>[l(W)]),_:1})],64)}}});export{G as default};
