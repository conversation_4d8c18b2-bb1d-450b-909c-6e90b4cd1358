import{i as v,u as C,h as P,l as T,r as o,a as V,o as m,e as n,w as a,f as c,q as p,b as h,d as N,s as r,t as s,y as H,F as D}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"AcademicBatchInchargeShow"},M=Object.assign(j,{setup(E){v();const d=C(),u=P(),_={},b="academic/batchIncharge/",t=T({..._}),g=e=>{Object.assign(t,e)};return(e,i)=>{const f=o("PageHeaderAction"),B=o("PageHeader"),$=o("TextMuted"),l=o("BaseDataView"),y=o("BaseButton"),A=o("ShowButton"),I=o("BaseCard"),k=o("ShowItem"),w=o("ParentTransition");return m(),V(D,null,[n(B,{title:e.$trans(c(d).meta.trans,{attribute:e.$trans(c(d).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.batch.batch"),path:"AcademicBatch"},{label:e.$trans("academic.batch_incharge.batch_incharge"),path:"AcademicBatchInchargeList"}]},{default:a(()=>[n(f,{name:"AcademicBatchIncharge",title:e.$trans("academic.batch_incharge.batch_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(w,{appear:"",visibility:!0},{default:a(()=>[n(k,{"init-url":b,uuid:c(d).params.uuid,onSetItem:g,onRedirectTo:i[1]||(i[1]=S=>c(u).push({name:"AcademicBatchIncharge"}))},{default:a(()=>[t.uuid?(m(),p(I,{key:0},{title:a(()=>[r(s(t.batch.course.name)+" - "+s(t.batch.name),1)]),footer:a(()=>[n(A,null,{default:a(()=>[c(H)("batch-incharge:edit")?(m(),p(y,{key:0,design:"primary",onClick:i[0]||(i[0]=S=>c(u).push({name:"AcademicBatchInchargeEdit",params:{uuid:t.uuid}}))},{default:a(()=>[r(s(e.$trans("general.edit")),1)]),_:1})):h("",!0)]),_:1})]),default:a(()=>[N("dl",R,[n(l,{label:e.$trans("employee.employee")},{default:a(()=>[r(s(t.employee.name)+" ",1),n($,{block:""},{default:a(()=>[r(s(t.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),n(l,{label:e.$trans("employee.incharge.props.period")},{default:a(()=>[r(s(t.period),1)]),_:1},8,["label"]),n(l,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.incharge.props.remarks")},{default:a(()=>[r(s(t.remarks),1)]),_:1},8,["label"]),n(l,{label:e.$trans("general.created_at")},{default:a(()=>[r(s(t.createdAt.formatted),1)]),_:1},8,["label"]),n(l,{label:e.$trans("general.updated_at")},{default:a(()=>[r(s(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):h("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
