import{i as O,u as L,h as M,j as q,H as z,l as E,m as G,r as s,a as J,o as m,e as o,w as a,f as l,q as p,b as f,d as b,s as u,t as r,y as w,F as K}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},W={class:"text-danger"},X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3"},x={name:"EmployeeDocumentShow"},te=Object.assign(x,{props:{employee:{type:Object,default(){return{}}}},setup(c){O();const g=L(),B=M();q("emitter");const D={},S={status:"",comment:""},$="employee/document/",v=z($),y=E({...S}),_=G(!1),e=E({...D}),h=t=>{Object.assign(e,t)},j=()=>{_.value=!0};return(t,n)=>{const U=s("PageHeaderAction"),A=s("PageHeader"),V=s("BaseBadge"),d=s("BaseDataView"),C=s("ListMedia"),F=s("BaseButton"),P=s("ShowButton"),k=s("BaseCard"),T=s("BaseSelect"),H=s("BaseTextarea"),I=s("FormAction"),N=s("ShowItem"),R=s("ParentTransition");return m(),J(K,null,[o(A,{title:t.$trans(l(g).meta.trans,{attribute:t.$trans(l(g).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:c.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:c.employee.uuid}}}]},{default:a(()=>[o(U,{name:"EmployeeDocument",title:t.$trans("employee.document.document"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(R,{appear:"",visibility:!0},{default:a(()=>[o(N,{"init-url":$,uuid:l(g).params.uuid,"module-uuid":l(g).params.muuid,onSetItem:h,onRedirectTo:n[5]||(n[5]=i=>l(B).push({name:"EmployeeDocument",params:{uuid:c.employee.uuid}})),refresh:_.value,onRefreshed:n[6]||(n[6]=i=>_.value=!1)},{default:a(()=>[e.uuid?(m(),p(k,{key:0},{title:a(()=>[u(r(e.type.name),1)]),footer:a(()=>[o(P,null,{default:a(()=>[l(w)("employee:edit")?(m(),p(F,{key:0,design:"primary",onClick:n[0]||(n[0]=i=>l(B).push({name:"EmployeeDocumentEdit",params:{uuid:c.employee.uuid,muuid:e.uuid}}))},{default:a(()=>[u(r(t.$trans("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:a(()=>[b("dl",Q,[o(d,{label:t.$trans("employee.document.props.title")},{default:a(()=>[u(r(e.title)+" ",1),e.selfUpload?(m(),p(V,{key:0,design:e.verificationStatus.color},{default:a(()=>[u(r(e.verificationStatus.label),1)]),_:1},8,["design"])):f("",!0)]),_:1},8,["label"]),o(d,{class:"col-span-1 sm:col-span-2",label:t.$trans("employee.document.props.description")},{default:a(()=>[u(r(e.description),1)]),_:1},8,["label"]),e.selfUpload&&e.verificationStatus.value=="rejected"?(m(),p(d,{key:0,class:"col-span-1 sm:col-span-2",label:t.$trans("contact.verification.props.comment")},{default:a(()=>[b("span",W,r(e.comment),1)]),_:1},8,["label"])):f("",!0),o(d,{label:t.$trans("employee.document.props.start_date")},{default:a(()=>[u(r(e.startDate.formatted),1)]),_:1},8,["label"]),o(d,{label:t.$trans("employee.document.props.end_date")},{default:a(()=>[u(r(e.endDate.formatted)+" ",1),e.isExpired?(m(),p(V,{key:0,design:"danger"},{default:a(()=>[u(r(t.$trans("employee.document.expired")),1)]),_:1})):f("",!0)]),_:1},8,["label"]),o(d,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[o(C,{media:e.media,url:`/app/employees/${c.employee.uuid}/documents/${e.uuid}/`},null,8,["media","url"])]),_:1}),o(d,{label:t.$trans("general.created_at")},{default:a(()=>[u(r(e.createdAt.formatted),1)]),_:1},8,["label"]),o(d,{label:t.$trans("general.updated_at")},{default:a(()=>[u(r(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0),l(w)("employee:self-service-action")&&e.uuid&&e.selfUpload&&e.verificationStatus.value=="pending"?(m(),p(k,{key:1},{title:a(()=>[u(r(t.$trans("contact.verification.props.action")),1)]),default:a(()=>[o(I,{"no-card":"","keep-adding":!1,"init-url":$,uuid:c.employee.uuid,"module-uuid":e.uuid,"no-data-fetch":!0,action:"action","init-form":S,form:y,"after-submit":j},{default:a(()=>[b("div",X,[b("div",Y,[o(T,{name:"status",label:t.$trans("global.select",{attribute:t.$trans("contact.verification.props.status")}),modelValue:y.status,"onUpdate:modelValue":n[1]||(n[1]=i=>y.status=i),options:[{value:"verify",label:t.$trans("contact.verification.action.verify")},{value:"reject",label:t.$trans("contact.verification.action.reject")}],error:l(v).status,"onUpdate:error":n[2]||(n[2]=i=>l(v).status=i)},null,8,["label","modelValue","options","error"])]),b("div",Z,[o(H,{modelValue:y.comment,"onUpdate:modelValue":n[3]||(n[3]=i=>y.comment=i),name:"comment",label:t.$trans("contact.comment"),error:l(v).comment,"onUpdate:error":n[4]||(n[4]=i=>l(v).comment=i)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form"])]),_:1})):f("",!0)]),_:1},8,["uuid","module-uuid","refresh"])]),_:1})],64)}}});export{te as default};
