import{u as M,G as y,H as D,l as N,n as I,r as i,q as h,b as k,o as b,w as a,d as w,e as n,f as u,J as O,i as P,h as R,m as V,a as A,s as r,t as o,y as H,F as E}from"./app-BAwPsakn.js";const L={class:"grid grid-cols-1 gap-6"},q={class:"col"},G={name:"StudentTransferMedia"},J=Object.assign(G,{props:{student:{type:Object,required:!0}},emits:["completed"],setup(v,{emit:_}){const g=M(),$=v,f={transferCertificate:[],mediaUpdated:!1,mediaToken:y(),mediaHash:[]},c="student/transfer/",e=D(c),d=N({...f});N({isLoaded:!g.params.uuid});const t=()=>{d.mediaToken=y(),d.mediaHash=[]},l=m=>{location.reload()},S=m=>{Object.assign(f,{transferCertificate:m.media,mediaToken:m.mediaToken||y()}),Object.assign(d,O(f))};return I(()=>{$.student.uuid&&S($.student)}),(m,s)=>{const T=i("MediaUpload"),B=i("FormAction");return v.student.uuid?(b(),h(B,{key:0,"no-card":"","no-data-fetch":"",action:"media","init-url":c,"init-form":f,form:d,"after-submit":l,"keep-adding":!1,onResetMediaFiles:t},{default:a(()=>[w("div",L,[w("div",q,[n(T,{multiple:"",label:m.$trans("general.file"),module:"admission",section:"transfer_certificate",media:d.transferCertificate,"media-token":d.mediaToken,onIsUpdated:s[0]||(s[0]=p=>d.mediaUpdated=!0),onSetHash:s[1]||(s[1]=p=>d.mediaHash.push(p)),error:u(e).transferCertificate,"onUpdate:error":s[2]||(s[2]=p=>u(e).transferCertificate=p)},null,8,["label","media","media-token","error"])])])]),_:1},8,["form"])):k("",!0)}}}),z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},K={name:"StudentTransferShow"},W=Object.assign(K,{setup(v){P();const _=M(),g=R(),$={},f="student/transfer/",c=V(!1),e=N({...$}),d=t=>{Object.assign(e,t)};return(t,l)=>{const S=i("PageHeaderAction"),m=i("PageHeader"),s=i("BaseDataView"),T=i("ListMedia"),B=i("BaseButton"),p=i("ShowButton"),F=i("BaseCard"),U=i("ShowItem"),j=i("ParentTransition");return b(),A(E,null,[n(m,{title:t.$trans(u(_).meta.trans,{attribute:t.$trans(u(_).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:t.$trans("student.transfer.transfer"),path:"StudentTransferList"}]},{default:a(()=>[n(S,{name:"StudentTransfer",title:t.$trans("student.transfer.transfer"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(j,{appear:"",visibility:!0},{default:a(()=>[n(U,{"init-url":f,uuid:u(_).params.uuid,onSetItem:d,onRedirectTo:l[2]||(l[2]=C=>u(g).push({name:"StudentTransfer"})),refresh:c.value,onRefreshed:l[3]||(l[3]=C=>c.value=!1)},{default:a(()=>[e.uuid?(b(),h(F,{key:0},{title:a(()=>[r(o(e.name),1)]),footer:a(()=>[n(p,null,{default:a(()=>[u(H)("student:transfer")?(b(),h(B,{key:0,design:"primary",onClick:l[0]||(l[0]=C=>u(g).push({name:"StudentTransferEdit",params:{uuid:e.uuid}}))},{default:a(()=>[r(o(t.$trans("general.edit")),1)]),_:1})):k("",!0)]),_:1})]),default:a(()=>[w("dl",z,[n(s,{label:t.$trans("student.admission.props.code_number")},{default:a(()=>[r(o(e.codeNumber),1)]),_:1},8,["label"]),n(s,{label:t.$trans("contact.props.birth_date")},{default:a(()=>[r(o(e.birthDate.formatted),1)]),_:1},8,["label"]),n(s,{label:t.$trans("contact.props.father_name")},{default:a(()=>[r(o(e.fatherName),1)]),_:1},8,["label"]),n(s,{label:t.$trans("contact.props.mother_name")},{default:a(()=>[r(o(e.motherName),1)]),_:1},8,["label"]),n(s,{label:t.$trans("academic.course.course")},{default:a(()=>[r(o(e.courseName+" "+e.batchName),1)]),_:1},8,["label"]),n(s,{label:t.$trans("student.admission.props.date")},{default:a(()=>[r(o(e.joiningDate.formatted),1)]),_:1},8,["label"]),n(s,{label:t.$trans("student.transfer.props.date")},{default:a(()=>[r(o(e.leavingDate.formatted),1)]),_:1},8,["label"]),n(s,{label:t.$trans("student.transfer_request.props.certificate_number")},{default:a(()=>[r(o(e.transferCertificateNumber),1)]),_:1},8,["label"]),n(s,{label:t.$trans("student.transfer.props.reason")},{default:a(()=>[r(o(e.reason),1)]),_:1},8,["label"]),n(s,{label:t.$trans("student.transfer.props.remarks")},{default:a(()=>[r(o(e.leavingRemarks),1)]),_:1},8,["label"]),n(s,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[n(T,{section:"transfer_certificate",media:e.media,url:`/app/student/transfers/${e.uuid}/`},null,8,["media","url"])]),_:1})])]),_:1})):k("",!0),e.uuid&&u(H)("student:transfer")?(b(),h(F,{key:1},{title:a(()=>[r(o(t.$trans("global.upload",{attribute:t.$trans("academic.certificate.types.transfer_certificate")})),1)]),default:a(()=>[n(J,{student:e,onCompleted:l[1]||(l[1]=C=>c.value=!0)},null,8,["student"])]),_:1})):k("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{W as default};
