import{l as E,r as l,q as v,o as d,w as e,d as w,e as n,h as M,j as N,y as c,m as B,f as o,a as S,F as U,v as O,s as p,t as u,b as I}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(V,{emit:f}){const y=f,g={name:"",alias:""},r=E({...g});return(b,s)=>{const m=l("BaseInput"),_=l("FilterForm");return d(),v(_,{"init-form":g,form:r,onHide:s[2]||(s[2]=$=>y("hide"))},{default:e(()=>[w("div",q,[w("div",z,[n(m,{type:"text",modelValue:r.name,"onUpdate:modelValue":s[0]||(s[0]=$=>r.name=$),name:"name",label:b.$trans("employee.department.props.name")},null,8,["modelValue","label"])]),w("div",G,[n(m,{type:"text",modelValue:r.alias,"onUpdate:modelValue":s[1]||(s[1]=$=>r.alias=$),name:"alias",label:b.$trans("employee.department.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={name:"EmployeeDepartmentList"},W=Object.assign(K,{setup(V){const f=M(),y=N("emitter");let g=["filter"];c("department:create")&&g.unshift("create");let r=[];c("department:export")&&(r=["print","pdf","excel"]),c("department:create")&&r.unshift("import");const b="employee/department/",s=B(!1),m=B(!1),_=E({}),$=i=>{Object.assign(_,i)};return(i,t)=>{const h=l("PageHeaderAction"),H=l("PageHeader"),T=l("BaseImport"),F=l("ParentTransition"),k=l("DataCell"),C=l("FloatingMenuItem"),A=l("FloatingMenu"),P=l("DataRow"),R=l("BaseButton"),j=l("DataTable"),L=l("ListItem");return d(),v(L,{"init-url":b,onSetItems:$},{header:e(()=>[n(H,{title:i.$trans("employee.department.department"),navs:[{label:i.$trans("employee.employee"),path:"Employee"}]},{default:e(()=>[n(h,{url:"employee/departments/",name:"EmployeeDepartment",title:i.$trans("employee.department.department"),actions:o(g),"dropdown-actions":o(r),onToggleFilter:t[0]||(t[0]=a=>s.value=!s.value),onToggleImport:t[1]||(t[1]=a=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:e(()=>[n(F,{appear:"",visibility:m.value},{default:e(()=>[n(T,{path:"employee/departments/import",onCancelled:t[2]||(t[2]=a=>m.value=!1),onHide:t[3]||(t[3]=a=>m.value=!1),onCompleted:t[4]||(t[4]=a=>o(y).emit("listItems"))})]),_:1},8,["visibility"])]),filter:e(()=>[n(F,{appear:"",visibility:s.value},{default:e(()=>[n(J,{onRefresh:t[5]||(t[5]=a=>o(y).emit("listItems")),onHide:t[6]||(t[6]=a=>s.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(j,{header:_.headers,meta:_.meta,module:"employee.department",onRefresh:t[8]||(t[8]=a=>o(y).emit("listItems"))},{actionButton:e(()=>[o(c)("department:create")?(d(),v(R,{key:0,onClick:t[7]||(t[7]=a=>o(f).push({name:"EmployeeDepartmentCreate"}))},{default:e(()=>[p(u(i.$trans("global.add",{attribute:i.$trans("employee.department.department")})),1)]),_:1})):I("",!0)]),default:e(()=>[(d(!0),S(U,null,O(_.data,a=>(d(),v(P,{key:a.uuid,onDoubleClick:D=>o(f).push({name:"EmployeeDepartmentShow",params:{uuid:a.uuid}})},{default:e(()=>[n(k,{name:"name"},{default:e(()=>[p(u(a.name),1)]),_:2},1024),n(k,{name:"alias"},{default:e(()=>[p(u(a.alias),1)]),_:2},1024),n(k,{name:"createdAt"},{default:e(()=>[p(u(a.createdAt.formatted),1)]),_:2},1024),n(k,{name:"action"},{default:e(()=>[n(A,null,{default:e(()=>[n(C,{icon:"fas fa-arrow-circle-right",onClick:D=>o(f).push({name:"EmployeeDepartmentShow",params:{uuid:a.uuid}})},{default:e(()=>[p(u(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(c)("department:edit")?(d(),v(C,{key:0,icon:"fas fa-edit",onClick:D=>o(f).push({name:"EmployeeDepartmentEdit",params:{uuid:a.uuid}})},{default:e(()=>[p(u(i.$trans("general.edit")),1)]),_:2},1032,["onClick"])):I("",!0),o(c)("department:create")?(d(),v(C,{key:1,icon:"fas fa-copy",onClick:D=>o(f).push({name:"EmployeeDepartmentDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[p(u(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):I("",!0),o(c)("department:delete")?(d(),v(C,{key:2,icon:"fas fa-trash",onClick:D=>o(y).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[p(u(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])):I("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
