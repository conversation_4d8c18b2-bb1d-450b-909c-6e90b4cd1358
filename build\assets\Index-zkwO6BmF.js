import{u as H,l as k,n as L,r as s,q as b,o as $,w as e,d as F,e as t,h as R,j as V,m as j,f as i,a as N,F as q,v as E,s as r,t as u}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(P,{emit:d}){H();const c=d,p={search:""},f=k({...p}),C=k({isLoaded:!0});return L(async()=>{C.isLoaded=!0}),(m,l)=>{const h=s("BaseInput"),o=s("FilterForm");return $(),b(o,{"init-form":p,form:f,multiple:[],onHide:l[1]||(l[1]=a=>c("hide"))},{default:e(()=>[F("div",O,[F("div",U,[t(h,{type:"text",modelValue:f.search,"onUpdate:modelValue":l[0]||(l[0]=a=>f.search=a),name:"search",label:m.$trans("general.search")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={name:"SitePageList"},K=Object.assign(G,{setup(P){const d=R(),c=V("emitter");let p=["filter"];p.unshift("create");let f=["print","pdf","excel"];const C="site/page/",m=j(!1),l=k({}),h=o=>{Object.assign(l,o)};return(o,a)=>{const S=s("BaseButton"),B=s("PageHeaderAction"),D=s("PageHeader"),w=s("ParentTransition"),_=s("DataCell"),I=s("TextMuted"),g=s("FloatingMenuItem"),y=s("FloatingMenu"),T=s("DataRow"),M=s("DataTable"),A=s("ListItem");return $(),b(A,{"init-url":C,"additional-query":{},onSetItems:h},{header:e(()=>[t(D,{title:o.$trans("site.page.page"),navs:[{label:o.$trans("site.site"),path:"Site"}]},{default:e(()=>[t(B,{url:"site/pages/",name:"SitePage",title:o.$trans("site.page.page"),actions:i(p),"dropdown-actions":i(f),"config-path":"SiteConfig",onToggleFilter:a[1]||(a[1]=n=>m.value=!m.value)},{after:e(()=>[t(S,{design:"white",onClick:a[0]||(a[0]=n=>i(d).push({name:"SiteConfig"}))},{default:e(()=>a[6]||(a[6]=[F("i",{class:"fas fa-cog"},null,-1)])),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(w,{appear:"",visibility:m.value},{default:e(()=>[t(z,{onRefresh:a[2]||(a[2]=n=>i(c).emit("listItems")),onHide:a[3]||(a[3]=n=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(w,{appear:"",visibility:!0},{default:e(()=>[t(M,{header:l.headers,meta:l.meta,module:"site.page",onRefresh:a[5]||(a[5]=n=>i(c).emit("listItems"))},{actionButton:e(()=>[t(S,{onClick:a[4]||(a[4]=n=>i(d).push({name:"SitePageCreate"}))},{default:e(()=>[r(u(o.$trans("global.add",{attribute:o.$trans("site.page.page")})),1)]),_:1})]),default:e(()=>[($(!0),N(q,null,E(l.data,n=>($(),b(T,{key:n.uuid,onDoubleClick:v=>i(d).push({name:"SitePageShow",params:{uuid:n.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[r(u(n.name),1)]),_:2},1024),t(_,{name:"title"},{default:e(()=>[r(u(n.title)+" ",1),t(I,{block:""},{default:e(()=>[r(u(n.subTitle),1)]),_:2},1024)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[r(u(n.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(y,null,{default:e(()=>[t(g,{icon:"fas fa-arrow-circle-right",onClick:v=>i(d).push({name:"SitePageShow",params:{uuid:n.uuid}})},{default:e(()=>[r(u(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-edit",onClick:v=>i(d).push({name:"SitePageEdit",params:{uuid:n.uuid}})},{default:e(()=>[r(u(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-copy",onClick:v=>i(d).push({name:"SitePageDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[r(u(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-trash",onClick:v=>i(c).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[r(u(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
