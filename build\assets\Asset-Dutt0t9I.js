import{r,a as n,o as c,d as i,e as m}from"./app-BAwPsakn.js";const b={class:"overflow-hidden bg-black lg:rounded-t-lg"},u={name:"SiteBlockAsset"},v=Object.assign(u,{props:{block:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},emits:["refreshItem"],setup(e,{emit:o}){const t=o;return(l,s)=>{const a=r("ImageUpload");return c(),n("div",b,[i("div",null,[m(a,{class:"h-32 w-full lg:h-48",disabled:e.disabled,label:l.$trans("site.assets.cover"),src:e.block.assets.cover,"upload-path":`site/blocks/${e.block.uuid}/assets/cover`,"remove-path":`site/blocks/${e.block.uuid}/assets/cover`,design:"modern","show-label":!1,onUploaded:s[0]||(s[0]=d=>t("refreshItem")),onRemoved:s[1]||(s[1]=d=>t("refreshItem"))},null,8,["disabled","label","src","upload-path","remove-path"])])])}}});export{v as _};
