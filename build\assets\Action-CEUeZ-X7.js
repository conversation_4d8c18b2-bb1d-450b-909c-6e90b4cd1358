import{H as v,l as f,r as m,q as U,o as g,w as c,d as u,e as l,f as t,J as R,u as F,a as P,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},k={class:"col-span-4 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},y={name:"AssetBuildingRoomForm"},E=Object.assign(y,{setup(_){const i={name:"",number:"",floor:"",description:""},n="asset/building/room/",r=v(n),d=f({floors:[]}),s=f({...i}),p=a=>{Object.assign(d,a)},B=a=>{Object.assign(i,{...a,floor:a.floorUuid}),Object.assign(s,R(i))};return(a,e)=>{const b=m("BaseInput"),V=m("BaseSelect"),A=m("BaseTextarea"),$=m("FormAction");return g(),U($,{"pre-requisites":!0,onSetPreRequisites:p,"init-url":n,"init-form":i,form:s,"set-form":B,redirect:"AssetBuildingRoom"},{default:c(()=>[u("div",q,[u("div",H,[l(b,{type:"text",modelValue:s.name,"onUpdate:modelValue":e[0]||(e[0]=o=>s.name=o),name:"name",label:a.$trans("asset.building.room.props.name"),error:t(r).name,"onUpdate:error":e[1]||(e[1]=o=>t(r).name=o),autofocus:""},null,8,["modelValue","label","error"])]),u("div",O,[l(b,{type:"text",modelValue:s.number,"onUpdate:modelValue":e[2]||(e[2]=o=>s.number=o),name:"number",label:a.$trans("asset.building.room.props.number"),error:t(r).number,"onUpdate:error":e[3]||(e[3]=o=>t(r).number=o),autofocus:""},null,8,["modelValue","label","error"])]),u("div",k,[l(V,{modelValue:s.floor,"onUpdate:modelValue":e[4]||(e[4]=o=>s.floor=o),name:"floor",label:a.$trans("asset.building.floor.floor"),options:d.floors,"label-prop":"nameWithBlock","value-prop":"uuid",error:t(r).floor,"onUpdate:error":e[5]||(e[5]=o=>t(r).floor=o)},null,8,["modelValue","label","options","error"])]),u("div",T,[l(A,{modelValue:s.description,"onUpdate:modelValue":e[6]||(e[6]=o=>s.description=o),name:"description",label:a.$trans("asset.building.room.props.description"),error:t(r).description,"onUpdate:error":e[7]||(e[7]=o=>t(r).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),S={name:"AssetBuildingRoomAction"},C=Object.assign(S,{setup(_){const i=F();return(n,r)=>{const d=m("PageHeaderAction"),s=m("PageHeader"),p=m("ParentTransition");return g(),P(j,null,[l(s,{title:n.$trans(t(i).meta.trans,{attribute:n.$trans(t(i).meta.label)}),navs:[{label:n.$trans("asset.asset"),path:"Asset"},{label:n.$trans("asset.building.building"),path:"AssetBuilding"},{label:n.$trans("asset.building.room.room"),path:"AssetBuildingRoomList"}]},{default:c(()=>[l(d,{name:"AssetBuildingRoom",title:n.$trans("asset.building.room.room"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(p,{appear:"",visibility:!0},{default:c(()=>[l(E)]),_:1})],64)}}});export{C as default};
