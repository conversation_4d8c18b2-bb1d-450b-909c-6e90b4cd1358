import{u as y,H as U,l as _,r as l,q as b,o as g,w as f,d,e as i,f as r,b as F,J as D,a as P,F as A}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},S={name:"EmployeeDesignationForm"},T=Object.assign(S,{setup(V){const u=y(),t={name:"",alias:"",parent:"",description:""},c="employee/designation/",s=U(c),n=_({...t}),p=_({parent:"",isLoaded:!u.params.uuid}),$=o=>{var e,m;Object.assign(t,{name:o.name,alias:o.alias,description:o.description,parent:(e=o.parent)==null?void 0:e.uuid}),Object.assign(n,D(t)),p.parent=(m=o.parent)==null?void 0:m.uuid,p.isLoaded=!0};return(o,e)=>{const m=l("BaseInput"),v=l("BaseSelectSearch"),B=l("BaseTextarea"),E=l("FormAction");return g(),b(E,{"init-url":c,"init-form":t,form:n,"set-form":$,redirect:"EmployeeDesignation"},{default:f(()=>[d("div",H,[d("div",j,[i(m,{type:"text",modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=a=>n.name=a),name:"name",label:o.$trans("employee.designation.props.name"),error:r(s).name,"onUpdate:error":e[1]||(e[1]=a=>r(s).name=a),autofocus:""},null,8,["modelValue","label","error"])]),d("div",k,[i(m,{type:"text",modelValue:n.alias,"onUpdate:modelValue":e[2]||(e[2]=a=>n.alias=a),name:"alias",label:o.$trans("employee.designation.props.alias"),error:r(s).alias,"onUpdate:error":e[3]||(e[3]=a=>r(s).alias=a),autofocus:""},null,8,["modelValue","label","error"])]),d("div",L,[p.isLoaded?(g(),b(v,{key:0,name:"parent",label:o.$trans("global.select",{attribute:o.$trans("employee.designation.props.parent")}),modelValue:n.parent,"onUpdate:modelValue":e[4]||(e[4]=a=>n.parent=a),error:r(s).parent,"onUpdate:error":e[5]||(e[5]=a=>r(s).parent=a),"init-search":p.parent,"search-action":"employee/designation/list"},null,8,["label","modelValue","error","init-search"])):F("",!0)]),d("div",O,[i(B,{modelValue:n.description,"onUpdate:modelValue":e[6]||(e[6]=a=>n.description=a),name:"description",label:o.$trans("employee.designation.props.description"),error:r(s).description,"onUpdate:error":e[7]||(e[7]=a=>r(s).description=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),C={name:"EmployeeDesignationAction"},w=Object.assign(C,{setup(V){const u=y();return(t,c)=>{const s=l("PageHeaderAction"),n=l("PageHeader"),p=l("ParentTransition");return g(),P(A,null,[i(n,{title:t.$trans(r(u).meta.trans,{attribute:t.$trans(r(u).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:t.$trans("employee.designation.designation"),path:"EmployeeDesignationList"}]},{default:f(()=>[i(s,{name:"EmployeeDesignation",title:t.$trans("employee.designation.designation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(p,{appear:"",visibility:!0},{default:f(()=>[i(T)]),_:1})],64)}}});export{w as default};
