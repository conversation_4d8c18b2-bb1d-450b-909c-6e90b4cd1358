import{i as ne,u as se,h as re,g as w,l as de,r as s,a as j,o as y,e as a,w as n,f as r,q,b as A,d as ue,s as d,t as u,F as oe}from"./app-BAwPsakn.js";const ie={key:0,class:"mb-4"},pe={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ce={name:"EmployeeProfileEditRequestShow"},be=Object.assign(ce,{props:{employee:{type:Object,default(){return{}}}},setup($){ne();const g=se(),F=re(),D={},O="employee/profileEditRequest/",M=w("contact.uniqueIdNumber1Label"),G=w("contact.uniqueIdNumber2Label"),U=w("contact.uniqueIdNumber3Label"),J=w("contact.uniqueIdNumber4Label"),K=w("contact.uniqueIdNumber5Label"),e=de({...D}),Q=t=>{Object.assign(e,t)};return(t,L)=>{const W=s("PageHeaderAction"),X=s("PageHeader"),Y=s("BaseAlert"),Z=s("BaseBadge"),o=s("BaseDataView"),l=s("HorizontalListItem"),f=s("HorizontalList"),N=s("BaseFieldset"),x=s("ListMedia"),ee=s("ShowButton"),ae=s("BaseCard"),te=s("ShowItem"),le=s("ParentTransition");return y(),j(oe,null,[a(X,{title:t.$trans(r(g).meta.trans,{attribute:t.$trans(r(g).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:$.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:$.employee.uuid}}}]},{default:n(()=>[a(W,{name:"EmployeeProfileEditRequest",title:t.$trans("employee.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(le,{appear:"",visibility:!0},{default:n(()=>[a(te,{"init-url":O,uuid:r(g).params.uuid,"module-uuid":r(g).params.muuid,onSetItem:Q,onRedirectTo:L[0]||(L[0]=_=>r(F).push({name:"EmployeeProfileEditRequest",params:{uuid:$.employee.uuid}}))},{default:n(()=>[e.uuid?(y(),q(ae,{key:0},{title:n(()=>[d(u(t.$trans("employee.edit_request.request_by_name",{attribute:e.user.profile.name})),1)]),footer:n(()=>[a(ee)]),default:n(()=>[e.isRejected&&e.comment?(y(),j("div",ie,[a(Y,{design:"error",size:"xs"},{default:n(()=>[d(u(e.comment),1)]),_:1})])):A("",!0),ue("dl",pe,[a(o,{label:t.$trans("employee.edit_request.props.status")},{default:n(()=>[a(Z,{design:e.status.color},{default:n(()=>[d(u(e.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),a(o,{class:"col-span-1 sm:col-span-2"},{default:n(()=>{var _,I,h,B,P,z,E,R,S,H,k,C,V;return[a(f,null,{default:n(()=>[a(l,{label:t.$trans("contact.props.contact_number"),value:e.data.new.contactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.alternate_contact_number"),value:e.data.new.alternateContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.email"),value:e.data.new.email},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_name"),value:e.data.new.fatherName},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_name"),value:e.data.new.motherName},null,8,["label","value"]),a(l,{label:r(M),value:e.data.new.uniqueIdNumber1},null,8,["label","value"]),a(l,{label:r(G),value:e.data.new.uniqueIdNumber2},null,8,["label","value"]),a(l,{label:r(U),value:e.data.new.uniqueIdNumber3},null,8,["label","value"]),a(l,{label:r(J),value:e.data.new.uniqueIdNumber4},null,8,["label","value"]),a(l,{label:r(K),value:e.data.new.uniqueIdNumber5},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.birth_place"),value:e.data.new.birthPlace},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.nationality"),value:e.data.new.nationality},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_tongue"),value:e.data.new.motherTongue},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.blood_group"),value:t.$trans("list.blood_groups."+e.data.new.bloodGroup)},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.marital_status"),value:t.$trans("list.marital_statuses."+e.data.new.maritalStatus)},null,8,["label","value"]),a(l,{label:t.$trans("contact.religion.religion"),value:e.data.new.religion},null,8,["label","value"]),a(l,{label:t.$trans("contact.category.category"),value:e.data.new.category},null,8,["label","value"]),a(l,{label:t.$trans("contact.caste.caste"),value:e.data.new.caste},null,8,["label","value"])]),_:1}),(_=e.data.new.presentAddress)!=null&&_.addressLine1||(I=e.data.new.presentAddress)!=null&&I.addressLine2||(h=e.data.new.presentAddress)!=null&&h.city||(B=e.data.new.presentAddress)!=null&&B.state||(P=e.data.new.presentAddress)!=null&&P.zipcode||(z=e.data.new.presentAddress)!=null&&z.country?(y(),q(N,{key:0,class:"mt-4"},{legend:n(()=>[d(u(t.$trans("contact.props.present_address")),1)]),default:n(()=>[a(f,null,{default:n(()=>{var i,p,c,m,b,v;return[a(l,{label:t.$trans("contact.props.address.address_line1"),value:(i=e.data.new.presentAddress)==null?void 0:i.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(p=e.data.new.presentAddress)==null?void 0:p.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:(c=e.data.new.presentAddress)==null?void 0:c.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(m=e.data.new.presentAddress)==null?void 0:m.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(b=e.data.new.presentAddress)==null?void 0:b.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(v=e.data.new.presentAddress)==null?void 0:v.country},null,8,["label","value"])]}),_:1})]),_:1})):A("",!0),(E=e.data.new.permanentAddress)!=null&&E.sameAsPresentAddress||(R=e.data.new.permanentAddress)!=null&&R.addressLine1||(S=e.data.new.permanentAddress)!=null&&S.addressLine2||(H=e.data.new.permanentAddress)!=null&&H.city||(k=e.data.new.permanentAddress)!=null&&k.state||(C=e.data.new.permanentAddress)!=null&&C.zipcode||(V=e.data.new.permanentAddress)!=null&&V.country?(y(),q(N,{key:1,class:"mt-4"},{legend:n(()=>[d(u(t.$trans("contact.props.permanent_address")),1)]),default:n(()=>[a(f,null,{default:n(()=>{var i,p,c,m,b,v,T;return[a(l,{label:t.$trans("contact.props.same_as_present_address"),value:(i=e.data.new.permanentAddress)!=null&&i.sameAsPresentAddress?t.$trans("general.yes"):t.$trans("general.no")},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line1"),value:(p=e.data.new.permanentAddress)==null?void 0:p.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(c=e.data.new.permanentAddress)==null?void 0:c.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:(m=e.data.new.permanentAddress)==null?void 0:m.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(b=e.data.new.permanentAddress)==null?void 0:b.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(v=e.data.new.permanentAddress)==null?void 0:v.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(T=e.data.new.permanentAddress)==null?void 0:T.country},null,8,["label","value"])]}),_:1})]),_:1})):A("",!0)]}),_:1}),a(o,{class:"col-span-1 sm:col-span-2"},{default:n(()=>[a(x,{media:e.media,url:`/app/employees/${$.employee.uuid}/edit-requests/${e.uuid}/`},null,8,["media","url"])]),_:1}),a(o,{label:t.$trans("general.created_at")},{default:n(()=>[d(u(e.createdAt.formatted),1)]),_:1},8,["label"]),a(o,{label:t.$trans("general.updated_at")},{default:n(()=>[d(u(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):A("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{be as default};
