import{u as Q,h as re,j as se,G as J,H as oe,g as A,l as T,n as ae,J as ne,r as i,q,o as p,w as u,e as n,d as l,s as b,t as c,f as o,a as z,b as N,I as K,F as le}from"./app-BAwPsakn.js";const de={class:"mt-4 grid grid-cols-3 gap-6"},ue={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},ge={class:"mt-4 grid grid-cols-3 gap-6"},Ae={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},Ue={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},Le={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},$e={class:"col-span-3 sm:col-span-1"},Pe={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},Se={key:0,class:"col-span-3 sm:col-span-1"},ke={key:1,class:"col-span-3 sm:col-span-1"},Ce={class:"grid grid-cols-3 gap-6"},Be={class:"grid grid-cols-3 gap-6"},Re={class:"col-span-3 sm:col-span-1"},Te={class:"mt-4 grid grid-cols-3 gap-6"},ze={class:"grid grid-cols-1"},Fe={class:"col"},Ge={name:"EmployeeProfileEditRequest"},je=Object.assign(Ge,{props:{employee:{type:Object,default(){return{}}}},setup(m){const L=Q();re(),se("emitter");const g=m,v={contactNumber:"",alternateContactNumber:"",email:"",fatherName:"",motherName:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",maritalStatus:"",religion:"",category:"",caste:"",presentAddress:{},permanentAddress:{sameAsPresentAddress:!1},media:[],mediaUpdated:!1,mediaToken:J(),mediaHash:[]},E="employee/profileEditRequest/",R="employee/",a=oe(E),W=A("contact.uniqueIdNumber1Label"),X=A("contact.uniqueIdNumber2Label"),Y=A("contact.uniqueIdNumber3Label"),Z=A("contact.uniqueIdNumber4Label"),x=A("contact.uniqueIdNumber5Label"),_=A("contact.enableCategoryField"),h=A("contact.enableCasteField"),f=T({genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[]}),r=T({...v}),V=T({religion:"",category:"",caste:"",isLoaded:!L.params.uuid}),ee=s=>{Object.assign(f,s)},te=()=>{r.mediaToken=J(),r.mediaHash=[]};return ae(async()=>{var e,S,U,I,d,y,$,P,k,C,B,t,F,G,j,H,O,w,M,D;let s=(e=g.employee)==null?void 0:e.contact;Object.assign(v,{contactNumber:s.contactNumber,alternateContactNumber:(S=s.alternateRecords)==null?void 0:S.contactNumber,email:s.email,fatherName:s.fatherName,motherName:s.motherName,alternateEmail:(U=s.alternateRecords)==null?void 0:U.email,uniqueIdNumber1:s.uniqueIdNumber1,uniqueIdNumber2:s.uniqueIdNumber2,uniqueIdNumber3:s.uniqueIdNumber3,uniqueIdNumber4:s.uniqueIdNumber4,uniqueIdNumber5:s.uniqueIdNumber5,birthPlace:s.birthPlace,bloodGroup:((I=s.bloodGroup)==null?void 0:I.value)||"",maritalStatus:((d=s.maritalStatus)==null?void 0:d.value)||"",nationality:s.nationality,motherTongue:s.motherTongue,religion:((y=s.religion)==null?void 0:y.uuid)||"",category:(($=s.category)==null?void 0:$.uuid)||"",caste:((P=s.caste)==null?void 0:P.uuid)||"",presentAddress:{addressLine1:(k=s.presentAddress)==null?void 0:k.addressLine1,addressLine2:(C=s.presentAddress)==null?void 0:C.addressLine2,city:(B=s.presentAddress)==null?void 0:B.city,state:(t=s.presentAddress)==null?void 0:t.state,zipcode:(F=s.presentAddress)==null?void 0:F.zipcode,country:(G=s.presentAddress)==null?void 0:G.country},permanentAddress:{sameAsPresentAddress:s.sameAsPresentAddress,addressLine1:(j=s.permanentAddress)==null?void 0:j.addressLine1,addressLine2:(H=s.permanentAddress)==null?void 0:H.addressLine2,city:(O=s.permanentAddress)==null?void 0:O.city,state:(w=s.permanentAddress)==null?void 0:w.state,zipcode:(M=s.permanentAddress)==null?void 0:M.zipcode,country:(D=s.permanentAddress)==null?void 0:D.country}}),Object.assign(r,ne(v)),V.isLoaded=!0}),(s,e)=>{const S=i("BaseAlert"),U=i("BaseLabel"),I=i("TextContent"),d=i("BaseInput"),y=i("BaseSelect"),$=i("AddressInput"),P=i("BaseFieldset"),k=i("BaseSwitch"),C=i("MediaUpload"),B=i("FormAction");return p(),q(B,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:ee,"pre-requisite-url":R,"init-url":E,uuid:o(L).params.uuid,"init-form":v,form:r,"keep-adding":!1,redirect:{name:"EmployeeProfileEditRequest",params:{uuid:m.employee.uuid}},onResetMediaFiles:te},{default:u(()=>[n(S,{design:"info",size:"xs"},{default:u(()=>[b(c(s.$trans("employee.edit_request.upload_document_info")),1)]),_:1}),l("div",de,[l("div",ue,[n(U,null,{default:u(()=>[b(c(s.$trans("contact.props.name")),1)]),_:1}),n(I,{class:"mt-1"},{default:u(()=>[b(c(m.employee.name),1)]),_:1})]),l("div",ie,[n(U,null,{default:u(()=>[b(c(s.$trans("contact.props.gender")),1)]),_:1}),n(I,{class:"mt-1"},{default:u(()=>[b(c(m.employee.contact.gender.label),1)]),_:1})]),l("div",me,[n(U,null,{default:u(()=>[b(c(s.$trans("contact.props.birth_date")),1)]),_:1}),n(I,{class:"mt-1"},{default:u(()=>[b(c(m.employee.contact.birthDate.formatted),1)]),_:1})]),l("div",pe,[n(d,{type:"text",modelValue:r.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=t=>r.contactNumber=t),name:"contactNumber",label:s.$trans("contact.props.primary_contact_number"),error:o(a).contactNumber,"onUpdate:error":e[1]||(e[1]=t=>o(a).contactNumber=t)},null,8,["modelValue","label","error"])]),l("div",be,[n(d,{type:"text",modelValue:r.alternateContactNumber,"onUpdate:modelValue":e[2]||(e[2]=t=>r.alternateContactNumber=t),name:"alternateContactNumber",label:s.$trans("contact.props.alternate_contact_number"),error:o(a).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=t=>o(a).alternateContactNumber=t)},null,8,["modelValue","label","error"])]),l("div",ce,[n(d,{type:"text",modelValue:r.email,"onUpdate:modelValue":e[4]||(e[4]=t=>r.email=t),name:"email",label:s.$trans("contact.props.email"),error:o(a).email,"onUpdate:error":e[5]||(e[5]=t=>o(a).email=t)},null,8,["modelValue","label","error"])]),l("div",ye,[n(d,{type:"text",modelValue:r.fatherName,"onUpdate:modelValue":e[6]||(e[6]=t=>r.fatherName=t),name:"fatherName",label:s.$trans("contact.props.father_name"),error:o(a).fatherName,"onUpdate:error":e[7]||(e[7]=t=>o(a).fatherName=t)},null,8,["modelValue","label","error"])]),l("div",Ne,[n(d,{type:"text",modelValue:r.motherName,"onUpdate:modelValue":e[8]||(e[8]=t=>r.motherName=t),name:"motherName",label:s.$trans("contact.props.mother_name"),error:o(a).motherName,"onUpdate:error":e[9]||(e[9]=t=>o(a).motherName=t)},null,8,["modelValue","label","error"])])]),l("div",ge,[l("div",Ae,[n(d,{type:"text",modelValue:r.uniqueIdNumber1,"onUpdate:modelValue":e[10]||(e[10]=t=>r.uniqueIdNumber1=t),name:"uniqueIdNumber1",label:o(W),error:o(a).uniqueIdNumber1,"onUpdate:error":e[11]||(e[11]=t=>o(a).uniqueIdNumber1=t)},null,8,["modelValue","label","error"])]),l("div",qe,[n(d,{type:"text",modelValue:r.uniqueIdNumber2,"onUpdate:modelValue":e[12]||(e[12]=t=>r.uniqueIdNumber2=t),name:"uniqueIdNumber2",label:o(X),error:o(a).uniqueIdNumber2,"onUpdate:error":e[13]||(e[13]=t=>o(a).uniqueIdNumber2=t)},null,8,["modelValue","label","error"])]),l("div",fe,[n(d,{type:"text",modelValue:r.uniqueIdNumber3,"onUpdate:modelValue":e[14]||(e[14]=t=>r.uniqueIdNumber3=t),name:"uniqueIdNumber3",label:o(Y),error:o(a).uniqueIdNumber3,"onUpdate:error":e[15]||(e[15]=t=>o(a).uniqueIdNumber3=t)},null,8,["modelValue","label","error"])]),l("div",Ve,[n(d,{type:"text",modelValue:r.uniqueIdNumber4,"onUpdate:modelValue":e[16]||(e[16]=t=>r.uniqueIdNumber4=t),name:"uniqueIdNumber4",label:o(Z),error:o(a).uniqueIdNumber4,"onUpdate:error":e[17]||(e[17]=t=>o(a).uniqueIdNumber4=t)},null,8,["modelValue","label","error"])]),l("div",Ue,[n(d,{type:"text",modelValue:r.uniqueIdNumber5,"onUpdate:modelValue":e[18]||(e[18]=t=>r.uniqueIdNumber5=t),name:"uniqueIdNumber5",label:o(x),error:o(a).uniqueIdNumber5,"onUpdate:error":e[19]||(e[19]=t=>o(a).uniqueIdNumber5=t)},null,8,["modelValue","label","error"])]),l("div",Ie,[n(d,{type:"text",modelValue:r.birthPlace,"onUpdate:modelValue":e[20]||(e[20]=t=>r.birthPlace=t),name:"birthPlace",label:s.$trans("contact.props.birth_place"),error:o(a).birthPlace,"onUpdate:error":e[21]||(e[21]=t=>o(a).birthPlace=t)},null,8,["modelValue","label","error"])]),l("div",Le,[n(d,{type:"text",modelValue:r.nationality,"onUpdate:modelValue":e[22]||(e[22]=t=>r.nationality=t),name:"nationality",label:s.$trans("contact.props.nationality"),error:o(a).nationality,"onUpdate:error":e[23]||(e[23]=t=>o(a).nationality=t)},null,8,["modelValue","label","error"])]),l("div",ve,[n(d,{type:"text",modelValue:r.motherTongue,"onUpdate:modelValue":e[24]||(e[24]=t=>r.motherTongue=t),name:"motherTongue",label:s.$trans("contact.props.mother_tongue"),error:o(a).motherTongue,"onUpdate:error":e[25]||(e[25]=t=>o(a).motherTongue=t)},null,8,["modelValue","label","error"])]),l("div",$e,[V.isLoaded?(p(),q(y,{key:0,modelValue:r.bloodGroup,"onUpdate:modelValue":e[26]||(e[26]=t=>r.bloodGroup=t),name:"bloodGroup",label:s.$trans("contact.props.blood_group"),options:f.bloodGroups,error:o(a).bloodGroup,"onUpdate:error":e[27]||(e[27]=t=>o(a).bloodGroup=t)},null,8,["modelValue","label","options","error"])):N("",!0)]),l("div",Pe,[V.isLoaded?(p(),q(y,{key:0,modelValue:r.maritalStatus,"onUpdate:modelValue":e[28]||(e[28]=t=>r.maritalStatus=t),name:"maritalStatus",label:s.$trans("contact.props.marital_status"),options:f.maritalStatuses,error:o(a).maritalStatus,"onUpdate:error":e[29]||(e[29]=t=>o(a).maritalStatus=t)},null,8,["modelValue","label","options","error"])):N("",!0)]),l("div",Ee,[V.isLoaded?(p(),q(y,{key:0,name:"religion",label:s.$trans("global.select",{attribute:s.$trans("contact.religion.religion")}),modelValue:r.religion,"onUpdate:modelValue":e[30]||(e[30]=t=>r.religion=t),error:o(a).religion,"onUpdate:error":e[31]||(e[31]=t=>o(a).religion=t),options:f.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):N("",!0)]),o(_)?(p(),z("div",Se,[V.isLoaded?(p(),q(y,{key:0,name:"category",label:s.$trans("global.select",{attribute:s.$trans("contact.category.category")}),modelValue:r.category,"onUpdate:modelValue":e[32]||(e[32]=t=>r.category=t),error:o(a).category,"onUpdate:error":e[33]||(e[33]=t=>o(a).category=t),options:f.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):N("",!0)])):N("",!0),o(h)?(p(),z("div",ke,[V.isLoaded?(p(),q(y,{key:0,name:"caste",label:s.$trans("global.select",{attribute:s.$trans("contact.caste.caste")}),modelValue:r.caste,"onUpdate:modelValue":e[34]||(e[34]=t=>r.caste=t),error:o(a).caste,"onUpdate:error":e[35]||(e[35]=t=>o(a).caste=t),options:f.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):N("",!0)])):N("",!0)]),n(P,{class:"mt-4"},{legend:u(()=>[b(c(s.$trans("contact.props.present_address")),1)]),default:u(()=>[l("div",Ce,[n($,{prefix:"presentAddress",addressLine1:r.presentAddress.addressLine1,"onUpdate:addressLine1":e[36]||(e[36]=t=>r.presentAddress.addressLine1=t),addressLine2:r.presentAddress.addressLine2,"onUpdate:addressLine2":e[37]||(e[37]=t=>r.presentAddress.addressLine2=t),city:r.presentAddress.city,"onUpdate:city":e[38]||(e[38]=t=>r.presentAddress.city=t),state:r.presentAddress.state,"onUpdate:state":e[39]||(e[39]=t=>r.presentAddress.state=t),zipcode:r.presentAddress.zipcode,"onUpdate:zipcode":e[40]||(e[40]=t=>r.presentAddress.zipcode=t),country:r.presentAddress.country,"onUpdate:country":e[41]||(e[41]=t=>r.presentAddress.country=t),formErrors:o(a),"onUpdate:formErrors":e[42]||(e[42]=t=>K(a)?a.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1}),n(P,{class:"mt-4"},{legend:u(()=>[b(c(s.$trans("contact.props.permanent_address")),1)]),default:u(()=>[l("div",Be,[l("div",Re,[n(k,{modelValue:r.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[43]||(e[43]=t=>r.permanentAddress.sameAsPresentAddress=t),name:"sameAsPresentAddress",label:s.$trans("contact.props.same_as_present_address"),error:o(a).sameAsPresentAddress,"onUpdate:error":e[44]||(e[44]=t=>o(a).sameAsPresentAddress=t)},null,8,["modelValue","label","error"])])]),l("div",Te,[r.permanentAddress.sameAsPresentAddress?N("",!0):(p(),q($,{key:0,prefix:"permanentAddress",addressLine1:r.permanentAddress.addressLine1,"onUpdate:addressLine1":e[45]||(e[45]=t=>r.permanentAddress.addressLine1=t),addressLine2:r.permanentAddress.addressLine2,"onUpdate:addressLine2":e[46]||(e[46]=t=>r.permanentAddress.addressLine2=t),city:r.permanentAddress.city,"onUpdate:city":e[47]||(e[47]=t=>r.permanentAddress.city=t),state:r.permanentAddress.state,"onUpdate:state":e[48]||(e[48]=t=>r.permanentAddress.state=t),zipcode:r.permanentAddress.zipcode,"onUpdate:zipcode":e[49]||(e[49]=t=>r.permanentAddress.zipcode=t),country:r.permanentAddress.country,"onUpdate:country":e[50]||(e[50]=t=>r.permanentAddress.country=t),formErrors:o(a),"onUpdate:formErrors":e[51]||(e[51]=t=>K(a)?a.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1}),l("div",ze,[l("div",Fe,[n(C,{multiple:"",label:s.$trans("general.file"),module:"contact_edit_request",media:r.media,"media-token":r.mediaToken,onIsUpdated:e[52]||(e[52]=t=>r.mediaUpdated=!0),onSetHash:e[53]||(e[53]=t=>r.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","form","redirect"])}}}),He={name:"EmployeeProfileEditRequestAction"},we=Object.assign(He,{props:{employee:{type:Object,default(){return{}}}},setup(m){const L=Q();return(g,v)=>{const E=i("PageHeaderAction"),R=i("PageHeader"),a=i("ParentTransition");return p(),z(le,null,[n(R,{title:g.$trans(o(L).meta.trans,{attribute:g.$trans(o(L).meta.label)}),navs:[{label:g.$trans("employee.employee"),path:"EmployeeList"},{label:m.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:m.employee.uuid}}},{label:g.$trans("employee.edit_request.edit_request"),path:{name:"EmployeeProfileEditRequest",params:{uuid:m.employee.uuid}}}]},{default:u(()=>[n(E,{name:"EmployeeProfileEditRequest",title:g.$trans("employee.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(a,{appear:"",visibility:!0},{default:u(()=>[n(je,{employee:m.employee},null,8,["employee"])]),_:1})],64)}}});export{we as default};
