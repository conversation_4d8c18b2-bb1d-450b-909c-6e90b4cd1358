import{i as w,u as E,h as S,l as v,r as n,a as C,o as m,e as t,w as a,f as p,q as V,b as k,d as A,s as o,t as s,F as I}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"EmployeePayrollPayHeadShow"},j=Object.assign(T,{setup(D){w();const i=E(),y=S(),u={},c="employee/payroll/payHead/",l=v({...u}),_=e=>{Object.assign(l,e)};return(e,d)=>{const b=n("PageHeaderAction"),f=n("PageHeader"),r=n("BaseDataView"),g=n("BaseButton"),h=n("ShowButton"),P=n("BaseCard"),$=n("ShowItem"),B=n("ParentTransition");return m(),C(I,null,[t(f,{title:e.$trans(p(i).meta.trans,{attribute:e.$trans(p(i).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.payroll.payroll"),path:"EmployeePayroll"},{label:e.$trans("employee.payroll.pay_head.pay_head"),path:"EmployeePayrollPayHeadList"}]},{default:a(()=>[t(b,{name:"EmployeePayrollPayHead",title:e.$trans("employee.payroll.pay_head.pay_head"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(B,{appear:"",visibility:!0},{default:a(()=>[t($,{"init-url":c,uuid:p(i).params.uuid,onSetItem:_,onRedirectTo:d[1]||(d[1]=H=>p(y).push({name:"EmployeePayrollPayHead"}))},{default:a(()=>[l.uuid?(m(),V(P,{key:0},{title:a(()=>[o(s(l.name),1)]),footer:a(()=>[t(h,null,{default:a(()=>[t(g,{design:"primary",onClick:d[0]||(d[0]=H=>p(y).push({name:"EmployeePayrollPayHeadEdit",params:{uuid:l.uuid}}))},{default:a(()=>[o(s(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[A("dl",N,[t(r,{label:e.$trans("employee.payroll.pay_head.props.name")},{default:a(()=>[o(s(l.name),1)]),_:1},8,["label"]),t(r,{label:e.$trans("employee.payroll.pay_head.props.code")},{default:a(()=>[o(s(l.code),1)]),_:1},8,["label"]),t(r,{label:e.$trans("employee.payroll.pay_head.props.alias")},{default:a(()=>[o(s(l.alias),1)]),_:1},8,["label"]),t(r,{label:e.$trans("employee.payroll.pay_head.props.category")},{default:a(()=>[o(s(l.category.label),1)]),_:1},8,["label"]),t(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.payroll.pay_head.props.description")},{default:a(()=>[o(s(l.description),1)]),_:1},8,["label"]),t(r,{label:e.$trans("general.created_at")},{default:a(()=>[o(s(l.createdAt.formatted),1)]),_:1},8,["label"]),t(r,{label:e.$trans("general.updated_at")},{default:a(()=>[o(s(l.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):k("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
