import{i as E,u as U,h as W,j as G,l as J,g as K,m as V,r as c,a as m,o as r,e as s,w as e,f as t,q as _,b as p,d as b,F as g,v as C,s as i,t as n,y as Q}from"./app-BAwPsakn.js";const X={class:"space-y-2"},Y={key:0},Z={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},ee={class:"flex flex-wrap gap-2"},te={class:"mt-4"},ae={name:"AcademicCourseShow"},ce=Object.assign(ae,{setup(oe){const A=E(),h=U(),S=W(),a=G("$trans"),D={},k="academic/course/",L=[{key:"batch",label:a("academic.batch.batch"),visibility:!0},{key:"action",label:"",visibility:!0}],o=J({...D}),P=f=>{Object.assign(o,f)},T=K("periods"),w=V(!1),v=V(!1),I=f=>{w.value=!0,A.dispatch(k+"updateCurrentPeriod",{uuid:o.uuid,form:{period_id:f.id}}).then(()=>{v.value=!0}).catch(()=>{}).finally(()=>{w.value=!1})};return(f,d)=>{const j=c("PageHeaderAction"),R=c("PageHeader"),x=c("TextMuted"),u=c("ListItemView"),H=c("ListContainerVertical"),y=c("BaseCard"),B=c("DataCell"),$=c("DataRow"),N=c("SimpleTable"),F=c("BaseDataView"),M=c("BaseBadge"),O=c("DetailLayoutVertical"),q=c("ShowItem"),z=c("ParentTransition");return r(),m(g,null,[s(R,{title:t(a)(t(h).meta.trans,{attribute:t(a)(t(h).meta.label)}),navs:[{label:t(a)("academic.academic"),path:"Academic"},{label:t(a)("academic.course.course"),path:"AcademicCourseList"}]},{default:e(()=>[s(j,{name:"AcademicCourse",title:t(a)("academic.course.course"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(z,{appear:"",visibility:!0},{default:e(()=>[s(q,{"init-url":k,uuid:t(h).params.uuid,onSetItem:P,onRedirectTo:d[0]||(d[0]=l=>t(S).push({name:"AcademicCourse"})),refresh:v.value,onRefreshed:d[1]||(d[1]=l=>v.value=!1)},{default:e(()=>[o.uuid?(r(),_(O,{key:0},{detail:e(()=>[b("div",X,[s(y,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(n(t(a)("academic.course.course")),1)]),action:e(()=>d[2]||(d[2]=[])),default:e(()=>[s(H,null,{default:e(()=>[s(u,{label:t(a)("academic.course.props.name")},{default:e(()=>[i(n(o.name)+" ",1),o.term?(r(),m("span",Y,"("+n(o.term)+")",1)):p("",!0),o.pgAccount?(r(),_(x,{key:1,block:""},{default:e(()=>[i(n(o.pgAccount),1)]),_:1})):p("",!0)]),_:1},8,["label"]),s(u,{label:t(a)("academic.division.division")},{default:e(()=>[i(n(o.division.name),1)]),_:1},8,["label"]),s(u,{label:t(a)("academic.course.props.code")},{default:e(()=>[i(n(o.code)+" ",1),s(x,{block:""},{default:e(()=>[i(n(o.shortcode),1)]),_:1})]),_:1},8,["label"]),s(u,{label:t(a)("academic.course.props.batch_with_same_subject")},{default:e(()=>[i(n(o.batchWithSameSubject?t(a)("general.yes"):t(a)("general.no")),1)]),_:1},8,["label"]),s(u,{label:t(a)("general.created_at")},{default:e(()=>[i(n(o.createdAt.formatted),1)]),_:1},8,["label"]),s(u,{label:t(a)("general.updated_at")},{default:e(()=>[i(n(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[s(y,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(n(t(a)("global.detail",{attribute:t(a)("academic.course.course")})),1)]),default:e(()=>[o.batches.length>0?(r(),_(N,{key:0,header:L},{default:e(()=>[(r(!0),m(g,null,C(o.batches,l=>(r(),_($,{key:l.uuid},{default:e(()=>[s(B,{name:"batch"},{default:e(()=>[i(n(l.name),1)]),_:2},1024),s(B,{name:"action"})]),_:2},1024))),128))]),_:1})):p("",!0),b("dl",Z,[s(F,{class:"col-span-1 sm:col-span-2",label:t(a)("academic.course.props.description")},{default:e(()=>[i(n(o.description),1)]),_:1},8,["label"])])]),_:1}),t(Q)("course:edit")?(r(),_(y,{key:0},{title:e(()=>[i(n(t(a)("global.update",{attribute:t(a)("academic.period.current_period")})),1)]),default:e(()=>[b("div",ee,[(r(!0),m(g,null,C(t(T),l=>(r(),_(M,{key:l.id,size:"md",design:"info",class:"cursor-pointer",onClick:se=>I(l)},{default:e(()=>[i(n(l.name),1)]),_:2},1032,["onClick"]))),128))]),b("div",te,[(r(!0),m(g,null,C(o.periodHistory,l=>(r(),m("div",{class:"dark:text-gray-400 text-sm",key:l.id},n(l.name)+" - "+n(l.datetime.formatted),1))),128))])]),_:1})):p("",!0)]),_:1})):p("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ce as default};
