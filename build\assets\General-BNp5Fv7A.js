import{u as y,j as _,H as j,c as S,l as p,r as a,a as q,o as D,e as n,f as t,w as m,d as u,s as U,t as A,F as R}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-4"},B={class:"col-span-3 sm:col-span-1"},F={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3"},L={name:"InventoryConfigGeneral"},W=Object.assign(L,{setup(M){const k=y(),i=_("$trans"),d="config/",s=j(d),N=S(()=>i("global.placeholder_info",{attribute:f.datePlaceholders})),f=p({datePlaceholders:""}),c={stockRequisitionNumberPrefix:"",stockRequisitionNumberSuffix:"",stockRequisitionNumberDigit:0,stockPurchaseNumberPrefix:"",stockPurchaseNumberSuffix:"",stockPurchaseNumberDigit:0,stockTransferNumberPrefix:"",stockTransferNumberSuffix:"",stockTransferNumberDigit:0,stockAdjustmentNumberPrefix:"",stockAdjustmentNumberSuffix:"",stockAdjustmentNumberDigit:0,type:"inventory"},o=p({...c}),x=b=>{Object.assign(f,{datePlaceholders:b.datePlaceholders.map(e=>e.value).join(", ")})};return(b,e)=>{const g=a("PageHeader"),l=a("BaseInput"),P=a("BaseAlert"),v=a("FormAction"),V=a("ParentTransition");return D(),q(R,null,[n(g,{title:t(i)(t(k).meta.label),navs:[{label:t(i)("inventory.inventory"),path:"Inventory"}]},null,8,["title","navs"]),n(V,{appear:"",visibility:!0},{default:m(()=>[n(v,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:x,"init-url":d,"data-fetch":"inventory","init-form":c,form:o,action:"store","stay-on":"",redirect:"Inventory"},{default:m(()=>[u("div",T,[u("div",B,[n(l,{type:"text",modelValue:o.stockRequisitionNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=r=>o.stockRequisitionNumberPrefix=r),name:"stockRequisitionNumberPrefix",label:t(i)("inventory.stock_requisition.config.props.number_prefix"),error:t(s).stockRequisitionNumberPrefix,"onUpdate:error":e[1]||(e[1]=r=>t(s).stockRequisitionNumberPrefix=r)},null,8,["modelValue","label","error"])]),u("div",F,[n(l,{type:"number",modelValue:o.stockRequisitionNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=r=>o.stockRequisitionNumberDigit=r),name:"stockRequisitionNumberDigit",label:t(i)("inventory.stock_requisition.config.props.number_digit"),error:t(s).stockRequisitionNumberDigit,"onUpdate:error":e[3]||(e[3]=r=>t(s).stockRequisitionNumberDigit=r)},null,8,["modelValue","label","error"])]),u("div",I,[n(l,{type:"text",modelValue:o.stockRequisitionNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=r=>o.stockRequisitionNumberSuffix=r),name:"stockRequisitionNumberSuffix",label:t(i)("inventory.stock_requisition.config.props.number_suffix"),error:t(s).stockRequisitionNumberSuffix,"onUpdate:error":e[5]||(e[5]=r=>t(s).stockRequisitionNumberSuffix=r)},null,8,["modelValue","label","error"])]),u("div",C,[n(l,{type:"text",modelValue:o.stockPurchaseNumberPrefix,"onUpdate:modelValue":e[6]||(e[6]=r=>o.stockPurchaseNumberPrefix=r),name:"stockPurchaseNumberPrefix",label:t(i)("inventory.stock_purchase.config.props.number_prefix"),error:t(s).stockPurchaseNumberPrefix,"onUpdate:error":e[7]||(e[7]=r=>t(s).stockPurchaseNumberPrefix=r)},null,8,["modelValue","label","error"])]),u("div",E,[n(l,{type:"number",modelValue:o.stockPurchaseNumberDigit,"onUpdate:modelValue":e[8]||(e[8]=r=>o.stockPurchaseNumberDigit=r),name:"stockPurchaseNumberDigit",label:t(i)("inventory.stock_purchase.config.props.number_digit"),error:t(s).stockPurchaseNumberDigit,"onUpdate:error":e[9]||(e[9]=r=>t(s).stockPurchaseNumberDigit=r)},null,8,["modelValue","label","error"])]),u("div",H,[n(l,{type:"text",modelValue:o.stockPurchaseNumberSuffix,"onUpdate:modelValue":e[10]||(e[10]=r=>o.stockPurchaseNumberSuffix=r),name:"stockPurchaseNumberSuffix",label:t(i)("inventory.stock_purchase.config.props.number_suffix"),error:t(s).stockPurchaseNumberSuffix,"onUpdate:error":e[11]||(e[11]=r=>t(s).stockPurchaseNumberSuffix=r)},null,8,["modelValue","label","error"])]),u("div",w,[n(l,{type:"text",modelValue:o.stockTransferNumberPrefix,"onUpdate:modelValue":e[12]||(e[12]=r=>o.stockTransferNumberPrefix=r),name:"stockTransferNumberPrefix",label:t(i)("inventory.stock_transfer.config.props.number_prefix"),error:t(s).stockTransferNumberPrefix,"onUpdate:error":e[13]||(e[13]=r=>t(s).stockTransferNumberPrefix=r)},null,8,["modelValue","label","error"])]),u("div",O,[n(l,{type:"number",modelValue:o.stockTransferNumberDigit,"onUpdate:modelValue":e[14]||(e[14]=r=>o.stockTransferNumberDigit=r),name:"stockTransferNumberDigit",label:t(i)("inventory.stock_transfer.config.props.number_digit"),error:t(s).stockTransferNumberDigit,"onUpdate:error":e[15]||(e[15]=r=>t(s).stockTransferNumberDigit=r)},null,8,["modelValue","label","error"])]),u("div",$,[n(l,{type:"text",modelValue:o.stockTransferNumberSuffix,"onUpdate:modelValue":e[16]||(e[16]=r=>o.stockTransferNumberSuffix=r),name:"stockTransferNumberSuffix",label:t(i)("inventory.stock_transfer.config.props.number_suffix"),error:t(s).stockTransferNumberSuffix,"onUpdate:error":e[17]||(e[17]=r=>t(s).stockTransferNumberSuffix=r)},null,8,["modelValue","label","error"])]),u("div",z,[n(l,{type:"text",modelValue:o.stockAdjustmentNumberPrefix,"onUpdate:modelValue":e[18]||(e[18]=r=>o.stockAdjustmentNumberPrefix=r),name:"stockAdjustmentNumberPrefix",label:t(i)("inventory.stock_adjustment.config.props.number_prefix"),error:t(s).stockAdjustmentNumberPrefix,"onUpdate:error":e[19]||(e[19]=r=>t(s).stockAdjustmentNumberPrefix=r)},null,8,["modelValue","label","error"])]),u("div",G,[n(l,{type:"number",modelValue:o.stockAdjustmentNumberDigit,"onUpdate:modelValue":e[20]||(e[20]=r=>o.stockAdjustmentNumberDigit=r),name:"stockAdjustmentNumberDigit",label:t(i)("inventory.stock_adjustment.config.props.number_digit"),error:t(s).stockAdjustmentNumberDigit,"onUpdate:error":e[21]||(e[21]=r=>t(s).stockAdjustmentNumberDigit=r)},null,8,["modelValue","label","error"])]),u("div",J,[n(l,{type:"text",modelValue:o.stockAdjustmentNumberSuffix,"onUpdate:modelValue":e[22]||(e[22]=r=>o.stockAdjustmentNumberSuffix=r),name:"stockAdjustmentNumberSuffix",label:t(i)("inventory.stock_adjustment.config.props.number_suffix"),error:t(s).stockAdjustmentNumberSuffix,"onUpdate:error":e[23]||(e[23]=r=>t(s).stockAdjustmentNumberSuffix=r)},null,8,["modelValue","label","error"])]),u("div",K,[n(P,{size:"xs",design:"info"},{default:m(()=>[U(A(N.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{W as default};
