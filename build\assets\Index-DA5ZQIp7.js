import{r as o,q as i,o as a,w as s,e as c}from"./app-BAwPsakn.js";const l={name:"UtilityConfig"},u=Object.assign(l,{setup(r){const t=[{name:"UtilityConfigGeneral",icon:"fas fa-cogs",label:"config.config"},{name:"UtilityConfigTodoList",icon:"fas fa-chevron-right",label:"utility.todo.list.list"}];return(f,_)=>{const e=o("router-view"),n=o("ModuleConfig");return a(),i(n,{navigations:t},{default:s(()=>[c(e)]),_:1})}}});export{u as default};
