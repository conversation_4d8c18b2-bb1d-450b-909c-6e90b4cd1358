import{u as U,l as C,n as O,r as u,q as y,o as f,w as e,d as _,e as s,b as g,h as E,j as z,y as v,m as D,f as i,a as G,F as J,v as K,s as c,t as b}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(R,{emit:h}){const n=U(),L=h,$={title:"",authors:[],publishers:[],topics:[],languages:[]},d=C({...$});C({});const r=C({isLoaded:!0,isLoaded:!(n.query.authors||n.query.publishers||n.query.topics)});return O(async()=>{r.authors=n.query.authors?n.query.authors.split(","):[],r.publishers=n.query.publishers?n.query.publishers.split(","):[],r.topics=n.query.topics?n.query.topics.split(","):[],r.languages=n.query.languages?n.query.languages.split(","):[],r.isLoaded=!0}),(p,l)=>{const q=u("BaseInput"),B=u("BaseSelectSearch"),I=u("FilterForm");return f(),y(I,{"init-form":$,form:d,multiple:["authors","publishers","topics","languages"],onHide:l[5]||(l[5]=t=>L("hide"))},{default:e(()=>[_("div",Q,[_("div",W,[s(q,{type:"text",modelValue:d.title,"onUpdate:modelValue":l[0]||(l[0]=t=>d.title=t),name:"title",label:p.$trans("library.book.props.title")},null,8,["modelValue","label"])]),_("div",X,[r.isLoaded?(f(),y(B,{key:0,multiple:"",name:"authors",label:p.$trans("global.select",{attribute:p.$trans("library.book.props.author")}),modelValue:d.authors,"onUpdate:modelValue":l[1]||(l[1]=t=>d.authors=t),"label-prop":"name","value-prop":"uuid","init-search":r.authors,"search-action":"option/list","additional-search-query":{type:"book_author"}},null,8,["label","modelValue","init-search"])):g("",!0)]),_("div",Y,[r.isLoaded?(f(),y(B,{key:0,multiple:"",name:"publishers",label:p.$trans("global.select",{attribute:p.$trans("library.book.props.publisher")}),modelValue:d.publishers,"onUpdate:modelValue":l[2]||(l[2]=t=>d.publishers=t),"label-prop":"name","value-prop":"uuid","init-search":r.publishers,"search-action":"option/list","additional-search-query":{type:"book_publisher"}},null,8,["label","modelValue","init-search"])):g("",!0)]),_("div",Z,[r.isLoaded?(f(),y(B,{key:0,multiple:"",name:"languages",label:p.$trans("global.select",{attribute:p.$trans("library.book.props.language")}),modelValue:d.languages,"onUpdate:modelValue":l[3]||(l[3]=t=>d.languages=t),"label-prop":"name","value-prop":"uuid","init-search":r.languages,"search-action":"option/list","additional-search-query":{type:"book_language"}},null,8,["label","modelValue","init-search"])):g("",!0)]),_("div",x,[r.isLoaded?(f(),y(B,{key:0,multiple:"",name:"topics",label:p.$trans("global.select",{attribute:p.$trans("library.book.props.topic")}),modelValue:d.topics,"onUpdate:modelValue":l[4]||(l[4]=t=>d.topics=t),"label-prop":"name","value-prop":"uuid","init-search":r.topics,"search-action":"option/list","additional-search-query":{type:"book_topic"}},null,8,["label","modelValue","init-search"])):g("",!0)])])]),_:1},8,["form"])}}},te={name:"LibraryBookList"},oe=Object.assign(te,{setup(R){const h=E(),n=z("emitter");let L=["filter"];v("book:create")&&L.unshift("create");let $=[];v("book:export")&&($=["print","pdf","excel"]),v("diocese:create")&&$.unshift("import");const d="library/book/",r=C({authors:[],publishers:[],topics:[],languages:[]}),p=D(!1),l=D(!1),q=C({}),B=t=>{Object.assign(r,t)},I=t=>{Object.assign(q,t)};return(t,o)=>{const F=u("BaseButton"),T=u("PageHeaderAction"),S=u("PageHeader"),P=u("BaseImport"),w=u("ParentTransition"),H=u("TextMuted"),k=u("DataCell"),V=u("FloatingMenuItem"),M=u("FloatingMenu"),j=u("DataRow"),A=u("DataTable"),N=u("ListItem");return f(),y(N,{"init-url":d,"pre-requisites":!0,onSetPreRequisites:B,onSetItems:I},{header:e(()=>[s(S,{title:t.$trans("library.book.book"),navs:[{label:t.$trans("library.library"),path:"Library"}]},{default:e(()=>[s(T,{url:"library/books/",name:"LibraryBook",title:t.$trans("library.book.book"),actions:i(L),"dropdown-actions":i($),onToggleFilter:o[1]||(o[1]=a=>p.value=!p.value),onToggleImport:o[2]||(o[2]=a=>l.value=!l.value)},{after:e(()=>[i(v)("library:config")?(f(),y(F,{key:0,design:"white",onClick:o[0]||(o[0]=a=>i(h).push({name:"LibraryConfig"}))},{default:e(()=>o[10]||(o[10]=[_("i",{class:"fas fa-cog"},null,-1)])),_:1})):g("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:e(()=>[s(w,{appear:"",visibility:l.value},{default:e(()=>[s(P,{path:"library/books/import",onCancelled:o[3]||(o[3]=a=>l.value=!1),onHide:o[4]||(o[4]=a=>l.value=!1),onCompleted:o[5]||(o[5]=a=>i(n).emit("listItems"))})]),_:1},8,["visibility"])]),filter:e(()=>[s(w,{appear:"",visibility:p.value},{default:e(()=>[s(ee,{onRefresh:o[6]||(o[6]=a=>i(n).emit("listItems")),"pre-requisites":r,onHide:o[7]||(o[7]=a=>p.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[s(w,{appear:"",visibility:!0},{default:e(()=>[s(A,{header:q.headers,meta:q.meta,module:"library.book",onRefresh:o[9]||(o[9]=a=>i(n).emit("listItems"))},{actionButton:e(()=>[i(v)("book:create")?(f(),y(F,{key:0,onClick:o[8]||(o[8]=a=>i(h).push({name:"LibraryBookCreate"}))},{default:e(()=>[c(b(t.$trans("global.add",{attribute:t.$trans("library.book.book")})),1)]),_:1})):g("",!0)]),default:e(()=>[(f(!0),G(J,null,K(q.data,a=>(f(),y(j,{key:a.uuid,onDoubleClick:m=>i(h).push({name:"LibraryBookShow",params:{uuid:a.uuid}})},{default:e(()=>[s(k,{name:"title"},{default:e(()=>[c(b(a.title)+" ",1),s(H,{block:""},{default:e(()=>[c(b(a.subTitle),1)]),_:2},1024)]),_:2},1024),s(k,{name:"copies"},{default:e(()=>[c(b(a.copiesCount),1)]),_:2},1024),s(k,{name:"author"},{default:e(()=>{var m;return[c(b(((m=a.author)==null?void 0:m.name)||"-"),1)]}),_:2},1024),s(k,{name:"publisher"},{default:e(()=>{var m;return[c(b(((m=a.publisher)==null?void 0:m.name)||"-"),1)]}),_:2},1024),s(k,{name:"topic"},{default:e(()=>{var m;return[c(b(((m=a.topic)==null?void 0:m.name)||"-"),1)]}),_:2},1024),s(k,{name:"isbnNumber"},{default:e(()=>[c(b(a.isbnNumber),1)]),_:2},1024),s(k,{name:"createdAt"},{default:e(()=>[c(b(a.createdAt.formatted),1)]),_:2},1024),s(k,{name:"action"},{default:e(()=>[s(M,null,{default:e(()=>[s(V,{icon:"fas fa-arrow-circle-right",onClick:m=>i(h).push({name:"LibraryBookShow",params:{uuid:a.uuid}})},{default:e(()=>[c(b(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(v)("book:edit")?(f(),y(V,{key:0,icon:"fas fa-edit",onClick:m=>i(h).push({name:"LibraryBookEdit",params:{uuid:a.uuid}})},{default:e(()=>[c(b(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])):g("",!0),i(v)("book:create")?(f(),y(V,{key:1,icon:"fas fa-copy",onClick:m=>i(h).push({name:"LibraryBookDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[c(b(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):g("",!0),i(v)("book:delete")?(f(),y(V,{key:2,icon:"fas fa-trash",onClick:m=>i(n).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[c(b(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{oe as default};
