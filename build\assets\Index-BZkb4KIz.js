import{H as _,k as g,l as v,r as t,q as V,o as b,w as a,e as p,d as m,f as c,s as B,t as U}from"./app-BAwPsakn.js";const y={class:"grid grid-cols-3 gap-6"},F={class:"col-span-3 sm:col-span-1"},P={name:"UserProfile"},N=Object.assign(P,{setup(k){const s="user/profile/",n=_(s),i={name:g("profile").value.name},o=v({...i});return(l,e)=>{const u=t("BaseInput"),d=t("FormAction"),f=t("ParentTransition");return b(),V(f,{appear:"",visibility:!0},{default:a(()=>[p(d,{"no-card":"","init-url":s,action:"updateProfile","init-form":i,form:o,"stay-on":"",redirect:"Dashboard"},{title:a(()=>[B(U(l.$trans("user.profile.profile")),1)]),default:a(()=>[m("div",y,[m("div",F,[p(u,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=r=>o.name=r),name:"name",label:l.$trans("user.profile.props.name"),error:c(n).name,"onUpdate:error":e[1]||(e[1]=r=>c(n).name=r),autofocus:""},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})}}});export{N as default};
