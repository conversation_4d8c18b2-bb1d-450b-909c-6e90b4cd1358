import{u as Y,i as Q,j as Z,D as ee,m as X,l as G,r as v,a as u,o as n,e as l,q as w,b as p,w as a,F as _,v as x,d as i,s as c,t as s,f as d,h as te,g as W,H as ae,n as se,J as ne}from"./app-BAwPsakn.js";import{_ as oe}from"./OnlinePaymentForm-hfFN_8uD.js";import"./Billdesk-CH1h7WlK.js";const le={class:"flex justify-start"},re={key:0},ie={class:"text-right sm:text-left"},ue={key:0},de={key:1,class:"text-xs"},ce={class:"text-right sm:text-left"},me={class:"ml-2"},pe={key:1},fe={key:2,class:"text-xs"},ge={key:0,class:"flex justify-between"},ye={key:0,class:"line-through mr-2"},be={key:0,class:"flex justify-between"},_e={key:0,class:"flex justify-between"},he={class:"truncate"},ve={key:0,class:"flex justify-between"},ke={class:"truncate"},$e={class:"flex justify-start"},Ce={name:"GuestPaymentFeeDetail"},De=Object.assign(Ce,{props:{state:{type:Object,default(){return{}}},paymentGateways:{type:Array,default(){return[]}}},emits:["setIsLoading","refresh"],setup(V,{emit:S}){Y(),Q();const F=S,N=V,f=Z("$trans"),{screenSize:U}=ee(),y=[{key:"title",label:f("finance.fee_structure.props.title"),visibility:!0},{key:"dueDate",label:f("finance.fee_structure.props.due_date"),visibility:!0},{key:"fee",label:f("student.fee.fee"),visibility:!0},{key:"total",label:f("finance.fee.total"),visibility:!0,align:"right"},{key:"paid",label:f("finance.fee.paid"),visibility:!0,align:"right"},{key:"balance",label:f("finance.fee.balance"),visibility:!0,align:"right"},{key:"action",label:"",visibility:!0}],O=[{key:"codeNumber",label:f("finance.transaction.props.code_number"),visibility:!0},{key:"date",label:f("finance.transaction.props.date"),visibility:!0},{key:"amount",label:f("finance.transaction.props.amount"),visibility:!0},{key:"action",label:"",visibility:!0}],h=C=>N.state.fees.filter(o=>o.installment.group.uuid==C.uuid),$=X(!1),b=G({feeGroup:{},fee:{},transaction:{}}),r=C=>{let o=N.state.transactions.filter(g=>g.records.filter(D=>D.additionalCharges.length>0).length>0),t=[];return o.filter(g=>!g.isCancelled&&!g.isRejected).forEach(g=>{g.records.forEach(D=>{D.studentFeeUuid==C.uuid&&t.push(...D.additionalCharges)})}),t},R=C=>{let o=N.state.transactions.filter(g=>g.records.filter(D=>D.additionalDiscounts.length>0).length>0),t=[];return o.filter(g=>!g.isCancelled&&!g.isRejected).forEach(g=>{g.records.forEach(D=>{D.studentFeeUuid==C.uuid&&t.push(...D.additionalDiscounts)})}),t},M=async(C={})=>{b.feeGroup={},b.fee=C,$.value=!0},E=async(C={})=>{b.fee={},b.feeGroup=C,$.value=!0},L=()=>{$.value=!1,b.fee={},b.feeGroup={}},I=()=>{b.fee={},b.feeGroup={},L(),F("refresh")},q=(C={})=>{window.open("/app/payment/export?code_number="+N.state.student.codeNumber+"&uuid="+N.state.student.uuid+"&output=pdf&reference_number="+C.referenceNumber,"_blank")};return(C,o)=>{const t=v("DataCell"),g=v("BaseButton"),D=v("FloatingMenuItem"),j=v("FloatingMenu"),B=v("DataRow"),A=v("BaseBadge"),P=v("SimpleTable"),T=v("BaseFieldset"),z=v("TextMuted");return n(),u(_,null,[l(T,{class:"mt-4","no-padding":""},{legend:a(()=>[c(s(d(f)("student.payment.online")),1)]),default:a(()=>[V.state.fees.length>0?(n(),w(P,{key:0,corner:"sharp",header:y},{default:a(()=>[(n(!0),u(_,null,x(V.state.feeGroups,e=>(n(),u(_,null,[l(B,{"is-heading":""},{default:a(()=>[l(t,{name:"title",colspan:3,"is-heading":""},{default:a(()=>[i("div",le,[i("span",null,[e.status.value=="paid"?(n(),u("span",re,o[0]||(o[0]=[i("i",{class:"fas fa-check-circle text-success mr-1"},null,-1)]))):p("",!0),c(" "+s(e.name),1)])])]),_:2},1024),l(t,{name:"total",align:"right","is-heading":""},{default:a(()=>[c(s(e.total.formatted),1)]),_:2},1024),l(t,{name:"paid",align:"right","is-heading":""},{default:a(()=>[c(s(e.paid.formatted),1)]),_:2},1024),l(t,{name:"balance",align:"right","is-heading":""},{default:a(()=>[c(s(e.balance.formatted),1)]),_:2},1024),l(t,{streched:""},{default:a(()=>[d(U).small&&e.balance.value>0?(n(),w(g,{key:0,block:"",onClick:m=>E(e)},{default:a(()=>[c(s(d(f)("global.pay",{attribute:d(f)("finance.fee_group.fee_group")})),1)]),_:2},1032,["onClick"])):p("",!0),!d(U).small&&e.balance.value>0?(n(),w(j,{key:1,slim:""},{default:a(()=>[l(D,{onClick:m=>E(e)},{default:a(()=>[c(s(d(f)("finance.payment.online")),1)]),_:2},1032,["onClick"])]),_:2},1024)):p("",!0)]),_:2},1024)]),_:2},1024),(n(!0),u(_,null,x(h(e),(m,H)=>(n(),w(B,{key:m.uuid},{default:a(()=>[l(t,{name:"title"},{default:a(()=>[i("div",ie,[m.status.value=="paid"?(n(),u("span",ue,o[1]||(o[1]=[i("i",{class:"fas fa-check-circle text-success mr-1"},null,-1)]))):p("",!0),c(" "+s(m.installment.title)+" ",1),m.concession?(n(),u("p",de,s(m.concession.name),1)):p("",!0)])]),_:2},1024),l(t,{name:"dueDate"},{default:a(()=>[i("div",ce,[m.overdue?(n(),w(A,{key:0,design:"error"},{default:a(()=>[c(s(m.dueDate.formatted)+" ",1),i("span",me,"("+s(m.overdue+" "+d(f)("list.durations.day"))+")",1)]),_:2},1024)):p("",!0),m.overdue===0?(n(),u("p",pe,s(m.dueDate.formatted),1)):p("",!0),m.lateFeeApplicable?(n(),u("p",fe,s(d(f)("finance.fee_structure.props.late_fee"))+" "+s(m.lateFee.formatted),1)):p("",!0)])]),_:2},1024),l(t,{name:"fee",streched:"",table:""},{default:a(()=>[(n(!0),u(_,null,x(m.records,k=>{var J,K;return n(),u(_,null,[k.amount.value>0?(n(),u("div",ge,[c(s(((J=k.head)==null?void 0:J.name)||((K=k.defaultFeeHead)==null?void 0:K.label)||"-")+" ",1),i("div",null,[k.concession.value>0?(n(),u("span",ye,s(k.amount.formatted),1)):p("",!0),i("span",null,s(k.amountWithConcession.formatted),1)])])):p("",!0)],64)}),256)),m.lateFee.amount.value>0?(n(),u("div",be,[c(s(d(f)("finance.fee_structure.late_fee"))+" ",1),i("div",null,[i("span",null,s(m.lateFee.amount.formatted),1)])])):p("",!0),(n(!0),u(_,null,x(r(m),k=>(n(),u(_,null,[k.amount.value>0?(n(),u("div",_e,[i("span",he,s(k.label),1),i("div",null,[i("span",null,s(k.amount.formatted),1)])])):p("",!0)],64))),256)),(n(!0),u(_,null,x(R(m),k=>(n(),u(_,null,[k.amount.value>0?(n(),u("div",ve,[i("span",ke,s(k.label),1),i("div",null,[i("span",null,"(-)"+s(k.amount.formatted),1)])])):p("",!0)],64))),256))]),_:2},1024),l(t,{align:"right",name:"total"},{default:a(()=>[c(s(m.total.formatted),1)]),_:2},1024),l(t,{align:"right",name:"paid"},{default:a(()=>[c(s(m.paid.formatted),1)]),_:2},1024),l(t,{align:"right",name:"balance"},{default:a(()=>[c(s(m.balance.formatted),1)]),_:2},1024),l(t,{name:"action",streched:""},{default:a(()=>[d(U).small&&m.balance.value>0?(n(),w(g,{key:0,block:"",onClick:k=>M(m)},{default:a(()=>[c(s(d(f)("finance.payment.online")),1)]),_:2},1032,["onClick"])):p("",!0),!d(U).small&&m.balance.value>0?(n(),w(j,{key:1},{default:a(()=>[l(D,{icon:"fas fa-credit-card",onClick:k=>M(m)},{default:a(()=>[c(s(d(f)("finance.payment.online")),1)]),_:2},1032,["onClick"])]),_:2},1024)):p("",!0)]),_:2},1024)]),_:2},1024))),128))],64))),256)),V.state.summary?(n(),w(B,{key:0,"is-heading":""},{default:a(()=>[l(t,{name:"title",colspan:3,"is-heading":""},{default:a(()=>[i("div",$e,[i("span",null,s(d(f)("global.grand",{attribute:d(f)("finance.fee.total")})),1)])]),_:1}),l(t,{name:"total",align:"right","is-heading":""},{default:a(()=>{var e;return[c(s((e=V.state.summary.grandTotal)==null?void 0:e.formatted),1)]}),_:1}),l(t,{name:"paid",align:"right","is-heading":""},{default:a(()=>{var e;return[c(s((e=V.state.summary.grandTotalPaid)==null?void 0:e.formatted),1)]}),_:1}),l(t,{name:"balance",align:"right","is-heading":""},{default:a(()=>{var e;return[c(s((e=V.state.summary.grandTotalBalance)==null?void 0:e.formatted),1)]}),_:1}),l(t)]),_:1})):p("",!0)]),_:1})):p("",!0)]),_:1}),V.state.transactions.length>0?(n(),w(T,{key:0,class:"mt-4","no-padding":""},{legend:a(()=>[c(s(d(f)("student.fee.receipt")),1)]),default:a(()=>[l(P,{corner:"sharp",header:O},{default:a(()=>[(n(!0),u(_,null,x(V.state.transactions,e=>(n(),w(B,null,{default:a(()=>[l(t,{name:"codeNumber"},{default:a(()=>[c(s(e.codeNumber)+" ",1),l(z,{block:""},{default:a(()=>[c(s(e.referenceNumber),1)]),_:2},1024),e.isCancelled.value||e.isRejected.value||e.isFailed?(n(),w(A,{key:0,design:"error"},{default:a(()=>[c(s(d(f)("general.failed")),1)]),_:1})):p("",!0)]),_:2},1024),l(t,{name:"date"},{default:a(()=>[c(s(e.date.formatted),1)]),_:2},1024),l(t,{name:"amount"},{default:a(()=>[c(s(e.amount.formatted),1)]),_:2},1024),l(t,{name:"action"},{default:a(()=>[l(g,{onClick:m=>q(e)},{default:a(()=>o[2]||(o[2]=[i("i",{class:"fas fa-print"},null,-1)])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024))),256))]),_:1})]),_:1})):p("",!0),l(oe,{"init-url":"guestPayment/",visibility:$.value,uuid:V.state.student.uuid,"pre-requisites":{paymentGateways:V.paymentGateways},"fee-group":b.feeGroup,fee:b.fee,date:V.state.date,onClose:L,onCompleted:I},null,8,["visibility","uuid","pre-requisites","fee-group","fee","date"])],64)}}}),Ve={class:"flex justify-end"},we={class:"flex justify-center"},Be={href:"/",class:"mb-6"},Ne=["src"],xe={key:0,class:"mb-6"},Fe=["innerHTML"],Ue={key:0,class:"grid grid-cols-2 gap-4"},je={class:"col-span-2 sm:col-span-1"},Pe={class:"mt-4 grid grid-cols-3 gap-4"},Te={key:0,class:"col-span-3 sm:col-span-1"},Oe={key:1,class:"col-span-3 sm:col-span-1"},Me={key:2,class:"col-span-3 sm:col-span-1"},Ge={class:"mt-4 grid grid-cols-3 gap-4"},Se={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},Le={key:0,class:"mt-4 grid grid-cols-3 gap-4"},Ie={class:"col-span-3 sm:col-span-1"},qe={class:"mt-4 grid grid-cols-3 gap-4"},Ae={class:"col-span-3 sm:col-span-1"},He={class:"col-span-3 sm:col-span-1"},Re={class:"mt-4 flex justify-end space-x-4"},ze={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-4"},We=["is-loading"],Je={class:"mt-4 flex justify-end space-x-4"},Ke={name:"GuestPayment"},Ze=Object.assign(Ke,{setup(V){const S=te(),F=Q(),N={type:"existing",team:"",period:"",course:"",batch:"",codeNumber:"",birthDate:"",name:"",contactNumber:"",heads:[],amount:"",gateway:""},f="guestPayment/",U=W("layout.display").value=="dark"?W("assets.iconLight"):W("assets.icon"),y=ae(f),O=X(!1),h=G({instruction:"",types:[],teams:[],paymentGateways:[],periods:[],courses:[],feeHeads:[]}),$=G({hasOneTeam:!1,hasProceeded:!1,previousDues:[]}),b=G({student:{},date:{},feeGroups:[],fees:[],transactions:[],summary:{}}),r=G({...N}),R=async()=>{await F.dispatch(f+"preRequisite").then(o=>{h.instruction=o.instruction,h.types=o.types,h.teams=o.teams,o.teams.length==1&&(r.team=o.teams[0].uuid,$.hasOneTeam=!0,M(r.team))}).catch(o=>{S.push({name:"Dashboard"})})},M=async o=>{if(o==null){r.team="";return}r.team=o,await F.dispatch(f+"getPeriods",{team:r.team}).then(t=>{h.periods=t.periods,h.paymentGateways=t.paymentGateways}).catch(t=>{})},E=async o=>{if(o==null){r.period="";return}r.period=o,await F.dispatch(f+"getCourses",{team:r.team,period:r.period}).then(t=>{h.courses=t.courses,h.feeHeads=t.feeHeads,t.feeHeads.forEach(g=>{r.heads.push({uuid:g.uuid,name:g.name,amount:0})})}).catch(t=>{})},L=()=>{},I=()=>{$.hasProceeded=!1,Object.assign(r,ne(N)),F.dispatch(f+"resetFormErrors")},q=async()=>{await F.dispatch(f+"getPaymentDetail",{form:{type:r.type,team:r.team,period:r.period,course:r.course,codeNumber:r.codeNumber,birthDate:r.birthDate}}).then(o=>{$.hasProceeded=!0,$.previousDues=o.previousDues,Object.assign(b,o.feeDetails)}).catch(o=>{})},C=async()=>{await q()};return se(()=>{R()}),(o,t)=>{const g=v("BaseAlert"),D=v("CustomCheckbox"),j=v("BaseSelect"),B=v("BaseInput"),A=v("DatePicker"),P=v("BaseButton"),T=v("BaseDataView"),z=v("BaseLoader");return n(),u(_,null,[i("div",Ve,[i("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:t[0]||(t[0]=e=>d(S).push({name:"Dashboard"}))},[t[18]||(t[18]=i("i",{class:"fas fa-home mr-1"},null,-1)),c(" "+s(o.$trans("global.go_to",{attribute:o.$trans("dashboard.home")})),1)])]),i("div",we,[i("a",Be,[i("img",{class:"h-16 w-auto",src:d(U),alt:""},null,8,Ne)])]),l(z,{"is-loading":O.value},{default:a(()=>[h.instruction?(n(),u("div",xe,[l(g,{size:"xs",design:"info"},{default:a(()=>[c(s(o.$trans("student.payment.payment_instruction_alert")),1)]),_:1}),i("div",{class:"mt-4 text-sm",innerHTML:h.instruction},null,8,Fe)])):p("",!0),$.hasProceeded?p("",!0):(n(),u(_,{key:1},[h.types.length>1?(n(),u("div",Ue,[i("div",je,[l(D,{label:o.$trans("general.type"),options:h.types,modelValue:r.type,"onUpdate:modelValue":t[1]||(t[1]=e=>r.type=e),error:d(y).type,"onUpdate:error":t[2]||(t[2]=e=>d(y).type=e)},null,8,["label","options","modelValue","error"])])])):p("",!0),i("div",Pe,[!r.team||!$.hasOneTeam?(n(),u("div",Te,[l(j,{modelValue:r.team,"onUpdate:modelValue":t[3]||(t[3]=e=>r.team=e),name:"team",label:o.$trans("team.team"),"label-prop":"name","value-prop":"uuid",options:h.teams,error:d(y).team,"onUpdate:error":t[4]||(t[4]=e=>d(y).team=e),onChange:M},null,8,["modelValue","label","options","error"])])):p("",!0),r.team?(n(),u("div",Oe,[l(j,{modelValue:r.period,"onUpdate:modelValue":t[5]||(t[5]=e=>r.period=e),name:"period",label:o.$trans("academic.period.period"),"label-prop":"name","value-prop":"uuid",options:h.periods,error:d(y).period,"onUpdate:error":t[6]||(t[6]=e=>d(y).period=e),onChange:E},null,8,["modelValue","label","options","error"])])):p("",!0),r.period?(n(),u("div",Me,[l(j,{modelValue:r.course,"onUpdate:modelValue":t[7]||(t[7]=e=>r.course=e),name:"course",label:o.$trans("academic.course.course"),"value-prop":"uuid",options:h.courses,error:d(y).course,"onUpdate:error":t[8]||(t[8]=e=>d(y).course=e)},{selectedOption:a(e=>[c(s(e.value.nameWithTerm),1)]),listOption:a(e=>[c(s(e.option.nameWithTerm),1)]),_:1},8,["modelValue","label","options","error"])])):p("",!0)]),r.type=="new"?(n(),u(_,{key:1},[i("div",Ge,[i("div",Se,[l(B,{type:"text",modelValue:r.name,"onUpdate:modelValue":t[9]||(t[9]=e=>r.name=e),name:"name",label:o.$trans("contact.props.name"),error:d(y).name,"onUpdate:error":t[10]||(t[10]=e=>d(y).name=e)},null,8,["modelValue","label","error"])]),i("div",Ee,[l(B,{type:"text",modelValue:r.contactNumber,"onUpdate:modelValue":t[11]||(t[11]=e=>r.contactNumber=e),name:"contactNumber",label:o.$trans("contact.props.contact_number"),error:d(y).contactNumber,"onUpdate:error":t[12]||(t[12]=e=>d(y).contactNumber=e)},null,8,["modelValue","label","error"])])]),r.heads.length?(n(),u("div",Le,[(n(!0),u(_,null,x(r.heads,(e,m)=>(n(),u("div",Ie,[l(B,{modelValue:e.amount,"onUpdate:modelValue":H=>e.amount=H,name:`heads.${m}.amount`,currency:"",label:e.name,error:d(y)[`heads.${m}.amount`],"onUpdate:error":H=>d(y)[`heads.${m}.amount`]=H,onChange:L},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]))),256))])):p("",!0)],64)):p("",!0),r.type=="existing"&&r.course?(n(),u(_,{key:2},[i("div",qe,[i("div",Ae,[l(B,{type:"text",modelValue:r.codeNumber,"onUpdate:modelValue":t[13]||(t[13]=e=>r.codeNumber=e),name:"codeNumber",label:o.$trans("student.admission.props.code_number"),error:d(y).codeNumber,"onUpdate:error":t[14]||(t[14]=e=>d(y).codeNumber=e)},null,8,["modelValue","label","error"])]),i("div",He,[l(A,{modelValue:r.birthDate,"onUpdate:modelValue":t[15]||(t[15]=e=>r.birthDate=e),name:"birthDate",label:o.$trans("contact.props.birth_date"),"no-clear":"",error:d(y).birthDate,"onUpdate:error":t[16]||(t[16]=e=>d(y).birthDate=e)},null,8,["modelValue","label","error"])])]),i("div",Re,[l(P,{onClick:I,design:"danger"},{default:a(()=>[c(s(o.$trans("general.reset")),1)]),_:1}),l(P,{onClick:q},{default:a(()=>[c(s(o.$trans("general.proceed")),1)]),_:1})])],64)):p("",!0)],64)),$.hasProceeded?(n(),u(_,{key:2},[i("dl",ze,[l(T,{label:o.$trans("student.admission.props.code_number")},{default:a(()=>{var e;return[c(s((e=b.student)==null?void 0:e.codeNumber),1)]}),_:1},8,["label"]),l(T,{label:o.$trans("student.props.name")},{default:a(()=>{var e;return[c(s((e=b.student)==null?void 0:e.name),1)]}),_:1},8,["label"]),l(T,{label:o.$trans("academic.course.course")},{default:a(()=>{var e,m;return[c(s(((e=b.student)==null?void 0:e.courseName)+" "+((m=b.student)==null?void 0:m.batchName)),1)]}),_:1},8,["label"])]),$.previousDues.length>0?(n(),u("div",{key:0,class:"mt-4","is-loading":O.value},[(n(!0),u(_,null,x($.previousDues,e=>(n(),u("div",null,[l(g,{size:"xs",design:"error"},{default:a(()=>[c(s(o.$trans("student.fee.previous_due_info",{period:e.period,amount:e.balance.formatted})),1)]),_:2},1024)]))),256))],8,We)):p("",!0),b.feeGroups.length?(n(),w(De,{key:1,state:b,"payment-gateways":h.paymentGateways,onSetIsLoading:t[17]||(t[17]=e=>O.value=e),onRefresh:C},null,8,["state","payment-gateways"])):p("",!0),i("div",Je,[l(P,{onClick:I,design:"danger"},{default:a(()=>[c(s(o.$trans("general.cancel")),1)]),_:1})])],64)):p("",!0)]),_:1},8,["is-loading"])],64)}}});export{Ze as default};
