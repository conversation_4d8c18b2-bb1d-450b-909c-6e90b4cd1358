import{u as R,l as T,n as j,r as s,q as f,o as c,w as e,d as y,e as a,h as S,j as O,y as v,m as U,f as o,a as E,F as q,v as z,s as l,t as d,b}from"./app-BAwPsakn.js";import{_ as G}from"./ModuleDropdown-sYr-p8uC.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(V,{emit:m}){R();const h=m,g={startDate:"",endDate:""},u=T({...g}),D=T({isLoaded:!0});return j(async()=>{D.isLoaded=!0}),(_,i)=>{const C=s("DatePicker"),r=s("FilterForm");return c(),f(r,{"init-form":g,form:u,multiple:[],onHide:i[2]||(i[2]=n=>h("hide"))},{default:e(()=>[y("div",J,[y("div",K,[a(C,{start:u.startDate,"onUpdate:start":i[0]||(i[0]=n=>u.startDate=n),end:u.endDate,"onUpdate:end":i[1]||(i[1]=n=>u.endDate=n),name:"dateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={name:"TransportVehicleTravelRecordList"},Z=Object.assign(W,{setup(V){const m=S(),h=O("emitter");let g=["filter"];v("vehicle-travel-record:create")&&g.unshift("create");let u=[];v("vehicle-travel-record:export")&&(u=["print","pdf","excel"]);const D="transport/vehicle/travelRecord/",_=U(!1),i=T({}),C=r=>{Object.assign(i,r)};return(r,n)=>{const B=s("PageHeaderAction"),I=s("PageHeader"),w=s("ParentTransition"),P=s("TextMuted"),$=s("DataCell"),k=s("FloatingMenuItem"),M=s("FloatingMenu"),A=s("DataRow"),H=s("BaseButton"),L=s("DataTable"),N=s("ListItem");return c(),f(N,{"init-url":D,onSetItems:C},{header:e(()=>[a(I,{title:r.$trans("transport.vehicle.travel_record.travel_record"),navs:[{label:r.$trans("transport.transport"),path:"Transport"},{label:r.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"}]},{default:e(()=>[a(B,{url:"transport/vehicle/travel-records/",name:"TransportVehicleTravelRecord",title:r.$trans("transport.vehicle.travel_record.travel_record"),actions:o(g),"dropdown-actions":o(u),onToggleFilter:n[0]||(n[0]=t=>_.value=!_.value)},{moduleOption:e(()=>[a(G)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(w,{appear:"",visibility:_.value},{default:e(()=>[a(Q,{onRefresh:n[1]||(n[1]=t=>o(h).emit("listItems")),onHide:n[2]||(n[2]=t=>_.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(w,{appear:"",visibility:!0},{default:e(()=>[a(L,{header:i.headers,meta:i.meta,module:"transport.vehicle.travel_record",onRefresh:n[4]||(n[4]=t=>o(h).emit("listItems"))},{actionButton:e(()=>[o(v)("vehicle-travel-record:create")?(c(),f(H,{key:0,onClick:n[3]||(n[3]=t=>o(m).push({name:"TransportVehicleTravelRecordCreate"}))},{default:e(()=>[l(d(r.$trans("global.add",{attribute:r.$trans("transport.vehicle.travel_record.travel_record")})),1)]),_:1})):b("",!0)]),default:e(()=>[(c(!0),E(q,null,z(i.data,t=>(c(),f(A,{key:t.uuid,onDoubleClick:p=>o(m).push({name:"TransportVehicleTravelRecordShow",params:{uuid:t.uuid}})},{default:e(()=>[a($,{name:"vehicle"},{default:e(()=>{var p;return[l(d((p=t.vehicle)==null?void 0:p.name)+" ",1),a(P,{block:""},{default:e(()=>{var F;return[l(d((F=t.vehicle)==null?void 0:F.registrationNumber),1)]}),_:2},1024)]}),_:2},1024),a($,{name:"date"},{default:e(()=>[l(d(t.date.formatted),1)]),_:2},1024),a($,{name:"log"},{default:e(()=>[l(d(t.log),1)]),_:2},1024),a($,{name:"createdAt"},{default:e(()=>[l(d(t.createdAt.formatted),1)]),_:2},1024),a($,{name:"action"},{default:e(()=>[a(M,null,{default:e(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:p=>o(m).push({name:"TransportVehicleTravelRecordShow",params:{uuid:t.uuid}})},{default:e(()=>[l(d(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(v)("vehicle-travel-record:edit")?(c(),f(k,{key:0,icon:"fas fa-edit",onClick:p=>o(m).push({name:"TransportVehicleTravelRecordEdit",params:{uuid:t.uuid}})},{default:e(()=>[l(d(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),o(v)("vehicle-travel-record:create")?(c(),f(k,{key:1,icon:"fas fa-copy",onClick:p=>o(m).push({name:"TransportVehicleTravelRecordDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[l(d(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),o(v)("vehicle-travel-record:delete")?(c(),f(k,{key:2,icon:"fas fa-trash",onClick:p=>o(h).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[l(d(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Z as default};
