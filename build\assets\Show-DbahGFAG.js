import{i as w,u as S,h as k,j as v,l as C,r as s,a as V,o as u,e as t,w as e,f as i,q as j,b as H,d as I,s as n,t as r,F as N}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"AcademicProgramTypeShow"},M=Object.assign(R,{setup(E){w();const d=S(),m=k();v("emitter");const _={},g="academic/programType/",o=C({..._}),f=a=>{Object.assign(o,a)};return(a,c)=>{const b=s("PageHeaderAction"),y=s("PageHeader"),p=s("TextMuted"),l=s("BaseDataView"),B=s("BaseButton"),T=s("ShowButton"),$=s("BaseCard"),P=s("ShowItem"),h=s("ParentTransition");return u(),V(N,null,[t(y,{title:a.$trans(i(d).meta.trans,{attribute:a.$trans(i(d).meta.label)}),navs:[{label:a.$trans("academic.academic"),path:"Academic"},{label:a.$trans("academic.program_type.program_type"),path:"AcademicProgramTypeList"}]},{default:e(()=>[t(b,{name:"AcademicProgramType",title:a.$trans("academic.program_type.program_type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(h,{appear:"",visibility:!0},{default:e(()=>[t(P,{"init-url":g,uuid:i(d).params.uuid,onSetItem:f,onRedirectTo:c[1]||(c[1]=A=>i(m).push({name:"AcademicProgramType"}))},{default:e(()=>[o.uuid?(u(),j($,{key:0},{title:e(()=>[n(r(o.name),1)]),footer:e(()=>[t(T,null,{default:e(()=>[t(B,{design:"primary",onClick:c[0]||(c[0]=A=>i(m).push({name:"AcademicProgramTypeEdit",params:{uuid:o.uuid}}))},{default:e(()=>[n(r(a.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[I("dl",D,[t(l,{label:a.$trans("academic.session.props.name")},{default:e(()=>[n(r(o.name)+" ",1),t(p,{block:""},{default:e(()=>[n(r(o.alias),1)]),_:1})]),_:1},8,["label"]),t(l,{label:a.$trans("academic.session.props.code")},{default:e(()=>[n(r(o.code)+" ",1),t(p,{block:""},{default:e(()=>[n(r(o.shortcode),1)]),_:1})]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:a.$trans("academic.program_type.props.description")},{default:e(()=>[n(r(o.description),1)]),_:1},8,["label"]),t(l,{label:a.$trans("general.created_at")},{default:e(()=>[n(r(o.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:a.$trans("general.updated_at")},{default:e(()=>[n(r(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):H("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
