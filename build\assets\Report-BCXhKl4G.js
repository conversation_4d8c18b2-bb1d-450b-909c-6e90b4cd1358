import{u as N,h as S,i as H,m as R,l as F,n as T,r as s,a as u,o as a,e as r,q as _,b as k,w as t,f as i,y as W,s as l,t as d,d as y,F as g,v as $}from"./app-BAwPsakn.js";import{_ as L}from"./Filter--6VJ5JII.js";const j={class:"p-2"},z={class:"divide-y divide-gray-200 dark:divide-gray-700"},M={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-2"},U={name:"EmployeeAttendanceWorkShiftReport"},Q=Object.assign(U,{setup(G){const o=N(),v=S(),A=H(),b="employee/attendance/workShift/",m=R(!1),c=F({employees:[],meta:{}}),h=async()=>{m.value=!0,await <PERSON><PERSON>dispatch(b+"fetchEmployee",{params:o.query}).then(e=>{m.value=!1,c.employees=e.data,c.meta=e.meta}).catch(e=>{m.value=!1})};return T(async()=>{o.query.startDate&&o.query.endDate&&await h()}),(e,p)=>{const w=s("BaseButton"),D=s("PageHeaderAction"),E=s("PageHeader"),P=s("ParentTransition"),C=s("BaseAlert"),B=s("BaseDataView"),q=s("Pagination"),V=s("BaseCard");return a(),u(g,null,[r(E,{title:e.$trans(i(o).meta.trans,{attribute:e.$trans(i(o).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"}]},{default:t(()=>[r(D,null,{default:t(()=>[i(W)("work-shift:assign")?(a(),_(w,{key:0,design:"primary",onClick:p[0]||(p[0]=n=>i(v).push({name:"EmployeeAttendanceWorkShiftAssign"}))},{default:t(()=>[l(d(e.$trans("global.assign",{attribute:e.$trans("employee.attendance.work_shift.work_shift")})),1)]),_:1})):k("",!0),r(w,{design:"white",onClick:p[1]||(p[1]=n=>i(v).push({name:"EmployeeAttendanceWorkShift"}))},{default:t(()=>[l(d(e.$trans("global.list",{attribute:e.$trans("employee.attendance.work_shift.work_shift")})),1)]),_:1})]),_:1})]),_:1},8,["title","navs"]),r(P,{appear:"",visibility:!0},{default:t(()=>[r(L,{onAfterFilter:h,"init-url":b})]),_:1}),i(o).query.startDate&&i(o).query.endDate?(a(),_(V,{key:0,"no-padding":"","no-content-padding":"","is-loading":m.value},{title:t(()=>[l(d(e.$trans("employee.attendance.work_shift.work_shift")),1)]),default:t(()=>[y("div",j,[c.employees.length==0?(a(),_(C,{key:0,size:"xs",design:"error"},{default:t(()=>[l(d(e.$trans("general.errors.record_not_found")),1)]),_:1})):k("",!0)]),y("div",z,[(a(!0),u(g,null,$(c.employees,(n,I)=>(a(),u("div",{class:"grid grid-cols-3 gap-6 px-4 py-2",key:n.uuid},[y("div",M,[r(B,{label:n.name+" ("+n.codeNumber+")",revert:""},{default:t(()=>[l(d(n.designation),1)]),_:2},1032,["label"])]),y("div",O,[n.workShifts.length?(a(!0),u(g,{key:0},$(n.workShifts,f=>(a(),_(B,{label:f.name+"("+f.code+")",revert:""},{default:t(()=>[l(d(f.startDate.formatted)+" - "+d(f.endDate.formatted),1)]),_:2},1032,["label"]))),256)):(a(),u(g,{key:1},[l(" - ")],64))])]))),128))]),r(q,{meta:c.meta,onRefresh:h},null,8,["meta"])]),_:1},8,["is-loading"])):k("",!0)],64)}}});export{Q as default};
