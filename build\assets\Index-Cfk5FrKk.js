import{u as P,i as H,m as F,H as M,l as B,n as L,a9 as E,r as c,q as W,o as g,w as p,d as i,e as l,f as o,s as j,a as S,b as $,t as b,F as C}from"./app-BAwPsakn.js";import"./lodash-CyHJH6Xs.js";const I={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={key:0,class:"ml-1"},J={key:0,class:"ml-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"flex items-center gap-2"},Z={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"mt-4 grid grid-cols-4 gap-6"},se={class:"col-span-3 sm:col-span-1"},oe={class:"mt-4 grid grid-cols-4 gap-6"},re={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},le={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},me={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(k,{emit:U}){const n=P(),R=H(),T=U,v=k,y={exam:"",attempt:"first",batch:"",subjects:[],title:"",showPrintDateTime:!1,signatory1:"",signatory2:"",signatory3:"",signatory4:"",showWatermark:!1},d=F(!1),f=F(!1),r=M(v.initUrl),a=B({...y}),m=B({exams:v.preRequisites.exams,subjects:[]}),u=B({exam:"",batch:"",subjects:[],isLoaded:!(n.query.exam&&n.query.batch&&n.query.subjects)}),x=async s=>{if(!s){a.batch="",a.subjects=[],m.subjects=[];return}a.batch=s||"",m.subjects=[],a.subjects=[],d.value=!0,await R.dispatch("academic/batch/listSubjects",{uuid:s||""}).then(t=>{m.subjects=t,d.value=!1}).catch(t=>{d.value=!1})};return L(async()=>{if(_.isEmpty(n.query)){u.isLoaded=!0;return}u.exam=n.query.exam,a.exam=n.query.exam,a.attempt=n.query.attempt,u.batch=n.query.batch,a.batch=n.query.batch,a.showWatermark=E(n.query.showWatermark||""),a.showPrintDateTime=E(n.query.showPrintDateTime||""),n.query.batch&&(await x(n.query.batch),a.subjects=n.query.subjects?n.query.subjects.split(","):[],u.subjects=n.query.subjects?n.query.subjects.split(","):[]),u.isLoaded=!0}),(s,t)=>{const h=c("BaseSelect"),D=c("BaseSelectSearch"),O=c("BaseSwitch"),V=c("BaseInput"),A=c("BaseFieldset"),N=c("FilterForm");return g(),W(N,{"init-form":y,multiple:["subjects"],form:a,onHide:t[22]||(t[22]=e=>T("hide"))},{default:p(()=>[i("div",I,[i("div",z,[l(h,{modelValue:a.exam,"onUpdate:modelValue":t[0]||(t[0]=e=>a.exam=e),name:"exam",label:s.$trans("exam.exam"),"value-prop":"uuid",options:k.preRequisites.exams,error:o(r).exam,"onUpdate:error":t[1]||(t[1]=e=>o(r).exam=e)},{selectedOption:p(e=>{var w,q;return[j(b(e.value.name)+" ",1),e.value.term?(g(),S("span",G,"("+b(((q=(w=e.value.term)==null?void 0:w.division)==null?void 0:q.name)||s.$trans("general.all"))+")",1)):$("",!0)]}),listOption:p(e=>{var w,q;return[j(b(e.option.name)+" ",1),e.option.term?(g(),S("span",J,"("+b(((q=(w=e.option.term)==null?void 0:w.division)==null?void 0:q.name)||s.$trans("general.all"))+")",1)):$("",!0)]}),_:1},8,["modelValue","label","options","error"])]),i("div",K,[u.isLoaded?(g(),W(h,{key:0,modelValue:a.attempt,"onUpdate:modelValue":t[2]||(t[2]=e=>a.attempt=e),name:"attempt",label:s.$trans("exam.schedule.props.attempt"),options:k.preRequisites.attempts,error:o(r).attempt,"onUpdate:error":t[3]||(t[3]=e=>o(r).attempt=e)},null,8,["modelValue","label","options","error"])):$("",!0)]),i("div",Q,[u.isLoaded?(g(),W(D,{key:0,name:"batch",label:s.$trans("global.select",{attribute:s.$trans("academic.batch.batch")}),modelValue:a.batch,"onUpdate:modelValue":t[4]||(t[4]=e=>a.batch=e),error:o(r).batch,"onUpdate:error":t[5]||(t[5]=e=>o(r).batch=e),"value-prop":"uuid","init-search":u.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:x},{selectedOption:p(e=>[j(b(e.value.course.name)+" "+b(e.value.name),1)]),listOption:p(e=>[j(b(e.option.course.nameWithTerm)+" "+b(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)]),i("div",X,[l(h,{multiple:"",modelValue:a.subjects,"onUpdate:modelValue":t[6]||(t[6]=e=>a.subjects=e),name:"subjects",label:s.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:m.subjects},null,8,["modelValue","label","options"])])]),l(A,{class:"mt-4"},{legend:p(()=>[i("div",Y,[j(b(s.$trans("global.show",{attribute:s.$trans("general.options")}))+" ",1),l(O,{reverse:"",modelValue:f.value,"onUpdate:modelValue":t[7]||(t[7]=e=>f.value=e),name:"showOptions"},null,8,["modelValue"])])]),default:p(()=>[f.value?(g(),S(C,{key:0},[i("div",Z,[i("div",ee,[l(O,{vertical:"",modelValue:a.showWatermark,"onUpdate:modelValue":t[8]||(t[8]=e=>a.showWatermark=e),name:"showWatermark",label:s.$trans("global.show",{attribute:s.$trans("print.watermark")}),error:o(r).showWatermark,"onUpdate:error":t[9]||(t[9]=e=>o(r).showWatermark=e)},null,8,["modelValue","label","error"])]),i("div",te,[l(O,{vertical:"",modelValue:a.showPrintDateTime,"onUpdate:modelValue":t[10]||(t[10]=e=>a.showPrintDateTime=e),name:"showPrintDateTime",label:s.$trans("global.show",{attribute:s.$trans("general.print_date_time")}),error:o(r).showPrintDateTime,"onUpdate:error":t[11]||(t[11]=e=>o(r).showPrintDateTime=e)},null,8,["modelValue","label","error"])])]),i("div",ae,[i("div",se,[l(V,{type:"text",modelValue:a.title,"onUpdate:modelValue":t[12]||(t[12]=e=>a.title=e),name:"title",label:s.$trans("print.title"),error:o(r).title,"onUpdate:error":t[13]||(t[13]=e=>o(r).title=e)},null,8,["modelValue","label","error"])])]),i("div",oe,[i("div",re,[l(V,{type:"text",modelValue:a.signatory1,"onUpdate:modelValue":t[14]||(t[14]=e=>a.signatory1=e),name:"signatory1",label:s.$trans("print.signatory1"),error:o(r).signatory1,"onUpdate:error":t[15]||(t[15]=e=>o(r).signatory1=e)},null,8,["modelValue","label","error"])]),i("div",ne,[l(V,{type:"text",modelValue:a.signatory2,"onUpdate:modelValue":t[16]||(t[16]=e=>a.signatory2=e),name:"signatory2",label:s.$trans("print.signatory2"),error:o(r).signatory2,"onUpdate:error":t[17]||(t[17]=e=>o(r).signatory2=e)},null,8,["modelValue","label","error"])]),i("div",le,[l(V,{type:"text",modelValue:a.signatory3,"onUpdate:modelValue":t[18]||(t[18]=e=>a.signatory3=e),name:"signatory3",label:s.$trans("print.signatory3"),error:o(r).signatory3,"onUpdate:error":t[19]||(t[19]=e=>o(r).signatory3=e)},null,8,["modelValue","label","error"])]),i("div",ie,[l(V,{type:"text",modelValue:a.signatory4,"onUpdate:modelValue":t[20]||(t[20]=e=>a.signatory4=e),name:"signatory4",label:s.$trans("print.signatory4"),error:o(r).signatory4,"onUpdate:error":t[21]||(t[21]=e=>o(r).signatory4=e)},null,8,["modelValue","label","error"])])])],64)):$("",!0)]),_:1})]),_:1},8,["form"])}}},ue={name:"ExamReportMarkSummary"},ce=Object.assign(ue,{setup(k){const U=P(),n=H();let R=["filter"],T=[];const v="exam/report/",y=F(!0),d=F(!1),f=B({exams:[],attempts:[]}),r=async()=>{d.value=!0,await n.dispatch(v+"preRequisite",{name:"mark-summary"}).then(m=>{d.value=!1,Object.assign(f,m)}).catch(m=>{d.value=!1})},a=async()=>{d.value=!0,await n.dispatch(v+"fetchReport",{name:"mark-summary",params:U.query}).then(m=>{d.value=!1,window.open("/print").document.write(m)}).catch(m=>{d.value=!1})};return L(async()=>{await r()}),(m,u)=>{const x=c("PageHeaderAction"),s=c("PageHeader"),t=c("ParentTransition"),h=c("BaseCard");return g(),S(C,null,[l(s,{title:m.$trans(o(U).meta.label),navs:[{label:m.$trans("exam.exam"),path:"Exam"},{label:m.$trans("exam.report.report"),path:"ExamReport"}]},{default:p(()=>[l(x,{url:"exam/reports/mark-summary/",name:"ExamReportMarkSummary",title:m.$trans("exam.report.mark_summary.mark_summary"),actions:o(R),"dropdown-actions":o(T),onToggleFilter:u[0]||(u[0]=D=>y.value=!y.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),l(t,{appear:"",visibility:y.value},{default:p(()=>[l(me,{onAfterFilter:a,"init-url":v,"pre-requisites":f,onHide:u[1]||(u[1]=D=>y.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),l(t,{appear:"",visibility:!0},{default:p(()=>[l(h,{"no-padding":"","no-content-padding":"","is-loading":d.value},null,8,["is-loading"])]),_:1})],64)}}});export{ce as default};
