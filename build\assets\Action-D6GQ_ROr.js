import{u as M,G as U,H as j,l as b,n as q,r as c,q as C,o as y,w as u,d as m,e as l,f as r,s as v,t as B,M as H,J as O,a as T,F as L}from"./app-BAwPsakn.js";import{d as D}from"./vuedraggable.umd-BRYqknf6.js";const N={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-2"},G=["onClick"],J={class:"mt-4 grid grid-cols-4 gap-4"},z={class:"col-span-4 sm:col-span-1"},K={class:"col-span-4 sm:col-span-1"},Q={class:"col-span-4 sm:col-span-1"},W={class:"col-span-4 sm:col-span-1"},X={class:"mt-4"},Y={name:"ExamAssessmentForm"},Z=Object.assign(Y,{setup(A){const _=M(),i={name:"",description:"",records:[]},f={uuid:U(),name:"",code:"",maxMark:"",passingMark:"",description:""},g="exam/assessment/",o=j(g),$=b({}),d=b({...i}),V=b({isLoaded:!_.params.uuid}),F=a=>{Object.assign($,a)},k=()=>{d.records.push({...f,uuid:U()}),V.isLoaded=!0},x=async a=>{await H()&&(d.records.length==1?d.records=[f]:d.records.splice(a,1))},E=a=>{let t=a.records.map(p=>({...p}));Object.assign(i,{...a,records:t}),Object.assign(d,O(i)),V.isLoaded=!0};return q(async()=>{_.params.uuid||k()}),(a,t)=>{const p=c("BaseInput"),P=c("BaseTextarea"),w=c("BaseFieldset"),R=c("BaseBadge"),h=c("FormAction");return y(),C(h,{"pre-requisites":!1,onSetPreRequisites:F,"init-url":g,"init-form":i,form:d,"set-form":E,redirect:"ExamAssessment"},{default:u(()=>[m("div",N,[m("div",I,[l(p,{type:"text",modelValue:d.name,"onUpdate:modelValue":t[0]||(t[0]=e=>d.name=e),name:"name",label:a.$trans("exam.assessment.props.name"),error:r(o).name,"onUpdate:error":t[1]||(t[1]=e=>r(o).name=e)},null,8,["modelValue","label","error"])]),m("div",S,[l(P,{rows:1,modelValue:d.description,"onUpdate:modelValue":t[2]||(t[2]=e=>d.description=e),name:"description",label:a.$trans("exam.assessment.props.description"),error:r(o).description,"onUpdate:error":t[3]||(t[3]=e=>r(o).description=e)},null,8,["modelValue","label","error"])])]),l(r(D),{list:d.records,"item-key":"uuid"},{item:u(({element:e,index:n})=>[l(w,{class:"mt-4"},{legend:u(()=>[t[5]||(t[5]=m("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),v(" "+B(n+1)+". ",1),m("span",{class:"text-danger ml-2 cursor-pointer",onClick:s=>x(n)},t[4]||(t[4]=[m("i",{class:"fas fa-times-circle"},null,-1)]),8,G)]),default:u(()=>[m("div",J,[m("div",z,[l(p,{type:"text",modelValue:e.name,"onUpdate:modelValue":s=>e.name=s,name:`records.${n}.name`,label:a.$trans("exam.assessment.props.name"),error:r(o)[`records.${n}.name`],"onUpdate:error":s=>r(o)[`records.${n}.name`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",K,[l(p,{type:"text",modelValue:e.code,"onUpdate:modelValue":s=>e.code=s,name:`records.${n}.code`,label:a.$trans("exam.assessment.props.code"),error:r(o)[`records.${n}.code`],"onUpdate:error":s=>r(o)[`records.${n}.code`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",Q,[l(p,{type:"number",modelValue:e.maxMark,"onUpdate:modelValue":s=>e.maxMark=s,name:`records.${n}.maxMark`,label:a.$trans("exam.assessment.props.max_mark"),error:r(o)[`records.${n}.maxMark`],"onUpdate:error":s=>r(o)[`records.${n}.maxMark`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),m("div",W,[l(p,{type:"number",modelValue:e.passingMark,"onUpdate:modelValue":s=>e.passingMark=s,name:`records.${n}.passingMark`,label:a.$trans("exam.assessment.props.passing_mark"),error:r(o)[`records.${n}.passingMark`],"onUpdate:error":s=>r(o)[`records.${n}.passingMark`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024)]),_:1},8,["list"]),m("div",X,[l(R,{design:"primary",onClick:k,class:"cursor-pointer"},{default:u(()=>[v(B(a.$trans("global.add",{attribute:a.$trans("general.record")})),1)]),_:1})])]),_:1},8,["form"])}}}),ee={name:"ExamAssessmentAction"},re=Object.assign(ee,{setup(A){const _=M();return(i,f)=>{const g=c("PageHeaderAction"),o=c("PageHeader"),$=c("ParentTransition");return y(),T(L,null,[l(o,{title:i.$trans(r(_).meta.trans,{attribute:i.$trans(r(_).meta.label)}),navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.assessment.assessment"),path:"ExamAssessmentList"}]},{default:u(()=>[l(g,{name:"ExamAssessment",title:i.$trans("exam.assessment.assessment"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l($,{appear:"",visibility:!0},{default:u(()=>[l(Z)]),_:1})],64)}}});export{re as default};
