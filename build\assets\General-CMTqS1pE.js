import{u as M,j as S,H as v,l as d,r as t,a as D,o as F,e as a,f as e,w as m,d as u,F as I}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-4"},B={class:"col-span-3 sm:col-span-1"},V={name:"MessConfigGeneral"},x=Object.assign(V,{setup(j){const h=M(),s=S("$trans"),l="config/",i=v(l);d({});const c={showMessScheduleInDashboard:!1,type:"mess"},n=d({...c}),p=_=>{};return(_,o)=>{const f=t("PageHeader"),b=t("BaseSwitch"),g=t("FormAction"),w=t("ParentTransition");return F(),D(I,null,[a(f,{title:e(s)(e(h).meta.label),navs:[{label:e(s)("mess.mess"),path:"Mess"}]},null,8,["title","navs"]),a(w,{appear:"",visibility:!0},{default:m(()=>[a(g,{"pre-requisites":!1,onSetPreRequisites:p,"init-url":l,"data-fetch":"mess","init-form":c,form:n,action:"store","stay-on":"",redirect:"Mess"},{default:m(()=>[u("div",P,[u("div",B,[a(b,{vertical:"",modelValue:n.showMessScheduleInDashboard,"onUpdate:modelValue":o[0]||(o[0]=r=>n.showMessScheduleInDashboard=r),name:"showMessScheduleInDashboard",label:e(s)("global.show",{attribute:e(s)("mess.config.props.schedule_in_dashboard")}),error:e(i).showMessScheduleInDashboard,"onUpdate:error":o[1]||(o[1]=r=>e(i).showMessScheduleInDashboard=r)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{x as default};
