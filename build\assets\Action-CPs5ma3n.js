import{u as v,H as F,l as u,r as i,q as I,o as _,w as y,d as m,e as l,f as a,J as P,a as S,F as U}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-4 sm:col-span-1"},H={class:"col-span-3"},O={name:"InventoryStockCategoryForm"},R=Object.assign(O,{setup(g){const c=v(),o={name:"",inventory:"",description:""},d="inventory/stockCategory/",r=F(d),p=u({inventories:[]}),n=u({...o}),f=u({inventory:"",isLoaded:!c.params.uuid}),b=s=>{Object.assign(p,s)},k=s=>{var e;Object.assign(o,{...s,inventory:(e=s.inventory)==null?void 0:e.uuid}),Object.assign(n,P(o)),f.isLoaded=!0};return(s,e)=>{const V=i("BaseInput"),B=i("BaseSelect"),$=i("BaseTextarea"),C=i("FormAction");return _(),I(C,{"pre-requisites":!0,onSetPreRequisites:b,"init-url":d,"init-form":o,form:n,"set-form":k,redirect:"InventoryStockCategory"},{default:y(()=>[m("div",j,[m("div",q,[l(V,{type:"text",modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=t=>n.name=t),name:"name",label:s.$trans("inventory.stock_category.props.name"),error:a(r).name,"onUpdate:error":e[1]||(e[1]=t=>a(r).name=t)},null,8,["modelValue","label","error"])]),m("div",A,[l(B,{modelValue:n.inventory,"onUpdate:modelValue":e[2]||(e[2]=t=>n.inventory=t),name:"inventory",label:s.$trans("inventory.inventory"),options:p.inventories,"label-prop":"name","value-prop":"uuid",error:a(r).inventory,"onUpdate:error":e[3]||(e[3]=t=>a(r).inventory=t)},null,8,["modelValue","label","options","error"])]),m("div",H,[l($,{modelValue:n.description,"onUpdate:modelValue":e[4]||(e[4]=t=>n.description=t),name:"description",label:s.$trans("inventory.stock_category.props.description"),error:a(r).description,"onUpdate:error":e[5]||(e[5]=t=>a(r).description=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),T={name:"InventoryStockCategoryAction"},L=Object.assign(T,{setup(g){const c=v();return(o,d)=>{const r=i("PageHeaderAction"),p=i("PageHeader"),n=i("ParentTransition");return _(),S(U,null,[l(p,{title:o.$trans(a(c).meta.trans,{attribute:o.$trans(a(c).meta.label)}),navs:[{label:o.$trans("inventory.inventory"),path:"Inventory"},{label:o.$trans("inventory.stock_category.stock_category"),path:"InventoryStockCategoryList"}]},{default:y(()=>[l(r,{name:"InventoryStockCategory",title:o.$trans("inventory.stock_category.stock_category"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(n,{appear:"",visibility:!0},{default:y(()=>[l(R)]),_:1})],64)}}});export{L as default};
