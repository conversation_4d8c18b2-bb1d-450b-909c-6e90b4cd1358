import{u as g,h as T,j as $,H as h,l as U,r as a,q as j,o as b,w as m,d as p,e as r,f as o,J as E,a as P,F as x}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-1 gap-y-2"},H={class:"col-span-1"},k={class:"col-span-1"},y={class:"col-span-1"},O={name:"BlogForm"},w=Object.assign(O,{setup(f){g();const d=T(),s=$("$trans"),i={title:"",subTitle:"",content:""},u="blog/",l=h(u),n=U({...i}),v=c=>{Object.assign(i,{...c}),Object.assign(n,E(i))},B=c=>{d.push({name:"BlogEdit",params:{uuid:c.blog.uuid}})};return(c,e)=>{const _=a("BaseInput"),V=a("MdEditor"),F=a("FormAction");return b(),j(F,{"less-padding":"","keep-adding":!1,"init-url":u,"init-form":i,form:n,"set-form":v,"after-submit":B},{default:m(()=>[p("div",A,[p("div",H,[r(_,{invisible:"","text-size":"2xl",type:"text",modelValue:n.title,"onUpdate:modelValue":e[0]||(e[0]=t=>n.title=t),name:"title",placeholder:o(s)("blog.props.title_placeholder"),error:o(l).title,"onUpdate:error":e[1]||(e[1]=t=>o(l).title=t),autofocus:""},null,8,["modelValue","placeholder","error"])]),p("div",k,[r(_,{invisible:"","text-size":"lg",type:"text",modelValue:n.subTitle,"onUpdate:modelValue":e[2]||(e[2]=t=>n.subTitle=t),name:"subTitle",placeholder:o(s)("blog.props.sub_title_placeholder"),error:o(l).subTitle,"onUpdate:error":e[3]||(e[3]=t=>o(l).subTitle=t),autofocus:""},null,8,["modelValue","placeholder","error"])]),p("div",y,[r(V,{placeholder:o(s)("blog.props.content_placeholder"),modelValue:n.content,"onUpdate:modelValue":e[4]||(e[4]=t=>n.content=t),error:o(l).content,"onUpdate:error":e[5]||(e[5]=t=>o(l).content=t)},null,8,["placeholder","modelValue","error"])])])]),_:1},8,["form"])}}}),z={name:"BlogAction"},I=Object.assign(z,{setup(f){const d=g();return(s,i)=>{const u=a("PageHeaderAction"),l=a("PageHeader"),n=a("ParentTransition");return b(),P(x,null,[r(l,{title:s.$trans(o(d).meta.trans,{attribute:s.$trans(o(d).meta.label)}),navs:[{label:s.$trans("blog.blog"),path:"BlogList"}]},{default:m(()=>[r(u,{name:"Blog",title:s.$trans("blog.blog"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(n,{appear:"",visibility:!0},{default:m(()=>[r(w)]),_:1})],64)}}});export{I as default};
