import{l as S,r as o,q as h,o as d,w as e,d as B,e as a,u as L,h as M,j as N,y as D,m as O,f as l,a as w,F as I,v as x,s as i,b as E,t as s}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(n,{emit:C}){const p=C,v={headline:"",location:""},c=S({...v});return(b,r)=>{const f=o("BaseInput"),g=o("FilterForm");return d(),h(g,{"init-form":v,form:c,onHide:r[2]||(r[2]=$=>p("hide"))},{default:e(()=>[B("div",q,[B("div",z,[a(f,{type:"text",modelValue:c.headline,"onUpdate:modelValue":r[0]||(r[0]=$=>c.headline=$),name:"headline",label:b.$trans("employee.experience.props.headline")},null,8,["modelValue","label"])]),B("div",G,[a(f,{type:"text",modelValue:c.location,"onUpdate:modelValue":r[1]||(r[1]=$=>c.location=$),name:"location",label:b.$trans("employee.experience.props.location")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={name:"EmployeeExperienceList"},W=Object.assign(K,{props:{employee:{type:Object,default(){return{}}}},setup(n){const C=L(),p=M(),v=N("emitter"),c=n;let b=["filter"];(D("employee:edit")||c.employee.selfService)&&b.unshift("create");const r="employee/experience/",f=O(!1),g=S({}),$=u=>{Object.assign(g,u)};return(u,m)=>{const V=o("PageHeaderAction"),T=o("PageHeader"),F=o("ParentTransition"),H=o("BaseBadge"),y=o("DataCell"),_=o("FloatingMenuItem"),P=o("FloatingMenu"),R=o("DataRow"),j=o("BaseButton"),A=o("DataTable"),U=o("ListItem");return d(),h(U,{"init-url":r,uuid:l(C).params.uuid,onSetItems:$},{header:e(()=>[n.employee.uuid?(d(),h(T,{key:0,title:u.$trans("employee.experience.experience"),navs:[{label:u.$trans("employee.employee"),path:"Employee"},{label:n.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:n.employee.uuid}}}]},{default:e(()=>[a(V,{url:`employees/${n.employee.uuid}/experiences/`,name:"EmployeeExperience",title:u.$trans("employee.experience.experience"),actions:l(b),"dropdown-actions":["print","pdf","excel"],onToggleFilter:m[0]||(m[0]=t=>f.value=!f.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):E("",!0)]),filter:e(()=>[a(F,{appear:"",visibility:f.value},{default:e(()=>[a(J,{onRefresh:m[1]||(m[1]=t=>l(v).emit("listItems")),onHide:m[2]||(m[2]=t=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a(A,{header:g.headers,meta:g.meta,module:"employee.experience",onRefresh:m[4]||(m[4]=t=>l(v).emit("listItems"))},{actionButton:e(()=>[l(D)("employee:edit")||n.employee.selfService?(d(),h(j,{key:0,onClick:m[3]||(m[3]=t=>l(p).push({name:"EmployeeExperienceCreate"}))},{default:e(()=>[i(s(u.$trans("global.add",{attribute:u.$trans("employee.experience.experience")})),1)]),_:1})):E("",!0)]),default:e(()=>[(d(!0),w(I,null,x(g.data,t=>(d(),h(R,{key:t.uuid,onDoubleClick:k=>l(p).push({name:"EmployeeExperienceShow",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[a(y,{name:"headline"},{default:e(()=>[i(s(t.headline)+" ",1),t.selfUpload?(d(),h(H,{key:0,design:t.verificationStatus.color},{default:e(()=>[i(s(t.verificationStatus.label),1)]),_:2},1032,["design"])):E("",!0)]),_:2},1024),a(y,{name:"location"},{default:e(()=>[i(s(t.location),1)]),_:2},1024),a(y,{name:"employmentType"},{default:e(()=>[i(s(t.employmentType.name),1)]),_:2},1024),a(y,{name:"startDate"},{default:e(()=>[i(s(t.startDate.formatted),1)]),_:2},1024),a(y,{name:"endDate"},{default:e(()=>[i(s(t.endDate.formatted),1)]),_:2},1024),a(y,{name:"createdAt"},{default:e(()=>[i(s(t.createdAt.formatted),1)]),_:2},1024),a(y,{name:"action"},{default:e(()=>[a(P,null,{default:e(()=>[a(_,{icon:"fas fa-arrow-circle-right",onClick:k=>l(p).push({name:"EmployeeExperienceShow",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[i(s(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),l(D)("employee:edit")||n.employee.selfService?(d(),w(I,{key:0},[a(_,{icon:"fas fa-edit",onClick:k=>l(p).push({name:"EmployeeExperienceEdit",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[i(s(u.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(_,{icon:"fas fa-copy",onClick:k=>l(p).push({name:"EmployeeExperienceDuplicate",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[i(s(u.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(_,{icon:"fas fa-trash",onClick:k=>l(v).emit("deleteItem",{uuid:n.employee.uuid,moduleUuid:t.uuid})},{default:e(()=>[i(s(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):E("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{W as default};
