import{u as b,h as G,H as O,l as _,r as l,q,o as v,w as g,d as y,e as n,f as s,J as f,a as A,F as H}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-3 gap-6"},S={class:"col-span-3"},D={class:"col-span-4 sm:col-span-1"},w={class:"col-span-4 sm:col-span-1"},E={name:"GalleryForm"},C=Object.assign(E,{setup(V){const u=b(),i=G(),p={title:"",date:"",type:""},d="gallery/",r=O(d),c=_({types:[]}),o=_({...p}),P=_({isLoaded:!u.params.uuid}),$=t=>{Object.assign(c,t),Object.assign(o,f(p))},B=t=>{var e,m;Object.assign(p,{...t,date:(e=t.date)==null?void 0:e.value,type:(m=t.type)==null?void 0:m.value}),Object.assign(o,f(p)),P.isLoaded=!0},F=t=>{var e;i.push({name:"GalleryShow",params:{uuid:(e=t.gallery)==null?void 0:e.uuid}})};return(t,e)=>{const m=l("BaseInput"),U=l("DatePicker"),j=l("BaseSelect"),k=l("FormAction");return v(),q(k,{"pre-requisites":!0,onSetPreRequisites:$,"init-url":d,"init-form":p,form:o,"set-form":B,"keep-adding":!1,"after-submit":F,redirect:"Gallery"},{default:g(()=>[y("div",R,[y("div",S,[n(m,{type:"text",modelValue:o.title,"onUpdate:modelValue":e[0]||(e[0]=a=>o.title=a),name:"title",label:t.$trans("gallery.props.title"),error:s(r).title,"onUpdate:error":e[1]||(e[1]=a=>s(r).title=a),autofocus:""},null,8,["modelValue","label","error"])]),y("div",D,[n(U,{modelValue:o.date,"onUpdate:modelValue":e[2]||(e[2]=a=>o.date=a),name:"date",label:t.$trans("gallery.props.date"),"no-clear":"",error:s(r).date,"onUpdate:error":e[3]||(e[3]=a=>s(r).date=a)},null,8,["modelValue","label","error"])]),y("div",w,[n(j,{modelValue:o.type,"onUpdate:modelValue":e[4]||(e[4]=a=>o.type=a),name:"type",label:t.$trans("gallery.props.type"),options:c.types,"label-prop":"label","value-prop":"value",error:s(r).type,"onUpdate:error":e[5]||(e[5]=a=>s(r).type=a)},null,8,["modelValue","label","options","error"])])])]),_:1},8,["form"])}}}),I={name:"GalleryAction"},N=Object.assign(I,{setup(V){const u=b();return(i,p)=>{const d=l("PageHeaderAction"),r=l("PageHeader"),c=l("ParentTransition");return v(),A(H,null,[n(r,{title:i.$trans(s(u).meta.trans,{attribute:i.$trans(s(u).meta.label)}),navs:[{label:i.$trans("gallery.gallery"),path:"Gallery"}]},{default:g(()=>[n(d,{name:"Gallery",title:i.$trans("gallery.gallery"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(c,{appear:"",visibility:!0},{default:g(()=>[n(C)]),_:1})],64)}}});export{N as default};
