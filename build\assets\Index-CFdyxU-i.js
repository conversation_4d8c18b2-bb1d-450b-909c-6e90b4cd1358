import{u as N,l as C,n as A,r as i,q as g,o as f,w as e,d as I,b as F,s as r,t as o,e as n,h as H,j as O,m as P,f as v,a as R,F as U,v as j}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(B,{emit:y}){const c=N(),k=y,b={inventories:[],employees:[],startDate:"",endDate:""},m=C({...b}),l=C({inventories:[],employees:[],isLoaded:!(c.query.inventories||c.query.employees)});return A(async()=>{l.inventories=c.query.inventories?c.query.inventories.split(","):[],l.employees=c.query.employees?c.query.employees.split(","):[],l.isLoaded=!0}),(p,u)=>{const s=i("BaseSelectSearch"),d=i("DatePicker"),D=i("FilterForm");return f(),g(D,{"init-form":b,form:m,multiple:["inventories","employees"],onHide:u[4]||(u[4]=t=>k("hide"))},{default:e(()=>[I("div",E,[I("div",z,[l.isLoaded?(f(),g(s,{key:0,multiple:"",name:"inventories",label:p.$trans("global.select",{attribute:p.$trans("inventory.inventory")}),modelValue:m.inventories,"onUpdate:modelValue":u[0]||(u[0]=t=>m.inventories=t),"value-prop":"uuid","init-search":l.inventories,"search-key":"name","search-action":"inventory/list"},{selectedOption:e(t=>[r(o(t.value.name),1)]),listOption:e(t=>[r(o(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):F("",!0)]),I("div",G,[l.isLoaded?(f(),g(s,{key:0,multiple:"",name:"employees",label:p.$trans("global.select",{attribute:p.$trans("employee.employee")}),modelValue:m.employees,"onUpdate:modelValue":u[1]||(u[1]=t=>m.employees=t),"value-prop":"uuid","init-search":l.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[r(o(t.value.name)+" ("+o(t.value.codeNumber)+") ",1)]),listOption:e(t=>[r(o(t.option.name)+" ("+o(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):F("",!0)]),I("div",J,[n(d,{start:m.startDate,"onUpdate:start":u[2]||(u[2]=t=>m.startDate=t),end:m.endDate,"onUpdate:end":u[3]||(u[3]=t=>m.endDate=t),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"InventoryInchargeList"},X=Object.assign(Q,{setup(B){const y=H(),c=O("emitter");let k=["create","filter"],b=["print","pdf","excel"];const m="inventory/incharge/",l=P(!1),p=C({}),u=s=>{Object.assign(p,s)};return(s,d)=>{const D=i("PageHeaderAction"),t=i("PageHeader"),w=i("ParentTransition"),_=i("DataCell"),V=i("TextMuted"),h=i("FloatingMenuItem"),L=i("FloatingMenu"),S=i("DataRow"),T=i("BaseButton"),q=i("DataTable"),M=i("ListItem");return f(),g(M,{"init-url":m,onSetItems:u},{header:e(()=>[n(t,{title:s.$trans("inventory.incharge.incharge"),navs:[{label:s.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[n(D,{url:"inventory/incharges/",name:"InventoryIncharge",title:s.$trans("inventory.incharge.incharge"),actions:v(k),"dropdown-actions":v(b),onToggleFilter:d[0]||(d[0]=a=>l.value=!l.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(w,{appear:"",visibility:l.value},{default:e(()=>[n(K,{onRefresh:d[1]||(d[1]=a=>v(c).emit("listItems")),onHide:d[2]||(d[2]=a=>l.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(w,{appear:"",visibility:!0},{default:e(()=>[n(q,{header:p.headers,meta:p.meta,module:"inventory.incharge",onRefresh:d[4]||(d[4]=a=>v(c).emit("listItems"))},{actionButton:e(()=>[n(T,{onClick:d[3]||(d[3]=a=>v(y).push({name:"InventoryInchargeCreate"}))},{default:e(()=>[r(o(s.$trans("global.add",{attribute:s.$trans("inventory.incharge.incharge")})),1)]),_:1})]),default:e(()=>[(f(!0),R(U,null,j(p.data,a=>(f(),g(S,{key:a.uuid,onDoubleClick:$=>v(y).push({name:"InventoryInchargeShow",params:{uuid:a.uuid}})},{default:e(()=>[n(_,{name:"inventory"},{default:e(()=>[r(o(a.inventory.name),1)]),_:2},1024),n(_,{name:"employee"},{default:e(()=>[r(o(a.employee.name)+" ",1),n(V,{block:""},{default:e(()=>[r(o(a.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),n(_,{name:"period"},{default:e(()=>[r(o(a.period),1)]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[r(o(a.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(L,null,{default:e(()=>[n(h,{icon:"fas fa-arrow-circle-right",onClick:$=>v(y).push({name:"InventoryInchargeShow",params:{uuid:a.uuid}})},{default:e(()=>[r(o(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(h,{icon:"fas fa-edit",onClick:$=>v(y).push({name:"InventoryInchargeEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(o(s.$trans("general.edit")),1)]),_:2},1032,["onClick"]),n(h,{icon:"fas fa-copy",onClick:$=>v(y).push({name:"InventoryInchargeDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(o(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),n(h,{icon:"fas fa-trash",onClick:$=>v(c).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(o(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
