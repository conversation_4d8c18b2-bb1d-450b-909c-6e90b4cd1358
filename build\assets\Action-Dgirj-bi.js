import{u as f,h as P,H as S,l as b,r,q as y,o as D,w as c,d,e as l,f as i,b as O,s as V,t as v,J as I,a as N,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},R={class:"col-span-2 sm:col-span-1"},T={class:"col-span-2 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},C={class:"col-span-3"},E={name:"AcademicDivisionInchargeForm"},w=Object.assign(E,{setup(h){const p=f();P();const n={division:"",employee:"",startDate:"",endDate:"",remarks:""},g="academic/divisionIncharge/",t=S(g),u=b({divisions:[]}),s=b({...n}),m=b({division:"",employee:"",isLoaded:!p.params.uuid}),k=o=>{Object.assign(u,o)},$=o=>{Object.assign(n,{...o,startDate:o.startDate.value,endDate:o.endDate.value,division:o.division.uuid,employee:o.employee.uuid}),Object.assign(s,I(n)),m.division=o.division.uuid,m.employee=o.employee.uuid,m.isLoaded=!0};return(o,a)=>{const A=r("BaseSelect"),U=r("BaseSelectSearch"),_=r("DatePicker"),B=r("BaseTextarea"),F=r("FormAction");return D(),y(F,{"pre-requisites":!0,onSetPreRequisites:k,"init-url":g,"init-form":n,form:s,setForm:$,redirect:"AcademicDivisionIncharge"},{default:c(()=>[d("div",q,[d("div",H,[l(A,{name:"division",label:o.$trans("academic.division.division"),modelValue:s.division,"onUpdate:modelValue":a[0]||(a[0]=e=>s.division=e),error:i(t).division,"onUpdate:error":a[1]||(a[1]=e=>i(t).division=e),"value-prop":"uuid","label-prop":"nameWithProgram",options:u.divisions},null,8,["label","modelValue","error","options"])]),d("div",R,[m.isLoaded?(D(),y(U,{key:0,name:"employee",label:o.$trans("global.select",{attribute:o.$trans("employee.employee")}),modelValue:s.employee,"onUpdate:modelValue":a[2]||(a[2]=e=>s.employee=e),error:i(t).employee,"onUpdate:error":a[3]||(a[3]=e=>i(t).employee=e),"value-prop":"uuid","init-search":m.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:c(e=>[V(v(e.value.name)+" ("+v(e.value.codeNumber)+") ",1)]),listOption:c(e=>[V(v(e.option.name)+" ("+v(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):O("",!0)]),d("div",T,[l(_,{modelValue:s.startDate,"onUpdate:modelValue":a[4]||(a[4]=e=>s.startDate=e),name:"startDate",label:o.$trans("employee.incharge.props.start_date"),"no-clear":"",error:i(t).startDate,"onUpdate:error":a[5]||(a[5]=e=>i(t).startDate=e)},null,8,["modelValue","label","error"])]),d("div",L,[l(_,{modelValue:s.endDate,"onUpdate:modelValue":a[6]||(a[6]=e=>s.endDate=e),name:"endDate",label:o.$trans("employee.incharge.props.end_date"),"no-clear":"",error:i(t).endDate,"onUpdate:error":a[7]||(a[7]=e=>i(t).endDate=e)},null,8,["modelValue","label","error"])]),d("div",C,[l(B,{modelValue:s.remarks,"onUpdate:modelValue":a[8]||(a[8]=e=>s.remarks=e),name:"remarks",label:o.$trans("employee.incharge.props.remarks"),error:i(t).remarks,"onUpdate:error":a[9]||(a[9]=e=>i(t).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),J={name:"AcademicDivisionInchargeAction"},z=Object.assign(J,{setup(h){const p=f();return(n,g)=>{const t=r("PageHeaderAction"),u=r("PageHeader"),s=r("ParentTransition");return D(),N(j,null,[l(u,{title:n.$trans(i(p).meta.trans,{attribute:n.$trans(i(p).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.division.division"),path:"AcademicDivision"},{label:n.$trans("academic.division_incharge.division_incharge"),path:"AcademicDivisionInchargeList"}]},{default:c(()=>[l(t,{name:"AcademicDivisionIncharge",title:n.$trans("academic.division_incharge.division_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(s,{appear:"",visibility:!0},{default:c(()=>[l(w)]),_:1})],64)}}});export{z as default};
