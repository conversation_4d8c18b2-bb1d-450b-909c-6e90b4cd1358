import{l as $,r as i,q as D,o as c,w as a,d as f,e as n,h as P,j as A,m as B,f as F,a as L,F as k,v as H,s as p,t as u}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},h={class:"col-span-3 sm:col-span-1"},j={__name:"FailedLoginAttemptFilter",emits:["hide"],setup(w,{emit:_}){const g=_,r={email:"",ip:"",startDate:"",endDate:""},t=$({...r});return(m,e)=>{const s=i("BaseInput"),v=i("DatePicker"),b=i("FilterForm");return c(),D(b,{"init-form":r,form:t,onHide:e[4]||(e[4]=o=>g("hide"))},{default:a(()=>[f("div",T,[f("div",U,[n(s,{type:"text",modelValue:t.email,"onUpdate:modelValue":e[0]||(e[0]=o=>t.email=o),name:"email",label:m.$trans("auth.login.props.email")},null,8,["modelValue","label"])]),f("div",R,[n(s,{type:"text",modelValue:t.ip,"onUpdate:modelValue":e[1]||(e[1]=o=>t.ip=o),name:"ip",label:m.$trans("utility.activity.ip")},null,8,["modelValue","label"])]),f("div",h,[n(v,{start:t.startDate,"onUpdate:start":e[2]||(e[2]=o=>t.startDate=o),end:t.endDate,"onUpdate:end":e[3]||(e[3]=o=>t.endDate=o),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},C={name:"FailedLoginAttemptList"},x=Object.assign(C,{setup(w){P();const _=A("emitter"),g="auth/failedLoginAttempt/",r=B(!1),t=$({}),m=e=>{Object.assign(t,e)};return(e,s)=>{const v=i("PageHeaderAction"),b=i("PageHeader"),o=i("ParentTransition"),d=i("DataCell"),y=i("DataRow"),V=i("DataTable"),I=i("ListItem");return c(),D(I,{"init-url":g,onSetItems:m},{header:a(()=>[n(b,{title:e.$trans("auth.failed_login_attempt")},{default:a(()=>[n(v,{url:"failed-login-attempts/",name:"User",title:e.$trans("auth.failed_login_attempt"),actions:["filter"],onToggleFilter:s[0]||(s[0]=l=>r.value=!r.value)},null,8,["title"])]),_:1},8,["title"])]),filter:a(()=>[n(o,{appear:"",visibility:r.value},{default:a(()=>[n(j,{onRefresh:s[1]||(s[1]=l=>F(_).emit("listItems")),onHide:s[2]||(s[2]=l=>r.value=!1)})]),_:1},8,["visibility"])]),default:a(()=>[n(o,{appear:"",visibility:!0},{default:a(()=>[n(V,{header:t.headers,meta:t.meta,module:"auth.failed_login_attempts",onRefresh:s[3]||(s[3]=l=>F(_).emit("listItems"))},{default:a(()=>[(c(!0),L(k,null,H(t.data,l=>(c(),D(y,{key:l.uuid},{default:a(()=>[n(d,{name:"email"},{default:a(()=>[p(u(l.email),1)]),_:2},1024),n(d,{name:"ip"},{default:a(()=>[p(u(l.ip),1)]),_:2},1024),n(d,{name:"browser"},{default:a(()=>[p(u(l.browser),1)]),_:2},1024),n(d,{name:"os"},{default:a(()=>[p(u(l.os),1)]),_:2},1024),n(d,{name:"createdAt"},{default:a(()=>[p(u(l.createdAt.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
