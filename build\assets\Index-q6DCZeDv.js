import{u as M,l as C,n as j,r as c,q as v,o as y,w as o,d as f,e as s,b as V,h as A,j as H,y as q,m as E,f as _,a as O,F as z,v as J,s as d,t as m}from"./app-BAwPsakn.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},re={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(F,{emit:$}){const p=M(),S=$,h=F,B={firstName:"",lastName:"",gender:"",email:"",birthStartDate:"",birthEndDate:"",startDate:"",endDate:"",bloodGroups:[],maritalStatuses:[],religions:[],categories:[],castes:[],source:"",address:""},e=C({...B}),D=C({genders:h.preRequisites.genders,bloodGroups:h.preRequisites.bloodGroups,maritalStatuses:h.preRequisites.maritalStatuses}),r=C({isLoaded:!(p.query.religions||p.query.castes||p.query.categories)});return j(async()=>{r.bloodGroups=p.query.bloodGroups?p.query.bloodGroups.split(","):[],r.maritalStatuses=p.query.maritalStatuses?p.query.maritalStatuses.split(","):[],r.religions=p.query.religions?p.query.religions.split(","):[],r.categories=p.query.categories?p.query.categories.split(","):[],r.castes=p.query.castes?p.query.castes.split(","):[],r.isLoaded=!0}),(n,t)=>{const i=c("BaseInput"),b=c("BaseSelect"),k=c("BaseSelectSearch"),N=c("DatePicker"),w=c("FilterForm");return y(),v(w,{"init-form":B,form:e,multiple:["bloodGroups","maritalStatuses","religions","castes","categories"],onHide:t[15]||(t[15]=a=>S("hide"))},{default:o(()=>[f("div",K,[f("div",Q,[s(i,{type:"text",modelValue:e.firstName,"onUpdate:modelValue":t[0]||(t[0]=a=>e.firstName=a),name:"firstName",label:n.$trans("contact.props.first_name")},null,8,["modelValue","label"])]),f("div",W,[s(i,{type:"text",modelValue:e.lastName,"onUpdate:modelValue":t[1]||(t[1]=a=>e.lastName=a),name:"lastName",label:n.$trans("contact.props.last_name")},null,8,["modelValue","label"])]),f("div",X,[s(b,{modelValue:e.gender,"onUpdate:modelValue":t[2]||(t[2]=a=>e.gender=a),name:"gender",label:n.$trans("contact.props.gender"),options:D.genders},null,8,["modelValue","label","options"])]),f("div",Y,[s(b,{multiple:"",modelValue:e.bloodGroups,"onUpdate:modelValue":t[3]||(t[3]=a=>e.bloodGroups=a),name:"bloodGroups",label:n.$trans("contact.props.blood_group"),options:D.bloodGroups},null,8,["modelValue","label","options"])]),f("div",Z,[s(b,{multiple:"",modelValue:e.maritalStatuses,"onUpdate:modelValue":t[4]||(t[4]=a=>e.maritalStatuses=a),name:"maritalStatuses",label:n.$trans("contact.props.marital_status"),options:D.maritalStatuses},null,8,["modelValue","label","options"])]),f("div",x,[r.isLoaded?(y(),v(k,{key:0,multiple:"",name:"religions",label:n.$trans("global.select",{attribute:n.$trans("contact.religion.religion")}),modelValue:e.religions,"onUpdate:modelValue":t[5]||(t[5]=a=>e.religions=a),"label-props":"name","value-prop":"uuid","init-search":r.religions,"search-action":"option/list","additional-search-query":{type:"religion"}},null,8,["label","modelValue","init-search"])):V("",!0)]),f("div",ee,[r.isLoaded?(y(),v(k,{key:0,multiple:"",name:"castes",label:n.$trans("global.select",{attribute:n.$trans("contact.caste.caste")}),modelValue:e.castes,"onUpdate:modelValue":t[6]||(t[6]=a=>e.castes=a),"label-props":"name","value-prop":"uuid","init-search":r.castes,"search-action":"option/list","additional-search-query":{type:"member_caste"}},null,8,["label","modelValue","init-search"])):V("",!0)]),f("div",te,[r.isLoaded?(y(),v(k,{key:0,multiple:"",name:"categories",label:n.$trans("global.select",{attribute:n.$trans("contact.category.category")}),modelValue:e.categories,"onUpdate:modelValue":t[7]||(t[7]=a=>e.categories=a),"label-props":"name","value-prop":"uuid","init-search":r.categories,"search-action":"option/list","additional-search-query":{type:"member_category"}},null,8,["label","modelValue","init-search"])):V("",!0)]),f("div",ae,[s(i,{type:"text",modelValue:e.email,"onUpdate:modelValue":t[8]||(t[8]=a=>e.email=a),name:"email",label:n.$trans("contact.props.email")},null,8,["modelValue","label"])]),f("div",se,[s(N,{start:e.birthStartDate,"onUpdate:start":t[9]||(t[9]=a=>e.birthStartDate=a),end:e.birthEndDate,"onUpdate:end":t[10]||(t[10]=a=>e.birthEndDate=a),name:"birthDateBetween",as:"range",label:n.$trans("global.date_between",{attribute:n.$trans("contact.props.birth_date")})},null,8,["start","end","label"])]),f("div",oe,[s(N,{start:e.startDate,"onUpdate:start":t[11]||(t[11]=a=>e.startDate=a),end:e.endDate,"onUpdate:end":t[12]||(t[12]=a=>e.endDate=a),name:"dateBetween",as:"range",label:n.$trans("global.date_between",{attribute:n.$trans("general.created_at")})},null,8,["start","end","label"])]),f("div",le,[s(i,{type:"text",modelValue:e.source,"onUpdate:modelValue":t[13]||(t[13]=a=>e.source=a),name:"source",label:n.$trans("contact.props.source")},null,8,["modelValue","label"])]),f("div",ne,[s(i,{type:"text",modelValue:e.address,"onUpdate:modelValue":t[14]||(t[14]=a=>e.address=a),name:"address",label:n.$trans("contact.props.address.address")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ie={name:"ContactList"},de=Object.assign(ie,{setup(F){const $=A(),p=H("emitter");let S=["filter"];q("contact:create")&&S.unshift("create"),q("contact:config")&&S.push("config");let h=[];q("contact:export")&&(h=["print","pdf","excel"]);const B="contact/",e=E(!1),D=C({genders:[]}),r=C({}),n=i=>{Object.assign(r,i)},t=i=>{Object.assign(D,i)};return(i,b)=>{const k=c("PageHeaderAction"),N=c("PageHeader"),w=c("ParentTransition"),a=c("BaseBadge"),g=c("DataCell"),G=c("TextMuted"),U=c("FloatingMenuItem"),R=c("FloatingMenu"),I=c("DataRow"),P=c("BaseButton"),L=c("DataTable"),T=c("ListItem");return y(),v(T,{"pre-requisites":!0,onSetPreRequisites:t,"init-url":B,onSetItems:n},{header:o(()=>[s(N,{title:i.$trans("contact.contact"),navs:[{label:i.$trans("contact.contact"),path:"Contact"}]},{default:o(()=>[s(k,{url:"contacts/",name:"Contact",title:i.$trans("contact.contact"),actions:_(S),"dropdown-actions":_(h),headers:r.headers,onToggleFilter:b[0]||(b[0]=l=>e.value=!e.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"])]),filter:o(()=>[s(w,{appear:"",visibility:e.value},{default:o(()=>[s(re,{"pre-requisites":D,onRefresh:b[1]||(b[1]=l=>_(p).emit("listItems")),onHide:b[2]||(b[2]=l=>e.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:o(()=>[s(w,{appear:"",visibility:!0},{default:o(()=>[s(L,{header:r.headers,meta:r.meta,module:"contact",onRefresh:b[4]||(b[4]=l=>_(p).emit("listItems"))},{actionButton:o(()=>[_(q)("contact:create")?(y(),v(P,{key:0,onClick:b[3]||(b[3]=l=>_($).push({name:"ContactCreate"}))},{default:o(()=>[d(m(i.$trans("global.add",{attribute:i.$trans("contact.contact")})),1)]),_:1})):V("",!0)]),default:o(()=>[(y(!0),O(z,null,J(r.data,l=>(y(),v(I,{key:l.uuid,onDoubleClick:u=>_($).push({name:"ContactShow",params:{uuid:l.uuid}})},{default:o(()=>[s(g,{name:"name"},{default:o(()=>[d(m(l.name)+" ",1),l.source.value?(y(),v(a,{key:0,label:l.source.label},null,8,["label"])):V("",!0)]),_:2},1024),s(g,{name:"gender"},{default:o(()=>[d(m(l.gender.label),1)]),_:2},1024),s(g,{name:"birthDate"},{default:o(()=>[d(m(l.birthDate.formatted),1)]),_:2},1024),s(g,{name:"contactNumber"},{default:o(()=>[d(m(l.contactNumber)+" ",1),l.email?(y(),v(G,{key:0,block:""},{default:o(()=>[d(m(l.email),1)]),_:2},1024)):V("",!0)]),_:2},1024),s(g,{name:"fatherName"},{default:o(()=>[d(m(l.fatherName),1)]),_:2},1024),s(g,{name:"motherName"},{default:o(()=>[d(m(l.motherName),1)]),_:2},1024),s(g,{name:"bloodGroup"},{default:o(()=>{var u;return[d(m(((u=l.bloodGroup)==null?void 0:u.label)||"-"),1)]}),_:2},1024),s(g,{name:"maritalStatus"},{default:o(()=>{var u;return[d(m(((u=l.maritalStatus)==null?void 0:u.label)||"-"),1)]}),_:2},1024),s(g,{name:"religion"},{default:o(()=>{var u;return[d(m(((u=l.religion)==null?void 0:u.name)||"-"),1)]}),_:2},1024),s(g,{name:"category"},{default:o(()=>{var u;return[d(m(((u=l.category)==null?void 0:u.name)||"-"),1)]}),_:2},1024),s(g,{name:"caste"},{default:o(()=>{var u;return[d(m(((u=l.caste)==null?void 0:u.name)||"-"),1)]}),_:2},1024),s(g,{name:"address"},{default:o(()=>[d(m(l.address),1)]),_:2},1024),s(g,{name:"createdAt"},{default:o(()=>[d(m(l.createdAt.formatted),1)]),_:2},1024),s(g,{name:"action"},{default:o(()=>[s(R,null,{default:o(()=>[s(U,{icon:"fas fa-arrow-circle-right",onClick:u=>_($).push({name:"ContactShow",params:{uuid:l.uuid}})},{default:o(()=>[d(m(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),_(q)("contact:create")?(y(),v(U,{key:0,icon:"fas fa-copy",onClick:u=>_($).push({name:"ContactDuplicate",params:{uuid:l.uuid}})},{default:o(()=>[d(m(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):V("",!0),_(q)("contact:delete")?(y(),v(U,{key:1,icon:"fas fa-trash",onClick:u=>_(p).emit("deleteItem",{uuid:l.uuid})},{default:o(()=>[d(m(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])):V("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{de as default};
