import{u as N,l as A,n as H,r as l,q as f,o as b,w as e,d as C,b as y,s as i,t as a,e as o,h as O,j as R,y as g,m as U,f as c,a as j,F as P,v as E}from"./app-BAwPsakn.js";const W={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(V,{emit:_}){const p=N(),$=_,v={batches:[],employees:[],startDate:"",endDate:""},u=A({...v}),r=A({batches:[],employees:[],isLoaded:!(p.query.batches||p.query.employees)});return H(async()=>{r.batches=p.query.batches?p.query.batches.split(","):[],r.employees=p.query.employees?p.query.employees.split(","):[],r.isLoaded=!0}),(h,d)=>{const s=l("BaseSelectSearch"),m=l("DatePicker"),I=l("FilterForm");return b(),f(I,{"init-form":v,form:u,multiple:["batches","employees"],onHide:d[4]||(d[4]=t=>$("hide"))},{default:e(()=>[C("div",W,[C("div",z,[r.isLoaded?(b(),f(s,{key:0,multiple:"",name:"batches",label:h.$trans("global.select",{attribute:h.$trans("academic.batch.batch")}),modelValue:u.batches,"onUpdate:modelValue":d[0]||(d[0]=t=>u.batches=t),"value-prop":"uuid","init-search":r.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[i(a(t.value.course.name)+" "+a(t.value.name),1)]),listOption:e(t=>[i(a(t.option.course.nameWithTerm)+" "+a(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):y("",!0)]),C("div",G,[r.isLoaded?(b(),f(s,{key:0,multiple:"",name:"employees",label:h.$trans("global.select",{attribute:h.$trans("employee.employee")}),modelValue:u.employees,"onUpdate:modelValue":d[1]||(d[1]=t=>u.employees=t),"value-prop":"uuid","init-search":r.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[i(a(t.value.name)+" ("+a(t.value.codeNumber)+") ",1)]),listOption:e(t=>[i(a(t.option.name)+" ("+a(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):y("",!0)]),C("div",J,[o(m,{start:u.startDate,"onUpdate:start":d[2]||(d[2]=t=>u.startDate=t),end:u.endDate,"onUpdate:end":d[3]||(d[3]=t=>u.endDate=t),name:"dateBetween",as:"range",label:h.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"AcademicBatchInchargeList"},Y=Object.assign(Q,{setup(V){const _=O(),p=R("emitter");let $=["filter"];g("batch-incharge:create")&&$.unshift("create");let v=[];g("batch-incharge:export")&&(v=["print","pdf","excel"]);const u="academic/batchIncharge/",r=U(!1),h=A({}),d=s=>{Object.assign(h,s)};return(s,m)=>{const I=l("PageHeaderAction"),t=l("PageHeader"),w=l("ParentTransition"),F=l("TextMuted"),k=l("DataCell"),B=l("FloatingMenuItem"),T=l("FloatingMenu"),L=l("DataRow"),S=l("BaseButton"),q=l("DataTable"),M=l("ListItem");return b(),f(M,{"init-url":u,onSetItems:d},{header:e(()=>[o(t,{title:s.$trans("academic.batch_incharge.batch_incharge"),navs:[{label:s.$trans("academic.academic"),path:"Academic"},{label:s.$trans("academic.batch.batch"),path:"AcademicBatch"}]},{default:e(()=>[o(I,{url:"academic/batch-incharges/",name:"AcademicBatchIncharge",title:s.$trans("academic.batch_incharge.batch_incharge"),actions:c($),"dropdown-actions":c(v),onToggleFilter:m[0]||(m[0]=n=>r.value=!r.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(w,{appear:"",visibility:r.value},{default:e(()=>[o(K,{onRefresh:m[1]||(m[1]=n=>c(p).emit("listItems")),onHide:m[2]||(m[2]=n=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(w,{appear:"",visibility:!0},{default:e(()=>[o(q,{header:h.headers,meta:h.meta,module:"academic.batch_incharge",onRefresh:m[4]||(m[4]=n=>c(p).emit("listItems"))},{actionButton:e(()=>[c(g)("batch-incharge:create")?(b(),f(S,{key:0,onClick:m[3]||(m[3]=n=>c(_).push({name:"AcademicBatchInchargeCreate"}))},{default:e(()=>[i(a(s.$trans("global.add",{attribute:s.$trans("academic.batch_incharge.batch_incharge")})),1)]),_:1})):y("",!0)]),default:e(()=>[(b(!0),j(P,null,E(h.data,n=>(b(),f(L,{key:n.uuid,onDoubleClick:D=>c(_).push({name:"AcademicBatchInchargeShow",params:{uuid:n.uuid}})},{default:e(()=>[o(k,{name:"batch"},{default:e(()=>[i(a(n.batch.course.name)+" ",1),o(F,{block:""},{default:e(()=>[i(a(n.batch.name),1)]),_:2},1024)]),_:2},1024),o(k,{name:"employee"},{default:e(()=>[i(a(n.employee.name)+" ",1),o(F,{block:""},{default:e(()=>[i(a(n.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),o(k,{name:"period"},{default:e(()=>[i(a(n.period),1)]),_:2},1024),o(k,{name:"createdAt"},{default:e(()=>[i(a(n.createdAt.formatted),1)]),_:2},1024),o(k,{name:"action"},{default:e(()=>[o(T,null,{default:e(()=>[o(B,{icon:"fas fa-arrow-circle-right",onClick:D=>c(_).push({name:"AcademicBatchInchargeShow",params:{uuid:n.uuid}})},{default:e(()=>[i(a(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),c(g)("batch-incharge:edit")?(b(),f(B,{key:0,icon:"fas fa-edit",onClick:D=>c(_).push({name:"AcademicBatchInchargeEdit",params:{uuid:n.uuid}})},{default:e(()=>[i(a(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):y("",!0),c(g)("batch-incharge:create")?(b(),f(B,{key:1,icon:"fas fa-copy",onClick:D=>c(_).push({name:"AcademicBatchInchargeDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[i(a(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):y("",!0),c(g)("batch-incharge:delete")?(b(),f(B,{key:2,icon:"fas fa-trash",onClick:D=>c(p).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[i(a(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):y("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
