import{u as q,h as E,j as C,m as F,l as z,r as u,z as K,a as f,o as l,q as d,b as r,e as s,w as t,f as n,y as U,F as I,A as v,d as p,t as i,s as c}from"./app-BAwPsakn.js";import{_ as G,a as J}from"./Og-CAJZ9P6B.js";const Q={class:"space-y-2"},W={class:"space-x-2"},X={key:0},Y={key:1},Z={key:0},ee={key:1},te={class:"grid grid-cols-1 gap-x-4 gap-y-8"},ae={class:"space-y-4"},ne={class:"px-4 py-2"},oe={class:"text-2xl font-bold text-gray-900 dark:text-gray-400"},se={key:0,class:"mt-2 font-semibold text-gray-800 dark:text-gray-500"},le={name:"BlogShow"},de=Object.assign(le,{setup(ie){const h=q(),A=E(),o=C("$trans"),y=C("emitter"),$={uuid:""},V="blog/",k=F(!1),e=z({...$}),L=x=>{Object.assign(e,x)};return(x,a)=>{const _=u("BaseButton"),T=u("PageHeaderAction"),D=u("PageHeader"),w=u("BaseBadge"),m=u("ListItemView"),P=u("TagList"),R=u("ListContainerVertical"),B=u("BaseCard"),H=u("ListMedia"),S=u("BaseDataView"),j=u("MarkdownContent"),M=u("DetailLayoutVertical"),N=u("ShowItem"),O=u("ParentTransition"),b=K("tooltip");return l(),f(I,null,[e.uuid?(l(),d(D,{key:0,title:n(o)(n(h).meta.trans,{attribute:n(o)(n(h).meta.label)}),navs:[{label:n(o)("blog.blog"),path:"BlogList"}]},{default:t(()=>[s(T,{name:"Blog",title:n(o)("blog.blog"),actions:["list"]},{default:t(()=>[n(U)("blog:edit")?(l(),f(I,{key:0},[e.pinnedAt.value?v((l(),d(_,{key:0,design:"white",onClick:a[0]||(a[0]=g=>n(y).emit("showActionItem",{uuid:e==null?void 0:e.uuid,action:"unpin",confirmation:!0}))},{default:t(()=>a[9]||(a[9]=[p("i",{class:"far fa-bookmark"},null,-1)])),_:1})),[[b,n(o)("general.unpin")]]):r("",!0),e.pinnedAt.value?r("",!0):v((l(),d(_,{key:1,design:"white",onClick:a[1]||(a[1]=g=>n(y).emit("showActionItem",{uuid:e==null?void 0:e.uuid,action:"pin",confirmation:!0}))},{default:t(()=>a[10]||(a[10]=[p("i",{class:"fas fa-bookmark"},null,-1)])),_:1})),[[b,n(o)("general.pin")]]),e.archivedAt.value?r("",!0):v((l(),d(_,{key:2,design:"white",onClick:a[2]||(a[2]=g=>n(y).emit("showActionItem",{uuid:e==null?void 0:e.uuid,action:"archive",confirmation:!0}))},{default:t(()=>a[11]||(a[11]=[p("i",{class:"fas fa-box-archive"},null,-1)])),_:1})),[[b,n(o)("general.archive")]]),e.archivedAt.value?v((l(),d(_,{key:3,design:"white",onClick:a[3]||(a[3]=g=>n(y).emit("showActionItem",{uuid:e==null?void 0:e.uuid,action:"unarchive",confirmation:!0}))},{default:t(()=>a[12]||(a[12]=[p("i",{class:"fas fa-box-open"},null,-1)])),_:1})),[[b,n(o)("general.unarchive")]]):r("",!0),v((l(),d(_,{design:"white",onClick:a[4]||(a[4]=g=>n(A).push({name:"BlogEdit",params:{uuid:e.uuid}}))},{default:t(()=>a[13]||(a[13]=[p("i",{class:"fas fa-pen-to-square"},null,-1)])),_:1})),[[b,n(o)("general.edit")]])],64)):r("",!0)]),_:1},8,["title"])]),_:1},8,["title","navs"])):r("",!0),s(O,{appear:"",visibility:!0},{default:t(()=>[s(N,{"init-url":V,uuid:n(h).params.uuid,onSetItem:L,onRedirectTo:a[7]||(a[7]=g=>n(A).push({name:"Blog"})),refresh:k.value,onRefreshed:a[8]||(a[8]=g=>k.value=!1)},{default:t(()=>[e.uuid?(l(),d(M,{key:0},{detail:t(()=>[p("div",Q,[s(B,{"no-padding":"","no-content-padding":""},{title:t(()=>[c(i(e.title),1)]),action:t(()=>a[14]||(a[14]=[])),default:t(()=>[s(R,null,{default:t(()=>[e.publishedAt.value||e.archivedAt.value||e.pinnedAt.value?(l(),d(m,{key:0},{default:t(()=>[p("div",W,[e.publishedAt.value?(l(),d(w,{key:0,design:"success"},{default:t(()=>[c(i(n(o)("blog.props.published")),1)]),_:1})):r("",!0),e.archivedAt.value?(l(),d(w,{key:1,design:"danger"},{default:t(()=>[c(i(n(o)("general.archived")),1)]),_:1})):r("",!0),e.pinnedAt.value?(l(),d(w,{key:2,design:"primary"},{default:t(()=>[c(i(n(o)("general.pinned")),1)]),_:1})):r("",!0)])]),_:1})):r("",!0),s(m,{label:n(o)("general.created_at")},{default:t(()=>[c(i(e.createdAt.formatted),1)]),_:1},8,["label"]),s(m,{label:n(o)("blog.props.published_at")},{default:t(()=>[c(i(e.publishedAt.formatted||"-"),1)]),_:1},8,["label"]),s(m,{label:n(o)("general.updated_at")},{default:t(()=>[c(i(e.updatedAt.formatted),1)]),_:1},8,["label"]),s(m,{label:n(o)("blog.category.category")},{default:t(()=>[e.category?(l(),f("span",X,[s(w,{design:"custom",color:e.category.color},{default:t(()=>[c(i(e.category.name),1)]),_:1},8,["color"])])):(l(),f("span",Y,"-"))]),_:1},8,["label"]),s(m,{label:n(o)("blog.props.slug")},{default:t(()=>[c(i(e.url),1)]),_:1},8,["label"]),s(m,{label:n(o)("blog.props.seo.robots")},{default:t(()=>[e.seo.robots?(l(),f("span",Z,a[15]||(a[15]=[p("i",{class:"far fa-check-circle fa-lg text-success"},null,-1)]))):(l(),f("span",ee,a[16]||(a[16]=[p("i",{class:"far fa-times-circle fa-lg text-danger"},null,-1)])))]),_:1},8,["label"]),s(m,{label:n(o)("blog.props.seo.meta_title")},{default:t(()=>[c(i(e.seo.metaTitle),1)]),_:1},8,["label"]),s(m,{label:n(o)("blog.props.seo.meta_description")},{default:t(()=>[c(i(e.seo.metaDescription),1)]),_:1},8,["label"]),s(m,{label:n(o)("blog.props.seo.meta_keywords")},{default:t(()=>[c(i(e.seo.metaKeywords),1)]),_:1},8,["label"]),s(m,{label:n(o)("general.tags")},{default:t(()=>[s(P,{tags:e.tags},null,8,["tags"])]),_:1},8,["label"])]),_:1})]),_:1}),s(B,null,{title:t(()=>[c(i(n(o)("general.media")),1)]),default:t(()=>[p("dl",te,[s(S,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[s(H,{grid:1,media:e.media,url:`/app/blogs/${e.uuid}/`},null,8,["media","url"])]),_:1})])]),_:1}),e.assets.defaultOg?r("",!0):(l(),d(B,{key:0,"no-padding":"","no-content-padding":""},{title:t(()=>[c(i(n(o)("blog.assets.og")),1)]),default:t(()=>[e.uuid?(l(),d(J,{key:0,disabled:"",blog:e,onRefreshItem:a[5]||(a[5]=g=>k.value=!0)},null,8,["blog"])):r("",!0)]),_:1}))])]),default:t(()=>[p("div",ae,[s(B,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{default:t(()=>[e.uuid?(l(),d(G,{key:0,disabled:"",blog:e,onRefreshItem:a[6]||(a[6]=g=>k.value=!0)},null,8,["blog"])):r("",!0),p("div",ne,[p("h1",oe,i(e.title),1),e.subTitle?(l(),f("h2",se,i(e.subTitle),1)):r("",!0),s(j,{class:"mt-2",content:e.contentHtml},null,8,["content"])])]),_:1})])]),_:1})):r("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{de as default};
