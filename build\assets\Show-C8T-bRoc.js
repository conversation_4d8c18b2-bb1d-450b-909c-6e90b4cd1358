import{i as v,u as C,h as P,j as T,l as V,r as i,a as y,o as p,e as t,w as e,f as r,q as D,b as j,d as H,s as n,t as o,F as I}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"AcademicSessionShow"},M=Object.assign(R,{setup(E){v();const c=C(),u=P();T("emitter");const _={},f="academic/session/",s=V({..._}),b=a=>{Object.assign(s,a)};return(a,d)=>{const g=i("PageHeaderAction"),$=i("PageHeader"),m=i("TextMuted"),l=i("BaseDataView"),B=i("BaseButton"),S=i("ShowButton"),h=i("BaseCard"),A=i("ShowItem"),w=i("ParentTransition");return p(),y(I,null,[t($,{title:a.$trans(r(c).meta.trans,{attribute:a.$trans(r(c).meta.label)}),navs:[{label:a.$trans("academic.academic"),path:"Academic"},{label:a.$trans("academic.session.session"),path:"AcademicSessionList"}]},{default:e(()=>[t(g,{name:"AcademicSession",title:a.$trans("academic.session.session"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(w,{appear:"",visibility:!0},{default:e(()=>[t(A,{"init-url":f,uuid:r(c).params.uuid,onSetItem:b,onRedirectTo:d[1]||(d[1]=k=>r(u).push({name:"AcademicSession"}))},{default:e(()=>[s.uuid?(p(),D(h,{key:0},{title:e(()=>[n(o(s.name),1)]),footer:e(()=>[t(S,null,{default:e(()=>[t(B,{design:"primary",onClick:d[0]||(d[0]=k=>r(u).push({name:"AcademicSessionEdit",params:{uuid:s.uuid}}))},{default:e(()=>[n(o(a.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[H("dl",N,[t(l,{label:a.$trans("academic.session.props.name")},{default:e(()=>[n(o(s.name)+" ",1),t(m,{block:""},{default:e(()=>[n(o(s.alias),1)]),_:1})]),_:1},8,["label"]),t(l,{label:a.$trans("academic.session.props.code")},{default:e(()=>[n(o(s.code)+" ",1),t(m,{block:""},{default:e(()=>[n(o(s.shortcode),1)]),_:1})]),_:1},8,["label"]),t(l,{label:a.$trans("academic.session.props.start_date")},{default:e(()=>[n(o(s.startDate.formatted),1)]),_:1},8,["label"]),t(l,{label:a.$trans("academic.session.props.end_date")},{default:e(()=>[n(o(s.endDate.formatted),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:a.$trans("academic.session.props.description")},{default:e(()=>[n(o(s.description),1)]),_:1},8,["label"]),t(l,{label:a.$trans("general.created_at")},{default:e(()=>[n(o(s.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:a.$trans("general.updated_at")},{default:e(()=>[n(o(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):j("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
