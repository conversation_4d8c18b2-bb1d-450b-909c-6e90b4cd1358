import{u as k,j,g as A,H as I,l as C,r as l,q as P,o as v,w as t,d as g,e as s,f as n,i as R,h as q,a as H,b as U,s as f,t as _,F as E}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},h={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},L={name:"TeamSyncForm"},z=Object.assign(L,{props:{},emits:["completed"],setup(F,{emit:d}){const p=k(),S=d,V=j("emitter"),m={team:"",role:!1,permission:!1},b="team/",a=A("teams").value.filter(r=>r.uuid!==p.params.uuid),o=I(b),y=C({}),u=C({...m}),$=r=>{Object.assign(y,r)},w=()=>{S("completed"),V.emit("listItems")};return(r,e)=>{const T=l("BaseSelect"),c=l("BaseSwitch"),B=l("FormAction");return v(),P(B,{"no-card":"","pre-requisites":!1,onSetPreRequisites:$,"init-url":b,uuid:n(p).params.uuid,"no-data-fetch":"",action:"sync","init-form":m,form:u,"keep-adding":!1,"after-submit":w},{default:t(()=>[g("div",N,[g("div",O,[s(T,{name:"team",label:r.$trans("team.team"),modelValue:u.team,"onUpdate:modelValue":e[0]||(e[0]=i=>u.team=i),error:n(o).team,"onUpdate:error":e[1]||(e[1]=i=>n(o).team=i),options:n(a),"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","error","options"])]),g("div",h,[s(c,{vertical:"",modelValue:u.role,"onUpdate:modelValue":e[2]||(e[2]=i=>u.role=i),name:"role",label:r.$trans("global.sync",{attribute:r.$trans("team.config.role.role")}),error:n(o).role,"onUpdate:error":e[3]||(e[3]=i=>n(o).role=i)},null,8,["modelValue","label","error"])]),g("div",D,[s(c,{vertical:"",modelValue:u.permission,"onUpdate:modelValue":e[4]||(e[4]=i=>u.permission=i),name:"permission",label:r.$trans("global.sync",{attribute:r.$trans("team.config.permission.permission")}),error:n(o).permission,"onUpdate:error":e[5]||(e[5]=i=>n(o).permission=i)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),G={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},J={name:"TeamShow"},M=Object.assign(J,{setup(F){R();const d=k(),p=q(),S={},V="team/",m=C({...S}),b=a=>{Object.assign(m,a)};return(a,o)=>{const y=l("PageHeaderAction"),u=l("PageHeader"),$=l("BaseDataView"),w=l("BaseButton"),r=l("ShowButton"),e=l("BaseCard"),T=l("ShowItem"),c=l("ParentTransition");return v(),H(E,null,[s(u,{title:a.$trans(n(d).meta.trans,{attribute:a.$trans(n(d).meta.label)}),navs:[{label:a.$trans("team.team"),path:"TeamList"}]},{default:t(()=>[s(y,{name:"Team",title:a.$trans("team.team"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(c,{appear:"",visibility:!0},{default:t(()=>[s(T,{"init-url":V,uuid:n(d).params.uuid,onSetItem:b,onRedirectTo:o[1]||(o[1]=B=>n(p).push({name:"Team"}))},{default:t(()=>[m.uuid?(v(),P(e,{key:0},{title:t(()=>[f(_(m.name),1)]),footer:t(()=>[s(r,null,{default:t(()=>[s(w,{design:"primary",onClick:o[0]||(o[0]=B=>n(p).push({name:"TeamEdit",params:{uuid:m.uuid}}))},{default:t(()=>[f(_(a.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[g("dl",G,[s($,{label:a.$trans("general.created_at")},{default:t(()=>[f(_(m.createdAt.formatted),1)]),_:1},8,["label"]),s($,{label:a.$trans("general.updated_at")},{default:t(()=>[f(_(m.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):U("",!0)]),_:1},8,["uuid"])]),_:1}),s(c,{appear:"",visibility:!0},{default:t(()=>[m.uuid?(v(),P(e,{key:0},{title:t(()=>[f(_(a.$trans("general.sync")),1)]),default:t(()=>[s(z,{onCompleted:o[2]||(o[2]=B=>a.emitter.emit("refreshItem"))})]),_:1})):U("",!0)]),_:1})],64)}}});export{M as default};
