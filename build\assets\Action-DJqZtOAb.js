import{u as v,H as V,l as _,r as i,q as $,o as f,w as u,d as p,e as a,f as s,J as I,a as P,F as A}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3"},O={name:"InventoryForm"},T=Object.assign(O,{setup(y){const l=v(),e={name:"",description:""},m="inventory/",r=V(m),t=_({...e}),d=_({isLoaded:!l.params.uuid}),g=c=>{Object.assign(e,{...c}),Object.assign(t,I(e)),d.isLoaded=!0};return(c,n)=>{const b=i("BaseInput"),B=i("BaseTextarea"),F=i("FormAction");return f(),$(F,{"init-url":m,"init-form":e,form:t,"set-form":g,redirect:"Inventory"},{default:u(()=>[p("div",H,[p("div",U,[a(b,{type:"text",modelValue:t.name,"onUpdate:modelValue":n[0]||(n[0]=o=>t.name=o),name:"name",label:c.$trans("inventory.props.name"),error:s(r).name,"onUpdate:error":n[1]||(n[1]=o=>s(r).name=o)},null,8,["modelValue","label","error"])]),p("div",j,[a(B,{modelValue:t.description,"onUpdate:modelValue":n[2]||(n[2]=o=>t.description=o),name:"description",label:c.$trans("inventory.props.description"),error:s(r).description,"onUpdate:error":n[3]||(n[3]=o=>s(r).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),k={name:"InventoryAction"},w=Object.assign(k,{setup(y){const l=v();return(e,m)=>{const r=i("PageHeaderAction"),t=i("PageHeader"),d=i("ParentTransition");return f(),P(A,null,[a(t,{title:e.$trans(s(l).meta.trans,{attribute:e.$trans(s(l).meta.label)}),navs:[{label:e.$trans("inventory.inventory"),path:"Inventory"}]},{default:u(()=>[a(r,{name:"Inventory",title:e.$trans("inventory.inventory"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(d,{appear:"",visibility:!0},{default:u(()=>[a(T)]),_:1})],64)}}});export{w as default};
