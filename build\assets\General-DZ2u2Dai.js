import{r as s,q as i,o as t,w as o,a as c,b as l,d,f as a,aS as r}from"./app-BAwPsakn.js";const u=["innerHTML"],p=["innerHTML"],_={name:"TripGeneral"},y=Object.assign(_,{props:{trip:{type:Object,default(){return{}}}},setup(e){return(m,f)=>{const n=s("BaseCard");return t(),i(n,null,{default:o(()=>[e.trip.itinerary?(t(),c("div",{key:0,class:"dark:text-gray-400 mb-4",innerHTML:a(r)(e.trip.itinerary)},null,8,u)):l("",!0),d("div",{class:"dark:text-gray-400",innerHTML:a(r)(e.trip.description)},null,8,p)]),_:1})}}});export{y as default};
