import{i as E,u as U,h as G,j as J,l as K,g as Q,m as B,r as l,a as p,o as d,e as o,w as e,f as t,q as u,b as f,d as g,F as v,v as k,s as c,t as s,y as W}from"./app-BAwPsakn.js";const X={class:"space-y-2"},Y={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},Z={class:"flex flex-wrap gap-2"},ee={class:"mt-4"},te={name:"AcademicDivisionShow"},ne=Object.assign(te,{setup(ae){const V=E(),b=U(),A=G(),a=J("$trans"),L={},C="academic/division/",P=[{key:"course",label:a("academic.course.course"),visibility:!0},{key:"action",label:"",visibility:!0}],i=K({...L}),S=_=>{Object.assign(i,_)},T=Q("periods"),D=B(!1),y=B(!1),I=_=>{D.value=!0,V.dispatch(C+"updateCurrentPeriod",{uuid:i.uuid,form:{period_id:_.id}}).then(()=>{y.value=!0}).catch(()=>{}).finally(()=>{D.value=!1})};return(_,r)=>{const R=l("PageHeaderAction"),H=l("PageHeader"),w=l("TextMuted"),m=l("ListItemView"),$=l("ListContainerVertical"),h=l("BaseCard"),x=l("DataCell"),j=l("DataRow"),N=l("SimpleTable"),F=l("BaseDataView"),M=l("BaseBadge"),O=l("DetailLayoutVertical"),q=l("ShowItem"),z=l("ParentTransition");return d(),p(v,null,[o(H,{title:t(a)(t(b).meta.trans,{attribute:t(a)(t(b).meta.label)}),navs:[{label:t(a)("academic.academic"),path:"Academic"},{label:t(a)("academic.division.division"),path:"AcademicDivisionList"}]},{default:e(()=>[o(R,{name:"AcademicDivision",title:t(a)("academic.division.division"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(z,{appear:"",visibility:!0},{default:e(()=>[o(q,{"init-url":C,uuid:t(b).params.uuid,onSetItem:S,onRedirectTo:r[0]||(r[0]=n=>t(A).push({name:"AcademicDivision"})),refresh:y.value,onRefreshed:r[1]||(r[1]=n=>y.value=!1)},{default:e(()=>[i.uuid?(d(),u(O,{key:0},{detail:e(()=>[g("div",X,[o(h,{"no-padding":"","no-content-padding":""},{title:e(()=>[c(s(t(a)("academic.division.division")),1)]),action:e(()=>r[2]||(r[2]=[])),default:e(()=>[o($,null,{default:e(()=>[o(m,{label:t(a)("academic.division.props.name")},{default:e(()=>[c(s(i.name)+" ",1),i.pgAccount?(d(),u(w,{key:0,block:""},{default:e(()=>[c(s(i.pgAccount),1)]),_:1})):f("",!0)]),_:1},8,["label"]),o(m,{label:t(a)("academic.division.props.code")},{default:e(()=>[c(s(i.code)+" ",1),o(w,{block:""},{default:e(()=>[c(s(i.shortcode),1)]),_:1})]),_:1},8,["label"]),o(m,{label:t(a)("academic.program.program")},{default:e(()=>{var n;return[c(s(((n=i==null?void 0:i.program)==null?void 0:n.name)||"-"),1)]}),_:1},8,["label"]),o(m,{label:t(a)("general.created_at")},{default:e(()=>[c(s(i.createdAt.formatted),1)]),_:1},8,["label"]),o(m,{label:t(a)("general.updated_at")},{default:e(()=>[c(s(i.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[o(h,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[c(s(t(a)("global.detail",{attribute:t(a)("academic.division.division")})),1)]),default:e(()=>[i.courses.length>0?(d(),u(N,{key:0,header:P},{default:e(()=>[(d(!0),p(v,null,k(i.courses,n=>(d(),u(j,{key:n.uuid},{default:e(()=>[o(x,{name:"course"},{default:e(()=>[c(s(n.name),1)]),_:2},1024),o(x,{name:"action"})]),_:2},1024))),128))]),_:1})):f("",!0),g("dl",Y,[o(F,{class:"col-span-1 sm:col-span-2",label:t(a)("academic.division.props.description")},{default:e(()=>[c(s(i.description),1)]),_:1},8,["label"])])]),_:1}),t(W)("division:edit")?(d(),u(h,{key:0},{title:e(()=>[c(s(t(a)("global.update",{attribute:t(a)("academic.period.current_period")})),1)]),default:e(()=>[g("div",Z,[(d(!0),p(v,null,k(t(T),n=>(d(),u(M,{key:n.id,size:"md",design:"info",class:"cursor-pointer",onClick:ie=>I(n)},{default:e(()=>[c(s(n.name),1)]),_:2},1032,["onClick"]))),128))]),g("div",ee,[(d(!0),p(v,null,k(i.periodHistory,n=>(d(),p("div",{class:"dark:text-gray-400 text-sm",key:n.id},s(n.name)+" - "+s(n.datetime.formatted),1))),128))])]),_:1})):f("",!0)]),_:1})):f("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ne as default};
