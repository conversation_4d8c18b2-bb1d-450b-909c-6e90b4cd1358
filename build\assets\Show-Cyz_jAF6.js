import{r as i,a as c,o as l,d as _,e as r,i as F,u as z,h as G,l as J,w as t,f as v,q as o,b as u,B as E,s as a,t as n,y as P,F as f,v as B}from"./app-BAwPsakn.js";const K={class:"overflow-hidden bg-black"},Q={name:"CalendarEventCoverImage"},W=Object.assign(Q,{props:{event:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},emits:["refreshItem"],setup($,{emit:h}){const k=h;return(I,y)=>{const e=i("ImageUpload");return l(),c("div",K,[_("div",null,[r(e,{class:"h-32 w-full lg:h-48",disabled:$.disabled,label:I.$trans("calendar.event.props.cover_image"),src:$.event.coverImage,"upload-path":`calendar/events/${$.event.uuid}/assets/cover`,"remove-path":`calendar/events/${$.event.uuid}/assets/cover`,design:"modern","show-label":!1,onUploaded:y[0]||(y[0]=w=>k("refreshItem")),onRemoved:y[1]||(y[1]=w=>k("refreshItem"))},null,8,["disabled","label","src","upload-path","remove-path"])])])}}}),X={class:"flex items-center justify-center gap-2"},Y={class:"text-sm"},Z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},x={class:"mt-8 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ee={class:"space-y-2"},te={class:"flex justify-center gap-2"},ae={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ne={name:"CalendarEventShow"},se=Object.assign(ne,{setup($){F();const h=z(),k=G(),I={},y="calendar/event/",e=J({...I}),w=s=>{Object.assign(e,s)},T=s=>e.audiences.filter(p=>p.type===s);return(s,p)=>{const S=i("PageHeaderAction"),j=i("PageHeader"),b=i("BaseDataView"),V=i("ListMedia"),C=i("BaseCard"),m=i("ListItemView"),g=i("TextMuted"),N=i("ListContainerVertical"),R=i("BaseBadge"),H=i("BaseButton"),M=i("ShowButton"),O=i("DetailLayoutVertical"),U=i("ShowItem"),q=i("ParentTransition");return l(),c(f,null,[r(j,{title:s.$trans(v(h).meta.trans,{attribute:s.$trans(v(h).meta.label)}),navs:[{label:s.$trans("calendar.calendar"),path:"Calendar"},{label:s.$trans("calendar.event.event"),path:"CalendarEventList"}]},{default:t(()=>[r(S,{name:"CalendarEvent",title:s.$trans("calendar.event.event"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(q,{appear:"",visibility:!0},{default:t(()=>[r(U,{"init-url":y,uuid:v(h).params.uuid,onSetItem:w,onRedirectTo:p[2]||(p[2]=d=>v(k).push({name:"CalendarEvent"}))},{default:t(()=>[e.uuid&&v(E)(["student","guardian"],"any")?(l(),o(C,{key:0},{title:t(()=>[_("div",X,[a(n(e.title)+" ",1),_("span",Y,n(e.duration),1)])]),default:t(()=>[_("dl",Z,[r(b,{label:s.$trans("calendar.event.props.type")},{default:t(()=>[a(n(e.type.name),1)]),_:1},8,["label"]),r(b,{label:s.$trans("calendar.event.props.venue")},{default:t(()=>[a(n(e.venue),1)]),_:1},8,["label"]),r(b,{label:s.$trans("general.period"),class:"col-span-1 sm:col-span-2"},{default:t(()=>[a(n(e.durationInDetail),1)]),_:1},8,["label"])]),_("dl",x,[r(b,{class:"col-span-1 sm:col-span-2",html:""},{default:t(()=>[a(n(e.description),1)]),_:1}),e.media.length>0?(l(),o(b,{key:0,class:"col-span-1 sm:col-span-2"},{default:t(()=>[r(V,{media:e.media,url:`/app/calendar/events/${e.uuid}/`},null,8,["media","url"])]),_:1})):u("",!0)])]),_:1})):u("",!0),e.uuid&&!v(E)(["student","guardian"],"any")?(l(),o(O,{key:1},{detail:t(()=>[_("div",ee,[r(C,{"no-padding":"","no-content-padding":""},{title:t(()=>[a(" #"+n(e.codeNumber),1)]),action:t(()=>p[3]||(p[3]=[])),default:t(()=>[r(N,null,{default:t(()=>[r(m,{label:s.$trans("calendar.event.props.title")},{default:t(()=>[a(n(e.title),1)]),_:1},8,["label"]),r(m,{label:s.$trans("calendar.event.props.venue")},{default:t(()=>[a(n(e.venue),1)]),_:1},8,["label"]),r(m,{label:s.$trans("calendar.event.props.start_date")},{default:t(()=>[a(n(e.startDate.formatted)+" ",1),e.startTime.value?(l(),o(g,{key:0,block:""},{default:t(()=>[a(n(e.startTime.formatted),1)]),_:1})):u("",!0)]),_:1},8,["label"]),r(m,{label:s.$trans("calendar.event.props.end_date")},{default:t(()=>{var d;return[a(n(((d=e.endDate)==null?void 0:d.formatted)||"-")+" ",1),e.endTime.value?(l(),o(g,{key:0,block:""},{default:t(()=>[a(n(e.endTime.formatted),1)]),_:1})):u("",!0)]}),_:1},8,["label"]),r(m,{label:s.$trans("employee.incharge.incharge")},{default:t(()=>[(l(!0),c(f,null,B(e.incharges,d=>{var A,D;return l(),c(f,null,[a(n(((A=d.employee)==null?void 0:A.name)||"-")+" ",1),(D=d.employee)!=null&&D.designation?(l(),o(g,{key:0},{default:t(()=>{var L;return[a(n(((L=d.employee)==null?void 0:L.designation)||"-"),1)]}),_:2},1024)):u("",!0)],64)}),256))]),_:1},8,["label"]),!e.isPublic&&e.forAlumni?(l(),o(m,{key:0,label:s.$trans("calendar.event.props.audience")},{default:t(()=>[a(n(s.$trans("calendar.event.props.for_alumni"))+" ",1),(l(!0),c(f,null,B(e.periodDetails,d=>(l(),o(g,{block:""},{default:t(()=>[a(n(d.name),1)]),_:2},1024))),256)),(l(!0),c(f,null,B(e.sessionDetails,d=>(l(),o(g,{block:""},{default:t(()=>[a(n(d.name),1)]),_:2},1024))),256))]),_:1},8,["label"])):u("",!0),!e.isPublic&&!e.forAlumni?(l(),c(f,{key:1},[r(m,{label:s.$trans("calendar.event.props.audience")},{default:t(()=>[a(n(e.studentAudienceType.label)+" ",1),(l(!0),c(f,null,B(T("student"),d=>(l(),o(g,{block:""},{default:t(()=>[a(n(d.name),1)]),_:2},1024))),256))]),_:1},8,["label"]),r(m,{label:s.$trans("calendar.event.props.audience")},{default:t(()=>[e.employeeAudienceType.value?(l(),c(f,{key:0},[a(n(e.employeeAudienceType.label)+" ",1),(l(!0),c(f,null,B(T("employee"),d=>(l(),o(g,{block:""},{default:t(()=>[a(n(d.name),1)]),_:2},1024))),256))],64)):(l(),c(f,{key:1},[a("-")],64))]),_:1},8,["label"]),r(m,{label:s.$trans("general.created_at")},{default:t(()=>[a(n(e.createdAt.formatted),1)]),_:1},8,["label"]),r(m,{label:s.$trans("general.updated_at")},{default:t(()=>[a(n(e.updatedAt.formatted),1)]),_:1},8,["label"])],64)):u("",!0)]),_:1})]),_:1}),v(P)("event:edit")?(l(),o(C,{key:0,"no-padding":"","no-content-padding":""},{title:t(()=>[a(n(s.$trans("calendar.event.props.cover_image")),1)]),default:t(()=>[e.uuid?(l(),o(W,{key:0,event:e,onRefreshItem:p[0]||(p[0]=d=>s.refreshItem=!0)},null,8,["event"])):u("",!0)]),_:1})):u("",!0)])]),default:t(()=>[e.uuid?(l(),o(C,{key:0},{title:t(()=>[_("div",te,[a(n(e.title)+" ",1),e.isPublic?(l(),o(R,{key:0,design:"primary"},{default:t(()=>[a(n(s.$trans("general.public")),1)]),_:1})):u("",!0)])]),footer:t(()=>[r(M,null,{default:t(()=>[v(P)("event:edit")?(l(),o(H,{key:0,design:"primary",onClick:p[1]||(p[1]=d=>v(k).push({name:"CalendarEventEdit",params:{uuid:e.uuid}}))},{default:t(()=>[a(n(s.$trans("general.edit")),1)]),_:1})):u("",!0)]),_:1})]),default:t(()=>[_("dl",ae,[r(b,{class:"col-span-1 sm:col-span-2",html:""},{default:t(()=>[a(n(e.description),1)]),_:1}),e.media.length>0?(l(),o(b,{key:0,class:"col-span-1 sm:col-span-2"},{default:t(()=>[r(V,{media:e.media,url:`/app/calendar/events/${e.uuid}/`},null,8,["media","url"])]),_:1})):u("",!0)])]),_:1})):u("",!0)]),_:1})):u("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{se as default};
