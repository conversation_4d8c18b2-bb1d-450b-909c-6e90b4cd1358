import{u as A,h as M,G as w,H as z,l as k,n as G,r as u,z as J,q as P,o as d,w as b,d as n,e as m,f as a,a as f,b as y,A as U,F as v,s as T,t as F,M as K,J as Q}from"./app-BAwPsakn.js";import{d as W}from"./vuedraggable.umd-BRYqknf6.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"mt-4 grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},se=["onClick"],le={class:"mt-4 grid grid-cols-5 gap-4"},re={class:"col-span-5 sm:col-span-1"},ne={class:"col-span-5 sm:col-span-1"},ie=["onClick"],me={key:0},pe={key:1},de={class:"col-span-5 sm:col-span-1"},ue=["onClick"],ce={key:0},fe={key:1},be={class:"col-span-5 sm:col-span-1"},_e={class:"col-span-5 sm:col-span-1"},Ve={class:"col-span-5 sm:col-span-1"},ye={class:"col-span-5 sm:col-span-1"},ge={key:2,class:"col-span-5 sm:col-span-2"},$e={class:"mt-4"},Ue={class:"mt-4 grid grid-cols-3 gap-6"},Fe={class:"col-span-3"},ve={class:"col-span-3 sm:col-span-1"},he={class:"col-span-3 sm:col-span-1"},Ce={key:1,class:"col-span-3"},ke={name:"AcademicCertificateTemplateForm"},Te=Object.assign(ke,{setup(S){const _=A();M();const c={name:"",numberPrefix:"",numberDigit:"",numberSuffix:"",type:"",for:{},customFields:[],hasCustomTemplateFile:!1,customTemplateFileName:"",hasCustomHeader:!1,content:""},h={uuid:w(),type:"",name:"",label:"",minLength:"",maxLength:"",minValue:"",maxValue:"",defaultValue:"",options:"",showLabel:!1,isRequired:!1},g="academic/certificateTemplate/",o=z(g),V=k({types:[],for:[],customFieldTypes:[]}),r=k({...c}),q=s=>{Object.assign(V,s)},D=k({isLoaded:!_.params.uuid}),L=()=>{r.customFields.push({...h,uuid:w()})},H=async s=>{await K()&&(r.customFields.length==1?r.customFields=[h]:r.customFields.splice(s,1))},R=s=>{Object.assign(c,{...s,type:s.type.value}),Object.assign(r,Q(c)),D.isLoaded=!0};return G(async()=>{_.params.uuid||L()}),(s,t)=>{const p=u("BaseInput"),C=u("BaseSelect"),N=u("BaseFieldset"),j=u("BaseBadge"),B=u("BaseSwitch"),E=u("BaseEditor"),O=u("FormInfo"),I=u("FormAction"),$=J("tooltip");return d(),P(I,{"pre-requisites":!0,onSetPreRequisites:q,"init-url":g,"init-form":c,form:r,setForm:R,redirect:"AcademicCertificateTemplate"},{default:b(()=>[n("div",X,[n("div",Y,[m(p,{type:"text",modelValue:r.name,"onUpdate:modelValue":t[0]||(t[0]=e=>r.name=e),name:"name",label:s.$trans("academic.certificate.template.props.name"),error:a(o).name,"onUpdate:error":t[1]||(t[1]=e=>a(o).name=e),autofocus:""},null,8,["modelValue","label","error"])]),n("div",Z,[m(C,{modelValue:r.type,"onUpdate:modelValue":t[2]||(t[2]=e=>r.type=e),name:"type",label:s.$trans("academic.certificate.template.props.type"),options:V.types,error:a(o).type,"onUpdate:error":t[3]||(t[3]=e=>a(o).type=e)},null,8,["modelValue","label","options","error"])]),n("div",x,[m(C,{modelValue:r.for,"onUpdate:modelValue":t[4]||(t[4]=e=>r.for=e),name:"for",label:s.$trans("academic.certificate.template.props.for"),options:V.for,"object-prop":!0,error:a(o).for,"onUpdate:error":t[5]||(t[5]=e=>a(o).for=e)},null,8,["modelValue","label","options","error"])])]),n("div",ee,[n("div",te,[m(p,{type:"text",modelValue:r.numberPrefix,"onUpdate:modelValue":t[6]||(t[6]=e=>r.numberPrefix=e),name:"numberPrefix",label:s.$trans("academic.certificate.template.props.number_prefix"),error:a(o).numberPrefix,"onUpdate:error":t[7]||(t[7]=e=>a(o).numberPrefix=e)},null,8,["modelValue","label","error"])]),n("div",ae,[m(p,{type:"number",modelValue:r.numberDigit,"onUpdate:modelValue":t[8]||(t[8]=e=>r.numberDigit=e),name:"numberDigit",label:s.$trans("academic.certificate.template.props.number_digit"),error:a(o).numberDigit,"onUpdate:error":t[9]||(t[9]=e=>a(o).numberDigit=e)},null,8,["modelValue","label","error"])]),n("div",oe,[m(p,{type:"text",modelValue:r.numberSuffix,"onUpdate:modelValue":t[10]||(t[10]=e=>r.numberSuffix=e),name:"numberSuffix",label:s.$trans("academic.certificate.template.props.number_suffix"),error:a(o).numberSuffix,"onUpdate:error":t[11]||(t[11]=e=>a(o).numberSuffix=e)},null,8,["modelValue","label","error"])])]),m(a(W),{list:r.customFields,"item-key":"uuid"},{item:b(({element:e,index:i})=>[m(N,{class:"mt-4"},{legend:b(()=>[t[21]||(t[21]=n("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),T(" "+F(s.$trans("academic.certificate.template.props.custom_field"))+" "+F(i+1)+". ",1),n("span",{class:"text-danger ml-2 cursor-pointer",onClick:l=>H(i)},t[20]||(t[20]=[n("i",{class:"fas fa-times-circle"},null,-1)]),8,se)]),default:b(()=>[n("div",le,[n("div",re,[m(C,{name:`custom_fields.${i}.type`,label:s.$trans("global.select",{attribute:s.$trans("custom_field.props.type")}),modelValue:e.type,"onUpdate:modelValue":l=>e.type=l,error:a(o)[`customFields.${i}.type`],"onUpdate:error":l=>a(o)[`customFields.${i}.type`]=l,options:V.customFieldTypes},null,8,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","options"])]),n("div",ne,[m(p,{type:"text",modelValue:e.name,"onUpdate:modelValue":l=>e.name=l,name:`custom_fields.${i}.name`,label:s.$trans("custom_field.props.name"),error:a(o)[`customFields.${i}.name`],"onUpdate:error":l=>a(o)[`customFields.${i}.name`]=l},{"additional-label":b(()=>[n("span",{class:"cursor-pointer",onClick:l=>e.isRequired=!e.isRequired},[e.isRequired?U((d(),f("span",me,t[22]||(t[22]=[n("i",{class:"fas fa-check-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[$,s.$trans("custom_field.props.is_required")]]):U((d(),f("span",pe,t[23]||(t[23]=[n("i",{class:"fas fa-question-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[$,s.$trans("custom_field.props.is_optional")]])],8,ie)]),_:2},1032,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),n("div",de,[m(p,{type:"text",modelValue:e.label,"onUpdate:modelValue":l=>e.label=l,name:`custom_fields.${i}.label`,label:s.$trans("custom_field.props.label"),error:a(o)[`customFields.${i}.label`],"onUpdate:error":l=>a(o)[`customFields.${i}.label`]=l},{"additional-label":b(()=>[n("span",{class:"cursor-pointer",onClick:l=>e.showLabel=!e.showLabel},[e.showLabel?U((d(),f("span",ce,t[24]||(t[24]=[n("i",{class:"fas fa-eye text-gray-900 dark:text-gray-300"},null,-1)]))),[[$,s.$trans("academic.certificate.template.props.show_label")]]):U((d(),f("span",fe,t[25]||(t[25]=[n("i",{class:"fas fa-eye-slash text-gray-900 dark:text-gray-300"},null,-1)]))),[[$,s.$trans("academic.certificate.template.props.hide_label")]])],8,ue)]),_:2},1032,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),e.type=="text_input"||e.type=="multi_line_text_input"?(d(),f(v,{key:0},[n("div",be,[m(p,{type:"text",modelValue:e.minLength,"onUpdate:modelValue":l=>e.minLength=l,name:`custom_fields.${i}.minLength`,label:s.$trans("custom_field.props.min_length"),error:a(o)[`customFields.${i}.minLength`],"onUpdate:error":l=>a(o)[`customFields.${i}.minLength`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),n("div",_e,[m(p,{type:"text",modelValue:e.maxLength,"onUpdate:modelValue":l=>e.maxLength=l,name:`custom_fields.${i}.maxLength`,label:s.$trans("custom_field.props.max_length"),error:a(o)[`customFields.${i}.maxLength`],"onUpdate:error":l=>a(o)[`customFields.${i}.maxLength`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])],64)):y("",!0),e.type=="number_input"||e.type=="currency_input"?(d(),f(v,{key:1},[n("div",Ve,[m(p,{type:"number",modelValue:e.minValue,"onUpdate:modelValue":l=>e.minValue=l,name:`custom_fields.${i}.minValue`,label:s.$trans("custom_field.props.min_value"),error:a(o)[`customFields.${i}.minValue`],"onUpdate:error":l=>a(o)[`customFields.${i}.minValue`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),n("div",ye,[m(p,{type:"number",modelValue:e.maxValue,"onUpdate:modelValue":l=>e.maxValue=l,name:`custom_fields.${i}.maxValue`,label:s.$trans("custom_field.props.max_value"),error:a(o)[`customFields.${i}.maxValue`],"onUpdate:error":l=>a(o)[`customFields.${i}.maxValue`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])],64)):y("",!0),e.type=="select_input"||e.type=="multi_select_input"||e.type=="checkbox_input"||e.type=="radio_input"?(d(),f("div",ge,[m(p,{type:"text",modelValue:e.options,"onUpdate:modelValue":l=>e.options=l,name:`custom_fields.${i}.options`,label:s.$trans("custom_field.props.options"),error:a(o)[`customFields.${i}.options`],"onUpdate:error":l=>a(o)[`customFields.${i}.options`]=l,"label-hint":s.$trans("custom_field.option_info")},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","label-hint"])])):y("",!0)])]),_:2},1024)]),_:1},8,["list"]),n("div",$e,[m(j,{design:"primary",onClick:L,class:"cursor-pointer"},{default:b(()=>[T(F(s.$trans("global.add",{attribute:s.$trans("custom_field.custom_field")})),1)]),_:1})]),n("div",Ue,[n("div",Fe,[m(B,{vertical:"",modelValue:r.hasCustomTemplateFile,"onUpdate:modelValue":t[12]||(t[12]=e=>r.hasCustomTemplateFile=e),name:"hasCustomTemplateFile",label:s.$trans("academic.certificate.template.props.has_custom_template_file"),error:a(o).hasCustomTemplateFile,"onUpdate:error":t[13]||(t[13]=e=>a(o).hasCustomTemplateFile=e)},null,8,["modelValue","label","error"])]),r.hasCustomTemplateFile?(d(),f(v,{key:0},[n("div",ve,[m(p,{type:"text",modelValue:r.customTemplateFileName,"onUpdate:modelValue":t[14]||(t[14]=e=>r.customTemplateFileName=e),name:"customTemplateFileName",label:s.$trans("academic.certificate.template.props.custom_template_file_name"),error:a(o).customTemplateFileName,"onUpdate:error":t[15]||(t[15]=e=>a(o).customTemplateFileName=e)},null,8,["modelValue","label","error"])]),n("div",he,[m(B,{vertical:"",modelValue:r.hasCustomHeader,"onUpdate:modelValue":t[16]||(t[16]=e=>r.hasCustomHeader=e),name:"hasCustomHeader",label:s.$trans("academic.certificate.template.props.has_custom_header"),error:a(o).hasCustomHeader,"onUpdate:error":t[17]||(t[17]=e=>a(o).hasCustomHeader=e)},null,8,["modelValue","label","error"])])],64)):y("",!0),r.hasCustomTemplateFile?y("",!0):(d(),f("div",Ce,[m(E,{modelValue:r.content,"onUpdate:modelValue":t[18]||(t[18]=e=>r.content=e),name:"content",edit:!!a(_).params.uuid,label:s.$trans("academic.certificate.template.props.content"),error:a(o).content,"onUpdate:error":t[19]||(t[19]=e=>a(o).content=e)},null,8,["modelValue","edit","label","error"]),r.for?(d(),P(O,{key:0},{default:b(()=>[T(F(r.for.variables),1)]),_:1})):y("",!0)]))])]),_:1},8,["form"])}}}),Le={name:"AcademicCertificateTemplateAction"},Pe=Object.assign(Le,{setup(S){const _=A();return(c,h)=>{const g=u("PageHeaderAction"),o=u("PageHeader"),V=u("ParentTransition");return d(),f(v,null,[m(o,{title:c.$trans(a(_).meta.trans,{attribute:c.$trans(a(_).meta.label)}),navs:[{label:c.$trans("academic.academic"),path:"Academic"},{label:c.$trans("academic.certificate.template.template"),path:"AcademicCertificateTemplateList"}]},{default:b(()=>[m(g,{name:"AcademicCertificateTemplate",title:c.$trans("academic.certificate.template.template"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),m(V,{appear:"",visibility:!0},{default:b(()=>[m(Te)]),_:1})],64)}}});export{Pe as default};
