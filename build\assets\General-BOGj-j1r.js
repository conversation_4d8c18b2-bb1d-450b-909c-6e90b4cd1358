import{u as F,j as T,H as E,c as Q,l as y,r as u,a as p,o as f,e as l,f as r,w as d,d as s,s as D,t as b,b as _,F as A}from"./app-BAwPsakn.js";const B={class:"grid grid-cols-3 gap-4"},I={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-2 sm:col-span-1"},h={class:"mt-4 grid grid-cols-3 gap-4"},ee={class:"col-span-3 sm:col-span-1"},te={class:"dark:text-gray-400"},re={class:"mt-4 grid grid-cols-3 gap-4"},oe={class:"col-span-3 sm:col-span-1"},ne={key:0,class:"col-span-3 sm:col-span-1"},le={key:1,class:"col-span-3 sm:col-span-1"},ae={name:"StudentConfigGeneral"},ue=Object.assign(ae,{setup(se){const P=F(),a=T("$trans"),g="config/",n=E(g),S=Q(()=>a("global.placeholder_info",{attribute:x.datePlaceholders})),x=y({datePlaceholders:""}),N={registrationNumberPrefix:"",registrationNumberSuffix:"",registrationNumberDigit:0,admissionNumberPrefix:"",admissionNumberSuffix:"",admissionNumberDigit:0,transferRequestNumberPrefix:"",transferRequestNumberSuffix:"",transferRequestNumberDigit:0,transferNumberPrefix:"",transferNumberSuffix:"",transferNumberDigit:0,attendancePastDayLimit:0,allowStudentToSubmitContactEditRequest:!1,lateFeeWaiverTillDate:"",allowFlexibleInstallmentPayment:!1,enableQrCodeAttendance:!1,useDynamicQrCode:!1,qrCodeExpiryDuration:"",type:"student"},o=y({...N}),q=c=>{Object.assign(x,{datePlaceholders:c.datePlaceholders.map(e=>e.value).join(", ")})};return(c,e)=>{const v=u("PageHeader"),i=u("BaseInput"),U=u("BaseAlert"),m=u("BaseSwitch"),C=u("DatePicker"),V=u("HelperText"),R=u("FormAction"),w=u("ParentTransition");return f(),p(A,null,[l(v,{title:r(a)(r(P).meta.label),navs:[{label:r(a)("student.student"),path:"Student"}]},null,8,["title","navs"]),l(w,{appear:"",visibility:!0},{default:d(()=>[l(R,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:q,"init-url":g,"data-fetch":"student","init-form":N,form:o,action:"store","stay-on":"",redirect:"Student"},{default:d(()=>[s("div",B,[s("div",I,[l(i,{type:"text",modelValue:o.registrationNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=t=>o.registrationNumberPrefix=t),name:"registrationNumberPrefix",label:r(a)("student.registration.config.props.number_prefix"),error:r(n).registrationNumberPrefix,"onUpdate:error":e[1]||(e[1]=t=>r(n).registrationNumberPrefix=t)},null,8,["modelValue","label","error"])]),s("div",k,[l(i,{type:"number",modelValue:o.registrationNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=t=>o.registrationNumberDigit=t),name:"registrationNumberDigit",label:r(a)("student.registration.config.props.number_digit"),error:r(n).registrationNumberDigit,"onUpdate:error":e[3]||(e[3]=t=>r(n).registrationNumberDigit=t)},null,8,["modelValue","label","error"])]),s("div",L,[l(i,{type:"text",modelValue:o.registrationNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=t=>o.registrationNumberSuffix=t),name:"registrationNumberSuffix",label:r(a)("student.registration.config.props.number_suffix"),error:r(n).registrationNumberSuffix,"onUpdate:error":e[5]||(e[5]=t=>r(n).registrationNumberSuffix=t)},null,8,["modelValue","label","error"])]),s("div",W,[l(i,{type:"text",modelValue:o.admissionNumberPrefix,"onUpdate:modelValue":e[6]||(e[6]=t=>o.admissionNumberPrefix=t),name:"admissionNumberPrefix",label:r(a)("student.config.props.number_prefix"),error:r(n).admissionNumberPrefix,"onUpdate:error":e[7]||(e[7]=t=>r(n).admissionNumberPrefix=t)},null,8,["modelValue","label","error"])]),s("div",j,[l(i,{type:"number",modelValue:o.admissionNumberDigit,"onUpdate:modelValue":e[8]||(e[8]=t=>o.admissionNumberDigit=t),name:"admissionNumberDigit",label:r(a)("student.config.props.number_digit"),error:r(n).admissionNumberDigit,"onUpdate:error":e[9]||(e[9]=t=>r(n).admissionNumberDigit=t)},null,8,["modelValue","label","error"])]),s("div",H,[l(i,{type:"text",modelValue:o.admissionNumberSuffix,"onUpdate:modelValue":e[10]||(e[10]=t=>o.admissionNumberSuffix=t),name:"admissionNumberSuffix",label:r(a)("student.config.props.number_suffix"),error:r(n).admissionNumberSuffix,"onUpdate:error":e[11]||(e[11]=t=>r(n).admissionNumberSuffix=t)},null,8,["modelValue","label","error"])]),s("div",O,[l(i,{type:"text",modelValue:o.transferRequestNumberPrefix,"onUpdate:modelValue":e[12]||(e[12]=t=>o.transferRequestNumberPrefix=t),name:"transferRequestNumberPrefix",label:r(a)("student.transfer_request.config.props.number_prefix"),error:r(n).transferRequestNumberPrefix,"onUpdate:error":e[13]||(e[13]=t=>r(n).transferRequestNumberPrefix=t)},null,8,["modelValue","label","error"])]),s("div",$,[l(i,{type:"number",modelValue:o.transferRequestNumberDigit,"onUpdate:modelValue":e[14]||(e[14]=t=>o.transferRequestNumberDigit=t),name:"transferRequestNumberDigit",label:r(a)("student.transfer_request.config.props.number_digit"),error:r(n).transferRequestNumberDigit,"onUpdate:error":e[15]||(e[15]=t=>r(n).transferRequestNumberDigit=t)},null,8,["modelValue","label","error"])]),s("div",z,[l(i,{type:"text",modelValue:o.transferRequestNumberSuffix,"onUpdate:modelValue":e[16]||(e[16]=t=>o.transferRequestNumberSuffix=t),name:"transferRequestNumberSuffix",label:r(a)("student.transfer_request.config.props.number_suffix"),error:r(n).transferRequestNumberSuffix,"onUpdate:error":e[17]||(e[17]=t=>r(n).transferRequestNumberSuffix=t)},null,8,["modelValue","label","error"])]),s("div",G,[l(i,{type:"text",modelValue:o.transferNumberPrefix,"onUpdate:modelValue":e[18]||(e[18]=t=>o.transferNumberPrefix=t),name:"transferNumberPrefix",label:r(a)("student.transfer.config.props.number_prefix"),error:r(n).transferNumberPrefix,"onUpdate:error":e[19]||(e[19]=t=>r(n).transferNumberPrefix=t)},null,8,["modelValue","label","error"])]),s("div",J,[l(i,{type:"number",modelValue:o.transferNumberDigit,"onUpdate:modelValue":e[20]||(e[20]=t=>o.transferNumberDigit=t),name:"transferNumberDigit",label:r(a)("student.transfer.config.props.number_digit"),error:r(n).transferNumberDigit,"onUpdate:error":e[21]||(e[21]=t=>r(n).transferNumberDigit=t)},null,8,["modelValue","label","error"])]),s("div",K,[l(i,{type:"text",modelValue:o.transferNumberSuffix,"onUpdate:modelValue":e[22]||(e[22]=t=>o.transferNumberSuffix=t),name:"transferNumberSuffix",label:r(a)("student.transfer.config.props.number_suffix"),error:r(n).transferNumberSuffix,"onUpdate:error":e[23]||(e[23]=t=>r(n).transferNumberSuffix=t)},null,8,["modelValue","label","error"])]),s("div",M,[l(U,{size:"xs",design:"info"},{default:d(()=>[D(b(S.value),1)]),_:1})]),s("div",X,[l(i,{type:"text",modelValue:o.attendancePastDayLimit,"onUpdate:modelValue":e[24]||(e[24]=t=>o.attendancePastDayLimit=t),name:"attendancePastDayLimit",label:r(a)("student.config.props.attendance_past_day_limit"),error:r(n).attendancePastDayLimit,"onUpdate:error":e[25]||(e[25]=t=>r(n).attendancePastDayLimit=t)},null,8,["modelValue","label","error"])]),s("div",Y,[l(m,{vertical:"",modelValue:o.allowStudentToSubmitContactEditRequest,"onUpdate:modelValue":e[26]||(e[26]=t=>o.allowStudentToSubmitContactEditRequest=t),name:"allowStudentToSubmitContactEditRequest",label:r(a)("student.config.props.allow_student_to_submit_contact_edit_request"),error:r(n).allowStudentToSubmitContactEditRequest,"onUpdate:error":e[27]||(e[27]=t=>r(n).allowStudentToSubmitContactEditRequest=t)},null,8,["modelValue","label","error"])]),s("div",Z,[l(C,{modelValue:o.lateFeeWaiverTillDate,"onUpdate:modelValue":e[28]||(e[28]=t=>o.lateFeeWaiverTillDate=t),name:"lateFeeWaiverTillDate",label:r(a)("student.config.props.late_fee_waiver_till_date"),error:r(n).lateFeeWaiverTillDate,"onUpdate:error":e[29]||(e[29]=t=>r(n).lateFeeWaiverTillDate=t)},null,8,["modelValue","label","error"])])]),s("div",h,[s("div",ee,[l(m,{vertical:"",modelValue:o.allowFlexibleInstallmentPayment,"onUpdate:modelValue":e[30]||(e[30]=t=>o.allowFlexibleInstallmentPayment=t),name:"allowFlexibleInstallmentPayment",label:r(a)("student.config.props.allow_flexible_installment_payment"),error:r(n).allowFlexibleInstallmentPayment,"onUpdate:error":e[31]||(e[31]=t=>r(n).allowFlexibleInstallmentPayment=t)},null,8,["modelValue","label","error"]),l(V,null,{default:d(()=>[s("span",te,b(r(a)("student.config.props.allow_flexible_installment_payment_info")),1)]),_:1})])]),s("div",re,[s("div",oe,[l(m,{vertical:"",modelValue:o.enableQrCodeAttendance,"onUpdate:modelValue":e[32]||(e[32]=t=>o.enableQrCodeAttendance=t),name:"enableQrCodeAttendance",label:r(a)("student.config.props.enable_qr_code_attendance"),error:r(n).enableQrCodeAttendance,"onUpdate:error":e[33]||(e[33]=t=>r(n).enableQrCodeAttendance=t)},null,8,["modelValue","label","error"])]),o.enableQrCodeAttendance?(f(),p("div",ne,[l(m,{vertical:"",modelValue:o.useDynamicQrCode,"onUpdate:modelValue":e[34]||(e[34]=t=>o.useDynamicQrCode=t),name:"useDynamicQrCode",label:r(a)("student.config.props.use_dynamic_qr_code"),error:r(n).useDynamicQrCode,"onUpdate:error":e[35]||(e[35]=t=>r(n).useDynamicQrCode=t)},null,8,["modelValue","label","error"]),l(V,null,{default:d(()=>[D(b(r(a)("student.config.props.dynamic_qr_code_tip")),1)]),_:1})])):_("",!0),o.enableQrCodeAttendance&&o.useDynamicQrCode?(f(),p("div",le,[l(i,{type:"text",modelValue:o.qrCodeExpiryDuration,"onUpdate:modelValue":e[36]||(e[36]=t=>o.qrCodeExpiryDuration=t),name:"qrCodeExpiryDuration",label:r(a)("student.config.props.qr_code_expiry_duration"),error:r(n).qrCodeExpiryDuration,"onUpdate:error":e[37]||(e[37]=t=>r(n).qrCodeExpiryDuration=t)},null,8,["modelValue","label","error"])])):_("",!0)])]),_:1},8,["form"])]),_:1})],64)}}});export{ue as default};
