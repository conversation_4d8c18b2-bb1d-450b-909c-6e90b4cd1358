import{u as A,i as j,m as E,l as O,n as W,r as B,z as G,a as o,o as r,d as n,b as l,t as c,A as f,a4 as L,a5 as T,f as g,F as p,v as F,s as m,q as b,w,h as J,e as P,x as V}from"./app-BAwPsakn.js";import{_ as S}from"./lodash-CyHJH6Xs.js";const X={class:"p-4 border-b border-gray-200 dark:border-gray-700"},Y={class:"flex justify-between items-center"},Z={class:"text-xl font-semibold dark:text-white"},ee={key:0,class:"relative mt-4"},te=["placeholder"],se={key:1,class:"relative mt-4"},ae=["placeholder"],re=["onClick"],oe={class:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"},ne={class:"flex items-center"},le=["src","alt"],ce={class:"font-medium dark:text-white"},de={key:0,class:"fas fa-graduation-cap"},ie={key:1,class:"fas fa-user-tie"},ue={key:0,class:"px-2 py-1 bg-primary text-white text-xs rounded-full"},he={key:0},ye={class:"p-4 text-sm text-gray-500 dark:text-gray-400"},fe={key:1,class:"p-4 text-center"},ge={class:"text-gray-500 dark:text-gray-400"},pe=["onClick"],me={class:"flex items-center p-4 border-b border-gray-200 dark:border-gray-700"},_e=["src","alt"],ke={class:"font-medium dark:text-white"},xe={key:0,class:"fas fa-graduation-cap"},Ce={key:1,class:"fas fa-user-tie"},be={key:0},ve={class:"p-4 text-sm text-gray-500 dark:text-gray-400"},we={name:"ChatUser"},Se=Object.assign(we,{emits:["onSelectChat"],setup(N,{emit:_}){const d=_,k=A(),y=j(),i=E(null),e=O({chats:[],selectedChat:null,userSearchQuery:"",chatSearchQuery:"",searchedUsers:[],showUserSearch:!1,isLoading:!1,nextCursor:null,hasMoreChats:!0}),u=async(a=null)=>{if(!(!e.hasMoreChats&&a)){e.isLoading=!0;try{const t=await y.dispatch("chat/getChats",{cursor:a,search:e.chatSearchQuery});a?e.chats.push(...t.data):e.chats=t.data,e.nextCursor=t.meta.nextCursor,e.hasMoreChats=!!t.meta.nextCursor}catch(t){console.error("Failed to get chats:",t)}finally{e.isLoading=!1}}},U=S.debounce(()=>{if(!i.value)return;const{scrollTop:a,scrollHeight:t,clientHeight:h}=i.value;a+h>=t-100&&!e.isLoading&&e.hasMoreChats&&u(e.nextCursor)},200),x=async a=>{await y.dispatch("chat/getChat",a).then(t=>{e.selectedChat=t,d("onSelectChat",e.selectedChat)}).catch(t=>{console.error("Failed to get chat:",t)}),e.isChatOpen=!0},Q=()=>{e.showUserSearch=!e.showUserSearch,e.userSearchQuery="",e.searchedUsers=[]},v=()=>{e.showUserSearch=!1,e.userSearchQuery="",e.searchedUsers=[]},D=()=>{e.chatSearchQuery="",u()},K=async a=>{a.unreadCount=0,await x(a.uuid)},q=async()=>{if(e.chatSearchQuery="",e.userSearchQuery.trim())try{const a=await y.dispatch("chat/searchUsers",e.userSearchQuery);e.searchedUsers=a}catch(a){console.error("Failed to search users:",a)}else e.searchedUsers=[]},z=async a=>{try{const t=await y.dispatch("chat/createChat",{name:a.name,is_group_chat:!1,participants:[a.uuid]});if(e.chats.find(h=>h.uuid==t.uuid)){await x(t.uuid),v();return}e.chats.unshift({uuid:t.uuid,name:a.name,avatar:a.avatar}),await x(t.uuid),v()}catch(t){console.error("Failed to start chat with user:",t)}},H=()=>{e.chatSearchQuery="",u()},I=()=>{e.userSearchQuery="",e.searchedUsers=[]},$=S.debounce(()=>{e.chats=[],e.nextCursor=null,e.hasMoreChats=!0,u()},300),M=S.debounce(q,300);return W(async()=>{await u(),k.params.uuid&&await x(k.params.uuid)}),(a,t)=>{const h=B("TextMuted"),C=G("tooltip");return r(),o(p,null,[n("div",X,[n("div",Y,[n("div",Z,c(a.$trans("chat.chats")),1),e.showUserSearch?l("",!0):(r(),o("button",{key:0,onClick:Q,class:"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"},t[5]||(t[5]=[n("i",{class:"fas fa-pen-to-square fa-lg"},null,-1)]))),e.showUserSearch?(r(),o("button",{key:1,onClick:Q,class:"text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100"},t[6]||(t[6]=[n("i",{class:"fas fa-times fa-lg"},null,-1)]))):l("",!0)]),e.showUserSearch?(r(),o("div",se,[f(n("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>e.userSearchQuery=s),type:"text",placeholder:a.$trans("chat.search_users"),class:"w-full border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-body dark:text-white",onInput:t[3]||(t[3]=(...s)=>g(M)&&g(M)(...s)),onKeydown:T(v,["esc"])},null,40,ae),[[L,e.userSearchQuery]]),e.userSearchQuery?(r(),o("button",{key:0,onClick:I,class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200",type:"button"},t[8]||(t[8]=[n("i",{class:"fas fa-times"},null,-1)]))):l("",!0)])):(r(),o("div",ee,[f(n("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>e.chatSearchQuery=s),type:"text",placeholder:a.$trans("chat.search_chats"),class:"w-full border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-dark-body dark:text-white",onInput:t[1]||(t[1]=(...s)=>g($)&&g($)(...s)),onKeydown:T(D,["esc"])},null,40,te),[[L,e.chatSearchQuery]]),e.chatSearchQuery?(r(),o("button",{key:0,onClick:H,class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200",type:"button"},t[7]||(t[7]=[n("i",{class:"fas fa-times"},null,-1)]))):l("",!0)]))]),n("ul",{class:"flex-1 scroller-thin-y scroller-hidden h-0",ref_key:"chatList",ref:i,onScroll:t[4]||(t[4]=(...s)=>g(U)&&g(U)(...s))},[e.showUserSearch?(r(),o(p,{key:1},[(r(!0),o(p,null,F(e.searchedUsers,s=>(r(),o("li",{key:s.id,onClick:R=>z(s),class:"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"},[n("div",me,[n("img",{src:s.avatar,alt:s.name,class:"w-10 h-10 rounded-full mr-3"},null,8,_e),n("div",ke,[m(c(s.name)+" ",1),s.detail?(r(),b(h,{key:0,block:""},{default:w(()=>[s.userType=="student"?f((r(),o("i",xe,null,512)),[[C,a.$trans("student.student")]]):l("",!0),s.userType=="employee"?f((r(),o("i",Ce,null,512)),[[C,a.$trans("employee.employee")]]):l("",!0),m(" "+c(s.detail),1)]),_:2},1024)):l("",!0)])])],8,pe))),128)),e.searchedUsers.length===0&&e.userSearchQuery?(r(),o("li",be,[n("div",ve,c(a.$trans("chat.no_users_found")),1)])):l("",!0)],64)):(r(),o(p,{key:0},[(r(!0),o(p,null,F(e.chats,s=>(r(),o("li",{key:s.uuid,onClick:R=>K(s),class:"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"},[n("div",oe,[n("div",ne,[n("img",{src:s.avatar,alt:s.name,class:"w-10 h-10 rounded-full mr-3"},null,8,le),n("div",ce,[m(c(s.name)+" ",1),s.recipient?(r(),b(h,{key:0,block:""},{default:w(()=>[s.recipient.type=="student"?f((r(),o("i",de,null,512)),[[C,a.$trans("student.student")]]):l("",!0),s.recipient.type=="employee"?f((r(),o("i",ie,null,512)),[[C,a.$trans("employee.employee")]]):l("",!0),m(" "+c(s.recipient.detail),1)]),_:2},1024)):l("",!0),s.latestMessage?(r(),b(h,{key:1,class:"mt-1",block:""},{default:w(()=>[m(c(s.latestMessage.content),1)]),_:2},1024)):l("",!0)])]),n("div",null,[s.unreadCount>0?(r(),o("span",ue,c(s.unreadCount),1)):l("",!0)])])],8,re))),128)),e.chats.length===0&&e.chatSearchQuery?(r(),o("li",he,[n("div",ye,c(a.$trans("chat.no_chats_found")),1)])):l("",!0),e.isLoading?(r(),o("li",fe,[n("span",ge,c(a.$trans("chat.loading")),1)])):l("",!0)],64))],544)],64)}}}),Ue={class:"flex flex-col md:flex-row h-[calc(100vh_-_100px)] bg-gray-100 dark:bg-dark-header"},Qe={key:0,class:"h-full flex items-center justify-center text-gray-500"},$e={name:"Chat"},Te=Object.assign($e,{setup(N){const _=J();j();const d=O({selectedChat:null}),k=i=>{d.selectedChat=i,_.push({name:"ChatMessage",params:{uuid:i.uuid}})},y=()=>{d.selectedChat=null,_.push({name:"Chat"})};return(i,e)=>{const u=B("router-view");return r(),o("div",Ue,[n("div",{class:V(["w-full md:w-1/4 bg-white dark:bg-dark-header border-r border-gray-200 dark:border-gray-700 flex flex-col",{"hidden md:flex":d.selectedChat}])},[P(Se,{onOnSelectChat:k})],2),n("div",{class:V(["flex-1 flex flex-col",{"hidden md:flex":!d.selectedChat}])},[d.selectedChat?l("",!0):(r(),o("div",Qe,c(i.$trans("chat.select_chat_tip")),1)),d.selectedChat?(r(),b(u,{key:1,chat:d.selectedChat,onOnCloseChat:y},null,8,["chat"])):l("",!0)],2)])}}});export{Te as default};
