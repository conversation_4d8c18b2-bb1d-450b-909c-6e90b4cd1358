import{u as V,H as f,l as g,r as t,a as v,o as R,e as i,f as n,w as p,d as u,F}from"./app-BAwPsakn.js";const L={class:"grid grid-cols-3 gap-4"},c={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},y={class:"col-span-3 sm:col-span-1"},T={class:"mt-4 grid grid-cols-3 gap-4"},_={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},j={name:"ContactConfigGeneral"},z=Object.assign(j,{setup(G){const q=V(),m="config/",a=f(m),b={enableMiddleNameField:!1,enableThirdNameField:!1,uniqueIdNumber1Label:"",uniqueIdNumber2Label:"",uniqueIdNumber3Label:"",uniqueIdNumber4Label:"",uniqueIdNumber5Label:"",isUniqueIdNumber1Required:!1,isUniqueIdNumber2Required:!1,isUniqueIdNumber3Required:!1,isUniqueIdNumber4Required:!1,isUniqueIdNumber5Required:!1,enableCategoryField:!1,enableCasteField:!1,type:"contact"},r=g({...b});return(o,e)=>{const N=t("PageHeader"),d=t("BaseSwitch"),s=t("BaseInput"),I=t("FormAction"),U=t("ParentTransition");return R(),v(F,null,[i(N,{title:o.$trans(n(q).meta.label),navs:[{label:o.$trans("contact.contact"),path:"Contact"}]},null,8,["title","navs"]),i(U,{appear:"",visibility:!0},{default:p(()=>[i(I,{"init-url":m,"data-fetch":"contact","init-form":b,form:r,action:"store","stay-on":"",redirect:"Contact"},{default:p(()=>[u("div",L,[u("div",c,[i(d,{vertical:"",modelValue:r.enableMiddleNameField,"onUpdate:modelValue":e[0]||(e[0]=l=>r.enableMiddleNameField=l),name:"enableMiddleNameField",label:o.$trans("contact.config.props.enable_middle_name_field"),error:n(a).enableMiddleNameField,"onUpdate:error":e[1]||(e[1]=l=>n(a).enableMiddleNameField=l)},null,8,["modelValue","label","error"])]),u("div",C,[i(d,{vertical:"",modelValue:r.enableThirdNameField,"onUpdate:modelValue":e[2]||(e[2]=l=>r.enableThirdNameField=l),name:"enableThirdNameField",label:o.$trans("contact.config.props.enable_third_name_field"),error:n(a).enableThirdNameField,"onUpdate:error":e[3]||(e[3]=l=>n(a).enableThirdNameField=l)},null,8,["modelValue","label","error"])]),u("div",$,[i(d,{vertical:"",modelValue:r.enableCategoryField,"onUpdate:modelValue":e[4]||(e[4]=l=>r.enableCategoryField=l),name:"enableCategoryField",label:o.$trans("global.enable",{attribute:o.$trans("contact.category.category")}),error:n(a).enableCategoryField,"onUpdate:error":e[5]||(e[5]=l=>n(a).enableCategoryField=l)},null,8,["modelValue","label","error"])]),u("div",y,[i(d,{vertical:"",modelValue:r.enableCasteField,"onUpdate:modelValue":e[6]||(e[6]=l=>r.enableCasteField=l),name:"enableCasteField",label:o.$trans("global.enable",{attribute:o.$trans("contact.caste.caste")}),error:n(a).enableCasteField,"onUpdate:error":e[7]||(e[7]=l=>n(a).enableCasteField=l)},null,8,["modelValue","label","error"])])]),u("div",T,[u("div",_,[i(s,{type:"text",modelValue:r.uniqueIdNumber1Label,"onUpdate:modelValue":e[8]||(e[8]=l=>r.uniqueIdNumber1Label=l),name:"uniqueIdNumber1Label",label:o.$trans("contact.config.props.unique_id_number1_label"),error:n(a).uniqueIdNumber1Label,"onUpdate:error":e[9]||(e[9]=l=>n(a).uniqueIdNumber1Label=l)},null,8,["modelValue","label","error"])]),u("div",B,[i(d,{vertical:"",modelValue:r.isUniqueIdNumber1Required,"onUpdate:modelValue":e[10]||(e[10]=l=>r.isUniqueIdNumber1Required=l),name:"uniqueIdNumber1Required",label:o.$trans("contact.config.props.unique_id_number1_required"),error:n(a).isUniqueIdNumber1Required,"onUpdate:error":e[11]||(e[11]=l=>n(a).isUniqueIdNumber1Required=l)},null,8,["modelValue","label","error"])]),e[28]||(e[28]=u("div",{class:"col-span-3 sm:col-span-1"},null,-1)),u("div",M,[i(s,{type:"text",modelValue:r.uniqueIdNumber2Label,"onUpdate:modelValue":e[12]||(e[12]=l=>r.uniqueIdNumber2Label=l),name:"uniqueIdNumber2Label",label:o.$trans("contact.config.props.unique_id_number2_label"),error:n(a).uniqueIdNumber2Label,"onUpdate:error":e[13]||(e[13]=l=>n(a).uniqueIdNumber2Label=l)},null,8,["modelValue","label","error"])]),u("div",w,[i(d,{vertical:"",modelValue:r.isUniqueIdNumber2Required,"onUpdate:modelValue":e[14]||(e[14]=l=>r.isUniqueIdNumber2Required=l),name:"uniqueIdNumber2Required",label:o.$trans("contact.config.props.unique_id_number2_required"),error:n(a).isUniqueIdNumber2Required,"onUpdate:error":e[15]||(e[15]=l=>n(a).isUniqueIdNumber2Required=l)},null,8,["modelValue","label","error"])]),e[29]||(e[29]=u("div",{class:"col-span-3 sm:col-span-1"},null,-1)),u("div",P,[i(s,{type:"text",modelValue:r.uniqueIdNumber3Label,"onUpdate:modelValue":e[16]||(e[16]=l=>r.uniqueIdNumber3Label=l),name:"uniqueIdNumber3Label",label:o.$trans("contact.config.props.unique_id_number3_label"),error:n(a).uniqueIdNumber3Label,"onUpdate:error":e[17]||(e[17]=l=>n(a).uniqueIdNumber3Label=l)},null,8,["modelValue","label","error"])]),u("div",E,[i(d,{vertical:"",modelValue:r.isUniqueIdNumber3Required,"onUpdate:modelValue":e[18]||(e[18]=l=>r.isUniqueIdNumber3Required=l),name:"uniqueIdNumber3Required",label:o.$trans("contact.config.props.unique_id_number3_required"),error:n(a).isUniqueIdNumber3Required,"onUpdate:error":e[19]||(e[19]=l=>n(a).isUniqueIdNumber3Required=l)},null,8,["modelValue","label","error"])]),e[30]||(e[30]=u("div",{class:"col-span-3 sm:col-span-1"},null,-1)),u("div",H,[i(s,{type:"text",modelValue:r.uniqueIdNumber4Label,"onUpdate:modelValue":e[20]||(e[20]=l=>r.uniqueIdNumber4Label=l),name:"uniqueIdNumber4Label",label:o.$trans("contact.config.props.unique_id_number4_label"),error:n(a).uniqueIdNumber4Label,"onUpdate:error":e[21]||(e[21]=l=>n(a).uniqueIdNumber4Label=l)},null,8,["modelValue","label","error"])]),u("div",k,[i(d,{vertical:"",modelValue:r.isUniqueIdNumber4Required,"onUpdate:modelValue":e[22]||(e[22]=l=>r.isUniqueIdNumber4Required=l),name:"uniqueIdNumber4Required",label:o.$trans("contact.config.props.unique_id_number4_required"),error:n(a).isUniqueIdNumber4Required,"onUpdate:error":e[23]||(e[23]=l=>n(a).isUniqueIdNumber4Required=l)},null,8,["modelValue","label","error"])]),e[31]||(e[31]=u("div",{class:"col-span-3 sm:col-span-1"},null,-1)),u("div",A,[i(s,{type:"text",modelValue:r.uniqueIdNumber5Label,"onUpdate:modelValue":e[24]||(e[24]=l=>r.uniqueIdNumber5Label=l),name:"uniqueIdNumber5Label",label:o.$trans("contact.config.props.unique_id_number5_label"),error:n(a).uniqueIdNumber5Label,"onUpdate:error":e[25]||(e[25]=l=>n(a).uniqueIdNumber5Label=l)},null,8,["modelValue","label","error"])]),u("div",S,[i(d,{vertical:"",modelValue:r.isUniqueIdNumber5Required,"onUpdate:modelValue":e[26]||(e[26]=l=>r.isUniqueIdNumber5Required=l),name:"uniqueIdNumber5Required",label:o.$trans("contact.config.props.unique_id_number5_required"),error:n(a).isUniqueIdNumber5Required,"onUpdate:error":e[27]||(e[27]=l=>n(a).isUniqueIdNumber5Required=l)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{z as default};
