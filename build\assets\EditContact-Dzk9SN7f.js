import{u as w,H as I,l as j,n as O,J as H,r as i,a as T,o as f,q as U,b as v,e as o,f as a,w as m,d,I as C,s as F,t as S,F as D}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3"},W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3"},Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},x={class:"mt-4 grid grid-cols-3 gap-6"},_={name:"StudentEditContact"},ee=Object.assign(_,{props:{guardian:{type:Object,default(){return{}}}},setup(p){const N=w(),k=p,A={contactNumber:"",email:"",alternateRecords:{},presentAddress:{},permanentAddress:{}},V="guardian/",n=I(V),t=j({...A});return O(async()=>{var e,b,l,u,c,y,g,L,s,z,E,P,$,B,R;let r=(e=k.guardian)==null?void 0:e.contact;Object.assign(A,{contactNumber:r.contactNumber,email:r.email,alternateRecords:{contactNumber:(b=r.alternateRecords)==null?void 0:b.contactNumber,email:(l=r.alternateRecords)==null?void 0:l.email},presentAddress:{addressLine1:(u=r.presentAddress)==null?void 0:u.addressLine1,addressLine2:(c=r.presentAddress)==null?void 0:c.addressLine2,city:(y=r.presentAddress)==null?void 0:y.city,state:(g=r.presentAddress)==null?void 0:g.state,zipcode:(L=r.presentAddress)==null?void 0:L.zipcode,country:(s=r.presentAddress)==null?void 0:s.country},permanentAddress:{sameAsPresentAddress:r.sameAsPresentAddress,addressLine1:(z=r.permanentAddress)==null?void 0:z.addressLine1,addressLine2:(E=r.permanentAddress)==null?void 0:E.addressLine2,city:(P=r.permanentAddress)==null?void 0:P.city,state:($=r.permanentAddress)==null?void 0:$.state,zipcode:(B=r.permanentAddress)==null?void 0:B.zipcode,country:(R=r.permanentAddress)==null?void 0:R.country}}),Object.assign(t,H(A))}),(r,e)=>{const b=i("PageHeader"),l=i("BaseInput"),u=i("AddressInput"),c=i("BaseFieldset"),y=i("BaseSwitch"),g=i("FormAction"),L=i("ParentTransition");return f(),T(D,null,[p.guardian.uuid?(f(),U(b,{key:0,title:r.$trans(a(N).meta.trans,{attribute:r.$trans(a(N).meta.label)}),navs:[{label:r.$trans("guardian.guardian"),path:"Guardian"},{label:p.guardian.contact.name,path:{name:"StudentShow",params:{uuid:p.guardian.uuid}}}]},null,8,["title","navs"])):v("",!0),o(L,{appear:"",visibility:!0},{default:m(()=>[p.guardian.uuid?(f(),U(g,{key:0,"init-url":V,"no-data-fetch":"","init-form":A,form:t,"stay-on":"",redirect:{name:"StudentShowContact",params:{uuid:p.guardian.uuid}}},{default:m(()=>[d("div",q,[d("div",G,[o(l,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=s=>t.contactNumber=s),name:"contactNumber",label:r.$trans("contact.props.contact_number"),error:a(n).contactNumber,"onUpdate:error":e[1]||(e[1]=s=>a(n).contactNumber=s)},null,8,["modelValue","label","error"])]),d("div",J,[o(l,{type:"text",modelValue:t.alternateRecords.contactNumber,"onUpdate:modelValue":e[2]||(e[2]=s=>t.alternateRecords.contactNumber=s),name:"alternateContactNumber",label:r.$trans("global.alternate",{attribute:r.$trans("contact.props.contact_number")}),error:a(n).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=s=>a(n).alternateContactNumber=s)},null,8,["modelValue","label","error"])]),e[24]||(e[24]=d("div",{class:"col-span-3 sm:col-span-1"},null,-1)),d("div",M,[o(l,{type:"text",modelValue:t.email,"onUpdate:modelValue":e[4]||(e[4]=s=>t.email=s),name:"email",label:r.$trans("contact.props.email"),error:a(n).email,"onUpdate:error":e[5]||(e[5]=s=>a(n).email=s)},null,8,["modelValue","label","error"])]),d("div",K,[o(l,{type:"text",modelValue:t.alternateRecords.email,"onUpdate:modelValue":e[6]||(e[6]=s=>t.alternateRecords.email=s),name:"alternateEmail",label:r.$trans("global.alternate",{attribute:r.$trans("contact.props.email")}),error:a(n).alternateEmail,"onUpdate:error":e[7]||(e[7]=s=>a(n).alternateEmail=s)},null,8,["modelValue","label","error"])]),e[25]||(e[25]=d("div",{class:"col-span-3 sm:col-span-1"},null,-1)),d("div",Q,[o(c,null,{legend:m(()=>[F(S(r.$trans("contact.props.present_address")),1)]),default:m(()=>[d("div",W,[o(u,{prefix:"presentAddress",addressLine1:t.presentAddress.addressLine1,"onUpdate:addressLine1":e[8]||(e[8]=s=>t.presentAddress.addressLine1=s),addressLine2:t.presentAddress.addressLine2,"onUpdate:addressLine2":e[9]||(e[9]=s=>t.presentAddress.addressLine2=s),city:t.presentAddress.city,"onUpdate:city":e[10]||(e[10]=s=>t.presentAddress.city=s),state:t.presentAddress.state,"onUpdate:state":e[11]||(e[11]=s=>t.presentAddress.state=s),zipcode:t.presentAddress.zipcode,"onUpdate:zipcode":e[12]||(e[12]=s=>t.presentAddress.zipcode=s),country:t.presentAddress.country,"onUpdate:country":e[13]||(e[13]=s=>t.presentAddress.country=s),formErrors:a(n),"onUpdate:formErrors":e[14]||(e[14]=s=>C(n)?n.value=s:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1})]),d("div",X,[o(c,null,{legend:m(()=>[F(S(r.$trans("contact.props.permanent_address")),1)]),default:m(()=>[d("div",Y,[d("div",Z,[o(y,{modelValue:t.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[15]||(e[15]=s=>t.permanentAddress.sameAsPresentAddress=s),name:"sameAsPresentAddress",label:r.$trans("contact.props.same_as_present_address"),error:a(n).sameAsPresentAddress,"onUpdate:error":e[16]||(e[16]=s=>a(n).sameAsPresentAddress=s)},null,8,["modelValue","label","error"])])]),d("div",x,[t.permanentAddress.sameAsPresentAddress?v("",!0):(f(),U(u,{key:0,prefix:"permanentAddress",addressLine1:t.permanentAddress.addressLine1,"onUpdate:addressLine1":e[17]||(e[17]=s=>t.permanentAddress.addressLine1=s),addressLine2:t.permanentAddress.addressLine2,"onUpdate:addressLine2":e[18]||(e[18]=s=>t.permanentAddress.addressLine2=s),city:t.permanentAddress.city,"onUpdate:city":e[19]||(e[19]=s=>t.permanentAddress.city=s),state:t.permanentAddress.state,"onUpdate:state":e[20]||(e[20]=s=>t.permanentAddress.state=s),zipcode:t.permanentAddress.zipcode,"onUpdate:zipcode":e[21]||(e[21]=s=>t.permanentAddress.zipcode=s),country:t.permanentAddress.country,"onUpdate:country":e[22]||(e[22]=s=>t.permanentAddress.country=s),formErrors:a(n),"onUpdate:formErrors":e[23]||(e[23]=s=>C(n)?n.value=s:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1})])])]),_:1},8,["form","redirect"])):v("",!0)]),_:1})],64)}}});export{ee as default};
