import{u as ue,l as L,n as me,r as p,q as d,o as n,w as t,d as b,e as u,s as i,a as _,b as m,t as s,h as de,i as ce,j as K,y as x,B as R,m as Q,z as pe,f as e,F as V,v as E,A as M,M as P}from"./app-BAwPsakn.js";const fe={class:"grid grid-cols-3 gap-6"},_e={class:"col-span-3 sm:col-span-1"},be={key:0},he={key:0},ke={class:"col-span-3 sm:col-span-1"},xe={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(N,{emit:F}){const v=ue(),o=F,y={batches:[],exams:[]},$=L({...y}),B=L({batches:[],exams:[],isLoaded:!(v.query.batches||v.query.exams)});return me(async()=>{B.batches=v.query.batches?v.query.batches.split(","):[],B.exams=v.query.exams?v.query.exams.split(","):[],B.isLoaded=!0}),(h,S)=>{const O=p("BaseSelect"),k=p("BaseSelectSearch"),j=p("FilterForm");return n(),d(j,{"init-form":y,form:$,multiple:["exams","batches"],onHide:S[2]||(S[2]=r=>o("hide"))},{default:t(()=>[b("div",fe,[b("div",_e,[u(O,{multiple:"",modelValue:$.exams,"onUpdate:modelValue":S[0]||(S[0]=r=>$.exams=r),name:"exams",label:h.$trans("global.select",{attribute:h.$trans("exam.exam")}),"track-by":["name"],"value-prop":"uuid",options:N.preRequisites.exams},{selectedOption:t(r=>{var T,A;return[i(s(r.value.name)+" ",1),r.value.term?(n(),_("span",be,"("+s(((A=(T=r.value.term)==null?void 0:T.division)==null?void 0:A.name)||h.$trans("general.all"))+")",1)):m("",!0)]}),listOption:t(r=>{var T,A;return[i(s(r.option.name)+" ",1),r.option.term?(n(),_("span",he,"("+s(((A=(T=r.option.term)==null?void 0:T.division)==null?void 0:A.name)||h.$trans("general.all"))+")",1)):m("",!0)]}),_:1},8,["modelValue","label","options"])]),b("div",ke,[B.isLoaded?(n(),d(k,{key:0,multiple:"",name:"batches",label:h.$trans("global.select",{attribute:h.$trans("academic.batch.batch")}),modelValue:$.batches,"onUpdate:modelValue":S[1]||(S[1]=r=>$.batches=r),"value-prop":"uuid","init-search":B.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(r=>[i(s(r.value.course.name)+" "+s(r.value.name),1)]),listOption:t(r=>[i(s(r.option.course.nameWithTerm)+" "+s(r.option.name),1)]),_:1},8,["label","modelValue","init-search"])):m("",!0)])])]),_:1},8,["form"])}}},ve={class:"grid grid-cols-1 gap-4 px-4 pt-4 lg:grid-cols-2"},ye={class:"py-2 text-center"},ge={class:"dark:text-gray-400"},Ce={class:"space-x-2"},$e=["onClick"],Se=["onClick"],we={key:0},Fe={key:0},Be={class:"text-xl font-semibold"},qe={class:"ml-1 space-x-1"},Te={key:0,class:"fas fa-file-lines"},Ae={key:1,class:"fas fa-id-card"},je={name:"ExamScheduleList"},Re=Object.assign(je,{setup(N){const F=de(),v=ce(),o=K("$trans"),y=K("emitter");let $=[];x("exam-schedule:create")&&$.unshift("create"),R(["student","guardian"],"any")||$.unshift("filter");let B=[];x("exam-schedule:export")&&(B=["print","pdf","excel"]);const h="exam/schedule/",S=L({exams:[]}),O=[{key:"subject",label:o("academic.subject.subject"),visibility:!0},{key:"date",label:o("exam.schedule.props.date"),visibility:!0},{key:"assessment",label:o("exam.assessment.assessment"),visibility:!0}],k=Q(!1),j=Q(!1),r=L({}),T=f=>{Object.assign(r,f)},A=f=>{Object.assign(S,f)},X=f=>f.filter(c=>c.date.value),Y=async f=>{await P()&&(k.value=!0,await v.dispatch(h+"copyToCourse",{uuid:f.uuid}).then(c=>{k.value=!1,y.emit("listItems")}).catch(c=>{k.value=!1}))},U=async(f,c)=>{await P()&&(k.value=!0,await v.dispatch(h+"updateForm",{uuid:f.uuid,value:c}).then(D=>{k.value=!1,y.emit("listItems")}).catch(D=>{k.value=!1}))},W=async(f,c)=>{await P()&&(k.value=!0,await v.dispatch(h+"togglePublishAdmitCard",{uuid:f.uuid}).then(D=>{k.value=!1,y.emit("listItems")}).catch(D=>{k.value=!1}))},Z=f=>{window.open(`/app/exam/schedules/${f.uuid}/marksheet?action=print`)};return(f,c)=>{const D=p("PageHeaderAction"),ee=p("PageHeader"),H=p("ParentTransition"),g=p("TextMuted"),z=p("BaseBadge"),C=p("DataCell"),G=p("DataRow"),te=p("SimpleTable"),ae=p("CardView"),se=p("Pagination"),ne=p("CardList"),w=p("FloatingMenuItem"),ie=p("FloatingMenu"),oe=p("BaseButton"),le=p("DataTable"),re=p("ListItem"),I=pe("tooltip");return n(),d(re,{"init-url":h,"pre-requisites":!e(R)(["student","guardian"],"any"),onSetPreRequisites:A,onSetItems:T},{header:t(()=>[u(ee,{title:e(o)("exam.schedule.schedule"),navs:[{label:e(o)("exam.exam"),path:"Exam"}]},{default:t(()=>[u(D,{url:"exam/schedules/",name:"ExamSchedule",title:e(o)("exam.schedule.schedule"),actions:e($),"dropdown-actions":e(B),onToggleFilter:c[0]||(c[0]=a=>j.value=!j.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[u(H,{appear:"",visibility:j.value},{default:t(()=>[u(xe,{onRefresh:c[1]||(c[1]=a=>e(y).emit("listItems")),"pre-requisites":S,onHide:c[2]||(c[2]=a=>j.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:t(()=>[e(R)(["student","guardian"],"any")?(n(),d(H,{key:0,appear:"",visibility:!0},{default:t(()=>[u(ne,{header:r.headers,meta:r.meta},{content:t(()=>[b("div",Be,s(e(o)("dashboard.nothing_to_show")),1)]),default:t(()=>[b("div",ve,[(n(!0),_(V,null,E(r.data,a=>(n(),d(ae,{key:a.uuid,"no-padding":""},{default:t(()=>[b("div",ye,[b("span",ge,s(a.exam.name),1),b("span",Ce,[e(R)("student")&&a.hasForm?M((n(),_("span",{key:0,class:"cursor-pointer",onClick:l=>e(F).push({name:"ExamScheduleFormSubmission",params:{uuid:a.uuid}})},c[6]||(c[6]=[b("i",{class:"fas fa-arrow-up-right-from-square"},null,-1)]),8,$e)),[[I,e(o)("exam.schedule.form")]]):m("",!0),a.marksheetAvailable?M((n(),_("span",{key:1,class:"cursor-pointer",onClick:l=>Z(a)},c[7]||(c[7]=[b("i",{class:"far fa-file-lines"},null,-1)]),8,Se)),[[I,e(o)("exam.marksheet.marksheet")]]):m("",!0)]),u(g,{block:""},{default:t(()=>[i(s(a.batch.course.name)+" "+s(a.batch.name),1)]),_:2},1024),a.isReassessment?(n(),d(z,{key:0},{default:t(()=>[i(s(e(o)("exam.schedule.reassessment")+" ("+a.attempt.label+")"),1)]),_:2},1024)):m("",!0)]),a.records.length>0?(n(),d(te,{key:0,header:O},{default:t(()=>[(n(!0),_(V,null,E(X(a.records),l=>(n(),d(G,{key:l.uuid},{default:t(()=>[u(C,{name:"subject"},{default:t(()=>[i(s(l.subject.name)+" ",1),l.subject.code?(n(),_("span",we,"("+s(l.subject.code)+")",1)):m("",!0),l.hasGrading?(n(),d(g,{key:1,block:""},{default:t(()=>[i("("+s(e(o)("exam.schedule.props.grading"))+")",1)]),_:1})):m("",!0)]),_:2},1024),u(C,{name:"date"},{default:t(()=>[i(s(l.date.formatted)+" ",1),l.startTime.value?(n(),d(g,{key:0,block:""},{default:t(()=>[i(s(l.startTime.formatted)+" ",1),l.endTime.value?(n(),_("span",Fe,"- "+s(l.endTime.formatted),1)):m("",!0)]),_:2},1024)):m("",!0)]),_:2},1024),u(C,{name:"assessment"},{default:t(()=>[(n(!0),_(V,null,E(l.assessments,q=>(n(),_("div",null,[i(s(q.name)+" ",1),u(g,null,{default:t(()=>[i(s(q.maxMark),1)]),_:2},1024)]))),256))]),_:2},1024)]),_:2},1024))),128))]),_:2},1024)):m("",!0)]),_:2},1024))),128))]),b("div",null,[u(se,{"card-view":"",meta:r.meta,onRefresh:c[3]||(c[3]=a=>e(y).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(n(),d(H,{key:1,appear:"",visibility:!0},{default:t(()=>[u(le,{header:r.headers,meta:r.meta,module:"exam.schedule",onRefresh:c[5]||(c[5]=a=>e(y).emit("listItems"))},{actionButton:t(()=>[e(x)("exam-schedule:create")?(n(),d(oe,{key:0,onClick:c[4]||(c[4]=a=>e(F).push({name:"ExamScheduleCreate"}))},{default:t(()=>[i(s(e(o)("global.add",{attribute:e(o)("exam.schedule.schedule")})),1)]),_:1})):m("",!0)]),default:t(()=>[(n(!0),_(V,null,E(r.data,a=>(n(),d(G,{key:a.uuid,onDoubleClick:l=>e(F).push({name:"ExamScheduleShow",params:{uuid:a.uuid}})},{default:t(()=>[u(C,{name:"exam"},{default:t(()=>{var l;return[b("span",null,s(a.exam.name),1),b("span",qe,[a.hasForm&&e(x)("exam-schedule:edit")?M((n(),_("i",Te,null,512)),[[I,e(o)("exam.form.form")]]):m("",!0),a.publishAdmitCard?M((n(),_("i",Ae,null,512)),[[I,e(o)("exam.admit_card.admit_card")]]):m("",!0)]),u(g,{block:""},{default:t(()=>{var q,J;return[i(s((J=(q=a.exam.term)==null?void 0:q.division)==null?void 0:J.name),1)]}),_:2},1024),a.isReassessment?(n(),d(z,{key:0},{default:t(()=>[i(s(e(o)("exam.schedule.reassessment")+" ("+a.attempt.label+")"),1)]),_:2},1024)):m("",!0),(l=a.examConfig)!=null&&l.publishMarksheet?(n(),d(g,{key:1,block:"",class:"text-success"},{default:t(()=>[i(s(e(o)("exam.marksheet.published")),1)]),_:1})):m("",!0)]}),_:2},1024),u(C,{name:"batch"},{default:t(()=>[i(s(a.batch.course.name)+" ",1),u(g,{block:""},{default:t(()=>[i(s(a.batch.name),1)]),_:2},1024)]),_:2},1024),u(C,{name:"assessment"},{default:t(()=>{var l;return[i(s(a.assessment.name)+" ",1),u(g,{block:""},{default:t(()=>{var q;return[i(s((q=a.observation)==null?void 0:q.name),1)]}),_:2},1024),((l=a.marksheetStatus)==null?void 0:l.value)=="processed"?(n(),d(g,{key:0,block:"",class:"text-info"},{default:t(()=>[i(s(e(o)("exam.marksheet.processed")),1)]),_:1})):m("",!0)]}),_:2},1024),u(C,{name:"grade"},{default:t(()=>[i(s(a.grade.name),1)]),_:2},1024),u(C,{name:"period"},{default:t(()=>[i(s(a.startDate.formatted)+" ",1),u(g,{block:""},{default:t(()=>[i(s(a.endDate.formatted),1)]),_:2},1024)]),_:2},1024),u(C,{name:"createdAt"},{default:t(()=>[i(s(a.createdAt.formatted),1)]),_:2},1024),u(C,{name:"action"},{default:t(()=>[u(ie,null,{default:t(()=>[e(R)("student")&&a.hasForm?(n(),d(w,{key:0,icon:"fas fa-file-lines",onClick:l=>e(F).push({name:"ExamScheduleFormSubmission",params:{uuid:a.uuid}})},{default:t(()=>[i(s(e(o)("exam.schedule.form")),1)]),_:2},1032,["onClick"])):m("",!0),u(w,{icon:"fas fa-arrow-circle-right",onClick:l=>e(F).push({name:"ExamScheduleShow",params:{uuid:a.uuid}})},{default:t(()=>[i(s(e(o)("general.show")),1)]),_:2},1032,["onClick"]),e(x)("exam-schedule:edit")?(n(),d(w,{key:1,icon:"fas fa-edit",onClick:l=>e(F).push({name:"ExamScheduleEdit",params:{uuid:a.uuid}})},{default:t(()=>[i(s(e(o)("general.edit")),1)]),_:2},1032,["onClick"])):m("",!0),e(x)("exam-schedule:create")&&a.batch.course.batchWithSameSubject?(n(),d(w,{key:2,icon:"fas fa-copy",onClick:l=>Y(a)},{default:t(()=>[i(s(e(o)("exam.schedule.copy_to_course")),1)]),_:2},1032,["onClick"])):m("",!0),e(x)("exam-schedule:edit")&&!a.hasForm?(n(),d(w,{key:3,icon:"fas fa-file",onClick:l=>U(a,!0)},{default:t(()=>[i(s(e(o)("global.enable",{attribute:e(o)("exam.form.form")})),1)]),_:2},1032,["onClick"])):m("",!0),e(x)("exam-schedule:edit")&&a.hasForm?(n(),d(w,{key:4,icon:"fas fa-file",onClick:l=>U(a,!1)},{default:t(()=>[i(s(e(o)("global.disable",{attribute:e(o)("exam.form.form")})),1)]),_:2},1032,["onClick"])):m("",!0),e(x)("exam-schedule:edit")&&!a.publishAdmitCard?(n(),d(w,{key:5,icon:"fas fa-id-card",onClick:l=>W(a)},{default:t(()=>[i(s(e(o)("global.publish",{attribute:e(o)("exam.admit_card.admit_card")})),1)]),_:2},1032,["onClick"])):m("",!0),e(x)("exam-schedule:edit")&&a.publishAdmitCard?(n(),d(w,{key:6,icon:"fas fa-id-card",onClick:l=>W(a)},{default:t(()=>[i(s(e(o)("global.unpublish",{attribute:e(o)("exam.admit_card.admit_card")})),1)]),_:2},1032,["onClick"])):m("",!0),e(x)("exam-schedule:delete")?(n(),d(w,{key:7,icon:"fas fa-trash",onClick:l=>e(y).emit("deleteItem",{uuid:a.uuid})},{default:t(()=>[i(s(e(o)("general.delete")),1)]),_:2},1032,["onClick"])):m("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1},8,["pre-requisites"])}}});export{Re as default};
