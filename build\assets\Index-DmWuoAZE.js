import{l as w,r as n,q as H,o as _,w as e,d as g,e as a,u as j,i as T,H as U,m as D,n as S,a as P,b as N,f as m,s as i,t as d,h as O,j as z,z as q,F as M,v as G,A as J}from"./app-BAwPsakn.js";import{_ as K}from"./ModuleDropdown-7izf9CwW.js";import{d as Q}from"./vuedraggable.umd-BRYqknf6.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",emits:["hide"],setup(F,{emit:y}){const c=y,v={name:"",code:"",alias:""},u=w({...v});return(f,l)=>{const r=n("BaseInput"),b=n("FilterForm");return _(),H(b,{"init-form":v,form:u,onHide:l[3]||(l[3]=t=>c("hide"))},{default:e(()=>[g("div",W,[g("div",X,[a(r,{type:"text",modelValue:u.name,"onUpdate:modelValue":l[0]||(l[0]=t=>u.name=t),name:"name",label:f.$trans("employee.payroll.pay_head.props.name")},null,8,["modelValue","label"])]),g("div",Y,[a(r,{type:"text",modelValue:u.code,"onUpdate:modelValue":l[1]||(l[1]=t=>u.code=t),name:"code",label:f.$trans("employee.payroll.pay_head.props.code")},null,8,["modelValue","label"])]),g("div",Z,[a(r,{type:"text",modelValue:u.alias,"onUpdate:modelValue":l[2]||(l[2]=t=>u.alias=t),name:"alias",label:f.$trans("employee.payroll.pay_head.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ee={key:0},ae={class:"flex border rounded-xl px-4 py-2"},te={key:1},le={key:2,class:"mt-4 flex justify-end"},oe={name:"EmployeePayrollPayHeadReorder"},se=Object.assign(oe,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(F,{emit:y}){j();const c=T(),v=y,u={payHeads:[]};U("employee/payroll/payHead/");const l=D(!1),r=w({payHeads:[]});w({...u});const b=async()=>{l.value=!0,await c.dispatch("employee/payroll/payHead/list",{params:{all:!0}}).then(p=>{l.value=!1,r.payHeads=p}).catch(p=>{l.value=!1})},t=async()=>{l.value=!0,await c.dispatch("employee/payroll/payHead/reorder",{data:{payHeads:r.payHeads}}).then(p=>{l.value=!1,v("refresh"),v("close")}).catch(p=>{l.value=!1})},o=()=>{v("close")};return S(()=>{b()}),(p,B)=>{const E=n("BaseLabel"),k=n("BaseAlert"),$=n("BaseButton"),h=n("BaseModal");return _(),H(h,{show:F.visibility,onClose:o},{title:e(()=>[i(d(p.$trans("global.reorder",{attribute:p.$trans("employee.payroll.pay_head.pay_head")})),1)]),default:e(()=>[r.payHeads.length?(_(),P("div",ee,[a(m(Q),{class:"space-y-2",list:r.payHeads,"item-key":"uuid"},{item:e(({element:V,index:I})=>[g("div",ae,[B[0]||(B[0]=g("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),a(E,null,{default:e(()=>[i(d(V.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(_(),P("div",te,[a(k,{design:"info",size:"xs"},{default:e(()=>[i(d(p.$trans("general.errors.record_not_found")),1)]),_:1})])),r.payHeads.length?(_(),P("div",le,[a($,{onClick:t},{default:e(()=>[i(d(p.$trans("general.reorder")),1)]),_:1})])):N("",!0)]),_:1},8,["show"])}}}),ne={name:"EmployeePayrollPayHeadList"},pe=Object.assign(ne,{setup(F){const y=O(),c=z("emitter");let v=["create","filter"];const u="employee/payroll/payHead/",f=D(!1),l=D(!1),r=w({}),b=t=>{Object.assign(r,t)};return(t,o)=>{const p=n("BaseButton"),B=n("PageHeaderAction"),E=n("PageHeader"),k=n("ParentTransition"),$=n("DataCell"),h=n("FloatingMenuItem"),V=n("FloatingMenu"),I=n("DataRow"),R=n("DataTable"),A=n("ListItem"),L=q("tooltip");return _(),P(M,null,[a(A,{"init-url":u,onSetItems:b},{header:e(()=>[a(E,{title:t.$trans("employee.payroll.pay_head.pay_head"),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:t.$trans("employee.payroll.payroll"),path:"EmployeePayroll"}]},{default:e(()=>[a(B,{url:"employee/payroll/pay-heads/",name:"EmployeePayrollPayHead",title:t.$trans("employee.payroll.pay_head.pay_head"),actions:m(v),"dropdown-actions":["print","pdf","excel"],onToggleFilter:o[1]||(o[1]=s=>f.value=!f.value)},{moduleOption:e(()=>[a(K)]),default:e(()=>[J((_(),H(p,{design:"white",onClick:o[0]||(o[0]=s=>l.value=!l.value)},{default:e(()=>o[8]||(o[8]=[g("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[L,t.$trans("global.reorder",{attribute:t.$trans("employee.payroll.pay_head.pay_head")})]])]),_:1},8,["title","actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(k,{appear:"",visibility:f.value},{default:e(()=>[a(x,{onRefresh:o[2]||(o[2]=s=>m(c).emit("listItems")),onHide:o[3]||(o[3]=s=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(k,{appear:"",visibility:!0},{default:e(()=>[a(R,{header:r.headers,meta:r.meta,module:"employee.payroll.pay_head",onRefresh:o[5]||(o[5]=s=>m(c).emit("listItems"))},{actionButton:e(()=>[a(p,{onClick:o[4]||(o[4]=s=>m(y).push({name:"EmployeePayrollPayHeadCreate"}))},{default:e(()=>[i(d(t.$trans("global.add",{attribute:t.$trans("employee.payroll.pay_head.pay_head")})),1)]),_:1})]),default:e(()=>[(_(!0),P(M,null,G(r.data,s=>(_(),H(I,{key:s.uuid,onDoubleClick:C=>m(y).push({name:"EmployeePayrollPayHeadShow",params:{uuid:s.uuid}})},{default:e(()=>[a($,{name:"name"},{default:e(()=>[i(d(s.name),1)]),_:2},1024),a($,{name:"code"},{default:e(()=>[i(d(s.code),1)]),_:2},1024),a($,{name:"alias"},{default:e(()=>[i(d(s.alias),1)]),_:2},1024),a($,{name:"category"},{default:e(()=>[i(d(s.category.label),1)]),_:2},1024),a($,{name:"createdAt"},{default:e(()=>[i(d(s.createdAt.formatted),1)]),_:2},1024),a($,{name:"action"},{default:e(()=>[a(V,null,{default:e(()=>[a(h,{icon:"fas fa-arrow-circle-right",onClick:C=>m(y).push({name:"EmployeePayrollPayHeadShow",params:{uuid:s.uuid}})},{default:e(()=>[i(d(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-edit",onClick:C=>m(y).push({name:"EmployeePayrollPayHeadEdit",params:{uuid:s.uuid}})},{default:e(()=>[i(d(t.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-copy",onClick:C=>m(y).push({name:"EmployeePayrollPayHeadDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[i(d(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-trash",onClick:C=>m(c).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[i(d(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),a(se,{visibility:l.value,onClose:o[6]||(o[6]=s=>l.value=!1),onRefresh:o[7]||(o[7]=s=>m(c).emit("listItems"))},null,8,["visibility"])],64)}}});export{pe as default};
