import{u as j,j as B,H as C,l as _,n as $,J as k,r as e,a as P,o,q as d,b as u,e as c,f as l,w as m,d as p,F}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-1"},w={class:"col-span-1"},E={name:"ContactEditPhoto"},G=Object.assign(E,{props:{guardian:{type:Object,default(){return{}}}},setup(a){const r=j(),s=B("emitter"),g=a,n={photo:""};C("guardian/");const i=_({...n}),h=async()=>{s.emit("guardianUpdated")},b=async()=>{s.emit("guardianUpdated")};return $(async()=>{Object.assign(n,{photo:g.guardian.contact.photo}),Object.assign(i,k(n))}),(t,N)=>{const v=e("PageHeader"),f=e("ImageUpload"),y=e("BaseCard"),U=e("ParentTransition");return o(),P(F,null,[a.guardian.uuid?(o(),d(v,{key:0,title:t.$trans(l(r).meta.trans,{attribute:t.$trans(l(r).meta.label)}),navs:[{label:t.$trans("guardian.guardian"),path:"Guardian"},{label:a.guardian.contact.name,path:{name:"GuardianShow",params:{uuid:a.guardian.uuid}}}]},null,8,["title","navs"])):u("",!0),c(U,{appear:"",visibility:!0},{default:m(()=>[a.guardian.uuid?(o(),d(y,{key:0},{default:m(()=>[p("div",O,[p("div",w,[c(f,{label:t.$trans("contact.props.photo"),src:i.photo,"upload-path":`guardians/${a.guardian.uuid}/photo`,"remove-path":`guardians/${a.guardian.uuid}/photo`,onUploaded:h,onRemoved:b},null,8,["label","src","upload-path","remove-path"])])])]),_:1})):u("",!0)]),_:1})],64)}}});export{G as default};
