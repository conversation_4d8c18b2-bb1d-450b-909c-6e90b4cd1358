import{u as M,h as G,i as K,m as Q,l as S,H as X,n as Y,r as n,a as d,o as s,e as l,q as _,b as B,w as o,f as i,s as c,t as r,d as h,F as k,v as D,J as Z}from"./app-BAwPsakn.js";import{_ as x}from"./Filter--6VJ5JII.js";const ee={class:"p-2"},te={class:"divide-y divide-gray-200 dark:divide-gray-700"},ae={class:"col-span-3 sm:col-span-1"},oe={key:0,class:"col-span-3 sm:col-span-2"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},re={name:"EmployeeAttendanceWorkShiftAssign"},ce=Object.assign(re,{setup(le){const u=M(),A=G(),E=K(),y={employees:[]},g="employee/attendance/workShift/",w=Q(!1),v=S({workShifts:[]}),b=X(g),p=S({...y}),V=S({meta:{}}),P=e=>{p.employees.forEach(f=>{f.workShifts.length==0&&(f.workShift=e.uuid)})},$=async()=>{w.value=!0,await E.dispatch(g+"fetchEmployee",{params:u.query}).then(e=>{w.value=!1,y.employees=e.data,V.meta=e.meta,Object.assign(p,Z(y))}).catch(e=>{w.value=!1})},R=e=>{Object.assign(v,e)},U=async()=>{await $()};return Y(async()=>{u.query.startDate&&u.query.endDate&&await $()}),(e,f)=>{const C=n("BaseButton"),F=n("PageHeaderAction"),H=n("PageHeader"),N=n("ParentTransition"),O=n("DropdownItem"),T=n("DropdownButton"),W=n("BaseAlert"),q=n("BaseDataView"),j=n("BaseSelect"),I=n("BaseTextarea"),L=n("FormAction"),z=n("Pagination"),J=n("BaseCard");return s(),d(k,null,[l(H,{title:e.$trans(i(u).meta.trans,{attribute:e.$trans(i(u).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"},{label:e.$trans("employee.attendance.work_shift.work_shift"),path:"EmployeeAttendanceWorkShiftReport"}]},{default:o(()=>[l(F,null,{default:o(()=>[l(C,{design:"white",onClick:f[0]||(f[0]=a=>i(A).push({name:"EmployeeAttendanceWorkShiftReport"}))},{default:o(()=>[c(r(e.$trans("global.report",{attribute:e.$trans("employee.attendance.work_shift.work_shift")})),1)]),_:1})]),_:1})]),_:1},8,["title","navs"]),l(N,{appear:"",visibility:!0},{default:o(()=>[l(x,{onAfterFilter:$,"init-url":g})]),_:1}),i(u).query.startDate&&i(u).query.endDate?(s(),_(J,{key:0,"no-padding":"","no-content-padding":"","is-loading":w.value},{title:o(()=>[c(r(e.$trans("employee.attendance.work_shift.assign")),1)]),action:o(()=>[v.workShifts?(s(),_(T,{key:0,label:e.$trans("employee.attendance.work_shift.work_shift")},{default:o(()=>[(s(!0),d(k,null,D(v.workShifts,a=>(s(),d("div",{key:a.uuid},[l(O,{as:"span",onClick:m=>P(a)},{default:o(()=>[c(r(a.name)+" ("+r(a.code)+") ",1)]),_:2},1032,["onClick"])]))),128))]),_:1},8,["label"])):B("",!0)]),default:o(()=>[h("div",ee,[p.employees.length==0?(s(),_(W,{key:0,size:"xs",design:"error"},{default:o(()=>[c(r(e.$trans("general.errors.record_not_found")),1)]),_:1})):B("",!0)]),p.employees.length?(s(),_(L,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":g,"pre-requisites":!0,"pre-requisite-custom-url":"assignPreRequisite",onSetPreRequisites:R,action:"assign","init-form":y,form:p,"after-submit":U},{default:o(()=>[h("div",te,[(s(!0),d(k,null,D(p.employees,(a,m)=>(s(),d("div",{class:"grid grid-cols-3 gap-6 px-4 py-2",key:a.uuid},[h("div",ae,[l(q,{label:a.name+" ("+a.codeNumber+")",revert:""},{default:o(()=>[c(r(a.designation),1)]),_:2},1032,["label"])]),a.workShifts.length?(s(),d("div",oe,[(s(!0),d(k,null,D(a.workShifts,t=>(s(),_(q,{label:t.name+" ("+t.code+")",revert:""},{default:o(()=>[c(r(t.startDate.formatted)+" - "+r(t.endDate.formatted),1)]),_:2},1032,["label"]))),256))])):(s(),d(k,{key:1},[h("div",ne,[l(j,{modelValue:a.workShift,"onUpdate:modelValue":t=>a.workShift=t,name:`employees.${m}.workShift`,placeholder:e.$trans("employee.attendance.work_shift.work_shift"),options:v.workShifts,"value-prop":"uuid",error:i(b)[`employees.${m}.workShift`],"onUpdate:error":t=>i(b)[`employees.${m}.workShift`]=t},{selectedOption:o(t=>[c(r(t.value.name)+" ("+r(t.value.code)+") ",1)]),listOption:o(t=>[c(r(t.option.name)+" ("+r(t.option.code)+") ",1)]),_:2},1032,["modelValue","onUpdate:modelValue","name","placeholder","options","error","onUpdate:error"])]),h("div",se,[l(I,{rows:1,modelValue:a.remarks,"onUpdate:modelValue":t=>a.remarks=t,name:`employees.${m}.remarks`,placeholder:e.$trans("employee.attendance.work_shift.props.remarks"),error:i(b)[`details.${m}.remarks`],"onUpdate:error":t=>i(b)[`details.${m}.remarks`]=t},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])],64))]))),128))])]),_:1},8,["form"])):B("",!0),l(z,{meta:V.meta,onRefresh:$},null,8,["meta"])]),_:1},8,["is-loading"])):B("",!0)],64)}}});export{ce as default};
