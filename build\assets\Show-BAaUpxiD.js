import{i as V,u as H,h as I,j as R,l as A,r as i,a as r,o as t,e as m,w as a,f as l,q as v,b as D,d,F as f,v as y,t as u}from"./app-BAwPsakn.js";const L={class:"flex items-center"},N={class:"mr-2"},O={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={key:0},M={class:"flex space-x-4"},q=["src"],x={key:1},E={key:2},U={name:"FormSubmissionShow"},J=Object.assign(U,{props:{form:{type:Object,default(){return{}}}},setup(c){V();const p=H(),h=I();R("emitter");const S={},$="form/submission/",n=A({...S}),k=e=>{Object.assign(n,e)},_=e=>{let s=n.responses.find(b=>b.uuid===e.uuid);return s?s.value:"-"};return(e,s)=>{const b=i("PageHeaderAction"),B=i("PageHeader"),w=i("ListMedia"),F=i("BaseDataView"),P=i("BaseCard"),j=i("ShowItem"),C=i("ParentTransition");return t(),r(f,null,[m(B,{title:e.$trans(l(p).meta.trans,{attribute:e.$trans(l(p).meta.label)}),navs:[{label:e.$trans("form.form"),path:"Form"},{label:e.$trans("form.submission.submission"),path:"FormSubmission"}]},{default:a(()=>[m(b,{name:"FormSubmission",title:e.$trans("form.submission.submission"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),m(C,{appear:"",visibility:!0},{default:a(()=>[m(j,{"init-url":$,uuid:l(p).params.uuid,"module-uuid":l(p).params.muuid,onSetItem:k,onRedirectTo:s[0]||(s[0]=o=>l(h).push({name:"FormSubmission",params:{uuid:n.uuid}}))},{default:a(()=>[c.form.uuid&&n.uuid?(t(),v(P,{key:0},{title:a(()=>[d("div",L,[d("span",N,u(c.form.name)+" "+u(e.$trans("general.by"))+" "+u(n.name)+" "+u(e.$trans("general.at"))+" "+u(n.submittedAt.formatted),1)])]),action:a(()=>s[1]||(s[1]=[])),footer:a(()=>s[2]||(s[2]=[])),default:a(()=>[d("dl",O,[(t(!0),r(f,null,y(c.form.fields,(o,z)=>(t(),v(F,{class:"col-span-1 sm:col-span-2",label:o.label},{default:a(()=>[o.type.value=="camera_image"?(t(),r("div",T,[d("div",M,[(t(!0),r(f,null,y(_(o),g=>(t(),r("div",{key:g},[d("img",{src:g},null,8,q)]))),128))])])):o.type.value=="file_upload"?(t(),r("div",x,[m(w,{media:n.media,section:o.slug,url:`/app/forms/${c.form.uuid}/submissions/${n.uuid}/`},null,8,["media","section","url"])])):(t(),r("div",E,u(_(o)),1))]),_:2},1032,["label"]))),256))])]),_:1})):D("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{J as default};
