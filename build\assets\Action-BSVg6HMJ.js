import{u as H,h as E,i as I,G as j,H as G,m as J,l as B,n as W,r as c,q as R,o as h,w as p,d as u,a as T,b as z,f as l,s as N,t as b,e as i,F as P,v as K,M as Q,J as X}from"./app-BAwPsakn.js";const Y={class:"grid grid-cols-4 gap-6"},Z={class:"col-span-4 sm:col-span-1"},x={class:"col-span-4 sm:col-span-1"},ee=["onClick"],se={class:"mt-4 grid grid-cols-4 gap-4"},ae={class:"col-span-4 sm:col-span-1"},te={class:"col-span-4 sm:col-span-1"},oe={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-1"},le={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-3"},ue={class:"mt-4"},ie={class:"mt-4 grid grid-cols-1"},ce={class:"col"},de={class:"col"},me={name:"ResourceSyllabusForm"},pe=Object.assign(me,{setup(w){const _=H();E(),I();const d={batches:[],subject:"",remarks:"",units:[],media:[],mediaUpdated:!1,mediaToken:j(),mediaHash:[]},S={uuid:j(),unitNumber:"",unitName:"",startDate:"",endDate:"",completionDate:"",description:""},v="resource/syllabus/",t=G(v);J(!1);const f=B({subjects:[],statuses:[]});B({selectedBatch:null,subjects:[]});const n=B({...d}),$=B({batches:[],subject:"",isLoaded:!_.params.uuid}),O=a=>{Object.assign(f,a)},A=()=>{n.mediaToken=j(),n.mediaHash=[]},F=()=>{n.units.push({...S,uuid:j()}),$.isLoaded=!0},C=async a=>{await Q()&&(n.units.length==1?n.units=[S]:n.units.splice(a,1))},L=async a=>{var D,V;let o=a.units.map(m=>{var U,g,k;return{...m,startDate:((U=m.startDate)==null?void 0:U.value)||"",endDate:((g=m.endDate)==null?void 0:g.value)||"",completionDate:((k=m.completionDate)==null?void 0:k.value)||""}}),y=a.records.map(m=>m.batch.uuid)||[];Object.assign(d,{...a,units:o,batches:y,subject:((V=(D=a.records[0])==null?void 0:D.subject)==null?void 0:V.uuid)||""}),Object.assign(n,X(d)),$.batches=y,$.isLoaded=!0};return W(async()=>{_.params.uuid||F()}),(a,o)=>{const y=c("BaseSelectSearch"),D=c("BaseSelect"),V=c("BaseInput"),m=c("DatePicker"),U=c("BaseTextarea"),g=c("BaseFieldset"),k=c("BaseBadge"),M=c("MediaUpload"),q=c("FormAction");return h(),R(q,{"pre-requisites":!0,onSetPreRequisites:O,"init-url":v,"init-form":d,form:n,setForm:L,redirect:"ResourceSyllabus",onResetMediaFiles:A},{default:p(()=>[u("div",Y,[u("div",Z,[$.isLoaded?(h(),R(y,{key:0,multiple:"",name:"batches",label:a.$trans("academic.batch.batch"),modelValue:n.batches,"onUpdate:modelValue":o[0]||(o[0]=e=>n.batches=e),error:l(t).batches,"onUpdate:error":o[1]||(o[1]=e=>l(t).batches=e),"value-prop":"uuid","init-search":$.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:p(e=>[N(b(e.value.course.name)+" - "+b(e.value.name),1)]),listOption:p(e=>[N(b(e.option.course.nameWithTerm)+" - "+b(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):z("",!0)]),u("div",x,[i(D,{modelValue:n.subject,"onUpdate:modelValue":o[2]||(o[2]=e=>n.subject=e),name:"subject",label:a.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:f.subjects,error:l(t).subject,"onUpdate:error":o[3]||(o[3]=e=>l(t).subject=e)},null,8,["modelValue","label","options","error"])])]),(h(!0),T(P,null,K(n.units,(e,r)=>(h(),R(g,{class:"mt-4",key:e.uuid},{legend:p(()=>[N(b(r+1)+". ",1),u("span",{class:"text-danger ml-2 cursor-pointer",onClick:s=>C(r)},o[8]||(o[8]=[u("i",{class:"fas fa-times-circle"},null,-1)]),8,ee)]),default:p(()=>[u("div",se,[u("div",ae,[i(V,{type:"text",modelValue:e.unitNumber,"onUpdate:modelValue":s=>e.unitNumber=s,name:`units.${r}.unitNumber`,label:a.$trans("resource.syllabus.props.unit_number"),error:l(t)[`units.${r}.unitNumber`],"onUpdate:error":s=>l(t)[`units.${r}.unitNumber`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),u("div",te,[i(V,{type:"text",modelValue:e.unitName,"onUpdate:modelValue":s=>e.unitName=s,name:`units.${r}.unitName`,label:a.$trans("resource.syllabus.props.unit_name"),error:l(t)[`units.${r}.unitName`],"onUpdate:error":s=>l(t)[`units.${r}.unitName`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),u("div",oe,[i(m,{modelValue:e.startDate,"onUpdate:modelValue":s=>e.startDate=s,name:`units.${r}.startDate`,label:a.$trans("resource.syllabus.props.start_date"),"no-clear":"",error:l(t)[`units.${r}.startDate`],"onUpdate:error":s=>l(t)[`units.${r}.startDate`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),u("div",re,[i(m,{modelValue:e.endDate,"onUpdate:modelValue":s=>e.endDate=s,name:`units.${r}.endDate`,label:a.$trans("resource.syllabus.props.end_date"),"no-clear":"",error:l(t)[`units.${r}.endDate`],"onUpdate:error":s=>l(t)[`units.${r}.endDate`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),u("div",le,[i(m,{modelValue:e.completionDate,"onUpdate:modelValue":s=>e.completionDate=s,name:`units.${r}.completionDate`,label:a.$trans("resource.syllabus.props.completion_date"),"no-clear":"",error:l(t)[`units.${r}.completionDate`],"onUpdate:error":s=>l(t)[`units.${r}.completionDate`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),u("div",ne,[i(U,{rows:1,modelValue:e.description,"onUpdate:modelValue":s=>e.description=s,name:`units.${r}.description`,label:a.$trans("resource.syllabus.props.description"),error:l(t)[`units.${r}.description`],"onUpdate:error":s=>l(t)[`units.${r}.description`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)),u("div",ue,[i(k,{design:"primary",onClick:F,class:"cursor-pointer"},{default:p(()=>[N(b(a.$trans("global.add",{attribute:a.$trans("resource.syllabus.props.unit")})),1)]),_:1})]),u("div",ie,[u("div",ce,[i(U,{rows:1,modelValue:n.remarks,"onUpdate:modelValue":o[4]||(o[4]=e=>n.remarks=e),name:"remarks",label:a.$trans("resource.syllabus.props.remarks"),error:l(t).remarks,"onUpdate:error":o[5]||(o[5]=e=>l(t).remarks=e)},null,8,["modelValue","label","error"])]),u("div",de,[i(M,{multiple:"",label:a.$trans("general.file"),module:"syllabus",media:n.media,"media-token":n.mediaToken,onIsUpdated:o[6]||(o[6]=e=>n.mediaUpdated=!0),onSetHash:o[7]||(o[7]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),be={name:"ResourceSyllabusAction"},$e=Object.assign(be,{setup(w){const _=H();return(d,S)=>{const v=c("PageHeaderAction"),t=c("PageHeader"),f=c("ParentTransition");return h(),T(P,null,[i(t,{title:d.$trans(l(_).meta.trans,{attribute:d.$trans(l(_).meta.label)}),navs:[{label:d.$trans("resource.resource"),path:"Resource"},{label:d.$trans("resource.syllabus.syllabus"),path:"ResourceSyllabusList"}]},{default:p(()=>[i(v,{name:"ResourceSyllabus",title:d.$trans("resource.syllabus.syllabus"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(f,{appear:"",visibility:!0},{default:p(()=>[i(pe)]),_:1})],64)}}});export{$e as default};
