import{u as ue,h as ye,j as Ve,G as le,H as Ue,g as V,l as T,n as Ie,J as qe,r as b,q,o as N,w as i,e as a,d as l,s as p,t as c,f as r,a as z,b as U,I as de,F as ve}from"./app-BAwPsakn.js";const Le={class:"mt-4 grid grid-cols-3 gap-6"},$e={class:"col-span-3 sm:col-span-1"},Ce={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},Pe={class:"col-span-3 sm:col-span-1"},Be={class:"col-span-3 sm:col-span-1"},De={class:"col-span-3 sm:col-span-1"},he={class:"grid grid-cols-3 gap-6"},Oe={class:"col-span-3 sm:col-span-1"},ke={class:"col-span-3 sm:col-span-1"},Re={class:"col-span-3 sm:col-span-1"},Se={class:"col-span-2 sm:col-span-1"},Te={class:"col-span-3 sm:col-span-1"},ze={class:"col-span-3 sm:col-span-1"},Fe={class:"mt-4 grid grid-cols-3 gap-6"},je={class:"col-span-3 sm:col-span-1"},He={class:"col-span-3 sm:col-span-1"},we={class:"col-span-3 sm:col-span-1"},Me={class:"col-span-2 sm:col-span-1"},Je={class:"col-span-3 sm:col-span-1"},Ke={class:"col-span-3 sm:col-span-1"},Qe={class:"mt-4 grid grid-cols-3 gap-6"},We={class:"col-span-3 sm:col-span-1"},Xe={class:"col-span-3 sm:col-span-1"},Ye={class:"col-span-3 sm:col-span-1"},Ze={class:"col-span-3 sm:col-span-1"},Ge={class:"col-span-3 sm:col-span-1"},xe={class:"col-span-3 sm:col-span-1"},_e={class:"col-span-3 sm:col-span-1"},et={class:"col-span-3 sm:col-span-1"},tt={class:"col-span-3 sm:col-span-1"},ot={class:"col-span-3 sm:col-span-1"},nt={key:0,class:"col-span-3 sm:col-span-1"},rt={key:1,class:"col-span-3 sm:col-span-1"},st={class:"grid grid-cols-3 gap-6"},at={class:"grid grid-cols-3 gap-6"},lt={class:"col-span-3 sm:col-span-1"},dt={class:"mt-4 grid grid-cols-3 gap-6"},ut={class:"grid grid-cols-1"},it={class:"col"},mt={name:"StudentProfileEditRequest"},pt=Object.assign(mt,{props:{student:{type:Object,default(){return{}}}},setup(f){const v=ue();ye(),Ve("emitter");const A=f,L={contactNumber:"",alternateContactNumber:"",email:"",fatherContactNumber:"",fatherEmail:"",fatherBirthDate:"",fatherOccupation:"",fatherAnnualIncome:"",motherContactNumber:"",motherEmail:"",motherBirthDate:"",motherOccupation:"",motherAnnualIncome:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",religion:"",category:"",caste:"",presentAddress:{},permanentAddress:{sameAsPresentAddress:!1},media:[],mediaUpdated:!1,mediaToken:le(),mediaHash:[]},D="student/profileEditRequest/",R="student/",s=Ue(D),ie=V("contact.uniqueIdNumber1Label"),me=V("contact.uniqueIdNumber2Label"),pe=V("contact.uniqueIdNumber3Label"),ce=V("contact.uniqueIdNumber4Label"),be=V("contact.uniqueIdNumber5Label"),fe=V("contact.enableCategoryField"),Ne=V("contact.enableCasteField"),$=T({genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[]}),o=T({...L}),C=T({religion:"",category:"",caste:"",isLoaded:!v.params.uuid}),ge=n=>{Object.assign($,n)},Ae=()=>{o.mediaToken=le(),o.mediaHash=[]};return Ie(async()=>{var g,d,E,I,y,P,h,O,k,t,F,j,H,w,M,J,K,Q,W,X,Y,Z,G,x,_,ee,te,oe,ne,re,se,ae;let n=(g=A.student)==null?void 0:g.contact,e=(n==null?void 0:n.guardians)||[];const m=e==null?void 0:e.find(S=>{var B;return((B=S.relation)==null?void 0:B.value)=="father"}),u=(d=n.guardians)==null?void 0:d.find(S=>{var B;return((B=S.relation)==null?void 0:B.value)=="mother"});Object.assign(L,{contactNumber:n.contactNumber,alternateContactNumber:(E=n.alternateRecords)==null?void 0:E.contactNumber,email:n.email,alternateEmail:(I=n.alternateRecords)==null?void 0:I.email,fatherContactNumber:((y=m==null?void 0:m.contact)==null?void 0:y.contactNumber)||n.fatherContactNumber,fatherEmail:((P=m==null?void 0:m.contact)==null?void 0:P.email)||n.fatherEmail,fatherBirthDate:(O=(h=m==null?void 0:m.contact)==null?void 0:h.birthDate)==null?void 0:O.value,fatherOccupation:(k=m==null?void 0:m.contact)==null?void 0:k.occupation,fatherAnnualIncome:(t=m==null?void 0:m.contact)==null?void 0:t.annualIncome,motherContactNumber:((F=u==null?void 0:u.contact)==null?void 0:F.contactNumber)||n.motherContactNumber,motherEmail:((j=u==null?void 0:u.contact)==null?void 0:j.email)||n.motherEmail,motherBirthDate:(w=(H=u==null?void 0:u.contact)==null?void 0:H.birthDate)==null?void 0:w.value,motherOccupation:(M=u==null?void 0:u.contact)==null?void 0:M.occupation,motherAnnualIncome:(J=u==null?void 0:u.contact)==null?void 0:J.annualIncome,uniqueIdNumber1:n.uniqueIdNumber1,uniqueIdNumber2:n.uniqueIdNumber2,uniqueIdNumber3:n.uniqueIdNumber3,uniqueIdNumber4:n.uniqueIdNumber4,uniqueIdNumber5:n.uniqueIdNumber5,birthPlace:n.birthPlace,bloodGroup:((K=n.bloodGroup)==null?void 0:K.value)||"",nationality:n.nationality,motherTongue:n.motherTongue,religion:((Q=n.religion)==null?void 0:Q.uuid)||"",category:((W=n.category)==null?void 0:W.uuid)||"",caste:((X=n.caste)==null?void 0:X.uuid)||"",presentAddress:{addressLine1:(Y=n.presentAddress)==null?void 0:Y.addressLine1,addressLine2:(Z=n.presentAddress)==null?void 0:Z.addressLine2,city:(G=n.presentAddress)==null?void 0:G.city,state:(x=n.presentAddress)==null?void 0:x.state,zipcode:(_=n.presentAddress)==null?void 0:_.zipcode,country:(ee=n.presentAddress)==null?void 0:ee.country},permanentAddress:{sameAsPresentAddress:n.sameAsPresentAddress,addressLine1:(te=n.permanentAddress)==null?void 0:te.addressLine1,addressLine2:(oe=n.permanentAddress)==null?void 0:oe.addressLine2,city:(ne=n.permanentAddress)==null?void 0:ne.city,state:(re=n.permanentAddress)==null?void 0:re.state,zipcode:(se=n.permanentAddress)==null?void 0:se.zipcode,country:(ae=n.permanentAddress)==null?void 0:ae.country}}),Object.assign(o,qe(L)),C.isLoaded=!0}),(n,e)=>{const m=b("BaseAlert"),u=b("BaseLabel"),g=b("TextContent"),d=b("BaseInput"),E=b("DatePicker"),I=b("BaseFieldset"),y=b("BaseSelect"),P=b("AddressInput"),h=b("BaseSwitch"),O=b("MediaUpload"),k=b("FormAction");return N(),q(k,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:ge,"pre-requisite-url":R,"init-url":D,uuid:r(v).params.uuid,"init-form":L,form:o,"keep-adding":!1,redirect:{name:"StudentProfileEditRequest",params:{uuid:f.student.uuid}},onResetMediaFiles:Ae},{default:i(()=>[a(m,{design:"info",size:"xs"},{default:i(()=>[p(c(n.$trans("student.edit_request.upload_document_info")),1)]),_:1}),l("div",Le,[l("div",$e,[a(u,null,{default:i(()=>[p(c(n.$trans("contact.props.name")),1)]),_:1}),a(g,{class:"mt-1"},{default:i(()=>[p(c(f.student.name),1)]),_:1})]),l("div",Ce,[a(u,null,{default:i(()=>[p(c(n.$trans("contact.props.gender")),1)]),_:1}),a(g,{class:"mt-1"},{default:i(()=>[p(c(f.student.contact.gender.label),1)]),_:1})]),l("div",Ee,[a(u,null,{default:i(()=>[p(c(n.$trans("contact.props.birth_date")),1)]),_:1}),a(g,{class:"mt-1"},{default:i(()=>[p(c(f.student.contact.birthDate.formatted),1)]),_:1})]),l("div",Pe,[a(d,{type:"text",modelValue:o.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=t=>o.contactNumber=t),name:"contactNumber",label:n.$trans("contact.props.primary_contact_number"),error:r(s).contactNumber,"onUpdate:error":e[1]||(e[1]=t=>r(s).contactNumber=t)},null,8,["modelValue","label","error"])]),l("div",Be,[a(d,{type:"text",modelValue:o.alternateContactNumber,"onUpdate:modelValue":e[2]||(e[2]=t=>o.alternateContactNumber=t),name:"alternateContactNumber",label:n.$trans("contact.props.alternate_contact_number"),error:r(s).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=t=>r(s).alternateContactNumber=t)},null,8,["modelValue","label","error"])]),l("div",De,[a(d,{type:"text",modelValue:o.email,"onUpdate:modelValue":e[4]||(e[4]=t=>o.email=t),name:"email",label:n.$trans("contact.props.email"),error:r(s).email,"onUpdate:error":e[5]||(e[5]=t=>r(s).email=t)},null,8,["modelValue","label","error"])])]),a(I,{class:"mt-4"},{legend:i(()=>[p(c(n.$trans("student.props.parent")),1)]),default:i(()=>[l("div",he,[l("div",Oe,[a(u,null,{default:i(()=>[p(c(n.$trans("contact.props.father_name")),1)]),_:1}),a(g,{class:"mt-1"},{default:i(()=>[p(c(f.student.contact.fatherName),1)]),_:1})]),l("div",ke,[a(d,{type:"text",modelValue:o.fatherContactNumber,"onUpdate:modelValue":e[6]||(e[6]=t=>o.fatherContactNumber=t),name:"fatherContactNumber",label:n.$trans("contact.props.contact_number"),error:r(s).fatherContactNumber,"onUpdate:error":e[7]||(e[7]=t=>r(s).fatherContactNumber=t)},null,8,["modelValue","label","error"])]),l("div",Re,[a(d,{type:"text",modelValue:o.fatherEmail,"onUpdate:modelValue":e[8]||(e[8]=t=>o.fatherEmail=t),name:"fatherEmail",label:n.$trans("contact.props.email"),error:r(s).fatherEmail,"onUpdate:error":e[9]||(e[9]=t=>r(s).fatherEmail=t)},null,8,["modelValue","label","error"])]),l("div",Se,[a(E,{modelValue:o.fatherBirthDate,"onUpdate:modelValue":e[10]||(e[10]=t=>o.fatherBirthDate=t),name:"fatherBirthDate",label:n.$trans("contact.props.birth_date"),error:r(s).fatherBirthDate,"onUpdate:error":e[11]||(e[11]=t=>r(s).fatherBirthDate=t)},null,8,["modelValue","label","error"])]),l("div",Te,[a(d,{type:"text",modelValue:o.fatherOccupation,"onUpdate:modelValue":e[12]||(e[12]=t=>o.fatherOccupation=t),name:"fatherOccupation",label:n.$trans("contact.props.occupation"),error:r(s).fatherOccupation,"onUpdate:error":e[13]||(e[13]=t=>r(s).fatherOccupation=t)},null,8,["modelValue","label","error"])]),l("div",ze,[a(d,{type:"text",modelValue:o.fatherAnnualIncome,"onUpdate:modelValue":e[14]||(e[14]=t=>o.fatherAnnualIncome=t),name:"fatherAnnualIncome",label:n.$trans("contact.props.annual_income"),error:r(s).fatherAnnualIncome,"onUpdate:error":e[15]||(e[15]=t=>r(s).fatherAnnualIncome=t)},null,8,["modelValue","label","error"])])]),l("div",Fe,[l("div",je,[a(u,null,{default:i(()=>[p(c(n.$trans("contact.props.mother_name")),1)]),_:1}),a(g,{class:"mt-1"},{default:i(()=>[p(c(f.student.contact.motherName),1)]),_:1})]),l("div",He,[a(d,{type:"text",modelValue:o.motherContactNumber,"onUpdate:modelValue":e[16]||(e[16]=t=>o.motherContactNumber=t),name:"motherContactNumber",label:n.$trans("contact.props.contact_number"),error:r(s).motherContactNumber,"onUpdate:error":e[17]||(e[17]=t=>r(s).motherContactNumber=t)},null,8,["modelValue","label","error"])]),l("div",we,[a(d,{type:"text",modelValue:o.motherEmail,"onUpdate:modelValue":e[18]||(e[18]=t=>o.motherEmail=t),name:"motherEmail",label:n.$trans("contact.props.email"),error:r(s).motherEmail,"onUpdate:error":e[19]||(e[19]=t=>r(s).motherEmail=t)},null,8,["modelValue","label","error"])]),l("div",Me,[a(E,{modelValue:o.motherBirthDate,"onUpdate:modelValue":e[20]||(e[20]=t=>o.motherBirthDate=t),name:"motherBirthDate",label:n.$trans("contact.props.birth_date"),error:r(s).motherBirthDate,"onUpdate:error":e[21]||(e[21]=t=>r(s).motherBirthDate=t)},null,8,["modelValue","label","error"])]),l("div",Je,[a(d,{type:"text",modelValue:o.motherOccupation,"onUpdate:modelValue":e[22]||(e[22]=t=>o.motherOccupation=t),name:"motherOccupation",label:n.$trans("contact.props.occupation"),error:r(s).motherOccupation,"onUpdate:error":e[23]||(e[23]=t=>r(s).motherOccupation=t)},null,8,["modelValue","label","error"])]),l("div",Ke,[a(d,{type:"text",modelValue:o.motherAnnualIncome,"onUpdate:modelValue":e[24]||(e[24]=t=>o.motherAnnualIncome=t),name:"motherAnnualIncome",label:n.$trans("contact.props.annual_income"),error:r(s).motherAnnualIncome,"onUpdate:error":e[25]||(e[25]=t=>r(s).motherAnnualIncome=t)},null,8,["modelValue","label","error"])])])]),_:1}),l("div",Qe,[l("div",We,[a(d,{type:"text",modelValue:o.uniqueIdNumber1,"onUpdate:modelValue":e[26]||(e[26]=t=>o.uniqueIdNumber1=t),name:"uniqueIdNumber1",label:r(ie),error:r(s).uniqueIdNumber1,"onUpdate:error":e[27]||(e[27]=t=>r(s).uniqueIdNumber1=t)},null,8,["modelValue","label","error"])]),l("div",Xe,[a(d,{type:"text",modelValue:o.uniqueIdNumber2,"onUpdate:modelValue":e[28]||(e[28]=t=>o.uniqueIdNumber2=t),name:"uniqueIdNumber2",label:r(me),error:r(s).uniqueIdNumber2,"onUpdate:error":e[29]||(e[29]=t=>r(s).uniqueIdNumber2=t)},null,8,["modelValue","label","error"])]),l("div",Ye,[a(d,{type:"text",modelValue:o.uniqueIdNumber3,"onUpdate:modelValue":e[30]||(e[30]=t=>o.uniqueIdNumber3=t),name:"uniqueIdNumber3",label:r(pe),error:r(s).uniqueIdNumber3,"onUpdate:error":e[31]||(e[31]=t=>r(s).uniqueIdNumber3=t)},null,8,["modelValue","label","error"])]),l("div",Ze,[a(d,{type:"text",modelValue:o.uniqueIdNumber4,"onUpdate:modelValue":e[32]||(e[32]=t=>o.uniqueIdNumber4=t),name:"uniqueIdNumber4",label:r(ce),error:r(s).uniqueIdNumber4,"onUpdate:error":e[33]||(e[33]=t=>r(s).uniqueIdNumber4=t)},null,8,["modelValue","label","error"])]),l("div",Ge,[a(d,{type:"text",modelValue:o.uniqueIdNumber5,"onUpdate:modelValue":e[34]||(e[34]=t=>o.uniqueIdNumber5=t),name:"uniqueIdNumber5",label:r(be),error:r(s).uniqueIdNumber5,"onUpdate:error":e[35]||(e[35]=t=>r(s).uniqueIdNumber5=t)},null,8,["modelValue","label","error"])]),l("div",xe,[a(d,{type:"text",modelValue:o.birthPlace,"onUpdate:modelValue":e[36]||(e[36]=t=>o.birthPlace=t),name:"birthPlace",label:n.$trans("contact.props.birth_place"),error:r(s).birthPlace,"onUpdate:error":e[37]||(e[37]=t=>r(s).birthPlace=t)},null,8,["modelValue","label","error"])]),l("div",_e,[a(d,{type:"text",modelValue:o.nationality,"onUpdate:modelValue":e[38]||(e[38]=t=>o.nationality=t),name:"nationality",label:n.$trans("contact.props.nationality"),error:r(s).nationality,"onUpdate:error":e[39]||(e[39]=t=>r(s).nationality=t)},null,8,["modelValue","label","error"])]),l("div",et,[a(d,{type:"text",modelValue:o.motherTongue,"onUpdate:modelValue":e[40]||(e[40]=t=>o.motherTongue=t),name:"motherTongue",label:n.$trans("contact.props.mother_tongue"),error:r(s).motherTongue,"onUpdate:error":e[41]||(e[41]=t=>r(s).motherTongue=t)},null,8,["modelValue","label","error"])]),l("div",tt,[C.isLoaded?(N(),q(y,{key:0,modelValue:o.bloodGroup,"onUpdate:modelValue":e[42]||(e[42]=t=>o.bloodGroup=t),name:"bloodGroup",label:n.$trans("contact.props.blood_group"),options:$.bloodGroups,error:r(s).bloodGroup,"onUpdate:error":e[43]||(e[43]=t=>r(s).bloodGroup=t)},null,8,["modelValue","label","options","error"])):U("",!0)]),l("div",ot,[C.isLoaded?(N(),q(y,{key:0,name:"religion",label:n.$trans("global.select",{attribute:n.$trans("contact.religion.religion")}),modelValue:o.religion,"onUpdate:modelValue":e[44]||(e[44]=t=>o.religion=t),error:r(s).religion,"onUpdate:error":e[45]||(e[45]=t=>r(s).religion=t),options:$.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):U("",!0)]),r(fe)?(N(),z("div",nt,[C.isLoaded?(N(),q(y,{key:0,name:"category",label:n.$trans("global.select",{attribute:n.$trans("contact.category.category")}),modelValue:o.category,"onUpdate:modelValue":e[46]||(e[46]=t=>o.category=t),error:r(s).category,"onUpdate:error":e[47]||(e[47]=t=>r(s).category=t),options:$.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):U("",!0)])):U("",!0),r(Ne)?(N(),z("div",rt,[C.isLoaded?(N(),q(y,{key:0,name:"caste",label:n.$trans("global.select",{attribute:n.$trans("contact.caste.caste")}),modelValue:o.caste,"onUpdate:modelValue":e[48]||(e[48]=t=>o.caste=t),error:r(s).caste,"onUpdate:error":e[49]||(e[49]=t=>r(s).caste=t),options:$.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):U("",!0)])):U("",!0)]),a(I,{class:"mt-4"},{legend:i(()=>[p(c(n.$trans("contact.props.present_address")),1)]),default:i(()=>[l("div",st,[a(P,{prefix:"presentAddress",addressLine1:o.presentAddress.addressLine1,"onUpdate:addressLine1":e[50]||(e[50]=t=>o.presentAddress.addressLine1=t),addressLine2:o.presentAddress.addressLine2,"onUpdate:addressLine2":e[51]||(e[51]=t=>o.presentAddress.addressLine2=t),city:o.presentAddress.city,"onUpdate:city":e[52]||(e[52]=t=>o.presentAddress.city=t),state:o.presentAddress.state,"onUpdate:state":e[53]||(e[53]=t=>o.presentAddress.state=t),zipcode:o.presentAddress.zipcode,"onUpdate:zipcode":e[54]||(e[54]=t=>o.presentAddress.zipcode=t),country:o.presentAddress.country,"onUpdate:country":e[55]||(e[55]=t=>o.presentAddress.country=t),formErrors:r(s),"onUpdate:formErrors":e[56]||(e[56]=t=>de(s)?s.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1}),a(I,{class:"mt-4"},{legend:i(()=>[p(c(n.$trans("contact.props.permanent_address")),1)]),default:i(()=>[l("div",at,[l("div",lt,[a(h,{modelValue:o.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[57]||(e[57]=t=>o.permanentAddress.sameAsPresentAddress=t),name:"sameAsPresentAddress",label:n.$trans("contact.props.same_as_present_address"),error:r(s).sameAsPresentAddress,"onUpdate:error":e[58]||(e[58]=t=>r(s).sameAsPresentAddress=t)},null,8,["modelValue","label","error"])])]),l("div",dt,[o.permanentAddress.sameAsPresentAddress?U("",!0):(N(),q(P,{key:0,prefix:"permanentAddress",addressLine1:o.permanentAddress.addressLine1,"onUpdate:addressLine1":e[59]||(e[59]=t=>o.permanentAddress.addressLine1=t),addressLine2:o.permanentAddress.addressLine2,"onUpdate:addressLine2":e[60]||(e[60]=t=>o.permanentAddress.addressLine2=t),city:o.permanentAddress.city,"onUpdate:city":e[61]||(e[61]=t=>o.permanentAddress.city=t),state:o.permanentAddress.state,"onUpdate:state":e[62]||(e[62]=t=>o.permanentAddress.state=t),zipcode:o.permanentAddress.zipcode,"onUpdate:zipcode":e[63]||(e[63]=t=>o.permanentAddress.zipcode=t),country:o.permanentAddress.country,"onUpdate:country":e[64]||(e[64]=t=>o.permanentAddress.country=t),formErrors:r(s),"onUpdate:formErrors":e[65]||(e[65]=t=>de(s)?s.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1}),l("div",ut,[l("div",it,[a(O,{multiple:"",label:n.$trans("general.file"),module:"contact_edit_request",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[66]||(e[66]=t=>o.mediaUpdated=!0),onSetHash:e[67]||(e[67]=t=>o.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","form","redirect"])}}}),ct={name:"StudentProfileEditRequestAction"},ft=Object.assign(ct,{props:{student:{type:Object,default(){return{}}}},setup(f){const v=ue();return(A,L)=>{const D=b("PageHeaderAction"),R=b("PageHeader"),s=b("ParentTransition");return N(),z(ve,null,[a(R,{title:A.$trans(r(v).meta.trans,{attribute:A.$trans(r(v).meta.label)}),navs:[{label:A.$trans("student.student"),path:"StudentList"},{label:f.student.contact.name,path:{name:"StudentShow",params:{uuid:f.student.uuid}}},{label:A.$trans("student.edit_request.edit_request"),path:{name:"StudentProfileEditRequest",params:{uuid:f.student.uuid}}}]},{default:i(()=>[a(D,{name:"StudentProfileEditRequest",title:A.$trans("student.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(s,{appear:"",visibility:!0},{default:i(()=>[a(pt,{student:f.student},null,8,["student"])]),_:1})],64)}}});export{ft as default};
