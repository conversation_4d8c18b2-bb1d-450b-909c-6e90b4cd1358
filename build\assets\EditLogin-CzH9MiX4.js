import{u as H,h as L,j as T,l as w,H as A,c as D,n as I,J,r as p,a as g,o as m,q as y,b as V,e as n,f as s,w as U,F as C,d as u,s as M,t as z}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={key:0,class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},_={class:"col-span-3 sm:col-span-1"},h={class:"col-span-3 sm:col-span-1"},ee={name:"ContactEditLogin"},te=Object.assign(ee,{props:{contact:{type:Object,default(){return{}}}},setup(d){const B=H(),j=L(),q=T("emitter"),$=d,c={username:"",email:"",password:"",passwordConfirmation:"",roles:[]},v="contact/",F="user/",k=w({}),r=A(v),f=D(()=>$.contact.user),l=w({isValidated:!1,existingUser:null}),a=w({...c}),P=t=>{Object.assign(k,t)},R=t=>{l.isValidated=!0,l.existingUser=t||null,a.username=t==null?void 0:t.username},O=()=>{q.emit("contactUpdated"),j.push({name:"ContactShowLogin",params:{uuid:$.contact.uuid}})};return I(async()=>{var t,e,b;l.isValidated=!!f.value,l.existingUser=f.value,Object.assign(c,{username:(t=f.value)==null?void 0:t.username,email:(e=f.value)==null?void 0:e.email,roles:((b=f.value)==null?void 0:b.roles.map(i=>i.uuid))||[]}),Object.assign(a,J(c))}),(t,e)=>{const b=p("PageHeader"),i=p("BaseInput"),E=p("BaseButton"),S=p("FormAction"),N=p("BaseSelect"),x=p("ParentTransition");return m(),g(C,null,[d.contact.uuid?(m(),y(b,{key:0,title:t.$trans(s(B).meta.trans,{attribute:t.$trans(s(B).meta.label)}),navs:[{label:t.$trans("contact.contact"),path:"Contact"},{label:d.contact.name,path:{name:"ContactShow",params:{uuid:d.contact.uuid}}}]},null,8,["title","navs"])):V("",!0),n(x,{appear:"",visibility:!0},{default:U(()=>[d.contact.uuid?(m(),g(C,{key:0},[l.isValidated?(m(),y(S,{key:1,"no-data-fetch":"","pre-requisites":!0,"pre-requisite-url":F,onSetPreRequisites:P,"init-url":v,action:l.existingUser?"updateUser":"createUser","init-form":c,form:a,"after-submit":O},{default:U(()=>[u("div",Q,[u("div",W,[n(i,{readonly:"",disabled:"",type:"text",modelValue:a.email,"onUpdate:modelValue":e[2]||(e[2]=o=>a.email=o),name:"email",label:t.$trans("contact.login.props.email"),error:s(r).email,"onUpdate:error":e[3]||(e[3]=o=>s(r).email=o)},null,8,["modelValue","label","error"])]),l.existingUser?(m(),g("div",X,[n(i,{readonly:"",disabled:"",type:"text",modelValue:a.username,"onUpdate:modelValue":e[4]||(e[4]=o=>a.username=o),name:"username",label:t.$trans("contact.login.props.username"),error:s(r).username,"onUpdate:error":e[5]||(e[5]=o=>s(r).username=o)},null,8,["modelValue","label","error"])])):V("",!0),l.existingUser?V("",!0):(m(),g(C,{key:1},[u("div",Y,[n(i,{type:"text",modelValue:a.username,"onUpdate:modelValue":e[6]||(e[6]=o=>a.username=o),name:"username",label:t.$trans("contact.login.props.username"),error:s(r).username,"onUpdate:error":e[7]||(e[7]=o=>s(r).username=o)},null,8,["modelValue","label","error"])]),u("div",Z,[n(i,{type:"password",modelValue:a.password,"onUpdate:modelValue":e[8]||(e[8]=o=>a.password=o),name:"password",label:t.$trans("contact.login.props.password"),error:s(r).password,"onUpdate:error":e[9]||(e[9]=o=>s(r).password=o)},null,8,["modelValue","label","error"])]),u("div",_,[n(i,{type:"password",modelValue:a.passwordConfirmation,"onUpdate:modelValue":e[10]||(e[10]=o=>a.passwordConfirmation=o),name:"passwordConfirmation",label:t.$trans("contact.login.props.password_confirmation"),error:s(r).passwordConfirmation,"onUpdate:error":e[11]||(e[11]=o=>s(r).passwordConfirmation=o)},null,8,["modelValue","label","error"])])],64)),u("div",h,[n(N,{modelValue:a.roles,"onUpdate:modelValue":e[12]||(e[12]=o=>a.roles=o),name:"roles",label:t.$trans("contact.login.props.role"),options:k.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:s(r).roles,"onUpdate:error":e[13]||(e[13]=o=>s(r).roles=o)},null,8,["modelValue","label","options","error"])])])]),_:1},8,["action","form"])):(m(),y(S,{key:0,"no-action-button":"","no-data-fetch":"","init-url":v,action:"confirmUser","init-form":c,form:a,"after-submit":R,"stay-on":"",redirect:{name:"ContactShowLogin",params:{uuid:d.contact.uuid}}},{default:U(()=>[u("div",G,[u("div",K,[n(i,{type:"text",modelValue:a.email,"onUpdate:modelValue":e[0]||(e[0]=o=>a.email=o),name:"email",label:t.$trans("contact.login.props.email"),error:s(r).email,"onUpdate:error":e[1]||(e[1]=o=>s(r).email=o)},null,8,["modelValue","label","error"])])]),n(E,{class:"mt-4",type:"submit"},{default:U(()=>[M(z(t.$trans("general.validate")),1)]),_:1})]),_:1},8,["form","redirect"]))],64)):V("",!0)]),_:1})],64)}}});export{te as default};
