import{u as g,H as T,l as h,r as o,q as _,o as d,w as p,d as f,e as l,f as a,a as v,b as C,F}from"./app-BAwPsakn.js";const B={class:"grid grid-cols-3 gap-6"},P={class:"col-span-3 sm:col-span-1"},R={name:"RoleForm"},V=Object.assign(R,{setup(r){const t=g(),e={name:""},i="team/role/",s=T(i),n=h({...e});return(u,m)=>{const b=o("BaseInput"),$=o("FormAction");return d(),_($,{"no-data-fetch":"","init-url":i,uuid:a(t).params.uuid,"init-form":e,form:n,redirect:{name:"TeamConfigRole",params:{uuid:a(t).params.uuid}}},{default:p(()=>[f("div",B,[f("div",P,[l(b,{type:"text",modelValue:n.name,"onUpdate:modelValue":m[0]||(m[0]=c=>n.name=c),name:"name",label:u.$trans("team.config.role.props.name"),error:a(s).name,"onUpdate:error":m[1]||(m[1]=c=>a(s).name=c),autofocus:""},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form","redirect"])}}}),A={name:"RoleAction"},k=Object.assign(A,{props:{team:{type:Object,default(){return{name:""}}}},setup(r){const t=g();return(e,i)=>{const s=o("PageHeaderAction"),n=o("PageHeader"),u=o("ParentTransition");return d(),v(F,null,[r.team.uuid?(d(),_(n,{key:0,title:e.$trans(a(t).meta.trans,{attribute:e.$trans(a(t).meta.label)}),navs:[{label:e.$trans("team.team"),path:"TeamList"},{label:r.team.name,path:{name:"TeamShow",params:{uuid:r.team.uuid}}},{label:e.$trans("team.config.config"),path:"TeamConfig"},{label:e.$trans("team.config.role.role"),path:"TeamConfigRole"}]},{default:p(()=>[l(s,{name:"TeamConfigRole",title:e.$trans("team.config.role.role"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"])):C("",!0),l(u,{appear:"",visibility:!0},{default:p(()=>[l(V)]),_:1})],64)}}});export{k as default};
