import{u as P,h as S,l as C,r as s,a as _,o as c,e as n,w as e,f as r,q as y,b,d,s as i,t as o,F as v,v as A,y as N}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},I={class:"flex text-xs"},j={class:"w-1/2"},O=["innerHTML"],E=["innerHTML"],F={name:"RecruitmentVacancyShow"},z=Object.assign(F,{setup(q){const m=P(),f=S(),g={},$="recruitment/vacancy/",t=C({...g}),B=a=>{Object.assign(t,a)};return(a,p)=>{const h=s("PageHeaderAction"),V=s("PageHeader"),l=s("BaseDataView"),T=s("TextMuted"),w=s("ListMedia"),R=s("BaseButton"),H=s("ShowButton"),M=s("BaseCard"),k=s("ShowItem"),L=s("ParentTransition");return c(),_(v,null,[n(V,{title:a.$trans(r(m).meta.trans,{attribute:a.$trans(r(m).meta.label)}),navs:[{label:a.$trans("recruitment.recruitment"),path:"Recruitment"},{label:a.$trans("recruitment.vacancy.vacancy"),path:"RecruitmentVacancy"}]},{default:e(()=>[n(h,{name:"RecruitmentVacancy",title:a.$trans("recruitment.vacancy.vacancy"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(L,{appear:"",visibility:!0},{default:e(()=>[n(k,{"init-url":$,uuid:r(m).params.uuid,"module-uuid":r(m).params.muuid,onSetItem:B,onRedirectTo:p[1]||(p[1]=u=>r(f).push({name:"RecruitmentVacancy",params:{uuid:t.uuid}}))},{default:e(()=>[t.uuid?(c(),y(M,{key:0},{title:e(()=>[i(" #"+o(t.codeNumber),1)]),footer:e(()=>[n(H,null,{default:e(()=>[r(N)("job-vacancy:edit")?(c(),y(R,{key:0,design:"primary",onClick:p[0]||(p[0]=u=>r(f).push({name:"RecruitmentVacancyEdit",params:{uuid:t.uuid}}))},{default:e(()=>[i(o(a.$trans("general.edit")),1)]),_:1})):b("",!0)]),_:1})]),default:e(()=>[d("dl",D,[n(l,{label:a.$trans("recruitment.vacancy.props.title"),class:"col-span-1 sm:col-span-2"},{default:e(()=>[i(o(t.title),1)]),_:1},8,["label"]),n(l,{label:a.$trans("employee.designation.designation")},{default:e(()=>[(c(!0),_(v,null,A(t.records,u=>(c(),_("div",I,[d("div",j,[i(o(u.designation.name)+" ",1),n(T,null,{default:e(()=>[i(o(u.employmentType.name),1)]),_:2},1024)]),d("div",null,o(u.numberOfPositions),1)]))),256))]),_:1},8,["label"]),n(l,{label:a.$trans("recruitment.vacancy.props.last_application_date")},{default:e(()=>[i(o(t.lastApplicationDate.formatted),1)]),_:1},8,["label"]),n(l,{label:a.$trans("recruitment.vacancy.props.description"),class:"col-span-1 sm:col-span-2"},{default:e(()=>[d("div",{innerHTML:t.description},null,8,O)]),_:1},8,["label"]),t.responsibility?(c(),y(l,{key:0,label:a.$trans("recruitment.vacancy.props.responsibility"),class:"col-span-1 sm:col-span-2"},{default:e(()=>[d("div",{innerHTML:t.responsibility},null,8,E)]),_:1},8,["label"])):b("",!0),n(l,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[n(w,{media:t.media,url:`/app/recruitment/vacancies/${t.uuid}/`},null,8,["media","url"])]),_:1}),n(l,{label:a.$trans("general.created_at")},{default:e(()=>[i(o(t.createdAt.formatted),1)]),_:1},8,["label"]),n(l,{label:a.$trans("general.updated_at")},{default:e(()=>[i(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):b("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{z as default};
