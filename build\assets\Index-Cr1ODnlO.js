import{u as T,j as E,H as L,l as w,K as B,n as F,r as d,a as z,o as U,q as j,b as H,e as t,f as a,w as m,d as i,s as u,F as k,J as y}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-2 gap-4"},P={class:"col-span-2 sm:col-span-1"},I={class:"col-span-2 sm:col-span-1"},A={class:"col-span-2 sm:col-span-1"},$={class:"col-span-2 sm:col-span-1"},q={class:"grid grid-cols-3 gap-4"},D={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},G={class:"grid grid-cols-3 gap-4"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"grid grid-cols-4 gap-6"},_={class:"col-span-4 sm:col-span-1"},ee={class:"col-span-4 sm:col-span-1"},le={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},ne={class:"grid grid-cols-4 gap-6"},oe={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-1"},te={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},se={class:"grid grid-cols-4 gap-6"},me={class:"col-span-4 sm:col-span-1"},de={class:"col-span-4 sm:col-span-1"},pe={class:"col-span-4 sm:col-span-1"},ge={class:"col-span-4 sm:col-span-1"},ue={class:"grid grid-cols-4 gap-6"},ce={class:"col-span-4 sm:col-span-1"},be={class:"col-span-4 sm:col-span-1"},Ve={class:"col-span-4 sm:col-span-1"},fe={class:"col-span-4 sm:col-span-1"},Ue={class:"grid grid-cols-4 gap-6"},ye={class:"col-span-4 sm:col-span-1"},Ne={class:"col-span-4 sm:col-span-1"},ve={class:"col-span-4 sm:col-span-1"},xe={class:"col-span-4 sm:col-span-1"},he={},Te=Object.assign(he,{__name:"Index",props:{team:{type:Object,default(){return{name:""}}}},setup(p){const N=T(),r=E("$trans"),v=p,V="team/",o=L(V),c={name:"",title1:"",title2:"",title3:"",email:"",phone:"",fax:"",website:"",addressLine1:"",addressLine2:"",city:"",state:"",zipcode:"",country:"",incharge1:{title:"",name:"",contactNumber:"",email:""},incharge2:{title:"",name:"",contactNumber:"",email:""},incharge3:{title:"",name:"",contactNumber:"",email:""},incharge4:{title:"",name:"",contactNumber:"",email:""},incharge5:{title:"",name:"",contactNumber:"",email:""}},n=w({...c});return B(()=>v.team.config,f=>{Object.assign(c,y(f)),Object.assign(n,y(c))}),F(()=>{}),(f,e)=>{const x=d("PageHeader"),b=d("CardHeader"),s=d("BaseInput"),g=d("BaseFieldset"),h=d("FormAction"),C=d("ParentTransition");return U(),z(k,null,[p.team.uuid?(U(),j(x,{key:0,title:a(r)(a(N).meta.label),navs:[{label:a(r)("team.team"),path:"TeamList"},{label:p.team.name,path:{name:"TeamShow",params:{uuid:p.team.uuid}}},{label:a(r)("team.config.config"),path:"TeamConfig"}]},null,8,["title","navs"])):H("",!0),t(C,{appear:"",visibility:!0},{default:m(()=>[t(h,{"init-url":V,"no-data-fetch":"","init-form":c,form:n,action:"storeConfig","stay-on":"",redirect:{name:"TeamConfig",params:{uuid:p.team.uuid}}},{default:m(()=>[t(b,{first:"",title:a(r)("team.config.general.about"),description:a(r)("team.config.general.about_info")},null,8,["title","description"]),i("div",O,[i("div",P,[t(s,{type:"text",modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=l=>n.name=l),name:"name",label:a(r)("team.config.general.props.name"),error:a(o).name,"onUpdate:error":e[1]||(e[1]=l=>a(o).name=l)},null,8,["modelValue","label","error"])]),i("div",I,[t(s,{type:"text",modelValue:n.title1,"onUpdate:modelValue":e[2]||(e[2]=l=>n.title1=l),name:"title1",label:a(r)("team.config.general.props.title1"),error:a(o).title1,"onUpdate:error":e[3]||(e[3]=l=>a(o).title1=l)},null,8,["modelValue","label","error"])]),i("div",A,[t(s,{type:"text",modelValue:n.title2,"onUpdate:modelValue":e[4]||(e[4]=l=>n.title2=l),name:"title2",label:a(r)("team.config.general.props.title2"),error:a(o).title2,"onUpdate:error":e[5]||(e[5]=l=>a(o).title2=l)},null,8,["modelValue","label","error"])]),i("div",$,[t(s,{type:"text",modelValue:n.title3,"onUpdate:modelValue":e[6]||(e[6]=l=>n.title3=l),name:"title3",label:a(r)("team.config.general.props.title3"),error:a(o).title3,"onUpdate:error":e[7]||(e[7]=l=>a(o).title3=l)},null,8,["modelValue","label","error"])])]),t(b,{title:a(r)("team.config.general.address"),description:a(r)("team.config.general.address_info")},null,8,["title","description"]),i("div",q,[i("div",D,[t(s,{type:"text",modelValue:n.addressLine1,"onUpdate:modelValue":e[8]||(e[8]=l=>n.addressLine1=l),name:"addressLine1",label:a(r)("team.config.general.props.address_line1"),error:a(o).addressLine1,"onUpdate:error":e[9]||(e[9]=l=>a(o).addressLine1=l)},null,8,["modelValue","label","error"])]),i("div",J,[t(s,{type:"text",modelValue:n.addressLine2,"onUpdate:modelValue":e[10]||(e[10]=l=>n.addressLine2=l),name:"addressLine2",label:a(r)("team.config.general.props.address_line2"),error:a(o).addressLine2,"onUpdate:error":e[11]||(e[11]=l=>a(o).addressLine2=l)},null,8,["modelValue","label","error"])]),i("div",K,[t(s,{type:"text",modelValue:n.city,"onUpdate:modelValue":e[12]||(e[12]=l=>n.city=l),name:"city",label:a(r)("team.config.general.props.city"),error:a(o).city,"onUpdate:error":e[13]||(e[13]=l=>a(o).city=l)},null,8,["modelValue","label","error"])]),i("div",M,[t(s,{type:"text",modelValue:n.state,"onUpdate:modelValue":e[14]||(e[14]=l=>n.state=l),name:"state",label:a(r)("team.config.general.props.state"),error:a(o).state,"onUpdate:error":e[15]||(e[15]=l=>a(o).state=l)},null,8,["modelValue","label","error"])]),i("div",R,[t(s,{type:"text",modelValue:n.zipcode,"onUpdate:modelValue":e[16]||(e[16]=l=>n.zipcode=l),name:"zipcode",label:a(r)("team.config.general.props.zipcode"),error:a(o).zipcode,"onUpdate:error":e[17]||(e[17]=l=>a(o).zipcode=l)},null,8,["modelValue","label","error"])]),i("div",S,[t(s,{type:"text",modelValue:n.country,"onUpdate:modelValue":e[18]||(e[18]=l=>n.country=l),name:"country",label:a(r)("team.config.general.props.country"),error:a(o).country,"onUpdate:error":e[19]||(e[19]=l=>a(o).country=l)},null,8,["modelValue","label","error"])])]),t(b,{title:a(r)("team.config.general.contact"),description:a(r)("team.config.general.contact_info")},null,8,["title","description"]),i("div",G,[i("div",Q,[t(s,{type:"text",modelValue:n.email,"onUpdate:modelValue":e[20]||(e[20]=l=>n.email=l),name:"email",label:a(r)("team.config.general.props.email"),error:a(o).email,"onUpdate:error":e[21]||(e[21]=l=>a(o).email=l)},null,8,["modelValue","label","error"])]),i("div",W,[t(s,{type:"text",modelValue:n.phone,"onUpdate:modelValue":e[22]||(e[22]=l=>n.phone=l),name:"phone",label:a(r)("team.config.general.props.phone"),error:a(o).phone,"onUpdate:error":e[23]||(e[23]=l=>a(o).phone=l)},null,8,["modelValue","label","error"])]),i("div",X,[t(s,{type:"text",modelValue:n.fax,"onUpdate:modelValue":e[24]||(e[24]=l=>n.fax=l),name:"fax",label:a(r)("team.config.general.props.fax"),error:a(o).fax,"onUpdate:error":e[25]||(e[25]=l=>a(o).fax=l)},null,8,["modelValue","label","error"])]),i("div",Y,[t(s,{type:"text",modelValue:n.website,"onUpdate:modelValue":e[26]||(e[26]=l=>n.website=l),name:"website",label:a(r)("team.config.general.props.website"),error:a(o).website,"onUpdate:error":e[27]||(e[27]=l=>a(o).website=l)},null,8,["modelValue","label","error"])])]),t(b,{title:a(r)("team.config.general.incharge"),description:a(r)("team.config.general.incharge_info")},null,8,["title","description"]),t(g,{class:"mt-4"},{legend:m(()=>e[68]||(e[68]=[u(" 1. ")])),default:m(()=>[i("div",Z,[i("div",_,[t(s,{type:"text",modelValue:n.incharge1.title,"onUpdate:modelValue":e[28]||(e[28]=l=>n.incharge1.title=l),name:"incharge1Title",label:a(r)("team.config.general.props.incharge_title"),error:a(o).incharge1Title,"onUpdate:error":e[29]||(e[29]=l=>a(o).incharge1Title=l)},null,8,["modelValue","label","error"])]),i("div",ee,[t(s,{type:"text",modelValue:n.incharge1.name,"onUpdate:modelValue":e[30]||(e[30]=l=>n.incharge1.name=l),name:"incharge1Name",label:a(r)("team.config.general.props.incharge_name"),error:a(o).incharge1Name,"onUpdate:error":e[31]||(e[31]=l=>a(o).incharge1Name=l)},null,8,["modelValue","label","error"])]),i("div",le,[t(s,{type:"text",modelValue:n.incharge1.email,"onUpdate:modelValue":e[32]||(e[32]=l=>n.incharge1.email=l),name:"incharge1Email",label:a(r)("team.config.general.props.incharge_email"),error:a(o).incharge1Email,"onUpdate:error":e[33]||(e[33]=l=>a(o).incharge1Email=l)},null,8,["modelValue","label","error"])]),i("div",ae,[t(s,{type:"text",modelValue:n.incharge1.contactNumber,"onUpdate:modelValue":e[34]||(e[34]=l=>n.incharge1.contactNumber=l),name:"incharge1ContactNumber",label:a(r)("team.config.general.props.incharge_contact_number"),error:a(o).incharge1ContactNumber,"onUpdate:error":e[35]||(e[35]=l=>a(o).incharge1ContactNumber=l)},null,8,["modelValue","label","error"])])])]),_:1}),t(g,{class:"mt-4"},{legend:m(()=>e[69]||(e[69]=[u(" 2. ")])),default:m(()=>[i("div",ne,[i("div",oe,[t(s,{type:"text",modelValue:n.incharge2.title,"onUpdate:modelValue":e[36]||(e[36]=l=>n.incharge2.title=l),name:"incharge2Title",label:a(r)("team.config.general.props.incharge_title"),error:a(o).incharge2Title,"onUpdate:error":e[37]||(e[37]=l=>a(o).incharge2Title=l)},null,8,["modelValue","label","error"])]),i("div",re,[t(s,{type:"text",modelValue:n.incharge2.name,"onUpdate:modelValue":e[38]||(e[38]=l=>n.incharge2.name=l),name:"incharge2Name",label:a(r)("team.config.general.props.incharge_name"),error:a(o).incharge2Name,"onUpdate:error":e[39]||(e[39]=l=>a(o).incharge2Name=l)},null,8,["modelValue","label","error"])]),i("div",te,[t(s,{type:"text",modelValue:n.incharge2.email,"onUpdate:modelValue":e[40]||(e[40]=l=>n.incharge2.email=l),name:"incharge2Email",label:a(r)("team.config.general.props.incharge_email"),error:a(o).incharge2Email,"onUpdate:error":e[41]||(e[41]=l=>a(o).incharge2Email=l)},null,8,["modelValue","label","error"])]),i("div",ie,[t(s,{type:"text",modelValue:n.incharge2.contactNumber,"onUpdate:modelValue":e[42]||(e[42]=l=>n.incharge2.contactNumber=l),name:"incharge2ContactNumber",label:a(r)("team.config.general.props.incharge_contact_number"),error:a(o).incharge2ContactNumber,"onUpdate:error":e[43]||(e[43]=l=>a(o).incharge2ContactNumber=l)},null,8,["modelValue","label","error"])])])]),_:1}),t(g,{class:"mt-4"},{legend:m(()=>e[70]||(e[70]=[u(" 3. ")])),default:m(()=>[i("div",se,[i("div",me,[t(s,{type:"text",modelValue:n.incharge3.title,"onUpdate:modelValue":e[44]||(e[44]=l=>n.incharge3.title=l),name:"incharge3Title",label:a(r)("team.config.general.props.incharge_title"),error:a(o).incharge3Title,"onUpdate:error":e[45]||(e[45]=l=>a(o).incharge3Title=l)},null,8,["modelValue","label","error"])]),i("div",de,[t(s,{type:"text",modelValue:n.incharge3.name,"onUpdate:modelValue":e[46]||(e[46]=l=>n.incharge3.name=l),name:"incharge3Name",label:a(r)("team.config.general.props.incharge_name"),error:a(o).incharge3Name,"onUpdate:error":e[47]||(e[47]=l=>a(o).incharge3Name=l)},null,8,["modelValue","label","error"])]),i("div",pe,[t(s,{type:"text",modelValue:n.incharge3.email,"onUpdate:modelValue":e[48]||(e[48]=l=>n.incharge3.email=l),name:"incharge3Email",label:a(r)("team.config.general.props.incharge_email"),error:a(o).incharge3Email,"onUpdate:error":e[49]||(e[49]=l=>a(o).incharge3Email=l)},null,8,["modelValue","label","error"])]),i("div",ge,[t(s,{type:"text",modelValue:n.incharge3.contactNumber,"onUpdate:modelValue":e[50]||(e[50]=l=>n.incharge3.contactNumber=l),name:"incharge3ContactNumber",label:a(r)("team.config.general.props.incharge_contact_number"),error:a(o).incharge3ContactNumber,"onUpdate:error":e[51]||(e[51]=l=>a(o).incharge3ContactNumber=l)},null,8,["modelValue","label","error"])])])]),_:1}),t(g,{class:"mt-4"},{legend:m(()=>e[71]||(e[71]=[u(" 4. ")])),default:m(()=>[i("div",ue,[i("div",ce,[t(s,{type:"text",modelValue:n.incharge4.title,"onUpdate:modelValue":e[52]||(e[52]=l=>n.incharge4.title=l),name:"incharge4Title",label:a(r)("team.config.general.props.incharge_title"),error:a(o).incharge4Title,"onUpdate:error":e[53]||(e[53]=l=>a(o).incharge4Title=l)},null,8,["modelValue","label","error"])]),i("div",be,[t(s,{type:"text",modelValue:n.incharge4.name,"onUpdate:modelValue":e[54]||(e[54]=l=>n.incharge4.name=l),name:"incharge4Name",label:a(r)("team.config.general.props.incharge_name"),error:a(o).incharge4Name,"onUpdate:error":e[55]||(e[55]=l=>a(o).incharge4Name=l)},null,8,["modelValue","label","error"])]),i("div",Ve,[t(s,{type:"text",modelValue:n.incharge4.email,"onUpdate:modelValue":e[56]||(e[56]=l=>n.incharge4.email=l),name:"incharge4Email",label:a(r)("team.config.general.props.incharge_email"),error:a(o).incharge4Email,"onUpdate:error":e[57]||(e[57]=l=>a(o).incharge4Email=l)},null,8,["modelValue","label","error"])]),i("div",fe,[t(s,{type:"text",modelValue:n.incharge4.contactNumber,"onUpdate:modelValue":e[58]||(e[58]=l=>n.incharge4.contactNumber=l),name:"incharge4ContactNumber",label:a(r)("team.config.general.props.incharge_contact_number"),error:a(o).incharge4ContactNumber,"onUpdate:error":e[59]||(e[59]=l=>a(o).incharge4ContactNumber=l)},null,8,["modelValue","label","error"])])])]),_:1}),t(g,{class:"mt-4"},{legend:m(()=>e[72]||(e[72]=[u(" 5. ")])),default:m(()=>[i("div",Ue,[i("div",ye,[t(s,{type:"text",modelValue:n.incharge5.title,"onUpdate:modelValue":e[60]||(e[60]=l=>n.incharge5.title=l),name:"incharge5Title",label:a(r)("team.config.general.props.incharge_title"),error:a(o).incharge5Title,"onUpdate:error":e[61]||(e[61]=l=>a(o).incharge5Title=l)},null,8,["modelValue","label","error"])]),i("div",Ne,[t(s,{type:"text",modelValue:n.incharge5.name,"onUpdate:modelValue":e[62]||(e[62]=l=>n.incharge5.name=l),name:"incharge5Name",label:a(r)("team.config.general.props.incharge_name"),error:a(o).incharge5Name,"onUpdate:error":e[63]||(e[63]=l=>a(o).incharge5Name=l)},null,8,["modelValue","label","error"])]),i("div",ve,[t(s,{type:"text",modelValue:n.incharge5.email,"onUpdate:modelValue":e[64]||(e[64]=l=>n.incharge5.email=l),name:"incharge5Email",label:a(r)("team.config.general.props.incharge_email"),error:a(o).incharge5Email,"onUpdate:error":e[65]||(e[65]=l=>a(o).incharge5Email=l)},null,8,["modelValue","label","error"])]),i("div",xe,[t(s,{type:"text",modelValue:n.incharge5.contactNumber,"onUpdate:modelValue":e[66]||(e[66]=l=>n.incharge5.contactNumber=l),name:"incharge5ContactNumber",label:a(r)("team.config.general.props.incharge_contact_number"),error:a(o).incharge5ContactNumber,"onUpdate:error":e[67]||(e[67]=l=>a(o).incharge5ContactNumber=l)},null,8,["modelValue","label","error"])])])]),_:1})]),_:1},8,["form","redirect"])]),_:1})],64)}}});export{Te as default};
