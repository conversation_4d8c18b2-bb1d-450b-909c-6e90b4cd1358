import{r as o,q as t,o as c,w as r,e as s}from"./app-BAwPsakn.js";const i={name:"BlogConfig"},u=Object.assign(i,{setup(g){const e=[{name:"BlogConfigGeneral",icon:"fas fa-cogs",label:"config.config"},{name:"BlogConfigCategory",icon:"fas fa-chevron-right",label:"blog.category.category"}];return(l,f)=>{const n=o("router-view"),a=o("ModuleConfig");return c(),t(a,{navigations:e},{default:r(()=>[s(n)]),_:1})}}});export{u as default};
