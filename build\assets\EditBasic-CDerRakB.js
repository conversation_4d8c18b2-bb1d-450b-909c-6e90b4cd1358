import{u as Z,h,j as x,H as _,g as N,l as $,n as ee,r as d,a as L,o as i,q as g,b as m,e as s,f as a,w as S,d as l,s as k,t as C,I as G,F as te,J as oe}from"./app-BAwPsakn.js";import{u as ae}from"./useCustomFields-C7JPVoj8.js";const re={class:"grid grid-cols-3 gap-6"},ne={class:"col-span-3 sm:col-span-2"},le={class:"flex"},se={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},Ue={key:0,class:"col-span-3 sm:col-span-1"},Fe={key:1,class:"col-span-3 sm:col-span-1"},Se={name:"ContactEditBasic"},Le=Object.assign(Se,{props:{student:{type:Object,default(){return{}}}},setup(c){const D=Z(),T=h(),E=x("emitter"),P=c,q={firstName:"",middleName:"",thirdName:"",lastName:"",firstName:"",middleName:"",gender:"",birthDate:"",anniversaryDate:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",maritalStatus:"",religion:"",category:"",caste:"",customFields:[]},B="student/",n=_(B),R=N("contact.uniqueIdNumber1Label"),j=N("contact.uniqueIdNumber2Label"),w=N("contact.uniqueIdNumber3Label"),O=N("contact.uniqueIdNumber4Label"),H=N("contact.uniqueIdNumber5Label"),A=N("contact.enableCategoryField"),z=N("contact.enableCasteField"),p=$({genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[],customFields:[]}),o=$({...q}),V=$({isLoaded:!D.params.uuid}),{customFields:J,setCustomFields:M}=ae(),K=r=>{Object.assign(p,r),Q()},Q=()=>{var e,f,I,v,u,U,y,b,F;let r=(e=P.student)==null?void 0:e.contact;Object.assign(q,{firstName:r.firstName,middleName:r.middleName,thirdName:r.thirdName,lastName:r.lastName,fatherName:r.fatherName,motherName:r.motherName,gender:(f=r.gender)==null?void 0:f.value,birthDate:(I=r.birthDate)==null?void 0:I.value,anniversaryDate:(v=r.anniversaryDate)==null?void 0:v.value,uniqueIdNumber1:r.uniqueIdNumber1,uniqueIdNumber2:r.uniqueIdNumber2,uniqueIdNumber3:r.uniqueIdNumber3,uniqueIdNumber4:r.uniqueIdNumber4,uniqueIdNumber5:r.uniqueIdNumber5,birthPlace:r.birthPlace,bloodGroup:((u=r.bloodGroup)==null?void 0:u.value)||"",maritalStatus:((U=r.maritalStatus)==null?void 0:U.value)||"",nationality:r.nationality,motherTongue:r.motherTongue,religion:((y=r.religion)==null?void 0:y.uuid)||"",category:((b=r.category)==null?void 0:b.uuid)||"",caste:((F=r.caste)==null?void 0:F.uuid)||""}),M(p.customFields,r.customFields),q.customFields=J.value,Object.assign(o,oe(q)),V.isLoaded=!0};ee(async()=>{});const W=()=>{E.emit("studentUpdated"),T.push({name:"StudentShowBasic",params:{uuid:P.student.uuid}})};return(r,e)=>{const f=d("PageHeader"),I=d("BaseLabel"),v=d("NameInput"),u=d("BaseInput"),U=d("BaseRadioGroup"),y=d("DatePicker"),b=d("BaseSelect"),F=d("CustomField"),X=d("FormAction"),Y=d("ParentTransition");return i(),L(te,null,[c.student.uuid?(i(),g(f,{key:0,title:r.$trans(a(D).meta.trans,{attribute:r.$trans(a(D).meta.label)}),navs:[{label:r.$trans("student.student"),path:"Student"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}}]},null,8,["title","navs"])):m("",!0),s(Y,{appear:"",visibility:!0},{default:S(()=>[c.student.uuid?(i(),g(X,{key:0,"pre-requisites":!0,onSetPreRequisites:K,"init-url":B,"no-data-fetch":"","init-form":q,form:o,"stay-on":"","after-submit":W,redirect:{name:"StudentShowBasic",params:{uuid:c.student.uuid}}},{default:S(()=>[l("div",re,[l("div",ne,[s(I,null,{default:S(()=>[k(C(r.$trans("contact.props.name")),1)]),_:1}),l("div",le,[s(v,{firstName:o.firstName,"onUpdate:firstName":e[0]||(e[0]=t=>o.firstName=t),middleName:o.middleName,"onUpdate:middleName":e[1]||(e[1]=t=>o.middleName=t),thirdName:o.thirdName,"onUpdate:thirdName":e[2]||(e[2]=t=>o.thirdName=t),lastName:o.lastName,"onUpdate:lastName":e[3]||(e[3]=t=>o.lastName=t),formErrors:a(n),"onUpdate:formErrors":e[4]||(e[4]=t=>G(n)?n.value=t:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),l("div",se,[s(u,{type:"text",modelValue:o.fatherName,"onUpdate:modelValue":e[5]||(e[5]=t=>o.fatherName=t),name:"fatherName",label:r.$trans("contact.props.father_name"),error:a(n).fatherName,"onUpdate:error":e[6]||(e[6]=t=>a(n).fatherName=t)},null,8,["modelValue","label","error"])]),l("div",ue,[s(u,{type:"text",modelValue:o.motherName,"onUpdate:modelValue":e[7]||(e[7]=t=>o.motherName=t),name:"motherName",label:r.$trans("contact.props.mother_name"),error:a(n).motherName,"onUpdate:error":e[8]||(e[8]=t=>a(n).motherName=t)},null,8,["modelValue","label","error"])]),l("div",de,[s(I,null,{default:S(()=>[k(C(r.$trans("contact.props.gender")),1)]),_:1}),s(U,{"top-margin":"",options:p.genders,name:"gender",modelValue:o.gender,"onUpdate:modelValue":e[9]||(e[9]=t=>o.gender=t),error:a(n).gender,"onUpdate:error":e[10]||(e[10]=t=>a(n).gender=t),horizontal:""},null,8,["options","modelValue","error"])]),l("div",ie,[s(y,{modelValue:o.birthDate,"onUpdate:modelValue":e[11]||(e[11]=t=>o.birthDate=t),name:"birthDate",label:r.$trans("contact.props.birth_date"),"no-clear":"",error:a(n).birthDate,"onUpdate:error":e[12]||(e[12]=t=>a(n).birthDate=t)},null,8,["modelValue","label","error"])]),l("div",me,[s(y,{modelValue:o.anniversaryDate,"onUpdate:modelValue":e[13]||(e[13]=t=>o.anniversaryDate=t),name:"anniversaryDate",label:r.$trans("contact.props.anniversary_date"),"no-clear":"",error:a(n).anniversaryDate,"onUpdate:error":e[14]||(e[14]=t=>a(n).anniversaryDate=t)},null,8,["modelValue","label","error"])]),l("div",pe,[s(u,{type:"text",modelValue:o.uniqueIdNumber1,"onUpdate:modelValue":e[15]||(e[15]=t=>o.uniqueIdNumber1=t),name:"uniqueIdNumber1",label:a(R),error:a(n).uniqueIdNumber1,"onUpdate:error":e[16]||(e[16]=t=>a(n).uniqueIdNumber1=t)},null,8,["modelValue","label","error"])]),l("div",be,[s(u,{type:"text",modelValue:o.uniqueIdNumber2,"onUpdate:modelValue":e[17]||(e[17]=t=>o.uniqueIdNumber2=t),name:"uniqueIdNumber2",label:a(j),error:a(n).uniqueIdNumber2,"onUpdate:error":e[18]||(e[18]=t=>a(n).uniqueIdNumber2=t)},null,8,["modelValue","label","error"])]),l("div",Ne,[s(u,{type:"text",modelValue:o.uniqueIdNumber3,"onUpdate:modelValue":e[19]||(e[19]=t=>o.uniqueIdNumber3=t),name:"uniqueIdNumber3",label:a(w),error:a(n).uniqueIdNumber3,"onUpdate:error":e[20]||(e[20]=t=>a(n).uniqueIdNumber3=t)},null,8,["modelValue","label","error"])]),l("div",ge,[s(u,{type:"text",modelValue:o.uniqueIdNumber4,"onUpdate:modelValue":e[21]||(e[21]=t=>o.uniqueIdNumber4=t),name:"uniqueIdNumber4",label:a(O),error:a(n).uniqueIdNumber4,"onUpdate:error":e[22]||(e[22]=t=>a(n).uniqueIdNumber4=t)},null,8,["modelValue","label","error"])]),l("div",ce,[s(u,{type:"text",modelValue:o.uniqueIdNumber5,"onUpdate:modelValue":e[23]||(e[23]=t=>o.uniqueIdNumber5=t),name:"uniqueIdNumber5",label:a(H),error:a(n).uniqueIdNumber5,"onUpdate:error":e[24]||(e[24]=t=>a(n).uniqueIdNumber5=t)},null,8,["modelValue","label","error"])]),l("div",Ve,[s(u,{type:"text",modelValue:o.birthPlace,"onUpdate:modelValue":e[25]||(e[25]=t=>o.birthPlace=t),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:a(n).birthPlace,"onUpdate:error":e[26]||(e[26]=t=>a(n).birthPlace=t)},null,8,["modelValue","label","error"])]),l("div",qe,[s(u,{type:"text",modelValue:o.nationality,"onUpdate:modelValue":e[27]||(e[27]=t=>o.nationality=t),name:"nationality",label:r.$trans("contact.props.nationality"),error:a(n).nationality,"onUpdate:error":e[28]||(e[28]=t=>a(n).nationality=t)},null,8,["modelValue","label","error"])]),l("div",Ie,[s(u,{type:"text",modelValue:o.motherTongue,"onUpdate:modelValue":e[29]||(e[29]=t=>o.motherTongue=t),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:a(n).motherTongue,"onUpdate:error":e[30]||(e[30]=t=>a(n).motherTongue=t)},null,8,["modelValue","label","error"])]),l("div",ye,[V.isLoaded?(i(),g(b,{key:0,modelValue:o.bloodGroup,"onUpdate:modelValue":e[31]||(e[31]=t=>o.bloodGroup=t),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:p.bloodGroups,error:a(n).bloodGroup,"onUpdate:error":e[32]||(e[32]=t=>a(n).bloodGroup=t)},null,8,["modelValue","label","options","error"])):m("",!0)]),l("div",fe,[V.isLoaded?(i(),g(b,{key:0,modelValue:o.maritalStatus,"onUpdate:modelValue":e[33]||(e[33]=t=>o.maritalStatus=t),name:"maritalStatus",label:r.$trans("contact.props.marital_status"),options:p.maritalStatuses,error:a(n).maritalStatus,"onUpdate:error":e[34]||(e[34]=t=>a(n).maritalStatus=t)},null,8,["modelValue","label","options","error"])):m("",!0)]),l("div",ve,[V.isLoaded?(i(),g(b,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:o.religion,"onUpdate:modelValue":e[35]||(e[35]=t=>o.religion=t),error:a(n).religion,"onUpdate:error":e[36]||(e[36]=t=>a(n).religion=t),options:p.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):m("",!0)]),a(A)?(i(),L("div",Ue,[V.isLoaded?(i(),g(b,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:o.category,"onUpdate:modelValue":e[37]||(e[37]=t=>o.category=t),error:a(n).category,"onUpdate:error":e[38]||(e[38]=t=>a(n).category=t),options:p.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):m("",!0)])):m("",!0),a(z)?(i(),L("div",Fe,[V.isLoaded?(i(),g(b,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:o.caste,"onUpdate:modelValue":e[39]||(e[39]=t=>o.caste=t),error:a(n).caste,"onUpdate:error":e[40]||(e[40]=t=>a(n).caste=t),options:p.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):m("",!0)])):m("",!0)]),s(F,{customFields:o.customFields,"onUpdate:customFields":e[41]||(e[41]=t=>o.customFields=t),formErrors:a(n),"onUpdate:formErrors":e[42]||(e[42]=t=>G(n)?n.value=t:null)},null,8,["customFields","formErrors"])]),_:1},8,["form","redirect"])):m("",!0)]),_:1})],64)}}});export{Le as default};
