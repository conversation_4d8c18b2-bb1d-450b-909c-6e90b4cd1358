import{i as D,u as j,h as A,l as H,r,a as _,o as d,e as s,w as e,f as i,q as b,b as f,d as I,F as g,v as P,s as o,t as l,y as M}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},E={name:"ResourceLessonPlanShow"},q=Object.assign(E,{setup(F){D();const p=j(),$=A(),y={},h="resource/lessonPlan/",a=H({...y}),k=t=>{Object.assign(a,t)};return(t,m)=>{const v=r("PageHeaderAction"),w=r("PageHeader"),B=r("TextMuted"),u=r("BaseDataView"),L=r("ListMedia"),R=r("BaseButton"),S=r("ShowButton"),C=r("BaseCard"),T=r("ShowItem"),V=r("ParentTransition");return d(),_(g,null,[s(w,{title:t.$trans(i(p).meta.trans,{attribute:t.$trans(i(p).meta.label)}),navs:[{label:t.$trans("resource.resource"),path:"Resource"},{label:t.$trans("resource.lesson_plan.lesson_plan"),path:"ResourceLessonPlan"}]},{default:e(()=>[s(v,{name:"ResourceLessonPlan",title:t.$trans("resource.lesson_plan.lesson_plan"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(V,{appear:"",visibility:!0},{default:e(()=>[s(T,{"init-url":h,uuid:i(p).params.uuid,"module-uuid":i(p).params.muuid,onSetItem:k,onRedirectTo:m[1]||(m[1]=n=>i($).push({name:"ResourceLessonPlan",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(d(),b(C,{key:0},{title:e(()=>[o(l(a.topic),1)]),footer:e(()=>[s(S,null,{default:e(()=>[i(M)("lesson-plan:edit")&&a.isEditable?(d(),b(R,{key:0,design:"primary",onClick:m[0]||(m[0]=n=>i($).push({name:"ResourceLessonPlanEdit",params:{uuid:a.uuid}}))},{default:e(()=>[o(l(t.$trans("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:e(()=>[I("dl",N,[s(u,{label:t.$trans("academic.course.course")},{default:e(()=>[(d(!0),_(g,null,P(a.records,n=>{var c;return d(),_("div",null,[o(l(((c=n.batch.course)==null?void 0:c.name)+" "+n.batch.name)+" ",1),n.subject?(d(),b(B,{key:0},{default:e(()=>[o(l(n.subject.name),1)]),_:2},1024)):f("",!0)])}),256))]),_:1},8,["label"]),s(u,{label:t.$trans("employee.employee")},{default:e(()=>{var n;return[o(l(((n=a.employee)==null?void 0:n.name)||"-")+" ",1),s(B,{block:""},{default:e(()=>{var c;return[o(l(((c=a.employee)==null?void 0:c.designation)||""),1)]}),_:1})]}),_:1},8,["label"]),s(u,{label:t.$trans("resource.lesson_plan.props.start_date")},{default:e(()=>[o(l(a.startDate.formatted),1)]),_:1},8,["label"]),s(u,{label:t.$trans("resource.lesson_plan.props.end_date")},{default:e(()=>[o(l(a.endDate.formatted),1)]),_:1},8,["label"]),(d(!0),_(g,null,P(a.details,n=>(d(),b(u,{class:"col-span-1 sm:col-span-2",key:n.uuid,label:n.heading},{default:e(()=>[o(l(n.description),1)]),_:2},1032,["label"]))),128)),s(u,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[s(L,{media:a.media,url:`/app/resource/lesson-plans/${a.uuid}/`},null,8,["media","url"])]),_:1}),s(u,{label:t.$trans("general.created_at")},{default:e(()=>[o(l(a.createdAt.formatted),1)]),_:1},8,["label"]),s(u,{label:t.$trans("general.updated_at")},{default:e(()=>[o(l(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{q as default};
