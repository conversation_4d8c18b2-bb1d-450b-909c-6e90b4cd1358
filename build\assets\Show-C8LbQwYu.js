import{i as S,u as A,h as N,l as P,r as l,a as T,o as u,e as r,w as e,f as d,q as b,b as _,d as V,s as a,t as o,y as H,F as I}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"ReceptionCorrespondenceShow"},L=Object.assign(j,{setup(E){S();const p=A(),m=N(),f={},$="reception/correspondence/",n=P({...f}),g=t=>{Object.assign(n,t)};return(t,c)=>{const B=l("PageHeaderAction"),C=l("PageHeader"),s=l("BaseDataView"),v=l("ListMedia"),h=l("BaseButton"),w=l("ShowButton"),y=l("BaseCard"),R=l("ShowItem"),k=l("ParentTransition");return u(),T(I,null,[r(C,{title:t.$trans(d(p).meta.trans,{attribute:t.$trans(d(p).meta.label)}),navs:[{label:t.$trans("reception.reception"),path:"Reception"},{label:t.$trans("reception.correspondence.correspondence"),path:"ReceptionCorrespondence"}]},{default:e(()=>[r(B,{name:"ReceptionCorrespondence",title:t.$trans("reception.correspondence.correspondence"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(k,{appear:"",visibility:!0},{default:e(()=>[r(R,{"init-url":$,uuid:d(p).params.uuid,"module-uuid":d(p).params.muuid,onSetItem:g,onRedirectTo:c[1]||(c[1]=i=>d(m).push({name:"ReceptionCorrespondence",params:{uuid:n.uuid}}))},{default:e(()=>[n.uuid?(u(),b(y,{key:0},{title:e(()=>[a(o(n.letterNumber),1)]),footer:e(()=>[r(w,null,{default:e(()=>[d(H)("correspondence:edit")?(u(),b(h,{key:0,design:"primary",onClick:c[0]||(c[0]=i=>d(m).push({name:"ReceptionCorrespondenceEdit",params:{uuid:n.uuid}}))},{default:e(()=>[a(o(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[V("dl",D,[r(s,{label:t.$trans("reception.correspondence.props.type")},{default:e(()=>[a(o(n.type.label),1)]),_:1},8,["label"]),r(s,{label:t.$trans("reception.correspondence.props.mode")},{default:e(()=>[a(o(n.mode.label),1)]),_:1},8,["label"]),r(s,{class:"col-span-1 sm:col-span-2",label:t.$trans("reception.correspondence.props.reference")},{default:e(()=>{var i;return[a(o(((i=n.reference)==null?void 0:i.letterNumber)||"-"),1)]}),_:1},8,["label"]),r(s,{label:t.$trans("reception.correspondence.props.sender_title")},{default:e(()=>[a(o(n.senderTitle),1)]),_:1},8,["label"]),r(s,{label:t.$trans("reception.correspondence.props.sender_address")},{default:e(()=>[a(o(n.senderAddress),1)]),_:1},8,["label"]),r(s,{label:t.$trans("reception.correspondence.props.receiver_title")},{default:e(()=>[a(o(n.receiverTitle),1)]),_:1},8,["label"]),r(s,{label:t.$trans("reception.correspondence.props.receiver_address")},{default:e(()=>[a(o(n.receiverAddress),1)]),_:1},8,["label"]),r(s,{label:t.$trans("reception.correspondence.props.date")},{default:e(()=>[a(o(n.date.formatted),1)]),_:1},8,["label"]),r(s,{class:"col-span-1 sm:col-span-2",label:t.$trans("reception.correspondence.props.remarks")},{default:e(()=>[a(o(n.remarks),1)]),_:1},8,["label"]),r(s,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[r(v,{media:n.media,url:`/app/reception/correspondences/${n.uuid}/`},null,8,["media","url"])]),_:1}),r(s,{label:t.$trans("general.created_at")},{default:e(()=>[a(o(n.createdAt.formatted),1)]),_:1},8,["label"]),r(s,{label:t.$trans("general.updated_at")},{default:e(()=>[a(o(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{L as default};
