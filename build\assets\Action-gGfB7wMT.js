import{H as A,l as g,r as d,q as $,o as f,w as c,d as u,e as r,f as t,J as v,u as U,a as P,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={class:"col-span-4 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},y={name:"AssetBuildingFloorForm"},E=Object.assign(y,{setup(_){const i={name:"",alias:"",block:"",description:""},a="asset/building/floor/",n=A(a),p=g({blocks:[]}),s=g({...i}),m=l=>{Object.assign(p,l)},B=l=>{Object.assign(i,{...l,block:l.blockUuid}),Object.assign(s,v(i))};return(l,e)=>{const b=d("BaseInput"),k=d("BaseSelect"),V=d("BaseTextarea"),F=d("FormAction");return f(),$(F,{"pre-requisites":!0,onSetPreRequisites:m,"init-url":a,"init-form":i,form:s,"set-form":B,redirect:"AssetBuildingFloor"},{default:c(()=>[u("div",q,[u("div",H,[r(b,{type:"text",modelValue:s.name,"onUpdate:modelValue":e[0]||(e[0]=o=>s.name=o),name:"name",label:l.$trans("asset.building.floor.props.name"),error:t(n).name,"onUpdate:error":e[1]||(e[1]=o=>t(n).name=o),autofocus:""},null,8,["modelValue","label","error"])]),u("div",O,[r(b,{type:"text",modelValue:s.alias,"onUpdate:modelValue":e[2]||(e[2]=o=>s.alias=o),name:"alias",label:l.$trans("asset.building.floor.props.alias"),error:t(n).alias,"onUpdate:error":e[3]||(e[3]=o=>t(n).alias=o),autofocus:""},null,8,["modelValue","label","error"])]),u("div",R,[r(k,{modelValue:s.block,"onUpdate:modelValue":e[4]||(e[4]=o=>s.block=o),name:"block",label:l.$trans("asset.building.block.block"),options:p.blocks,"label-prop":"name","value-prop":"uuid",error:t(n).block,"onUpdate:error":e[5]||(e[5]=o=>t(n).block=o)},null,8,["modelValue","label","options","error"])]),u("div",T,[r(V,{modelValue:s.description,"onUpdate:modelValue":e[6]||(e[6]=o=>s.description=o),name:"description",label:l.$trans("asset.building.floor.props.description"),error:t(n).description,"onUpdate:error":e[7]||(e[7]=o=>t(n).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),S={name:"AssetBuildingFloorAction"},C=Object.assign(S,{setup(_){const i=U();return(a,n)=>{const p=d("PageHeaderAction"),s=d("PageHeader"),m=d("ParentTransition");return f(),P(j,null,[r(s,{title:a.$trans(t(i).meta.trans,{attribute:a.$trans(t(i).meta.label)}),navs:[{label:a.$trans("asset.asset"),path:"Asset"},{label:a.$trans("asset.building.building"),path:"AssetBuilding"},{label:a.$trans("asset.building.floor.floor"),path:"AssetBuildingFloorList"}]},{default:c(()=>[r(p,{name:"AssetBuildingFloor",title:a.$trans("asset.building.floor.floor"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(m,{appear:"",visibility:!0},{default:c(()=>[r(E)]),_:1})],64)}}});export{C as default};
