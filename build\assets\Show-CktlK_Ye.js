import{i as D,u as N,h as A,l as H,r as s,a as f,o as r,e as a,w as t,f as i,q as b,b as g,d as p,s as u,t as o,F as h,v as I}from"./app-BAwPsakn.js";const T={class:"flex items-center space-x-2"},j={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},O={class:"flex"},M={name:"StudentFeeRefundShow"},E=Object.assign(M,{props:{student:{type:Object,default(){return{}}}},setup(c){D();const m=N(),B=A(),S={},y="student/feeRefund/",n=H({...S}),$=e=>{Object.assign(n,e)};return(e,_)=>{const w=s("PageHeaderAction"),v=s("PageHeader"),R=s("BaseBadge"),d=s("BaseDataView"),P=s("PaymentMethodDetail"),k=s("ShowButton"),C=s("BaseCard"),F=s("ShowItem"),V=s("ParentTransition");return r(),f(h,null,[a(v,{title:e.$trans(i(m).meta.trans,{attribute:e.$trans(i(m).meta.label)}),navs:[{label:e.$trans("student.student"),path:"Student"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}}]},{default:t(()=>[a(w,{name:"StudentFeeRefund",title:e.$trans("student.fee_refund.fee_refund"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(V,{appear:"",visibility:!0},{default:t(()=>[a(F,{"init-url":y,uuid:i(m).params.uuid,"module-uuid":i(m).params.muuid,onSetItem:$,onRedirectTo:_[0]||(_[0]=l=>i(B).push({name:"StudentFeeRefund",params:{uuid:c.student.uuid}}))},{default:t(()=>[n.uuid?(r(),b(C,{key:0},{title:t(()=>{var l;return[p("div",T,[p("span",null,o((l=n.transaction)==null?void 0:l.codeNumber),1),n.isCancelled?(r(),b(R,{key:0,design:"danger"},{default:t(()=>[u(o(e.$trans("general.cancelled")),1)]),_:1})):g("",!0)])]}),footer:t(()=>[a(k)]),default:t(()=>[p("dl",j,[a(d,{label:e.$trans("student.fee_refund.props.total")},{default:t(()=>[u(o(n.total.formatted),1)]),_:1},8,["label"]),a(d,{label:e.$trans("student.fee_refund.props.date")},{default:t(()=>[u(o(n.date.formatted),1)]),_:1},8,["label"]),a(d,{label:e.$trans("finance.fee_head.fee_head")},{default:t(()=>[(r(!0),f(h,null,I(n.records,l=>(r(),f("div",O,o(l.head.name)+" "+o(l.amount.formatted),1))),256))]),_:1},8,["label"]),a(d,{label:e.$trans("finance.payment_method.payment_method")},{default:t(()=>[a(P,{payments:n.transaction.payments},null,8,["payments"])]),_:1},8,["label"]),a(d,{class:"col-span-1 sm:col-span-2",label:e.$trans("student.fee.props.remarks")},{default:t(()=>[u(o(n.remarks),1)]),_:1},8,["label"]),a(d,{label:e.$trans("general.created_at")},{default:t(()=>[u(o(n.createdAt.formatted),1)]),_:1},8,["label"]),a(d,{label:e.$trans("general.updated_at")},{default:t(()=>[u(o(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):g("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{E as default};
