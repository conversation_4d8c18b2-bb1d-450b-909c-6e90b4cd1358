import{u as P,l as F,n as R,r as i,q as y,o as c,w as e,d as w,b as k,s as m,t as o,e as a,h as T,j as M,y as $,m as O,f as s,a as j,F as q,v as U}from"./app-BAwPsakn.js";import{_ as A}from"./ModuleDropdown-IVNsXs7q.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(E,{emit:_}){const v=P(),D=_,b={employees:[],startDate:"",endDate:""},d=F({...b}),p=F({employees:[],isLoaded:!v.query.employees});return R(async()=>{p.employees=v.query.employees?v.query.employees.split(","):[],p.isLoaded=!0}),(f,u)=>{const n=i("BaseSelectSearch"),r=i("DatePicker"),L=i("FilterForm");return c(),y(L,{"init-form":b,form:d,multiple:["employees"],onHide:u[3]||(u[3]=l=>D("hide"))},{default:e(()=>[w("div",z,[w("div",G,[p.isLoaded?(c(),y(n,{key:0,multiple:"",name:"employees",label:f.$trans("global.select",{attribute:f.$trans("employee.employee")}),modelValue:d.employees,"onUpdate:modelValue":u[0]||(u[0]=l=>d.employees=l),"value-prop":"uuid","init-search":p.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(l=>[m(o(l.value.name)+" ("+o(l.value.codeNumber)+") ",1)]),listOption:e(l=>[m(o(l.option.name)+" ("+o(l.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):k("",!0)]),w("div",J,[a(r,{start:d.startDate,"onUpdate:start":u[1]||(u[1]=l=>d.startDate=l),end:d.endDate,"onUpdate:end":u[2]||(u[2]=l=>d.endDate=l),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"EmployeeLeaveAllocationList"},Y=Object.assign(Q,{setup(E){const _=T(),v=M("emitter");let D=["filter"];$("leave-allocation:create")&&D.unshift("create");let b=[];$("leave-allocation:export")&&(b=["print","pdf","excel"]);const d="employee/leave/allocation/",p=O(!1),f=F({}),u=n=>{Object.assign(f,n)};return(n,r)=>{const L=i("PageHeaderAction"),l=i("PageHeader"),B=i("ParentTransition"),g=i("DataCell"),h=i("FloatingMenuItem"),I=i("FloatingMenu"),S=i("DataRow"),N=i("BaseButton"),V=i("DataTable"),H=i("ListItem");return c(),y(H,{"init-url":d,onSetItems:u},{header:e(()=>[a(l,{title:n.$trans("employee.leave.allocation.allocation"),navs:[{label:n.$trans("employee.employee"),path:"Employee"},{label:n.$trans("employee.leave.leave"),path:"EmployeeLeave"}]},{default:e(()=>[a(L,{url:"employee/leave/allocations/",name:"EmployeeLeaveAllocation",title:n.$trans("employee.leave.allocation.allocation"),actions:s(D),"dropdown-actions":s(b),onToggleFilter:r[0]||(r[0]=t=>p.value=!p.value)},{moduleOption:e(()=>[a(A)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(B,{appear:"",visibility:p.value},{default:e(()=>[a(K,{onRefresh:r[1]||(r[1]=t=>s(v).emit("listItems")),onHide:r[2]||(r[2]=t=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(B,{appear:"",visibility:!0},{default:e(()=>[a(V,{header:f.headers,meta:f.meta,module:"employee.leave.allocation",onRefresh:r[4]||(r[4]=t=>s(v).emit("listItems"))},{actionButton:e(()=>[s($)("leave-allocation:create")?(c(),y(N,{key:0,onClick:r[3]||(r[3]=t=>s(_).push({name:"EmployeeLeaveAllocationCreate"}))},{default:e(()=>[m(o(n.$trans("global.add",{attribute:n.$trans("employee.leave.allocation.allocation")})),1)]),_:1})):k("",!0)]),default:e(()=>[(c(!0),j(q,null,U(f.data,t=>(c(),y(S,{key:t.uuid,onDoubleClick:C=>s(_).push({name:"EmployeeLeaveAllocationShow",params:{uuid:t.uuid}})},{default:e(()=>[a(g,{name:"employee"},{default:e(()=>[m(o(t.employee.name)+" ("+o(t.employee.codeNumber)+") ",1)]),_:2},1024),a(g,{name:"designation"},{default:e(()=>[m(o(t.employee.designation),1)]),_:2},1024),a(g,{name:"startDate"},{default:e(()=>[m(o(t.startDate.formatted),1)]),_:2},1024),a(g,{name:"endDate"},{default:e(()=>[m(o(t.endDate.formatted),1)]),_:2},1024),a(g,{name:"createdAt"},{default:e(()=>[m(o(t.createdAt.formatted),1)]),_:2},1024),a(g,{name:"action"},{default:e(()=>[a(I,null,{default:e(()=>[a(h,{icon:"fas fa-arrow-circle-right",onClick:C=>s(_).push({name:"EmployeeLeaveAllocationShow",params:{uuid:t.uuid}})},{default:e(()=>[m(o(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),s($)("leave-allocation:edit")?(c(),y(h,{key:0,icon:"fas fa-edit",onClick:C=>s(_).push({name:"EmployeeLeaveAllocationEdit",params:{uuid:t.uuid}})},{default:e(()=>[m(o(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),s($)("leave-allocation:create")?(c(),y(h,{key:1,icon:"fas fa-copy",onClick:C=>s(_).push({name:"EmployeeLeaveAllocationDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[m(o(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),s($)("leave-allocation:delete")?(c(),y(h,{key:2,icon:"fas fa-trash",onClick:C=>s(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[m(o(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
