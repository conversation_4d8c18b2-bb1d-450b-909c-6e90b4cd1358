import{_ as i,r as s,q as c,o as d,w as t,d as e,e as o}from"./app-BAwPsakn.js";const _={},l={class:"bg-primary flex min-h-screen items-center overflow-hidden"},p={class:"mx-auto w-full max-w-screen-xl px-4 py-4 sm:px-6"},m={class:"bg-secondary rounded-lg px-4 py-16 sm:px-6 sm:py-24 md:grid md:place-items-center lg:px-8"},x={class:"mx-auto max-w-max"},u={class:"sm:flex"};function f(h,v,w,y,g,C){const n=s("router-view"),r=s("ChildTransition"),a=s("ParentTransition");return d(),c(a,{appear:"",visibility:!0},{default:t(()=>[e("div",l,[e("div",p,[e("div",m,[e("div",x,[o(r,{direction:"ltr"},{default:t(()=>[e("main",u,[o(n)])]),_:1})])])])])]),_:1})}const $=i(_,[["render",f]]);export{$ as default};
