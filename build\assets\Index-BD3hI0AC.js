import{H as re,l as j,r as p,q as y,o as l,w as i,d as o,e as d,f as u,u as X,h as Y,i as Z,j as Q,K as ee,n as te,z as se,y as M,x as G,t as h,A as x,a as T,s as I,b as L,g as de,m as J,F as ue,v as ce}from"./app-BAwPsakn.js";import{i as me,t as pe,a as fe}from"./table-FwhM-Z75.js";import{d as W}from"./vuedraggable.umd-BRYqknf6.js";const ge={class:"grid grid-cols-1"},ye={class:"col-span-1"},ve={name:"UtilityTodoListForm"},_e=Object.assign(ve,{emits:["completed"],setup(H,{emit:$}){const k=$,g={name:""},D="utility/todo/",f=re(D),q=j({...g}),m=c=>{k("completed",c)};return(c,U)=>{const w=p("BaseInput"),r=p("FormAction");return l(),y(r,{"no-card":!0,"init-url":D,"init-form":g,action:"addList",form:q,"keep-adding":!1,"after-submit":m},{default:i(()=>[o("div",ge,[o("div",ye,[d(w,{type:"text",modelValue:q.name,"onUpdate:modelValue":U[0]||(U[0]=V=>q.name=V),name:"name",placeholder:c.$trans("utility.todo.list.props.name"),error:u(f).name,"onUpdate:error":U[1]||(U[1]=V=>u(f).name=V)},null,8,["modelValue","placeholder","error"])])])]),_:1},8,["form"])}}}),he={class:"flex h-[calc(100vh_-_250px)] w-80 flex-shrink-0 flex-col rounded-md bg-gray-100 p-3 dark:bg-neutral-800"},ke={class:"scroller-thin-y scroller-hidden min-h-0 flex-1"},be={key:0,class:"mt-2 rounded-md border-2 border-dashed border-gray-400 p-2 text-sm text-gray-400 dark:border-gray-500 dark:text-gray-500"},$e={class:"space-y-2 p-3"},we=["onClick"],Ce={class:"flex justify-between text-xs"},Ae={class:"space-x-2"},Ue={class:"cursor-move"},Be={key:0,class:"font-medium"},Ie={key:1},Le={class:"space-x-2"},De={key:0},qe=["onClick"],Te={key:1},Ve=["onClick"],xe={key:2},Fe=["onClick"],Se={key:3},Re=["onClick"],Oe={name:"UtilityTodoListBoardView"},Pe=Object.assign(Oe,{props:{todos:{type:Object,default(){return{data:[]}}},preRequisites:{type:Object,default:()=>({})},isLoading:{type:Boolean,default:!1}},emits:["newListAdded"],setup(H,{emit:$}){X();const k=Y(),g=Z(),D=Q("emitter"),f=$,q=H,m=j({lists:[],emptyList:[]}),c=()=>{m.lists=[];let s=[{name:"Uncategorized",uuid:"uncategorized",items:[]}];q.preRequisites.todoLists.forEach(n=>{s.push({name:n.name,uuid:n.uuid,items:[]})}),q.todos.data.forEach(n=>{var B;if((B=n.list)!=null&&B.uuid){let v=s.find(A=>A.name==n.list.name);v?v.items.push(n):s.push({name:n.list.name,uuid:n.list.uuid,items:[n]})}else s.find(v=>v.name==="Uncategorized").items.push(n)}),s.find(n=>n.name==="Uncategorized").items.length===0&&(s=s.filter(n=>n.name!=="Uncategorized")),m.lists=s},U=s=>{let n=m.lists.map(B=>B.uuid);g.dispatch("utility/todo/reorderList",{uuids:n}).then(B=>{}).catch(B=>{})},w=s=>{var B,v;let n=0;if(s.removed&&(n=s.removed.oldIndex),s.added){let A=s.added.element.uuid,e=(B=s.added.element.list)==null?void 0:B.uuid,t=null,_=[];m.lists.forEach(S=>{S.items.find(R=>R.uuid===A)&&(t=S.uuid,_=S.items.map(R=>R.uuid))}),g.dispatch("utility/todo/moveList",{uuid:A,listUuid:t,itemUuids:_}).then(S=>{}).catch(S=>{e===void 0&&(e="uncategorized"),m.lists.find(O=>O.uuid===e).items.splice(n,0,s.added.element);let F=m.lists.find(O=>O.uuid===t);F.items=F.items.filter(O=>O.uuid!==A)})}if(s.moved){s.moved.element.uuid;let A=((v=s.moved.element.list)==null?void 0:v.uuid)||"uncategorized",t=m.lists.find(_=>_.uuid===A).items.map(_=>_.uuid);g.dispatch("utility/todo/reorderItems",{uuids:t}).then(_=>{}).catch(_=>{})}},r=s=>{f("newListAdded",s.option),m.lists.push({name:s.option.name,uuid:s.option.uuid,items:[]})},V=async s=>{D.emit("actionItem",{uuid:s.uuid,action:"status",confirmation:!0})},z=async s=>{D.emit("actionItem",{uuid:s.uuid,action:"archive",confirmation:!0})},C=async s=>{D.emit("actionItem",{uuid:s.uuid,action:"unarchive",confirmation:!0})};return ee(()=>q.todos,s=>{c()},{deep:!0}),te(()=>{c()}),(s,n)=>{const B=p("KanBanView"),v=se("tooltip");return l(),y(B,{"add-list":!!u(M)("utility:config")},{addListTitle:i(()=>[I(h(s.$trans("global.add",{attribute:s.$trans("utility.todo.list.list")})),1)]),addListForm:i(()=>[d(_e,{onCompleted:r})]),default:i(()=>[d(u(W),{class:"flex space-x-2",list:m.lists,onChange:U,"item-key":"uuid",disabled:!u(M)("utility:config")},{item:i(({element:A})=>[o("div",he,[o("h3",{class:G(["pb-2 text-sm font-medium text-gray-900 dark:text-gray-300",{"cursor-move":!!u(M)("utility:config")}])},h(A.name),3),o("div",ke,[d(u(W),{class:"mt-2 space-y-3",list:A.items,group:"items",onChange:w,"item-key":"uuid"},{header:i(()=>[A.items.length===0?(l(),T("div",be,h(s.$trans("utility.todo.list.info_empty")),1)):L("",!0)]),item:i(({element:e})=>[o("div",{class:G(["dark:bg-dark-body rounded-md border-2 bg-white text-gray-700 shadow-sm dark:text-gray-400",{"border-success":e.completedAt,"border-danger":e.isOverdue,"border-warning":e.isDueToday,"border-gray-400":!e.completedAt&&!e.isOverdue&&!e.isDueToday}])},[o("div",$e,[o("p",{class:"cursor-pointer text-sm",onClick:t=>u(k).push({name:"UtilityTodoShow",params:{uuid:e.uuid}})},h(e.title),9,we),o("div",Ce,[o("div",Ae,[x((l(),T("span",Ue,n[0]||(n[0]=[o("i",{class:"fas fa-arrows-up-down-left-right text-gray-400 dark:text-gray-900"},null,-1)]))),[[v,s.$trans("general.move")]]),e.isOverdue?x((l(),T("span",Be,[n[1]||(n[1]=o("i",{class:"far fa-clock"},null,-1)),I(" "+h(e.due.formatted),1)])),[[v,e.overdueDaysDisplay]]):(l(),T("span",Ie,[n[2]||(n[2]=o("i",{class:"far fa-clock"},null,-1)),I(" "+h(e.due.formatted),1)]))]),o("span",Le,[e.completedAt?x((l(),T("span",De,[o("i",{class:"far fa-times-circle fa-lg text-danger cursor-pointer",onClick:t=>V(e)},null,8,qe)])),[[v,s.$trans("utility.todo.mark_as_incomplete")]]):x((l(),T("span",Te,[o("i",{class:"far fa-check-circle fa-lg text-success cursor-pointer",onClick:t=>V(e)},null,8,Ve)])),[[v,s.$trans("utility.todo.mark_as_complete")]]),e.completedAt&&!e.isArchived?x((l(),T("span",xe,[o("i",{class:"fas fa-box-archive cursor-pointer text-gray-400",onClick:t=>z(e)},null,8,Fe)])),[[v,s.$trans("general.archive")]]):L("",!0),e.isArchived?x((l(),T("span",Se,[o("i",{class:"fas fa-box-open cursor-pointer text-gray-400",onClick:t=>C(e)},null,8,Re)])),[[v,s.$trans("general.unarchive")]]):L("",!0)])])])],2)]),_:2},1032,["list"])])])]),_:1},8,["list","disabled"])]),_:1},8,["add-list"])}}}),je={class:"grid grid-cols-3 gap-6"},ze={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},He={class:"col-span-3 sm:col-span-1"},Me={class:"col-span-3 sm:col-span-1"},Ne={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(H,{emit:$}){const k=$,g=H,D={search:"",startDate:"",endDate:"",status:"",isArchived:!1},f=j({...D}),q=j({statuses:g.preRequisites.statuses});return(m,c)=>{const U=p("BaseInput"),w=p("DatePicker"),r=p("BaseSelect"),V=p("BaseSwitch"),z=p("FilterForm");return l(),y(z,{"init-form":D,form:f,onHide:c[5]||(c[5]=C=>k("hide"))},{default:i(()=>[o("div",je,[o("div",ze,[d(U,{type:"text",modelValue:f.search,"onUpdate:modelValue":c[0]||(c[0]=C=>f.search=C),name:"search",label:m.$trans("general.search")},null,8,["modelValue","label"])]),o("div",Ee,[d(w,{start:f.startDate,"onUpdate:start":c[1]||(c[1]=C=>f.startDate=C),end:f.endDate,"onUpdate:end":c[2]||(c[2]=C=>f.endDate=C),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])]),o("div",He,[d(r,{modelValue:f.status,"onUpdate:modelValue":c[3]||(c[3]=C=>f.status=C),name:"status",label:m.$trans("utility.todo.status"),options:q.statuses},null,8,["modelValue","label","options"])]),o("div",Me,[d(V,{vertical:"",modelValue:f.isArchived,"onUpdate:modelValue":c[4]||(c[4]=C=>f.isArchived=C),name:"isArchived",label:m.$trans("general.archived")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Ke={class:"space-x-1"},Ge={key:0},Je=["onClick"],We={key:1},Xe=["onClick"],Ye={name:"UilityTodoList"},tt=Object.assign(Ye,{setup(H){const $=X(),k=Y();Z();const g=Q("emitter");let D=[];M("todo:export")&&(D=["print","pdf","excel"]);const f=de("utility.todoView"),q="utility/todo/",m=j({statuses:[],todoLists:[]}),c=J(!1),U=J(!1),w=j({data:[]}),r=j({...me}),V=e=>{Object.assign(w,e),r.pageItems=w.data.map(t=>t.uuid),c.value=!1},z=async e=>{g.emit("actionItem",{uuid:e.uuid,action:"status",confirmation:!0})},C=async e=>{g.emit("actionItem",{uuid:e.uuid,action:"archive",confirmation:!0})},s=async e=>{g.emit("actionItem",{uuid:e.uuid,action:"unarchive",confirmation:!0})},n=e=>{Object.assign(m,e)},B=e=>{e=="delete"&&g.emit("deleteItems",{global:r.global,uuids:r.items})},v=e=>{m.todoLists.push(e)},A=()=>{$.name=="UtilityTodoList"&&(!$.query.view||$.query.view===void 0)&&k.push({query:{view:f.value}})};return ee(()=>[r.items,r.pageItems,$.query.view],([e,t,_],[S,R,F])=>{_!=F?(A(),_&&F!==void 0&&g.emit("listItems")):r.all=fe(r)}),te(()=>{c.value=!0,A()}),(e,t)=>{const _=p("BaseButton"),S=p("PageHeaderAction"),R=p("PageHeader"),F=p("ParentTransition"),O=p("BaseArrayCheckbox"),ie=p("BaseCheckbox"),P=p("DataCell"),K=p("BaseBadge"),E=p("FloatingMenuItem"),ae=p("FloatingMenu"),oe=p("DataRow"),ne=p("DataTable"),le=p("ListItem"),N=se("tooltip");return l(),y(le,{"init-url":q,"pre-requisites":!0,onSetPreRequisites:n,onSetItems:V},{header:i(()=>[d(R,{title:e.$trans("utility.todo.todo")},{default:i(()=>[d(S,{url:"utility/todos/",name:"UtilityTodo",title:e.$trans("utility.todo.todo"),actions:["create","filter"],"bulk-actions":[{name:"delete",label:e.$trans("general.delete"),icon:"fas fa-trash"}],"show-bulk-action":r.items.length>0||r.global,"dropdown-actions":u(D),headers:w.headers,onToggleFilter:t[3]||(t[3]=a=>U.value=!U.value),onOnBulkAction:B},{after:i(()=>[u(M)("utility:config")?(l(),y(_,{key:0,design:"white",onClick:t[0]||(t[0]=a=>u(k).push({name:"UtilityConfig"}))},{default:i(()=>t[12]||(t[12]=[o("i",{class:"fas fa-cog"},null,-1)])),_:1})):L("",!0),u($).query.view!="list"?x((l(),y(_,{key:1,design:"white",onClick:t[1]||(t[1]=a=>u(k).push({query:{view:"list"}}))},{default:i(()=>t[13]||(t[13]=[o("i",{class:"fas fa-list"},null,-1)])),_:1})),[[N,e.$trans("global._view",{attribute:e.$trans("general.views.list")})]]):L("",!0),u($).query.view!="board"?x((l(),y(_,{key:2,design:"white",onClick:t[2]||(t[2]=a=>u(k).push({query:{view:"board"}}))},{default:i(()=>t[14]||(t[14]=[o("i",{class:"fas fa-table"},null,-1)])),_:1})),[[N,e.$trans("global._view",{attribute:e.$trans("general.views.board")})]]):L("",!0)]),_:1},8,["title","bulk-actions","show-bulk-action","dropdown-actions","headers"])]),_:1},8,["title"])]),filter:i(()=>[d(F,{appear:"",visibility:U.value},{default:i(()=>[d(Ne,{onRefresh:t[4]||(t[4]=a=>u(g).emit("listItems")),"pre-requisites":m,onHide:t[5]||(t[5]=a=>U.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:i(()=>[u($).query.view=="list"?(l(),y(F,{key:0,appear:"",visibility:!0},{default:i(()=>[d(ne,{onToggleSelectAll:t[9]||(t[9]=a=>r.items=u(pe)(a,r)),onToggleGlobalSelect:t[10]||(t[10]=a=>r.global=!r.global),selected:r,header:w.headers,meta:w.meta,module:"utility.todo",onRefresh:t[11]||(t[11]=a=>u(g).emit("listItems"))},{actionButton:i(()=>[d(_,{onClick:t[8]||(t[8]=a=>u(k).push({name:"UtilityTodoCreate"}))},{default:i(()=>[I(h(e.$trans("global.add",{attribute:e.$trans("utility.todo.todo")})),1)]),_:1})]),default:i(()=>[(l(!0),T(ue,null,ce(w.data,a=>(l(),y(oe,{key:a.uuid,onDoubleClick:b=>u(k).push({name:"UtilityTodoShow",params:{uuid:a.uuid}})},{default:i(()=>[d(P,{name:"selectAll"},{default:i(()=>[r.global?L("",!0):(l(),y(O,{key:0,items:r.items,"onUpdate:items":t[6]||(t[6]=b=>r.items=b),value:a.uuid},null,8,["items","value"])),r.global?(l(),y(ie,{key:1,modelValue:r.global,"onUpdate:modelValue":t[7]||(t[7]=b=>r.global=b)},null,8,["modelValue"])):L("",!0)]),_:2},1024),d(P,{name:"title"},{default:i(()=>[I(h(a.title),1)]),_:2},1024),d(P,{name:"due"},{default:i(()=>[I(h(a.due.formatted),1)]),_:2},1024),d(P,{name:"status"},{default:i(()=>[a.status?(l(),y(K,{key:0,design:"success",label:e.$trans("utility.todo.completed")},null,8,["label"])):(l(),y(K,{key:1,design:"error",label:e.$trans("utility.todo.incomplete")},null,8,["label"]))]),_:2},1024),d(P,{name:"completedAt"},{default:i(()=>{var b;return[I(h((b=a.completedAt)==null?void 0:b.formatted),1)]}),_:2},1024),d(P,{name:"statusUpdate"},{default:i(()=>[o("span",Ke,[a.completedAt?x((l(),T("span",Ge,[o("i",{class:"far fa-times-circle fa-lg text-danger cursor-pointer",onClick:b=>z(a)},null,8,Je)])),[[N,e.$trans("utility.todo.mark_as_incomplete")]]):x((l(),T("span",We,[o("i",{class:"far fa-check-circle fa-lg text-success cursor-pointer",onClick:b=>z(a)},null,8,Xe)])),[[N,e.$trans("utility.todo.mark_as_complete")]])])]),_:2},1024),d(P,{name:"action"},{default:i(()=>[d(ae,null,{default:i(()=>[a.completedAt&&!a.isArchived?(l(),y(E,{key:0,icon:"fas fa-box-archive",onClick:b=>C(a)},{default:i(()=>[I(h(e.$trans("general.archive")),1)]),_:2},1032,["onClick"])):L("",!0),a.isArchived?(l(),y(E,{key:1,icon:"fas fa-box-open",onClick:b=>s(a)},{default:i(()=>[I(h(e.$trans("general.unarchive")),1)]),_:2},1032,["onClick"])):L("",!0),d(E,{icon:"fas fa-arrow-circle-right",onClick:b=>u(k).push({name:"UtilityTodoShow",params:{uuid:a.uuid}})},{default:i(()=>[I(h(e.$trans("general.show")),1)]),_:2},1032,["onClick"]),d(E,{icon:"fas fa-edit",onClick:b=>u(k).push({name:"UtilityTodoEdit",params:{uuid:a.uuid}})},{default:i(()=>[I(h(e.$trans("general.edit")),1)]),_:2},1032,["onClick"]),d(E,{icon:"fas fa-copy",onClick:b=>u(k).push({name:"UtilityTodoDuplicate",params:{uuid:a.uuid}})},{default:i(()=>[I(h(e.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),d(E,{icon:"fas fa-trash",onClick:b=>u(g).emit("deleteItem",{uuid:a.uuid})},{default:i(()=>[I(h(e.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["selected","header","meta"])]),_:1})):L("",!0),u($).query.view=="board"?(l(),y(F,{key:1,appear:"",visibility:!0},{default:i(()=>[w.meta?(l(),y(Pe,{key:0,"is-loading":c.value,todos:w,"pre-requisites":m,onNewListAdded:v},null,8,["is-loading","todos","pre-requisites"])):L("",!0)]),_:1})):L("",!0)]),_:1})}}});export{tt as default};
