import{l as R,r as i,q as f,o as p,w as e,d as y,e as n,h as H,j,y as v,m as L,f as o,a as M,F as S,v as U,s as l,t as d,b as D}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(B,{emit:m}){const b=m,g={letterNumber:"",startDate:"",endDate:""},u=R({...g});return(C,r)=>{const _=i("BaseInput"),w=i("DatePicker"),s=i("FilterForm");return p(),f(s,{"init-form":g,form:u,multiple:[],onHide:r[3]||(r[3]=a=>b("hide"))},{default:e(()=>[y("div",E,[y("div",O,[n(_,{type:"text",modelValue:u.letterNumber,"onUpdate:modelValue":r[0]||(r[0]=a=>u.letterNumber=a),name:"letterNumber",label:C.$trans("reception.correspondence.props.letter_number")},null,8,["modelValue","label"])]),y("div",q,[n(w,{start:u.startDate,"onUpdate:start":r[1]||(r[1]=a=>u.startDate=a),end:u.endDate,"onUpdate:end":r[2]||(r[2]=a=>u.endDate=a),name:"dateBetween",as:"range",label:C.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},G={name:"ReceptionCorrespondenceList"},K=Object.assign(G,{setup(B){const m=H(),b=j("emitter");let g=["filter"];v("correspondence:create")&&g.unshift("create");let u=[];v("correspondence:export")&&(u=["print","pdf","excel"]);const C="reception/correspondence/",r=L(!1),_=R({}),w=s=>{Object.assign(_,s)};return(s,a)=>{const I=i("PageHeaderAction"),h=i("PageHeader"),F=i("ParentTransition"),c=i("DataCell"),$=i("FloatingMenuItem"),N=i("FloatingMenu"),P=i("DataRow"),T=i("BaseButton"),V=i("DataTable"),A=i("ListItem");return p(),f(A,{"init-url":C,onSetItems:w},{header:e(()=>[n(h,{title:s.$trans("reception.correspondence.correspondence"),navs:[{label:s.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[n(I,{url:"reception/correspondences/",name:"ReceptionCorrespondence",title:s.$trans("reception.correspondence.correspondence"),actions:o(g),"dropdown-actions":o(u),onToggleFilter:a[0]||(a[0]=t=>r.value=!r.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(F,{appear:"",visibility:r.value},{default:e(()=>[n(z,{onRefresh:a[1]||(a[1]=t=>o(b).emit("listItems")),onHide:a[2]||(a[2]=t=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(V,{header:_.headers,meta:_.meta,module:"reception.correspondence",onRefresh:a[4]||(a[4]=t=>o(b).emit("listItems"))},{actionButton:e(()=>[o(v)("correspondence:create")?(p(),f(T,{key:0,onClick:a[3]||(a[3]=t=>o(m).push({name:"ReceptionCorrespondenceCreate"}))},{default:e(()=>[l(d(s.$trans("global.add",{attribute:s.$trans("reception.correspondence.correspondence")})),1)]),_:1})):D("",!0)]),default:e(()=>[(p(!0),M(S,null,U(_.data,t=>(p(),f(P,{key:t.uuid,onDoubleClick:k=>o(m).push({name:"ReceptionCorrespondenceShow",params:{uuid:t.uuid}})},{default:e(()=>[n(c,{name:"type"},{default:e(()=>[l(d(t.type.label),1)]),_:2},1024),n(c,{name:"letterNumber"},{default:e(()=>[l(d(t.letterNumber),1)]),_:2},1024),n(c,{name:"mode"},{default:e(()=>[l(d(t.mode.label),1)]),_:2},1024),n(c,{name:"sender"},{default:e(()=>[l(d(t.senderTitle),1)]),_:2},1024),n(c,{name:"receiver"},{default:e(()=>[l(d(t.receiverTitle),1)]),_:2},1024),n(c,{name:"date"},{default:e(()=>[l(d(t.date.formatted),1)]),_:2},1024),n(c,{name:"createdAt"},{default:e(()=>[l(d(t.createdAt.formatted),1)]),_:2},1024),n(c,{name:"action"},{default:e(()=>[n(N,null,{default:e(()=>[n($,{icon:"fas fa-arrow-circle-right",onClick:k=>o(m).push({name:"ReceptionCorrespondenceShow",params:{uuid:t.uuid}})},{default:e(()=>[l(d(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(v)("correspondence:edit")?(p(),f($,{key:0,icon:"fas fa-edit",onClick:k=>o(m).push({name:"ReceptionCorrespondenceEdit",params:{uuid:t.uuid}})},{default:e(()=>[l(d(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):D("",!0),o(v)("correspondence:create")?(p(),f($,{key:1,icon:"fas fa-copy",onClick:k=>o(m).push({name:"ReceptionCorrespondenceDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[l(d(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):D("",!0),o(v)("correspondence:delete")?(p(),f($,{key:2,icon:"fas fa-trash",onClick:k=>o(b).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[l(d(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
