import{H as $,l as D,r as m,q as A,o as f,w as b,d as i,e as r,f as o,J as P,u as B,a as F,F as S}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-6"},k={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},y={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3"},w={name:"AcademicSessionForm"},E=Object.assign(w,{setup(v){const d={name:"",code:"",shortcode:"",alias:"",startDate:"",endDate:"",description:""},l="academic/session/",t=$(l),c=D({}),a=D({...d}),u=n=>{Object.assign(c,n)},U=n=>{Object.assign(d,{...n,startDate:n.startDate.value,endDate:n.endDate.value}),Object.assign(a,P(d))};return(n,e)=>{const p=m("BaseInput"),V=m("DatePicker"),_=m("BaseTextarea"),g=m("FormAction");return f(),A(g,{"has-setup-wizard":!0,"pre-requisites":!1,onSetPreRequisites:u,"init-url":l,"init-form":d,form:a,"set-form":U,redirect:"AcademicSession"},{default:b(()=>[i("div",j,[i("div",k,[r(p,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=s=>a.name=s),name:"name",label:n.$trans("academic.session.props.name"),error:o(t).name,"onUpdate:error":e[1]||(e[1]=s=>o(t).name=s)},null,8,["modelValue","label","error"])]),i("div",q,[r(p,{type:"text",modelValue:a.code,"onUpdate:modelValue":e[2]||(e[2]=s=>a.code=s),name:"code",label:n.$trans("academic.session.props.code"),error:o(t).code,"onUpdate:error":e[3]||(e[3]=s=>o(t).code=s)},null,8,["modelValue","label","error"])]),i("div",y,[r(p,{type:"text",modelValue:a.shortcode,"onUpdate:modelValue":e[4]||(e[4]=s=>a.shortcode=s),name:"shortcode",label:n.$trans("academic.session.props.shortcode"),error:o(t).shortcode,"onUpdate:error":e[5]||(e[5]=s=>o(t).shortcode=s)},null,8,["modelValue","label","error"])]),i("div",H,[r(p,{type:"text",modelValue:a.alias,"onUpdate:modelValue":e[6]||(e[6]=s=>a.alias=s),name:"alias",label:n.$trans("academic.session.props.alias"),error:o(t).alias,"onUpdate:error":e[7]||(e[7]=s=>o(t).alias=s)},null,8,["modelValue","label","error"])]),i("div",O,[r(V,{modelValue:a.startDate,"onUpdate:modelValue":e[8]||(e[8]=s=>a.startDate=s),name:"startDate",label:n.$trans("academic.session.props.start_date"),"no-clear":"",error:o(t).startDate,"onUpdate:error":e[9]||(e[9]=s=>o(t).startDate=s)},null,8,["modelValue","label","error"])]),i("div",R,[r(V,{modelValue:a.endDate,"onUpdate:modelValue":e[10]||(e[10]=s=>a.endDate=s),name:"endDate",label:n.$trans("academic.session.props.end_date"),"no-clear":"",error:o(t).endDate,"onUpdate:error":e[11]||(e[11]=s=>o(t).endDate=s)},null,8,["modelValue","label","error"])]),i("div",T,[r(_,{modelValue:a.description,"onUpdate:modelValue":e[12]||(e[12]=s=>a.description=s),name:"description",label:n.$trans("academic.session.props.description"),error:o(t).description,"onUpdate:error":e[13]||(e[13]=s=>o(t).description=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),C={name:"AcademicSessionAction"},N=Object.assign(C,{setup(v){const d=B();return(l,t)=>{const c=m("PageHeaderAction"),a=m("PageHeader"),u=m("ParentTransition");return f(),F(S,null,[r(a,{title:l.$trans(o(d).meta.trans,{attribute:l.$trans(o(d).meta.label)}),navs:[{label:l.$trans("academic.academic"),path:"Academic"},{label:l.$trans("academic.session.session"),path:"AcademicSessionList"}]},{default:b(()=>[r(c,{name:"AcademicSession",title:l.$trans("academic.session.session"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(u,{appear:"",visibility:!0},{default:b(()=>[r(E)]),_:1})],64)}}});export{N as default};
