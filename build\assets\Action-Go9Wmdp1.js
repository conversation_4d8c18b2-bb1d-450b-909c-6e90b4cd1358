import{H as b,l as g,r as l,q as V,o as u,w as m,d as c,e as s,f as n,u as $,a as B,F}from"./app-BAwPsakn.js";const U={class:"grid grid-cols-3 gap-6"},P={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},H={name:"DeviceForm"},T=Object.assign(H,{setup(_){const i={name:"",code:"",description:""},a="device/",r=b(a),t=g({...i});return(d,e)=>{const p=l("BaseInput"),v=l("BaseTextarea"),f=l("FormAction");return u(),V(f,{"init-url":a,"init-form":i,form:t,redirect:"Device"},{default:m(()=>[c("div",U,[c("div",P,[s(p,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),name:"name",label:d.$trans("device.props.name"),error:n(r).name,"onUpdate:error":e[1]||(e[1]=o=>n(r).name=o),autofocus:""},null,8,["modelValue","label","error"])]),c("div",A,[s(p,{type:"text",modelValue:t.code,"onUpdate:modelValue":e[2]||(e[2]=o=>t.code=o),name:"code",label:d.$trans("device.props.code"),error:n(r).code,"onUpdate:error":e[3]||(e[3]=o=>n(r).code=o)},null,8,["modelValue","label","error"])]),c("div",D,[s(v,{modelValue:t.description,"onUpdate:modelValue":e[4]||(e[4]=o=>t.description=o),name:"description",label:d.$trans("device.props.description"),error:n(r).description,"onUpdate:error":e[5]||(e[5]=o=>n(r).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),k={name:"DeviceAction"},E=Object.assign(k,{setup(_){const i=$();return(a,r)=>{const t=l("PageHeaderAction"),d=l("PageHeader"),e=l("ParentTransition");return u(),B(F,null,[s(d,{title:a.$trans(n(i).meta.trans,{attribute:a.$trans(n(i).meta.label)}),navs:[{label:a.$trans("device.device"),path:"Device"}]},{default:m(()=>[s(t,{name:"Device",title:a.$trans("device.device"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(e,{appear:"",visibility:!0},{default:m(()=>[s(T)]),_:1})],64)}}});export{E as default};
