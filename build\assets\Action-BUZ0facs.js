import{i as L,u as A,h as z,H as G,g as J,l as U,r as m,z as M,q as B,o as p,w as c,d as n,a as u,b as i,e as l,f as r,s as v,t as b,F as S,I as K,A as Q,aP as W,J as X}from"./app-BAwPsakn.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3"},h={key:0,class:"col-span-3"},x={key:0,class:"ml-1"},ee={key:0,class:"ml-1"},oe={class:"col-span-3 sm:col-span-2"},te={class:"flex"},ae={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},ne={class:"mt-4 grid grid-cols-3 gap-6"},me={key:0,class:"col-span-3 sm:col-span-1"},pe={key:1,class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ce={key:0,class:"mt-4 grid grid-cols-3 gap-6"},ge={class:"col-span-3"},Ve={class:"text-danger"},fe={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},Ue={class:"col-span-3 sm:col-span-1"},ve={class:"flex space-x-2"},we={class:"col-span-3 sm:col-span-1"},$e={name:"EmployeeForm"},Te=Object.assign($e,{setup(C){L();const f=A();z();const y={department:"",designation:"",employmentStatus:"",joiningDate:"",codeNumber:"",employeeType:"new",firstName:"",middleName:"",thirdName:"",lastName:"",birthDate:"",gender:"",contactNumber:"",email:"",roles:[],createUserAccount:!1,username:"",password:"",passwordConfirmation:"",type:""},w="employee/",a=G(w),$=J("teams"),d=U({genders:[],employeeTypes:[],types:[],codeNumber:"",departments:[],designations:[],employmentStatuses:[],roles:[]}),t=U({...y}),T=U({hidePassword:!0}),N=U({department:"",designation:"",employmentStatus:"",isLoaded:!f.params.uuid}),P=s=>{Object.assign(t,s),N.department=s.department.name,N.designation=s.designation.name,N.employmentStatus=s.employmentStatus.name,N.isLoaded=!0},j=s=>{Object.assign(d,s),y.codeNumber=s.codeNumber,Object.assign(t,X(y))},E=()=>{var s=W(12);t.password=s,t.passwordConfirmation=s};return(s,o)=>{const k=m("BaseRadioGroup"),q=m("BaseSelectSearch"),D=m("BaseLabel"),F=m("NameInput"),_=m("DatePicker"),g=m("BaseInput"),V=m("BaseSelect"),R=m("BaseSwitch"),H=m("HelperText"),O=m("FormAction"),I=M("tooltip");return p(),B(O,{"pre-requisites":!0,onSetPreRequisites:j,"init-url":w,"init-form":y,form:t,"set-form":P,redirect:"Employee"},{default:c(()=>[n("div",Y,[n("div",Z,[l(k,{"top-margin":"",options:d.employeeTypes,name:"employeeType",modelValue:t.employeeType,"onUpdate:modelValue":o[0]||(o[0]=e=>t.employeeType=e),error:r(a).employeeType,"onUpdate:error":o[1]||(o[1]=e=>r(a).employeeType=e),horizontal:""},null,8,["options","modelValue","error"])]),t.employeeType=="existing"||t.employeeType=="other_team_member"?(p(),u("div",h,[l(q,{name:"employee",label:s.$trans("global.select",{attribute:s.$trans("employee.employee")}),modelValue:t.employee,"onUpdate:modelValue":o[2]||(o[2]=e=>t.employee=e),error:r(a).employee,"onUpdate:error":o[3]||(o[3]=e=>r(a).employee=e),"value-prop":"uuid","search-action":"employee/list","search-key":"search","additional-search-query":{status:"all",otherTeamMember:t.employeeType=="other_team_member"}},{selectedOption:c(e=>[v(b(e.value.name)+" ("+b(e.value.codeNumber)+") ",1),t.employeeType=="other_team_member"?(p(),u("span",x,b(e.value.teamName),1)):i("",!0)]),listOption:c(e=>[v(b(e.option.name)+" ("+b(e.option.codeNumber)+") ",1),t.employeeType=="other_team_member"?(p(),u("span",ee,b(e.option.teamName),1)):i("",!0)]),_:1},8,["label","modelValue","error","additional-search-query"])])):i("",!0),t.employeeType=="new"?(p(),u(S,{key:1},[n("div",oe,[l(D,null,{default:c(()=>[v(b(s.$trans("employee.props.name")),1)]),_:1}),n("div",te,[l(F,{firstName:t.firstName,"onUpdate:firstName":o[4]||(o[4]=e=>t.firstName=e),middleName:t.middleName,"onUpdate:middleName":o[5]||(o[5]=e=>t.middleName=e),thirdName:t.thirdName,"onUpdate:thirdName":o[6]||(o[6]=e=>t.thirdName=e),lastName:t.lastName,"onUpdate:lastName":o[7]||(o[7]=e=>t.lastName=e),formErrors:r(a),"onUpdate:formErrors":o[8]||(o[8]=e=>K(a)?a.value=e:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),n("div",ae,[l(D,null,{default:c(()=>[v(b(s.$trans("contact.props.gender")),1)]),_:1}),l(k,{"top-margin":"",options:d.genders,name:"gender",modelValue:t.gender,"onUpdate:modelValue":o[9]||(o[9]=e=>t.gender=e),error:r(a).gender,"onUpdate:error":o[10]||(o[10]=e=>r(a).gender=e),horizontal:""},null,8,["options","modelValue","error"])]),n("div",re,[l(_,{modelValue:t.birthDate,"onUpdate:modelValue":o[11]||(o[11]=e=>t.birthDate=e),name:"birthDate",label:s.$trans("contact.props.birth_date"),"no-clear":"",error:r(a).birthDate,"onUpdate:error":o[12]||(o[12]=e=>r(a).birthDate=e)},null,8,["modelValue","label","error"])]),n("div",se,[l(g,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":o[13]||(o[13]=e=>t.contactNumber=e),name:"contactNumber",label:s.$trans("contact.props.contact_number"),error:r(a).contactNumber,"onUpdate:error":o[14]||(o[14]=e=>r(a).contactNumber=e),autofocus:""},null,8,["modelValue","label","error"])]),n("div",le,[l(g,{type:"text",modelValue:t.email,"onUpdate:modelValue":o[15]||(o[15]=e=>t.email=e),name:"email",label:s.$trans("contact.props.email"),error:r(a).email,"onUpdate:error":o[16]||(o[16]=e=>r(a).email=e),autofocus:""},null,8,["modelValue","label","error"])])],64)):i("",!0)]),n("div",ne,[t.employeeType!="other_team_member"?(p(),u("div",me,[l(g,{type:"text",modelValue:t.codeNumber,"onUpdate:modelValue":o[17]||(o[17]=e=>t.codeNumber=e),name:"codeNumber",label:s.$trans("employee.props.code_number"),error:r(a).codeNumber,"onUpdate:error":o[18]||(o[18]=e=>r(a).codeNumber=e),autofocus:""},null,8,["modelValue","label","error"])])):i("",!0),t.employeeType=="other_team_member"&&r($).length>1?(p(),u("div",pe,[l(V,{modelValue:t.roles,"onUpdate:modelValue":o[19]||(o[19]=e=>t.roles=e),name:"roles",label:s.$trans("contact.login.props.role"),options:d.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:r(a).roles,"onUpdate:error":o[20]||(o[20]=e=>r(a).roles=e)},null,8,["modelValue","label","options","error"])])):i("",!0),n("div",de,[l(_,{modelValue:t.joiningDate,"onUpdate:modelValue":o[21]||(o[21]=e=>t.joiningDate=e),name:"joiningDate",label:s.$trans("employee.props.joining_date"),"no-clear":"",error:r(a).joiningDate,"onUpdate:error":o[22]||(o[22]=e=>r(a).joiningDate=e)},null,8,["modelValue","label","error"])]),n("div",ie,[l(V,{name:"type",label:s.$trans("global.select",{attribute:s.$trans("employee.type")}),options:d.types,modelValue:t.type,"onUpdate:modelValue":o[23]||(o[23]=e=>t.type=e),error:r(a).type,"onUpdate:error":o[24]||(o[24]=e=>r(a).type=e)},null,8,["label","options","modelValue","error"])]),n("div",ue,[l(V,{name:"employmentStatus",label:s.$trans("global.select",{attribute:s.$trans("employee.employment_status.employment_status")}),modelValue:t.employmentStatus,"onUpdate:modelValue":o[25]||(o[25]=e=>t.employmentStatus=e),error:r(a).employmentStatus,"onUpdate:error":o[26]||(o[26]=e=>r(a).employmentStatus=e),"label-prop":"name","value-prop":"uuid",options:d.employmentStatuses},null,8,["label","modelValue","error","options"])]),n("div",ye,[l(V,{name:"department",label:s.$trans("global.select",{attribute:s.$trans("employee.department.department")}),modelValue:t.department,"onUpdate:modelValue":o[27]||(o[27]=e=>t.department=e),error:r(a).department,"onUpdate:error":o[28]||(o[28]=e=>r(a).department=e),"label-prop":"name","value-prop":"uuid",options:d.departments},null,8,["label","modelValue","error","options"])]),n("div",be,[l(V,{name:"designation",label:s.$trans("global.select",{attribute:s.$trans("employee.designation.designation")}),modelValue:t.designation,"onUpdate:modelValue":o[29]||(o[29]=e=>t.designation=e),error:r(a).designation,"onUpdate:error":o[30]||(o[30]=e=>r(a).designation=e),"label-prop":"name","value-prop":"uuid",options:d.designations},null,8,["label","modelValue","error","options"])])]),t.employeeType=="new"?(p(),u("div",ce,[n("div",ge,[l(R,{vertical:"",modelValue:t.createUserAccount,"onUpdate:modelValue":o[31]||(o[31]=e=>t.createUserAccount=e),name:"createUserAccount",label:s.$trans("global.create",{attribute:s.$trans("contact.user_account")}),error:r(a).createUserAccount,"onUpdate:error":o[32]||(o[32]=e=>r(a).createUserAccount=e)},null,8,["modelValue","label","error"]),t.email?i("",!0):(p(),B(H,{key:0},{default:c(()=>[n("div",Ve,b(s.$trans("employee.email_required_to_create_account")),1)]),_:1}))]),t.email&&t.createUserAccount?(p(),u(S,{key:0},[n("div",fe,[l(V,{modelValue:t.roles,"onUpdate:modelValue":o[33]||(o[33]=e=>t.roles=e),name:"roles",label:s.$trans("contact.login.props.role"),options:d.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:r(a).roles,"onUpdate:error":o[34]||(o[34]=e=>r(a).roles=e)},null,8,["modelValue","label","options","error"])]),n("div",Ne,[l(g,{type:"text",modelValue:t.username,"onUpdate:modelValue":o[35]||(o[35]=e=>t.username=e),name:"username",label:s.$trans("contact.login.props.username"),error:r(a).username,"onUpdate:error":o[36]||(o[36]=e=>r(a).username=e)},null,8,["modelValue","label","error"])]),n("div",Ue,[l(g,{type:T.hidePassword?"password":"text",modelValue:t.password,"onUpdate:modelValue":o[38]||(o[38]=e=>t.password=e),name:"password",label:s.$trans("contact.login.props.password"),error:r(a).password,"onUpdate:error":o[39]||(o[39]=e=>r(a).password=e)},{"additional-label":c(()=>[n("div",ve,[Q(n("i",{class:"fas fa-key cursor-pointer",onClick:E},null,512),[[I,s.$trans("global.generate",{attribute:s.$trans("auth.login.props.password")})]]),t.password?(p(),u("i",{key:0,class:"fas fa-eye cursor-pointer",onClick:o[37]||(o[37]=e=>T.hidePassword=!T.hidePassword)})):i("",!0)])]),_:1},8,["type","modelValue","label","error"])]),n("div",we,[l(g,{type:"password",modelValue:t.passwordConfirmation,"onUpdate:modelValue":o[40]||(o[40]=e=>t.passwordConfirmation=e),name:"passwordConfirmation",label:s.$trans("contact.login.props.password_confirmation"),error:r(a).passwordConfirmation,"onUpdate:error":o[41]||(o[41]=e=>r(a).passwordConfirmation=e)},null,8,["modelValue","label","error"])])],64)):i("",!0)])):i("",!0)]),_:1},8,["form"])}}}),Se={name:"EmployeeAction"},De=Object.assign(Se,{setup(C){const f=A();return(y,w)=>{const a=m("PageHeaderAction"),$=m("PageHeader"),d=m("ParentTransition");return p(),u(S,null,[l($,{title:y.$trans(r(f).meta.trans,{attribute:y.$trans(r(f).meta.label)}),navs:[{label:y.$trans("employee.employee"),path:"EmployeeList"}]},{default:c(()=>[l(a,{name:"Employee",title:y.$trans("employee.employee"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(d,{appear:"",visibility:!0},{default:c(()=>[l(Te)]),_:1})],64)}}});export{De as default};
