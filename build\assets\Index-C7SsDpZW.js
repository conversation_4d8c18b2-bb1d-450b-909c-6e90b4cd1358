import{l as I,r as i,q as _,o as y,w as e,d as B,e as a,u as A,h as L,j as M,y as h,m as N,f as s,a as w,F as E,v as O,s as n,b as D,t as l}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",emits:["hide"],setup(o,{emit:F}){const v=F,g={course:"",institute:"",affiliatedTo:""},d=I({...g});return($,m)=>{const p=i("BaseInput"),b=i("FilterForm");return y(),_(b,{"init-form":g,form:d,onHide:m[3]||(m[3]=c=>v("hide"))},{default:e(()=>[B("div",z,[B("div",G,[a(p,{type:"text",modelValue:d.course,"onUpdate:modelValue":m[0]||(m[0]=c=>d.course=c),name:"course",label:$.$trans("employee.qualification.props.course")},null,8,["modelValue","label"])]),B("div",J,[a(p,{type:"text",modelValue:d.institute,"onUpdate:modelValue":m[1]||(m[1]=c=>d.institute=c),name:"institute",label:$.$trans("employee.qualification.props.institute")},null,8,["modelValue","label"])]),B("div",K,[a(p,{type:"text",modelValue:d.affiliatedTo,"onUpdate:modelValue":m[2]||(m[2]=c=>d.affiliatedTo=c),name:"affiliatedTo",label:$.$trans("employee.qualification.props.affiliated_to")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},X={name:"EmployeeQualificationList"},Z=Object.assign(X,{props:{employee:{type:Object,default(){return{}}}},setup(o){const F=A(),v=L(),g=M("emitter"),d=o;let $=["filter"];(h("employee:edit")||d.employee.selfService)&&$.unshift("create");const m="employee/qualification/",p=N(!1),b=I({}),c=u=>{Object.assign(b,u)};return(u,r)=>{const S=i("PageHeaderAction"),T=i("PageHeader"),V=i("ParentTransition"),Q=i("BaseBadge"),f=i("DataCell"),k=i("FloatingMenuItem"),H=i("FloatingMenu"),P=i("DataRow"),R=i("BaseButton"),U=i("DataTable"),j=i("ListItem");return y(),_(j,{"init-url":m,uuid:s(F).params.uuid,onSetItems:c},{header:e(()=>[o.employee.uuid?(y(),_(T,{key:0,title:u.$trans("employee.qualification.qualification"),navs:[{label:u.$trans("employee.employee"),path:"Employee"},{label:o.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:o.employee.uuid}}}]},{default:e(()=>[a(S,{url:`employees/${o.employee.uuid}/qualifications/`,name:"EmployeeQualification",title:u.$trans("employee.qualification.qualification"),actions:s($),"dropdown-actions":["print","pdf","excel"],onToggleFilter:r[0]||(r[0]=t=>p.value=!p.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):D("",!0)]),filter:e(()=>[a(V,{appear:"",visibility:p.value},{default:e(()=>[a(W,{onRefresh:r[1]||(r[1]=t=>s(g).emit("listItems")),onHide:r[2]||(r[2]=t=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(V,{appear:"",visibility:!0},{default:e(()=>[a(U,{header:b.headers,meta:b.meta,module:"employee.qualification",onRefresh:r[4]||(r[4]=t=>s(g).emit("listItems"))},{actionButton:e(()=>[s(h)("employee:edit")||o.employee.selfService?(y(),_(R,{key:0,onClick:r[3]||(r[3]=t=>s(v).push({name:"EmployeeQualificationCreate"}))},{default:e(()=>[n(l(u.$trans("global.add",{attribute:u.$trans("employee.qualification.qualification")})),1)]),_:1})):D("",!0)]),default:e(()=>[(y(!0),w(E,null,O(b.data,t=>(y(),_(P,{key:t.uuid,onDoubleClick:C=>s(v).push({name:"EmployeeQualificationShow",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[a(f,{name:"course"},{default:e(()=>[n(l(t.course)+" ",1),t.selfUpload?(y(),_(Q,{key:0,design:t.verificationStatus.color},{default:e(()=>[n(l(t.verificationStatus.label),1)]),_:2},1032,["design"])):D("",!0)]),_:2},1024),a(f,{name:"institute"},{default:e(()=>[n(l(t.institute),1)]),_:2},1024),a(f,{name:"level"},{default:e(()=>[n(l(t.level.name),1)]),_:2},1024),a(f,{name:"startDate"},{default:e(()=>[n(l(t.startDate.formatted),1)]),_:2},1024),a(f,{name:"endDate"},{default:e(()=>[n(l(t.endDate.formatted),1)]),_:2},1024),a(f,{name:"result"},{default:e(()=>[n(l(t.result),1)]),_:2},1024),a(f,{name:"createdAt"},{default:e(()=>[n(l(t.createdAt.formatted),1)]),_:2},1024),a(f,{name:"action"},{default:e(()=>[a(H,null,{default:e(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:C=>s(v).push({name:"EmployeeQualificationShow",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[n(l(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),s(h)("employee:edit")||o.employee.selfService?(y(),w(E,{key:0},[a(k,{icon:"fas fa-edit",onClick:C=>s(v).push({name:"EmployeeQualificationEdit",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[n(l(u.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-copy",onClick:C=>s(v).push({name:"EmployeeQualificationDuplicate",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[n(l(u.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-trash",onClick:C=>s(g).emit("deleteItem",{uuid:o.employee.uuid,moduleUuid:t.uuid})},{default:e(()=>[n(l(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{Z as default};
