import{u as E,l as A,n as P,r,q as j,o as f,w as e,d as I,b as v,s as l,t as s,e as o,h as W,j as z,y as h,m as q,f as c,a as G,F as J,v as K}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={__name:"Filter",emits:["hide"],setup(T,{emit:y}){const d=E(),C=y,$={batches:[],subjects:[],employees:[],startDate:"",endDate:""},p=A({...$}),u=A({batches:[],subjects:[],employees:[],isLoaded:!(d.query.batches||d.query.subjects||d.query.employees)});return P(async()=>{u.batches=d.query.batches?d.query.batches.split(","):[],u.subjects=d.query.subjects?d.query.subjects.split(","):[],u.employees=d.query.employees?d.query.employees.split(","):[],u.isLoaded=!0}),(b,i)=>{const g=r("BaseSelectSearch"),m=r("DatePicker"),n=r("FilterForm");return f(),j(n,{"init-form":$,form:p,multiple:["batches","subjects","employees"],onHide:i[5]||(i[5]=t=>C("hide"))},{default:e(()=>[I("div",Q,[I("div",X,[u.isLoaded?(f(),j(g,{key:0,multiple:"",name:"batches",label:b.$trans("global.select",{attribute:b.$trans("academic.batch.batch")}),modelValue:p.batches,"onUpdate:modelValue":i[0]||(i[0]=t=>p.batches=t),"value-prop":"uuid","init-search":u.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[l(s(t.value.course.name)+" "+s(t.value.name),1)]),listOption:e(t=>[l(s(t.option.course.nameWithTerm)+" "+s(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),I("div",Y,[u.isLoaded?(f(),j(g,{key:0,multiple:"",name:"subjects",label:b.$trans("global.select",{attribute:b.$trans("academic.subject.subject")}),modelValue:p.subjects,"onUpdate:modelValue":i[1]||(i[1]=t=>p.subjects=t),"value-prop":"uuid","init-search":u.subjects,"search-action":"academic/subject/list"},{selectedOption:e(t=>[l(s(t.value.name)+" ("+s(t.value.code)+") ",1)]),listOption:e(t=>[l(s(t.option.name)+" ("+s(t.option.code)+") ",1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),I("div",Z,[u.isLoaded?(f(),j(g,{key:0,multiple:"",name:"employees",label:b.$trans("global.select",{attribute:b.$trans("employee.employee")}),modelValue:p.employees,"onUpdate:modelValue":i[2]||(i[2]=t=>p.employees=t),"value-prop":"uuid","init-search":u.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[l(s(t.value.name)+" ("+s(t.value.codeNumber)+") ",1)]),listOption:e(t=>[l(s(t.option.name)+" ("+s(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),I("div",x,[o(m,{start:p.startDate,"onUpdate:start":i[3]||(i[3]=t=>p.startDate=t),end:p.endDate,"onUpdate:end":i[4]||(i[4]=t=>p.endDate=t),name:"dateBetween",as:"range",label:b.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},te={name:"AcademicSubjectInchargeList"},se=Object.assign(te,{setup(T){const y=W(),d=z("emitter");let C=["filter"];h("subject-incharge:create")&&C.unshift("create");let $=[];h("subject-incharge:export")&&($=["print","pdf","excel"]),h("subject-incharge:create")&&$.unshift("import");const p="academic/subjectIncharge/",u=q(!1),b=q(!1),i=A({}),g=m=>{Object.assign(i,m)};return(m,n)=>{const t=r("PageHeaderAction"),L=r("PageHeader"),O=r("BaseImport"),S=r("ParentTransition"),w=r("TextMuted"),k=r("DataCell"),D=r("FloatingMenuItem"),H=r("FloatingMenu"),M=r("DataRow"),N=r("BaseButton"),R=r("DataTable"),U=r("ListItem");return f(),j(U,{"init-url":p,onSetItems:g},{header:e(()=>[o(L,{title:m.$trans("academic.subject_incharge.subject_incharge"),navs:[{label:m.$trans("academic.academic"),path:"Academic"},{label:m.$trans("academic.subject.subject"),path:"AcademicSubject"}]},{default:e(()=>[o(t,{url:"academic/subject-incharges/",name:"AcademicSubjectIncharge",title:m.$trans("academic.subject_incharge.subject_incharge"),actions:c(C),"dropdown-actions":c($),onToggleFilter:n[0]||(n[0]=a=>u.value=!u.value),onToggleImport:n[1]||(n[1]=a=>b.value=!b.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:e(()=>[o(S,{appear:"",visibility:b.value},{default:e(()=>[o(O,{path:"academic/subject-incharges/import",onCancelled:n[2]||(n[2]=a=>b.value=!1),onHide:n[3]||(n[3]=a=>b.value=!1),onCompleted:n[4]||(n[4]=a=>c(d).emit("listItems"))})]),_:1},8,["visibility"])]),filter:e(()=>[o(S,{appear:"",visibility:u.value},{default:e(()=>[o(ee,{onRefresh:n[5]||(n[5]=a=>c(d).emit("listItems")),onHide:n[6]||(n[6]=a=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(S,{appear:"",visibility:!0},{default:e(()=>[o(R,{header:i.headers,meta:i.meta,module:"academic.subject_incharge",onRefresh:n[8]||(n[8]=a=>c(d).emit("listItems"))},{actionButton:e(()=>[c(h)("subject-incharge:create")?(f(),j(N,{key:0,onClick:n[7]||(n[7]=a=>c(y).push({name:"AcademicSubjectInchargeCreate"}))},{default:e(()=>[l(s(m.$trans("global.add",{attribute:m.$trans("academic.subject_incharge.subject_incharge")})),1)]),_:1})):v("",!0)]),default:e(()=>[(f(!0),G(J,null,K(i.data,a=>(f(),j(M,{key:a.uuid,onDoubleClick:_=>c(y).push({name:"AcademicSubjectInchargeShow",params:{uuid:a.uuid}})},{default:e(()=>[o(k,{name:"subject"},{default:e(()=>[l(s(a.subject.name)+" ",1),o(w,{block:""},{default:e(()=>[l(s(a.subject.code),1)]),_:2},1024)]),_:2},1024),o(k,{name:"batch"},{default:e(()=>{var _,B,F;return[l(s(((B=(_=a==null?void 0:a.batch)==null?void 0:_.course)==null?void 0:B.name)||"-")+" ",1),(F=a==null?void 0:a.batch)!=null&&F.name?(f(),j(w,{key:0,block:""},{default:e(()=>{var V;return[l(s((V=a==null?void 0:a.batch)==null?void 0:V.name),1)]}),_:2},1024)):v("",!0)]}),_:2},1024),o(k,{name:"employee"},{default:e(()=>[l(s(a.employee.name)+" ",1),o(w,{block:""},{default:e(()=>[l(s(a.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),o(k,{name:"period"},{default:e(()=>[l(s(a.period),1)]),_:2},1024),o(k,{name:"createdAt"},{default:e(()=>[l(s(a.createdAt.formatted),1)]),_:2},1024),o(k,{name:"action"},{default:e(()=>[o(H,null,{default:e(()=>[o(D,{icon:"fas fa-arrow-circle-right",onClick:_=>c(y).push({name:"AcademicSubjectInchargeShow",params:{uuid:a.uuid}})},{default:e(()=>[l(s(m.$trans("general.show")),1)]),_:2},1032,["onClick"]),c(h)("subject-incharge:edit")?(f(),j(D,{key:0,icon:"fas fa-edit",onClick:_=>c(y).push({name:"AcademicSubjectInchargeEdit",params:{uuid:a.uuid}})},{default:e(()=>[l(s(m.$trans("general.edit")),1)]),_:2},1032,["onClick"])):v("",!0),c(h)("subject-incharge:create")?(f(),j(D,{key:1,icon:"fas fa-copy",onClick:_=>c(y).push({name:"AcademicSubjectInchargeDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[l(s(m.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):v("",!0),c(h)("subject-incharge:delete")?(f(),j(D,{key:2,icon:"fas fa-trash",onClick:_=>c(d).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[l(s(m.$trans("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{se as default};
