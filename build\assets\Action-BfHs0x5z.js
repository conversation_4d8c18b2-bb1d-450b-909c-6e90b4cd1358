import{u as F,H as O,l as g,r as p,q as V,o as f,w as c,d as l,b as N,e as t,f as r,s as v,t as L,I as S,J as $,a as q,F as H}from"./app-BAwPsakn.js";const I={class:"grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-2"},M={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},_={class:"grid grid-cols-3 gap-6"},h={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},ne={name:"FinanceLedgerForm"},ae=Object.assign(ne,{setup(A){const m=F(),i={name:"",alias:"",code:"",type:"",openingBalance:"",description:"",contactNumber:"",email:"",codePrefix:"",codeDigit:"",codeSuffix:"",address:{addressLine1:"",addressLine2:"",city:"",state:"",zipcode:"",country:""},account:{name:"",number:"",bankName:"",branchName:"",branchAddress:"",branchCode:""}},y="finance/ledger/",o=O(y),b=g({ledgerTypes:[]}),u=g({selectedLedgerType:{}}),a=g({...i}),U=g({type:"",isLoaded:!m.params.uuid}),T=s=>{Object.assign(b,s),Object.assign(a,$(i))},k=s=>{var e,d;Object.assign(i,{...s,openingBalance:s.openingBalance.value,type:((e=s.type)==null?void 0:e.uuid)||""}),Object.assign(a,$(i)),U.type=((d=s.type)==null?void 0:d.uuid)||"",u.selectedLedgerType=s.type,U.isLoaded=!0},C=s=>{a.type=s.uuid||""};return(s,e)=>{const d=p("BaseInput"),P=p("BaseSelect"),j=p("BaseTextarea"),B=p("BaseFieldset"),z=p("AddressInput"),E=p("FormAction");return f(),V(E,{"pre-requisites":!0,onSetPreRequisites:T,"init-url":y,"init-form":i,form:a,"set-form":k,redirect:"FinanceLedger"},{default:c(()=>[l("div",I,[l("div",R,[t(d,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=n=>a.name=n),name:"name",label:s.$trans("finance.ledger.props.name"),error:r(o).name,"onUpdate:error":e[1]||(e[1]=n=>r(o).name=n),autofocus:""},null,8,["modelValue","label","error"])]),l("div",D,[t(d,{type:"text",modelValue:a.alias,"onUpdate:modelValue":e[2]||(e[2]=n=>a.alias=n),name:"alias",label:s.$trans("finance.ledger.props.alias"),error:r(o).alias,"onUpdate:error":e[3]||(e[3]=n=>r(o).alias=n)},null,8,["modelValue","label","error"])]),l("div",w,[t(d,{type:"text",modelValue:a.code,"onUpdate:modelValue":e[4]||(e[4]=n=>a.code=n),name:"code",label:s.$trans("finance.ledger.props.code"),error:r(o).code,"onUpdate:error":e[5]||(e[5]=n=>r(o).code=n)},null,8,["modelValue","label","error"])]),l("div",J,[t(P,{name:"type",label:s.$trans("global.select",{attribute:s.$trans("finance.ledger_type.ledger_type")}),options:b.ledgerTypes,"object-prop":!0,modelValue:u.selectedLedgerType,"onUpdate:modelValue":e[6]||(e[6]=n=>u.selectedLedgerType=n),error:r(o).type,"onUpdate:error":e[7]||(e[7]=n=>r(o).type=n),onChange:C,"label-prop":"name","value-prop":"uuid"},null,8,["label","options","modelValue","error"])]),l("div",G,[t(d,{modelValue:a.openingBalance,"onUpdate:modelValue":e[8]||(e[8]=n=>a.openingBalance=n),name:"openingBalance",label:s.$trans("finance.ledger.props.opening_balance"),error:r(o).openingBalance,"onUpdate:error":e[9]||(e[9]=n=>r(o).openingBalance=n),currency:""},null,8,["modelValue","label","error"])]),l("div",K,[t(j,{modelValue:a.description,"onUpdate:modelValue":e[10]||(e[10]=n=>a.description=n),name:"description",label:s.$trans("finance.ledger.props.description"),error:r(o).description,"onUpdate:error":e[11]||(e[11]=n=>r(o).description=n)},null,8,["modelValue","label","error"])])]),u.selectedLedgerType.hasAccount?(f(),V(B,{key:0,class:"mt-4"},{legend:c(()=>[v(L(s.$trans("finance.account.info")),1)]),default:c(()=>[l("div",M,[l("div",Q,[t(d,{type:"text",modelValue:a.account.name,"onUpdate:modelValue":e[12]||(e[12]=n=>a.account.name=n),name:"accountName",label:s.$trans("finance.account.props.name"),error:r(o).accountName,"onUpdate:error":e[13]||(e[13]=n=>r(o).accountName=n)},null,8,["modelValue","label","error"])]),l("div",W,[t(d,{type:"text",modelValue:a.account.number,"onUpdate:modelValue":e[14]||(e[14]=n=>a.account.number=n),name:"accountNumber",label:s.$trans("finance.account.props.number"),error:r(o).accountNumber,"onUpdate:error":e[15]||(e[15]=n=>r(o).accountNumber=n)},null,8,["modelValue","label","error"])]),l("div",X,[t(d,{type:"text",modelValue:a.account.bankName,"onUpdate:modelValue":e[16]||(e[16]=n=>a.account.bankName=n),name:"accountBankName",label:s.$trans("finance.account.props.bank_name"),error:r(o).accountBankName,"onUpdate:error":e[17]||(e[17]=n=>r(o).accountBankName=n)},null,8,["modelValue","label","error"])]),l("div",Y,[t(d,{type:"text",modelValue:a.account.branchName,"onUpdate:modelValue":e[18]||(e[18]=n=>a.account.branchName=n),name:"accountBranchName",label:s.$trans("finance.account.props.branch_name"),error:r(o).accountBranchName,"onUpdate:error":e[19]||(e[19]=n=>r(o).accountBranchName=n)},null,8,["modelValue","label","error"])]),l("div",Z,[t(d,{type:"text",modelValue:a.account.branchAddress,"onUpdate:modelValue":e[20]||(e[20]=n=>a.account.branchAddress=n),name:"accountBranchAddress",label:s.$trans("finance.account.props.branch_address"),error:r(o).accountBranchAddress,"onUpdate:error":e[21]||(e[21]=n=>r(o).accountBranchAddress=n)},null,8,["modelValue","label","error"])]),l("div",x,[t(d,{type:"text",modelValue:a.account.branchCode,"onUpdate:modelValue":e[22]||(e[22]=n=>a.account.branchCode=n),name:"accountBranchCode",label:s.$trans("finance.account.props.branch_code"),error:r(o).accountBranchCode,"onUpdate:error":e[23]||(e[23]=n=>r(o).accountBranchCode=n)},null,8,["modelValue","label","error"])])])]),_:1})):N("",!0),u.selectedLedgerType.hasContact?(f(),V(B,{key:1,class:"mt-4"},{legend:c(()=>[v(L(s.$trans("contact.info")),1)]),default:c(()=>[l("div",_,[l("div",h,[t(d,{type:"text",modelValue:a.contactNumber,"onUpdate:modelValue":e[24]||(e[24]=n=>a.contactNumber=n),name:"contactNumber",label:s.$trans("finance.ledger.props.contact_number"),error:r(o).contactNumber,"onUpdate:error":e[25]||(e[25]=n=>r(o).contactNumber=n)},null,8,["modelValue","label","error"])]),l("div",ee,[t(d,{type:"text",modelValue:a.email,"onUpdate:modelValue":e[26]||(e[26]=n=>a.email=n),name:"email",label:s.$trans("finance.ledger.props.email"),error:r(o).email,"onUpdate:error":e[27]||(e[27]=n=>r(o).email=n)},null,8,["modelValue","label","error"])]),e[35]||(e[35]=l("div",{class:"col-span-3 sm:col-span-1"},null,-1)),t(z,{addressLine1:a.address.addressLine1,"onUpdate:addressLine1":e[28]||(e[28]=n=>a.address.addressLine1=n),addressLine2:a.address.addressLine2,"onUpdate:addressLine2":e[29]||(e[29]=n=>a.address.addressLine2=n),city:a.address.city,"onUpdate:city":e[30]||(e[30]=n=>a.address.city=n),state:a.address.state,"onUpdate:state":e[31]||(e[31]=n=>a.address.state=n),zipcode:a.address.zipcode,"onUpdate:zipcode":e[32]||(e[32]=n=>a.address.zipcode=n),country:a.address.country,"onUpdate:country":e[33]||(e[33]=n=>a.address.country=n),formErrors:r(o),"onUpdate:formErrors":e[34]||(e[34]=n=>S(o)?o.value=n:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1})):N("",!0)]),_:1},8,["form"])}}}),oe={name:"FinanceLedgerAction"},se=Object.assign(oe,{setup(A){const m=F();return(i,y)=>{const o=p("PageHeaderAction"),b=p("PageHeader"),u=p("ParentTransition");return f(),q(H,null,[t(b,{title:i.$trans(r(m).meta.trans,{attribute:i.$trans(r(m).meta.label)}),navs:[{label:i.$trans("finance.finance"),path:"Finance"},{label:i.$trans("finance.ledger.ledger"),path:"FinanceLedgerList"}]},{default:c(()=>[t(o,{name:"FinanceLedger",title:i.$trans("finance.ledger.ledger"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(u,{appear:"",visibility:!0},{default:c(()=>[t(ae)]),_:1})],64)}}});export{se as default};
