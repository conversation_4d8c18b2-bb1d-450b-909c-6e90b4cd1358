import{u as y,G as f,H as q,l as U,r as i,q as T,o as _,w as c,d as l,e as r,f as n,s as k,t as v,b as $,a as j,J as Q,F as h}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-4 gap-6"},R={class:"col-span-4 sm:col-span-3"},w={class:"col-span-4 sm:col-span-1"},L={class:"col-span-4 sm:col-span-1"},C={class:"col-span-4 sm:col-span-1"},I={class:"col-span-4 sm:col-span-1"},G={class:"col-span-4 sm:col-span-1"},J={class:"mt-4 grid grid-cols-4 gap-6"},W={class:"col-span-4 sm:col-span-1"},z={class:"col-span-4 sm:col-span-1"},K={class:"mt-4 grid grid-cols-4 gap-6"},X={class:"col-span-4 sm:col-span-1"},Y={class:"col-span-4 sm:col-span-1"},Z={key:0,class:"col-span-4 sm:col-span-1"},ee={class:"col-span-4"},ae={class:"col-span-4"},te={class:"mt-4 grid grid-cols-1"},ne={class:"col"},se={name:"ExamOnlineExamForm"},oe=Object.assign(se,{setup(E){const m=y(),d={title:"",type:"",date:"",startTime:"",endDate:"",endTime:"",batches:[],subject:"",passPercentage:0,hasNegativeMarking:!1,negativeMarkPercentPerQuestion:0,instructions:"",description:"",media:[],mediaUpdated:!1,mediaToken:f(),mediaHash:[]},x="exam/onlineExam/",s=q(x),b=U({types:[],subjects:[]}),t=U({...d}),V=U({batches:[],subject:"",isLoaded:!m.params.uuid}),M=o=>{Object.assign(b,o)},B=()=>{t.mediaToken=f(),t.mediaHash=[]},D=o=>{var p,g;let a=o.records.map(u=>u.batch.uuid)||[];Object.assign(d,{...o,type:o.type.value,date:o.date.value,startTime:o.startTime.at,endDate:o.endDate.value,endTime:o.endTime.at,passPercentage:o.passPercentage.value,batches:a,subject:((g=(p=o.records[0])==null?void 0:p.subject)==null?void 0:g.uuid)||""}),Object.assign(t,Q(d)),V.batches=a,V.isLoaded=!0};return(o,a)=>{const p=i("BaseInput"),g=i("BaseSelect"),u=i("DatePicker"),O=i("HelperText"),H=i("BaseSelectSearch"),N=i("BaseSwitch"),P=i("BaseEditor"),S=i("MediaUpload"),F=i("FormAction");return _(),T(F,{"pre-requisites":!0,onSetPreRequisites:M,"init-url":x,"init-form":d,form:t,"set-form":D,redirect:"ExamOnlineExam",onResetMediaFiles:B},{default:c(()=>[l("div",A,[l("div",R,[r(p,{type:"text",modelValue:t.title,"onUpdate:modelValue":a[0]||(a[0]=e=>t.title=e),name:"title",label:o.$trans("exam.online_exam.props.title"),error:n(s).title,"onUpdate:error":a[1]||(a[1]=e=>n(s).title=e)},null,8,["modelValue","label","error"])]),l("div",w,[r(g,{modelValue:t.type,"onUpdate:modelValue":a[2]||(a[2]=e=>t.type=e),name:"type",label:o.$trans("exam.online_exam.props.type"),options:b.types,error:n(s).type,"onUpdate:error":a[3]||(a[3]=e=>n(s).type=e)},null,8,["modelValue","label","options","error"])]),l("div",L,[r(u,{modelValue:t.date,"onUpdate:modelValue":a[4]||(a[4]=e=>t.date=e),name:"date",label:o.$trans("exam.online_exam.props.date"),"no-clear":"",error:n(s).date,"onUpdate:error":a[5]||(a[5]=e=>n(s).date=e)},null,8,["modelValue","label","error"])]),l("div",C,[r(u,{modelValue:t.startTime,"onUpdate:modelValue":a[6]||(a[6]=e=>t.startTime=e),name:"startTime",label:o.$trans("exam.online_exam.props.start_time"),as:"time",error:n(s).startTime,"onUpdate:error":a[7]||(a[7]=e=>n(s).startTime=e)},null,8,["modelValue","label","error"])]),l("div",I,[r(u,{modelValue:t.endDate,"onUpdate:modelValue":a[8]||(a[8]=e=>t.endDate=e),name:"endDate",label:o.$trans("exam.online_exam.props.end_date"),"no-clear":"",error:n(s).endDate,"onUpdate:error":a[9]||(a[9]=e=>n(s).endDate=e)},null,8,["modelValue","label","error"]),r(O,null,{default:c(()=>[k(v(o.$trans("exam.online_exam.end_date_info")),1)]),_:1})]),l("div",G,[r(u,{modelValue:t.endTime,"onUpdate:modelValue":a[10]||(a[10]=e=>t.endTime=e),name:"endTime",label:o.$trans("exam.online_exam.props.end_time"),as:"time",error:n(s).endTime,"onUpdate:error":a[11]||(a[11]=e=>n(s).endTime=e)},null,8,["modelValue","label","error"])])]),l("div",J,[l("div",W,[V.isLoaded?(_(),T(H,{key:0,multiple:"",name:"batches",label:o.$trans("academic.batch.batch"),modelValue:t.batches,"onUpdate:modelValue":a[12]||(a[12]=e=>t.batches=e),error:n(s).batches,"onUpdate:error":a[13]||(a[13]=e=>n(s).batches=e),"value-prop":"uuid","init-search":V.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:c(e=>[k(v(e.value.course.name)+" - "+v(e.value.name),1)]),listOption:c(e=>[k(v(e.option.course.nameWithTerm)+" - "+v(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)]),l("div",z,[r(g,{modelValue:t.subject,"onUpdate:modelValue":a[14]||(a[14]=e=>t.subject=e),name:"subject",label:o.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:b.subjects,error:n(s).subject,"onUpdate:error":a[15]||(a[15]=e=>n(s).subject=e)},null,8,["modelValue","label","options","error"])])]),l("div",K,[l("div",X,[r(p,{type:"number",modelValue:t.passPercentage,"onUpdate:modelValue":a[16]||(a[16]=e=>t.passPercentage=e),name:"passPercentage",label:o.$trans("exam.online_exam.props.pass_percentage"),error:n(s).passPercentage,"onUpdate:error":a[17]||(a[17]=e=>n(s).passPercentage=e)},null,8,["modelValue","label","error"])]),l("div",Y,[r(N,{vertical:"",modelValue:t.hasNegativeMarking,"onUpdate:modelValue":a[18]||(a[18]=e=>t.hasNegativeMarking=e),name:"hasNegativeMarking",label:o.$trans("exam.online_exam.props.has_negative_marking"),error:n(s).hasNegativeMarking,"onUpdate:error":a[19]||(a[19]=e=>n(s).hasNegativeMarking=e)},null,8,["modelValue","label","error"])]),t.hasNegativeMarking?(_(),j("div",Z,[r(p,{type:"number",modelValue:t.negativeMarkPercentPerQuestion,"onUpdate:modelValue":a[20]||(a[20]=e=>t.negativeMarkPercentPerQuestion=e),name:"negativeMarkPercentPerQuestion",label:o.$trans("exam.online_exam.props.negative_mark_percent_per_question"),error:n(s).negativeMarkPercentPerQuestion,"onUpdate:error":a[21]||(a[21]=e=>n(s).negativeMarkPercentPerQuestion=e)},null,8,["modelValue","label","error"])])):$("",!0),l("div",ee,[r(P,{modelValue:t.instructions,"onUpdate:modelValue":a[22]||(a[22]=e=>t.instructions=e),name:"instructions",edit:!!n(m).params.uuid,label:o.$trans("exam.online_exam.props.instructions"),error:n(s).instructions,"onUpdate:error":a[23]||(a[23]=e=>n(s).instructions=e)},null,8,["modelValue","edit","label","error"])]),l("div",ae,[r(P,{modelValue:t.description,"onUpdate:modelValue":a[24]||(a[24]=e=>t.description=e),name:"description",edit:!!n(m).params.uuid,label:o.$trans("exam.online_exam.props.description"),error:n(s).description,"onUpdate:error":a[25]||(a[25]=e=>n(s).description=e)},null,8,["modelValue","edit","label","error"])])]),l("div",te,[l("div",ne,[r(S,{multiple:"",label:o.$trans("general.file"),module:"online_exam",media:t.media,"media-token":t.mediaToken,onIsUpdated:a[26]||(a[26]=e=>t.mediaUpdated=!0),onSetHash:a[27]||(a[27]=e=>t.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),re={name:"ExamOnlineExamAction"},ie=Object.assign(re,{setup(E){const m=y();return(d,x)=>{const s=i("PageHeaderAction"),b=i("PageHeader"),t=i("ParentTransition");return _(),j(h,null,[r(b,{title:d.$trans(n(m).meta.trans,{attribute:d.$trans(n(m).meta.label)}),navs:[{label:d.$trans("exam.exam"),path:"Exam"},{label:d.$trans("exam.online_exam.online_exam"),path:"ExamOnlineExamList"}]},{default:c(()=>[r(s,{name:"ExamOnlineExam",title:d.$trans("exam.online_exam.online_exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(t,{appear:"",visibility:!0},{default:c(()=>[r(oe)]),_:1})],64)}}});export{ie as default};
