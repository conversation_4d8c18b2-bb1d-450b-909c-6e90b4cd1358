import{l as I,r as s,q as g,o as c,w as e,d as D,e as t,u as L,h as M,j as N,y as S,m as O,f as u,a as w,F as B,v as U,s as d,t as l,b as F}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(o,{emit:k}){const m=k,p={name:""},_=I({...p});return(b,r)=>{const v=s("BaseInput"),h=s("FilterForm");return c(),g(h,{"init-form":p,form:_,onHide:r[1]||(r[1]=a=>m("hide"))},{default:e(()=>[D("div",E,[D("div",q,[t(v,{type:"text",modelValue:_.name,"onUpdate:modelValue":r[0]||(r[0]=a=>_.name=a),name:"name",label:b.$trans("finance.fee_head.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={name:"StudentCustomFeeList"},K=Object.assign(G,{props:{student:{type:Object,default(){return{}}}},setup(o){const k=L(),m=M(),p=N("emitter");let _=["filter"];S("fee:edit")&&_.unshift("create");const b="student/customFee/",r=O(!1),v=I({}),h=a=>{Object.assign(v,a)};return(a,i)=>{const H=s("PageHeaderAction"),V=s("PageHeader"),y=s("ParentTransition"),C=s("DataCell"),$=s("FloatingMenuItem"),P=s("FloatingMenu"),R=s("DataRow"),T=s("BaseButton"),j=s("DataTable"),A=s("ListItem");return c(),g(A,{"init-url":b,uuid:u(k).params.uuid,onSetItems:h},{header:e(()=>[o.student.uuid?(c(),g(V,{key:0,title:a.$trans("student.fee.custom_fee"),navs:[{label:a.$trans("student.student"),path:"Student"},{label:o.student.contact.name,path:{name:"StudentShow",params:{uuid:o.student.uuid}}}]},{default:e(()=>[t(H,{url:`students/${o.student.uuid}/custom-fees/`,name:"StudentCustomFee",title:a.$trans("student.fee.custom_fee"),actions:u(_),"dropdown-actions":["print","pdf","excel"],onToggleFilter:i[0]||(i[0]=n=>r.value=!r.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):F("",!0)]),filter:e(()=>[t(y,{appear:"",visibility:r.value},{default:e(()=>[t(z,{onRefresh:i[1]||(i[1]=n=>u(p).emit("listItems")),onHide:i[2]||(i[2]=n=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(y,{appear:"",visibility:!0},{default:e(()=>[t(j,{header:v.headers,meta:v.meta,module:"student.custom_fee",onRefresh:i[4]||(i[4]=n=>u(p).emit("listItems"))},{actionButton:e(()=>[u(S)("fee:edit")?(c(),g(T,{key:0,onClick:i[3]||(i[3]=n=>u(m).push({name:"StudentCustomFeeCreate"}))},{default:e(()=>[d(l(a.$trans("global.add",{attribute:a.$trans("student.fee.custom_fee")})),1)]),_:1})):F("",!0)]),default:e(()=>[(c(!0),w(B,null,U(v.data,n=>(c(),g(R,{key:n.uuid,onDoubleClick:f=>u(m).push({name:"StudentCustomFeeShow",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:e(()=>[t(C,{name:"feeHead"},{default:e(()=>{var f;return[d(l(((f=n.head)==null?void 0:f.name)||"-"),1)]}),_:2},1024),t(C,{name:"amount"},{default:e(()=>[d(l(n.amount.formatted),1)]),_:2},1024),t(C,{name:"paid"},{default:e(()=>[d(l(n.paid.formatted),1)]),_:2},1024),t(C,{name:"dueDate"},{default:e(()=>[d(l(n.dueDate.formatted),1)]),_:2},1024),t(C,{name:"createdAt"},{default:e(()=>[d(l(n.createdAt.formatted),1)]),_:2},1024),t(C,{name:"action"},{default:e(()=>[t(P,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:f=>u(m).push({name:"StudentCustomFeeShow",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:e(()=>[d(l(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),u(S)("fee:edit")?(c(),w(B,{key:0},[t($,{icon:"fas fa-edit",onClick:f=>u(m).push({name:"StudentCustomFeeEdit",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:e(()=>[d(l(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-copy",onClick:f=>u(m).push({name:"StudentCustomFeeDuplicate",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:e(()=>[d(l(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-trash",onClick:f=>u(p).emit("deleteItem",{uuid:o.student.uuid,moduleUuid:n.uuid})},{default:e(()=>[d(l(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):F("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{K as default};
