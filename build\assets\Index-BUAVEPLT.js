import{u as S,l as k,n as N,r as s,q as F,o as c,w as e,d as D,e as t,h as E,j as O,m as U,f as l,a as C,F as h,v as T,s as r,t as u}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(M,{emit:m}){S();const f=m,v={name:""},_=k({...v}),I=k({isLoaded:!0});return N(async()=>{I.isLoaded=!0}),(p,d)=>{const y=s("BaseInput"),o=s("FilterForm");return c(),F(o,{"init-form":v,form:_,multiple:[],onHide:d[1]||(d[1]=n=>f("hide"))},{default:e(()=>[D("div",q,[D("div",z,[t(y,{type:"text",modelValue:_.name,"onUpdate:modelValue":d[0]||(d[0]=n=>_.name=n),name:"name",label:p.$trans("inventory.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},J={name:"InventoryList"},Q=Object.assign(J,{setup(M){const m=E(),f=O("emitter");let v=["create","filter"],_=["print","pdf","excel"];const I="inventory/",p=U(!1),d=k({}),y=o=>{Object.assign(d,o)};return(o,n)=>{const b=s("BaseButton"),A=s("PageHeaderAction"),H=s("PageHeader"),w=s("ParentTransition"),$=s("DataCell"),L=s("TextMuted"),g=s("FloatingMenuItem"),P=s("FloatingMenu"),R=s("DataRow"),V=s("DataTable"),j=s("ListItem");return c(),F(j,{"init-url":I,onSetItems:y},{header:e(()=>[t(H,{title:o.$trans("inventory.inventory"),navs:[]},{default:e(()=>[t(A,{url:"inventories/",name:"Inventory",title:o.$trans("inventory.inventory"),actions:l(v),"dropdown-actions":l(_),onToggleFilter:n[1]||(n[1]=a=>p.value=!p.value)},{default:e(()=>[t(b,{design:"white",onClick:n[0]||(n[0]=a=>l(m).push({name:"InventoryIncharge"}))},{default:e(()=>[r(u(o.$trans("inventory.incharge.incharge")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title"])]),filter:e(()=>[t(w,{appear:"",visibility:p.value},{default:e(()=>[t(G,{onRefresh:n[2]||(n[2]=a=>l(f).emit("listItems")),onHide:n[3]||(n[3]=a=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(w,{appear:"",visibility:!0},{default:e(()=>[t(V,{header:d.headers,meta:d.meta,module:"inventory",onRefresh:n[5]||(n[5]=a=>l(f).emit("listItems"))},{actionButton:e(()=>[t(b,{onClick:n[4]||(n[4]=a=>l(m).push({name:"InventoryCreate"}))},{default:e(()=>[r(u(o.$trans("global.add",{attribute:o.$trans("inventory.inventory")})),1)]),_:1})]),default:e(()=>[(c(!0),C(h,null,T(d.data,a=>(c(),F(R,{key:a.uuid,onDoubleClick:i=>l(m).push({name:"InventoryShow",params:{uuid:a.uuid}})},{default:e(()=>[t($,{name:"name"},{default:e(()=>[r(u(a.name),1)]),_:2},1024),t($,{name:"incharge"},{default:e(()=>[(c(!0),C(h,null,T(a.incharges,i=>{var B;return c(),C("div",null,[r(u(((B=i==null?void 0:i.employee)==null?void 0:B.name)||"-")+" ",1),t(L,null,{default:e(()=>[r(u(i==null?void 0:i.period),1)]),_:2},1024)])}),256))]),_:2},1024),t($,{name:"createdAt"},{default:e(()=>[r(u(a.createdAt.formatted),1)]),_:2},1024),t($,{name:"action"},{default:e(()=>[t(P,null,{default:e(()=>[t(g,{icon:"fas fa-arrow-circle-right",onClick:i=>l(m).push({name:"InventoryShow",params:{uuid:a.uuid}})},{default:e(()=>[r(u(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-edit",onClick:i=>l(m).push({name:"InventoryEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(u(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-copy",onClick:i=>l(m).push({name:"InventoryDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(u(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-trash",onClick:i=>l(f).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(u(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
