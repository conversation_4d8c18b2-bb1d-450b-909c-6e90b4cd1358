import{h as u,g as i,r as d,a as c,b as l,f as e,B as m,o as p,e as f,w as _,s as g,t as C}from"./app-BAwPsakn.js";const B={__name:"EditRequestInfo",props:{student:{type:Object,default(){return{}}}},setup(s){const n=u(),a=i("student.allowStudentToSubmitContactEditRequest");return(o,t)=>{const r=d("BaseAlert");return e(m)(["student","guardian"],"any")&&e(a)?(p(),c("div",{key:0,class:"mb-4 cursor-pointer",onClick:t[0]||(t[0]=q=>e(n).push({name:"StudentProfileEditRequestNew",params:{uuid:s.student.uuid}}))},[f(r,{design:"info",size:"xs"},{default:_(()=>[g(C(o.$trans("student.edit_request.edit_info")),1)]),_:1})])):l("",!0)}}};export{B as _};
