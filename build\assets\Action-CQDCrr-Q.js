import{u as V,l as w,H as A,r as m,a as c,o as f,d as i,e as t,w as g,s as S,t as j,b,f as a,F as U,J as H}from"./app-BAwPsakn.js";const O={class:"my-4"},k={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},T={key:0,class:"mt-6 grid grid-cols-3 gap-6"},D={class:"col-span-3 sm:col-span-1"},I={key:1,class:"mt-6 grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},L={name:"UserForm"},G=Object.assign(L,{setup(v){const p=V(),l={name:"",roles:[],email:"",username:"",password:"",passwordConfirmation:"",forceChangePassword:!0},_="user/",u=w({}),n=A(_),o=w({...l}),C=r=>{Object.assign(l,{name:r.profile.name,roles:r.roles.map(e=>e.uuid),email:r.email,username:r.username,forceChangePassword:!1}),Object.assign(o,H(l))},P=r=>{Object.assign(u,r)};return(r,e)=>{const $=m("BaseAlert"),d=m("BaseInput"),y=m("BaseSelect"),B=m("BaseSwitch"),F=m("FormAction");return f(),c(U,null,[i("div",O,[t($,{size:"xs",design:"info"},{default:g(()=>[S(j(r.$trans("user.create_tip")),1)]),_:1})]),t(F,{"pre-requisites":!0,onSetPreRequisites:P,"init-url":_,"init-form":l,form:o,"set-form":C,redirect:"User"},{default:g(()=>[i("div",k,[i("div",q,[t(d,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=s=>o.name=s),name:"name",label:r.$trans("user.props.name"),error:a(n).name,"onUpdate:error":e[1]||(e[1]=s=>a(n).name=s),autofocus:""},null,8,["modelValue","label","error"])]),i("div",N,[t(d,{type:"text",modelValue:o.email,"onUpdate:modelValue":e[2]||(e[2]=s=>o.email=s),name:"email",label:r.$trans("user.props.email"),error:a(n).email,"onUpdate:error":e[3]||(e[3]=s=>a(n).email=s)},null,8,["modelValue","label","error"])]),i("div",R,[t(d,{type:"text",modelValue:o.username,"onUpdate:modelValue":e[4]||(e[4]=s=>o.username=s),name:"username",label:r.$trans("user.props.username"),error:a(n).username,"onUpdate:error":e[5]||(e[5]=s=>a(n).username=s)},null,8,["modelValue","label","error"])]),i("div",E,[t(y,{modelValue:o.roles,"onUpdate:modelValue":e[6]||(e[6]=s=>o.roles=s),name:"roles",label:r.$trans("team.config.role.role"),options:u.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:a(n).roles,"onUpdate:error":e[7]||(e[7]=s=>a(n).roles=s)},null,8,["modelValue","label","options","error"])])]),a(p).meta.type==="edit"?(f(),c("div",T,[i("div",D,[t(B,{modelValue:o.forceChangePassword,"onUpdate:modelValue":e[8]||(e[8]=s=>o.forceChangePassword=s),name:"forceChangePassword",label:r.$trans("user.props.force_change_password")},null,8,["modelValue","label"])])])):b("",!0),o.forceChangePassword?(f(),c("div",I,[i("div",z,[t(d,{type:"password",modelValue:o.password,"onUpdate:modelValue":e[9]||(e[9]=s=>o.password=s),name:"password",label:r.$trans("user.props.password"),error:a(n).password,"onUpdate:error":e[10]||(e[10]=s=>a(n).password=s)},null,8,["modelValue","label","error"])]),i("div",J,[t(d,{type:"password",modelValue:o.passwordConfirmation,"onUpdate:modelValue":e[11]||(e[11]=s=>o.passwordConfirmation=s),name:"passwordConfirmation",label:r.$trans("user.props.password_confirmation"),error:a(n).passwordConfirmation,"onUpdate:error":e[12]||(e[12]=s=>a(n).passwordConfirmation=s)},null,8,["modelValue","label","error"])])])):b("",!0)]),_:1},8,["form"])],64)}}}),K={name:"UserAction"},Q=Object.assign(K,{setup(v){const p=V();return(l,_)=>{const u=m("PageHeaderAction"),n=m("PageHeader"),o=m("ParentTransition");return f(),c(U,null,[t(n,{title:l.$trans(a(p).meta.trans,{attribute:l.$trans(a(p).meta.label)}),navs:[{label:l.$trans("user.user"),path:"UserList"}]},{default:g(()=>[t(u,{name:"User",title:l.$trans("user.user"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(o,{appear:"",visibility:!0},{default:g(()=>[t(G)]),_:1})],64)}}});export{Q as default};
