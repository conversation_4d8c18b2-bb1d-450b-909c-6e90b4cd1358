import{u as b,h as B,H as C,l as u,r as m,q as N,o as g,w as _,d as i,e as l,f as r,J as P,a as I,F as U}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"mt-4 grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},k={name:"AcademicIdCardTemplateForm"},y=Object.assign(k,{setup(F){const c=b();B();const t={name:"",for:{},customTemplateFileName:""},p="academic/idCardTemplate/",s=C(p),d=u({for:[]}),o=u({...t}),T=n=>{Object.assign(d,n)},A=u({isLoaded:!c.params.uuid}),V=n=>{Object.assign(t,{...n}),Object.assign(o,P(t)),A.isLoaded=!0};return(n,e)=>{const f=m("BaseInput"),v=m("BaseSelect"),$=m("FormAction");return g(),N($,{"pre-requisites":!0,onSetPreRequisites:T,"init-url":p,"init-form":t,form:o,setForm:V,redirect:"AcademicIdCardTemplate"},{default:_(()=>[i("div",j,[i("div",q,[l(f,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=a=>o.name=a),name:"name",label:n.$trans("academic.id_card.template.props.name"),error:r(s).name,"onUpdate:error":e[1]||(e[1]=a=>r(s).name=a),autofocus:""},null,8,["modelValue","label","error"])]),i("div",H,[l(v,{modelValue:o.for,"onUpdate:modelValue":e[2]||(e[2]=a=>o.for=a),name:"for",label:n.$trans("academic.id_card.template.props.for"),options:d.for,"object-prop":!0,error:r(s).for,"onUpdate:error":e[3]||(e[3]=a=>r(s).for=a)},null,8,["modelValue","label","options","error"])])]),i("div",O,[i("div",R,[l(f,{type:"text",modelValue:o.customTemplateFileName,"onUpdate:modelValue":e[4]||(e[4]=a=>o.customTemplateFileName=a),name:"customTemplateFileName",label:n.$trans("academic.id_card.template.props.custom_template_file_name"),error:r(s).customTemplateFileName,"onUpdate:error":e[5]||(e[5]=a=>r(s).customTemplateFileName=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),E={name:"AcademicIdCardTemplateAction"},S=Object.assign(E,{setup(F){const c=b();return(t,p)=>{const s=m("PageHeaderAction"),d=m("PageHeader"),o=m("ParentTransition");return g(),I(U,null,[l(d,{title:t.$trans(r(c).meta.trans,{attribute:t.$trans(r(c).meta.label)}),navs:[{label:t.$trans("academic.academic"),path:"Academic"},{label:t.$trans("academic.id_card.template.template"),path:"AcademicIdCardTemplateList"}]},{default:_(()=>[l(s,{name:"AcademicIdCardTemplate",title:t.$trans("academic.id_card.template.template"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(o,{appear:"",visibility:!0},{default:_(()=>[l(y)]),_:1})],64)}}});export{S as default};
