import{u as O,l as I,n as z,r as o,q as v,o as y,w as e,d as k,e as n,b as B,h as G,j as J,y as g,m as P,f as p,a as K,F as Q,v as W,s as r,t as u}from"./app-BAwPsakn.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(F,{emit:$}){const f=O(),R=$,C={codeNumber:"",name:"",types:[],sources:[],startDate:"",endDate:"",status:""},i=I({...C}),b=I({types:[],sources:[],isLoaded:!(f.query.types||f.query.sources)});return z(async()=>{b.types=f.query.types?f.query.types.split(","):[],b.sources=f.query.sources?f.query.sources.split(","):[],b.isLoaded=!0}),(m,a)=>{const q=o("BaseInput"),D=o("BaseSelectSearch"),w=o("DatePicker"),d=o("BaseSelect"),s=o("FilterForm");return y(),v(s,{"init-form":C,form:i,multiple:["types","sources"],onHide:a[7]||(a[7]=l=>R("hide"))},{default:e(()=>[k("div",X,[k("div",Y,[n(q,{type:"text",modelValue:i.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=l=>i.codeNumber=l),name:"codeNumber",label:m.$trans("reception.enquiry.props.code_number")},null,8,["modelValue","label"])]),k("div",Z,[n(q,{type:"text",modelValue:i.name,"onUpdate:modelValue":a[1]||(a[1]=l=>i.name=l),name:"name",label:m.$trans("reception.enquiry.props.name")},null,8,["modelValue","label"])]),k("div",x,[b.isLoaded?(y(),v(D,{key:0,multiple:"",name:"types",label:m.$trans("global.select",{attribute:m.$trans("reception.enquiry.type.type")}),modelValue:i.types,"onUpdate:modelValue":a[2]||(a[2]=l=>i.types=l),"value-prop":"uuid","init-search":b.types,"search-action":"option/list","additional-search-query":{type:"enquiry_type"}},null,8,["label","modelValue","init-search"])):B("",!0)]),k("div",ee,[b.isLoaded?(y(),v(D,{key:0,multiple:"",name:"sources",label:m.$trans("global.select",{attribute:m.$trans("reception.enquiry.source.source")}),modelValue:i.sources,"onUpdate:modelValue":a[3]||(a[3]=l=>i.sources=l),"value-prop":"uuid","init-search":b.sources,"search-action":"option/list","additional-search-query":{type:"enquiry_source"}},null,8,["label","modelValue","init-search"])):B("",!0)]),k("div",te,[n(w,{start:i.startDate,"onUpdate:start":a[4]||(a[4]=l=>i.startDate=l),end:i.endDate,"onUpdate:end":a[5]||(a[5]=l=>i.endDate=l),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])]),k("div",ne,[n(d,{name:"status",label:m.$trans("global.select",{attribute:m.$trans("reception.enquiry.props.status")}),modelValue:i.status,"onUpdate:modelValue":a[6]||(a[6]=l=>i.status=l),options:F.preRequisites.statuses},null,8,["label","modelValue","options"])])])]),_:1},8,["form"])}}},se={name:"ReceptionEnquiryList"},ie=Object.assign(se,{setup(F){const $=G(),f=J("emitter");let R=["filter"];g("reception:config")&&R.push("config"),g("enquiry:create")&&R.unshift("create");let C=[];g("enquiry:export")&&(C=["print","pdf","excel"]),g("enquiry:create")&&C.unshift("import");const i="reception/enquiry/",b=I({statuses:[]}),m=P(!1),a=P(!1),q=I({}),D=d=>{Object.assign(q,d)},w=d=>{Object.assign(b,d)};return(d,s)=>{const l=o("PageHeaderAction"),E=o("PageHeader"),T=o("BaseImport"),h=o("ParentTransition"),_=o("DataCell"),N=o("TextMuted"),L=o("BaseBadge"),V=o("FloatingMenuItem"),U=o("FloatingMenu"),H=o("DataRow"),M=o("BaseButton"),j=o("DataTable"),A=o("ListItem");return y(),v(A,{"init-url":i,"additional-query":{},onSetItems:D,"pre-requisites":!0,onSetPreRequisites:w},{header:e(()=>[n(E,{title:d.$trans("reception.enquiry.enquiry"),navs:[{label:d.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[n(l,{url:"reception/enquiries/",name:"ReceptionEnquiry",title:d.$trans("reception.enquiry.enquiry"),actions:p(R),"dropdown-actions":p(C),"config-path":"ReceptionConfig",onToggleFilter:s[0]||(s[0]=t=>m.value=!m.value),onToggleImport:s[1]||(s[1]=t=>a.value=!a.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:e(()=>[n(h,{appear:"",visibility:a.value},{default:e(()=>[n(T,{path:"reception/enquiries/import",onCancelled:s[2]||(s[2]=t=>a.value=!1),onHide:s[3]||(s[3]=t=>a.value=!1),onCompleted:s[4]||(s[4]=t=>p(f).emit("listItems"))})]),_:1},8,["visibility"])]),filter:e(()=>[n(h,{appear:"",visibility:m.value},{default:e(()=>[n(ae,{onRefresh:s[5]||(s[5]=t=>p(f).emit("listItems")),"pre-requisites":b,onHide:s[6]||(s[6]=t=>m.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[n(h,{appear:"",visibility:!0},{default:e(()=>[n(j,{header:q.headers,meta:q.meta,module:"reception.enquiry",onRefresh:s[8]||(s[8]=t=>p(f).emit("listItems"))},{actionButton:e(()=>[p(g)("enquiry:create")?(y(),v(M,{key:0,onClick:s[7]||(s[7]=t=>p($).push({name:"ReceptionEnquiryCreate"}))},{default:e(()=>[r(u(d.$trans("global.add",{attribute:d.$trans("reception.enquiry.enquiry")})),1)]),_:1})):B("",!0)]),default:e(()=>[(y(!0),K(Q,null,W(q.data,t=>(y(),v(H,{key:t.uuid,onDoubleClick:c=>p($).push({name:"ReceptionEnquiryShow",params:{uuid:t.uuid}})},{default:e(()=>[n(_,{name:"codeNumber"},{default:e(()=>[r(u(t.codeNumber),1)]),_:2},1024),n(_,{name:"date"},{default:e(()=>[r(u(t.date.formatted),1)]),_:2},1024),n(_,{name:"name"},{default:e(()=>[r(u(t.name)+" ",1),n(N,{block:""},{default:e(()=>[r(u(t.contactNumber),1)]),_:2},1024)]),_:2},1024),n(_,{name:"type"},{default:e(()=>{var c;return[r(u(((c=t.type)==null?void 0:c.name)||"-"),1)]}),_:2},1024),n(_,{name:"source"},{default:e(()=>{var c;return[r(u(((c=t.source)==null?void 0:c.name)||"-"),1)]}),_:2},1024),n(_,{name:"count"},{default:e(()=>[r(u(t.recordsCount),1)]),_:2},1024),n(_,{name:"employee"},{default:e(()=>{var c;return[r(u(((c=t.employee)==null?void 0:c.name)||"-")+" ",1),t.employee?(y(),v(N,{key:0,block:""},{default:e(()=>{var S;return[r(u(((S=t.employee)==null?void 0:S.designation)||"-"),1)]}),_:2},1024)):B("",!0)]}),_:2},1024),n(_,{name:"status"},{default:e(()=>[n(L,{design:t.status.color},{default:e(()=>[r(u(t.status.label),1)]),_:2},1032,["design"])]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[r(u(t.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(U,null,{default:e(()=>[n(V,{icon:"fas fa-arrow-circle-right",onClick:c=>p($).push({name:"ReceptionEnquiryShow",params:{uuid:t.uuid}})},{default:e(()=>[r(u(d.$trans("general.show")),1)]),_:2},1032,["onClick"]),p(g)("enquiry:edit")?(y(),v(V,{key:0,icon:"fas fa-edit",onClick:c=>p($).push({name:"ReceptionEnquiryEdit",params:{uuid:t.uuid}})},{default:e(()=>[r(u(d.$trans("general.edit")),1)]),_:2},1032,["onClick"])):B("",!0),p(g)("enquiry:create")?(y(),v(V,{key:1,icon:"fas fa-copy",onClick:c=>p($).push({name:"ReceptionEnquiryDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[r(u(d.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):B("",!0),p(g)("enquiry:delete")?(y(),v(V,{key:2,icon:"fas fa-trash",onClick:c=>p(f).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[r(u(d.$trans("general.delete")),1)]),_:2},1032,["onClick"])):B("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ie as default};
