import{l as w,r as l,q as A,o as v,w as e,d as C,e as t,h as R,j as T,m as j,f as o,a as L,F as M,v as S,s as u,t as r}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},O={__name:"Filter",emits:["hide"],setup(h,{emit:d}){const c=d,f={name:"",alias:""},m=w({...f});return(g,i)=>{const p=l("BaseInput"),B=l("FilterForm");return v(),A(B,{"init-form":f,form:m,onHide:i[2]||(i[2]=n=>c("hide"))},{default:e(()=>[C("div",N,[C("div",U,[t(p,{type:"text",modelValue:m.name,"onUpdate:modelValue":i[0]||(i[0]=n=>m.name=n),name:"name",label:g.$trans("asset.building.block.props.name")},null,8,["modelValue","label"])]),C("div",E,[t(p,{type:"text",modelValue:m.alias,"onUpdate:modelValue":i[1]||(i[1]=n=>m.alias=n),name:"alias",label:g.$trans("asset.building.block.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},q={name:"AssetBuildingBlockList"},G=Object.assign(q,{setup(h){const d=R(),c=T("emitter");let f=["create","filter"],m=["print","pdf","excel"];const g="asset/building/block/",i=j(!1),p=w({}),B=n=>{Object.assign(p,n)};return(n,s)=>{const $=l("BaseButton"),I=l("PageHeaderAction"),D=l("PageHeader"),F=l("ParentTransition"),_=l("DataCell"),b=l("FloatingMenuItem"),V=l("FloatingMenu"),y=l("DataRow"),H=l("DataTable"),P=l("ListItem");return v(),A(P,{"init-url":g,onSetItems:B},{header:e(()=>[t(D,{title:n.$trans("asset.building.block.block"),navs:[{label:n.$trans("asset.asset"),path:"Asset"},{label:n.$trans("asset.building.building"),path:"AssetBuilding"}]},{default:e(()=>[t(I,{url:"asset/building/blocks/",name:"AssetBuildingBlock",title:n.$trans("asset.building.block.block"),actions:o(f),"dropdown-actions":o(m),onToggleFilter:s[2]||(s[2]=a=>i.value=!i.value)},{default:e(()=>[t($,{design:"white",onClick:s[0]||(s[0]=a=>o(d).push({name:"AssetBuildingFloor"}))},{default:e(()=>[u(r(n.$trans("asset.building.floor.floor")),1)]),_:1}),t($,{design:"white",onClick:s[1]||(s[1]=a=>o(d).push({name:"AssetBuildingRoom"}))},{default:e(()=>[u(r(n.$trans("asset.building.room.room")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(F,{appear:"",visibility:i.value},{default:e(()=>[t(O,{onRefresh:s[3]||(s[3]=a=>o(c).emit("listItems")),onHide:s[4]||(s[4]=a=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(F,{appear:"",visibility:!0},{default:e(()=>[t(H,{header:p.headers,meta:p.meta,module:"asset.building.block",onRefresh:s[6]||(s[6]=a=>o(c).emit("listItems"))},{actionButton:e(()=>[t($,{onClick:s[5]||(s[5]=a=>o(d).push({name:"AssetBuildingBlockCreate"}))},{default:e(()=>[u(r(n.$trans("global.add",{attribute:n.$trans("asset.building.block.block")})),1)]),_:1})]),default:e(()=>[(v(!0),L(M,null,S(p.data,a=>(v(),A(y,{key:a.uuid,onDoubleClick:k=>o(d).push({name:"AssetBuildingBlockShow",params:{uuid:a.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[u(r(a.name),1)]),_:2},1024),t(_,{name:"alias"},{default:e(()=>[u(r(a.alias),1)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[u(r(a.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(V,null,{default:e(()=>[t(b,{icon:"fas fa-arrow-circle-right",onClick:k=>o(d).push({name:"AssetBuildingBlockShow",params:{uuid:a.uuid}})},{default:e(()=>[u(r(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(b,{icon:"fas fa-edit",onClick:k=>o(d).push({name:"AssetBuildingBlockEdit",params:{uuid:a.uuid}})},{default:e(()=>[u(r(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(b,{icon:"fas fa-copy",onClick:k=>o(d).push({name:"AssetBuildingBlockDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[u(r(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(b,{icon:"fas fa-trash",onClick:k=>o(c).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[u(r(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{G as default};
