import{i as v,u as C,h as P,l as T,r as n,a as V,o as d,e as a,w as e,f as r,q as p,b as _,d as N,s,t as l,y as A,F as D}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"HostelBlockInchargeShow"},M=Object.assign(j,{setup(E){v();const u=C(),m=P(),g={},h="hostel/blockIncharge/",o=T({...g}),b=t=>{Object.assign(o,t)};return(t,i)=>{const f=n("PageHeaderAction"),k=n("PageHeader"),B=n("TextMuted"),c=n("BaseDataView"),y=n("BaseButton"),$=n("ShowButton"),H=n("BaseCard"),I=n("ShowItem"),w=n("ParentTransition");return d(),V(D,null,[a(k,{title:t.$trans(r(u).meta.trans,{attribute:t.$trans(r(u).meta.label)}),navs:[{label:t.$trans("hostel.hostel"),path:"Hostel"},{label:t.$trans("hostel.block_incharge.block_incharge"),path:"HostelBlockInchargeList"}]},{default:e(()=>[a(f,{name:"HostelBlockIncharge",title:t.$trans("hostel.block_incharge.block_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(w,{appear:"",visibility:!0},{default:e(()=>[a(I,{"init-url":h,uuid:r(u).params.uuid,onSetItem:b,onRedirectTo:i[1]||(i[1]=S=>r(m).push({name:"HostelBlockIncharge"}))},{default:e(()=>[o.uuid?(d(),p(H,{key:0},{title:e(()=>[s(l(o.block.name),1)]),footer:e(()=>[a($,null,{default:e(()=>[r(A)("hostel-incharge:edit")?(d(),p(y,{key:0,design:"primary",onClick:i[0]||(i[0]=S=>r(m).push({name:"HostelBlockInchargeEdit",params:{uuid:o.uuid}}))},{default:e(()=>[s(l(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[N("dl",R,[a(c,{label:t.$trans("employee.employee")},{default:e(()=>[s(l(o.employee.name)+" ",1),a(B,{block:""},{default:e(()=>[s(l(o.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),a(c,{label:t.$trans("employee.incharge.props.period")},{default:e(()=>[s(l(o.period),1)]),_:1},8,["label"]),a(c,{class:"col-span-1 sm:col-span-2",label:t.$trans("employee.incharge.props.remarks")},{default:e(()=>[s(l(o.remarks),1)]),_:1},8,["label"]),a(c,{label:t.$trans("general.created_at")},{default:e(()=>[s(l(o.createdAt.formatted),1)]),_:1},8,["label"]),a(c,{label:t.$trans("general.updated_at")},{default:e(()=>[s(l(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
