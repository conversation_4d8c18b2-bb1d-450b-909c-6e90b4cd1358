import{u as V,i as A,j as E,m as R,l as F,n as G,r as n,a as p,o as l,q as _,b,e as a,w as e,f as c,F as v,v as L,s as o,t as d,d as g}from"./app-BAwPsakn.js";const O={key:0},W={key:0},$={name:"StudentShowSubject"},M=Object.assign($,{props:{student:{type:Object,default(){return{}}}},setup(u){const k=V(),h=A(),t=E("$trans"),j=u,S="student/",r=R(!1),y=F({subjects:[]}),w=[{key:"name",label:t("academic.subject.props.name"),visibility:!0},{key:"code",label:t("academic.subject.props.code"),visibility:!0},{key:"credit",label:t("academic.subject.props.credit"),visibility:!0},{key:"maxClassPerWeek",label:t("academic.subject.props.max_class_per_week"),visibility:!0},{key:"isElective",label:t("academic.subject.props.is_elective"),visibility:!0},{key:"hasGrading",label:t("academic.subject.props.has_grading"),visibility:!0}];let x=[];const C=async()=>{r.value=!0,await h.dispatch(S+"fetchSubject",{uuid:j.student.uuid}).then(f=>{r.value=!1,y.subjects=f.subjects}).catch(f=>{r.value=!1})};return G(async()=>{await C()}),(f,m)=>{const P=n("PageHeaderAction"),B=n("PageHeader"),i=n("DataCell"),D=n("DataRow"),H=n("SimpleTable"),T=n("BaseCard"),N=n("ParentTransition");return l(),p(v,null,[u.student.uuid?(l(),_(B,{key:0,title:c(t)(c(k).meta.label),navs:[{label:c(t)("student.student"),path:"Student"},{label:u.student.contact.name,path:{name:"StudentShow",params:{uuid:u.student.uuid}}}]},{default:e(()=>[a(P,{"additional-actions":c(x)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):b("",!0),a(N,{appear:"",visibility:!0},{default:e(()=>[u.student.uuid?(l(),_(T,{key:0,"is-loading":r.value,"no-padding":"","no-content-padding":""},{title:e(()=>[o(d(c(t)("global.overview",{attribute:c(t)("academic.subject.subject")})),1)]),default:e(()=>[a(H,{header:w},{default:e(()=>[(l(!0),p(v,null,L(y.subjects,s=>(l(),_(D,{key:s.uuid},{default:e(()=>[a(i,null,{default:e(()=>[o(d(s.name),1)]),_:2},1024),a(i,null,{default:e(()=>[o(d(s.code),1)]),_:2},1024),a(i,null,{default:e(()=>[o(d(s.credit),1)]),_:2},1024),a(i,null,{default:e(()=>[o(d(s.maxClassPerWeek),1)]),_:2},1024),a(i,null,{default:e(()=>[s.isElective?(l(),p("span",O,m[0]||(m[0]=[g("i",{class:"far fa-check-circle text-success fa-lg"},null,-1)]))):b("",!0)]),_:2},1024),a(i,null,{default:e(()=>[s.hasGrading?(l(),p("span",W,m[1]||(m[1]=[g("i",{class:"far fa-check-circle text-success fa-lg"},null,-1)]))):b("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["is-loading"])):b("",!0)]),_:1})],64)}}});export{M as default};
