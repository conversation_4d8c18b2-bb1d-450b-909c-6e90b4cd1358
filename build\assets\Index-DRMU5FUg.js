import{l as w,r,q as D,o as _,w as e,d as y,e as t,h as B,j as k,m as A,f as b,a as H,F as T,v as V,s as m,t as u}from"./app-BAwPsakn.js";const h={class:"grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},U={class:"col-span-3 sm:col-span-1"},j={__name:"Filter",emits:["hide"],setup(F,{emit:c}){const f=c,l={search:"",startDate:"",endDate:""},n=w({...l});return(p,a)=>{const o=r("BaseInput"),v=r("DatePicker"),g=r("FilterForm");return _(),D(g,{"init-form":l,form:n,onHide:a[3]||(a[3]=i=>f("hide"))},{default:e(()=>[y("div",h,[y("div",R,[t(o,{type:"text",modelValue:n.search,"onUpdate:modelValue":a[0]||(a[0]=i=>n.search=i),name:"search",label:p.$trans("general.search")},null,8,["modelValue","label"])]),y("div",U,[t(v,{type:"text",start:n.startDate,"onUpdate:start":a[1]||(a[1]=i=>n.startDate=i),end:n.endDate,"onUpdate:end":a[2]||(a[2]=i=>n.endDate=i),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},x={name:"ActivityLogList"},N=Object.assign(x,{setup(F){B();const c=k("emitter"),f="utility/activityLog/",l=A(!1),n=w({}),p=a=>{Object.assign(n,a)};return(a,o)=>{const v=r("PageHeaderAction"),g=r("PageHeader"),i=r("ParentTransition"),d=r("DataCell"),$=r("DataRow"),I=r("DataTable"),P=r("ListItem");return _(),D(P,{"init-url":f,onSetItems:p},{header:e(()=>[t(g,{title:a.$trans("utility.activity.log")},{default:e(()=>[t(v,{url:"utility/activity-logs/",name:"UtilityActivityLog",title:a.$trans("utility.activity.log"),actions:["filter"],"dropdown-actions":["print","pdf","excel"],onToggleFilter:o[0]||(o[0]=s=>l.value=!l.value)},null,8,["title"])]),_:1},8,["title"])]),filter:e(()=>[t(i,{appear:"",visibility:l.value},{default:e(()=>[t(j,{onRefresh:o[1]||(o[1]=s=>b(c).emit("listItems")),onHide:o[2]||(o[2]=s=>l.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(i,{appear:"",visibility:!0},{default:e(()=>[t(I,{header:n.headers,meta:n.meta,module:"utility.activity",onRefresh:o[3]||(o[3]=s=>b(c).emit("listItems"))},{default:e(()=>[(_(!0),H(T,null,V(n.data,s=>(_(),D($,{key:s.uuid},{default:e(()=>[t(d,{name:"user"},{default:e(()=>[m(u(s.user?s.user.profile.name:"-"),1)]),_:2},1024),t(d,{name:"activity"},{default:e(()=>[m(u(s.activity),1)]),_:2},1024),t(d,{name:"ip"},{default:e(()=>[m(u(s.ip),1)]),_:2},1024),t(d,{name:"browser"},{default:e(()=>[m(u(s.browser),1)]),_:2},1024),t(d,{name:"os"},{default:e(()=>[m(u(s.os),1)]),_:2},1024),t(d,{name:"createdAt"},{default:e(()=>[m(u(s.createdAt.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{N as default};
