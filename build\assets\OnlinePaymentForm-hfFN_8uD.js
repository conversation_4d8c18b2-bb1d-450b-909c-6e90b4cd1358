import{n as A,r as k,a as $,b as r,o as l,d as v,e as c,w as o,s as d,t as m,V as J,u as K,i as Q,H as W,m as X,l as N,L as Y,q as u,F as z,f as G,y as Z,M as x}from"./app-BAwPsakn.js";import{_ as ee,a as ae,b as te,c as ne,d as le,e as ie}from"./Billdesk-CH1h7WlK.js";const re=["action"],oe=["value"],se=["value"],ue={name:"PaymentGatewayPayzone"},de=Object.assign(ue,{props:{payment:{type:Object,required:!0}},setup(t){const U=()=>{document.getElementById("payzoneForm").submit()};return A(()=>{setTimeout(U,3e3)}),(R,F)=>{const C=k("TextMuted");return t.payment?(l(),$("form",{key:0,id:"payzoneForm",class:"mt-4",method:"post",action:t.payment.pgUrl,onSubmit:J(U,["prevent"])},[v("input",{type:"hidden",name:"payload",value:t.payment.payload},null,8,oe),v("input",{type:"hidden",name:"signature",value:t.payment.signature},null,8,se),c(C,{block:""},{default:o(()=>[d(m(R.$trans("general.redirecting_to_payment_gateway")),1)]),_:1})],40,re)):r("",!0)}}}),me={class:"grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-8 mb-4"},ye={class:"grid grid-cols-3 gap-4"},fe={class:"col-span-3 sm:col-span-1"},pe={key:0,class:"col-span-3"},ce={class:"mt-4"},ge={name:"StudentFeeOnlinePaymentForm"},ke=Object.assign(ge,{props:{visibility:{type:Boolean,default:!1},initUrl:{type:String,default:"student/payment/"},uuid:{type:String,default:""},date:{type:Object,default(){return{}}},fee:{type:Object,default(){return{}}},feeGroup:{type:Object,default(){return{}}},feeHead:{type:Object,default(){return{}}},preRequisites:{type:Object,default(){return{}}},tempPaymentUuid:{type:String,default:""}},emits:["close","completed"],setup(t,{emit:U}){const R=K(),F=Q(),C=U,i=t,s={uuid:i.uuid||R.params.uuid,feeHead:null,feeGroup:null,feeInstallment:null,date:"",amount:"",gateway:"",tempPaymentUuid:i.tempPaymentUuid},V=W(i.initUrl),O=X(!1),n=N({initiated:!1,payment:null}),e=N({...s}),T=()=>{_(),F.dispatch(i.initUrl+"resetFormErrors"),C("close")},h=()=>{n.initiated=!1,C("completed")},B=a=>{O.value=a},g=a=>!!i.preRequisites.paymentGateways.find(y=>y.value==a),_=()=>{n.initiated=!1,n.payment=null,e.gateway="",s.feeGroup=null,s.feeHead=null,s.feeInstallment=null,s.date="",Object.assign(e,s)},w=async()=>{await x()&&(O.value=!0,await F.dispatch(i.initUrl+"initiatePayment",e).then(a=>{if(a.hasOwnProperty("payment")&&a.payment.amount<=0){location.reload();return}n.initiated=!0,n.payment=a,O.value=!1}).catch(a=>{O.value=!1}))};return Y(()=>{var a,y,f,H,j,p;(a=i.feeGroup)!=null&&a.uuid?(s.feeGroup=i.feeGroup.uuid,s.amount=i.feeGroup.balance.value):(y=i.feeHead)!=null&&y.uuid?(s.feeHead=i.feeHead.uuid,s.amount=i.feeHead.balance.value):(f=i.fee)!=null&&f.uuid&&(s.feeInstallment=i.fee.uuid,s.amount=i.fee.balance.value),((j=(H=i.preRequisites)==null?void 0:H.paymentGateways)==null?void 0:j.length)==1&&(s.gateway=i.preRequisites.paymentGateways[0].value),s.date=(p=i.date)==null?void 0:p.value,Object.assign(e,s)}),(a,y)=>{const f=k("BaseDataView"),H=k("BaseInput"),j=k("BaseRadioGroup"),p=k("BaseButton"),D=k("BaseModal");return l(),u(D,{show:t.visibility,onClose:T},{title:o(()=>[d(m(a.$trans("global.pay",{attribute:a.$trans("student.fee.fee")})),1)]),default:o(()=>{var P,q,L,M,S,E,I;return[v("dl",me,[c(f,{label:a.$trans("general.date")},{default:o(()=>[d(m(t.date.formatted),1)]),_:1},8,["label"]),(P=t.feeGroup)!=null&&P.uuid?(l(),$(z,{key:0},[c(f,{label:a.$trans("finance.fee_group.fee_group")},{default:o(()=>[d(m(t.feeGroup.name),1)]),_:1},8,["label"]),c(f,{label:a.$trans("finance.fee.balance")},{default:o(()=>[d(m(t.feeGroup.balance.formatted),1)]),_:1},8,["label"])],64)):(q=t.feeHead)!=null&&q.uuid?(l(),$(z,{key:1},[c(f,{label:a.$trans("finance.fee_head.fee_head")},{default:o(()=>[d(m(t.feeHead.name),1)]),_:1},8,["label"]),c(f,{label:a.$trans("finance.fee.balance")},{default:o(()=>[d(m(t.feeHead.balance.formatted),1)]),_:1},8,["label"])],64)):t.fee.uuid?(l(),$(z,{key:2},[(L=t.fee.installment)!=null&&L.title?(l(),u(f,{key:0,label:a.$trans("finance.fee_structure.installment")},{default:o(()=>[d(m(t.fee.installment.title),1)]),_:1},8,["label"])):r("",!0),c(f,{label:a.$trans("finance.fee.balance")},{default:o(()=>[d(m(t.fee.balance.formatted),1)]),_:1},8,["label"])],64)):r("",!0)]),v("div",ye,[v("div",fe,[c(H,{disabled:!G(Z)("fee:partial-payment"),modelValue:e.amount,"onUpdate:modelValue":y[0]||(y[0]=b=>e.amount=b),name:"amount",label:a.$trans("student.fee.props.amount"),currency:"",error:G(V).amount,"onUpdate:error":y[1]||(y[1]=b=>G(V).amount=b)},null,8,["disabled","modelValue","label","error"])]),t.preRequisites.paymentGateways.length>1?(l(),$("div",pe,[c(j,{disabled:n.initiated,options:t.preRequisites.paymentGateways,name:"gateway",modelValue:e.gateway,"onUpdate:modelValue":y[2]||(y[2]=b=>e.gateway=b),error:G(V).gateway,"onUpdate:error":y[3]||(y[3]=b=>G(V).gateway=b),horizontal:""},null,8,["disabled","options","modelValue","error"])])):r("",!0)]),g("razorpay")&&e.gateway=="razorpay"?(l(),u(ee,{key:0,"init-url":t.initUrl,form:e,payment:n.payment,onLoading:B,onRefresh:h},null,8,["init-url","form","payment"])):r("",!0),g("paystack")&&e.gateway=="paystack"?(l(),u(ae,{key:1,"init-url":t.initUrl,form:e,payment:n.payment,onLoading:B,onRefresh:h},null,8,["init-url","form","payment"])):r("",!0),g("stripe")&&e.gateway=="stripe"?(l(),u(te,{key:2,"init-url":t.initUrl,form:e,payment:n.payment,onCancel:_,onLoading:B,onRefresh:h},null,8,["init-url","form","payment"])):r("",!0),g("payzone")&&e.gateway=="payzone"&&((M=n==null?void 0:n.payment)!=null&&M.token)?(l(),u(de,{key:3,"init-url":t.initUrl,form:e,payment:n.payment,onCancel:_,onLoading:B,onRefresh:h},null,8,["init-url","form","payment"])):r("",!0),g("paypal")&&e.gateway=="paypal"&&((S=n.payment)!=null&&S.token)?(l(),u(ne,{key:4,"init-url":t.initUrl,form:e,payment:n.payment,onCancel:_,onLoading:B,onRefresh:h},null,8,["init-url","form","payment"])):r("",!0),g("ccavenue")&&e.gateway=="ccavenue"&&((E=n.payment)!=null&&E.token)?(l(),u(le,{key:5,payment:n.payment},null,8,["payment"])):r("",!0),g("billdesk")&&e.gateway=="billdesk"&&((I=n.payment)!=null&&I.token)?(l(),u(ie,{key:6,payment:n.payment},null,8,["payment"])):r("",!0),v("div",ce,[e.gateway?(l(),$(z,{key:0},[e.gateway!="stripe"&&e.gateway!="payzone"&&e.gateway!="paypal"&&e.gateway!="ccavenue"&&e.gateway!="billdesk"?(l(),u(p,{key:0,design:"primary",onClick:w},{default:o(()=>[d(m(a.$trans("general.proceed")),1)]),_:1})):r("",!0),e.gateway=="stripe"&&!n.payment?(l(),u(p,{key:1,design:"primary",onClick:w},{default:o(()=>[d(m(a.$trans("general.proceed")),1)]),_:1})):r("",!0),e.gateway=="payzone"&&!n.payment?(l(),u(p,{key:2,design:"primary",onClick:w},{default:o(()=>[d(m(a.$trans("general.proceed")),1)]),_:1})):r("",!0),e.gateway=="paypal"&&!n.payment?(l(),u(p,{key:3,design:"primary",onClick:w},{default:o(()=>[d(m(a.$trans("general.proceed")),1)]),_:1})):r("",!0),e.gateway=="ccavenue"&&!n.payment?(l(),u(p,{key:4,design:"primary",onClick:w},{default:o(()=>[d(m(a.$trans("general.proceed")),1)]),_:1})):r("",!0),e.gateway=="billdesk"&&!n.payment?(l(),u(p,{key:5,design:"primary",onClick:w},{default:o(()=>[d(m(a.$trans("general.proceed")),1)]),_:1})):r("",!0)],64)):r("",!0)])]}),_:1},8,["show"])}}});export{ke as _};
