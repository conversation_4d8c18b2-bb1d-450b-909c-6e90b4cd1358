import{r as i,a as r,o as g,d as a,e as m,b,s as u,t as c,F as f}from"./app-BAwPsakn.js";const p={class:"overflow-hidden bg-black lg:rounded-t-lg"},v={name:"BlogAsset"},B=Object.assign(v,{props:{blog:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},emits:["refreshItem"],setup(s,{emit:o}){const t=o;return(l,e)=>{const d=i("ImageUpload");return g(),r("div",p,[a("div",null,[m(d,{class:"h-32 w-full lg:h-48",disabled:s.disabled,label:l.$trans("blog.assets.cover"),src:s.blog.assets.cover,"upload-path":`blogs/${s.blog.uuid}/assets/cover`,"remove-path":`blogs/${s.blog.uuid}/assets/cover`,design:"modern","show-label":!1,onUploaded:e[0]||(e[0]=n=>t("refreshItem")),onRemoved:e[1]||(e[1]=n=>t("refreshItem"))},null,8,["disabled","label","src","upload-path","remove-path"])])])}}}),h={class:"overflow-hidden bg-black"},$={key:0,class:"mx-2 mt-4 text-sm text-gray-600 dark:text-gray-400"},I={name:"BlogOg"},k=Object.assign(I,{props:{blog:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},emits:["refreshItem"],setup(s,{emit:o}){const t=o;return(l,e)=>{const d=i("ImageUpload");return g(),r(f,null,[a("div",h,[a("div",null,[m(d,{class:"h-32 w-full lg:h-48",disabled:s.disabled,label:l.$trans("blog.assets.og"),src:s.blog.assets.og,"upload-path":`blogs/${s.blog.uuid}/assets/og`,"remove-path":`blogs/${s.blog.uuid}/assets/og`,design:"modern","show-label":!1,onUploaded:e[0]||(e[0]=n=>t("refreshItem")),onRemoved:e[1]||(e[1]=n=>t("refreshItem"))},null,8,["disabled","label","src","upload-path","remove-path"])])]),s.blog.assets.defaultOg?(g(),r("p",$,[e[2]||(e[2]=a("i",{class:"fas fa-circle-info"},null,-1)),u(" "+c(l.$trans("blog.assets.og_info")),1)])):b("",!0)],64)}}});export{B as _,k as a};
