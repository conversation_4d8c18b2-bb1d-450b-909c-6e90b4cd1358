import{u as m,h as i,i as l,j as f,m as d,l as p,n as _,p as v,r as h,q as g,b,o as j}from"./app-BAwPsakn.js";const w={name:"FormSubmission"},F=Object.assign(w,{setup(k){const r=m(),n=i(),u=l(),s=f("emitter"),e=d(!1),t=p({}),a=async()=>{e.value=!0,await u.dispatch("form/get",{uuid:r.params.uuid}).then(o=>{Object.assign(t,o),e.value=!1}).catch(o=>{e.value=!1,n.push({name:"FormList"})})};return _(async()=>{s.on("formUpdated",()=>{a()}),await a()}),v(()=>{s.all.delete("formUpdated")}),(o,y)=>{const c=h("router-view");return t.uuid?(j(),g(c,{key:0,form:t},null,8,["form"])):b("",!0)}}});export{F as default};
