import{u as O,h as L,i as D,l as R,n as N,r as n,q as B,o as g,w as e,d as F,e as t,s as r,t as i,j as H,m as I,f as _,a as S,F as U,v as A,b as E}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["refresh","hide"],setup(j,{emit:v}){const d=O(),m=L();D();const C=v,c={search:""},l=R({...c}),u=()=>{Object.assign(l,c),h(),C("hide")};N(async()=>{Object.assign(l,{search:d.query.search})});const h=async()=>{await m.push({name:d.name,query:{...d.query,...l}}),C("refresh")};return(f,a)=>{const o=n("BaseInput"),$=n("BaseButton"),y=n("BaseCard");return g(),B(y,null,{footer:e(()=>[t($,{design:"error",class:"mr-4",onClick:u},{default:e(()=>[r(i(f.$trans("general.cancel")),1)]),_:1}),t($,{onClick:h},{default:e(()=>[r(i(f.$trans("general.filter")),1)]),_:1})]),default:e(()=>[F("div",z,[F("div",G,[t(o,{type:"text",modelValue:l.search,"onUpdate:modelValue":a[0]||(a[0]=p=>l.search=p),name:"search",label:f.$trans("general.search")},null,8,["modelValue","label"])])])]),_:1})}}},K={name:"LocaleList"},W=Object.assign(K,{setup(j){const v=L(),d=D(),m=H("emitter"),C="config/locale/",c=I(!1),l=I(!1),u=R({}),h=a=>{Object.assign(u,a)},f=a=>{l.value=!0,d.dispatch("config/locale/sync",{uuid:a}).then(()=>{}).catch(()=>{}).finally(()=>{l.value=!1})};return(a,o)=>{const $=n("PageHeaderAction"),y=n("ParentTransition"),p=n("DataCell"),b=n("FloatingMenuItem"),w=n("FloatingMenu"),V=n("DataRow"),P=n("BaseButton"),T=n("DataTable"),q=n("ListItem"),M=n("ConfigPage");return g(),B(M,{"no-background":""},{action:e(()=>[t($,{name:"ConfigLocale",title:a.$trans("config.locale.locale"),actions:["create","filter"],onToggleFilter:o[0]||(o[0]=s=>c.value=!c.value)},null,8,["title"])]),default:e(()=>[t(q,{class:"sm:-mt-4","init-url":C,onSetItems:h},{filter:e(()=>[t(y,{appear:"",visibility:c.value},{default:e(()=>[t(J,{onRefresh:o[1]||(o[1]=s=>_(m).emit("listItems")),onHide:o[2]||(o[2]=s=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(y,{appear:"",visibility:!0},{default:e(()=>[t(T,{header:u.headers,meta:u.meta,module:"config.locale",onRefresh:o[4]||(o[4]=s=>_(m).emit("listItems"))},{actionButton:e(()=>[t(P,{onClick:o[3]||(o[3]=s=>_(v).push({name:"LocaleCreate"}))},{default:e(()=>[r(i(a.$trans("global.add",{attribute:a.$trans("config.locale.locale")})),1)]),_:1})]),default:e(()=>[(g(!0),S(U,null,A(u.data,s=>(g(),B(V,{key:s.uuid},{default:e(()=>[t(p,{name:"name"},{default:e(()=>[r(i(s.name),1)]),_:2},1024),t(p,{name:"code"},{default:e(()=>[r(i(s.code),1)]),_:2},1024),t(p,{name:"action"},{default:e(()=>[s.isDefault?E("",!0):(g(),B(w,{key:0},{default:e(()=>[t(b,{icon:"fas fa-edit",onClick:k=>_(v).push({name:"ConfigLocaleEdit",params:{uuid:s.uuid}})},{default:e(()=>[r(i(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(b,{icon:"fas fa-arrows-rotate",onClick:k=>f(s.uuid)},{default:e(()=>[r(i(a.$trans("general.sync")),1)]),_:2},1032,["onClick"]),t(b,{icon:"fas fa-trash",onClick:k=>_(m).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[r(i(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})]),_:1})}}});export{W as default};
