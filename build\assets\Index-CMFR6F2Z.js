import{l as B,r as o,q as g,o as b,w as e,d as C,e as t,u as j,h as U,j as L,y as F,m as M,f as s,a as A,F as w,v as O,s as l,t as i,b as S}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(d,{emit:h}){const f=h,v={name:"",alias:"",number:""},r=B({...v});return($,u)=>{const c=o("BaseInput"),y=o("FilterForm");return b(),g(y,{"init-form":v,form:r,onHide:u[3]||(u[3]=n=>f("hide"))},{default:e(()=>[C("div",E,[C("div",q,[t(c,{type:"text",modelValue:r.name,"onUpdate:modelValue":u[0]||(u[0]=n=>r.name=n),name:"name",label:$.$trans("finance.account.props.name")},null,8,["modelValue","label"])]),C("div",z,[t(c,{type:"text",modelValue:r.alias,"onUpdate:modelValue":u[1]||(u[1]=n=>r.alias=n),name:"alias",label:$.$trans("finance.account.props.alias")},null,8,["modelValue","label"])]),C("div",G,[t(c,{type:"text",modelValue:r.number,"onUpdate:modelValue":u[2]||(u[2]=n=>r.number=n),name:"number",label:$.$trans("finance.account.props.number")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={name:"StudentAccountList"},W=Object.assign(K,{props:{student:{type:Object,default(){return{}}}},setup(d){const h=j(),f=U(),v=L("emitter");let r=["filter"];F("student:edit")&&r.unshift("create");const $="student/account/",u=M(!1),c=B({}),y=n=>{Object.assign(c,n)};return(n,m)=>{const I=o("PageHeaderAction"),D=o("PageHeader"),V=o("ParentTransition"),p=o("DataCell"),_=o("FloatingMenuItem"),N=o("FloatingMenu"),H=o("DataRow"),P=o("BaseButton"),R=o("DataTable"),T=o("ListItem");return b(),g(T,{"init-url":$,uuid:s(h).params.uuid,onSetItems:y},{header:e(()=>[d.student.uuid?(b(),g(D,{key:0,title:n.$trans("finance.account.account"),navs:[{label:n.$trans("student.student"),path:"Student"},{label:d.student.contact.name,path:{name:"StudentShow",params:{uuid:d.student.uuid}}}]},{default:e(()=>[t(I,{url:`students/${d.student.uuid}/accounts/`,name:"StudentAccount",title:n.$trans("finance.account.account"),actions:s(r),"dropdown-actions":["print","pdf","excel"],onToggleFilter:m[0]||(m[0]=a=>u.value=!u.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):S("",!0)]),filter:e(()=>[t(V,{appear:"",visibility:u.value},{default:e(()=>[t(J,{onRefresh:m[1]||(m[1]=a=>s(v).emit("listItems")),onHide:m[2]||(m[2]=a=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(V,{appear:"",visibility:!0},{default:e(()=>[t(R,{header:c.headers,meta:c.meta,module:"student.account",onRefresh:m[4]||(m[4]=a=>s(v).emit("listItems"))},{actionButton:e(()=>[s(F)("student:edit")?(b(),g(P,{key:0,onClick:m[3]||(m[3]=a=>s(f).push({name:"StudentAccountCreate"}))},{default:e(()=>[l(i(n.$trans("global.add",{attribute:n.$trans("finance.account.account")})),1)]),_:1})):S("",!0)]),default:e(()=>[(b(!0),A(w,null,O(c.data,a=>(b(),g(H,{key:a.uuid,onDoubleClick:k=>s(f).push({name:"StudentAccountShow",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:e(()=>[t(p,{name:"name"},{default:e(()=>[l(i(a.name),1)]),_:2},1024),t(p,{name:"alias"},{default:e(()=>[l(i(a.alias),1)]),_:2},1024),t(p,{name:"number"},{default:e(()=>[l(i(a.number),1)]),_:2},1024),t(p,{name:"bankName"},{default:e(()=>[l(i(a.bankName),1)]),_:2},1024),t(p,{name:"branchName"},{default:e(()=>[l(i(a.branchName),1)]),_:2},1024),t(p,{name:"createdAt"},{default:e(()=>[l(i(a.createdAt.formatted),1)]),_:2},1024),t(p,{name:"action"},{default:e(()=>[t(N,null,{default:e(()=>[t(_,{icon:"fas fa-arrow-circle-right",onClick:k=>s(f).push({name:"StudentAccountShow",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:e(()=>[l(i(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),s(F)("student:edit")?(b(),A(w,{key:0},[t(_,{icon:"fas fa-edit",onClick:k=>s(f).push({name:"StudentAccountEdit",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:e(()=>[l(i(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-copy",onClick:k=>s(f).push({name:"StudentAccountDuplicate",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:e(()=>[l(i(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-trash",onClick:k=>s(v).emit("deleteItem",{uuid:d.student.uuid,moduleUuid:a.uuid})},{default:e(()=>[l(i(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):S("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{W as default};
