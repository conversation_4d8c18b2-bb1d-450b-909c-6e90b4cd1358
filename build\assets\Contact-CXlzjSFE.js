import{u as _,j as A,y as B,r as d,a as P,o as i,q as m,b,e as n,w as c,f as a,d as w,s,t as l,F as D}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},V={name:"ContactShowContact"},v=Object.assign(V,{props:{contact:{type:Object,default(){return{}}}},setup(t){const p=_(),e=A("$trans"),f=t;let u=[];return B("contact:edit")&&u.push({label:e("general.edit"),path:{name:"ContactEditContact",params:{uuid:f.contact.uuid}}}),(k,x)=>{const g=d("PageHeaderAction"),C=d("PageHeader"),o=d("BaseDataView"),y=d("BaseCard"),h=d("ParentTransition");return i(),P(D,null,[t.contact.uuid?(i(),m(C,{key:0,title:a(e)(a(p).meta.label),navs:[{label:a(e)("contact.contact"),path:"Contact"},{label:t.contact.name,path:{name:"ContactShow",params:{uuid:t.contact.uuid}}}]},{default:c(()=>[n(g,{"additional-actions":a(u)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):b("",!0),n(h,{appear:"",visibility:!0},{default:c(()=>[t.contact.uuid?(i(),m(y,{key:0},{default:c(()=>[w("dl",N,[n(o,{label:a(e)("contact.props.contact_number")},{default:c(()=>[s(l(t.contact.contactNumber),1)]),_:1},8,["label"]),n(o,{label:a(e)("global.alternate",{attribute:a(e)("contact.props.contact_number")})},{default:c(()=>{var r;return[s(l((r=t.contact.alternateRecords)==null?void 0:r.contactNumber),1)]}),_:1},8,["label"]),n(o),n(o,{label:a(e)("contact.props.email")},{default:c(()=>[s(l(t.contact.email),1)]),_:1},8,["label"]),n(o,{label:a(e)("global.alternate",{attribute:a(e)("contact.props.email")})},{default:c(()=>{var r;return[s(l((r=t.contact.alternateRecords)==null?void 0:r.email),1)]}),_:1},8,["label"]),n(o),n(o,{class:"col-span-1 sm:col-span-3",label:a(e)("contact.props.present_address")},{default:c(()=>[s(l(t.contact.presentAddressDisplay),1)]),_:1},8,["label"]),n(o,{class:"col-span-1 sm:col-span-3",label:a(e)("contact.props.permanent_address")},{default:c(()=>[s(l(t.contact.sameAsPresentAddress?t.contact.presentAddressDisplay:t.contact.permanentAddressDisplay),1)]),_:1},8,["label"])])]),_:1})):b("",!0)]),_:1})],64)}}});export{v as default};
