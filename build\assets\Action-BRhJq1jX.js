import{u as g,G as b,H as y,l as v,r as d,q as B,o as h,w as f,d as i,e as n,f as l,J as H,a as j,F as A}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},D={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3"},N={class:"grid grid-cols-1"},w={class:"col"},C={name:"TransportVehicleFuelRecordForm"},G=Object.assign(C,{setup(U){const p=g(),s={vehicle:"",quantity:"",pricePerUnit:"",log:"",date:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:b(),mediaHash:[]},m="transport/vehicle/fuelRecord/",a=y(m),u=v({vehicles:[]}),t=v({...s}),_=v({vehicle:"",isLoaded:!p.params.uuid}),V=o=>{Object.assign(u,o)},k=()=>{t.mediaToken=b(),t.mediaHash=[]},P=o=>{var e;Object.assign(s,{...o,pricePerUnit:o.pricePerUnit.value,date:o.date.value,vehicle:(e=o.vehicle)==null?void 0:e.uuid}),Object.assign(t,H(s)),_.vehicle=o.vehicle.name,_.isLoaded=!0};return(o,e)=>{const $=d("BaseSelect"),c=d("BaseInput"),F=d("DatePicker"),T=d("BaseTextarea"),q=d("MediaUpload"),R=d("FormAction");return h(),B(R,{"pre-requisites":!0,onSetPreRequisites:V,"init-url":m,"init-form":s,form:t,"set-form":P,redirect:"TransportVehicleFuelRecord",onResetMediaFiles:k},{default:f(()=>[i("div",O,[i("div",D,[n($,{modelValue:t.vehicle,"onUpdate:modelValue":e[0]||(e[0]=r=>t.vehicle=r),name:"vehicle",label:o.$trans("transport.vehicle.vehicle"),"label-prop":"nameWithRegistrationNumber","value-prop":"uuid",options:u.vehicles,error:l(a).vehicle,"onUpdate:error":e[1]||(e[1]=r=>l(a).vehicle=r)},null,8,["modelValue","label","options","error"])]),i("div",M,[n(c,{type:"number",step:.01,modelValue:t.quantity,"onUpdate:modelValue":e[2]||(e[2]=r=>t.quantity=r),name:"quantity",label:o.$trans("transport.vehicle.fuel_record.props.quantity"),error:l(a).quantity,"onUpdate:error":e[3]||(e[3]=r=>l(a).quantity=r)},null,8,["modelValue","label","error"])]),i("div",S,[n(c,{currency:"",modelValue:t.pricePerUnit,"onUpdate:modelValue":e[4]||(e[4]=r=>t.pricePerUnit=r),name:"pricePerUnit",label:o.$trans("transport.vehicle.fuel_record.props.price_per_unit"),error:l(a).pricePerUnit,"onUpdate:error":e[5]||(e[5]=r=>l(a).pricePerUnit=r)},null,8,["modelValue","label","error"])]),i("div",E,[n(c,{type:"number",modelValue:t.log,"onUpdate:modelValue":e[6]||(e[6]=r=>t.log=r),"trailing-text":o.$trans("list.unit.km"),name:"log",label:o.$trans("transport.vehicle.fuel_record.props.log"),error:l(a).log,"onUpdate:error":e[7]||(e[7]=r=>l(a).log=r)},null,8,["modelValue","trailing-text","label","error"])]),i("div",I,[n(F,{modelValue:t.date,"onUpdate:modelValue":e[8]||(e[8]=r=>t.date=r),name:"date",label:o.$trans("transport.vehicle.fuel_record.props.date"),"no-clear":"",error:l(a).date,"onUpdate:error":e[9]||(e[9]=r=>l(a).date=r)},null,8,["modelValue","label","error"])]),i("div",L,[n(T,{modelValue:t.remarks,"onUpdate:modelValue":e[10]||(e[10]=r=>t.remarks=r),name:"remarks",label:o.$trans("transport.vehicle.fuel_record.props.remarks"),error:l(a).remarks,"onUpdate:error":e[11]||(e[11]=r=>l(a).remarks=r)},null,8,["modelValue","label","error"])])]),i("div",N,[i("div",w,[n(q,{multiple:"",label:o.$trans("general.file"),module:"vehicle_fuel_record",media:t.media,"media-token":t.mediaToken,onIsUpdated:e[12]||(e[12]=r=>t.mediaUpdated=!0),onSetHash:e[13]||(e[13]=r=>t.mediaHash.push(r))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),J={name:"TransportVehicleFuelRecordAction"},z=Object.assign(J,{setup(U){const p=g();return(s,m)=>{const a=d("PageHeaderAction"),u=d("PageHeader"),t=d("ParentTransition");return h(),j(A,null,[n(u,{title:s.$trans(l(p).meta.trans,{attribute:s.$trans(l(p).meta.label)}),navs:[{label:s.$trans("transport.transport"),path:"Transport"},{label:s.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:s.$trans("transport.vehicle.fuel_record.fuel_record"),path:"TransportVehicleFuelRecordList"}]},{default:f(()=>[n(a,{name:"TransportVehicleFuelRecord",title:s.$trans("transport.vehicle.fuel_record.fuel_record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(t,{appear:"",visibility:!0},{default:f(()=>[n(G)]),_:1})],64)}}});export{z as default};
