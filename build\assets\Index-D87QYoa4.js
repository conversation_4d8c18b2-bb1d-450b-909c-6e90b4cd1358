import{u as T,G as N,H as z,l as w,K as G,r as u,q as V,o as d,w as s,e as l,d as b,a as B,b as Q,f as m,F as L,v as J,s as _,t as g,M as W,J as P,i as X,m as I,n as K,h as Y,j as Z,y as ee,p as te}from"./app-BAwPsakn.js";import{d as oe}from"./vuedraggable.umd-BRYqknf6.js";const ne={key:0},se={key:1},ae={class:"grid grid-cols-2 gap-6"},le={class:"col-span-2 sm:col-span-1"},ie={class:"col-span-2 sm:col-span-1"},re={class:"col-span-2"},ue=["onClick"],de={class:"mt-4 grid grid-cols-4 gap-4"},me={class:"col-span-3 sm:col-span-3"},ce={class:"col-span-3 sm:col-span-1"},pe={class:"mt-4"},fe={key:0,class:"col-span-2"},ve={name:"OnlineExamQuestionForm"},be=Object.assign(ve,{props:{onlineExam:{type:Object,default(){return{}}},visibility:{type:Boolean,default:!1},action:{type:String,default:"create"},selectedQuestion:{type:Object,default(){return{}}}},emits:["close","completed"],setup(c,{emit:M}){T();const C=M,a=c,k={title:"",description:"",type:"mcq",mark:0,options:[],header:"",showHeader:!1},O={uuid:N(),title:"",isCorrect:!1},x="exam/onlineExam/question/",r=z(x),f=w({types:[]}),t=w({}),E=w({isLoaded:!0}),h=n=>{Object.assign(f,n)},R=()=>{t.options.push({...O,uuid:N()}),E.isLoaded=!0},v=async n=>{await W()&&(t.options.length==1?t.options=[O]:t.options.splice(n,1))},j=n=>{t.options[n].isCorrect&&t.options.forEach((e,U)=>{U!==n&&(e.isCorrect=!1)})};return G(()=>[a.action,a.selectedQuestion.uuid],n=>{if(!a.selectedQuestion.uuid){Object.assign(t,P(k));return}Object.assign(k,{title:a.selectedQuestion.title,mark:a.selectedQuestion.mark,type:a.selectedQuestion.type.value,options:a.selectedQuestion.options,header:a.selectedQuestion.header,showHeader:!!a.selectedQuestion.header}),Object.assign(t,P(k))}),(n,e)=>{const U=u("BaseSelect"),p=u("BaseInput"),y=u("BaseEditor"),q=u("BaseSwitch"),F=u("BaseFieldset"),S=u("BaseBadge"),A=u("FormAction"),D=u("BaseSideover");return d(),V(D,{visibility:c.visibility,onClose:e[12]||(e[12]=H=>C("close"))},{title:s(()=>[c.action=="create"?(d(),B("span",ne,g(n.$trans("global.add",{attribute:n.$trans("exam.online_exam.question.question")})),1)):(d(),B("span",se,g(n.$trans("global.edit",{attribute:n.$trans("exam.online_exam.question.question")})),1))]),default:s(()=>{var H;return[l(A,{sideover:"","no-card":"","no-data-fetch":"","cancel-action":"","pre-requisites":!0,onSetPreRequisites:h,"keep-adding":c.action!="update",action:c.action,"init-url":x,"init-form":k,moduleUuid:(H=c.selectedQuestion)==null?void 0:H.uuid,form:t,onEnd:e[9]||(e[9]=o=>C("close")),onCompleted:e[10]||(e[10]=o=>C("completed")),onCancelled:e[11]||(e[11]=o=>C("close"))},{default:s(()=>[b("div",ae,[b("div",le,[l(U,{disabled:c.onlineExam.type.value=="mcq",modelValue:t.type,"onUpdate:modelValue":e[0]||(e[0]=o=>t.type=o),name:"type",label:n.$trans("exam.online_exam.question.props.type"),options:f.types,error:m(r).type,"onUpdate:error":e[1]||(e[1]=o=>m(r).type=o)},null,8,["disabled","modelValue","label","options","error"])]),b("div",ie,[l(p,{type:"number",modelValue:t.mark,"onUpdate:modelValue":e[2]||(e[2]=o=>t.mark=o),name:"mark",label:n.$trans("exam.online_exam.question.props.mark"),error:m(r).mark,"onUpdate:error":e[3]||(e[3]=o=>m(r).mark=o)},null,8,["modelValue","label","error"])]),b("div",re,[l(y,{modelValue:t.title,"onUpdate:modelValue":e[4]||(e[4]=o=>t.title=o),name:"title",edit:!0,toolbar:"minimal",label:n.$trans("exam.online_exam.question.props.title"),error:m(r).title,"onUpdate:error":e[5]||(e[5]=o=>m(r).title=o),height:100},null,8,["modelValue","label","error"])])]),t.type=="mcq"?(d(),B(L,{key:0},[(d(!0),B(L,null,J(t.options,(o,i)=>(d(),V(F,{class:"mt-4",key:o.uuid},{legend:s(()=>[_(g(i+1)+". ",1),b("span",{class:"text-danger ml-2 cursor-pointer",onClick:$=>v(i)},e[13]||(e[13]=[b("i",{class:"fas fa-times-circle"},null,-1)]),8,ue)]),default:s(()=>[b("div",de,[b("div",me,[l(p,{type:"text",modelValue:o.title,"onUpdate:modelValue":$=>o.title=$,name:`options.${i}.title`,label:n.$trans("exam.online_exam.question.props.option"),error:m(r)[`options.${i}.title`],"onUpdate:error":$=>m(r)[`options.${i}.title`]=$},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),b("div",ce,[l(q,{vertical:"",modelValue:o.isCorrect,"onUpdate:modelValue":$=>o.isCorrect=$,name:`options.${i}.isCorrect`,label:n.$trans("exam.online_exam.question.props.correct_answer"),error:m(r)[`options.${i}.isCorrect`],"onUpdate:error":$=>m(r)[`options.${i}.isCorrect`]=$,onChange:$=>j(i)},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","onChange"])])])]),_:2},1024))),128)),b("div",pe,[l(S,{design:"primary",onClick:R,class:"cursor-pointer"},{default:s(()=>[_(g(n.$trans("global.add",{attribute:n.$trans("general.option")})),1)]),_:1})])],64)):Q("",!0),l(F,{class:"mt-4"},{legend:s(()=>[l(q,{reverse:"",modelValue:t.showHeader,"onUpdate:modelValue":e[6]||(e[6]=o=>t.showHeader=o),name:"showHeader",label:n.$trans("global.show",{attribute:n.$trans("exam.online_exam.question.props.header")})},null,8,["modelValue","label"])]),default:s(()=>[t.showHeader?(d(),B("div",fe,[l(y,{modelValue:t.header,"onUpdate:modelValue":e[7]||(e[7]=o=>t.header=o),name:"header",edit:!0,toolbar:"minimal",label:n.$trans("exam.online_exam.question.props.header"),error:m(r).header,"onUpdate:error":e[8]||(e[8]=o=>m(r).header=o),height:100},null,8,["modelValue","label","error"])])):Q("",!0)]),_:1})]),_:1},8,["keep-adding","action","moduleUuid","form"])]}),_:1},8,["visibility"])}}}),ge={key:0},ye={class:"flex border rounded-xl px-4 py-2"},$e=["innerHTML"],_e={key:1},ke={key:2,class:"mt-4 flex justify-end"},he={name:"ExamOnlineExamQuestionReorder"},Be=Object.assign(he,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(c,{emit:M}){const C=T(),a=X(),k=M,O=c,x={questions:[]};z("exam/onlineExam/question/");const f=I(!1),t=w({questions:[]});w({...x});const E=async()=>{f.value=!0,await a.dispatch("exam/onlineExam/question/list",{uuid:C.params.uuid,params:{all:!0}}).then(v=>{f.value=!1,t.questions=v}).catch(v=>{f.value=!1})},h=async()=>{f.value=!0,await a.dispatch("exam/onlineExam/question/reorder",{uuid:C.params.uuid,data:{questions:t.questions}}).then(v=>{f.value=!1,k("refresh"),k("close")}).catch(v=>{f.value=!1})},R=()=>{k("close")};return K(()=>{}),G(O.submission,v=>{E()},{deep:!0,immediate:!0}),(v,j)=>{const n=u("BaseAlert"),e=u("BaseButton"),U=u("BaseModal");return d(),V(U,{show:c.visibility,onClose:R},{title:s(()=>[_(g(v.$trans("global.reorder",{attribute:v.$trans("exam.online_exam.question.question")})),1)]),default:s(()=>[t.questions.length?(d(),B("div",ge,[l(m(oe),{class:"space-y-2",list:t.questions,"item-key":"uuid"},{item:s(({element:p,index:y})=>[b("div",ye,[j[0]||(j[0]=b("i",{class:"fas fa-arrows mr-2 cursor-pointer dark:text-gray-400"},null,-1)),b("div",{class:"dark:text-gray-400",innerHTML:p.title},null,8,$e)])]),_:1},8,["list"])])):(d(),B("div",_e,[l(n,{design:"info",size:"xs"},{default:s(()=>[_(g(v.$trans("general.errors.record_not_found")),1)]),_:1})])),t.questions.length?(d(),B("div",ke,[l(e,{onClick:h},{default:s(()=>[_(g(v.$trans("general.reorder")),1)]),_:1})])):Q("",!0)]),_:1},8,["show"])}}}),Ce=["innerHTML"],xe={name:"OnlineExamQuestionList"},we=Object.assign(xe,{props:{onlineExam:{type:Object,default(){return{}}}},emits:["refresh"],setup(c,{emit:M}){const C=T();Y();const a=Z("emitter"),k=M;ee("online-exam:edit");const O="exam/onlineExam/question/",x=I("create"),r=I(!1),f=I(!1),t=w({}),E=w({uuid:null,title:"",type:"",mark:0}),h=w({}),R=p=>{Object.assign(t,p)},v=p=>{Object.assign(h,{...p}),x.value="update",r.value=!0},j=p=>{Object.assign(h,{...p}),x.value="create",r.value=!0},n=()=>{k("refresh")},e=()=>{Object.assign(h,E),n(),a.emit("listItems")},U=()=>{Object.assign(h,E),r.value=!1};return K(async()=>{a.on("addQuestion",()=>{Object.assign(h,E),x.value="create",r.value=!0}),a.on("reorderQuestion",()=>{f.value=!0}),a.on("actionPerformed",()=>{n()})}),te(()=>{a.all.delete("addQuestion"),a.all.delete("reorderQuestion"),a.all.delete("actionPerformed")}),(p,y)=>{const q=u("DataCell"),F=u("FloatingMenuItem"),S=u("FloatingMenu"),A=u("DataRow"),D=u("BaseButton"),H=u("DataTable"),o=u("ListItem");return d(),B(L,null,[l(o,{"init-url":O,uuid:m(C).params.uuid,onSetItems:R},{default:s(()=>[l(H,{header:t.headers,meta:t.meta,module:"exam.online_exam.question",onRefresh:y[1]||(y[1]=i=>m(a).emit("listItems"))},{actionButton:s(()=>[c.onlineExam.canManageQuestion?Q("",!0):(d(),V(D,{key:0,onClick:y[0]||(y[0]=i=>r.value=!0)},{default:s(()=>[_(g(p.$trans("global.add",{attribute:p.$trans("exam.online_exam.question.question")})),1)]),_:1}))]),default:s(()=>[(d(!0),B(L,null,J(t.data,i=>(d(),V(A,{key:i.uuid},{default:s(()=>[l(q,{name:"title"},{default:s(()=>[b("div",{innerHTML:i.title},null,8,Ce)]),_:2},1024),l(q,{name:"type"},{default:s(()=>[_(g(i.type.label),1)]),_:2},1024),l(q,{name:"mark"},{default:s(()=>[_(g(i.mark),1)]),_:2},1024),l(q,{name:"createdAt"},{default:s(()=>[_(g(i.createdAt.formatted),1)]),_:2},1024),l(q,{name:"action"},{default:s(()=>[c.onlineExam.publishedAt.value?Q("",!0):(d(),V(S,{key:0},{default:s(()=>[l(F,{icon:"fas fa-edit",onClick:$=>v(i)},{default:s(()=>[_(g(p.$trans("general.edit")),1)]),_:2},1032,["onClick"]),l(F,{icon:"fas fa-copy",onClick:$=>j(i)},{default:s(()=>[_(g(p.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),l(F,{icon:"fas fa-trash",onClick:$=>m(a).emit("deleteItem",{uuid:c.onlineExam.uuid,moduleUuid:i.uuid})},{default:s(()=>[_(g(p.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["uuid"]),f.value?(d(),V(Be,{key:0,visibility:f.value,onClose:y[2]||(y[2]=i=>f.value=!1),onRefresh:y[3]||(y[3]=i=>m(a).emit("listItems"))},null,8,["visibility"])):Q("",!0),c.onlineExam?(d(),V(be,{key:1,"online-exam":c.onlineExam,action:x.value,visibility:r.value,"selected-question":h,onCompleted:e,onClose:U},null,8,["online-exam","action","visibility","selected-question"])):Q("",!0)],64)}}});export{we as default};
