import{i as N,u as k,h as C,l as P,r as l,a as V,o as m,e as s,w as a,f as u,q as c,b as _,d as b,s as n,t as o,F as D}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},I={class:"mt-4 text-sm space-y-2"},T={name:"StudentLeaveRequestShow"},A=Object.assign(T,{setup(E){N();const i=k(),p=C(),f={},v="student/leaveRequest/",e=P({...f}),q=t=>{Object.assign(e,t)};return(t,d)=>{const g=l("PageHeaderAction"),$=l("PageHeader"),r=l("BaseDataView"),h=l("ListMedia"),B=l("BaseButton"),S=l("ShowButton"),R=l("BaseCard"),w=l("ShowItem"),y=l("ParentTransition");return m(),V(D,null,[s($,{title:t.$trans(u(i).meta.trans,{attribute:t.$trans(u(i).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:t.$trans("student.leave_request.leave_request"),path:"StudentLeaveRequestList"}]},{default:a(()=>[s(g,{name:"StudentLeaveRequest",title:t.$trans("student.leave_request.leave_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(y,{appear:"",visibility:!0},{default:a(()=>[s(w,{"init-url":v,uuid:u(i).params.uuid,onSetItem:q,onRedirectTo:d[1]||(d[1]=L=>u(p).push({name:"StudentLeaveRequest"}))},{default:a(()=>[e.uuid?(m(),c(R,{key:0},{title:a(()=>[n(o(e.student.name),1)]),footer:a(()=>[s(S,null,{default:a(()=>[e.isEditable?(m(),c(B,{key:0,design:"primary",onClick:d[0]||(d[0]=L=>u(p).push({name:"StudentLeaveRequestEdit",params:{uuid:e.uuid}}))},{default:a(()=>[n(o(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:a(()=>[b("dl",H,[s(r,{label:t.$trans("student.admission.props.code_number")},{default:a(()=>[n(o(e.student.codeNumber),1)]),_:1},8,["label"]),s(r,{label:t.$trans("contact.props.birth_date")},{default:a(()=>[n(o(e.student.birthDate.formatted),1)]),_:1},8,["label"]),s(r,{label:t.$trans("contact.props.father_name")},{default:a(()=>[n(o(e.student.fatherName),1)]),_:1},8,["label"]),s(r,{label:t.$trans("contact.props.mother_name")},{default:a(()=>[n(o(e.student.motherName),1)]),_:1},8,["label"]),s(r,{label:t.$trans("academic.course.course")},{default:a(()=>[n(o(e.student.courseName+" "+e.student.batchName),1)]),_:1},8,["label"]),s(r,{label:t.$trans("student.leave_request.props.requester")},{default:a(()=>[n(o(e.requester.profile.name),1)]),_:1},8,["label"]),s(r,{label:t.$trans("general.period")},{default:a(()=>[n(o(e.period),1)]),_:1},8,["label"]),s(r,{label:t.$trans("student.leave_request.props.category")},{default:a(()=>[n(o(e.category.name),1)]),_:1},8,["label"]),s(r,{label:t.$trans("student.leave_request.props.reason")},{default:a(()=>[n(o(e.reason),1)]),_:1},8,["label"])]),b("div",I,[s(h,{media:e.media,url:`/app/student/leave-requests/${e.uuid}/`},null,8,["media","url"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{A as default};
