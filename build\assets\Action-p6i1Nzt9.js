import{H as y,l as b,r as m,q as B,o as _,w as f,d as p,e as a,f as r,J as H,u as R,a as F,F as P}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},O={class:"col-span-4 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},E={name:"HostelRoomForm"},S=Object.assign(E,{setup(V){const i={name:"",number:"",floor:"",capacity:"",description:""},l="hostel/room/",s=y(l),u=b({floors:[]}),t=b({...i}),d=n=>{Object.assign(u,n)},g=n=>{Object.assign(i,{...n,floor:n.floorUuid}),Object.assign(t,H(i))};return(n,o)=>{const c=m("BaseInput"),v=m("BaseSelect"),U=m("BaseTextarea"),$=m("FormAction");return _(),B($,{"pre-requisites":!0,onSetPreRequisites:d,"init-url":l,"init-form":i,form:t,"set-form":g,redirect:"HostelRoom"},{default:f(()=>[p("div",j,[p("div",q,[a(c,{type:"text",modelValue:t.name,"onUpdate:modelValue":o[0]||(o[0]=e=>t.name=e),name:"name",label:n.$trans("hostel.room.props.name"),error:r(s).name,"onUpdate:error":o[1]||(o[1]=e=>r(s).name=e),autofocus:""},null,8,["modelValue","label","error"])]),p("div",A,[a(c,{type:"text",modelValue:t.number,"onUpdate:modelValue":o[2]||(o[2]=e=>t.number=e),name:"number",label:n.$trans("hostel.room.props.number"),error:r(s).number,"onUpdate:error":o[3]||(o[3]=e=>r(s).number=e),autofocus:""},null,8,["modelValue","label","error"])]),p("div",O,[a(v,{modelValue:t.floor,"onUpdate:modelValue":o[4]||(o[4]=e=>t.floor=e),name:"floor",label:n.$trans("hostel.floor.floor"),options:u.floors,"label-prop":"nameWithBlock","value-prop":"uuid",error:r(s).floor,"onUpdate:error":o[5]||(o[5]=e=>r(s).floor=e)},null,8,["modelValue","label","options","error"])]),p("div",k,[a(c,{type:"text",modelValue:t.capacity,"onUpdate:modelValue":o[6]||(o[6]=e=>t.capacity=e),name:"capacity",label:n.$trans("hostel.room.props.capacity"),error:r(s).capacity,"onUpdate:error":o[7]||(o[7]=e=>r(s).capacity=e)},null,8,["modelValue","label","error"])]),p("div",T,[a(U,{modelValue:t.description,"onUpdate:modelValue":o[8]||(o[8]=e=>t.description=e),name:"description",label:n.$trans("hostel.room.props.description"),error:r(s).description,"onUpdate:error":o[9]||(o[9]=e=>r(s).description=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),h={name:"HostelRoomAction"},C=Object.assign(h,{setup(V){const i=R();return(l,s)=>{const u=m("PageHeaderAction"),t=m("PageHeader"),d=m("ParentTransition");return _(),F(P,null,[a(t,{title:l.$trans(r(i).meta.trans,{attribute:l.$trans(r(i).meta.label)}),navs:[{label:l.$trans("hostel.hostel"),path:"Hostel"},{label:l.$trans("hostel.room.room"),path:"HostelRoomList"}]},{default:f(()=>[a(u,{name:"HostelRoom",title:l.$trans("hostel.room.room"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(d,{appear:"",visibility:!0},{default:f(()=>[a(S)]),_:1})],64)}}});export{C as default};
