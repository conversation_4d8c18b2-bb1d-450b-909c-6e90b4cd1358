import{i as ne,u as se,h as re,g as w,l as ue,r as s,a as O,o as $,e as a,w as n,f as r,q,b as _,d as de,s as o,t as i,F as oe}from"./app-BAwPsakn.js";const ie={key:0,class:"mb-4"},ce={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},pe={name:"StudentProfileEditRequestShow"},me=Object.assign(pe,{props:{student:{type:Object,default(){return{}}}},setup(f){ne();const h=se(),T=re(),j={},F="student/profileEditRequest/",M=w("contact.uniqueIdNumber1Label"),G=w("contact.uniqueIdNumber2Label"),U=w("contact.uniqueIdNumber3Label"),J=w("contact.uniqueIdNumber4Label"),K=w("contact.uniqueIdNumber5Label"),e=ue({...j}),Q=t=>{Object.assign(e,t)};return(t,y)=>{const W=s("PageHeaderAction"),X=s("PageHeader"),Y=s("BaseAlert"),Z=s("BaseBadge"),c=s("BaseDataView"),l=s("HorizontalListItem"),A=s("HorizontalList"),L=s("BaseFieldset"),x=s("ListMedia"),ee=s("ShowButton"),ae=s("BaseCard"),te=s("ShowItem"),le=s("ParentTransition");return $(),O(oe,null,[a(X,{title:t.$trans(r(h).meta.trans,{attribute:t.$trans(r(h).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:f.student.contact.name,path:{name:"StudentShow",params:{uuid:f.student.uuid}}}]},{default:n(()=>[a(W,{name:"StudentProfileEditRequest",title:t.$trans("student.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(le,{appear:"",visibility:!0},{default:n(()=>[a(te,{"init-url":F,uuid:r(h).params.uuid,"module-uuid":r(h).params.muuid,onSetItem:Q,onRedirectTo:y[0]||(y[0]=g=>r(T).push({name:"StudentProfileEditRequest",params:{uuid:f.student.uuid}}))},{default:n(()=>[e.uuid?($(),q(ae,{key:0},{title:n(()=>[o(i(t.$trans("student.edit_request.request_by_name",{attribute:e.user.profile.name})),1)]),footer:n(()=>[a(ee)]),default:n(()=>[e.isRejected&&e.comment?($(),O("div",ie,[a(Y,{design:"error",size:"xs"},{default:n(()=>[o(i(e.comment),1)]),_:1})])):_("",!0),de("dl",ce,[a(c,{label:t.$trans("student.edit_request.props.status")},{default:n(()=>[a(Z,{design:e.status.color},{default:n(()=>[o(i(e.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),a(c,{class:"col-span-1 sm:col-span-2"},{default:n(()=>{var g,I,N,B,S,P,z,R,C,E,H,k,V;return[a(A,null,{default:n(()=>{var u,d;return[a(l,{label:t.$trans("contact.props.contact_number"),value:e.data.new.contactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.alternate_contact_number"),value:e.data.new.alternateContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.email"),value:e.data.new.email},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_contact_number"),value:e.data.new.fatherContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_email"),value:e.data.new.fatherEmail},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_birth_date"),value:(u=e.data.new.fatherBirthDate)==null?void 0:u.formatted},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_occupation"),value:e.data.new.fatherOccupation},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.father_annual_income"),value:e.data.new.fatherAnnualIncome},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_contact_number"),value:e.data.new.motherContactNumber},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_email"),value:e.data.new.motherEmail},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_birth_date"),value:(d=e.data.new.motherBirthDate)==null?void 0:d.formatted},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_occupation"),value:e.data.new.motherOccupation},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_annual_income"),value:e.data.new.motherAnnualIncome},null,8,["label","value"]),a(l,{label:r(M),value:e.data.new.uniqueIdNumber1},null,8,["label","value"]),a(l,{label:r(G),value:e.data.new.uniqueIdNumber2},null,8,["label","value"]),a(l,{label:r(U),value:e.data.new.uniqueIdNumber3},null,8,["label","value"]),a(l,{label:r(J),value:e.data.new.uniqueIdNumber4},null,8,["label","value"]),a(l,{label:r(K),value:e.data.new.uniqueIdNumber5},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.birth_place"),value:e.data.new.birthPlace},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.nationality"),value:e.data.new.nationality},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.mother_tongue"),value:e.data.new.motherTongue},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.blood_group"),value:t.$trans("list.blood_groups."+e.data.new.bloodGroup)},null,8,["label","value"]),a(l,{label:t.$trans("contact.religion.religion"),value:e.data.new.religion},null,8,["label","value"]),a(l,{label:t.$trans("contact.category.category"),value:e.data.new.category},null,8,["label","value"]),a(l,{label:t.$trans("contact.caste.caste"),value:e.data.new.caste},null,8,["label","value"])]}),_:1}),(g=e.data.new.presentAddress)!=null&&g.addressLine1||(I=e.data.new.presentAddress)!=null&&I.addressLine2||(N=e.data.new.presentAddress)!=null&&N.city||(B=e.data.new.presentAddress)!=null&&B.state||(S=e.data.new.presentAddress)!=null&&S.zipcode||(P=e.data.new.presentAddress)!=null&&P.country?($(),q(L,{key:0,class:"mt-4"},{legend:n(()=>[o(i(t.$trans("contact.props.present_address")),1)]),default:n(()=>[a(A,null,{default:n(()=>{var u,d,p,b,m,v;return[a(l,{label:t.$trans("contact.props.address.address_line1"),value:(u=e.data.new.presentAddress)==null?void 0:u.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(d=e.data.new.presentAddress)==null?void 0:d.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:(p=e.data.new.presentAddress)==null?void 0:p.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(b=e.data.new.presentAddress)==null?void 0:b.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(m=e.data.new.presentAddress)==null?void 0:m.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(v=e.data.new.presentAddress)==null?void 0:v.country},null,8,["label","value"])]}),_:1})]),_:1})):_("",!0),(z=e.data.new.permanentAddress)!=null&&z.sameAsPresentAddress||(R=e.data.new.permanentAddress)!=null&&R.addressLine1||(C=e.data.new.permanentAddress)!=null&&C.addressLine2||(E=e.data.new.permanentAddress)!=null&&E.city||(H=e.data.new.permanentAddress)!=null&&H.state||(k=e.data.new.permanentAddress)!=null&&k.zipcode||(V=e.data.new.permanentAddress)!=null&&V.country?($(),q(L,{key:1,class:"mt-4"},{legend:n(()=>[o(i(t.$trans("contact.props.permanent_address")),1)]),default:n(()=>[a(A,null,{default:n(()=>{var u,d,p,b,m,v,D;return[a(l,{label:t.$trans("contact.props.same_as_present_address"),value:(u=e.data.new.permanentAddress)!=null&&u.sameAsPresentAddress?t.$trans("general.yes"):t.$trans("general.no")},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line1"),value:(d=e.data.new.permanentAddress)==null?void 0:d.addressLine1},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.address_line2"),value:(p=e.data.new.permanentAddress)==null?void 0:p.addressLine2},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.city"),value:(b=e.data.new.permanentAddress)==null?void 0:b.city},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.state"),value:(m=e.data.new.permanentAddress)==null?void 0:m.state},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.zipcode"),value:(v=e.data.new.permanentAddress)==null?void 0:v.zipcode},null,8,["label","value"]),a(l,{label:t.$trans("contact.props.address.country"),value:(D=e.data.new.permanentAddress)==null?void 0:D.country},null,8,["label","value"])]}),_:1})]),_:1})):_("",!0)]}),_:1}),a(c,{class:"col-span-1 sm:col-span-2"},{default:n(()=>[a(x,{media:e.media,url:`/app/students/${f.student.uuid}/edit-requests/${e.uuid}/`},null,8,["media","url"])]),_:1}),a(c,{label:t.$trans("general.created_at")},{default:n(()=>[o(i(e.createdAt.formatted),1)]),_:1},8,["label"]),a(c,{label:t.$trans("general.updated_at")},{default:n(()=>[o(i(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{me as default};
