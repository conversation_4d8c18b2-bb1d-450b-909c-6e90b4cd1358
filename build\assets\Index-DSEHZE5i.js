import{u as P,j as W,l as V,H as z,n as q,r as d,q as $,o as _,w as t,d as b,e,b as A,s as u,t as s,h as G,i as J,y as K,m as T,a as H,f as y,F as M,v as Q}from"./app-BAwPsakn.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(R,{emit:F}){const h=P();W("moment");const k=F,w=R,v={codeNumber:"",voucherNumber:"",name:"",batches:[],startDate:"",endDate:""},l=V({...v});z(w.initUrl);const c=V({isLoaded:!h.query.batches});return q(async()=>{c.batches=h.query.batches?h.query.batches.split(","):[],c.isLoaded=!0}),(i,a)=>{const p=d("BaseInput"),D=d("BaseSelectSearch"),r=d("DatePicker"),f=d("FilterForm");return _(),$(f,{"init-form":v,multiple:["batches"],form:l,onHide:a[6]||(a[6]=n=>k("hide"))},{default:t(()=>[b("div",X,[b("div",Y,[e(p,{type:"text",modelValue:l.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=n=>l.codeNumber=n),name:"codeNumber",label:i.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),b("div",Z,[e(p,{type:"text",modelValue:l.voucherNumber,"onUpdate:modelValue":a[1]||(a[1]=n=>l.voucherNumber=n),name:"voucherNumber",label:i.$trans("finance.transaction.props.code_number")},null,8,["modelValue","label"])]),b("div",x,[e(p,{type:"text",modelValue:l.name,"onUpdate:modelValue":a[2]||(a[2]=n=>l.name=n),name:"name",label:i.$trans("contact.props.name")},null,8,["modelValue","label"])]),b("div",ee,[c.isLoaded?(_(),$(D,{key:0,multiple:"",name:"batches",label:i.$trans("global.select",{attribute:i.$trans("academic.batch.batch")}),modelValue:l.batches,"onUpdate:modelValue":a[3]||(a[3]=n=>l.batches=n),"value-prop":"uuid","init-search":c.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(n=>[u(s(n.value.course.name)+" "+s(n.value.name),1)]),listOption:t(n=>[u(s(n.option.course.nameWithTerm)+" "+s(n.option.name),1)]),_:1},8,["label","modelValue","init-search"])):A("",!0)]),b("div",te,[e(r,{start:l.startDate,"onUpdate:start":a[4]||(a[4]=n=>l.startDate=n),end:l.endDate,"onUpdate:end":a[5]||(a[5]=n=>l.endDate=n),name:"dateBetween",as:"range",label:i.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ne={name:"FinanceReportFeeRefund"},se=Object.assign(ne,{setup(R){const F=P(),h=G(),k=J();let w=["filter"],v=[];K("finance:export")&&(v=["print","pdf","excel"]);const l="finance/report/",c=T(!1),i=T(!1),a=V({headers:[],data:[],meta:{total:0}}),p=async()=>{i.value=!0,await k.dispatch(l+"fetchReport",{name:"fee-refund",params:F.query}).then(r=>{i.value=!1,Object.assign(a,r)}).catch(r=>{i.value=!1})},D=r=>{window.open(`/app/students/${r.studentUuid}/transactions/${r.uuid}/export?action=print`)};return q(async()=>{await p()}),(r,f)=>{const n=d("PageHeaderAction"),L=d("PageHeader"),B=d("ParentTransition"),g=d("TextMuted"),m=d("DataCell"),C=d("FloatingMenuItem"),O=d("FloatingMenu"),j=d("DataRow"),I=d("DataTable"),E=d("BaseCard");return _(),H(M,null,[e(L,{title:r.$trans(y(F).meta.label),navs:[{label:r.$trans("finance.finance"),path:"Finance"},{label:r.$trans("finance.report.report"),path:"FinanceReport"}]},{default:t(()=>[e(n,{url:"finance/reports/fee-refund/",name:"FinanceReportFeeRefund",title:r.$trans("finance.report.fee_refund.fee_refund"),actions:y(w),"dropdown-actions":y(v),headers:a.headers,onToggleFilter:f[0]||(f[0]=o=>c.value=!c.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),e(B,{appear:"",visibility:c.value},{default:t(()=>[e(ae,{onAfterFilter:p,"init-url":l,onHide:f[1]||(f[1]=o=>c.value=!1)})]),_:1},8,["visibility"]),e(B,{appear:"",visibility:!0},{default:t(()=>[e(E,{"no-padding":"","no-content-padding":"","is-loading":i.value},{default:t(()=>[e(I,{header:a.headers,footer:a.footers,meta:a.meta,module:"finance.report.fee_refund",onRefresh:p},{default:t(()=>[(_(!0),H(M,null,Q(a.data,o=>(_(),$(j,{key:o.uuid},{default:t(()=>[e(m,{name:"voucherNumber"},{default:t(()=>[u(s(o.voucherNumber)+" ",1),o.isOnline?(_(),$(g,{key:0,block:""},{default:t(()=>[u(s(o.referenceNumber),1)]),_:2},1024)):A("",!0)]),_:2},1024),e(m,{name:"name"},{default:t(()=>[u(s(o.name)+" ",1),e(g,{block:""},{default:t(()=>[u(s(o.rollNumber||o.codeNumber),1)]),_:2},1024)]),_:2},1024),e(m,{name:"fatherName"},{default:t(()=>[u(s(o.fatherName)+" ",1),e(g,{block:""},{default:t(()=>[u(s(o.contactNumber),1)]),_:2},1024)]),_:2},1024),e(m,{name:"course"},{default:t(()=>[u(s(o.courseName)+" ",1),e(g,{block:""},{default:t(()=>[u(s(o.batchName),1)]),_:2},1024)]),_:2},1024),e(m,{name:"amount"},{default:t(()=>[u(s(o.amount.formatted),1)]),_:2},1024),e(m,{name:"date"},{default:t(()=>[u(s(o.date.formatted),1)]),_:2},1024),e(m,{name:"ledger"},{default:t(()=>{var N,U;return[u(s((U=(N=o.payment)==null?void 0:N.ledger)==null?void 0:U.name)+" ",1),e(g,{block:""},{default:t(()=>{var S;return[u(s((S=o.payment)==null?void 0:S.methodName),1)]}),_:2},1024)]}),_:2},1024),e(m,{name:"action"},{default:t(()=>[e(O,null,{default:t(()=>[e(C,{icon:"fas fa-print",onClick:N=>D(o)},{default:t(()=>[u(s(r.$trans("global.print",{attribute:r.$trans("student.fee.receipt")})),1)]),_:2},1032,["onClick"]),e(C,{icon:"fas fa-arrow-circle-right",onClick:N=>y(h).push({name:"StudentShowFee",params:{uuid:o.studentUuid}})},{default:t(()=>[u(s(r.$trans("global.show",{attribute:r.$trans("student.fee.fee")})),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{se as default};
