import{u as _,H as j,l as S,r as l,q as f,o as g,w as m,d as a,e as i,f as r,t as u}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-1 gap-6"},C={class:"col-span-1"},I={class:"col-span-1"},B={class:"col-span-1"},U={class:"col-span-1"},$={class:"text-sm"},y={name:"ConfigSMSTemplateForm"},P=Object.assign(y,{setup(b){_();const p={subject:"",content:"",templateId:"",variablesDisplay:""},c="config/smsTemplate/",n=j(c),o=S({...p});return(s,e)=>{const d=l("BaseInput"),v=l("BaseTextarea"),V=l("FormAction");return g(),f(V,{"init-url":c,"init-form":p,form:o,redirect:"ConfigSMSTemplate"},{default:m(()=>[a("div",T,[a("div",C,[i(d,{type:"text",modelValue:o.subject,"onUpdate:modelValue":e[0]||(e[0]=t=>o.subject=t),name:"subject",label:s.$trans("config.sms.template.props.subject"),error:r(n).subject,"onUpdate:error":e[1]||(e[1]=t=>r(n).subject=t),autofocus:""},null,8,["modelValue","label","error"])]),a("div",I,[i(d,{type:"text",modelValue:o.templateId,"onUpdate:modelValue":e[2]||(e[2]=t=>o.templateId=t),name:"templateId",label:s.$trans("config.sms.template.props.template_id"),error:r(n).templateId,"onUpdate:error":e[3]||(e[3]=t=>r(n).templateId=t)},null,8,["modelValue","label","error"])]),a("div",B,[i(v,{modelValue:o.content,"onUpdate:modelValue":e[4]||(e[4]=t=>o.content=t),name:"description",label:s.$trans("config.sms.template.props.content"),error:r(n).content,"onUpdate:error":e[5]||(e[5]=t=>r(n).content=t)},null,8,["modelValue","label","error"])]),a("div",U,[a("p",$,u(s.$trans("config.sms.template.available_variables"))+": "+u(o.variablesDisplay),1)])])]),_:1},8,["form"])}}}),A={name:"ConfigSMSTemplateAction"},x=Object.assign(A,{setup(b){return _(),(p,c)=>{const n=l("PageHeaderAction"),o=l("ParentTransition"),s=l("ConfigPage");return g(),f(s,{"no-background":""},{action:m(()=>[i(n,{name:"ConfigSMSTemplate",title:p.$trans("config.sms.template.template"),actions:["list"]},null,8,["title"])]),default:m(()=>[i(o,{appear:"",visibility:!0},{default:m(()=>[i(P)]),_:1})]),_:1})}}});export{x as default};
