import{u as N,i as H,m as O,H as G,l as F,n as L,a9 as q,r as v,q as U,o as d,w as m,a as w,b as p,d as y,e as i,f as a,s as g,t as u,B as J,F as M,h as K,y as Q}from"./app-BAwPsakn.js";const X={key:0,class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={key:0,class:"col-span-3 sm:col-span-1"},P={class:"ml-1"},ee={class:"ml-1"},te={key:1,class:"col-span-3 sm:col-span-1"},se={key:0,class:"ml-1"},ae={key:0,class:"ml-1"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},re={class:"flex items-center gap-2"},le={key:0,class:"grid grid-cols-3 gap-6"},ue={key:0,class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},ve={key:0,class:"col-span-3 sm:col-span-1"},be={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup($,{emit:A}){const l=N(),x=H(),T=A,C=$,S={type:"",term:"",exam:"",batch:"",students:[],cumulativeAssessment:!1,resultDate:"",column:1,marginTop:0,showCourseWise:!1,showAllStudent:!1,showSummaryReport:!1,sortSummaryReportByRank:!1},k=O(!1),c=O(!1),o=G(C.initUrl),s=F({...S}),R=F({exams:C.preRequisites.exams,students:[]}),r=F({exam:"",batch:"",students:[],isLoaded:!(l.query.exam||l.query.batch||l.query.students)}),f=async n=>{R.students=[],s.students=[];let t="studying";s.showAllStudent&&(t="all"),k.value=!0,await x.dispatch("student/listAll",{params:{batch:n.uuid,status:t}}).then(h=>{R.students=h,k.value=!1}).catch(h=>{k.value=!1}),r.students=[]},D=()=>{R.students=[],s.students=[],r.students=[]};return L(async()=>{if(_.isEmpty(l.query)){r.isLoaded=!0;return}s.type=l.query.type,r.exam=l.query.exam,s.exam=l.query.exam,r.batch=l.query.batch,s.batch=l.query.batch,l.query.batch&&(await f({uuid:l.query.batch}),s.students=l.query.students?l.query.students.split(","):[]),s.showCourseWise=q(l.query.showCourseWise||""),s.showAllStudent=q(l.query.showAllStudent||""),s.showSummaryReport=q(l.query.showSummaryReport||""),s.sortSummaryReportByRank=q(l.query.sortSummaryReportByRank||""),s.cumulativeAssessment=q(l.query.cumulativeAssessment||""),r.isLoaded=!0}),(n,t)=>{const h=v("BaseSelect"),W=v("BaseSelectSearch"),V=v("BaseSwitch"),j=v("DatePicker"),E=v("BaseInput"),I=v("BaseFieldset"),z=v("FilterForm");return d(),U(z,{"init-form":S,multiple:["students"],form:s,onHide:t[27]||(t[27]=e=>T("hide"))},{default:m(()=>[r.isLoaded?(d(),w("div",X,[y("div",Y,[i(h,{modelValue:s.type,"onUpdate:modelValue":t[0]||(t[0]=e=>s.type=e),name:"type",label:n.$trans("exam.marksheet.type"),options:$.preRequisites.types,error:a(o).type,"onUpdate:error":t[1]||(t[1]=e=>a(o).type=e)},null,8,["modelValue","label","options","error"])]),s.type=="term_wise"?(d(),w("div",Z,[i(h,{modelValue:s.term,"onUpdate:modelValue":t[2]||(t[2]=e=>s.term=e),name:"term",label:n.$trans("exam.term.term"),options:$.preRequisites.terms,"value-prop":"uuid",error:a(o).term,"onUpdate:error":t[3]||(t[3]=e=>a(o).term=e)},{selectedOption:m(e=>{var b;return[g(u(e.value.name)+" ",1),y("span",P,"("+u(((b=e.value.division)==null?void 0:b.name)||n.$trans("general.all"))+")",1)]}),listOption:m(e=>{var b;return[g(u(e.option.name)+" ",1),y("span",ee,"("+u(((b=e.option.division)==null?void 0:b.name)||n.$trans("general.all"))+")",1)]}),_:1},8,["modelValue","label","options","error"])])):p("",!0),s.type=="exam_wise"||s.type=="exam_wise_default"||s.type=="exam_wise_credit_based"?(d(),w("div",te,[i(h,{modelValue:s.exam,"onUpdate:modelValue":t[4]||(t[4]=e=>s.exam=e),name:"exam",label:n.$trans("exam.exam"),"value-prop":"uuid",options:$.preRequisites.exams,error:a(o).exam,"onUpdate:error":t[5]||(t[5]=e=>a(o).exam=e)},{selectedOption:m(e=>{var b,B;return[g(u(e.value.name)+" ",1),e.value.term?(d(),w("span",se,"("+u(((B=(b=e.value.term)==null?void 0:b.division)==null?void 0:B.name)||n.$trans("general.all"))+")",1)):p("",!0)]}),listOption:m(e=>{var b,B;return[g(u(e.option.name)+" ",1),e.option.term?(d(),w("span",ae,"("+u(((B=(b=e.option.term)==null?void 0:b.division)==null?void 0:B.name)||n.$trans("general.all"))+")",1)):p("",!0)]}),_:1},8,["modelValue","label","options","error"])])):p("",!0),y("div",oe,[r.isLoaded?(d(),U(W,{key:0,name:"batch",label:n.$trans("global.select",{attribute:n.$trans("academic.batch.batch")}),modelValue:s.batch,"onUpdate:modelValue":t[6]||(t[6]=e=>s.batch=e),error:a(o).batch,"onUpdate:error":t[7]||(t[7]=e=>a(o).batch=e),"value-prop":"uuid","init-search":r.batch,"search-key":"course_batch","search-action":"academic/batch/list",onSelected:f,onRemoved:D},{selectedOption:m(e=>[g(u(e.value.course.name)+" "+u(e.value.name),1)]),listOption:m(e=>[g(u(e.option.course.nameWithTerm)+" "+u(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):p("",!0)]),y("div",ne,[s.batch?(d(),U(h,{key:0,multiple:"",name:"students",label:n.$trans("global.select",{attribute:n.$trans("student.student")}),options:R.students,modelValue:s.students,"onUpdate:modelValue":t[8]||(t[8]=e=>s.students=e),error:a(o).students,"onUpdate:error":t[9]||(t[9]=e=>a(o).students=e),"track-by":"name","value-prop":"uuid"},{selectedOption:m(e=>[g(u(e.value.name)+" ("+u(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:m(e=>[g(u(e.option.name)+" ("+u(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","options","modelValue","error"])):p("",!0)])])):p("",!0),a(J)(["student","guardian"],"any")?p("",!0):(d(),U(I,{key:1,class:"mt-4"},{legend:m(()=>[y("div",re,[g(u(n.$trans("global.show",{attribute:n.$trans("general.options")}))+" ",1),i(V,{reverse:"",modelValue:c.value,"onUpdate:modelValue":t[10]||(t[10]=e=>c.value=e),name:"showOptions"},null,8,["modelValue"])])]),default:m(()=>[c.value?(d(),w("div",le,[s.type=="exam_wise"||s.type=="exam_wise_default"?(d(),w("div",ue,[i(V,{vertical:"",modelValue:s.cumulativeAssessment,"onUpdate:modelValue":t[11]||(t[11]=e=>s.cumulativeAssessment=e),name:"showSno",label:n.$trans("exam.marksheet.cumulative_assessment"),error:a(o).cumulativeAssessment,"onUpdate:error":t[12]||(t[12]=e=>a(o).cumulativeAssessment=e)},null,8,["modelValue","label","error"])])):p("",!0),y("div",ie,[i(j,{modelValue:s.resultDate,"onUpdate:modelValue":t[13]||(t[13]=e=>s.resultDate=e),name:"resultDate",label:n.$trans("exam.result_date"),"no-clear":"",error:a(o).resultDate,"onUpdate:error":t[14]||(t[14]=e=>a(o).resultDate=e)},null,8,["modelValue","label","error"])]),y("div",me,[i(E,{type:"number",modelValue:s.column,"onUpdate:modelValue":t[15]||(t[15]=e=>s.column=e),name:"column",label:n.$trans("print.column"),error:a(o).column,"onUpdate:error":t[16]||(t[16]=e=>a(o).column=e)},null,8,["modelValue","label","error"])]),y("div",de,[i(E,{"leading-text":n.$trans("list.unit.mm"),type:"number",modelValue:s.marginTop,"onUpdate:modelValue":t[17]||(t[17]=e=>s.marginTop=e),name:"marginTop",label:n.$trans("print.margin_top"),error:a(o).marginTop,"onUpdate:error":t[18]||(t[18]=e=>a(o).marginTop=e)},null,8,["leading-text","modelValue","label","error"])]),y("div",pe,[i(V,{vertical:"",modelValue:s.showCourseWise,"onUpdate:modelValue":t[19]||(t[19]=e=>s.showCourseWise=e),name:"showCourseWise",label:n.$trans("exam.marksheet.course_wise"),error:a(o).showCourseWise,"onUpdate:error":t[20]||(t[20]=e=>a(o).showCourseWise=e)},null,8,["modelValue","label","error"])]),y("div",ce,[i(V,{vertical:"",modelValue:s.showAllStudent,"onUpdate:modelValue":t[21]||(t[21]=e=>s.showAllStudent=e),name:"showAllStudent",label:n.$trans("global.list_all",{attribute:n.$trans("student.student")}),error:a(o).showAllStudent,"onUpdate:error":t[22]||(t[22]=e=>a(o).showAllStudent=e)},null,8,["modelValue","label","error"])]),s.students.length==0?(d(),w(M,{key:1},[y("div",ye,[i(V,{vertical:"",modelValue:s.showSummaryReport,"onUpdate:modelValue":t[23]||(t[23]=e=>s.showSummaryReport=e),name:"showSummaryReport",label:n.$trans("exam.marksheet.show_summary_report"),error:a(o).showSummaryReport,"onUpdate:error":t[24]||(t[24]=e=>a(o).showSummaryReport=e)},null,8,["modelValue","label","error"])]),s.showSummaryReport?(d(),w("div",ve,[i(V,{vertical:"",modelValue:s.sortSummaryReportByRank,"onUpdate:modelValue":t[25]||(t[25]=e=>s.sortSummaryReportByRank=e),name:"sortSummaryReportByRank",label:n.$trans("exam.marksheet.sort_summary_report_by_rank"),error:a(o).sortSummaryReportByRank,"onUpdate:error":t[26]||(t[26]=e=>a(o).sortSummaryReportByRank=e)},null,8,["modelValue","label","error"])])):p("",!0)],64)):p("",!0)])):p("",!0)]),_:1}))]),_:1},8,["form"])}}},he={name:"ExamMarksheet"},ge=Object.assign(he,{setup($){const A=N(),l=K(),x=H();let T=[],C=[];const S="exam/marksheet/",k=O(!0),c=O(!1),o=F({types:[],terms:[],exams:[]}),s=async()=>{c.value=!0,await x.dispatch(S+"preRequisite").then(r=>{c.value=!1,Object.assign(o,r)}).catch(r=>{c.value=!1})},R=async()=>{c.value=!0,await x.dispatch(S+"fetchReport",{params:A.query}).then(r=>{c.value=!1,window.open("/print").document.write(r)}).catch(r=>{c.value=!1})};return L(async()=>{await s()}),(r,f)=>{const D=v("BaseButton"),n=v("PageHeaderAction"),t=v("PageHeader"),h=v("ParentTransition"),W=v("BaseCard");return d(),w(M,null,[i(t,{title:r.$trans(a(A).meta.label),navs:[{label:r.$trans("exam.exam"),path:"Exam"}]},{default:m(()=>[i(n,{name:"ExamMarksheet",title:r.$trans("exam.marksheet.marksheet"),actions:a(T),"dropdown-actions":a(C),onToggleFilter:f[1]||(f[1]=V=>k.value=!k.value)},{default:m(()=>[a(Q)("exam-marksheet:access")?(d(),U(D,{key:0,design:"white",onClick:f[0]||(f[0]=V=>a(l).push({name:"ExamMarksheetPrint"}))},{default:m(()=>[g(u(r.$trans("global.print",{attribute:r.$trans("exam.marksheet.marksheet")})),1)]),_:1})):p("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),i(h,{appear:"",visibility:k.value},{default:m(()=>[i(be,{"is-loading":c.value,onAfterFilter:R,"init-url":S,"pre-requisites":o,onHide:f[2]||(f[2]=V=>k.value=!1)},null,8,["is-loading","pre-requisites"])]),_:1},8,["visibility"]),i(h,{appear:"",visibility:!0},{default:m(()=>[i(W,{"no-padding":"","no-content-padding":"","is-loading":c.value},null,8,["is-loading"])]),_:1})],64)}}});export{ge as default};
