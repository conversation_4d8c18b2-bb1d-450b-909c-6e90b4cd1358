import{u as V,G as g,H,l as D,r as d,q as T,o as U,w as f,d as i,e as r,f as a,J as j,a as O,F as q}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},M={class:"col-span-2 sm:col-span-1"},w={class:"col-span-2 sm:col-span-1"},I={class:"col-span-3 sm:col-span-2"},L={class:"grid grid-cols-1"},C={class:"col"},N={name:"EmployeeDocumentForm"},G=Object.assign(N,{setup(p){const m=V(),l={type:"",title:"",description:"",startDate:"",endDate:"",media:[],mediaUpdated:!1,mediaToken:g(),mediaHash:[]},b="employee/document/",s=H(b),u=D({types:[]}),o=D({...l}),_=D({type:"",isLoaded:!m.params.muuid}),$=n=>{Object.assign(u,n)},k=()=>{o.mediaToken=g(),o.mediaHash=[]},B=n=>{var e,c,y;Object.assign(l,{...n,type:(e=n.type)==null?void 0:e.uuid,startDate:((c=n.startDate)==null?void 0:c.value)||"",endDate:((y=n.endDate)==null?void 0:y.value)||""}),Object.assign(o,j(l)),_.type=n.type.name,_.isLoaded=!0};return(n,e)=>{const c=d("BaseSelect"),y=d("BaseInput"),v=d("DatePicker"),E=d("BaseTextarea"),F=d("MediaUpload"),P=d("FormAction");return U(),T(P,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:$,"init-url":b,uuid:a(m).params.uuid,"module-uuid":a(m).params.muuid,"init-form":l,form:o,"set-form":B,redirect:{name:"EmployeeDocument",params:{uuid:a(m).params.uuid}},onResetMediaFiles:k},{default:f(()=>[i("div",A,[i("div",R,[r(c,{modelValue:o.type,"onUpdate:modelValue":e[0]||(e[0]=t=>o.type=t),name:"type",label:n.$trans("employee.document_type.document_type"),"label-prop":"name","value-prop":"uuid",options:u.types,error:a(s).type,"onUpdate:error":e[1]||(e[1]=t=>a(s).type=t)},null,8,["modelValue","label","options","error"])]),i("div",S,[r(y,{type:"text",modelValue:o.title,"onUpdate:modelValue":e[2]||(e[2]=t=>o.title=t),name:"title",label:n.$trans("employee.document.props.title"),error:a(s).title,"onUpdate:error":e[3]||(e[3]=t=>a(s).title=t),autofocus:""},null,8,["modelValue","label","error"])]),i("div",M,[r(v,{modelValue:o.startDate,"onUpdate:modelValue":e[4]||(e[4]=t=>o.startDate=t),name:"startDate",label:n.$trans("employee.document.props.start_date"),error:a(s).startDate,"onUpdate:error":e[5]||(e[5]=t=>a(s).startDate=t)},null,8,["modelValue","label","error"])]),i("div",w,[r(v,{modelValue:o.endDate,"onUpdate:modelValue":e[6]||(e[6]=t=>o.endDate=t),name:"endDate",label:n.$trans("employee.document.props.end_date"),error:a(s).endDate,"onUpdate:error":e[7]||(e[7]=t=>a(s).endDate=t)},null,8,["modelValue","label","error"])]),i("div",I,[r(E,{modelValue:o.description,"onUpdate:modelValue":e[8]||(e[8]=t=>o.description=t),name:"description",label:n.$trans("employee.document.props.description"),error:a(s).description,"onUpdate:error":e[9]||(e[9]=t=>a(s).description=t)},null,8,["modelValue","label","error"])])]),i("div",L,[i("div",C,[r(F,{multiple:"",label:n.$trans("general.file"),module:"document",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[10]||(e[10]=t=>o.mediaUpdated=!0),onSetHash:e[11]||(e[11]=t=>o.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),J={name:"EmployeeDocumentAction"},K=Object.assign(J,{props:{employee:{type:Object,default(){return{}}}},setup(p){const m=V();return(l,b)=>{const s=d("PageHeaderAction"),u=d("PageHeader"),o=d("ParentTransition");return U(),O(q,null,[r(u,{title:l.$trans(a(m).meta.trans,{attribute:l.$trans(a(m).meta.label)}),navs:[{label:l.$trans("employee.employee"),path:"EmployeeList"},{label:p.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:p.employee.uuid}}},{label:l.$trans("employee.document.document"),path:{name:"EmployeeDocument",params:{uuid:p.employee.uuid}}}]},{default:f(()=>[r(s,{name:"EmployeeDocument",title:l.$trans("employee.document.document"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(o,{appear:"",visibility:!0},{default:f(()=>[r(G)]),_:1})],64)}}});export{K as default};
