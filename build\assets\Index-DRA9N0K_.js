import{u as W,i as G,m as D,H as K,l as S,n as P,r as h,q,o as i,w as s,d as k,b as $,f as d,s as v,a as x,t as r,e as c,h as te,j as ae,F as H,v as I,<PERSON>,<PERSON> as se}from"./app-BAwPsakn.js";const ne={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},re={key:0,class:"ml-1"},le={key:0,class:"ml-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},ce={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide","cancel"],setup(M,{emit:y}){const p=W(),F=G(),l=y,j=M,B={exam:"",attempt:"first",batch:"",subject:""},V=D(!1),n=K(j.initUrl),o=S({...B}),g=S({exams:j.preRequisites.exams,subjects:[]}),b=S({exam:"",batch:"",subject:"",isLoaded:!(p.query.exam&&p.query.batch&&p.query.subject)}),O=()=>{l("cancel")},N=async m=>{if(!m){o.batch="",o.subject="",g.subjects=[];return}o.batch=m||"",g.subjects=[],o.subject="",V.value=!0,await F.dispatch("academic/batch/listSubjects",{uuid:m||""}).then(a=>{g.subjects=a,V.value=!1}).catch(a=>{V.value=!1})};return P(async()=>{b.exam=p.query.exam,o.exam=p.query.exam,b.batch=p.query.batch,o.batch=p.query.batch,p.query.batch&&(await N(p.query.batch),b.subject=p.query.subject,o.subject=p.query.subject),b.isLoaded=!0}),(m,a)=>{const A=h("BaseSelect"),t=h("BaseSelectSearch"),f=h("FilterForm");return i(),q(f,{"init-form":B,form:o,onCancel:O,onHide:a[8]||(a[8]=e=>l("hide"))},{default:s(()=>[k("div",ne,[k("div",oe,[b.isLoaded?(i(),q(A,{key:0,modelValue:o.exam,"onUpdate:modelValue":a[0]||(a[0]=e=>o.exam=e),name:"exam",label:m.$trans("exam.exam"),"value-prop":"uuid",options:M.preRequisites.exams,error:d(n).exam,"onUpdate:error":a[1]||(a[1]=e=>d(n).exam=e)},{selectedOption:s(e=>{var w,C;return[v(r(e.value.name)+" ",1),e.value.term?(i(),x("span",re,"("+r(((C=(w=e.value.term)==null?void 0:w.division)==null?void 0:C.name)||m.$trans("general.all"))+")",1)):$("",!0)]}),listOption:s(e=>{var w,C;return[v(r(e.option.name)+" ",1),e.option.term?(i(),x("span",le,"("+r(((C=(w=e.option.term)==null?void 0:w.division)==null?void 0:C.name)||m.$trans("general.all"))+")",1)):$("",!0)]}),_:1},8,["modelValue","label","options","error"])):$("",!0)]),k("div",ie,[b.isLoaded?(i(),q(A,{key:0,modelValue:o.attempt,"onUpdate:modelValue":a[2]||(a[2]=e=>o.attempt=e),name:"attempt",label:m.$trans("exam.schedule.props.attempt"),options:M.preRequisites.attempts,error:d(n).attempt,"onUpdate:error":a[3]||(a[3]=e=>d(n).attempt=e)},null,8,["modelValue","label","options","error"])):$("",!0)]),k("div",me,[b.isLoaded?(i(),q(t,{key:0,name:"batch",label:m.$trans("global.select",{attribute:m.$trans("academic.batch.batch")}),modelValue:o.batch,"onUpdate:modelValue":a[4]||(a[4]=e=>o.batch=e),error:d(n).batch,"onUpdate:error":a[5]||(a[5]=e=>d(n).batch=e),"value-prop":"uuid","init-search":b.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:N},{selectedOption:s(e=>[v(r(e.value.course.name)+" "+r(e.value.name),1)]),listOption:s(e=>[v(r(e.option.course.nameWithTerm)+" "+r(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)]),k("div",ue,[c(A,{modelValue:o.subject,"onUpdate:modelValue":a[6]||(a[6]=e=>o.subject=e),name:"subject",label:m.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:g.subjects,error:d(n).subject,"onUpdate:error":a[7]||(a[7]=e=>d(n).subject=e)},null,8,["modelValue","label","options","error"])])])]),_:1},8,["form"])}}},de={class:"p-2 space-y-2"},pe={class:"divide-y divide-gray-200 dark:divide-gray-700"},be={class:"col-span-4 sm:col-span-1"},fe={class:"mt-1"},he={key:0,class:"col-span-4 sm:col-span-3"},ve={class:"flex flex-col sm:flex-row gap-2"},ye={key:0,class:"mt-2"},ge={name:"ExamMark"},_e=Object.assign(ge,{setup(M){const y=W(),p=te(),F=G();ae("emitter");const l={exam:"",batch:"",subject:"",students:[],attempt:"first"},j="exam/mark/",B=K(j),V=D(!1),n=D(!1),o=S({attempts:[],exams:[]}),g=S({...l}),b=S({meta:{},showComment:!0}),O=async()=>{n.value=!0,await F.dispatch(j+"preRequisite").then(t=>{n.value=!1,Object.assign(o,t)}).catch(t=>{n.value=!1})},N=()=>{l.exam="",l.batch="",l.subject="",l.students=[],l.attempt="first",Object.assign(g,J(l))},m=async()=>{!y.query.batch||!y.query.subject||(n.value=!0,await F.dispatch(j+"fetch",{params:y.query}).then(t=>{n.value=!1,l.exam=y.query.exam,l.batch=y.query.batch,l.subject=y.query.subject,l.students=t.data,l.attempt=y.query.attempt,b.meta=t.meta,Object.assign(g,J(l))}).catch(t=>{n.value=!1}))},a=async()=>{await se()&&(n.value=!0,await F.dispatch(j+"remove",{form:y.query}).then(t=>{n.value=!1,b.meta.marksheetStatus="pending",m()}).catch(t=>{n.value=!1}))},A=t=>{b.meta.marksheetStatus="pending"};return P(async()=>{await O(),await m()}),(t,f)=>{const e=h("BaseButton"),w=h("PageHeaderAction"),C=h("PageHeader"),R=h("BaseAlert"),Q=h("BaseImport"),T=h("ParentTransition"),X=h("BaseCheckbox"),Y=h("BaseDataView"),z=h("BaseInput"),Z=h("FormAction"),ee=h("BaseCard");return i(),x(H,null,[c(C,{title:t.$trans(d(y).meta.label),navs:[{label:t.$trans("exam.exam"),path:"Exam"}]},{default:s(()=>[c(w,null,{default:s(()=>[c(e,{design:"white",onClick:f[0]||(f[0]=u=>d(p).push({name:"ExamObservationMark"}))},{default:s(()=>[v(r(t.$trans("exam.observation_mark")),1)]),_:1}),c(e,{design:"white",onClick:f[1]||(f[1]=u=>d(p).push({name:"ExamComment"}))},{default:s(()=>[v(r(t.$trans("exam.comment")),1)]),_:1}),c(e,{design:"white",onClick:f[2]||(f[2]=u=>d(p).push({name:"ExamAttendance"}))},{default:s(()=>[v(r(t.$trans("student.attendance.attendance")),1)]),_:1}),c(e,{design:"white",onClick:f[3]||(f[3]=u=>V.value=!V.value)},{default:s(()=>[v(r(t.$trans("general.import")),1)]),_:1})]),_:1})]),_:1},8,["title","navs"]),c(T,{appear:"",visibility:V.value},{default:s(()=>[c(Q,{path:"exam/mark/import","additional-option":{},"has-validation":!1,onCancelled:f[4]||(f[4]=u=>V.value=!1),onHide:f[5]||(f[5]=u=>V.value=!1),onCompleted:m},{header:s(()=>[c(R,{size:"xs",design:"info"},{default:s(()=>[v(r(t.$trans("general.import_info")),1)]),_:1})]),_:1})]),_:1},8,["visibility"]),c(T,{appear:"",visibility:!0},{default:s(()=>[c(ce,{onAfterFilter:m,onCancel:N,"init-url":j,"pre-requisites":o},null,8,["pre-requisites"])]),_:1}),c(ee,{"no-padding":"","no-content-padding":"","is-loading":n.value},{title:s(()=>[v(r(t.$trans("exam.record")),1)]),action:s(()=>[b.meta.markRecorded?(i(),q(e,{key:0,design:"error",onClick:a},{default:s(()=>[v(r(t.$trans("global.remove",{attribute:t.$trans("exam.assessment.props.mark")})),1)]),_:1})):$("",!0)]),default:s(()=>[k("div",de,[g.students.length==0?(i(),q(R,{key:0,size:"xs",design:"error"},{default:s(()=>[v(r(t.$trans("general.errors.record_not_found")),1)]),_:1})):$("",!0),b.meta.marksheetStatus=="processed"?(i(),q(R,{key:1,size:"xs",design:"info"},{default:s(()=>[v(r(t.$trans("exam.schedule.marksheet_processed_info")),1)]),_:1})):$("",!0)]),g.students.length?(i(),q(Z,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":j,action:"store","init-form":l,form:g,"after-submit":A},{default:s(()=>[k("div",pe,[(i(!0),x(H,null,I(g.students,(u,U)=>(i(),x("div",{class:"grid grid-cols-4 gap-6 px-4 py-2",key:u.uuid},[k("div",be,[c(Y,null,{default:s(()=>[v(r(u.name)+" ("+r(u.rollNumber||u.codeNumber)+") ",1),k("div",fe,[c(X,{modelValue:u.isNotApplicable,"onUpdate:modelValue":_=>u.isNotApplicable=_,name:`students.${U}.isNotApplicable`,label:t.$trans("global.is_not",{attribute:t.$trans("exam.schedule.props.applicable")})},null,8,["modelValue","onUpdate:modelValue","name","label"])])]),_:2},1024)]),u.isNotApplicable?$("",!0):(i(),x("div",he,[k("div",ve,[(i(!0),x(H,null,I(u.marks,(_,E)=>(i(),q(z,{type:"text",modelValue:_.obtainedMark,"onUpdate:modelValue":L=>_.obtainedMark=L,name:`students.${U}.marks.${E}.obtainedMark`,label:_.name+" ("+_.maxMark+")",error:d(B)[`students.${U}.marks.${E}.obtainedMark`],"onUpdate:error":L=>d(B)[`students.${U}.marks.${E}.obtainedMark`]=L},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"]))),256))]),b.showComment?(i(),x("div",ye,[c(z,{type:"text",modelValue:u.comment,"onUpdate:modelValue":_=>u.comment=_,name:`students.${U}.comment`,placeholder:t.$trans("exam.comment"),error:d(B)[`students.${U}.comment`],"onUpdate:error":_=>d(B)[`students.${U}.comment`]=_},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])):$("",!0)]))]))),128))])]),_:1},8,["form"])):$("",!0)]),_:1},8,["is-loading"])],64)}}});export{_e as default};
