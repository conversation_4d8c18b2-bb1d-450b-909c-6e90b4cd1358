import{u as F,G as B,H as I,l as D,r as l,q as k,o as $,w as g,d as a,b as R,e as i,f as r,I as j,J as O,a as _,F as L}from"./app-BAwPsakn.js";const M={class:"grid grid-cols-3 gap-6"},S={class:"col-span-3 sm:col-span-1"},w={class:"col-span-2 sm:col-span-2"},C={class:"col-span-2 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"mt-4 grid grid-cols-1 gap-4"},W={class:"col"},X={class:"col"},Y={class:"col"},Z={name:"ActivityTripForm"},x=Object.assign(Z,{setup(P){const m=F(),d={type:"",title:"",startDate:"",startTime:"",endDate:"",endTime:"",fee:"",studentAudienceType:"",employeeAudienceType:"",studentAudiences:[],employeeAudiences:[],venue:"",summary:"",itinerary:"",description:"",media:[],mediaUpdated:!1,mediaToken:B(),mediaHash:[]},U="activity/trip/",n=I(U),y=D({types:[],studentAudienceTypes:[],employeeAudienceTypes:[]}),s=D({...d}),c=D({studentAudiences:[],employeeAudiences:[],isLoaded:!m.params.uuid}),H=o=>{Object.assign(y,o)},E=()=>{s.mediaToken=B(),s.mediaHash=[]},q=o=>{var u,p,A,T,f,b,V;let t=o.audiences.filter(e=>e.type=="student").map(e=>e.uuid),v=o.audiences.filter(e=>e.type=="employee").map(e=>e.uuid);Object.assign(d,{...o,type:((u=o.type)==null?void 0:u.uuid)||"",startDate:o.startDate.value,startTime:((p=o.startTime)==null?void 0:p.at)||"",endDate:((A=o.endDate)==null?void 0:A.value)||"",endTime:((T=o.endTime)==null?void 0:T.at)||"",studentAudienceType:((f=o.studentAudienceType)==null?void 0:f.value)||"",employeeAudienceType:((b=o.employeeAudienceType)==null?void 0:b.value)||"",fee:((V=o.fee)==null?void 0:V.value)||"",studentAudiences:t,employeeAudiences:v}),Object.assign(s,O(d)),c.studentAudiences=t,c.employeeAudiences=v,c.isLoaded=!0};return(o,t)=>{const v=l("BaseSelect"),u=l("BaseInput"),p=l("DatePicker"),A=l("AudienceInput"),T=l("BaseTextarea"),f=l("BaseEditor"),b=l("MediaUpload"),V=l("FormAction");return $(),k(V,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":U,"init-form":d,form:s,"set-form":q,redirect:"ActivityTrip",onResetMediaFiles:E},{default:g(()=>[a("div",M,[a("div",S,[i(v,{modelValue:s.type,"onUpdate:modelValue":t[0]||(t[0]=e=>s.type=e),name:"type",label:o.$trans("activity.trip.props.type"),options:y.types,"label-prop":"name","value-prop":"uuid",error:r(n).type,"onUpdate:error":t[1]||(t[1]=e=>r(n).type=e)},null,8,["modelValue","label","options","error"])]),a("div",w,[i(u,{type:"text",modelValue:s.title,"onUpdate:modelValue":t[2]||(t[2]=e=>s.title=e),name:"title",label:o.$trans("activity.trip.props.title"),error:r(n).title,"onUpdate:error":t[3]||(t[3]=e=>r(n).title=e),autofocus:""},null,8,["modelValue","label","error"])]),a("div",C,[i(u,{type:"text",modelValue:s.venue,"onUpdate:modelValue":t[4]||(t[4]=e=>s.venue=e),name:"venue",label:o.$trans("activity.trip.props.venue"),error:r(n).venue,"onUpdate:error":t[5]||(t[5]=e=>r(n).venue=e),autofocus:""},null,8,["modelValue","label","error"])]),a("div",N,[i(p,{as:"date",modelValue:s.startDate,"onUpdate:modelValue":t[6]||(t[6]=e=>s.startDate=e),name:"startDate",label:o.$trans("activity.trip.props.start_date"),"no-clear":"",error:r(n).startDate,"onUpdate:error":t[7]||(t[7]=e=>r(n).startDate=e)},null,8,["modelValue","label","error"])]),a("div",G,[i(p,{as:"time",modelValue:s.startTime,"onUpdate:modelValue":t[8]||(t[8]=e=>s.startTime=e),name:"startTime",label:o.$trans("activity.trip.props.start_time"),error:r(n).startTime,"onUpdate:error":t[9]||(t[9]=e=>r(n).startTime=e)},null,8,["modelValue","label","error"])]),a("div",J,[i(p,{as:"date",modelValue:s.endDate,"onUpdate:modelValue":t[10]||(t[10]=e=>s.endDate=e),name:"endDate",label:o.$trans("activity.trip.props.end_date"),error:r(n).endDate,"onUpdate:error":t[11]||(t[11]=e=>r(n).endDate=e)},null,8,["modelValue","label","error"])]),a("div",z,[i(p,{as:"time",modelValue:s.endTime,"onUpdate:modelValue":t[12]||(t[12]=e=>s.endTime=e),name:"endTime",label:o.$trans("activity.trip.props.end_time"),error:r(n).endTime,"onUpdate:error":t[13]||(t[13]=e=>r(n).endTime=e)},null,8,["modelValue","label","error"])]),a("div",K,[i(u,{currency:"",modelValue:s.fee,"onUpdate:modelValue":t[14]||(t[14]=e=>s.fee=e),name:"fee",label:o.$trans("activity.trip.props.fee"),error:r(n).fee,"onUpdate:error":t[15]||(t[15]=e=>r(n).fee=e)},null,8,["modelValue","label","error"])])]),c.isLoaded?($(),k(A,{key:0,"pre-requisites":y,studentAudienceType:s.studentAudienceType,"onUpdate:studentAudienceType":t[16]||(t[16]=e=>s.studentAudienceType=e),employeeAudienceType:s.employeeAudienceType,"onUpdate:employeeAudienceType":t[17]||(t[17]=e=>s.employeeAudienceType=e),studentAudiences:s.studentAudiences,"onUpdate:studentAudiences":t[18]||(t[18]=e=>s.studentAudiences=e),employeeAudiences:s.employeeAudiences,"onUpdate:employeeAudiences":t[19]||(t[19]=e=>s.employeeAudiences=e),formErrors:r(n),"onUpdate:formErrors":t[20]||(t[20]=e=>j(n)?n.value=e:null)},null,8,["pre-requisites","studentAudienceType","employeeAudienceType","studentAudiences","employeeAudiences","formErrors"])):R("",!0),a("div",Q,[a("div",W,[i(T,{rows:2,modelValue:s.summary,"onUpdate:modelValue":t[21]||(t[21]=e=>s.summary=e),name:"summary",label:o.$trans("activity.trip.props.summary"),error:r(n).summary,"onUpdate:error":t[22]||(t[22]=e=>r(n).summary=e)},null,8,["modelValue","label","error"])]),a("div",X,[i(f,{modelValue:s.description,"onUpdate:modelValue":t[23]||(t[23]=e=>s.description=e),name:"description",edit:!!r(m).params.uuid,label:o.$trans("activity.trip.props.description"),error:r(n).description,"onUpdate:error":t[24]||(t[24]=e=>r(n).description=e)},null,8,["modelValue","edit","label","error"])]),a("div",Y,[i(b,{multiple:"",label:o.$trans("general.file"),module:"trip",media:s.media,"media-token":s.mediaToken,onIsUpdated:t[25]||(t[25]=e=>s.mediaUpdated=!0),onSetHash:t[26]||(t[26]=e=>s.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),h={name:"ActivityTripAction"},te=Object.assign(h,{setup(P){const m=F();return(d,U)=>{const n=l("PageHeaderAction"),y=l("PageHeader"),s=l("ParentTransition");return $(),_(L,null,[i(y,{title:d.$trans(r(m).meta.trans,{attribute:d.$trans(r(m).meta.label)}),navs:[{label:d.$trans("activity.activity"),path:"Activity"},{label:d.$trans("activity.trip.trip"),path:"ActivityTripList"}]},{default:g(()=>[i(n,{name:"ActivityTrip",title:d.$trans("activity.trip.trip"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(s,{appear:"",visibility:!0},{default:g(()=>[i(x)]),_:1})],64)}}});export{te as default};
