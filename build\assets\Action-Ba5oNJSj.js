import{u as G,l as q,H as x,r as V,z as aa,q as f,o as c,w as $,d as p,b as U,e as i,f as t,a as H,A as w,F as R,s as C,t as k,v as D,x as N,G as z,J as L}from"./app-BAwPsakn.js";import{d as ea}from"./vuedraggable.umd-BRYqknf6.js";const oa={class:"grid grid-cols-2 gap-6"},la={class:"col-span-2 sm:col-span-1"},ra={class:"col-span-2 sm:col-span-1"},sa={class:"col-span-2"},ta={class:"col-span-2 sm:col-span-1"},na={class:"flex items-center justify-between"},pa={class:"ml-2"},ia=["onClick"],da={class:"grid grid-cols-4 gap-3"},ca={class:"col-span-4 sm:col-span-1"},ua={class:"col-span-4 sm:col-span-1"},ma={class:"space-x-2"},ya=["onClick"],_a={key:0},$a={key:1},Va={class:"col-span-4 sm:col-span-1"},va={class:"col-span-4 sm:col-span-1"},ha={class:"mt-4"},ga={key:0,class:"mt-2 space-y-4"},ba=["onClick"],fa={class:"col-span-4 sm:col-span-1"},Ua={class:"col-span-4 sm:col-span-1"},Fa={class:"col-span-4 sm:col-span-1"},Ha={class:"col-span-4 sm:col-span-1"},Ta={class:"flex items-end align-baseline"},ka={class:"flex-1"},Pa=["onClick"],Ca={class:"flex justify-between mt-2"},Ba={class:"mt-2"},Oa={class:"mt-4"},Ea={name:"EmployeePayrollSalaryTemplateForm"},Ra=Object.assign(Ea,{setup(J){G();const g={name:"",alias:"",hasHourlyPayroll:!1,records:[],description:""},b="employee/payroll/salaryTemplate/",h=q({attendanceTypes:[],payHeadTypes:[],payHeads:[],variables:[],comparisonOperators:[],logicalOperators:[]}),n=x(b),m=q({...g}),j=e=>h.variables.filter(o=>o.value!==e.code),K=e=>{e.conditionalFormulas||(e.conditionalFormulas=[]),e.conditionalFormulas.push({conditions:[{referencePayHead:null,operator:">",value:0,logicalOperator:"AND"}],formula:""})},M=(e,o)=>{e.conditionalFormulas.splice(o,1)},Q=e=>{e.conditions.push({referencePayHead:null,operator:">",value:0,logicalOperator:"AND"})},W=(e,o)=>{e.conditions.splice(o,1)},X=e=>{Object.assign(h,e),h.payHeads.forEach((o,v)=>{g.records.push({uuid:z(),payHead:o,attendanceType:"",type:"",position:v,computation:"",hasRange:!1,hasCondition:!1,conditionalFormulas:[],minValue:"",maxValue:"",asTotal:!1})}),Object.assign(m,L(g))},Y=e=>{e.asTotal=!e.asTotal},Z=e=>{let o=[];h.payHeads.forEach((v,A)=>{var O,F,E,B;let l=e.records.find(T=>T.payHead.uuid==v.uuid);o.push({uuid:(l==null?void 0:l.uuid)||z(),payHead:v,attendanceType:(l==null?void 0:l.attendanceType)||"",type:((O=l==null?void 0:l.type)==null?void 0:O.value)||"",position:A,computation:(l==null?void 0:l.computation)||"",hasCondition:(l==null?void 0:l.hasCondition)||!1,hasRange:(l==null?void 0:l.hasRange)||!1,conditionalFormulas:((F=l==null?void 0:l.conditionalFormulas)==null?void 0:F.map(T=>({conditions:T.conditions.map(P=>({referencePayHead:P.referencePayHead,operator:P.operator,value:P.value,logicalOperator:P.logicalOperator})),formula:T.formula})))||[],minValue:((E=l==null?void 0:l.minValue)==null?void 0:E.value)||0,maxValue:((B=l==null?void 0:l.maxValue)==null?void 0:B.value)||0,asTotal:(l==null?void 0:l.asTotal)||!1})}),Object.assign(g,{...e,records:o}),Object.assign(m,L(g))};return(e,o)=>{const v=V("BaseInput"),A=V("BaseTextarea"),l=V("BaseSwitch"),O=V("BaseLabel"),F=V("BaseSelect"),E=V("HelperText"),B=V("BaseButton"),T=V("BaseFieldset"),P=V("BaseAlert"),I=V("FormAction"),S=aa("tooltip");return c(),f(I,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:X,"init-url":b,"init-form":g,form:m,"set-form":Z,redirect:"EmployeePayrollSalaryTemplate"},{default:$(()=>[p("div",oa,[p("div",la,[i(v,{type:"text",modelValue:m.name,"onUpdate:modelValue":o[0]||(o[0]=a=>m.name=a),name:"name",label:e.$trans("employee.payroll.salary_template.props.name"),error:t(n).name,"onUpdate:error":o[1]||(o[1]=a=>t(n).name=a),autofocus:""},null,8,["modelValue","label","error"])]),p("div",ra,[i(v,{type:"text",modelValue:m.alias,"onUpdate:modelValue":o[2]||(o[2]=a=>m.alias=a),name:"alias",label:e.$trans("employee.payroll.salary_template.props.alias"),error:t(n).alias,"onUpdate:error":o[3]||(o[3]=a=>t(n).alias=a),autofocus:""},null,8,["modelValue","label","error"])]),p("div",sa,[i(A,{modelValue:m.description,"onUpdate:modelValue":o[4]||(o[4]=a=>m.description=a),name:"description",label:e.$trans("employee.payroll.salary_template.props.description"),error:t(n).description,"onUpdate:error":o[5]||(o[5]=a=>t(n).description=a)},null,8,["modelValue","label","error"])]),p("div",ta,[i(l,{vertical:"",modelValue:m.hasHourlyPayroll,"onUpdate:modelValue":o[6]||(o[6]=a=>m.hasHourlyPayroll=a),name:"hasHourlyPayroll",label:e.$trans("employee.payroll.salary_template.props.hourly_payroll"),error:t(n).hasHourlyPayroll,"onUpdate:error":o[7]||(o[7]=a=>t(n).hasHourlyPayroll=a)},null,8,["modelValue","label","error"])])]),m.hasHourlyPayroll?U("",!0):(c(),f(t(ea),{key:0,list:m.records,"item-key":"uuid",handle:".handle"},{item:$(({element:a,index:r})=>[i(T,{class:"mt-4"},{legend:$(()=>[p("div",na,[o[8]||(o[8]=p("i",{class:"fas fa-arrows mr-2 cursor-pointer handle"},null,-1)),i(O,{class:N({"text-success":a.payHead.category.value=="earning","text-danger":a.payHead.category.value=="deduction","text-info":a.payHead.category.value=="employee_contribution"})},{default:$(()=>[C(k(a.payHead.name)+" ("+k(a.payHead.code)+") ",1)]),_:2},1032,["class"]),p("span",pa,[w(p("i",{class:N(["fas cursor-pointer",{"fa-equals":!a.asTotal,"fa-not-equal":a.asTotal}]),onClick:s=>Y(a)},null,10,ia),[[S,a.asTotal?e.$trans("payroll.salary_template.props.as_total"):e.$trans("payroll.salary_template.props.as_component")]])])])]),default:$(()=>[p("div",da,[p("div",ca,[i(F,{modelValue:a.type,"onUpdate:modelValue":s=>a.type=s,name:`records.${r}.type`,label:e.$trans("employee.payroll.salary_template.props.type"),options:h.payHeadTypes,error:t(n)[`records.${r}.type`],"onUpdate:error":s=>t(n)[`records.${r}.type`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error"])]),p("div",ua,[a.type=="computation"?(c(),f(v,{key:0,type:"text",modelValue:a.computation,"onUpdate:modelValue":s=>a.computation=s,name:`records.${r}.computation`,label:e.$trans("employee.payroll.salary_template.props.computation"),error:t(n)[`records.${r}.computation`],"onUpdate:error":s=>t(n)[`records.${r}.computation`]=s},{"additional-label":$(()=>[p("div",ma,[p("span",{class:"cursor-pointer",onClick:s=>a.hasRange=!a.hasRange},[a.hasRange?w((c(),H("span",$a,o[10]||(o[10]=[p("i",{class:"fas fa-times-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[S,e.$trans("employee.payroll.salary_template.props.doesnt_have_range")]]):w((c(),H("span",_a,o[9]||(o[9]=[p("i",{class:"fas fa-check-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[S,e.$trans("employee.payroll.salary_template.props.has_range")]])],8,ya)])]),_:2},1032,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):U("",!0),a.type=="production_based"?(c(),f(F,{key:1,modelValue:a.attendanceType,"onUpdate:modelValue":s=>a.attendanceType=s,name:`records.${r}.attendanceType`,label:e.$trans("employee.attendance.type.type"),options:h.attendanceTypes,"label-prop":"name","value-prop":"uuid","object-prop":!0,error:t(n)[`records.${r}.attendanceType`],"onUpdate:error":s=>t(n)[`records.${r}.attendanceType`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error"])):U("",!0)]),p("div",Va,[a.type=="computation"&&a.hasRange?(c(),f(v,{key:0,currency:"",modelValue:a.minValue,"onUpdate:modelValue":s=>a.minValue=s,name:`records.${r}.minValue`,label:e.$trans("employee.payroll.salary_template.props.min_value"),error:t(n)[`records.${r}.minValue`],"onUpdate:error":s=>t(n)[`records.${r}.minValue`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):U("",!0)]),p("div",va,[a.type=="computation"&&a.hasRange?(c(),f(v,{key:0,currency:"",modelValue:a.maxValue,"onUpdate:modelValue":s=>a.maxValue=s,name:`records.${r}.maxValue`,label:e.$trans("employee.payroll.salary_template.props.max_value"),error:t(n)[`records.${r}.maxValue`],"onUpdate:error":s=>t(n)[`records.${r}.maxValue`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):U("",!0)])]),a.type=="computation"?(c(),H(R,{key:0},[p("div",ha,[i(l,{reverse:"",modelValue:a.hasCondition,"onUpdate:modelValue":s=>a.hasCondition=s,name:`records.${r}.hasCondition`},null,8,["modelValue","onUpdate:modelValue","name"]),i(E,null,{default:$(()=>[C(k(e.$trans("employee.payroll.salary_template.conditional_pay_head_info")),1)]),_:1})]),a.hasCondition?(c(),H("div",ga,[(c(!0),H(R,null,D(a.conditionalFormulas,(s,d)=>(c(),f(T,{key:d},{legend:$(()=>[C(k(e.$trans("employee.payroll.pay_head.pay_head",{index:d+1}))+" ",1),p("i",{class:"ml-2 fas fa-times-circle text-danger cursor-pointer",onClick:_=>M(a,d)},null,8,ba)]),default:$(()=>[(c(!0),H(R,null,D(s.conditions,(_,y)=>(c(),H("div",{key:y,class:"grid grid-cols-4 gap-2 mb-2"},[p("div",fa,[i(F,{modelValue:_.referencePayHead,"onUpdate:modelValue":u=>_.referencePayHead=u,name:`records.${r}.conditionalFormulas.${d}.conditions.${y}.referencePayHead`,placeholder:e.$trans("employee.payroll.pay_head.pay_head"),options:j(a.payHead),"label-prop":"label","value-prop":"value","object-prop":!1,error:t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.referencePayHead`],"onUpdate:error":u=>t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.referencePayHead`]=u},null,8,["modelValue","onUpdate:modelValue","name","placeholder","options","error","onUpdate:error"])]),p("div",Ua,[i(F,{modelValue:_.operator,"onUpdate:modelValue":u=>_.operator=u,name:`records.${r}.conditionalFormulas.${d}.conditions.${y}.operator`,placeholder:e.$trans("employee.payroll.salary_template.props.operator"),options:h.comparisonOperators,error:t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.operator`],"onUpdate:error":u=>t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.operator`]=u},null,8,["modelValue","onUpdate:modelValue","name","placeholder","options","error","onUpdate:error"])]),p("div",Fa,[i(v,{type:"number",modelValue:_.value,"onUpdate:modelValue":u=>_.value=u,name:`records.${r}.conditionalFormulas.${d}.conditions.${y}.value`,placeholder:e.$trans("employee.payroll.salary_template.props.value"),error:t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.value`],"onUpdate:error":u=>t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.value`]=u},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),p("div",Ha,[p("div",Ta,[p("div",ka,[y<s.conditions.length-1?(c(),f(F,{key:0,modelValue:_.logicalOperator,"onUpdate:modelValue":u=>_.logicalOperator=u,name:`records.${r}.conditionalFormulas.${d}.conditions.${y}.logicalOperator`,label:e.$trans("employee.payroll.salary_template.props.logical_operator"),options:h.logicalOperators,error:t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.logicalOperator`],"onUpdate:error":u=>t(n)[`records.${r}.conditionalFormulas.${d}.conditions.${y}.logicalOperator`]=u},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error"])):U("",!0)]),p("i",{class:"ml-4 fas fa-times-circle text-danger cursor-pointer",onClick:u=>W(s,y)},null,8,Pa)])])]))),128)),i(v,{type:"text",modelValue:s.formula,"onUpdate:modelValue":_=>s.formula=_,name:`records.${r}.conditionalFormulas.${d}.formula`,placeholder:e.$trans("employee.payroll.salary_template.props.conditional_value"),error:t(n)[`records.${r}.conditionalFormulas.${d}.formula`],"onUpdate:error":_=>t(n)[`records.${r}.conditionalFormulas.${d}.formula`]=_},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"]),p("div",Ca,[i(B,{onClick:_=>Q(s),size:"sm"},{default:$(()=>[C(k(e.$trans("global.add",{attribute:e.$trans("general.sub_condition")})),1)]),_:2},1032,["onClick"])])]),_:2},1024))),128))])):U("",!0),p("div",Ba,[a.hasCondition?(c(),f(B,{key:0,onClick:s=>K(a),class:"mt-2",size:"sm"},{default:$(()=>[C(k(e.$trans("global.add",{attribute:e.$trans("general.condition")})),1)]),_:2},1032,["onClick"])):U("",!0)])],64)):U("",!0)]),_:2},1024)]),_:1},8,["list"])),p("div",Oa,[i(P,{design:"info"},{default:$(()=>[C(k(e.$trans("employee.payroll.salary_template.variable_info",{variable:h.variables.map(a=>a.value).join(", ")})),1)]),_:1})])]),_:1},8,["form"])}}}),ja={name:"EmployeePayrollSalaryTemplateAction"},wa=Object.assign(ja,{setup(J){const g=G();return(b,h)=>{const n=V("PageHeaderAction"),m=V("PageHeader"),j=V("ParentTransition");return c(),H(R,null,[i(m,{title:b.$trans(t(g).meta.trans,{attribute:b.$trans(t(g).meta.label)}),navs:[{label:b.$trans("employee.employee"),path:"Employee"},{label:b.$trans("employee.payroll.payroll"),path:"EmployeePayroll"},{label:b.$trans("employee.payroll.salary_template.salary_template"),path:"EmployeePayrollSalaryTemplateList"}]},{default:$(()=>[i(n,{name:"EmployeePayrollSalaryTemplate",title:b.$trans("employee.payroll.salary_template.salary_template"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(j,{appear:"",visibility:!0},{default:$(()=>[i(Ra)]),_:1})],64)}}});export{wa as default};
