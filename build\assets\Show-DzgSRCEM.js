import{i as v,u as S,h as k,l as C,r as s,a as m,o as u,e as n,w as t,f as p,q as P,b as y,d as V,s as l,t as o,F as H}from"./app-BAwPsakn.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},N={key:0},D={name:"EmployeeAttendanceTypeShow"},F=Object.assign(D,{setup(R){v();const i=S(),c=k(),_={},b="employee/attendance/type/",a=C({..._}),f=e=>{Object.assign(a,e)};return(e,d)=>{const g=s("PageHeaderAction"),$=s("PageHeader"),r=s("BaseDataView"),B=s("BaseButton"),h=s("ShowButton"),A=s("BaseCard"),T=s("ShowItem"),w=s("ParentTransition");return u(),m(H,null,[n($,{title:e.$trans(p(i).meta.trans,{attribute:e.$trans(p(i).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"},{label:e.$trans("employee.attendance.type.type"),path:"EmployeeAttendanceTypeList"}]},{default:t(()=>[n(g,{name:"EmployeeAttendanceType",title:e.$trans("employee.attendance.type.type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(w,{appear:"",visibility:!0},{default:t(()=>[n(T,{"init-url":b,uuid:p(i).params.uuid,onSetItem:f,onRedirectTo:d[1]||(d[1]=E=>p(c).push({name:"EmployeeAttendanceType"}))},{default:t(()=>[a.uuid?(u(),P(A,{key:0},{title:t(()=>[l(o(a.name),1)]),footer:t(()=>[n(h,null,{default:t(()=>[n(B,{design:"primary",onClick:d[0]||(d[0]=E=>p(c).push({name:"EmployeeAttendanceTypeEdit",params:{uuid:a.uuid}}))},{default:t(()=>[l(o(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[V("dl",I,[n(r,{label:e.$trans("employee.attendance.type.props.name")},{default:t(()=>[l(o(a.name),1)]),_:1},8,["label"]),n(r,{label:e.$trans("employee.attendance.type.props.code")},{default:t(()=>[l(o(a.code),1)]),_:1},8,["label"]),n(r,{label:e.$trans("employee.attendance.type.props.category")},{default:t(()=>[l(o(a.category.label)+" ",1),a.unit.value?(u(),m("span",N,"("+o(a.unit.label)+")",1)):y("",!0)]),_:1},8,["label"]),n(r,{label:e.$trans("employee.attendance.type.props.alias")},{default:t(()=>[l(o(a.alias),1)]),_:1},8,["label"]),n(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.attendance.type.props.description")},{default:t(()=>[l(o(a.description),1)]),_:1},8,["label"]),n(r,{label:e.$trans("general.created_at")},{default:t(()=>[l(o(a.createdAt.formatted),1)]),_:1},8,["label"]),n(r,{label:e.$trans("general.updated_at")},{default:t(()=>[l(o(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
