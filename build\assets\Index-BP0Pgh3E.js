import{u as j,l as I,n as E,r as i,q as f,o as u,w as e,d as B,e as n,b as $,h as O,j as Q,y,m as x,f as l,a as F,F as z,v as G,s as a,t as s,aN as J}from"./app-BAwPsakn.js";const K={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={__name:"Filter",emits:["hide"],setup(A,{emit:v}){const k=j(),C=v,h={codeNumber:"",purposes:[],startDate:"",endDate:""},m=I({...h}),_=I({purposes:[],isLoaded:!k.query.purposes});return E(async()=>{_.purposes=k.query.purposes?k.query.purposes.split(","):[],_.isLoaded=!0}),(g,p)=>{const V=i("BaseInput"),w=i("BaseSelectSearch"),o=i("DatePicker"),r=i("FilterForm");return u(),f(r,{"init-form":h,form:m,multiple:["purposes"],onHide:p[4]||(p[4]=d=>C("hide"))},{default:e(()=>[B("div",K,[B("div",W,[n(V,{type:"text",modelValue:m.codeNumber,"onUpdate:modelValue":p[0]||(p[0]=d=>m.codeNumber=d),name:"codeNumber",label:g.$trans("reception.visitor_log.props.code_number")},null,8,["modelValue","label"])]),B("div",X,[_.isLoaded?(u(),f(w,{key:0,multiple:"",name:"purposes",label:g.$trans("global.select",{attribute:g.$trans("reception.visitor_log.purpose.purpose")}),modelValue:m.purposes,"onUpdate:modelValue":p[1]||(p[1]=d=>m.purposes=d),"value-prop":"uuid","init-search":_.purposes,"search-action":"option/list","additional-search-query":{type:"visiting_purpose"}},null,8,["label","modelValue","init-search"])):$("",!0)]),B("div",Y,[n(o,{start:m.startDate,"onUpdate:start":p[2]||(p[2]=d=>m.startDate=d),end:m.endDate,"onUpdate:end":p[3]||(p[3]=d=>m.endDate=d),name:"dateBetween",as:"range",label:g.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},L={key:0},ee={key:1},te={name:"ReceptionVisitorLogList"},oe=Object.assign(te,{setup(A){const v=O(),k=Q("emitter");let C=["filter"];y("reception:config")&&C.push("config"),y("visitor-log:create")&&C.unshift("create");let h=[];y("visitor-log:export")&&(h=["print","pdf","excel"]);const m="reception/visitorLog/",_=x(!1),g=I({}),p=o=>{Object.assign(g,o)},V=o=>{k.emit("actionItem",{uuid:o.uuid,action:"markExit",confirmation:!0})},w=o=>{let r="/app/reception/visitor-logs/"+o.uuid+"/export",d={};window.open(J(r,d),"_blank").focus()};return(o,r)=>{const d=i("PageHeaderAction"),P=i("PageHeader"),N=i("ParentTransition"),b=i("DataCell"),R=i("TextMuted"),S=i("BaseBadge"),D=i("FloatingMenuItem"),T=i("FloatingMenu"),q=i("DataRow"),M=i("BaseButton"),H=i("DataTable"),U=i("ListItem");return u(),f(U,{"init-url":m,"additional-query":{},onSetItems:p},{header:e(()=>[n(P,{title:o.$trans("reception.visitor_log.visitor_log"),navs:[{label:o.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[n(d,{url:"reception/visitor-logs/",name:"ReceptionVisitorLog",title:o.$trans("reception.visitor_log.visitor_log"),actions:l(C),"dropdown-actions":l(h),"config-path":"ReceptionConfig",onToggleFilter:r[0]||(r[0]=t=>_.value=!_.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(N,{appear:"",visibility:_.value},{default:e(()=>[n(Z,{onRefresh:r[1]||(r[1]=t=>l(k).emit("listItems")),onHide:r[2]||(r[2]=t=>_.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(N,{appear:"",visibility:!0},{default:e(()=>[n(H,{header:g.headers,meta:g.meta,module:"reception.visitor_log",onRefresh:r[4]||(r[4]=t=>l(k).emit("listItems"))},{actionButton:e(()=>[l(y)("visitor-log:create")?(u(),f(M,{key:0,onClick:r[3]||(r[3]=t=>l(v).push({name:"ReceptionVisitorLogCreate"}))},{default:e(()=>[a(s(o.$trans("global.add",{attribute:o.$trans("reception.visitor_log.visitor_log")})),1)]),_:1})):$("",!0)]),default:e(()=>[(u(!0),F(z,null,G(g.data,t=>(u(),f(q,{key:t.uuid,onDoubleClick:c=>l(v).push({name:"ReceptionVisitorLogShow",params:{uuid:t.uuid}})},{default:e(()=>[n(b,{name:"codeNumber"},{default:e(()=>[a(s(t.codeNumber),1)]),_:2},1024),n(b,{name:"entryAt"},{default:e(()=>[a(s(t.entryAt.formatted)+" ",1),t.exitAt.value?(u(),f(R,{key:0,block:""},{default:e(()=>[a(s(t.exitAt.formatted),1)]),_:2},1024)):$("",!0),B("div",null,[t.exitAt.value?$("",!0):(u(),f(S,{key:0,class:"cursor-pointer",design:"info",onClick:c=>V(t)},{default:e(()=>[a(s(o.$trans("global.mark",{attribute:o.$trans("reception.visitor_log.props.exit")})),1)]),_:2},1032,["onClick"]))])]),_:2},1024),n(b,{name:"type"},{default:e(()=>{var c;return[a(s((c=t.type)==null?void 0:c.label),1)]}),_:2},1024),n(b,{name:"name"},{default:e(()=>[a(s(t.name)+" ",1),n(R,{block:""},{default:e(()=>[a(s(t.contactNumber),1)]),_:2},1024)]),_:2},1024),n(b,{name:"purpose"},{default:e(()=>{var c;return[a(s(((c=t.purpose)==null?void 0:c.name)||"-"),1)]}),_:2},1024),n(b,{name:"employee"},{default:e(()=>[t.employee?(u(),F("span",L,[a(s(t.employee.name)+" ",1),n(R,{block:""},{default:e(()=>[a(s(t.employee.designation),1)]),_:2},1024)])):(u(),F("span",ee,"-"))]),_:2},1024),n(b,{name:"count"},{default:e(()=>[a(s(t.count),1)]),_:2},1024),n(b,{name:"createdAt"},{default:e(()=>[a(s(t.createdAt.formatted),1)]),_:2},1024),n(b,{name:"action"},{default:e(()=>[n(T,null,{default:e(()=>[n(D,{icon:"fas fa-arrow-circle-right",onClick:c=>l(v).push({name:"ReceptionVisitorLogShow",params:{uuid:t.uuid}})},{default:e(()=>[a(s(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(D,{icon:"fas fa-arrow-circle-right",onClick:c=>w(t)},{default:e(()=>[a(s(o.$trans("global.print",{attribute:o.$trans("reception.visitor_log.pass")})),1)]),_:2},1032,["onClick"]),l(y)("visitor-log:edit")?(u(),f(D,{key:0,icon:"fas fa-edit",onClick:c=>l(v).push({name:"ReceptionVisitorLogEdit",params:{uuid:t.uuid}})},{default:e(()=>[a(s(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),l(y)("visitor-log:create")?(u(),f(D,{key:1,icon:"fas fa-copy",onClick:c=>l(v).push({name:"ReceptionVisitorLogDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[a(s(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),l(y)("visitor-log:delete")?(u(),f(D,{key:2,icon:"fas fa-trash",onClick:c=>l(k).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[a(s(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{oe as default};
