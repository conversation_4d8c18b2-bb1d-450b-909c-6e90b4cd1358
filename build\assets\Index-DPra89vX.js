import{l as w,r,q as p,o as m,w as e,d as R,e as s,h as N,j as O,y as c,m as P,f as a,a as U,F as E,v as z,s as l,t as u,b as $}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(V,{emit:d}){const g=d,I=V,k={name:"",inventory:""},v=w({...k}),b=w({inventories:I.preRequisites.inventories});return(y,i)=>{const _=r("BaseInput"),F=r("BaseSelect"),S=r("FilterForm");return m(),p(S,{"init-form":k,form:v,multiple:[],onHide:i[2]||(i[2]=t=>g("hide"))},{default:e(()=>[R("div",G,[R("div",J,[s(_,{type:"text",modelValue:v.name,"onUpdate:modelValue":i[0]||(i[0]=t=>v.name=t),name:"name",label:y.$trans("inventory.stock_category.props.name")},null,8,["modelValue","label"])]),R("div",K,[s(F,{modelValue:v.inventory,"onUpdate:modelValue":i[1]||(i[1]=t=>v.inventory=t),name:"inventory","label-prop":"name","value-prop":"uuid",label:y.$trans("inventory.inventory"),options:b.inventories},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},W={name:"InventoryStockCategoryList"},Y=Object.assign(W,{setup(V){const d=N(),g=O("emitter");let I=["filter"];c("stock-category:create")&&I.unshift("create");let k=[];c("stock-category:export")&&(k=["print","pdf","excel"]),c("stock-category:create")&&k.unshift("import");const v="inventory/stockCategory/",b=w({inventories:[]}),y=P(!1),i=P(!1),_=w({}),F=t=>{Object.assign(_,t)},S=t=>{Object.assign(b,t)};return(t,n)=>{const q=r("BaseButton"),h=r("PageHeaderAction"),H=r("PageHeader"),T=r("BaseImport"),D=r("ParentTransition"),C=r("DataCell"),B=r("FloatingMenuItem"),j=r("FloatingMenu"),A=r("DataRow"),L=r("DataTable"),M=r("ListItem");return m(),p(M,{"init-url":v,"pre-requisites":!0,onSetPreRequisites:S,onSetItems:F},{header:e(()=>[s(H,{title:t.$trans("inventory.stock_category.stock_category"),navs:[{label:t.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[s(h,{url:"inventory/stock-categories/",name:"InventoryStockCategory",title:t.$trans("inventory.stock_category.stock_category"),actions:a(I),"dropdown-actions":a(k),onToggleFilter:n[1]||(n[1]=o=>y.value=!y.value),onToggleImport:n[2]||(n[2]=o=>i.value=!i.value)},{default:e(()=>[a(c)("inventory:config")?(m(),p(q,{key:0,design:"white",onClick:n[0]||(n[0]=o=>a(d).push({name:"Inventory"}))},{default:e(()=>[l(u(t.$trans("global.manage",{attribute:t.$trans("inventory.inventory")})),1)]),_:1})):$("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:e(()=>[s(D,{appear:"",visibility:i.value},{default:e(()=>[s(T,{path:"inventory/stock-categories/import",onCancelled:n[3]||(n[3]=o=>i.value=!1),onHide:n[4]||(n[4]=o=>i.value=!1),onCompleted:n[5]||(n[5]=o=>a(g).emit("listItems"))})]),_:1},8,["visibility"])]),filter:e(()=>[s(D,{appear:"",visibility:y.value},{default:e(()=>[s(Q,{onRefresh:n[6]||(n[6]=o=>a(g).emit("listItems")),"pre-requisites":b,onHide:n[7]||(n[7]=o=>y.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[s(D,{appear:"",visibility:!0},{default:e(()=>[s(L,{header:_.headers,meta:_.meta,module:"inventory.stock_category",onRefresh:n[9]||(n[9]=o=>a(g).emit("listItems"))},{actionButton:e(()=>[a(c)("stock-category:create")?(m(),p(q,{key:0,onClick:n[8]||(n[8]=o=>a(d).push({name:"InventoryStockCategoryCreate"}))},{default:e(()=>[l(u(t.$trans("global.add",{attribute:t.$trans("inventory.stock_category.stock_category")})),1)]),_:1})):$("",!0)]),default:e(()=>[(m(!0),U(E,null,z(_.data,o=>(m(),p(A,{key:o.uuid,onDoubleClick:f=>a(d).push({name:"InventoryStockCategoryShow",params:{uuid:o.uuid}})},{default:e(()=>[s(C,{name:"name"},{default:e(()=>[l(u(o.name),1)]),_:2},1024),s(C,{name:"inventory"},{default:e(()=>{var f;return[l(u(((f=o.inventory)==null?void 0:f.name)||"-"),1)]}),_:2},1024),s(C,{name:"createdAt"},{default:e(()=>[l(u(o.createdAt.formatted),1)]),_:2},1024),s(C,{name:"action"},{default:e(()=>[s(j,null,{default:e(()=>[s(B,{icon:"fas fa-arrow-circle-right",onClick:f=>a(d).push({name:"InventoryStockCategoryShow",params:{uuid:o.uuid}})},{default:e(()=>[l(u(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(c)("stock-category:edit")?(m(),p(B,{key:0,icon:"fas fa-edit",onClick:f=>a(d).push({name:"InventoryStockCategoryEdit",params:{uuid:o.uuid}})},{default:e(()=>[l(u(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),a(c)("stock-category:create")?(m(),p(B,{key:1,icon:"fas fa-copy",onClick:f=>a(d).push({name:"InventoryStockCategoryDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[l(u(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),a(c)("stock-category:delete")?(m(),p(B,{key:2,icon:"fas fa-trash",onClick:f=>a(g).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[l(u(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
