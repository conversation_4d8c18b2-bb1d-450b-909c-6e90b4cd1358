import{u as M,l as I,n as N,r,q as v,o as g,w as e,d as $,b as w,s as l,t as o,e as a,h as H,j as O,m as P,f,a as R,F as U,v as j}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(F,{emit:_}){const p=M(),k=_,b={departments:[],employees:[],startDate:"",endDate:""},c=I({...b}),i=I({departments:[],employees:[],isLoaded:!(p.query.departments||p.query.employees)});return N(async()=>{i.departments=p.query.departments?p.query.departments.split(","):[],i.employees=p.query.employees?p.query.employees.split(","):[],i.isLoaded=!0}),(u,m)=>{const s=r("BaseSelectSearch"),d=r("DatePicker"),C=r("FilterForm");return g(),v(C,{"init-form":b,form:c,multiple:["departments","employees"],onHide:m[4]||(m[4]=t=>k("hide"))},{default:e(()=>[$("div",E,[$("div",z,[i.isLoaded?(g(),v(s,{key:0,multiple:"",name:"departments",label:u.$trans("global.select",{attribute:u.$trans("academic.department.department")}),modelValue:c.departments,"onUpdate:modelValue":m[0]||(m[0]=t=>c.departments=t),"value-prop":"uuid","init-search":i.departments,"search-key":"course_department","search-action":"academic/department/list"},{selectedOption:e(t=>[l(o(t.value.name),1)]),listOption:e(t=>[l(o(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):w("",!0)]),$("div",G,[i.isLoaded?(g(),v(s,{key:0,multiple:"",name:"employees",label:u.$trans("global.select",{attribute:u.$trans("employee.employee")}),modelValue:c.employees,"onUpdate:modelValue":m[1]||(m[1]=t=>c.employees=t),"value-prop":"uuid","init-search":i.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[l(o(t.value.name)+" ("+o(t.value.codeNumber)+") ",1)]),listOption:e(t=>[l(o(t.option.name)+" ("+o(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):w("",!0)]),$("div",J,[a(d,{start:c.startDate,"onUpdate:start":m[2]||(m[2]=t=>c.startDate=t),end:c.endDate,"onUpdate:end":m[3]||(m[3]=t=>c.endDate=t),name:"dateBetween",as:"range",label:u.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"AcademicDepartmentInchargeList"},X=Object.assign(Q,{setup(F){const _=H(),p=O("emitter");let k=["create","filter"],b=["print","pdf","excel"];const c="academic/departmentIncharge/",i=P(!1),u=I({}),m=s=>{Object.assign(u,s)};return(s,d)=>{const C=r("PageHeaderAction"),t=r("PageHeader"),A=r("ParentTransition"),y=r("DataCell"),B=r("TextMuted"),h=r("FloatingMenuItem"),V=r("FloatingMenu"),L=r("DataRow"),S=r("BaseButton"),T=r("DataTable"),q=r("ListItem");return g(),v(q,{"init-url":c,onSetItems:m},{header:e(()=>[a(t,{title:s.$trans("academic.department_incharge.department_incharge"),navs:[{label:s.$trans("academic.academic"),path:"Academic"},{label:s.$trans("academic.department.department"),path:"AcademicDepartment"}]},{default:e(()=>[a(C,{url:"academic/department-incharges/",name:"AcademicDepartmentIncharge",title:s.$trans("academic.department_incharge.department_incharge"),actions:f(k),"dropdown-actions":f(b),onToggleFilter:d[0]||(d[0]=n=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(A,{appear:"",visibility:i.value},{default:e(()=>[a(K,{onRefresh:d[1]||(d[1]=n=>f(p).emit("listItems")),onHide:d[2]||(d[2]=n=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(A,{appear:"",visibility:!0},{default:e(()=>[a(T,{header:u.headers,meta:u.meta,module:"academic.department_incharge",onRefresh:d[4]||(d[4]=n=>f(p).emit("listItems"))},{actionButton:e(()=>[a(S,{onClick:d[3]||(d[3]=n=>f(_).push({name:"AcademicDepartmentInchargeCreate"}))},{default:e(()=>[l(o(s.$trans("global.add",{attribute:s.$trans("academic.department_incharge.department_incharge")})),1)]),_:1})]),default:e(()=>[(g(!0),R(U,null,j(u.data,n=>(g(),v(L,{key:n.uuid,onDoubleClick:D=>f(_).push({name:"AcademicDepartmentInchargeShow",params:{uuid:n.uuid}})},{default:e(()=>[a(y,{name:"department"},{default:e(()=>[l(o(n.department.name),1)]),_:2},1024),a(y,{name:"employee"},{default:e(()=>[l(o(n.employee.name)+" ",1),a(B,{block:""},{default:e(()=>[l(o(n.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),a(y,{name:"period"},{default:e(()=>[l(o(n.period),1)]),_:2},1024),a(y,{name:"createdAt"},{default:e(()=>[l(o(n.createdAt.formatted),1)]),_:2},1024),a(y,{name:"action"},{default:e(()=>[a(V,null,{default:e(()=>[a(h,{icon:"fas fa-arrow-circle-right",onClick:D=>f(_).push({name:"AcademicDepartmentInchargeShow",params:{uuid:n.uuid}})},{default:e(()=>[l(o(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-edit",onClick:D=>f(_).push({name:"AcademicDepartmentInchargeEdit",params:{uuid:n.uuid}})},{default:e(()=>[l(o(s.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-copy",onClick:D=>f(_).push({name:"AcademicDepartmentInchargeDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[l(o(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-trash",onClick:D=>f(p).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[l(o(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
