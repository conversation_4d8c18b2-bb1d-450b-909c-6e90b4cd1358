import{r as o,q as a,o as c,w as i,e as r}from"./app-BAwPsakn.js";const s={name:"ContactConfig"},_=Object.assign(s,{setup(f){const n=[{name:"ContactConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"ContactConfigReligion",icon:"fas fa-chevron-right",label:"contact.religion.religion"},{name:"ContactConfigCaste",icon:"fas fa-chevron-right",label:"contact.caste.caste"},{name:"ContactConfigCategory",icon:"fas fa-chevron-right",label:"contact.category.category"}];return(g,l)=>{const t=o("router-view"),e=o("ModuleConfig");return c(),a(e,{navigations:n},{default:i(()=>[r(t)]),_:1})}}});export{_ as default};
