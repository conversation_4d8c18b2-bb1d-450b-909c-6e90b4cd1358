import{u as V,l as I,n as N,r as o,q as b,o as f,w as e,d as A,e as t,h as O,j as U,m as E,f as d,a as z,F as G,v as J,s as i,t as l,b as R}from"./app-BAwPsakn.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(h,{emit:c}){V();const _=c,k={name:""},m=I({...k}),B=I({isLoaded:!0});return N(async()=>{B.isLoaded=!0}),(g,r)=>{const u=o("BaseInput"),F=o("BaseSelect"),w=o("FilterForm");return f(),b(w,{"init-form":k,form:m,multiple:[],onHide:r[2]||(r[2]=p=>_("hide"))},{default:e(()=>[A("div",K,[A("div",Q,[t(u,{type:"text",modelValue:m.name,"onUpdate:modelValue":r[0]||(r[0]=p=>m.name=p),name:"name",label:g.$trans("academic.id_card.template.props.name")},null,8,["modelValue","label"])]),A("div",W,[t(F,{modelValue:m.for,"onUpdate:modelValue":r[1]||(r[1]=p=>m.for=p),name:"for",label:g.$trans("academic.id_card.template.props.for"),options:h.preRequisites.for},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},Y={name:"AcademicIdCardTemplateList"},x=Object.assign(Y,{setup(h){V();const c=O(),_=U("emitter");let k=["create","filter"],m=["print","pdf","excel"];const B="academic/idCardTemplate/",g=I({for:[]}),r=E(!1),u=I({}),F=n=>{Object.assign(u,n)},w=n=>{Object.assign(g,n)},p=n=>{window.open(`/app/academic/id-card-templates/${n.uuid}/export?action=print`)};return(n,s)=>{const y=o("BaseButton"),q=o("PageHeaderAction"),P=o("PageHeader"),D=o("ParentTransition"),C=o("DataCell"),T=o("BaseBadge"),M=o("TextMuted"),$=o("FloatingMenuItem"),S=o("FloatingMenu"),j=o("DataRow"),H=o("DataTable"),L=o("ListItem");return f(),b(L,{"init-url":B,"pre-requisites":!0,onSetPreRequisites:w,onSetItems:F},{header:e(()=>[t(P,{title:n.$trans("academic.id_card.template.template"),navs:[{label:n.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(q,{url:"academic/id-card-templates/",name:"AcademicIdCardTemplate",title:n.$trans("academic.id_card.template.template"),actions:d(k),"dropdown-actions":d(m),onToggleFilter:s[1]||(s[1]=a=>r.value=!r.value)},{default:e(()=>[t(y,{design:"white",onClick:s[0]||(s[0]=a=>d(c).push({name:"AcademicIdCard"}))},{default:e(()=>[i(l(n.$trans("academic.id_card.id_card")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(D,{appear:"",visibility:r.value},{default:e(()=>[t(X,{onRefresh:s[2]||(s[2]=a=>d(_).emit("listItems")),"pre-requisites":g,onHide:s[3]||(s[3]=a=>r.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(D,{appear:"",visibility:!0},{default:e(()=>[t(H,{header:u.headers,meta:u.meta,module:"academic.id_card.template",onRefresh:s[5]||(s[5]=a=>d(_).emit("listItems"))},{actionButton:e(()=>[t(y,{onClick:s[4]||(s[4]=a=>d(c).push({name:"AcademicIdCardTemplateCreate"}))},{default:e(()=>[i(l(n.$trans("global.add",{attribute:n.$trans("academic.id_card.template.template")})),1)]),_:1})]),default:e(()=>[(f(!0),z(G,null,J(u.data,a=>(f(),b(j,{key:a.uuid,onDoubleClick:v=>d(c).push({name:"AcademicIdCardTemplateShow",params:{uuid:a.uuid}})},{default:e(()=>[t(C,{name:"name"},{default:e(()=>[i(l(a.name),1)]),_:2},1024),t(C,{name:"for"},{default:e(()=>[i(l(a.for.label)+" ",1),a.hasCustomTemplateFile?(f(),b(T,{key:0,design:"info"},{default:e(()=>[i(l(n.$trans("academic.id_card.template.props.custom_template_file")),1)]),_:1})):R("",!0),a.hasCustomTemplateFile?(f(),b(M,{key:1,block:""},{default:e(()=>[i(l(a.customTemplateFileName),1)]),_:2},1024)):R("",!0)]),_:2},1024),t(C,{name:"createdAt"},{default:e(()=>[i(l(a.createdAt.formatted),1)]),_:2},1024),t(C,{name:"action"},{default:e(()=>[t(S,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:v=>d(c).push({name:"AcademicIdCardTemplateShow",params:{uuid:a.uuid}})},{default:e(()=>[i(l(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-print",onClick:v=>p(a)},{default:e(()=>[i(l(n.$trans("general.print")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-edit",onClick:v=>d(c).push({name:"AcademicIdCardTemplateEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(l(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-copy",onClick:v=>d(c).push({name:"AcademicIdCardTemplateDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[i(l(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-trash",onClick:v=>d(_).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(l(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
