import{u as T,j,l as B,H as N,n as U,r as o,q as C,o as g,w as t,d as v,e,b as O,s as d,t as l,f as _,h as E,i as M,y as W,m as F,a as S,F as R,v as z}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(V,{emit:y}){const h=T();j("moment");const k=y,w=V,b={date:"",batches:[],status:"all"},i=B({...b}),f=N(w.initUrl),u=B({isLoaded:!h.query.batches});return U(async()=>{u.batches=h.query.batches?h.query.batches.split(","):[],u.isLoaded=!0}),(n,s)=>{const c=o("DatePicker"),m=o("BaseSelectSearch"),$=o("CustomCheckbox"),D=o("FilterForm");return g(),C(D,{"init-form":b,multiple:["batches"],form:i,onHide:s[4]||(s[4]=a=>k("hide"))},{default:t(()=>[v("div",G,[v("div",I,[e(c,{modelValue:i.date,"onUpdate:modelValue":s[0]||(s[0]=a=>i.date=a),name:"date",as:"date",label:n.$trans("general.date")},null,8,["modelValue","label"])]),v("div",J,[u.isLoaded?(g(),C(m,{key:0,multiple:"",name:"batches",label:n.$trans("global.select",{attribute:n.$trans("academic.batch.batch")}),modelValue:i.batches,"onUpdate:modelValue":s[1]||(s[1]=a=>i.batches=a),"value-prop":"uuid","init-search":u.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(a=>[d(l(a.value.course.name)+" "+l(a.value.name),1)]),listOption:t(a=>[d(l(a.option.course.nameWithTerm)+" "+l(a.option.name),1)]),_:1},8,["label","modelValue","init-search"])):O("",!0)]),v("div",K,[e($,{label:n.$trans("student.attendance.status"),options:[{label:n.$trans("general.all"),value:"all"},{label:n.$trans("student.attendance.statuses.marked"),value:"marked"},{label:n.$trans("student.attendance.statuses.not_marked"),value:"not_marked"}],modelValue:i.status,"onUpdate:modelValue":s[2]||(s[2]=a=>i.status=a),error:_(f).status,"onUpdate:error":s[3]||(s[3]=a=>_(f).status=a)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},X={name:"StudentReportDateWiseAttendance"},x=Object.assign(X,{setup(V){const y=T(),h=E(),k=M();let w=["filter"],b=[];W("student:list-attendance")&&(b=["print","pdf","excel"]);const i="student/report/",f=F(!0),u=F(!1),n=B({headers:[],data:[],meta:{total:0}}),s=async()=>{u.value=!0,await k.dispatch(i+"fetchReport",{name:"date-wise-attendance",params:y.query}).then(c=>{u.value=!1,Object.assign(n,c)}).catch(c=>{u.value=!1})};return U(async()=>{await s()}),(c,m)=>{const $=o("PageHeaderAction"),D=o("PageHeader"),a=o("ParentTransition"),q=o("TextMuted"),p=o("DataCell"),A=o("BaseButton"),H=o("DataRow"),L=o("DataTable"),P=o("BaseCard");return g(),S(R,null,[e(D,{title:c.$trans(_(y).meta.label),navs:[{label:c.$trans("student.student"),path:"Student"},{label:c.$trans("student.report.report"),path:"StudentReport"}]},{default:t(()=>[e($,{url:"student/reports/date-wise-attendance/",name:"StudentReportDateWiseAttendance",title:c.$trans("student.report.date_wise_attendance.date_wise_attendance"),actions:_(w),"dropdown-actions":_(b),headers:n.headers,onToggleFilter:m[0]||(m[0]=r=>f.value=!f.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),e(a,{appear:"",visibility:f.value},{default:t(()=>[e(Q,{onAfterFilter:s,"init-url":i,onHide:m[1]||(m[1]=r=>f.value=!1)})]),_:1},8,["visibility"]),e(a,{appear:"",visibility:!0},{default:t(()=>[e(P,{"no-padding":"","no-content-padding":"","is-loading":u.value},{default:t(()=>[e(L,{header:n.headers,footer:n.footers,meta:n.meta,module:"student.report.date_wise_attendance",onRefresh:s},{default:t(()=>[(g(!0),S(R,null,z(n.data,r=>(g(),C(H,{key:r.uuid},{default:t(()=>[e(p,{name:"course_batch"},{default:t(()=>[d(l(r.courseBatch)+" ",1),e(q,{block:""},{default:t(()=>[d(l(r.incharge),1)]),_:2},1024)]),_:2},1024),e(p,{name:"strength"},{default:t(()=>[d(l(r.strength),1)]),_:2},1024),e(p,{name:"present"},{default:t(()=>[d(l(r.present),1)]),_:2},1024),e(p,{name:"absent"},{default:t(()=>[d(l(r.absent),1)]),_:2},1024),e(p,{name:"late"},{default:t(()=>[d(l(r.late),1)]),_:2},1024),e(p,{name:"halfDay"},{default:t(()=>[d(l(r.halfDay),1)]),_:2},1024),e(p,{name:"total"},{default:t(()=>[d(l(r.total),1)]),_:2},1024),e(p,{name:"action"},{default:t(()=>[e(A,{design:"white",size:"sm",onClick:Y=>_(h).push({name:"StudentList",query:{batches:r.batchUuid}})},{default:t(()=>m[2]||(m[2]=[v("i",{class:"fas fa-up-right-from-square"},null,-1)])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{x as default};
