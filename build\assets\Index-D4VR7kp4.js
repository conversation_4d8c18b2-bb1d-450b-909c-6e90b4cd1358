import{u as L,i as N,m as R,H as M,l as x,n as E,a9 as C,r as y,q as k,o as g,w as p,a as S,b as v,d as l,e as i,f as s,s as V,t as m,B as z,F as H}from"./app-BAwPsakn.js";const G={key:0,class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={key:0,class:"ml-1"},Q={key:0,class:"ml-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},ee={class:"flex items-center gap-2"},te={class:"grid grid-cols-3 gap-6"},ae={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"mt-4 grid grid-cols-3 gap-6"},re={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},ie={class:"mt-4 grid grid-cols-4 gap-6"},de={class:"col-span-4 sm:col-span-1"},ue={class:"col-span-4 sm:col-span-1"},me={class:"col-span-4 sm:col-span-1"},pe={class:"col-span-4 sm:col-span-1"},ce={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(B,{emit:F}){const r=L(),O=N(),A=F,f=B,w={exam:"",attempt:"first",batch:"",students:[],showSno:!1,showPrintDateTime:!1,column:1,title:"",signatory1:"",signatory2:"",signatory3:"",signatory4:"",showWatermark:!1},c=R(!1),h=R(!1),o=M(f.initUrl),a=x({...w}),u=x({exams:f.preRequisites.exams}),d=x({exam:"",batch:"",students:[],isLoaded:!(r.query.exam||r.query.batch||r.query.students)}),D=async n=>{u.students=[],a.students=[];let t="studying";a.showAllStudent&&(t="all"),c.value=!0,await O.dispatch("student/listAll",{params:{batch:n.uuid,status:t}}).then(b=>{u.students=b,c.value=!1}).catch(b=>{c.value=!1}),d.students=[]},W=()=>{u.students=[],a.students=[],d.students=[]};return E(async()=>{if(_.isEmpty(r.query)){d.isLoaded=!0;return}d.exam=r.query.exam,a.exam=r.query.exam,a.attempt=r.query.attempt,d.batch=r.query.batch,a.batch=r.query.batch,a.showSno=C(r.query.showSno||""),a.showWatermark=C(r.query.showWatermark||""),a.showPrintDateTime=C(r.query.showPrintDateTime||""),a.students=d.students=r.query.students?r.query.students.split(","):[],d.isLoaded=!0}),(n,t)=>{const b=y("BaseSelect"),P=y("BaseSelectSearch"),T=y("BaseSwitch"),U=y("BaseInput"),j=y("BaseFieldset"),I=y("FilterForm");return g(),k(I,{"init-form":w,multiple:["students"],form:a,onHide:t[27]||(t[27]=e=>A("hide"))},{default:p(()=>[d.isLoaded?(g(),S("div",G,[l("div",J,[i(b,{modelValue:a.exam,"onUpdate:modelValue":t[0]||(t[0]=e=>a.exam=e),name:"exam",label:n.$trans("exam.exam"),"value-prop":"uuid",options:B.preRequisites.exams,error:s(o).exam,"onUpdate:error":t[1]||(t[1]=e=>s(o).exam=e)},{selectedOption:p(e=>{var $,q;return[V(m(e.value.name)+" ",1),e.value.term?(g(),S("span",K,"("+m(((q=($=e.value.term)==null?void 0:$.division)==null?void 0:q.name)||n.$trans("general.all"))+")",1)):v("",!0)]}),listOption:p(e=>{var $,q;return[V(m(e.option.name)+" ",1),e.option.term?(g(),S("span",Q,"("+m(((q=($=e.option.term)==null?void 0:$.division)==null?void 0:q.name)||n.$trans("general.all"))+")",1)):v("",!0)]}),_:1},8,["modelValue","label","options","error"])]),l("div",X,[d.isLoaded?(g(),k(b,{key:0,modelValue:a.attempt,"onUpdate:modelValue":t[2]||(t[2]=e=>a.attempt=e),name:"attempt",label:n.$trans("exam.schedule.props.attempt"),options:B.preRequisites.attempts,error:s(o).attempt,"onUpdate:error":t[3]||(t[3]=e=>s(o).attempt=e)},null,8,["modelValue","label","options","error"])):v("",!0)]),l("div",Y,[d.isLoaded?(g(),k(P,{key:0,name:"batch",label:n.$trans("global.select",{attribute:n.$trans("academic.batch.batch")}),modelValue:a.batch,"onUpdate:modelValue":t[4]||(t[4]=e=>a.batch=e),error:s(o).batch,"onUpdate:error":t[5]||(t[5]=e=>s(o).batch=e),"value-prop":"uuid","init-search":d.batch,"search-key":"course_batch","search-action":"academic/batch/list",onSelected:D,onRemoved:W},{selectedOption:p(e=>[V(m(e.value.course.name)+" "+m(e.value.name),1)]),listOption:p(e=>[V(m(e.option.course.nameWithTerm)+" "+m(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):v("",!0)]),l("div",Z,[a.batch?(g(),k(b,{key:0,multiple:"",name:"students",label:n.$trans("global.select",{attribute:n.$trans("student.student")}),options:u.students,modelValue:a.students,"onUpdate:modelValue":t[6]||(t[6]=e=>a.students=e),error:s(o).students,"onUpdate:error":t[7]||(t[7]=e=>s(o).students=e),"track-by":"name","value-prop":"uuid"},{selectedOption:p(e=>[V(m(e.value.name)+" ("+m(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:p(e=>[V(m(e.option.name)+" ("+m(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","options","modelValue","error"])):v("",!0)])])):v("",!0),s(z)(["student","guardian"],"any")?v("",!0):(g(),k(j,{key:1,class:"mt-4"},{legend:p(()=>[l("div",ee,[V(m(n.$trans("global.show",{attribute:n.$trans("general.options")}))+" ",1),i(T,{reverse:"",modelValue:h.value,"onUpdate:modelValue":t[8]||(t[8]=e=>h.value=e),name:"showOptions"},null,8,["modelValue"])])]),default:p(()=>[h.value?(g(),S(H,{key:0},[l("div",te,[l("div",ae,[i(T,{vertical:"",modelValue:a.showSno,"onUpdate:modelValue":t[9]||(t[9]=e=>a.showSno=e),name:"showSno",label:n.$trans("global.show",{attribute:n.$trans("general.sno")}),error:s(o).showSno,"onUpdate:error":t[10]||(t[10]=e=>s(o).showSno=e)},null,8,["modelValue","label","error"])]),l("div",se,[i(T,{vertical:"",modelValue:a.showWatermark,"onUpdate:modelValue":t[11]||(t[11]=e=>a.showWatermark=e),name:"showWatermark",label:n.$trans("global.show",{attribute:n.$trans("print.watermark")}),error:s(o).showWatermark,"onUpdate:error":t[12]||(t[12]=e=>s(o).showWatermark=e)},null,8,["modelValue","label","error"])]),l("div",oe,[i(T,{vertical:"",modelValue:a.showPrintDateTime,"onUpdate:modelValue":t[13]||(t[13]=e=>a.showPrintDateTime=e),name:"showPrintDateTime",label:n.$trans("global.show",{attribute:n.$trans("general.print_date_time")}),error:s(o).showPrintDateTime,"onUpdate:error":t[14]||(t[14]=e=>s(o).showPrintDateTime=e)},null,8,["modelValue","label","error"])])]),l("div",ne,[l("div",re,[i(U,{type:"number",modelValue:a.column,"onUpdate:modelValue":t[15]||(t[15]=e=>a.column=e),name:"column",label:n.$trans("print.column"),error:s(o).column,"onUpdate:error":t[16]||(t[16]=e=>s(o).column=e)},null,8,["modelValue","label","error"])]),l("div",le,[i(U,{type:"text",modelValue:a.title,"onUpdate:modelValue":t[17]||(t[17]=e=>a.title=e),name:"title",label:n.$trans("print.title"),error:s(o).title,"onUpdate:error":t[18]||(t[18]=e=>s(o).title=e)},null,8,["modelValue","label","error"])])]),l("div",ie,[l("div",de,[i(U,{type:"text",modelValue:a.signatory1,"onUpdate:modelValue":t[19]||(t[19]=e=>a.signatory1=e),name:"signatory1",label:n.$trans("print.signatory1"),error:s(o).signatory1,"onUpdate:error":t[20]||(t[20]=e=>s(o).signatory1=e)},null,8,["modelValue","label","error"])]),l("div",ue,[i(U,{type:"text",modelValue:a.signatory2,"onUpdate:modelValue":t[21]||(t[21]=e=>a.signatory2=e),name:"signatory2",label:n.$trans("print.signatory2"),error:s(o).signatory2,"onUpdate:error":t[22]||(t[22]=e=>s(o).signatory2=e)},null,8,["modelValue","label","error"])]),l("div",me,[i(U,{type:"text",modelValue:a.signatory3,"onUpdate:modelValue":t[23]||(t[23]=e=>a.signatory3=e),name:"signatory3",label:n.$trans("print.signatory3"),error:s(o).signatory3,"onUpdate:error":t[24]||(t[24]=e=>s(o).signatory3=e)},null,8,["modelValue","label","error"])]),l("div",pe,[i(U,{type:"text",modelValue:a.signatory4,"onUpdate:modelValue":t[25]||(t[25]=e=>a.signatory4=e),name:"signatory4",label:n.$trans("print.signatory4"),error:s(o).signatory4,"onUpdate:error":t[26]||(t[26]=e=>s(o).signatory4=e)},null,8,["modelValue","label","error"])])])],64)):v("",!0)]),_:1}))]),_:1},8,["form"])}}},ge={name:"ExamAdmitCard"},be=Object.assign(ge,{setup(B){const F=L(),r=N();let O=["filter"],A=[];const f="exam/admitCard/",w=R(!0),c=R(!1),h=x({exams:[],attempts:[]}),o=async()=>{c.value=!0,await r.dispatch(f+"preRequisite").then(u=>{c.value=!1,Object.assign(h,u)}).catch(u=>{c.value=!1})},a=async()=>{c.value=!0,await r.dispatch(f+"fetchReport",{params:F.query}).then(u=>{c.value=!1,window.open("/print").document.write(u)}).catch(u=>{c.value=!1})};return E(async()=>{await o()}),(u,d)=>{const D=y("PageHeaderAction"),W=y("PageHeader"),n=y("ParentTransition"),t=y("BaseCard");return g(),S(H,null,[i(W,{title:u.$trans(s(F).meta.label),navs:[{label:u.$trans("exam.exam"),path:"Exam"}]},{default:p(()=>[i(D,{name:"ExamAdmitCard",title:u.$trans("exam.admit_card.admit_card"),actions:s(O),"dropdown-actions":s(A),onToggleFilter:d[0]||(d[0]=b=>w.value=!w.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),i(n,{appear:"",visibility:w.value},{default:p(()=>[i(ce,{onAfterFilter:a,"init-url":f,"pre-requisites":h,onHide:d[1]||(d[1]=b=>w.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),i(n,{appear:"",visibility:!0},{default:p(()=>[i(t,{"no-padding":"","no-content-padding":"","is-loading":c.value},null,8,["is-loading"])]),_:1})],64)}}});export{be as default};
