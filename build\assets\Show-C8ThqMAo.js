import{i as W,u as R,h as j,j as N,l as $,r as o,a as f,o as d,e as n,w as e,f as t,q as _,b as u,d as O,F as g,v as F,s as i,t as l,y as q}from"./app-BAwPsakn.js";const U={class:"space-y-4"},z={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},G={name:"EmployeeAttendanceWorkShiftShow"},M=Object.assign(G,{setup(J){W();const y=R(),h=j(),a=N("$trans"),w={},v="employee/attendance/workShift/",S=[{key:"day",label:a("list.durations.day"),visibility:!0},{key:"holiday",label:a("employee.attendance.work_shift.props.is_holiday"),visibility:!0},{key:"overnight",label:a("employee.attendance.work_shift.props.is_overnight"),visibility:!0},{key:"start_time",label:a("employee.attendance.work_shift.props.start_time"),visibility:!0},{key:"end_time",label:a("employee.attendance.work_shift.props.end_time"),visibility:!0},{key:"action",label:"",visibility:!0}],s=$({...w}),B=k=>{Object.assign(s,k)};return(k,p)=>{const V=o("PageHeaderAction"),D=o("PageHeader"),m=o("ListItemView"),A=o("ListContainerVertical"),b=o("BaseCard"),c=o("DataCell"),C=o("DataRow"),E=o("SimpleTable"),H=o("BaseDataView"),T=o("BaseButton"),L=o("ShowButton"),x=o("DetailLayoutVertical"),I=o("ShowItem"),P=o("ParentTransition");return d(),f(g,null,[n(D,{title:t(a)(t(y).meta.trans,{attribute:t(a)(t(y).meta.label)}),navs:[{label:t(a)("employee.employee"),path:"Employee"},{label:t(a)("employee.attendance.attendance"),path:"EmployeeAttendance"},{label:t(a)("employee.attendance.work_shift.work_shift"),path:"EmployeeAttendanceWorkShiftList"}]},{default:e(()=>[n(V,{name:"EmployeeAttendanceWorkShift",title:t(a)("employee.attendance.work_shift.work_shift"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(P,{appear:"",visibility:!0},{default:e(()=>[n(I,{"init-url":v,uuid:t(y).params.uuid,onSetItem:B,onRedirectTo:p[1]||(p[1]=r=>t(h).push({name:"EmployeeAttendanceWorkShift"}))},{default:e(()=>[s.uuid?(d(),_(x,{key:0},{detail:e(()=>[n(b,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(l(t(a)("global.detail",{attribute:t(a)("employee.attendance.work_shift.work_shift")})),1)]),default:e(()=>[n(A,null,{default:e(()=>[n(m,{label:t(a)("employee.attendance.work_shift.props.name")},{default:e(()=>[i(l(s.name),1)]),_:1},8,["label"]),n(m,{label:t(a)("employee.attendance.work_shift.props.code")},{default:e(()=>[i(l(s.code),1)]),_:1},8,["label"]),n(m,{label:t(a)("general.created_at")},{default:e(()=>[i(l(s.createdAt.formatted),1)]),_:1},8,["label"]),n(m,{label:t(a)("general.updated_at")},{default:e(()=>[i(l(s.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[O("div",U,[n(b,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(l(t(a)("employee.attendance.work_shift.work_shift")),1)]),footer:e(()=>[n(L,null,{default:e(()=>[t(q)("work-shift:edit")?(d(),_(T,{key:0,design:"primary",onClick:p[0]||(p[0]=r=>t(h).push({name:"EmployeeAttendanceWorkShiftEdit",params:{uuid:s.uuid}}))},{default:e(()=>[i(l(t(a)("general.edit")),1)]),_:1})):u("",!0)]),_:1})]),default:e(()=>[s.records.length>0?(d(),_(E,{key:0,header:S},{default:e(()=>[(d(!0),f(g,null,F(s.records,r=>(d(),_(C,{key:r.uuid},{default:e(()=>[n(c,{name:"day"},{default:e(()=>[i(l(r.label),1)]),_:2},1024),n(c,{name:"holiday"},{default:e(()=>[i(l(r.isHoliday?t(a)("general.yes"):t(a)("general.no")),1)]),_:2},1024),n(c,{name:"overnight"},{default:e(()=>[i(l(r.isHoliday?"-":r.isOvernight?t(a)("general.yes"):t(a)("general.no")),1)]),_:2},1024),n(c,{name:"startTime"},{default:e(()=>[i(l(r.isHoliday?"-":r.startTimeDisplay),1)]),_:2},1024),n(c,{name:"endtime"},{default:e(()=>[i(l(r.isHoliday?"-":r.endTimeDisplay),1)]),_:2},1024),n(c,{name:"action"})]),_:2},1024))),128))]),_:1})):u("",!0),s.description?(d(),f("dl",z,[n(H,{class:"col-span-1 sm:col-span-2",label:t(a)("employee.attendance.work_shift.props.description")},{default:e(()=>[i(l(s.description),1)]),_:1},8,["label"])])):u("",!0)]),_:1})])]),_:1})):u("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
