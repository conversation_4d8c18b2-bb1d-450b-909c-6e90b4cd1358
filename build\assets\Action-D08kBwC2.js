import{u as S,h as j,j as y,H as q,l as k,r as i,q as A,o as c,w as V,d as u,a as _,b as f,e as r,f as o,F as g,J as E}from"./app-BAwPsakn.js";const H={class:"mt-4 grid grid-cols-3 gap-4"},O={class:"col-span-3 sm:col-span-2"},R={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3"},x={class:"col-span-3"},C={class:"col-span-3"},N={key:0,class:"col-span-3"},I={class:"col-span-3"},M={name:"SiteBlockForm"},D=Object.assign(M,{setup(v){S(),j();const n=y("$trans"),a={name:"",isSlider:!1,title:"",subTitle:"",content:"",menu:null,url:""},b="site/block/",s=q(b),m=k({menus:[]}),l=k({...a}),B=d=>{Object.assign(m,d)},U=d=>{var e;Object.assign(a,{...d,menu:(e=d.menu)==null?void 0:e.uuid}),Object.assign(l,E(a))};return(d,e)=>{const p=i("BaseInput"),$=i("BaseSwitch"),F=i("BaseSelect"),P=i("MdEditor"),T=i("FormAction");return c(),A(T,{"pre-requisites":!0,onSetPreRequisites:B,"keep-adding":!1,"init-url":b,"init-form":a,form:l,"set-form":U,redirect:"SiteBlock"},{default:V(()=>[u("div",H,[u("div",O,[r(p,{type:"text",modelValue:l.name,"onUpdate:modelValue":e[0]||(e[0]=t=>l.name=t),name:"name",label:o(n)("site.block.props.name"),error:o(s).name,"onUpdate:error":e[1]||(e[1]=t=>o(s).name=t),autofocus:""},null,8,["modelValue","label","error"])]),u("div",R,[r($,{vertical:"",modelValue:l.isSlider,"onUpdate:modelValue":e[2]||(e[2]=t=>l.isSlider=t),name:"isSlider",label:o(n)("global.is",{attribute:o(n)("site.block.props.slider")}),error:o(s).isSlider,"onUpdate:error":e[3]||(e[3]=t=>o(s).isSlider=t)},null,8,["modelValue","label","error"])]),l.isSlider?f("",!0):(c(),_(g,{key:0},[u("div",w,[r(p,{type:"text",modelValue:l.title,"onUpdate:modelValue":e[4]||(e[4]=t=>l.title=t),name:"title",label:o(n)("site.block.props.title"),error:o(s).title,"onUpdate:error":e[5]||(e[5]=t=>o(s).title=t)},null,8,["modelValue","label","error"])]),u("div",x,[r(p,{type:"text",modelValue:l.subTitle,"onUpdate:modelValue":e[6]||(e[6]=t=>l.subTitle=t),name:"subTitle",label:o(n)("site.block.props.sub_title"),error:o(s).subTitle,"onUpdate:error":e[7]||(e[7]=t=>o(s).subTitle=t)},null,8,["modelValue","label","error"])]),u("div",C,[r(F,{modelValue:l.menu,"onUpdate:modelValue":e[8]||(e[8]=t=>l.menu=t),name:"menu",label:o(n)("global.select",{attribute:o(n)("site.menu.menu")}),"label-prop":"name","value-prop":"uuid",options:m.menus,error:o(s).menu,"onUpdate:error":e[9]||(e[9]=t=>o(s).menu=t)},null,8,["modelValue","label","options","error"])]),l.menu?f("",!0):(c(),_("div",N,[r(p,{type:"text",modelValue:l.url,"onUpdate:modelValue":e[10]||(e[10]=t=>l.url=t),name:"url",label:o(n)("site.block.props.url"),error:o(s).url,"onUpdate:error":e[11]||(e[11]=t=>o(s).url=t)},null,8,["modelValue","label","error"])])),u("div",I,[r(P,{placeholder:o(n)("site.block.props.content"),modelValue:l.content,"onUpdate:modelValue":e[12]||(e[12]=t=>l.content=t),error:o(s).content,"onUpdate:error":e[13]||(e[13]=t=>o(s).content=t)},null,8,["placeholder","modelValue","error"])])],64))])]),_:1},8,["form"])}}}),J={name:"SiteBlockAction"},z=Object.assign(J,{setup(v){const n=S();return(a,b)=>{const s=i("PageHeaderAction"),m=i("PageHeader"),l=i("ParentTransition");return c(),_(g,null,[r(m,{title:a.$trans(o(n).meta.trans,{attribute:a.$trans(o(n).meta.label)}),navs:[{label:a.$trans("site.site"),path:"Site"},{label:a.$trans("site.block.block"),path:"SiteBlockList"}]},{default:V(()=>[r(s,{name:"SiteBlock",title:a.$trans("site.block.block"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(l,{appear:"",visibility:!0},{default:V(()=>[r(D)]),_:1})],64)}}});export{z as default};
