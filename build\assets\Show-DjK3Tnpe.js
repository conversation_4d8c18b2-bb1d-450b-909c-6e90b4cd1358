import{i as N,u as R,h as M,j as O,l as q,r as i,a as r,o as d,e as a,w as e,f as t,q as _,b as c,d as z,F as y,v as B,s as o,t as n,y as G,B as U}from"./app-BAwPsakn.js";const J={class:"space-y-4"},K={key:0},Q={key:0},W={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},X={name:"ExamScheduleShow"},ee=Object.assign(X,{setup(Y){N();const x=R(),g=M(),s=O("$trans"),w={},T="exam/schedule/",V=[{key:"subject",label:s("academic.subject.subject"),visibility:!0},{key:"date",label:s("exam.schedule.props.date"),visibility:!0},{key:"assessment",label:s("exam.assessment.assessment"),visibility:!0},{key:"action",label:"",visibility:!0}],u=q({...w}),j=k=>{Object.assign(u,k)};return(k,p)=>{const C=i("PageHeaderAction"),D=i("PageHeader"),b=i("TextMuted"),m=i("ListItemView"),E=i("ListContainerVertical"),v=i("BaseCard"),S=i("BaseButton"),h=i("DataCell"),L=i("DataRow"),I=i("SimpleTable"),P=i("BaseDataView"),A=i("ShowButton"),H=i("DetailLayoutVertical"),$=i("ShowItem"),F=i("ParentTransition");return d(),r(y,null,[a(D,{title:t(s)(t(x).meta.trans,{attribute:t(s)(t(x).meta.label)}),navs:[{label:t(s)("exam.exam"),path:"Exam"},{label:t(s)("exam.schedule.schedule"),path:"ExamScheduleList"}]},{default:e(()=>[a(C,{name:"ExamSchedule",title:t(s)("exam.schedule.schedule"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(F,{appear:"",visibility:!0},{default:e(()=>[a($,{"init-url":T,uuid:t(x).params.uuid,onSetItem:j,onRedirectTo:p[2]||(p[2]=l=>t(g).push({name:"ExamSchedule"}))},{default:e(()=>[u.uuid?(d(),_(H,{key:0},{detail:e(()=>[a(v,{"no-padding":"","no-content-padding":""},{title:e(()=>[o(n(t(s)("global.detail",{attribute:t(s)("exam.schedule.schedule")})),1)]),default:e(()=>[a(E,null,{default:e(()=>[a(m,{label:t(s)("exam.exam")},{default:e(()=>[o(n(u.exam.name)+" ",1),a(b,null,{default:e(()=>{var l,f;return[o(n((f=(l=u.exam.term)==null?void 0:l.division)==null?void 0:f.name),1)]}),_:1})]),_:1},8,["label"]),a(m,{label:t(s)("academic.batch.batch")},{default:e(()=>[o(n(u.batch.course.name)+" ",1),a(b,null,{default:e(()=>[o(n(u.batch.name),1)]),_:1})]),_:1},8,["label"]),a(m,{label:t(s)("exam.grade.grade")},{default:e(()=>[o(n(u.grade.name),1)]),_:1},8,["label"]),a(m,{label:t(s)("exam.assessment.assessment")},{default:e(()=>[o(n(u.assessment.name),1)]),_:1},8,["label"]),a(m,{label:t(s)("exam.observation.observation")},{default:e(()=>{var l;return[o(n((l=u.observation)==null?void 0:l.name),1)]}),_:1},8,["label"]),a(m,{label:t(s)("general.created_at")},{default:e(()=>[o(n(u.createdAt.formatted),1)]),_:1},8,["label"]),a(m,{label:t(s)("general.updated_at")},{default:e(()=>[o(n(u.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[z("div",J,[a(v,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[o(n(t(s)("exam.schedule.schedule")),1)]),action:e(()=>[t(U)("student")&&u.hasForm?(d(),_(S,{key:0,design:"primary",size:"xs",class:"cursor-pointer",onClick:p[0]||(p[0]=l=>t(g).push({name:"ExamScheduleFormSubmission",params:{uuid:u.uuid}}))},{default:e(()=>[o(n(t(s)("exam.schedule.form")),1)]),_:1})):c("",!0)]),footer:e(()=>[a(A,null,{default:e(()=>[t(G)("exam-schedule:edit")?(d(),_(S,{key:0,design:"primary",onClick:p[1]||(p[1]=l=>t(g).push({name:"ExamScheduleEdit",params:{uuid:u.uuid}}))},{default:e(()=>[o(n(t(s)("general.edit")),1)]),_:1})):c("",!0)]),_:1})]),default:e(()=>[u.records.length>0?(d(),_(I,{key:0,header:V},{default:e(()=>[(d(!0),r(y,null,B(u.records,l=>(d(),_(L,{key:l.uuid},{default:e(()=>[a(h,{name:"subject"},{default:e(()=>[o(n(l.subject.name)+" ",1),l.subject.code?(d(),r("span",K,"("+n(l.subject.code)+")",1)):c("",!0),l.hasGrading?(d(),_(b,{key:1,block:""},{default:e(()=>[o("("+n(t(s)("exam.schedule.props.grading"))+")",1)]),_:1})):c("",!0)]),_:2},1024),a(h,{name:"date"},{default:e(()=>[o(n(l.date.formatted)+" ",1),l.startTime.value?(d(),_(b,{key:0,block:""},{default:e(()=>[o(n(l.startTime.formatted)+" ",1),l.endTime.value?(d(),r("span",Q,"- "+n(l.endTime.formatted),1)):c("",!0)]),_:2},1024)):c("",!0)]),_:2},1024),a(h,{name:"assessment"},{default:e(()=>[(d(!0),r(y,null,B(l.assessments,f=>(d(),r("div",null,[o(n(f.name)+" ",1),a(b,null,{default:e(()=>[o(n(f.maxMark),1)]),_:2},1024)]))),256))]),_:2},1024),a(h,{name:"action"})]),_:2},1024))),128))]),_:1})):c("",!0),u.description?(d(),r("dl",W,[a(P,{class:"col-span-1 sm:col-span-2",label:t(s)("exam.schedule.props.description")},{default:e(()=>[o(n(u.description),1)]),_:1},8,["label"])])):c("",!0)]),_:1})])]),_:1})):c("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{ee as default};
