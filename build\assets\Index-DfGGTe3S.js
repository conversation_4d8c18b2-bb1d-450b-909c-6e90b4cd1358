import{u as C,j as U,l as w,H as B,n as R,r as n,q as F,o as $,w as d,d as p,e as r,b as D,s as k,t as v,f as l,i as H,m as S,a as q,F as P,aN as T}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-3 gap-6"},N={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},E={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(V,{emit:m}){const c=C();U("moment");const g=m,_=V,i={date:"",batches:[],status:"all",output:"print"},s=w({...i}),u=B(_.initUrl),o=w({isLoaded:!c.query.batches});return R(async()=>{o.batches=c.query.batches?c.query.batches.split(","):[],o.isLoaded=!0}),(t,a)=>{const h=n("DatePicker"),b=n("BaseSelectSearch"),f=n("CustomCheckbox"),y=n("FilterForm");return $(),F(y,{"init-form":i,multiple:["batches"],form:s,onHide:a[6]||(a[6]=e=>g("hide"))},{default:d(()=>[p("div",A,[p("div",N,[r(h,{modelValue:s.date,"onUpdate:modelValue":a[0]||(a[0]=e=>s.date=e),name:"date",as:"date",label:t.$trans("general.date")},null,8,["modelValue","label"])]),p("div",L,[o.isLoaded?($(),F(b,{key:0,multiple:"",name:"batches",label:t.$trans("global.select",{attribute:t.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":a[1]||(a[1]=e=>s.batches=e),"value-prop":"uuid","init-search":o.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:d(e=>[k(v(e.value.course.nameWithTerm)+" "+v(e.value.name),1)]),listOption:d(e=>[k(v(e.option.course.nameWithTerm)+" "+v(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):D("",!0)]),p("div",W,[r(f,{label:t.$trans("resource.status"),options:[{label:t.$trans("general.all"),value:"all"},{label:t.$trans("resource.submitted"),value:"submitted"},{label:t.$trans("resource.not_submitted"),value:"not_submitted"}],modelValue:s.status,"onUpdate:modelValue":a[2]||(a[2]=e=>s.status=e),error:l(u).status,"onUpdate:error":a[3]||(a[3]=e=>l(u).status=e)},null,8,["label","options","modelValue","error"])]),p("div",j,[r(f,{label:t.$trans("general.action"),options:[{label:t.$trans("general.print"),value:"print"},{label:t.$trans("general.pdf"),value:"pdf"}],modelValue:s.output,"onUpdate:modelValue":a[4]||(a[4]=e=>s.output=e),error:l(u).output,"onUpdate:error":a[5]||(a[5]=e=>l(u).output=e)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},O={name:"ResourceReportDateWiseStudentDiary"},Q=Object.assign(O,{setup(V){const m=C();H();let c=["filter"],g=[];const _="resource/report/",i=S(!0),s=S(!1),u=async()=>{let o="/app/resource/reports/date-wise-student-diary/export",t=m.query;window.open(T(o,t),"_blank").focus()};return R(async()=>{}),(o,t)=>{const a=n("PageHeaderAction"),h=n("PageHeader"),b=n("ParentTransition"),f=n("BaseCard");return $(),q(P,null,[r(h,{title:o.$trans(l(m).meta.label),navs:[{label:o.$trans("resource.resource"),path:"Resource"},{label:o.$trans("resource.report.report"),path:"ResourceReport"}]},{default:d(()=>[r(a,{url:"resource/reports/date-wise-student-diary/",name:"ResourceReportDateWiseStudentDiary",title:o.$trans("resource.report.date_wise_student_diary.date_wise_student_diary"),actions:l(c),"dropdown-actions":l(g),onToggleFilter:t[0]||(t[0]=y=>i.value=!0)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),r(b,{appear:"",visibility:i.value},{default:d(()=>[r(E,{onAfterFilter:u,"init-url":_,onHide:t[1]||(t[1]=y=>i.value=!1)})]),_:1},8,["visibility"]),r(b,{appear:"",visibility:!0},{default:d(()=>[r(f,{"no-padding":"","no-content-padding":"","is-loading":s.value},null,8,["is-loading"])]),_:1})],64)}}});export{Q as default};
