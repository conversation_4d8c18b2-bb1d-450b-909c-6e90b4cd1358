import{u as j,j as E,l as D,H as I,n as O,r as u,q as B,o as m,w as o,d as p,e as a,b as P,s as c,t as s,i as W,y as z,m as G,a as $,f as k,F as T,v as H}from"./app-BAwPsakn.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(h,{emit:y}){const b=j();E("moment");const q=y,F=h,_={codeNumber:"",name:"",batches:[],feeConcession:"",feeConcessionType:"",feeGroup:"",startDate:"",endDate:""},t=D({..._});I(F.initUrl);const d=D({isLoaded:!b.query.batches});return O(async()=>{d.batches=b.query.batches?b.query.batches.split(","):[],d.isLoaded=!0}),(i,e)=>{const N=u("BaseInput"),g=u("BaseSelectSearch"),r=u("BaseSelect"),v=u("DatePicker"),w=u("FilterForm");return m(),B(w,{"init-form":_,multiple:["batches"],form:t,onHide:e[8]||(e[8]=n=>q("hide"))},{default:o(()=>[p("div",J,[p("div",K,[a(N,{type:"text",modelValue:t.codeNumber,"onUpdate:modelValue":e[0]||(e[0]=n=>t.codeNumber=n),name:"codeNumber",label:i.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),p("div",Q,[a(N,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[1]||(e[1]=n=>t.name=n),name:"name",label:i.$trans("contact.props.name")},null,8,["modelValue","label"])]),p("div",X,[d.isLoaded?(m(),B(g,{key:0,multiple:"",name:"batches",label:i.$trans("global.select",{attribute:i.$trans("academic.batch.batch")}),modelValue:t.batches,"onUpdate:modelValue":e[2]||(e[2]=n=>t.batches=n),"value-prop":"uuid","init-search":d.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:o(n=>[c(s(n.value.course.name)+" "+s(n.value.name),1)]),listOption:o(n=>[c(s(n.option.course.nameWithTerm)+" "+s(n.option.name),1)]),_:1},8,["label","modelValue","init-search"])):P("",!0)]),p("div",Y,[a(r,{name:"feeGroup",label:i.$trans("global.select",{attribute:i.$trans("finance.fee_group.fee_group")}),modelValue:t.feeGroup,"onUpdate:modelValue":e[3]||(e[3]=n=>t.feeGroup=n),options:h.preRequisites.feeGroups,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),p("div",Z,[a(r,{name:"feeConcession",label:i.$trans("global.select",{attribute:i.$trans("finance.fee_concession.fee_concession")}),modelValue:t.feeConcession,"onUpdate:modelValue":e[4]||(e[4]=n=>t.feeConcession=n),options:h.preRequisites.feeConcessions,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),p("div",x,[a(r,{name:"feeConcessionType",label:i.$trans("global.select",{attribute:i.$trans("finance.fee_concession.type.type")}),modelValue:t.feeConcessionType,"onUpdate:modelValue":e[5]||(e[5]=n=>t.feeConcessionType=n),options:h.preRequisites.feeConcessionTypes,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),p("div",ee,[a(v,{start:t.startDate,"onUpdate:start":e[6]||(e[6]=n=>t.startDate=n),end:t.endDate,"onUpdate:end":e[7]||(e[7]=n=>t.endDate=n),name:"dateBetween",as:"range",label:i.$trans("finance.fee_concession.given_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ne={key:0,class:"flex justify-between mr-4"},te={name:"FinanceReportFeeConcession"},se=Object.assign(te,{setup(h){const y=j(),b=W();let q=["filter"],F=[];z("finance:export")&&(F=["print","pdf","excel"]);const _="finance/report/",t=G(!1),d=G(!1),i=D({feeGroups:[],feeConcessions:[],feeConcessionTypes:[]}),e=D({headers:[],data:[],meta:{total:0}}),N=async()=>{d.value=!0,await b.dispatch(_+"preRequisite",{name:"fee-concession",params:y.query}).then(r=>{d.value=!1,Object.assign(i,r)}).catch(r=>{d.value=!1})},g=async()=>{d.value=!0,await b.dispatch(_+"fetchReport",{name:"fee-concession",params:y.query}).then(r=>{d.value=!1,Object.assign(e,r)}).catch(r=>{d.value=!1})};return O(async()=>{await N(),await g()}),(r,v)=>{const w=u("PageHeaderAction"),n=u("PageHeader"),R=u("ParentTransition"),f=u("DataCell"),V=u("TextMuted"),A=u("DataRow"),L=u("DataTable"),M=u("BaseCard");return m(),$(T,null,[a(n,{title:r.$trans(k(y).meta.label),navs:[{label:r.$trans("finance.finance"),path:"Finance"},{label:r.$trans("finance.report.report"),path:"FinanceReport"}]},{default:o(()=>[a(w,{url:"finance/reports/fee-concession/",name:"FinanceReportFeeConcession",title:r.$trans("finance.report.fee_concession.fee_concession"),actions:k(q),"dropdown-actions":k(F),headers:e.headers,onToggleFilter:v[0]||(v[0]=l=>t.value=!t.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),a(R,{appear:"",visibility:t.value},{default:o(()=>[a(ae,{onAfterFilter:g,"init-url":_,"pre-requisites":i,onHide:v[1]||(v[1]=l=>t.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),a(R,{appear:"",visibility:!0},{default:o(()=>[a(M,{"no-padding":"","no-content-padding":"","is-loading":d.value},{default:o(()=>[a(L,{header:e.headers,footer:e.footers,meta:e.meta,module:"finance.report.fee_concession",onRefresh:g},{default:o(()=>[(m(!0),$(T,null,H(e.data,l=>(m(),B(A,{key:l.uuid},{default:o(()=>[a(f,{name:"codeNumber"},{default:o(()=>[c(s(l.codeNumber),1)]),_:2},1024),a(f,{name:"name"},{default:o(()=>[c(s(l.name)+" ",1),a(V,{block:""},{default:o(()=>[c(s(l.rollNumber),1)]),_:2},1024)]),_:2},1024),a(f,{name:"fatherName"},{default:o(()=>[c(s(l.fatherName)+" ",1),a(V,{block:""},{default:o(()=>[c(s(l.contactNumber),1)]),_:2},1024)]),_:2},1024),a(f,{name:"course"},{default:o(()=>[c(s(l.courseName)+" ",1),a(V,{block:""},{default:o(()=>[c(s(l.batchName),1)]),_:2},1024)]),_:2},1024),a(f,{name:"installment"},{default:o(()=>[c(s(l.installmentTitle)+" ",1),a(V,{block:""},{default:o(()=>[c(s(l.feeGroupName),1)]),_:2},1024)]),_:2},1024),a(f,{name:"concession"},{default:o(()=>[c(s(l.concessionName)+" ",1),a(V,{block:""},{default:o(()=>[c(s(l.concessionType),1)]),_:2},1024)]),_:2},1024),a(f,{name:"detail",table:""},{default:o(()=>[(m(!0),$(T,null,H(l.records,C=>{var U,S;return m(),$(T,null,[C.concession.value>0?(m(),$("div",ne,[c(s(((U=C.head)==null?void 0:U.name)||((S=C.defaultFeeHead)==null?void 0:S.label)||"-")+" ",1),p("span",null,s(C.concessionGiven.formatted)+" / "+s(C.concession.formatted),1)])):P("",!0)],64)}),256))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{se as default};
