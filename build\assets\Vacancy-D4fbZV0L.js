import{h as $,i as D,g as h,m as L,l as N,n as A,r as g,a,o as n,d as s,e as d,f,s as i,t,w as u,b as p,F as y,v as x}from"./app-BAwPsakn.js";const M={class:"flex justify-end"},T={class:"flex justify-center"},z={href:"/",class:"mb-6"},E=["src"],F={class:"space-y-4"},J={key:0},S={class:"text-2xl font-semibold text-gray-800 dark:text-gray-300"},I={key:0,class:"mt-4"},O={key:1,class:"mt-4 space-y-4"},R=["onClick"],U={class:"px-4 py-2 space-y-2 text-sm text-gray-700 dark:text-gray-400"},q={class:"flex justify-between"},G={class:"flex justify-between"},H={class:"text-xl font-semibold text-gray-700 dark:text-gray-400"},K={class:"italic"},P={class:"flex flex-col sm:flex-row sm:justify-between"},Q={name:"JobVacancy"},Z=Object.assign(Q,{setup(W){const _=$(),v=D(),b=h("layout.display").value=="dark"?h("assets.iconLight"):h("assets.icon"),k="recruitment/",w=L(!1),m=N({vacancies:[]}),V=async()=>{await v.dispatch(k+"getVacancies").then(e=>{m.vacancies=e}).catch(e=>{_.push({name:"Dashboard"})})},j=e=>{let o=[];return e.forEach(l=>{o.push(l.designation)}),o.join(", ")};return A(()=>{V()}),(e,o)=>{const l=g("BaseAlert"),B=g("TextMuted"),C=g("BaseLoader");return n(),a(y,null,[s("div",M,[s("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:o[0]||(o[0]=c=>f(_).push({name:"Dashboard"}))},[o[1]||(o[1]=s("i",{class:"fas fa-home mr-1"},null,-1)),i(" "+t(e.$trans("global.go_to",{attribute:e.$trans("dashboard.home")})),1)])]),s("div",T,[s("a",z,[s("img",{class:"h-16 w-auto",src:f(b),alt:""},null,8,E)])]),d(C,{"is-loading":w.value},{default:u(()=>[s("div",F,[m.vacancies.length?p("",!0):(n(),a("div",J,[d(l,{design:"info",size:"xs"},{default:u(()=>[i(t(e.$trans("recruitment.vacancy.no_vacancy")),1)]),_:1})])),(n(!0),a(y,null,x(m.vacancies,c=>(n(),a("div",null,[s("h1",S,t(c.team),1),c.vacancies.length?p("",!0):(n(),a("div",I,[d(l,{design:"info",size:"xs"},{default:u(()=>[i(t(e.$trans("recruitment.vacancy.no_vacancy")),1)]),_:1})])),c.vacancies.length?(n(),a("div",O,[(n(!0),a(y,null,x(c.vacancies,r=>(n(),a("div",{class:"border border-gray-200 dark:border-gray-700 shadow-xl rounded-md cursor-pointer",onClick:X=>f(_).push({name:"JobVacancyDetail",params:{slug:r.slug}})},[s("div",U,[s("div",q,[s("span",null,"#"+t(r.codeNumber),1)]),s("div",G,[s("h2",H,t(r.title),1)]),s("div",K,t(r.summary),1),s("div",P,[i(t(j(r.records))+" ",1),s("span",null,[i(t(e.$trans("recruitment.vacancy.props.last_application_date"))+" ",1),d(B,null,{default:u(()=>[i(t(r.lastApplicationDate.formatted),1)]),_:2},1024)])])])],8,R))),256))])):p("",!0)]))),256))])]),_:1},8,["is-loading"])],64)}}});export{Z as default};
