import{u as A,l as h,n as X,r as o,q as g,o as r,w as e,d as u,e as s,h as Y,j as Z,y,c as ee,m as L,a as I,b,f as n,F as j,v as V,s as c,t as d}from"./app-BAwPsakn.js";import{F as te}from"./v3-GGBodInF.js";const ae={class:"grid grid-cols-3 gap-6"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},le={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(H,{emit:P}){A();const _=P,k={title:"",startDate:"",endDate:""},m=h({...k});h({});const q=h({isLoaded:!0});return X(async()=>{q.isLoaded=!0}),(D,i)=>{const $=o("BaseInput"),C=o("DatePicker"),F=o("FilterForm");return r(),g(F,{"init-form":k,form:m,multiple:[],onHide:i[3]||(i[3]=p=>_("hide"))},{default:e(()=>[u("div",ae,[u("div",ne,[s($,{type:"text",modelValue:m.title,"onUpdate:modelValue":i[0]||(i[0]=p=>m.title=p),name:"title",label:D.$trans("gallery.props.title")},null,8,["modelValue","label"])]),u("div",se,[s(C,{start:m.startDate,"onUpdate:start":i[1]||(i[1]=p=>m.startDate=p),end:m.endDate,"onUpdate:end":i[2]||(i[2]=p=>m.endDate=p),name:"dateBetween",as:"range",label:D.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},oe={class:"mt-4 grid grid-cols-3 gap-4"},re=["onClick"],ie={class:"relative h-72 flex border border-gray-200 dark:border-gray-700 shadow-lg"},ue={class:"relative overflow-hidden w-full h-full"},de=["src"],me={class:"absolute py-1 bottom-0 w-full bg-black opacity-70 flex items-center justify-center rounded-b-lg"},ce={class:"px-4 text-center truncate text-gray-50"},pe={class:"mt-1 block text-gray-50 text-xs"},fe={name:"GalleryList"},ye=Object.assign(fe,{setup(H){const P=A(),_=Y(),k=Z("emitter");let m=["filter"];y("gallery:config")&&m.push("config"),y("gallery:create")&&m.unshift("create");let q=[];const D="gallery/",i=ee(()=>P.query.view||"card"),$=L(!1),C=L(!1),F=h({}),p=h({images:[]}),v=h({}),S=l=>{p.images=l.images.map(t=>t.url),C.value=!C.value},T=()=>{p.images=[]},x=()=>{},E=l=>{Object.assign(v,l)},M=l=>{Object.assign(F,l)};return(l,t)=>{const R=o("BaseButton"),O=o("PageHeaderAction"),U=o("PageHeader"),G=o("ParentTransition"),w=o("DataCell"),B=o("FloatingMenuItem"),N=o("FloatingMenu"),z=o("DataRow"),J=o("DataTable"),K=o("EmptyCard"),Q=o("Pagination"),W=o("ListItem");return r(),I(j,null,[s(W,{"pre-requisites":!0,onSetPreRequisites:M,"init-url":D,onSetItems:E},{header:e(()=>[s(U,{title:l.$trans("gallery.gallery"),navs:[{label:l.$trans("gallery.gallery"),path:"Gallery"}]},{default:e(()=>[s(O,{url:"galleries/",name:"Gallery",title:l.$trans("gallery.gallery"),actions:n(m),"dropdown-actions":n(q),headers:v.headers,onToggleFilter:t[2]||(t[2]=a=>$.value=!$.value)},{after:e(()=>[n(y)("gallery:edit")&&i.value!="list"?(r(),g(R,{key:0,design:"white",onClick:t[0]||(t[0]=a=>n(_).push({query:{view:"list"}}))},{default:e(()=>t[8]||(t[8]=[u("i",{class:"fas fa-list"},null,-1)])),_:1})):b("",!0),n(y)("gallery:edit")&&i.value!="card"?(r(),g(R,{key:1,design:"white",onClick:t[1]||(t[1]=a=>n(_).push({query:{view:"card"}}))},{default:e(()=>t[9]||(t[9]=[u("i",{class:"fas fa-image"},null,-1)])),_:1})):b("",!0)]),_:1},8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"])]),filter:e(()=>[s(G,{appear:"",visibility:$.value},{default:e(()=>[s(le,{"pre-requisites":F,onRefresh:t[3]||(t[3]=a=>n(k).emit("listItems")),onHide:t[4]||(t[4]=a=>$.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[i.value=="list"?(r(),g(G,{key:0,appear:"",visibility:!0},{default:e(()=>[s(J,{header:v.headers,meta:v.meta,module:"gallery",onRefresh:t[6]||(t[6]=a=>n(k).emit("listItems"))},{actionButton:e(()=>[n(y)("gallery:create")?(r(),g(R,{key:0,onClick:t[5]||(t[5]=a=>n(_).push({name:"GalleryCreate"}))},{default:e(()=>[c(d(l.$trans("global.add",{attribute:l.$trans("gallery.gallery")})),1)]),_:1})):b("",!0)]),default:e(()=>[(r(!0),I(j,null,V(v.data,a=>(r(),g(z,{key:a.uuid,onDoubleClick:f=>n(_).push({name:"GalleryShow",params:{uuid:a.uuid}})},{default:e(()=>[s(w,{name:"title"},{default:e(()=>[c(d(a.titleExcerpt),1)]),_:2},1024),s(w,{name:"type"},{default:e(()=>[c(d(a.type.label),1)]),_:2},1024),s(w,{name:"date"},{default:e(()=>[c(d(a.date.formatted),1)]),_:2},1024),s(w,{name:"imagesCount"},{default:e(()=>[c(d(a.imagesCount),1)]),_:2},1024),s(w,{name:"createdAt"},{default:e(()=>[c(d(a.createdAt.formatted),1)]),_:2},1024),s(w,{name:"action"},{default:e(()=>[s(N,null,{default:e(()=>[s(B,{icon:"fas fa-arrow-circle-right",onClick:f=>n(_).push({name:"GalleryShow",params:{uuid:a.uuid}})},{default:e(()=>[c(d(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(y)("gallery:edit")?(r(),g(B,{key:0,icon:"fas fa-edit",onClick:f=>n(_).push({name:"GalleryEdit",params:{uuid:a.uuid}})},{default:e(()=>[c(d(l.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),n(y)("gallery:create")?(r(),g(B,{key:1,icon:"fas fa-copy",onClick:f=>n(_).push({name:"GalleryDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[c(d(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),n(y)("gallery:delete")?(r(),g(B,{key:2,icon:"fas fa-trash",onClick:f=>n(k).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[c(d(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})):b("",!0),i.value=="card"?(r(),g(G,{key:1,appear:"",visibility:!0},{default:e(()=>{var a;return[u("div",oe,[(r(!0),I(j,null,V(v.data,f=>(r(),I("div",{class:"col-span-3 sm:col-span-1 cursor-pointer",onClick:ge=>S(f)},[u("div",ie,[u("div",ue,[u("img",{src:f.thumbnailUrl,class:"w-full h-full object-cover rounded-lg"},null,8,de),u("div",me,[u("div",ce,[c(d(f.title)+" ",1),u("span",pe,d(f.date.formatted),1)])])])])],8,re))),256))]),s(K,{total:(a=v.meta)==null?void 0:a.total},null,8,["total"]),u("div",null,[s(Q,{"card-view":"",transparent:"",meta:v.meta,onRefresh:t[7]||(t[7]=f=>n(k).emit("listItems"))},null,8,["meta"])])]}),_:1})):b("",!0)]),_:1}),s(n(te),{toggler:C.value,slide:1,sources:p.images,"on-close":T,onOpen:x},null,8,["toggler","sources"])],64)}}});export{ye as default};
