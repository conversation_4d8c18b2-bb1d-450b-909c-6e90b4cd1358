import{u as H,l as A,H as L,n as E,r as i,q as v,o as p,w as o,d as g,b as V,f as m,s as d,t as r,e as l,h as Y,i as Z,j as x,m as ee,K as ae,a as U,F as C,v as te}from"./app-BAwPsakn.js";import{i as oe,t as le,a as ne}from"./table-FwhM-Z75.js";const se={class:"grid grid-cols-3 gap-6"},re={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},de={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(T,{emit:h}){const S=H(),w=h,k=T,b={name:"",batch:""},_=A({...b}),u=L(k.initUrl),f=A({batch:"",isLoaded:!S.query.batch});return E(async()=>{f.batch=S.query.batch,f.isLoaded=!0}),(n,t)=>{const F=i("BaseSelectSearch"),B=i("BaseInput"),q=i("FilterForm");return p(),v(q,{"init-form":b,form:_,onHide:t[3]||(t[3]=c=>w("hide"))},{default:o(()=>[g("div",se,[g("div",re,[f.isLoaded?(p(),v(F,{key:0,name:"batch",label:n.$trans("global.select",{attribute:n.$trans("academic.batch.batch")}),modelValue:_.batch,"onUpdate:modelValue":t[0]||(t[0]=c=>_.batch=c),error:m(u).batch,"onUpdate:error":t[1]||(t[1]=c=>m(u).batch=c),"value-prop":"uuid","init-search":f.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:o(c=>[d(r(c.value.course.name)+" "+r(c.value.name),1)]),listOption:o(c=>[d(r(c.option.course.nameWithTerm)+" "+r(c.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):V("",!0)]),g("div",ie,[l(B,{type:"text",modelValue:_.name,"onUpdate:modelValue":t[2]||(t[2]=c=>_.name=c),name:"name",label:n.$trans("student.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},me={class:"p-2"},ue={class:"grid grid-cols-3 gap-6"},ce={class:"col-span-3 sm:col-span-1"},pe={class:"mt-4 grid grid-cols-3 gap-6"},be={key:0,class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},ge={key:0,class:"col-span-3 sm:col-span-1"},he={class:"col-span-3 sm:col-span-1"},_e={class:"col-span-3 sm:col-span-1"},ve={name:"StudentPromotion"},Ve=Object.assign(ve,{setup(T){const h=H();Y();const S=Z();x("emitter");const w={batch:"",name:"",selectAll:!1,students:[],markAsAlumni:!1,period:"",newBatch:"",date:"",assignFee:!1},k="student/promotion/",b=ee(!1),_=A({periods:[]}),u=L(k),f=A({data:[]}),n=A({...w});A({});const t=A({...oe}),F=async()=>{b.value=!0,await S.dispatch(k+"preRequisite").then(s=>{b.value=!1,Object.assign(_,s)}).catch(s=>{b.value=!1})},B=async()=>{b.value=!0,await S.dispatch(k+"fetch",{params:h.query}).then(s=>{b.value=!1,n.batch=h.query.batch,n.name=h.query.name,n.perPage=h.query.perPage,Object.assign(f,s),t.pageItems=s.data.map(a=>a.uuid)}).catch(s=>{b.value=!1})},q=()=>{t.global=!t.global,n.selectAll=t.global},c=async()=>{t.items=[],t.pageItems=[],t.all=!1,n.selectAll=!1,await B()};return ae(()=>[t.items,t.pageItems],([s,a],[P,I])=>{n.students=s,t.all=ne(t)}),E(async()=>{await F(),h.query.batch&&await B()}),(s,a)=>{const P=i("PageHeaderAction"),I=i("PageHeader"),D=i("ParentTransition"),M=i("BaseAlert"),R=i("BaseCard"),G=i("BaseArrayCheckbox"),W=i("BaseCheckbox"),y=i("DataCell"),$=i("TextMuted"),z=i("DataRow"),K=i("DataTable"),O=i("BaseSwitch"),j=i("DatePicker"),J=i("BaseSelect"),Q=i("BaseSelectSearch"),X=i("FormAction");return p(),U(C,null,[l(I,{title:s.$trans(m(h).meta.label),navs:[{label:s.$trans("student.student"),path:"Student"}]},{default:o(()=>[l(P)]),_:1},8,["title","navs"]),l(D,{appear:"",visibility:!0},{default:o(()=>[l(de,{onAfterFilter:B,"init-url":k})]),_:1}),f.data.length==0?(p(),v(R,{key:0,"no-padding":"","no-content-padding":"","is-loading":b.value},{title:o(()=>[d(r(s.$trans("student.promotion.promotion")),1)]),default:o(()=>[g("div",me,[l(M,{size:"xs",design:"error"},{default:o(()=>[d(r(s.$trans("general.errors.record_not_found")),1)]),_:1})])]),_:1},8,["is-loading"])):V("",!0),f.data.length>0?(p(),v(D,{key:1,appear:"",visibility:!0},{default:o(()=>[l(K,{onToggleSelectAll:a[2]||(a[2]=e=>t.items=m(le)(e,t)),onToggleGlobalSelect:q,selected:t,header:f.headers,meta:f.meta,module:"student.promotion",onRefresh:B},{actionButton:o(()=>a[15]||(a[15]=[])),default:o(()=>[(p(!0),U(C,null,te(f.data,e=>(p(),v(z,{key:e.uuid},{default:o(()=>[l(y,{name:"selectAll"},{default:o(()=>[t.global?V("",!0):(p(),v(G,{key:0,items:t.items,"onUpdate:items":a[0]||(a[0]=N=>t.items=N),value:e.uuid},null,8,["items","value"])),t.global?(p(),v(W,{key:1,modelValue:t.global,"onUpdate:modelValue":a[1]||(a[1]=N=>t.global=N)},null,8,["modelValue"])):V("",!0)]),_:2},1024),l(y,{name:"codeNumber"},{default:o(()=>[d(r(e.codeNumber)+" ",1),l($,{block:""},{default:o(()=>[d(r(e.joiningDate.formatted),1)]),_:2},1024)]),_:2},1024),l(y,{name:"name"},{default:o(()=>[d(r(e.name)+" ",1),l($,{block:""},{default:o(()=>[d(r(e.gender.label),1)]),_:2},1024)]),_:2},1024),l(y,{name:"course"},{default:o(()=>[d(r(e.courseName)+" ",1),l($,{block:""},{default:o(()=>[d(r(e.batchName),1)]),_:2},1024)]),_:2},1024),l(y,{name:"birthDate"},{default:o(()=>[d(r(e.birthDate.formatted),1)]),_:2},1024),l(y,{name:"contactNumber"},{default:o(()=>[d(r(e.contactNumber)+" ",1),l($,{block:""},{default:o(()=>[d(r(e.email),1)]),_:2},1024)]),_:2},1024),l(y,{name:"parent"},{default:o(()=>[d(r(e.fatherName)+" ",1),l($,{block:""},{default:o(()=>[d(r(e.motherName),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["selected","header","meta"])]),_:1})):V("",!0),t.items.length>0?(p(),v(D,{key:2,appear:"",visibility:!0},{default:o(()=>[l(R,{"is-loading":b.value},{title:o(()=>[d(r(s.$trans("student.promotion.promote")),1)]),default:o(()=>[l(X,{"no-card":"","keep-adding":!1,"init-url":k,action:"promote","init-form":w,form:n,"after-submit":c},{default:o(()=>[g("div",ue,[g("div",ce,[l(O,{vertical:"",modelValue:n.markAsAlumni,"onUpdate:modelValue":a[3]||(a[3]=e=>n.markAsAlumni=e),name:"markAsAlumni",label:s.$trans("global.mark",{attribute:s.$trans("student.alumni.alumni")}),error:m(u).markAsAlumni,"onUpdate:error":a[4]||(a[4]=e=>m(u).markAsAlumni=e)},null,8,["modelValue","label","error"])])]),g("div",pe,[n.markAsAlumni?(p(),U("div",be,[l(j,{modelValue:n.date,"onUpdate:modelValue":a[5]||(a[5]=e=>n.date=e),name:"date",label:s.$trans("student.alumni.props.date"),"no-clear":"",error:m(u).date,"onUpdate:error":a[6]||(a[6]=e=>m(u).date=e)},null,8,["modelValue","label","error"])])):(p(),U(C,{key:1},[g("div",fe,[l(J,{modelValue:n.period,"onUpdate:modelValue":a[7]||(a[7]=e=>n.period=e),name:"period",label:s.$trans("academic.period.period"),"label-prop":"name","value-prop":"uuid",options:_.periods,error:m(u).period,"onUpdate:error":a[8]||(a[8]=e=>m(u).period=e)},null,8,["modelValue","label","options","error"])]),n.period?(p(),U("div",ge,[l(Q,{name:"newBatch",label:s.$trans("global.select",{attribute:s.$trans("academic.batch.batch")}),modelValue:n.newBatch,"onUpdate:modelValue":a[9]||(a[9]=e=>n.newBatch=e),error:m(u).newBatch,"onUpdate:error":a[10]||(a[10]=e=>m(u).newBatch=e),"value-prop":"uuid","search-key":"course_batch","search-action":"academic/batch/list","additional-search-query":{period:n.period}},{selectedOption:o(e=>[d(r(e.value.course.name)+" "+r(e.value.name),1)]),listOption:o(e=>[d(r(e.option.course.nameWithTerm)+" "+r(e.option.name),1)]),_:1},8,["label","modelValue","error","additional-search-query"])])):V("",!0),g("div",he,[l(j,{modelValue:n.date,"onUpdate:modelValue":a[11]||(a[11]=e=>n.date=e),name:"date",label:s.$trans("student.promotion.props.date"),"no-clear":"",error:m(u).date,"onUpdate:error":a[12]||(a[12]=e=>m(u).date=e)},null,8,["modelValue","label","error"])]),g("div",_e,[l(O,{vertical:"",modelValue:n.assignFee,"onUpdate:modelValue":a[13]||(a[13]=e=>n.assignFee=e),name:"assignFee",label:s.$trans("global.assign",{attribute:s.$trans("student.fee.fee")}),error:m(u).assignFee,"onUpdate:error":a[14]||(a[14]=e=>m(u).assignFee=e)},null,8,["modelValue","label","error"])])],64))])]),_:1},8,["form"])]),_:1},8,["is-loading"])]),_:1})):V("",!0)],64)}}});export{Ve as default};
