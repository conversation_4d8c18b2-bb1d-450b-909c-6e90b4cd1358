import{r as e,q as t,o as l,w as i,e as m}from"./app-BAwPsakn.js";const c={name:"EmployeeConfig"},y=Object.assign(c,{setup(p){const o=[{name:"EmployeeConfigGeneral",icon:"fas fa-cogs",label:"config.config"},{name:"EmployeeConfigEmploymentStatus",icon:"fas fa-chevron-right",label:"employee.employment_status.employment_status"},{name:"EmployeeConfigEmploymentType",icon:"fas fa-chevron-right",label:"employee.employment_type.employment_type"},{name:"EmployeeConfigQualificationLevel",icon:"fas fa-chevron-right",label:"employee.qualification_level.qualification_level"},{name:"EmployeeConfigDocumentType",icon:"fas fa-chevron-right",label:"employee.document_type.document_type"},{name:"EmployeeConfigGroup",icon:"fas fa-chevron-right",label:"employee.group.group"}];return(f,s)=>{const n=e("router-view"),a=e("ModuleConfig");return l(),t(a,{navigations:o},{default:i(()=>[m(n)]),_:1})}}});export{y as default};
