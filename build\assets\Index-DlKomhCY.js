import{i as A,u as D,h as I,l as z,m as $,c as V,n as W,r as v,q as M,o as p,w as i,d as o,e as r,a as m,F as U,v as C,t as f,s as j,b as P}from"./app-BAwPsakn.js";const G={class:"bg-secondary w-full rounded-lg px-8 py-4"},J={key:0,class:"text-primary text-center text-xl"},K={key:1,class:"text-dark-primary text-center"},O={class:"mb-4 text-sm font-medium"},Q={class:"mb-4 grid grid-cols-6 gap-6"},X={class:"col-span-6 sm:col-span-2"},Y={class:"grid grid-cols-6 gap-6"},Z={class:"col-span-6 sm:col-span-3"},ee={class:"col-span-6 sm:col-span-3"},se={class:"col-span-6 sm:col-span-2"},te={class:"col-span-6 sm:col-span-2"},ae={class:"col-span-6 sm:col-span-2"},le={class:"grid grid-cols-6 gap-6"},oe={class:"col-span-6 sm:col-span-3"},re={class:"col-span-6 sm:col-span-3"},ne={class:"col-span-6 sm:col-span-2"},de={class:"col-span-6 sm:col-span-2"},ie={class:"col-span-6 sm:col-span-2"},ue={class:"grid grid-cols-6 gap-6"},pe={class:"col-span-6 sm:col-span-3"},me={class:"col-span-6 sm:col-span-3"},ce={__name:"Index",setup(be){const u=A(),w=D(),k=I(),B={dbHost:"localhost",dbPort:"3306",dbName:"",dbUsername:"",dbPassword:"",name:"",email:"",username:"",password:"",passwordConfirmation:"",accessCode:"",registeredEmail:"",checkSymlink:w.query.check_symlink,skipSymlink:w.query.skip_symlink},a=z(B),d=$(null),L=V(()=>u.getters["setup/install/getPreRequisites"]),b=V(()=>u.getters["setup/install/getApp"]),l=V(()=>u.getters["setup/install/getFormErrors"]);$(null),W(async()=>{d.value=!0,await u.dispatch("setup/install/fetchPreRequisite",{}).then(t=>{d.value=!1}).catch(t=>{d.value=!1})});const N=()=>{},_=()=>{},E=()=>{},q=()=>u.getters["setup/install/hasValidPreRequisite"],F=()=>g("db"),H=()=>g("user"),R=()=>g("license"),g=t=>(d.value=!0,u.dispatch("setup/install/validate",{option:t,form:a}).then(e=>(d.value=!1,!0)).catch(e=>(d.value=!1,!1))),T=async()=>{d.value=!0,await u.dispatch("setup/install/finish",{form:a}).then(()=>{d.value=!1,k.push({name:"Login"})}).catch(t=>{d.value=!1})};return(t,e)=>{const x=v("BaseAlert"),c=v("TabContent"),n=v("BaseInput"),h=v("FormWizard"),S=v("BaseLoader");return p(),M(S,{"is-loading":d.value},{default:i(()=>[o("div",G,[r(h,{onComplete:T,"next-button-text":t.$trans("setup.install.next"),"previous-button-text":t.$trans("setup.install.previous"),"finish-button-text":t.$trans("setup.install.finish")},{header:i(()=>[b.value.title?(p(),m("p",J,f(b.value.title+" "+b.value.version),1)):P("",!0),b.value.subtitle?(p(),m("p",K,f(b.value.subtitle),1)):P("",!0)]),default:i(()=>[r(c,{title:t.$trans("setup.install.step",{attribute:1}),description:t.$trans("setup.install.prerequisite_check"),"before-change":q},{default:i(()=>[(p(!0),m(U,null,C(L.value,s=>(p(),m(U,null,[o("h6",O,f(s.title),1),o("div",Q,[(p(!0),m(U,null,C(s.items,y=>(p(),m("div",X,[r(x,{design:y.type},{default:i(()=>[j(f(y.message),1)]),_:2},1032,["design"])]))),256))])],64))),256))]),_:1},8,["title","description"]),r(c,{title:t.$trans("setup.install.step",{attribute:2}),description:t.$trans("setup.install.database_setup"),"before-change":F,"after-load":N},{default:i(()=>[o("div",Y,[o("div",Z,[r(n,{type:"text",modelValue:a.dbHost,"onUpdate:modelValue":e[0]||(e[0]=s=>a.dbHost=s),name:"dbHost",label:t.$trans("setup.install.props.db_host"),error:l.value.dbHost,"onUpdate:error":e[1]||(e[1]=s=>l.value.dbHost=s),autofocus:""},null,8,["modelValue","label","error"])]),o("div",ee,[r(n,{type:"number",modelValue:a.dbPort,"onUpdate:modelValue":e[2]||(e[2]=s=>a.dbPort=s),name:"dbPort",label:t.$trans("setup.install.props.db_port"),error:l.value.dbPort,"onUpdate:error":e[3]||(e[3]=s=>l.value.dbPort=s)},null,8,["modelValue","label","error"])]),o("div",se,[r(n,{type:"text",modelValue:a.dbName,"onUpdate:modelValue":e[4]||(e[4]=s=>a.dbName=s),name:"dbName",label:t.$trans("setup.install.props.db_name"),error:l.value.dbName,"onUpdate:error":e[5]||(e[5]=s=>l.value.dbName=s)},null,8,["modelValue","label","error"])]),o("div",te,[r(n,{type:"text",modelValue:a.dbUsername,"onUpdate:modelValue":e[6]||(e[6]=s=>a.dbUsername=s),name:"dbUsername",label:t.$trans("setup.install.props.db_username"),error:l.value.dbUsername,"onUpdate:error":e[7]||(e[7]=s=>l.value.dbUsername=s)},null,8,["modelValue","label","error"])]),o("div",ae,[r(n,{type:"password",modelValue:a.dbPassword,"onUpdate:modelValue":e[8]||(e[8]=s=>a.dbPassword=s),name:"dbPassword",label:t.$trans("setup.install.props.db_password"),error:l.value.dbPassword,"onUpdate:error":e[9]||(e[9]=s=>l.value.dbPassword=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["title","description"]),r(c,{title:t.$trans("setup.install.step",{attribute:3}),description:t.$trans("setup.install.account_setup"),"before-change":H,"after-load":_},{default:i(()=>[o("div",le,[o("div",oe,[r(n,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[10]||(e[10]=s=>a.name=s),name:"name",label:t.$trans("setup.install.props.name"),error:l.value.name,"onUpdate:error":e[11]||(e[11]=s=>l.value.name=s)},null,8,["modelValue","label","error"])]),o("div",re,[r(n,{type:"email",modelValue:a.email,"onUpdate:modelValue":e[12]||(e[12]=s=>a.email=s),name:"email",label:t.$trans("setup.install.props.email"),error:l.value.email,"onUpdate:error":e[13]||(e[13]=s=>l.value.email=s),autofocus:""},null,8,["modelValue","label","error"])]),o("div",ne,[r(n,{type:"text",modelValue:a.username,"onUpdate:modelValue":e[14]||(e[14]=s=>a.username=s),name:"username",label:t.$trans("setup.install.props.username"),error:l.value.username,"onUpdate:error":e[15]||(e[15]=s=>l.value.username=s)},null,8,["modelValue","label","error"])]),o("div",de,[r(n,{type:"password",modelValue:a.password,"onUpdate:modelValue":e[16]||(e[16]=s=>a.password=s),name:"password",label:t.$trans("setup.install.props.password"),error:l.value.password,"onUpdate:error":e[17]||(e[17]=s=>l.value.password=s)},null,8,["modelValue","label","error"])]),o("div",ie,[r(n,{type:"password",modelValue:a.passwordConfirmation,"onUpdate:modelValue":e[18]||(e[18]=s=>a.passwordConfirmation=s),name:"passwordConfirmation",label:t.$trans("setup.install.props.password_confirmation"),error:l.value.passwordConfirmation,"onUpdate:error":e[19]||(e[19]=s=>l.value.passwordConfirmation=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["title","description"]),r(c,{title:t.$trans("setup.install.step",{attribute:4}),description:t.$trans("setup.install.license_validation"),"before-change":R,"after-load":E},{default:i(()=>[o("div",ue,[o("div",pe,[r(n,{type:"text",modelValue:a.accessCode,"onUpdate:modelValue":e[20]||(e[20]=s=>a.accessCode=s),name:"accessCode",label:t.$trans("setup.license.props.access_code"),error:l.value.accessCode,"onUpdate:error":e[21]||(e[21]=s=>l.value.accessCode=s)},null,8,["modelValue","label","error"])]),o("div",me,[r(n,{type:"email",modelValue:a.registeredEmail,"onUpdate:modelValue":e[22]||(e[22]=s=>a.registeredEmail=s),name:"registeredEmail",label:t.$trans("setup.license.props.registered_email"),error:l.value.registeredEmail,"onUpdate:error":e[23]||(e[23]=s=>l.value.registeredEmail=s),autofocus:""},null,8,["modelValue","label","error"])])])]),_:1},8,["title","description"])]),_:1},8,["next-button-text","previous-button-text","finish-button-text"])])]),_:1},8,["is-loading"])}}};export{ce as default};
