import{i as $,u as v,h as w,l as h,r as t,a as k,o as i,e as r,w as a,f as n,q as l,b as d,y,s as C,t as P,F as H}from"./app-BAwPsakn.js";const I={name:"ExamFormShow"},R=Object.assign(I,{setup(T){$();const s=v(),u=w(),p={},c="exam/form/",m=h({...p}),f=e=>{Object.assign(m,e)};return(e,o)=>{const _=t("PageHeaderAction"),B=t("PageHeader"),g=t("BaseButton"),x=t("ShowButton"),b=t("BaseCard"),E=t("ShowItem"),F=t("ParentTransition");return i(),k(H,null,[r(B,{title:e.$trans(n(s).meta.trans,{attribute:e.$trans(n(s).meta.label)}),navs:[{label:e.$trans("exam.exam"),path:"Exam"},{label:e.$trans("exam.form.form"),path:"ExamFormList"}]},{default:a(()=>[r(_,{name:"ExamForm",title:e.$trans("exam.form.form"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(F,{appear:"",visibility:!0},{default:a(()=>[r(E,{"init-url":c,uuid:n(s).params.uuid,onSetItem:f,onRedirectTo:o[1]||(o[1]=S=>n(u).push({name:"ExamForm"}))},{default:a(()=>[m.uuid?(i(),l(b,{key:0},{title:a(()=>o[2]||(o[2]=[])),footer:a(()=>[r(x,null,{default:a(()=>[n(y)("exam-form:edit")?(i(),l(g,{key:0,design:"primary",onClick:o[0]||(o[0]=S=>n(u).push({name:"ExamFormEdit",params:{uuid:m.uuid}}))},{default:a(()=>[C(P(e.$trans("general.edit")),1)]),_:1})):d("",!0)]),_:1})]),_:1})):d("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{R as default};
