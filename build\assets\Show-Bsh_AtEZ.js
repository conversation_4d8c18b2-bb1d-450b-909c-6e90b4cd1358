import{i as C,u as P,h as U,l as A,r as n,a as f,o as d,e as a,w as t,f as c,q as m,b as H,d as I,s as l,t as s,F as g,v as N}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},F={name:"TodoShow"},E=Object.assign(F,{setup(R){C();const p=P(),_=U(),y={},B="utility/todo/",o=A({...y}),$=e=>{Object.assign(o,e)};return(e,i)=>{const T=n("PageHeaderAction"),w=n("PageHeader"),r=n("BaseDataView"),b=n("BaseBadge"),h=n("BaseButton"),k=n("ShowButton"),v=n("BaseCard"),S=n("ShowItem"),V=n("ParentTransition");return d(),f(g,null,[a(w,{title:e.$trans(c(p).meta.trans,{attribute:e.$trans(c(p).meta.label)}),navs:[{label:e.$trans("utility.utility"),path:"Utility"},{label:e.$trans("utility.todo.todo"),path:"UtilityTodoList"}]},{default:t(()=>[a(T,{name:"UtilityTodo",title:e.$trans("utility.todo.todo"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(V,{appear:"",visibility:!0},{default:t(()=>[a(S,{"init-url":B,uuid:c(p).params.uuid,onSetItem:$,onRedirectTo:i[1]||(i[1]=u=>c(_).push({name:"Todo"}))},{default:t(()=>[o.uuid?(d(),m(v,{key:0},{title:t(()=>[l(s(o.title),1)]),action:t(()=>i[2]||(i[2]=[])),footer:t(()=>[a(k,null,{default:t(()=>[a(h,{design:"primary",onClick:i[0]||(i[0]=u=>c(_).push({name:"UtilityTodoEdit",params:{uuid:o.uuid}}))},{default:t(()=>[l(s(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[I("dl",D,[a(r,{label:e.$trans("utility.todo.due")},{default:t(()=>[l(s(o.due.formatted),1)]),_:1},8,["label"]),a(r,null,{default:t(()=>[o.status?(d(),m(b,{key:0,design:"success",label:e.$trans("utility.todo.completed")},null,8,["label"])):(d(),m(b,{key:1,design:"error",label:e.$trans("utility.todo.incomplete")},null,8,["label"]))]),_:1}),a(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("utility.todo.props.description"),html:""},{default:t(()=>[l(s(o.description),1)]),_:1},8,["label"]),(d(!0),f(g,null,N(o.customFields||[],u=>(d(),m(r,{key:u.uuid,label:u.label},{default:t(()=>[l(s(u.formattedValue),1)]),_:2},1032,["label"]))),128)),a(r,{label:e.$trans("general.created_at")},{default:t(()=>[l(s(o.createdAt.formatted),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.updated_at")},{default:t(()=>[l(s(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):H("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
