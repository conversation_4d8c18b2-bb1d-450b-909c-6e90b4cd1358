import{i as J,u as K,h as Q,c as S,l as x,m as B,r as c,z as W,a as i,o as n,e as p,w as u,f as d,q as X,b as _,d as r,y as T,s as b,t as y,F as k,v as j,ai as Y,x as P,A as z,M as I,aj as w,ak as C,al as Z}from"./app-BAwPsakn.js";const ee={key:0,class:"flex justify-center space-x-4"},te={class:"mt-4 grid grid-cols-3 gap-4"},se={class:"col-span-3 sm:col-span-1"},ae={class:"h-24 flex border border-gray-200 dark:border-gray-700 shadow-lg items-center justify-center"},re={key:0,class:"h-1 w-full bg-gray-200"},le={class:"mt-4 grid grid-cols-3 gap-4"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"relative h-72 flex border border-gray-200 dark:border-gray-700 shadow-lg items-center justify-center"},ie={key:0,class:"absolute z-50 right-2 top-2"},ce={class:"flex gap-2"},ue=["onClick"],de=["onClick"],ge=["src"],pe={name:"GalleryShow"},_e=Object.assign(pe,{setup(fe){const D=J(),h=K(),E=Q(),G={},M="gallery/",A=S(()=>D.getters["config/config"]("system.postMaxSize")),H=S(()=>f.media.filter(e=>e.status==="waiting")),g=B(null);B(!1);const f=x({media:[]}),a=x({...G}),N=e=>{Object.assign(a,e)},O=async e=>{var t;if(typeof((t=g.value)==null?void 0:t.files)!="object"){toast.error($trans("general.errors.invalid_action"));return}for(let s=0;s<g.value.files.length;s++)g.value.files[s].size>A.value?toast.error($trans("general.errors.file_too_large")):f.media.push({uuid:null,file:g.value.files[s],status:"waiting",msg:"",progress:0});V()},V=async()=>{const e=Z.CancelToken;f.media.forEach((t,s)=>{if(t.status==="waiting"){let m=new FormData;m.append("file",t.file),w({url:"/app/galleries/"+a.uuid+"/upload",method:"POST",data:m,upload:!0,onUploadProgress:l=>{t.progress=Math.round(l.loaded*100/l.total)},cancelToken:new e(l=>{t.cancel=l})}).then(l=>{t.status="uploaded",a.images.push(l)}).catch(l=>{if(l===void 0)f.media.splice(s,1);else{let v=C(l);t.status="error",t.msg=v.message}})}})},F=async e=>{e.isCover||await I()&&await w({url:"/app/galleries/"+a.uuid+"/images/"+e.uuid+"/cover",method:"POST"}).then(t=>{a.images.forEach(s=>{s.isCover=s.uuid===e.uuid})}).catch(t=>{C(t)})},R=async e=>{await I()&&await w({url:"/app/galleries/"+a.uuid+"/images/"+e.uuid,method:"DELETE"}).then(t=>{a.images=a.images.filter(s=>s.uuid!==e.uuid)}).catch(t=>{C(t)})};return(e,t)=>{const s=c("PageHeaderAction"),m=c("PageHeader"),l=c("BaseButton"),v=c("BaseCard"),U=c("ShowItem"),L=c("ParentTransition"),$=W("tooltip");return n(),i(k,null,[p(m,{title:e.$trans(d(h).meta.trans,{attribute:e.$trans(d(h).meta.label)}),navs:[{label:e.$trans("gallery.gallery"),path:"Gallery"}]},{default:u(()=>[p(s,{name:"Gallery",title:e.$trans("gallery.gallery"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),p(L,{appear:"",visibility:!0},{default:u(()=>[p(U,{"init-url":M,uuid:d(h).params.uuid,onSetItem:N,onRedirectTo:t[0]||(t[0]=o=>d(E).push({name:"Gallery"}))},{default:u(()=>[a.uuid?(n(),X(v,{key:0},{title:u(()=>[b(y(a.title)+" ("+y(a.type.label)+") ",1)]),default:u(()=>[d(T)("gallery:edit")?(n(),i("div",ee,[p(l,{as:"label"},{default:u(()=>[t[1]||(t[1]=r("i",{class:"fas fa-upload mr-2"},null,-1)),b(" "+y(e.$trans("global.upload",{attribute:e.$trans("gallery.props.image")}))+" ",1),r("input",{multiple:"",accept:"image/jpeg, image/png",ref_key:"file",ref:g,type:"file",onChange:O,name:"images",id:"galleryImage",class:"hidden"},null,544)]),_:1})])):_("",!0),r("div",te,[(n(!0),i(k,null,j(H.value,o=>(n(),i("div",se,[r("div",ae,[r("div",null,[b(y(o.file.name)+" ",1),o.status=="waiting"?(n(),i("div",re,[r("div",{class:P(["h-1",{"bg-info":o.progress<100,"bg-success":o.progress==100}]),style:Y({width:o.progress+"%"})},null,6)])):_("",!0)])])]))),256))]),r("div",le,[(n(!0),i(k,null,j(a.images,o=>(n(),i("div",oe,[r("div",ne,[d(T)("gallery:edit")?(n(),i("div",ie,[r("div",ce,[z(r("i",{class:P(["fas fa-image cursor-pointer text-gray-500",{"text-success":o.isCover}]),onClick:q=>F(o)},null,10,ue),[[$,e.$trans("gallery.make_cover")]]),z(r("i",{class:"far fa-circle-xmark cursor-pointer text-gray-500",onClick:q=>R(o)},null,8,de),[[$,e.$trans("global.delete",{attribute:e.$trans("gallery.props.image")})]])])])):_("",!0),r("img",{src:o.url,class:"px-4 py-2 max-h-full object-cover"},null,8,ge)])]))),256))])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{_e as default};
