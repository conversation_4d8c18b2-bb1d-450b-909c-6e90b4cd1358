import{H as f,l as P,r as i,q as _,o as V,w as p,e as t,d,f as e,s as g,t as U}from"./app-BAwPsakn.js";const b={class:"grid grid-cols-3 gap-6"},y={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},v={class:"col-span-3 sm:col-span-1"},k={name:"UserPassword"},$=Object.assign(k,{setup(B){const w="user/profile/",a=f(w),m={currentPassword:"",newPassword:"",newPasswordConfirmation:""},r=P({...m});return(n,o)=>{const l=i("BaseInput"),u=i("FormAction"),c=i("ParentTransition");return V(),_(c,{appear:"",visibility:!0},{default:p(()=>[t(u,{"no-card":"","init-url":w,"init-form":m,form:r,action:"changePassword",redirect:"UserProfile"},{title:p(()=>[g(U(n.$trans("auth.password.change_password")),1)]),default:p(()=>[d("div",b,[d("div",y,[t(l,{type:"password","leading-icon":"fas fa-key",modelValue:r.currentPassword,"onUpdate:modelValue":o[0]||(o[0]=s=>r.currentPassword=s),name:"currentPassword",label:n.$trans("auth.password.props.current_password"),error:e(a).currentPassword,"onUpdate:error":o[1]||(o[1]=s=>e(a).currentPassword=s),autofocus:""},null,8,["modelValue","label","error"])]),d("div",C,[t(l,{type:"password","leading-icon":"fas fa-key",modelValue:r.newPassword,"onUpdate:modelValue":o[2]||(o[2]=s=>r.newPassword=s),name:"newPassword",label:n.$trans("auth.password.props.new_password"),error:e(a).newPassword,"onUpdate:error":o[3]||(o[3]=s=>e(a).newPassword=s)},null,8,["modelValue","label","error"])]),d("div",v,[t(l,{type:"password","leading-icon":"fas fa-key",modelValue:r.newPasswordConfirmation,"onUpdate:modelValue":o[4]||(o[4]=s=>r.newPasswordConfirmation=s),name:"newPasswordConfirmation",label:n.$trans("auth.password.props.new_password_confirmation"),error:e(a).newPasswordConfirmation,"onUpdate:error":o[5]||(o[5]=s=>e(a).newPasswordConfirmation=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})}}});export{$ as default};
