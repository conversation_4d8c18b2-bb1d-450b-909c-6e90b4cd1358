import{u as x,h as A,j as D,l as w,H,c as L,n as T,J as I,r as p,a as b,o as d,q as y,b as V,e as n,f as r,w as U,F as B,d as u,s as J,t as M,A as z,af as K}from"./app-BAwPsakn.js";const Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={key:0,class:"col-span-3 sm:col-span-1"},h={class:"col-span-3 sm:col-span-1"},_={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},se={name:"GuardianEditLogin"},re=Object.assign(se,{props:{guardian:{type:Object,default(){return{}}}},setup(m){const $=x(),j=A(),q=D("emitter"),k=m,c={username:"",email:"",password:"",passwordConfirmation:"",roles:[]},v="guardian/",F="user/",C=w({}),t=H(v),g=L(()=>{var s,e;return(e=(s=k.guardian)==null?void 0:s.contact)==null?void 0:e.user}),l=w({isValidated:!1,existingUser:null}),o=w({...c}),P=s=>{Object.assign(C,s)},R=s=>{l.isValidated=!0,l.existingUser=s||null,o.username=s==null?void 0:s.username},G=()=>{q.emit("guardianUpdated"),j.push({name:"GuardianShowLogin",params:{uuid:k.guardian.uuid}})};return T(async()=>{var s,e,f;l.isValidated=!!g.value,l.existingUser=g.value,Object.assign(c,{username:(s=g.value)==null?void 0:s.username,email:(e=g.value)==null?void 0:e.email,roles:((f=g.value)==null?void 0:f.roles.map(i=>i.uuid))||[]}),Object.assign(o,I(c))}),(s,e)=>{const f=p("PageHeader"),i=p("BaseInput"),O=p("BaseButton"),S=p("FormAction"),E=p("BaseSelect"),N=p("ParentTransition");return d(),b(B,null,[m.guardian.uuid?(d(),y(f,{key:0,title:s.$trans(r($).meta.trans,{attribute:s.$trans(r($).meta.label)}),navs:[{label:s.$trans("guardian.guardian"),path:"Guardian"},{label:m.guardian.contact.name,path:{name:"GuardianShow",params:{uuid:m.guardian.uuid}}}]},null,8,["title","navs"])):V("",!0),n(N,{appear:"",visibility:!0},{default:U(()=>[m.guardian.uuid?(d(),b(B,{key:0},[l.isValidated?(d(),y(S,{key:1,"no-data-fetch":"","pre-requisites":!0,"pre-requisite-url":F,onSetPreRequisites:P,"init-url":v,action:l.existingUser?"updateUser":"createUser","init-form":c,form:o,"after-submit":G},{default:U(()=>[u("div",X,[u("div",Y,[n(i,{readonly:"",disabled:"",type:"text",modelValue:o.email,"onUpdate:modelValue":e[2]||(e[2]=a=>o.email=a),name:"email",label:s.$trans("contact.login.props.email"),error:r(t).email,"onUpdate:error":e[3]||(e[3]=a=>r(t).email=a)},null,8,["modelValue","label","error"])]),l.existingUser?(d(),b("div",Z,[n(i,{readonly:"",disabled:"",type:"text",modelValue:o.username,"onUpdate:modelValue":e[4]||(e[4]=a=>o.username=a),name:"username",label:s.$trans("contact.login.props.username"),error:r(t).username,"onUpdate:error":e[5]||(e[5]=a=>r(t).username=a)},null,8,["modelValue","label","error"])])):V("",!0),l.existingUser?V("",!0):(d(),b(B,{key:1},[u("div",h,[n(i,{type:"text",modelValue:o.username,"onUpdate:modelValue":e[6]||(e[6]=a=>o.username=a),name:"username",label:s.$trans("contact.login.props.username"),error:r(t).username,"onUpdate:error":e[7]||(e[7]=a=>r(t).username=a)},null,8,["modelValue","label","error"])]),u("div",_,[n(i,{type:"password",modelValue:o.password,"onUpdate:modelValue":e[8]||(e[8]=a=>o.password=a),name:"password",label:s.$trans("contact.login.props.password"),error:r(t).password,"onUpdate:error":e[9]||(e[9]=a=>r(t).password=a)},null,8,["modelValue","label","error"])]),u("div",ee,[n(i,{type:"password",modelValue:o.passwordConfirmation,"onUpdate:modelValue":e[10]||(e[10]=a=>o.passwordConfirmation=a),name:"passwordConfirmation",label:s.$trans("contact.login.props.password_confirmation"),error:r(t).passwordConfirmation,"onUpdate:error":e[11]||(e[11]=a=>r(t).passwordConfirmation=a)},null,8,["modelValue","label","error"])])],64)),z(u("div",ae,[n(E,{modelValue:o.roles,"onUpdate:modelValue":e[12]||(e[12]=a=>o.roles=a),name:"roles",label:s.$trans("contact.login.props.role"),options:C.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:r(t).roles,"onUpdate:error":e[13]||(e[13]=a=>r(t).roles=a)},null,8,["modelValue","label","options","error"])],512),[[K,!1]])])]),_:1},8,["action","form"])):(d(),y(S,{key:0,"no-action-button":"","no-data-fetch":"","init-url":v,action:"confirmUser","init-form":c,form:o,"after-submit":R,"stay-on":"",redirect:{name:"GuardianShowLogin",params:{uuid:m.guardian.uuid}}},{default:U(()=>[u("div",Q,[u("div",W,[n(i,{type:"text",modelValue:o.email,"onUpdate:modelValue":e[0]||(e[0]=a=>o.email=a),name:"email",label:s.$trans("contact.login.props.email"),error:r(t).email,"onUpdate:error":e[1]||(e[1]=a=>r(t).email=a)},null,8,["modelValue","label","error"])])]),n(O,{class:"mt-4",type:"submit"},{default:U(()=>[J(M(s.$trans("general.validate")),1)]),_:1})]),_:1},8,["form","redirect"]))],64)):V("",!0)]),_:1})],64)}}});export{re as default};
