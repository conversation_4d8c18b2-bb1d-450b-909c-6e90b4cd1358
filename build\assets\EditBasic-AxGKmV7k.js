import{u as M,j as K,H as Q,g as c,l as P,n as W,J as X,r as d,a as k,o as p,q,b,e as s,f as o,w as L,d as u,s as C,t as G,I as Y,F as Z}from"./app-BAwPsakn.js";const x={class:"grid grid-cols-3 gap-6"},_={class:"col-span-3 sm:col-span-2"},ee={class:"flex"},ae={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},ye={key:0,class:"col-span-3 sm:col-span-1"},Ve={key:1,class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Ue={name:"ContactEditBasic"},fe=Object.assign(Ue,{props:{contact:{type:Object,default(){return{}}}},setup(N){const B=M(),T=K("emitter"),l=N,U={firstName:"",middleName:"",thirdName:"",lastName:"",fatherName:"",motherName:"",gender:"",birthDate:"",anniversaryDate:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",maritalStatus:"",religion:"",category:"",caste:"",occupation:"",annualIncome:""},h="contact/",n=Q(h),F=c("contact.uniqueIdNumber1Label"),j=c("contact.uniqueIdNumber2Label"),E=c("contact.uniqueIdNumber3Label"),R=c("contact.uniqueIdNumber4Label"),O=c("contact.uniqueIdNumber5Label"),w=c("contact.enableCategoryField"),H=c("contact.enableCasteField"),v=P({genders:[],bloodGroups:[],maritalStatuses:[]}),t=P({...U}),m=P({religion:"",category:"",caste:"",isLoaded:!B.params.uuid}),A=r=>{Object.assign(v,r)};W(async()=>{var r,e,f,y,$,i,D,V,I,g,S;Object.assign(U,{firstName:l.contact.firstName,middleName:l.contact.middleName,thirdName:l.contact.thirdName,lastName:l.contact.lastName,fatherName:l.contact.fatherName,motherName:l.contact.motherName,gender:(r=l.contact.gender)==null?void 0:r.value,birthDate:(e=l.contact.birthDate)==null?void 0:e.value,anniversaryDate:(f=l.contact.anniversaryDate)==null?void 0:f.value,uniqueIdNumber1:l.contact.uniqueIdNumber1,uniqueIdNumber2:l.contact.uniqueIdNumber2,uniqueIdNumber3:l.contact.uniqueIdNumber3,birthPlace:l.contact.birthPlace,bloodGroup:((y=l.contact.bloodGroup)==null?void 0:y.value)||"",maritalStatus:(($=l.contact.maritalStatus)==null?void 0:$.value)||"",nationality:l.contact.nationality,motherTongue:l.contact.motherTongue,religion:((i=l.contact.religion)==null?void 0:i.uuid)||"",category:((D=l.contact.category)==null?void 0:D.uuid)||"",caste:((V=l.contact.caste)==null?void 0:V.uuid)||"",occupation:l.contact.occupation,annualIncome:l.contact.annualIncome}),Object.assign(t,X(U)),m.religion=((I=l.contact.religion)==null?void 0:I.name)||"",m.category=((g=l.contact.category)==null?void 0:g.name)||"",m.caste=((S=l.contact.caste)==null?void 0:S.name)||"",m.isLoaded=!0});const z=()=>{T.emit("contactUpdated")};return(r,e)=>{const f=d("PageHeader"),y=d("BaseLabel"),$=d("NameInput"),i=d("BaseInput"),D=d("BaseRadioGroup"),V=d("DatePicker"),I=d("BaseSelect"),g=d("BaseSelectSearch"),S=d("FormAction"),J=d("ParentTransition");return p(),k(Z,null,[N.contact.uuid?(p(),q(f,{key:0,title:r.$trans(o(B).meta.trans,{attribute:r.$trans(o(B).meta.label)}),navs:[{label:r.$trans("contact.contact"),path:"Contact"},{label:N.contact.name,path:{name:"ContactShow",params:{uuid:N.contact.uuid}}}]},null,8,["title","navs"])):b("",!0),s(J,{appear:"",visibility:!0},{default:L(()=>[N.contact.uuid?(p(),q(S,{key:0,"pre-requisites":!0,onSetPreRequisites:A,"init-url":h,"no-data-fetch":"","init-form":U,form:t,"stay-on":"","after-submit":z,redirect:{name:"ContactShowBasic",params:{uuid:N.contact.uuid}}},{default:L(()=>[u("div",x,[u("div",_,[s(y,null,{default:L(()=>[C(G(r.$trans("contact.props.name")),1)]),_:1}),u("div",ee,[s($,{firstName:t.firstName,"onUpdate:firstName":e[0]||(e[0]=a=>t.firstName=a),middleName:t.middleName,"onUpdate:middleName":e[1]||(e[1]=a=>t.middleName=a),thirdName:t.thirdName,"onUpdate:thirdName":e[2]||(e[2]=a=>t.thirdName=a),lastName:t.lastName,"onUpdate:lastName":e[3]||(e[3]=a=>t.lastName=a),formErrors:o(n),"onUpdate:formErrors":e[4]||(e[4]=a=>Y(n)?n.value=a:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),u("div",ae,[s(i,{type:"text",modelValue:t.fatherName,"onUpdate:modelValue":e[5]||(e[5]=a=>t.fatherName=a),name:"fatherName",label:r.$trans("contact.props.father_name"),error:o(n).fatherName,"onUpdate:error":e[6]||(e[6]=a=>o(n).fatherName=a)},null,8,["modelValue","label","error"])]),u("div",te,[s(i,{type:"text",modelValue:t.motherName,"onUpdate:modelValue":e[7]||(e[7]=a=>t.motherName=a),name:"motherName",label:r.$trans("contact.props.mother_name"),error:o(n).motherName,"onUpdate:error":e[8]||(e[8]=a=>o(n).motherName=a)},null,8,["modelValue","label","error"])]),u("div",oe,[s(y,null,{default:L(()=>[C(G(r.$trans("contact.props.gender")),1)]),_:1}),s(D,{"top-margin":"",options:v.genders,name:"gender",modelValue:t.gender,"onUpdate:modelValue":e[9]||(e[9]=a=>t.gender=a),error:o(n).gender,"onUpdate:error":e[10]||(e[10]=a=>o(n).gender=a),horizontal:""},null,8,["options","modelValue","error"])]),u("div",ne,[s(V,{modelValue:t.birthDate,"onUpdate:modelValue":e[11]||(e[11]=a=>t.birthDate=a),name:"birthDate",label:r.$trans("contact.props.birth_date"),"no-clear":"",error:o(n).birthDate,"onUpdate:error":e[12]||(e[12]=a=>o(n).birthDate=a)},null,8,["modelValue","label","error"])]),u("div",re,[s(V,{modelValue:t.anniversaryDate,"onUpdate:modelValue":e[13]||(e[13]=a=>t.anniversaryDate=a),name:"anniversaryDate",label:r.$trans("contact.props.anniversary_date"),"no-clear":"",error:o(n).anniversaryDate,"onUpdate:error":e[14]||(e[14]=a=>o(n).anniversaryDate=a)},null,8,["modelValue","label","error"])]),u("div",le,[s(i,{type:"text",modelValue:t.uniqueIdNumber1,"onUpdate:modelValue":e[15]||(e[15]=a=>t.uniqueIdNumber1=a),name:"uniqueIdNumber1",label:o(F),error:o(n).uniqueIdNumber1,"onUpdate:error":e[16]||(e[16]=a=>o(n).uniqueIdNumber1=a)},null,8,["modelValue","label","error"])]),u("div",se,[s(i,{type:"text",modelValue:t.uniqueIdNumber2,"onUpdate:modelValue":e[17]||(e[17]=a=>t.uniqueIdNumber2=a),name:"uniqueIdNumber2",label:o(j),error:o(n).uniqueIdNumber2,"onUpdate:error":e[18]||(e[18]=a=>o(n).uniqueIdNumber2=a)},null,8,["modelValue","label","error"])]),u("div",ue,[s(i,{type:"text",modelValue:t.uniqueIdNumber3,"onUpdate:modelValue":e[19]||(e[19]=a=>t.uniqueIdNumber3=a),name:"uniqueIdNumber3",label:o(E),error:o(n).uniqueIdNumber3,"onUpdate:error":e[20]||(e[20]=a=>o(n).uniqueIdNumber3=a)},null,8,["modelValue","label","error"])]),u("div",ie,[s(i,{type:"text",modelValue:t.uniqueIdNumber4,"onUpdate:modelValue":e[21]||(e[21]=a=>t.uniqueIdNumber4=a),name:"uniqueIdNumber4",label:o(R),error:o(n).uniqueIdNumber4,"onUpdate:error":e[22]||(e[22]=a=>o(n).uniqueIdNumber4=a)},null,8,["modelValue","label","error"])]),u("div",me,[s(i,{type:"text",modelValue:t.uniqueIdNumber5,"onUpdate:modelValue":e[23]||(e[23]=a=>t.uniqueIdNumber5=a),name:"uniqueIdNumber5",label:o(O),error:o(n).uniqueIdNumber5,"onUpdate:error":e[24]||(e[24]=a=>o(n).uniqueIdNumber5=a)},null,8,["modelValue","label","error"])]),u("div",de,[s(i,{type:"text",modelValue:t.birthPlace,"onUpdate:modelValue":e[25]||(e[25]=a=>t.birthPlace=a),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:o(n).birthPlace,"onUpdate:error":e[26]||(e[26]=a=>o(n).birthPlace=a)},null,8,["modelValue","label","error"])]),u("div",pe,[s(i,{type:"text",modelValue:t.nationality,"onUpdate:modelValue":e[27]||(e[27]=a=>t.nationality=a),name:"nationality",label:r.$trans("contact.props.nationality"),error:o(n).nationality,"onUpdate:error":e[28]||(e[28]=a=>o(n).nationality=a)},null,8,["modelValue","label","error"])]),u("div",ce,[s(i,{type:"text",modelValue:t.motherTongue,"onUpdate:modelValue":e[29]||(e[29]=a=>t.motherTongue=a),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:o(n).motherTongue,"onUpdate:error":e[30]||(e[30]=a=>o(n).motherTongue=a)},null,8,["modelValue","label","error"])]),u("div",be,[s(I,{modelValue:t.bloodGroup,"onUpdate:modelValue":e[31]||(e[31]=a=>t.bloodGroup=a),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:v.bloodGroups,error:o(n).bloodGroup,"onUpdate:error":e[32]||(e[32]=a=>o(n).bloodGroup=a)},null,8,["modelValue","label","options","error"])]),u("div",Ne,[s(I,{modelValue:t.maritalStatus,"onUpdate:modelValue":e[33]||(e[33]=a=>t.maritalStatus=a),name:"maritalStatus",label:r.$trans("contact.props.marital_status"),options:v.maritalStatuses,error:o(n).maritalStatus,"onUpdate:error":e[34]||(e[34]=a=>o(n).maritalStatus=a)},null,8,["modelValue","label","options","error"])]),u("div",ge,[m.isLoaded?(p(),q(g,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:t.religion,"onUpdate:modelValue":e[35]||(e[35]=a=>t.religion=a),error:o(n).religion,"onUpdate:error":e[36]||(e[36]=a=>o(n).religion=a),"label-prop":"name","value-prop":"uuid","init-search":m.religion,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"religion"}},null,8,["label","modelValue","error","init-search"])):b("",!0)]),o(w)?(p(),k("div",ye,[m.isLoaded?(p(),q(g,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:t.category,"onUpdate:modelValue":e[37]||(e[37]=a=>t.category=a),error:o(n).category,"onUpdate:error":e[38]||(e[38]=a=>o(n).category=a),"label-prop":"name","value-prop":"uuid","init-search":m.category,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"member_category"}},null,8,["label","modelValue","error","init-search"])):b("",!0)])):b("",!0),o(H)?(p(),k("div",Ve,[m.isLoaded?(p(),q(g,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:t.caste,"onUpdate:modelValue":e[39]||(e[39]=a=>t.caste=a),error:o(n).caste,"onUpdate:error":e[40]||(e[40]=a=>o(n).caste=a),"label-prop":"name","value-prop":"uuid","init-search":m.caste,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"member_caste"}},null,8,["label","modelValue","error","init-search"])):b("",!0)])):b("",!0),u("div",Ie,[s(i,{type:"text",modelValue:t.occupation,"onUpdate:modelValue":e[41]||(e[41]=a=>t.occupation=a),name:"occupation",label:r.$trans("contact.props.occupation"),error:o(n).occupation,"onUpdate:error":e[42]||(e[42]=a=>o(n).occupation=a)},null,8,["modelValue","label","error"])]),u("div",qe,[s(i,{type:"text",modelValue:t.annualIncome,"onUpdate:modelValue":e[43]||(e[43]=a=>t.annualIncome=a),name:"annualIncome",label:r.$trans("contact.props.annual_income"),error:o(n).annualIncome,"onUpdate:error":e[44]||(e[44]=a=>o(n).annualIncome=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form","redirect"])):b("",!0)]),_:1})],64)}}});export{fe as default};
