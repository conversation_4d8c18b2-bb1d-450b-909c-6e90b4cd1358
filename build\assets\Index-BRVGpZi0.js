import{u as g,h,j as m,r as b,a as i,o as l,e as v,d as t,f as d,F as u,v as w,x,t as o,s as f}from"./app-BAwPsakn.js";const y={class:"divide-y divide-gray-700 dark:divide-gray-200 overflow-hidden rounded-lg dark:bg-dark-header bg-white shadow sm:grid sm:grid-cols-2 sm:gap-px sm:divide-y-0"},k=["onClick"],j={class:"text-xl text-gray-900 dark:text-gray-300 font-semibold"},z={class:"mt-8"},S={class:"text-base font-semibold leading-6 text-gray-800 dark:text-gray-400"},R={class:"mt-2 text-sm text-gray-500"},V={name:"StudentReport"},H=Object.assign(V,{setup(B){const c=g(),_=h(),e=m("$trans"),r=[{name:e("student.report.date_wise_attendance.date_wise_attendance"),title:e("student.report.date_wise_attendance.module_title"),description:e("student.report.date_wise_attendance.module_description"),path:"StudentReportDateWiseAttendance"},{name:e("student.report.batch_wise_attendance.batch_wise_attendance"),title:e("student.report.batch_wise_attendance.module_title"),description:e("student.report.batch_wise_attendance.module_description"),path:"StudentReportBatchWiseAttendance"},{name:e("student.report.subject_wise_attendance.subject_wise_attendance"),title:e("student.report.subject_wise_attendance.module_title"),description:e("student.report.subject_wise_attendance.module_description"),path:"StudentReportSubjectWiseAttendance"}];return(C,n)=>{const p=b("PageHeader");return l(),i(u,null,[v(p,{title:d(e)(d(c).meta.label),navs:[{label:d(e)("student.student"),path:"Student"}]},null,8,["title","navs"]),t("div",y,[(l(),i(u,null,w(r,(s,a)=>t("div",{key:s.name,onClick:M=>d(_).push({name:s.path}),class:x([a===0?"rounded-tl-lg rounded-tr-lg sm:rounded-tr-none":"",a===1?"sm:rounded-tr-lg":"",a===r.length-2?"sm:rounded-bl-lg":"",a===r.length-1?"rounded-bl-lg rounded-br-lg sm:rounded-bl-none":"","group relative dark:bg-dark-header bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 cursor-pointer"])},[t("div",j,o(s.name),1),t("div",z,[t("h3",S,[n[0]||(n[0]=t("span",{class:"absolute inset-0","aria-hidden":"true"},null,-1)),f(" "+o(s.title),1)]),t("p",R,o(s.description),1)]),n[1]||(n[1]=t("span",{class:"pointer-events-none absolute right-6 top-6 text-gray-300 group-hover:text-gray-400","aria-hidden":"true"},[t("svg",{class:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24"},[t("path",{d:"M20 4h1a1 1 0 00-1-1v1zm-1 12a1 1 0 102 0h-2zM8 3a1 1 0 000 2V3zM3.293 19.293a1 1 0 101.414 1.414l-1.414-1.414zM19 4v12h2V4h-2zm1-1H8v2h12V3zm-.707.293l-16 16 1.414 1.414 16-16-1.414-1.414z"})])],-1))],10,k)),64))])],64)}}});export{H as default};
