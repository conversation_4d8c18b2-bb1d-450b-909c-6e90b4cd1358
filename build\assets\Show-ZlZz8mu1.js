import{i as S,u as v,h as A,l as D,r as d,a as P,o,e as r,w as a,f as p,q as s,b as c,d as T,s as n,t as l,y as V,F as H}from"./app-BAwPsakn.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"FinanceLedgerShow"},M=Object.assign(R,{setup(j){S();const f=v(),g=A(),_={},k="finance/ledger/",e=D({..._}),y=t=>{Object.assign(e,t)};return(t,m)=>{const h=d("PageHeaderAction"),B=d("PageHeader"),u=d("BaseDataView"),$=d("LedgerBalance"),i=d("TextMuted"),w=d("BaseButton"),C=d("ShowButton"),L=d("BaseCard"),N=d("ShowItem"),F=d("ParentTransition");return o(),P(H,null,[r(B,{title:t.$trans(p(f).meta.trans,{attribute:t.$trans(p(f).meta.label)}),navs:[{label:t.$trans("finance.finance"),path:"Finance"},{label:t.$trans("finance.ledger.ledger"),path:"FinanceLedgerList"}]},{default:a(()=>[r(h,{name:"FinanceLedger",title:t.$trans("finance.ledger.ledger"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(F,{appear:"",visibility:!0},{default:a(()=>[r(N,{"init-url":k,uuid:p(f).params.uuid,onSetItem:y,onRedirectTo:m[1]||(m[1]=b=>p(g).push({name:"FinanceLedger"}))},{default:a(()=>[e.uuid?(o(),s(L,{key:0},{title:a(()=>[n(l(e.name),1)]),footer:a(()=>[r(C,null,{default:a(()=>[p(V)("ledger-type:edit")&&!e.isDefault?(o(),s(w,{key:0,design:"primary",onClick:m[0]||(m[0]=b=>p(g).push({name:"FinanceLedgerEdit",params:{uuid:e.uuid}}))},{default:a(()=>[n(l(t.$trans("general.edit")),1)]),_:1})):c("",!0)]),_:1})]),default:a(()=>[T("dl",I,[r(u,{label:t.$trans("finance.ledger.props.alias")},{default:a(()=>[n(l(e.alias||"-"),1)]),_:1},8,["label"]),r(u,{label:t.$trans("finance.ledger_type.ledger_type")},{default:a(()=>{var b;return[n(l(((b=e.type)==null?void 0:b.name)||"-"),1)]}),_:1},8,["label"]),r(u,{label:t.$trans("finance.ledger.props.opening_balance")},{default:a(()=>[n(l(e.openingBalance.formatted),1)]),_:1},8,["label"]),r(u,{label:t.$trans("finance.ledger.props.net_balance")},{default:a(()=>[r($,{highlight:"",ledger:e},null,8,["ledger"])]),_:1},8,["label"]),e.type.hasContact?(o(),s(u,{key:0,label:t.$trans("finance.ledger.props.contact")},{default:a(()=>[n(l(e.contactNumber)+" ",1),e.email?(o(),s(i,{key:0,block:""},{default:a(()=>[n(l(e.email),1)]),_:1})):c("",!0),e.addressDisplay?(o(),s(i,{key:1,block:""},{default:a(()=>[n(l(e.addressDisplay),1)]),_:1})):c("",!0)]),_:1},8,["label"])):c("",!0),e.type.hasAccount?(o(),s(u,{key:1,label:t.$trans("finance.account.props.name")},{default:a(()=>[n(l(e.account.name)+" ",1),e.account.number?(o(),s(i,{key:0,block:""},{default:a(()=>[n(l(e.account.number),1)]),_:1})):c("",!0),e.account.bankName?(o(),s(i,{key:1,block:""},{default:a(()=>[n(l(e.account.bankName),1)]),_:1})):c("",!0),e.account.branchName?(o(),s(i,{key:2,block:""},{default:a(()=>[n(l(e.account.branchName),1)]),_:1})):c("",!0),e.account.branchCode?(o(),s(i,{key:3,block:""},{default:a(()=>[n(l(e.account.branchCode),1)]),_:1})):c("",!0),e.account.branchAddress?(o(),s(i,{key:4,block:""},{default:a(()=>[n(l(e.account.branchAddress),1)]),_:1})):c("",!0)]),_:1},8,["label"])):c("",!0),r(u,{class:"col-span-1 sm:col-span-2",label:t.$trans("finance.ledger.props.description")},{default:a(()=>[n(l(e.description),1)]),_:1},8,["label"]),r(u,{label:t.$trans("general.created_at")},{default:a(()=>[n(l(e.createdAt.formatted),1)]),_:1},8,["label"]),r(u,{label:t.$trans("general.updated_at")},{default:a(()=>[n(l(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):c("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
