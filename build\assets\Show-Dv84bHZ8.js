import{i as S,u as v,h as P,l as T,r as n,a as V,o as d,e as t,w as a,f as c,q as p,b as _,d as N,s,t as r,y as H,F as D}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"AcademicCourseInchargeShow"},M=Object.assign(j,{setup(E){S();const u=v(),m=P(),g={},h="academic/courseIncharge/",o=T({...g}),f=e=>{Object.assign(o,e)};return(e,i)=>{const b=n("PageHeaderAction"),B=n("PageHeader"),$=n("TextMuted"),l=n("BaseDataView"),y=n("BaseButton"),C=n("ShowButton"),A=n("BaseCard"),I=n("ShowItem"),k=n("ParentTransition");return d(),V(D,null,[t(B,{title:e.$trans(c(u).meta.trans,{attribute:e.$trans(c(u).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.course.course"),path:"AcademicCourse"},{label:e.$trans("academic.course_incharge.course_incharge"),path:"AcademicCourseInchargeList"}]},{default:a(()=>[t(b,{name:"AcademicCourseIncharge",title:e.$trans("academic.course_incharge.course_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(k,{appear:"",visibility:!0},{default:a(()=>[t(I,{"init-url":h,uuid:c(u).params.uuid,onSetItem:f,onRedirectTo:i[1]||(i[1]=w=>c(m).push({name:"AcademicCourseIncharge"}))},{default:a(()=>[o.uuid?(d(),p(A,{key:0},{title:a(()=>[s(r(o.course.name),1)]),footer:a(()=>[t(C,null,{default:a(()=>[c(H)("course-incharge:edit")?(d(),p(y,{key:0,design:"primary",onClick:i[0]||(i[0]=w=>c(m).push({name:"AcademicCourseInchargeEdit",params:{uuid:o.uuid}}))},{default:a(()=>[s(r(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:a(()=>[N("dl",R,[t(l,{label:e.$trans("employee.employee")},{default:a(()=>[s(r(o.employee.name)+" ",1),t($,{block:""},{default:a(()=>[s(r(o.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),t(l,{label:e.$trans("employee.incharge.props.period")},{default:a(()=>[s(r(o.period),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.incharge.props.remarks")},{default:a(()=>[s(r(o.remarks),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.created_at")},{default:a(()=>[s(r(o.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.updated_at")},{default:a(()=>[s(r(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
