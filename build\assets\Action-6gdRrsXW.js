import{u as m,r as t,a as p,o as l,e,w as n,f as o,F as _}from"./app-BAwPsakn.js";import{_ as d}from"./Form-BYmZZDvP.js";const u={name:"AcademicProgramTypeAction"},A=Object.assign(u,{setup(g){const r=m();return(a,f)=>{const s=t("PageHeaderAction"),c=t("PageHeader"),i=t("ParentTransition");return l(),p(_,null,[e(c,{title:a.$trans(o(r).meta.trans,{attribute:a.$trans(o(r).meta.label)}),navs:[{label:a.$trans("academic.academic"),path:"Academic"},{label:a.$trans("academic.program_type.program_type"),path:"AcademicProgramTypeList"}]},{default:n(()=>[e(s,{name:"AcademicProgramType",title:a.$trans("academic.program_type.program_type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),e(i,{appear:"",visibility:!0},{default:n(()=>[e(d)]),_:1})],64)}}});export{A as default};
