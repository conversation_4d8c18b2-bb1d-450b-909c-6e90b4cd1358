import{u as D,G as _,H as j,l as f,r as l,q as k,o as U,w as p,d as r,e as i,f as s,b as q,s as g,t as b,J as A,a as E,F as R}from"./app-BAwPsakn.js";const L={class:"grid grid-cols-3 gap-6"},P={class:"col-span-3 sm:col-span-1"},C={class:"col-span-2 sm:col-span-2"},w={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"mt-4 grid grid-cols-1 gap-4"},W={class:"col"},X={class:"col"},Y={class:"col"},Z={name:"DisciplineIncidentForm"},h=Object.assign(Z,{setup(F){const u=D(),d={category:"",title:"",date:"",nature:"",severity:"",reportedBy:"",student:"",description:"",action:"",media:[],mediaUpdated:!1,mediaToken:_(),mediaHash:[]},V="discipline/incident/",a=j(V),m=f({categories:[]}),n=f({...d}),y=f({student:"",isLoaded:!u.params.uuid}),N=o=>{Object.assign(m,o)},H=()=>{n.mediaToken=_(),n.mediaHash=[]},S=o=>{var t,c,v;Object.assign(d,{...o,category:((t=o.category)==null?void 0:t.uuid)||"",date:o.date.value,nature:((c=o.nature)==null?void 0:c.value)||"",severity:((v=o.severity)==null?void 0:v.value)||"",student:o.student}),Object.assign(n,A(d)),y.student=o.student,y.isLoaded=!0};return(o,t)=>{const c=l("BaseSelect"),v=l("BaseInput"),I=l("DatePicker"),$=l("TextMuted"),T=l("BaseSelectSearch"),B=l("BaseEditor"),O=l("MediaUpload"),M=l("FormAction");return U(),k(M,{"pre-requisites":!0,onSetPreRequisites:N,"init-url":V,"init-form":d,form:n,"set-form":S,redirect:"DisciplineIncident",onResetMediaFiles:H},{default:p(()=>[r("div",L,[r("div",P,[i(c,{modelValue:n.category,"onUpdate:modelValue":t[0]||(t[0]=e=>n.category=e),name:"category",label:o.$trans("discipline.incident.props.category"),options:m.categories,"label-prop":"name","value-prop":"uuid",error:s(a).category,"onUpdate:error":t[1]||(t[1]=e=>s(a).category=e)},null,8,["modelValue","label","options","error"])]),r("div",C,[i(v,{type:"text",modelValue:n.title,"onUpdate:modelValue":t[2]||(t[2]=e=>n.title=e),name:"title",label:o.$trans("discipline.incident.props.title"),error:s(a).title,"onUpdate:error":t[3]||(t[3]=e=>s(a).title=e),autofocus:""},null,8,["modelValue","label","error"])]),r("div",w,[i(c,{modelValue:n.nature,"onUpdate:modelValue":t[4]||(t[4]=e=>n.nature=e),name:"nature",label:o.$trans("discipline.incident.props.nature"),options:m.natures,error:s(a).nature,"onUpdate:error":t[5]||(t[5]=e=>s(a).nature=e)},null,8,["modelValue","label","options","error"])]),r("div",G,[i(c,{modelValue:n.severity,"onUpdate:modelValue":t[6]||(t[6]=e=>n.severity=e),name:"severity",label:o.$trans("discipline.incident.props.severity"),options:m.severities,error:s(a).severity,"onUpdate:error":t[7]||(t[7]=e=>s(a).severity=e)},null,8,["modelValue","label","options","error"])]),r("div",J,[i(I,{as:"date",modelValue:n.date,"onUpdate:modelValue":t[8]||(t[8]=e=>n.date=e),name:"date",label:o.$trans("discipline.incident.props.date"),"no-clear":"",error:s(a).date,"onUpdate:error":t[9]||(t[9]=e=>s(a).date=e)},null,8,["modelValue","label","error"])]),r("div",z,[y.isLoaded?(U(),k(T,{key:0,name:"student",label:o.$trans("student.student"),placeholder:o.$trans("global.select",{attribute:o.$trans("student.student")}),modelValue:n.student,"onUpdate:modelValue":t[10]||(t[10]=e=>n.student=e),error:s(a).student,"onUpdate:error":t[11]||(t[11]=e=>s(a).student=e),"value-prop":"uuid","init-search":y.student,"search-key":"name","search-action":"student/summary"},{selectedOption:p(e=>[g(b(e.value.name)+" ("+b(e.value.courseName+" "+e.value.batchName)+") ",1),i($,null,{default:p(()=>[g(b(e.value.codeNumber),1)]),_:2},1024)]),listOption:p(e=>[g(b(e.option.name)+" ("+b(e.option.courseName+" "+e.option.batchName)+") ",1),i($,null,{default:p(()=>[g(b(e.option.codeNumber),1)]),_:2},1024)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):q("",!0)]),r("div",K,[i(v,{type:"text",modelValue:n.reportedBy,"onUpdate:modelValue":t[12]||(t[12]=e=>n.reportedBy=e),name:"reportedBy",label:o.$trans("discipline.incident.props.reported_by"),error:s(a).reportedBy,"onUpdate:error":t[13]||(t[13]=e=>s(a).reportedBy=e)},null,8,["modelValue","label","error"])])]),r("div",Q,[r("div",W,[i(B,{modelValue:n.description,"onUpdate:modelValue":t[14]||(t[14]=e=>n.description=e),name:"description",edit:!!s(u).params.uuid,label:o.$trans("discipline.incident.props.description"),error:s(a).description,"onUpdate:error":t[15]||(t[15]=e=>s(a).description=e)},null,8,["modelValue","edit","label","error"])]),r("div",X,[i(B,{modelValue:n.action,"onUpdate:modelValue":t[16]||(t[16]=e=>n.action=e),name:"action",edit:!!s(u).params.uuid,label:o.$trans("discipline.incident.props.action"),error:s(a).action,"onUpdate:error":t[17]||(t[17]=e=>s(a).action=e)},null,8,["modelValue","edit","label","error"])]),r("div",Y,[i(O,{multiple:"",label:o.$trans("general.file"),module:"incident",media:n.media,"media-token":n.mediaToken,onIsUpdated:t[18]||(t[18]=e=>n.mediaUpdated=!0),onSetHash:t[19]||(t[19]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),x={name:"DisciplineIncidentAction"},te=Object.assign(x,{setup(F){const u=D();return(d,V)=>{const a=l("PageHeaderAction"),m=l("PageHeader"),n=l("ParentTransition");return U(),E(R,null,[i(m,{title:d.$trans(s(u).meta.trans,{attribute:d.$trans(s(u).meta.label)}),navs:[{label:d.$trans("discipline.discipline"),path:"Discipline"},{label:d.$trans("discipline.incident.incident"),path:"DisciplineIncidentList"}]},{default:p(()=>[i(a,{name:"DisciplineIncident",title:d.$trans("discipline.incident.incident"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(n,{appear:"",visibility:!0},{default:p(()=>[i(h)]),_:1})],64)}}});export{te as default};
