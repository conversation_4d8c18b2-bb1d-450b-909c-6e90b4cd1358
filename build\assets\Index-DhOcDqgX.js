import{H as c,l as k,r as d,q as V,o as y,w as f,e as r,d as i,f as n}from"./app-BAwPsakn.js";const w={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-2"},v={class:"col-span-3 sm:col-span-2"},C={class:"col-span-3 sm:col-span-2"},$={class:"col-span-3 sm:col-span-2"},_={class:"col-span-3 sm:col-span-2"},B={class:"col-span-3 sm:col-span-2"},F={name:"ConfigSocialNetworkForm"},A=Object.assign(F,{setup(H){const u="config/",t=c(u),p={facebook:"",twitter:"",google:"",linkedin:"",youtube:"",github:"",type:"social_network"},l=k({...p});return(a,o)=>{const g=d("CardHeader"),s=d("BaseInput"),m=d("FormAction"),b=d("ConfigPage");return y(),V(b,null,{default:f(()=>[r(m,{"no-card":"","init-url":u,"data-fetch":"social_network","init-form":p,form:l,action:"store","stay-on":"",redirect:"Config"},{default:f(()=>[r(g,{first:"",title:a.$trans("config.social.social_config"),description:a.$trans("config.social.social_info")},null,8,["title","description"]),i("div",w,[i("div",U,[r(s,{type:"text","leading-icon":"fab fa-facebook",modelValue:l.facebook,"onUpdate:modelValue":o[0]||(o[0]=e=>l.facebook=e),name:"facebook",label:a.$trans("config.social.props.facebook"),error:n(t).facebook,"onUpdate:error":o[1]||(o[1]=e=>n(t).facebook=e),autofocus:""},null,8,["modelValue","label","error"])]),i("div",v,[r(s,{type:"text","leading-icon":"fab fa-twitter",modelValue:l.twitter,"onUpdate:modelValue":o[2]||(o[2]=e=>l.twitter=e),name:"twitter",label:a.$trans("config.social.props.twitter"),error:n(t).twitter,"onUpdate:error":o[3]||(o[3]=e=>n(t).twitter=e)},null,8,["modelValue","label","error"])]),i("div",C,[r(s,{type:"text","leading-icon":"fab fa-google",modelValue:l.google,"onUpdate:modelValue":o[4]||(o[4]=e=>l.google=e),name:"google",label:a.$trans("config.social.props.google"),error:n(t).google,"onUpdate:error":o[5]||(o[5]=e=>n(t).google=e)},null,8,["modelValue","label","error"])]),i("div",$,[r(s,{type:"text","leading-icon":"fab fa-linkedin",modelValue:l.linkedin,"onUpdate:modelValue":o[6]||(o[6]=e=>l.linkedin=e),name:"linkedin",label:a.$trans("config.social.props.linkedin"),error:n(t).linkedin,"onUpdate:error":o[7]||(o[7]=e=>n(t).linkedin=e)},null,8,["modelValue","label","error"])]),i("div",_,[r(s,{type:"text","leading-icon":"fab fa-youtube",modelValue:l.youtube,"onUpdate:modelValue":o[8]||(o[8]=e=>l.youtube=e),name:"youtube",label:a.$trans("config.social.props.youtube"),error:n(t).youtube,"onUpdate:error":o[9]||(o[9]=e=>n(t).youtube=e)},null,8,["modelValue","label","error"])]),i("div",B,[r(s,{type:"text","leading-icon":"fab fa-github",modelValue:l.github,"onUpdate:modelValue":o[10]||(o[10]=e=>l.github=e),name:"github",label:a.$trans("config.social.props.github"),error:n(t).github,"onUpdate:error":o[11]||(o[11]=e=>n(t).github=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})}}});export{A as default};
