import{h as p,r,q as i,o as u,w as o,e as d,f as l,s as c,t as _}from"./app-BAwPsakn.js";const g={__name:"404",setup(m){const s=p();return(e,t)=>{const a=r("BaseButton"),n=r("ErrorPage");return u(),i(n,{type:"404",title:e.$trans("general.errors.404_title"),description:e.$trans("general.errors.404_description")},{default:o(()=>[d(a,{type:"button",onClick:t[0]||(t[0]=f=>l(s).push({name:"Dashboard"}))},{default:o(()=>[c(_(e.$trans("dashboard.dashboard")),1)]),_:1})]),_:1},8,["title","description"])}}};export{g as default};
