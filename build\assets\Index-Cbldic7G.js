import{i as x,H as N,m as S,l as j,n as q,r as s,q as v,o as a,w as r,e as i,a as c,F as y,v as V,x as E,b as H,f as d,d as h,s as M,t as O,J as P}from"./app-BAwPsakn.js";const A={class:"flex items-center"},D={class:"ml-2"},L={key:0,class:"grid grid-cols-3 gap-4 pl-4"},R={name:"ConfigModule"},J=Object.assign(R,{setup($){const C=x(),m="config/",t=N(m),u=S(!1),l={modules:[]},p=j({...l}),U=async()=>{u.value=!0,await C.dispatch(m+"modulePreRequisite").then(n=>{u.value=!1,Object.assign(l,n),Object.assign(p,P(l))}).catch(n=>{u.value=!1})};return q(async()=>{await U()}),(n,_)=>{const B=s("CardHeader"),g=s("BaseSwitch"),F=s("BaseFieldset"),b=s("FormAction"),k=s("ConfigPage");return a(),v(k,null,{action:r(()=>_[0]||(_[0]=[])),default:r(()=>[i(b,{"no-card":"","init-url":m,"init-form":l,form:p,action:"storeModule","stay-on":"",redirect:"Config"},{default:r(()=>[i(B,{first:"",title:n.$trans("config.module.module_config"),description:n.$trans("config.module.module_info")},null,8,["title","description"]),(a(!0),c(y,null,V(p.modules,(o,w)=>(a(),v(F,{class:E({"mt-4":w>0}),key:o.name},{legend:r(()=>[h("div",A,[M(O(o.label)+" ",1),h("span",D,[i(g,{reverse:"",modelValue:o.visibility,"onUpdate:modelValue":e=>o.visibility=e,name:o.name,error:d(t)[o.name],"onUpdate:error":e=>d(t)[o.name]=e},null,8,["modelValue","onUpdate:modelValue","name","error","onUpdate:error"])])])]),default:r(()=>[o.children.length>0&&o.visibility?(a(),c("div",L,[(a(!0),c(y,null,V(o.children,e=>(a(),c("div",{class:"col-span-3 sm:col-span-1",key:e.name},[i(g,{reverse:"",modelValue:e.visibility,"onUpdate:modelValue":f=>e.visibility=f,name:e.name,label:e.label,error:d(t)[e.name],"onUpdate:error":f=>d(t)[e.name]=f},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]))),128))])):H("",!0)]),_:2},1032,["class"]))),128))]),_:1},8,["form"])]),_:1})}}});export{J as default};
