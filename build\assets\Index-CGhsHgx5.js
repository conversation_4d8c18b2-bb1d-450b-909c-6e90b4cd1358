import{u as A,j as O,l as b,H as N,n as U,r as l,q,o as m,w as r,d as u,e as s,h as E,i as I,y as W,m as H,a as F,f as R,F as D,v as P,s as V,t as $}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(c,{emit:f}){A(),O("moment");const v=f,h=c,g={startDate:"",endDate:"",period:"",ledgers:[],paymentMethods:[],pgAccount:""},e=b({...g});N(h.initUrl);const d=b({isLoaded:!0});return U(async()=>{d.isLoaded=!0}),(n,t)=>{const i=l("DatePicker"),_=l("BaseSelect"),y=l("BaseInput"),o=l("FilterForm");return m(),q(o,{"init-form":g,multiple:[],form:e,onHide:t[6]||(t[6]=a=>v("hide"))},{default:r(()=>[u("div",z,[u("div",G,[s(i,{start:e.startDate,"onUpdate:start":t[0]||(t[0]=a=>e.startDate=a),end:e.endDate,"onUpdate:end":t[1]||(t[1]=a=>e.endDate=a),name:"dateBetween",as:"range",label:n.$trans("general.date_between")},null,8,["start","end","label"])]),u("div",J,[s(_,{name:"period",label:n.$trans("global.select",{attribute:n.$trans("academic.period.period")}),modelValue:e.period,"onUpdate:modelValue":t[2]||(t[2]=a=>e.period=a),"label-prop":"name","value-prop":"uuid",options:c.preRequisites.periods},null,8,["label","modelValue","options"])]),u("div",K,[s(_,{multiple:"",name:"ledgers",label:n.$trans("global.select",{attribute:n.$trans("finance.ledger.ledger")}),modelValue:e.ledgers,"onUpdate:modelValue":t[3]||(t[3]=a=>e.ledgers=a),"label-prop":"name","value-prop":"uuid",options:c.preRequisites.ledgers},null,8,["label","modelValue","options"])]),u("div",Q,[s(_,{multiple:"",modelValue:e.paymentMethods,"onUpdate:modelValue":t[4]||(t[4]=a=>e.paymentMethods=a),name:"paymentMethods",label:n.$trans("finance.payment_method.payment_method"),"label-prop":"name","value-prop":"uuid",options:c.preRequisites.paymentMethods},null,8,["modelValue","label","options"])]),u("div",X,[s(y,{type:"text",modelValue:e.pgAccount,"onUpdate:modelValue":t[5]||(t[5]=a=>e.pgAccount=a),name:"pgAccount",label:n.$trans("finance.config.props.pg_account")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Z={name:"FinanceReportHeadWiseFeePayment"},ee=Object.assign(Z,{setup(c){const f=A();E();const v=I();let h=["filter"],g=[];W("finance:export")&&(g=["print","pdf","excel"]);const e="finance/report/",d=H(!1),n=H(!1),t=b({periods:[],ledgers:[],paymentMethods:[]}),i=b({headers:[],data:[],meta:{total:0}}),_=async()=>{n.value=!0,await v.dispatch(e+"preRequisite",{name:"head-wise-fee-payment",params:f.query}).then(o=>{n.value=!1,Object.assign(t,o)}).catch(o=>{n.value=!1})},y=async()=>{n.value=!0,await v.dispatch(e+"fetchReport",{name:"head-wise-fee-payment",params:f.query}).then(o=>{n.value=!1,Object.assign(i,o)}).catch(o=>{n.value=!1})};return U(async()=>{await _(),await y()}),(o,a)=>{const k=l("PageHeaderAction"),M=l("PageHeader"),B=l("ParentTransition"),w=l("DataCell"),j=l("DataRow"),C=l("DataTable"),T=l("BaseCard");return m(),F(D,null,[s(M,{title:o.$trans(R(f).meta.label),navs:[{label:o.$trans("finance.finance"),path:"Finance"},{label:o.$trans("finance.report.report"),path:"FinanceReport"}]},{default:r(()=>[s(k,{url:"finance/reports/head-wise-fee-payment/",name:"FinanceReportHeadWiseFeePayment",title:o.$trans("finance.report.head_wise_fee_payment.head_wise_fee_payment"),actions:R(h),"dropdown-actions":R(g),headers:i.headers,onToggleFilter:a[0]||(a[0]=p=>d.value=!d.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),s(B,{appear:"",visibility:d.value},{default:r(()=>[s(Y,{onAfterFilter:y,"init-url":e,"pre-requisites":t,onHide:a[1]||(a[1]=p=>d.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),s(B,{appear:"",visibility:!0},{default:r(()=>[s(T,{"no-padding":"","no-content-padding":"","is-loading":n.value},{default:r(()=>[s(C,{header:i.headers,"sticky-columns":["date"],footer:i.footers,meta:i.meta,module:"finance.report.head_wise_fee_payment",onRefresh:y},{default:r(()=>[(m(!0),F(D,null,P(i.data,p=>(m(),q(j,{key:p.uuid},{default:r(()=>[s(w,{"sticky-column":"",name:"date"},{default:r(()=>[V($(p.date.formatted),1)]),_:2},1024),(m(!0),F(D,null,P(p.feeHeads,(S,L)=>(m(),q(w,{name:L},{default:r(()=>[V($(S.formatted),1)]),_:2},1032,["name"]))),256)),s(w,{name:"total"},{default:r(()=>[V($(p.total.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{ee as default};
