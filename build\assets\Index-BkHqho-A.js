import{u as T,j as A,l as B,H as I,n as C,r as i,q as D,o as g,w as o,d as c,e as a,b as k,s as u,t as l,h as M,i as E,y as W,m as U,a as O,f as $,F as S,v as z}from"./app-BAwPsakn.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ne={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(q,{emit:h}){const f=T();A("moment");const V=h,N=q,v={installment:"",codeNumber:"",name:"",batches:[],feeGroup:"",dueOn:"",minDue:"",groups:[],address:""},t=B({...v});I(N.initUrl);const m=B({isLoaded:!(f.query.batches||f.query.groups)});return C(async()=>{m.batches=f.query.batches?f.query.batches.split(","):[],m.groups=f.query.groups?f.query.groups.split(","):[],m.isLoaded=!0}),(s,n)=>{const p=i("BaseInput"),F=i("BaseSelectSearch"),y=i("BaseSelect"),d=i("DatePicker"),b=i("FilterForm");return g(),D(b,{"init-form":v,multiple:["batches","groups"],form:t,onHide:n[9]||(n[9]=e=>V("hide"))},{default:o(()=>[c("div",J,[c("div",K,[a(p,{type:"text",modelValue:t.installment,"onUpdate:modelValue":n[0]||(n[0]=e=>t.installment=e),name:"installment",label:s.$trans("finance.fee_structure.installment")},null,8,["modelValue","label"])]),c("div",Q,[a(p,{type:"text",modelValue:t.codeNumber,"onUpdate:modelValue":n[1]||(n[1]=e=>t.codeNumber=e),name:"codeNumber",label:s.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),c("div",X,[a(p,{type:"text",modelValue:t.name,"onUpdate:modelValue":n[2]||(n[2]=e=>t.name=e),name:"name",label:s.$trans("contact.props.name")},null,8,["modelValue","label"])]),c("div",Y,[m.isLoaded?(g(),D(F,{key:0,multiple:"",name:"batches",label:s.$trans("global.select",{attribute:s.$trans("academic.batch.batch")}),modelValue:t.batches,"onUpdate:modelValue":n[3]||(n[3]=e=>t.batches=e),"value-prop":"uuid","init-search":m.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:o(e=>[u(l(e.value.course.name)+" "+l(e.value.name),1)]),listOption:o(e=>[u(l(e.option.course.nameWithTerm)+" "+l(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):k("",!0)]),c("div",Z,[a(y,{name:"feeGroup",label:s.$trans("global.select",{attribute:s.$trans("finance.fee_group.fee_group")}),modelValue:t.feeGroup,"onUpdate:modelValue":n[4]||(n[4]=e=>t.feeGroup=e),options:q.preRequisites.feeGroups,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),c("div",x,[a(d,{modelValue:t.dueOn,"onUpdate:modelValue":n[5]||(n[5]=e=>t.dueOn=e),name:"dueOn",as:"date",label:s.$trans("finance.report.fee_due.props.due_on")},null,8,["modelValue","label"])]),c("div",ee,[a(p,{type:"text",modelValue:t.minDue,"onUpdate:modelValue":n[6]||(n[6]=e=>t.minDue=e),name:"minDue",label:s.$trans("global.min_item",{attribute:s.$trans("finance.fee.due")})},null,8,["modelValue","label"])]),c("div",ae,[m.isLoaded?(g(),D(F,{key:0,multiple:"",modelValue:t.groups,"onUpdate:modelValue":n[7]||(n[7]=e=>t.groups=e),name:"groups","label-prop":"name","value-prop":"uuid",label:s.$trans("student.group.group"),"init-search":m.groups,"search-action":"option/list","additional-search-query":{type:"student_group"}},null,8,["modelValue","label","init-search"])):k("",!0)]),c("div",te,[a(p,{type:"text",modelValue:t.address,"onUpdate:modelValue":n[8]||(n[8]=e=>t.address=e),name:"address",label:s.$trans("contact.props.address.address")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},oe={name:"FinanceReportFeeDue"},le=Object.assign(oe,{setup(q){const h=T(),f=M(),V=E();let N=["filter"],v=[];W("finance:export")&&(v=["print","pdf","excel"]);const t="finance/report/",m=U(!1),s=U(!1),n=B({feeGroups:[]}),p=B({headers:[],data:[],meta:{total:0}}),F=async()=>{s.value=!0,await V.dispatch(t+"preRequisite",{name:"fee-due",params:h.query}).then(d=>{s.value=!1,Object.assign(n,d)}).catch(d=>{s.value=!1})},y=async()=>{s.value=!0,await V.dispatch(t+"fetchReport",{name:"fee-due",params:h.query}).then(d=>{s.value=!1,Object.assign(p,d)}).catch(d=>{s.value=!1})};return C(async()=>{await F(),await y()}),(d,b)=>{const e=i("BaseButton"),G=i("PageHeaderAction"),H=i("PageHeader"),w=i("ParentTransition"),_=i("DataCell"),R=i("TextMuted"),j=i("DataRow"),L=i("DataTable"),P=i("BaseCard");return g(),O(S,null,[a(H,{title:d.$trans($(h).meta.label),navs:[{label:d.$trans("finance.finance"),path:"Finance"},{label:d.$trans("finance.report.report"),path:"FinanceReport"}]},{default:o(()=>[a(G,{url:"finance/reports/fee-due/",name:"FinanceReportFeeDue",title:d.$trans("finance.report.fee_due.fee_due"),actions:$(N),"dropdown-actions":$(v),headers:p.headers,onToggleFilter:b[1]||(b[1]=r=>m.value=!m.value)},{default:o(()=>[a(e,{design:"white",onClick:b[0]||(b[0]=r=>$(f).push({name:"FinanceReportInstallmentWiseFeeDue"}))},{default:o(()=>[u(l(d.$trans("finance.report.installment_wise_fee_due.installment_wise_fee_due")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),a(w,{appear:"",visibility:m.value},{default:o(()=>[a(ne,{onAfterFilter:y,"init-url":t,"pre-requisites":n,onHide:b[2]||(b[2]=r=>m.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),a(w,{appear:"",visibility:!0},{default:o(()=>[a(P,{"no-padding":"","no-content-padding":"","is-loading":s.value},{default:o(()=>[a(L,{header:p.headers,footer:p.footers,meta:p.meta,module:"finance.report.fee_due",onRefresh:y},{default:o(()=>[(g(!0),O(S,null,z(p.data,r=>(g(),D(j,{key:r.uuid},{default:o(()=>[a(_,{name:"codeNumber"},{default:o(()=>[u(l(r.codeNumber),1)]),_:2},1024),a(_,{name:"name"},{default:o(()=>[u(l(r.name)+" ",1),a(R,{block:""},{default:o(()=>[u(l(r.rollNumber),1)]),_:2},1024)]),_:2},1024),a(_,{name:"fatherName"},{default:o(()=>[u(l(r.fatherName)+" ",1),a(R,{block:""},{default:o(()=>[u(l(r.contactNumber),1)]),_:2},1024)]),_:2},1024),a(_,{name:"course"},{default:o(()=>[u(l(r.courseName)+" ",1),a(R,{block:""},{default:o(()=>[u(l(r.batchName),1)]),_:2},1024)]),_:2},1024),a(_,{name:"feeGroup"},{default:o(()=>[u(l(r.feeGroupName),1)]),_:2},1024),a(_,{name:"dueFee"},{default:o(()=>[u(l(r.dueFee.formatted),1)]),_:2},1024),a(_,{name:"finalDueDate"},{default:o(()=>[u(l(r.finalDueDate.formatted),1)]),_:2},1024),a(_,{name:"overdueBy"},{default:o(()=>[u(l(r.overdueBy),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{le as default};
