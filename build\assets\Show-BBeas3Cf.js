import{i as F,u as S,h as P,l as C,r as s,a as N,o as c,e as r,w as e,f as u,q as m,b as _,d as A,s as o,t as l,y as H,F as I}from"./app-BAwPsakn.js";const M={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},q={name:"TransportVehicleFuelRecordShow"},E=Object.assign(q,{setup(D){F();const i=S(),p=P(),f={},h="transport/vehicle/fuelRecord/",a=C({...f}),b=t=>{Object.assign(a,t)};return(t,d)=>{const v=s("PageHeaderAction"),g=s("PageHeader"),$=s("TextMuted"),n=s("BaseDataView"),B=s("ListMedia"),T=s("BaseButton"),V=s("ShowButton"),R=s("BaseCard"),k=s("ShowItem"),w=s("ParentTransition");return c(),N(I,null,[r(g,{title:t.$trans(u(i).meta.trans,{attribute:t.$trans(u(i).meta.label)}),navs:[{label:t.$trans("transport.transport"),path:"Transport"},{label:t.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:t.$trans("transport.vehicle.fuel_record.fuel_record"),path:"TransportVehicleFuelRecord"}]},{default:e(()=>[r(v,{name:"TransportVehicleFuelRecord",title:t.$trans("transport.vehicle.fuel_record.fuel_record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(w,{appear:"",visibility:!0},{default:e(()=>[r(k,{"init-url":h,uuid:u(i).params.uuid,"module-uuid":u(i).params.muuid,onSetItem:b,onRedirectTo:d[1]||(d[1]=y=>u(p).push({name:"TransportVehicleFuelRecord",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(c(),m(R,{key:0},{title:e(()=>[o(l(a.vehicle.name)+" ",1),r($,{block:""},{default:e(()=>[o(l(a.vehicle.registrationNumber),1)]),_:1})]),footer:e(()=>[r(V,null,{default:e(()=>[u(H)("vehicle-fuel-record:edit")?(c(),m(T,{key:0,design:"primary",onClick:d[0]||(d[0]=y=>u(p).push({name:"TransportVehicleFuelRecordEdit",params:{uuid:a.uuid}}))},{default:e(()=>[o(l(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[A("dl",M,[r(n,{label:t.$trans("transport.vehicle.fuel_record.props.date")},{default:e(()=>[o(l(a.date.formatted),1)]),_:1},8,["label"]),r(n,{label:t.$trans("transport.vehicle.fuel_record.props.quantity")},{default:e(()=>[o(l(a.quantity||"-"),1)]),_:1},8,["label"]),r(n,{label:t.$trans("transport.vehicle.fuel_record.props.price_per_unit")},{default:e(()=>[o(l(a.pricePerUnit.formatted||"-"),1)]),_:1},8,["label"]),r(n,{label:t.$trans("transport.vehicle.fuel_record.props.cost")},{default:e(()=>[o(l(a.cost.formatted||"-"),1)]),_:1},8,["label"]),r(n,{label:t.$trans("transport.vehicle.fuel_record.props.log")},{default:e(()=>[o(l(a.log||"-"),1)]),_:1},8,["label"]),r(n,{class:"col-span-1 sm:col-span-2",label:t.$trans("transport.vehicle.fuel_record.props.remarks")},{default:e(()=>[o(l(a.remarks),1)]),_:1},8,["label"]),r(n,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[r(B,{media:a.media,url:`/app/transport/vehicle/fuel-records/${a.uuid}/`},null,8,["media","url"])]),_:1}),r(n,{label:t.$trans("general.created_at")},{default:e(()=>[o(l(a.createdAt.formatted),1)]),_:1},8,["label"]),r(n,{label:t.$trans("general.updated_at")},{default:e(()=>[o(l(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{E as default};
