import{u as R,l as h,n as S,r,q as i,o,w as e,d as B,e as m,h as j,j as N,y as d,m as O,f as a,a as U,F as q,v as x,b as c,s as l,t as s}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(I,{emit:f}){R();const v=f,F={name:""},_=h({...F}),C=h({isLoaded:!0});return S(async()=>{C.isLoaded=!0}),(g,p)=>{const y=r("BaseInput"),n=r("FilterForm");return o(),i(n,{"init-form":F,form:_,multiple:[],onHide:p[1]||(p[1]=u=>v("hide"))},{default:e(()=>[B("div",z,[B("div",G,[m(y,{type:"text",modelValue:_.name,"onUpdate:modelValue":p[0]||(p[0]=u=>_.name=u),name:"name",label:g.$trans("form.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={class:"text-danger"},Q={name:"FormList"},X=Object.assign(Q,{setup(I){const f=j(),v=N("emitter");let F=["filter"];d("form:create")&&F.unshift("create");let _=[];d("form:export")&&(_=["print","pdf","excel"]);const C="form/",g=O(!1),p=h({}),y=n=>{Object.assign(p,n)};return(n,u)=>{const T=r("PageHeaderAction"),M=r("PageHeader"),w=r("ParentTransition"),D=r("TextMuted"),b=r("DataCell"),V=r("BaseBadge"),k=r("FloatingMenuItem"),A=r("FloatingMenu"),E=r("DataRow"),H=r("BaseButton"),L=r("DataTable"),P=r("ListItem");return o(),i(P,{"init-url":C,onSetItems:y},{header:e(()=>[m(M,{title:n.$trans("form.form"),navs:[]},{default:e(()=>[m(T,{url:"forms/",name:"Form",title:n.$trans("form.form"),actions:a(F),"dropdown-actions":a(_),onToggleFilter:u[0]||(u[0]=t=>g.value=!g.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title"])]),filter:e(()=>[m(w,{appear:"",visibility:g.value},{default:e(()=>[m(J,{onRefresh:u[1]||(u[1]=t=>a(v).emit("listItems")),onHide:u[2]||(u[2]=t=>g.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[m(w,{appear:"",visibility:!0},{default:e(()=>[m(L,{header:p.headers,meta:p.meta,module:"form",onRefresh:u[4]||(u[4]=t=>a(v).emit("listItems"))},{actionButton:e(()=>[a(d)("form:create")?(o(),i(H,{key:0,onClick:u[3]||(u[3]=t=>a(f).push({name:"FormCreate"}))},{default:e(()=>[l(s(n.$trans("global.add",{attribute:n.$trans("form.form")})),1)]),_:1})):c("",!0)]),default:e(()=>[(o(!0),U(q,null,x(p.data,t=>(o(),i(E,{key:t.uuid,onDoubleClick:$=>a(f).push({name:"FormShow",params:{uuid:t.uuid}})},{default:e(()=>[m(b,{name:"name"},{default:e(()=>[l(s(t.name)+" ",1),m(D,{block:""},{default:e(()=>[l(s(t.excerpt),1)]),_:2},1024)]),_:2},1024),m(b,{name:"dueDate"},{default:e(()=>[l(s(t.dueDate.formatted)+" ",1),t.isExpired?(o(),i(D,{key:0,block:""},{default:e(()=>[B("span",K,s(n.$trans("form.statuses.expired")),1)]),_:1})):c("",!0)]),_:2},1024),a(d)("form-submission:manage")?(o(),i(b,{key:0,name:"status"},{default:e(()=>[m(V,{design:t.status.color},{default:e(()=>[l(s(t.status.label),1)]),_:2},1032,["design"])]),_:2},1024)):c("",!0),a(d)("form-submission:manage")?(o(),i(b,{key:1,name:"submission"},{default:e(()=>[l(s(t.submissionsCount),1)]),_:2},1024)):c("",!0),a(d)("form-submission:manage")?(o(),i(b,{key:2,name:"createdAt"},{default:e(()=>[l(s(t.createdAt.formatted),1)]),_:2},1024)):c("",!0),m(b,{name:"action"},{default:e(()=>[m(A,null,{default:e(()=>[t.isExpired?c("",!0):(o(),i(k,{key:0,icon:"fas fa-arrow-circle-right",onClick:$=>a(f).push({name:"FormSubmit",params:{uuid:t.uuid}})},{default:e(()=>[l(s(n.$trans("global.submit",{attribute:n.$trans("form.form")})),1)]),_:2},1032,["onClick"])),a(d)("form-submission:manage")?(o(),i(k,{key:1,icon:"far fa-file-lines",onClick:$=>a(f).push({name:"FormSubmission",params:{uuid:t.uuid}})},{default:e(()=>[l(s(n.$trans("global.view",{attribute:n.$trans("form.submission.submission")})),1)]),_:2},1032,["onClick"])):c("",!0),a(d)("form-submission:manage")?(o(),i(k,{key:2,icon:"fas fa-arrow-circle-right",onClick:$=>a(f).push({name:"FormShow",params:{uuid:t.uuid}})},{default:e(()=>[l(s(n.$trans("general.show")),1)]),_:2},1032,["onClick"])):c("",!0),a(d)("form:edit")&&t.isEditable?(o(),i(k,{key:3,icon:"fas fa-edit",onClick:$=>a(f).push({name:"FormEdit",params:{uuid:t.uuid}})},{default:e(()=>[l(s(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):c("",!0),a(d)("form:create")?(o(),i(k,{key:4,icon:"fas fa-copy",onClick:$=>a(f).push({name:"FormDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[l(s(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):c("",!0),a(d)("form:delete")&&t.isEditable?(o(),i(k,{key:5,icon:"fas fa-trash",onClick:$=>a(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[l(s(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):c("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
