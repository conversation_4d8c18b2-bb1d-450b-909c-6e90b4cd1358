import{i as j,u as $,h as F,j as g,l as O,r as s,a as m,o as _,e as t,w as e,f as n,q as f,b as y,d as u,F as T,v as z,s as i,t as l}from"./app-BAwPsakn.js";const E={class:"space-y-2"},U=["innerHTML"],G={class:"space-y-4"},J={key:1,class:"px-4 py-2"},K={class:"px-4 py-2"},Q={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},W={name:"InventoryStockTransferShow"},Z=Object.assign(W,{setup(X){j();const p=$(),S=F(),a=g("$trans");g("emitter");const h={},L="inventory/stockTransfer/",V=[{key:"name",label:a("inventory.stock_item.props.name"),visibility:!0},{key:"quantity",label:a("inventory.stock_transfer.props.quantity"),visibility:!0}],o=O({...h}),w=v=>{Object.assign(o,v)};return(v,d)=>{const I=s("PageHeaderAction"),x=s("PageHeader"),c=s("ListItemView"),B=s("ListContainerVertical"),b=s("BaseCard"),C=s("TextMuted"),k=s("DataCell"),D=s("DataRow"),P=s("SimpleTable"),H=s("BaseAlert"),N=s("ListMedia"),A=s("BaseDataView"),M=s("DetailLayoutVertical"),q=s("ShowItem"),R=s("ParentTransition");return _(),m(T,null,[t(x,{title:n(a)(n(p).meta.trans,{attribute:n(a)(n(p).meta.label)}),navs:[{label:n(a)("inventory.inventory"),path:"Inventory"},{label:n(a)("inventory.stock_transfer.stock_transfer"),path:"InventoryStockTransferList"}]},{default:e(()=>[t(I,{name:"InventoryStockTransfer",title:n(a)("inventory.stock_transfer.stock_transfer"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(R,{appear:"",visibility:!0},{default:e(()=>[t(q,{"init-url":L,uuid:n(p).params.uuid,onSetItem:w,onRedirectTo:d[0]||(d[0]=r=>n(S).push({name:"InventoryStockTransfer"}))},{default:e(()=>[o.uuid?(_(),f(M,{key:0},{detail:e(()=>[u("div",E,[t(b,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(l(n(a)("inventory.stock_transfer.props.code_number"))+" "+l(o.codeNumber),1)]),action:e(()=>d[1]||(d[1]=[])),default:e(()=>[t(B,null,{default:e(()=>[t(c,{label:n(a)("inventory.inventory")},{default:e(()=>{var r;return[i(l((r=o.inventory)==null?void 0:r.name),1)]}),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_transfer.props.from")},{default:e(()=>{var r;return[i(l(((r=o.fromPlace)==null?void 0:r.fullName)||"-"),1)]}),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_transfer.props.to")},{default:e(()=>{var r;return[i(l(((r=o.toPlace)==null?void 0:r.fullName)||"-"),1)]}),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_transfer.props.date")},{default:e(()=>[i(l(o.date.formatted),1)]),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_transfer.props.description")},{default:e(()=>[u("div",{innerHTML:o.description},null,8,U)]),_:1},8,["label"]),t(c,{label:n(a)("general.created_at")},{default:e(()=>[i(l(o.createdAt.formatted),1)]),_:1},8,["label"]),t(c,{label:n(a)("general.updated_at")},{default:e(()=>[i(l(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[u("div",G,[t(b,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(l(n(a)("inventory.stock_transfer.props.items")),1)]),footer:e(()=>d[2]||(d[2]=[])),default:e(()=>[o.items.length>0?(_(),f(P,{key:0,header:V},{default:e(()=>[(_(!0),m(T,null,z(o.items,r=>(_(),f(D,{key:r.uuid},{default:e(()=>[t(k,{name:"name"},{default:e(()=>[i(l(r.item.name)+" ",1),t(C,{block:""},{default:e(()=>[i(l(r.description),1)]),_:2},1024)]),_:2},1024),t(k,{name:"quantity"},{default:e(()=>[i(l(r.quantity),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):y("",!0),o.items.length===0?(_(),m("div",J,[t(H,{design:"info",size:"xs"},{default:e(()=>[i(l(n(a)("general.errors.record_not_found")),1)]),_:1})])):y("",!0),u("div",K,[u("dl",Q,[t(A,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(N,{media:o.media,url:`/app/inventory/stock-transfers/${o.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1})])]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Z as default};
