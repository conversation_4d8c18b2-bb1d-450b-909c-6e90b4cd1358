import{u as w,j as q,H as b,c as x,l as p,r as o,a as P,o as R,e as l,f as e,w as u,d as _,F as C}from"./app-BAwPsakn.js";const L={class:"grid grid-cols-3 gap-4"},F={class:"col-span-3 sm:col-span-1"},W={name:"EmployeeLeaveConfigGeneral"},V=Object.assign(W,{setup(j){const h=w(),a=q("$trans"),n="config/",i=b(n);x(()=>a("global.placeholder_info",{attribute:c.datePlaceholders}));const c=p({datePlaceholders:""}),d={allowEmployeeRequestLeaveWithExhaustedCredit:!1,type:"employee"},s=p({...d}),v=m=>{Object.assign(c,{datePlaceholders:m.datePlaceholders.map(t=>t.value).join(", ")})};return(m,t)=>{const f=o("PageHeader"),y=o("BaseSwitch"),E=o("FormAction"),g=o("ParentTransition");return R(),P(C,null,[l(f,{title:e(a)(e(h).meta.label),navs:[{label:e(a)("employee.employee"),path:"Employee"},{label:e(a)("employee.leave.leave"),path:"EmployeeLeave"}]},null,8,["title","navs"]),l(g,{appear:"",visibility:!0},{default:u(()=>[l(E,{"pre-requisites":!1,onSetPreRequisites:v,"init-url":n,"data-fetch":"employee",action:"store","init-form":d,form:s,"stay-on":"",redirect:"EmployeeLeave"},{default:u(()=>[_("div",L,[_("div",F,[l(y,{vertical:"",modelValue:s.allowEmployeeRequestLeaveWithExhaustedCredit,"onUpdate:modelValue":t[0]||(t[0]=r=>s.allowEmployeeRequestLeaveWithExhaustedCredit=r),name:"allowEmployeeRequestLeaveWithExhaustedCredit",label:e(a)("employee.leave.config.props.allow_employee_request_leave_with_exhausted_credit"),error:e(i).allowEmployeeRequestLeaveWithExhaustedCredit,"onUpdate:error":t[1]||(t[1]=r=>e(i).allowEmployeeRequestLeaveWithExhaustedCredit=r)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{V as default};
