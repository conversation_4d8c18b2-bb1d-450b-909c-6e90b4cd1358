import{l as R,r as d,q as _,o as r,w as e,d as w,e as a,u as z,i as x,H as G,m as P,n as J,a as B,b as h,f as l,s,t as i,h as K,j as Q,y as k,z as W,F as j,v as S,A as X}from"./app-BAwPsakn.js";import{d as Y}from"./vuedraggable.umd-BRYqknf6.js";const Z={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},oe={__name:"Filter",emits:["hide"],setup(T,{emit:f}){const g=f,$={name:"",code:"",shortcode:""},p=R({...$});return(C,n)=>{const c=d("BaseInput"),y=d("FilterForm");return r(),_(y,{"init-form":$,form:p,onHide:n[3]||(n[3]=v=>g("hide"))},{default:e(()=>[w("div",Z,[w("div",ee,[a(c,{type:"text",modelValue:p.name,"onUpdate:modelValue":n[0]||(n[0]=v=>p.name=v),name:"name",label:C.$trans("academic.division.props.name")},null,8,["modelValue","label"])]),w("div",te,[a(c,{type:"text",modelValue:p.code,"onUpdate:modelValue":n[1]||(n[1]=v=>p.code=v),name:"code",label:C.$trans("academic.division.props.code")},null,8,["modelValue","label"])]),w("div",ae,[a(c,{type:"text",modelValue:p.shortcode,"onUpdate:modelValue":n[2]||(n[2]=v=>p.shortcode=v),name:"shortcode",label:C.$trans("academic.division.props.shortcode")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ne={key:0},ie={class:"flex border rounded-xl px-4 py-2"},se={key:1},le={key:2,class:"mt-4 flex justify-end"},re={name:"AcademicDivisionReorder"},de=Object.assign(re,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(T,{emit:f}){z();const g=x(),$=f,p={divisions:[]};G("academic/division/");const n=P(!1),c=R({divisions:[]});R({...p});const y=async()=>{n.value=!0,await g.dispatch("academic/division/list",{params:{all:!0}}).then(t=>{n.value=!1,c.divisions=t}).catch(t=>{n.value=!1})},v=async()=>{n.value=!0,await g.dispatch("academic/division/reorder",{data:{divisions:c.divisions}}).then(t=>{n.value=!1,$("refresh"),$("close")}).catch(t=>{n.value=!1})},u=()=>{$("close")};return J(()=>{y()}),(t,D)=>{const H=d("BaseLabel"),L=d("BaseAlert"),V=d("BaseButton"),A=d("BaseModal");return r(),_(A,{show:T.visibility,onClose:u},{title:e(()=>[s(i(t.$trans("global.reorder",{attribute:t.$trans("academic.division.division")})),1)]),default:e(()=>[c.divisions.length?(r(),B("div",ne,[a(l(Y),{class:"space-y-2",list:c.divisions,"item-key":"uuid"},{item:e(({element:b,index:F})=>[w("div",ie,[D[0]||(D[0]=w("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),a(H,null,{default:e(()=>{var M;return[s(i((M=b.program)==null?void 0:M.name)+" "+i(b.name),1)]}),_:2},1024)])]),_:1},8,["list"])])):(r(),B("div",se,[a(L,{design:"info",size:"xs"},{default:e(()=>[s(i(t.$trans("general.errors.record_not_found")),1)]),_:1})])),c.divisions.length?(r(),B("div",le,[a(V,{onClick:v},{default:e(()=>[s(i(t.$trans("general.reorder")),1)]),_:1})])):h("",!0)]),_:1},8,["show"])}}}),ue={name:"AcademicDivisionList"},pe=Object.assign(ue,{setup(T){const f=K(),g=Q("emitter");let $=["filter"];k("division:create")&&$.unshift("create");let p=[];k("division:export")&&(p=["print","pdf","excel"]);const C="academic/division/",n=P(!1),c=P(!1),y=R({}),v=u=>{Object.assign(y,u)};return(u,t)=>{const D=d("BaseButton"),H=d("PageHeaderAction"),L=d("PageHeader"),V=d("ParentTransition"),A=d("TextMuted"),b=d("DataCell"),F=d("FloatingMenuItem"),M=d("FloatingMenu"),N=d("DataRow"),q=d("DataTable"),E=d("ListItem"),O=W("tooltip");return r(),B(j,null,[a(E,{"init-url":C,"additional-query":{details:!0},onSetItems:v},{header:e(()=>[a(L,{title:u.$trans("academic.division.division"),navs:[{label:u.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[a(H,{url:"academic/divisions/",name:"AcademicDivision",title:u.$trans("academic.division.division"),actions:l($),"dropdown-actions":l(p),"additional-dropdown-actions-query":{details:!0},onToggleFilter:t[2]||(t[2]=o=>n.value=!n.value)},{default:e(()=>[X((r(),_(D,{design:"white",onClick:t[0]||(t[0]=o=>c.value=!c.value)},{default:e(()=>t[9]||(t[9]=[w("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[O,u.$trans("global.reorder",{attribute:u.$trans("academic.division.division")})]]),l(k)("division-incharge:read")?(r(),_(D,{key:0,design:"white",onClick:t[1]||(t[1]=o=>l(f).push({name:"AcademicDivisionIncharge"}))},{default:e(()=>[s(i(u.$trans("employee.incharge.incharge")),1)]),_:1})):h("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(V,{appear:"",visibility:n.value},{default:e(()=>[a(oe,{onRefresh:t[3]||(t[3]=o=>l(g).emit("listItems")),onHide:t[4]||(t[4]=o=>n.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(V,{appear:"",visibility:!0},{default:e(()=>[a(q,{header:y.headers,meta:y.meta,module:"academic.division",onRefresh:t[6]||(t[6]=o=>l(g).emit("listItems"))},{actionButton:e(()=>[l(k)("division:create")?(r(),_(D,{key:0,onClick:t[5]||(t[5]=o=>l(f).push({name:"AcademicDivisionCreate"}))},{default:e(()=>[s(i(u.$trans("global.add",{attribute:u.$trans("academic.division.division")})),1)]),_:1})):h("",!0)]),default:e(()=>[(r(!0),B(j,null,S(y.data,o=>(r(),_(N,{key:o.uuid,onDoubleClick:m=>l(f).push({name:"AcademicDivisionShow",params:{uuid:o.uuid}})},{default:e(()=>[a(b,{name:"name"},{default:e(()=>[s(i(o.name)+" ",1),o.pgAccount?(r(),_(A,{key:0,block:""},{default:e(()=>[s(i(o.pgAccount),1)]),_:2},1024)):h("",!0)]),_:2},1024),a(b,{name:"code"},{default:e(()=>[s(i(o.code)+" ",1),a(A,{block:""},{default:e(()=>[s(i(o.shortcode),1)]),_:2},1024)]),_:2},1024),a(b,{name:"program"},{default:e(()=>{var m;return[s(i(((m=o.program)==null?void 0:m.name)||"-")+" ",1),a(A,{block:""},{default:e(()=>{var I,U;return[s(i(((U=(I=o.program)==null?void 0:I.department)==null?void 0:U.name)||"-"),1)]}),_:2},1024)]}),_:2},1024),a(b,{name:"incharge"},{default:e(()=>[(r(!0),B(j,null,S(o.incharges,m=>{var I;return r(),B("div",null,[s(i(((I=m==null?void 0:m.employee)==null?void 0:I.name)||"-")+" ",1),a(A,null,{default:e(()=>[s(i(m==null?void 0:m.period),1)]),_:2},1024)])}),256))]),_:2},1024),a(b,{name:"createdAt"},{default:e(()=>[s(i(o.createdAt.formatted),1)]),_:2},1024),a(b,{name:"action"},{default:e(()=>[a(M,null,{default:e(()=>[a(F,{icon:"fas fa-arrow-circle-right",onClick:m=>l(f).push({name:"AcademicDivisionShow",params:{uuid:o.uuid}})},{default:e(()=>[s(i(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),l(k)("division:edit")?(r(),_(F,{key:0,icon:"fas fa-edit",onClick:m=>l(f).push({name:"AcademicDivisionEdit",params:{uuid:o.uuid}})},{default:e(()=>[s(i(u.$trans("general.edit")),1)]),_:2},1032,["onClick"])):h("",!0),l(k)("division:create")?(r(),_(F,{key:1,icon:"fas fa-copy",onClick:m=>l(f).push({name:"AcademicDivisionDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[s(i(u.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):h("",!0),l(k)("division:delete")?(r(),_(F,{key:2,icon:"fas fa-trash",onClick:m=>l(g).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[s(i(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])):h("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),a(de,{visibility:c.value,onClose:t[7]||(t[7]=o=>c.value=!1),onRefresh:t[8]||(t[8]=o=>l(g).emit("listItems"))},null,8,["visibility"])],64)}}});export{pe as default};
