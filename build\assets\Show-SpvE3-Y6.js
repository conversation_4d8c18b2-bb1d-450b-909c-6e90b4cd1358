import{i as A,u as q,h as H,l as I,r as l,a as c,o as p,e as s,w as a,f as d,q as g,b as f,d as B,s as n,t as o,F as b,v as w,y as M,aN as D}from"./app-BAwPsakn.js";const L={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={class:"flex space-x-2"},E=["src"],F={name:"ReceptionGatePassShow"},U=Object.assign(F,{setup(O){A();const m=q(),$=H(),k={},v="reception/gatePass/",t=I({...k}),h=e=>{Object.assign(t,e)},y=e=>{let i="/app/reception/gate-passes/"+e.uuid+"/export",_={};window.open(D(i,_),"_blank").focus()};return(e,i)=>{const _=l("PageHeaderAction"),R=l("PageHeader"),r=l("BaseDataView"),S=l("TextMuted"),C=l("ListMedia"),P=l("BaseButton"),G=l("ShowButton"),T=l("BaseCard"),N=l("ShowItem"),V=l("ParentTransition");return p(),c(b,null,[s(R,{title:e.$trans(d(m).meta.trans,{attribute:e.$trans(d(m).meta.label)}),navs:[{label:e.$trans("reception.reception"),path:"Reception"},{label:e.$trans("reception.gate_pass.gate_pass"),path:"ReceptionGatePass"}]},{default:a(()=>[s(_,{name:"ReceptionGatePass",title:e.$trans("reception.gate_pass.gate_pass"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(V,{appear:"",visibility:!0},{default:a(()=>[s(N,{"init-url":v,uuid:d(m).params.uuid,"module-uuid":d(m).params.muuid,onSetItem:h,onRedirectTo:i[2]||(i[2]=u=>d($).push({name:"ReceptionGatePass",params:{uuid:t.uuid}}))},{default:a(()=>[t.uuid?(p(),g(T,{key:0},{title:a(()=>[n(o(t.codeNumber),1)]),footer:a(()=>[s(G,null,{default:a(()=>[s(P,{design:"success",onClick:i[0]||(i[0]=u=>y(t))},{default:a(()=>[n(o(e.$trans("global.print",{attribute:e.$trans("reception.gate_pass.gate_pass")})),1)]),_:1}),d(M)("gate-pass:edit")?(p(),g(P,{key:0,design:"primary",onClick:i[1]||(i[1]=u=>d($).push({name:"ReceptionGatePassEdit",params:{uuid:t.uuid}}))},{default:a(()=>[n(o(e.$trans("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:a(()=>[B("dl",L,[s(r,{label:e.$trans("reception.gate_pass.props.to")},{default:a(()=>[n(o(t.requesterType.label),1)]),_:1},8,["label"]),s(r,{label:e.$trans("reception.gate_pass.props.requester")},{default:a(()=>[(p(!0),c(b,null,w(t.audiences,u=>(p(),c("div",null,[n(o(u.name)+" ",1),s(S,null,{default:a(()=>[n(o(u.detail),1)]),_:2},1024)]))),256))]),_:1},8,["label"]),s(r,{label:e.$trans("reception.gate_pass.props.purpose")},{default:a(()=>[n(o(t.purpose.name),1)]),_:1},8,["label"]),s(r,{label:e.$trans("reception.gate_pass.props.datetime")},{default:a(()=>[n(o(t.startAt.formatted),1)]),_:1},8,["label"]),s(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("reception.gate_pass.props.reason")},{default:a(()=>[n(o(t.reason),1)]),_:1},8,["label"]),s(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("reception.gate_pass.props.remarks")},{default:a(()=>[n(o(t.remarks),1)]),_:1},8,["label"]),t.images.length?(p(),g(r,{key:0,class:"col-span-1 sm:col-span-2"},{default:a(()=>[B("div",j,[(p(!0),c(b,null,w(t.images,u=>(p(),c("img",{src:u.url,class:"max-w-1/4"},null,8,E))),256))])]),_:1})):f("",!0),s(r,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[s(C,{media:t.media,url:`/app/reception/gate-passes/${t.uuid}/`},null,8,["media","url"])]),_:1}),s(r,{label:e.$trans("general.created_at")},{default:a(()=>[n(o(t.createdAt.formatted),1)]),_:1},8,["label"]),s(r,{label:e.$trans("general.updated_at")},{default:a(()=>[n(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{U as default};
