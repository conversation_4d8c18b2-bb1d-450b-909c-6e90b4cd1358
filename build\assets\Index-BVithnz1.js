import{i as w,H as N,l as V,r as i,q as D,o as u,w as p,e as l,d as a,a as U,b as v,f as t,F as b,s as H,t as A}from"./app-BAwPsakn.js";const k={class:"grid grid-cols-3 gap-4"},F={class:"col-span-3 sm:col-span-1"},_={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},R={class:"grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},z={class:"grid grid-cols-3 gap-4"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},L={name:"ConfigMail"},X=Object.assign(L,{setup(Q){const y=w(),g="config/",r=N(g),f=V({}),c={driver:"",fromName:"",fromAddress:"",smtpHost:"",smtpPort:"",smtpUsername:"",smtpPassword:"",smtpEncryption:"",mailgunDomain:"",mailgunSecret:"",mailgunEndpoint:"",type:"mail"},s=V({...c}),P=n=>{Object.assign(f,n)},$=()=>{y.dispatch("config/testMailConnection").catch(n=>{})};return(n,o)=>{const E=i("BaseButton"),d=i("CardHeader"),B=i("BaseSelect"),m=i("BaseInput"),C=i("FormAction"),S=i("ConfigPage");return u(),D(S,null,{action:p(()=>[l(E,{design:"primary",onClick:$},{default:p(()=>[H(A(n.$trans("config.mail.test_connection")),1)]),_:1})]),default:p(()=>[l(C,{"no-card":"","init-url":g,"pre-requisites":{data:["mailDrivers"]},onSetPreRequisites:P,"data-fetch":"mail","init-form":c,form:s,action:"store","stay-on":"",redirect:"Config"},{default:p(()=>[l(d,{first:"",title:n.$trans("config.mail.mail_config"),description:n.$trans("config.mail.mail_info")},null,8,["title","description"]),a("div",k,[a("div",F,[l(B,{modelValue:s.driver,"onUpdate:modelValue":o[0]||(o[0]=e=>s.driver=e),name:"driver",label:n.$trans("config.mail.props.driver"),options:f.mailDrivers,error:t(r).driver,"onUpdate:error":o[1]||(o[1]=e=>t(r).driver=e)},null,8,["modelValue","label","options","error"])]),a("div",_,[l(m,{type:"text",modelValue:s.fromName,"onUpdate:modelValue":o[2]||(o[2]=e=>s.fromName=e),name:"fromName",label:n.$trans("config.mail.props.from_name"),error:t(r).fromName,"onUpdate:error":o[3]||(o[3]=e=>t(r).fromName=e)},null,8,["modelValue","label","error"])]),a("div",q,[l(m,{type:"text",modelValue:s.fromAddress,"onUpdate:modelValue":o[4]||(o[4]=e=>s.fromAddress=e),name:"fromAddress",label:n.$trans("config.mail.props.from_address"),error:t(r).fromAddress,"onUpdate:error":o[5]||(o[5]=e=>t(r).fromAddress=e)},null,8,["modelValue","label","error"])])]),s.driver=="smtp"?(u(),U(b,{key:0},[l(d,{title:n.$trans("config.mail.smtp")},null,8,["title"]),a("div",R,[a("div",j,[l(m,{type:"text",modelValue:s.smtpHost,"onUpdate:modelValue":o[6]||(o[6]=e=>s.smtpHost=e),name:"smtpHost",label:n.$trans("config.mail.props.smtp_host"),error:t(r).smtpHost,"onUpdate:error":o[7]||(o[7]=e=>t(r).smtpHost=e)},null,8,["modelValue","label","error"])]),a("div",I,[l(m,{type:"text",modelValue:s.smtpPort,"onUpdate:modelValue":o[8]||(o[8]=e=>s.smtpPort=e),name:"smtpPort",label:n.$trans("config.mail.props.smtp_port"),error:t(r).smtpPort,"onUpdate:error":o[9]||(o[9]=e=>t(r).smtpPort=e)},null,8,["modelValue","label","error"])]),a("div",M,[l(m,{type:"text",modelValue:s.smtpEncryption,"onUpdate:modelValue":o[10]||(o[10]=e=>s.smtpEncryption=e),name:"smtpEncryption",label:n.$trans("config.mail.props.smtp_encryption"),error:t(r).smtpEncryption,"onUpdate:error":o[11]||(o[11]=e=>t(r).smtpEncryption=e)},null,8,["modelValue","label","error"])]),a("div",O,[l(m,{type:"text",modelValue:s.smtpUsername,"onUpdate:modelValue":o[12]||(o[12]=e=>s.smtpUsername=e),name:"smtpUsername",label:n.$trans("config.mail.props.smtp_username"),error:t(r).smtpUsername,"onUpdate:error":o[13]||(o[13]=e=>t(r).smtpUsername=e)},null,8,["modelValue","label","error"])]),a("div",T,[l(m,{type:"password",modelValue:s.smtpPassword,"onUpdate:modelValue":o[14]||(o[14]=e=>s.smtpPassword=e),name:"smtpPassword",label:n.$trans("config.mail.props.smtp_password"),error:t(r).smtpPassword,"onUpdate:error":o[15]||(o[15]=e=>t(r).smtpPassword=e)},null,8,["modelValue","label","error"])])])],64)):v("",!0),s.driver=="mailgun"?(u(),U(b,{key:1},[l(d,{title:n.$trans("config.mail.mailgun")},null,8,["title"]),a("div",z,[a("div",G,[l(m,{type:"text",modelValue:s.mailgunDomain,"onUpdate:modelValue":o[16]||(o[16]=e=>s.mailgunDomain=e),name:"mailgunDomain",label:n.$trans("config.mail.props.mailgun_domain"),error:t(r).mailgunDomain,"onUpdate:error":o[17]||(o[17]=e=>t(r).mailgunDomain=e)},null,8,["modelValue","label","error"])]),a("div",J,[l(m,{type:"text",modelValue:s.mailgunSecret,"onUpdate:modelValue":o[18]||(o[18]=e=>s.mailgunSecret=e),name:"mailgunSecret",label:n.$trans("config.mail.props.mailgun_secret"),error:t(r).mailgunSecret,"onUpdate:error":o[19]||(o[19]=e=>t(r).mailgunSecret=e)},null,8,["modelValue","label","error"])]),a("div",K,[l(m,{type:"text",modelValue:s.mailgunEndpoint,"onUpdate:modelValue":o[20]||(o[20]=e=>s.mailgunEndpoint=e),name:"mailgunEndpoint",label:n.$trans("config.mail.props.mailgun_endpoint"),error:t(r).mailgunEndpoint,"onUpdate:error":o[21]||(o[21]=e=>t(r).mailgunEndpoint=e)},null,8,["modelValue","label","error"])])])],64)):v("",!0)]),_:1},8,["form"])]),_:1})}}});export{X as default};
