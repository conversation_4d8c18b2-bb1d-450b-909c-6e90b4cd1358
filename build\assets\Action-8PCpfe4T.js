import{H as U,l as j,r as c,q as $,o as V,w as b,d,e as n,f as a,J as A,u as B,a as F,F as P}from"./app-BAwPsakn.js";const S={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3"},w={name:"AcademicSubjectForm"},E=Object.assign(w,{setup(f){const i={name:"",alias:"",code:"",shortcode:"",type:"",position:"",description:""},l="academic/subject/",s=U(l),p=j({types:[]}),o=j({...i}),u=r=>{Object.assign(p,r)},y=r=>{var e;Object.assign(i,{...r,type:((e=r.type)==null?void 0:e.uuid)||""}),Object.assign(o,A(i))};return(r,e)=>{const m=c("BaseInput"),_=c("BaseSelect"),g=c("BaseTextarea"),v=c("FormAction");return V(),$(v,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:u,"init-url":l,"init-form":i,form:o,"set-form":y,redirect:"AcademicSubject"},{default:b(()=>[d("div",S,[d("div",q,[n(m,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=t=>o.name=t),name:"name",label:r.$trans("academic.subject.props.name"),error:a(s).name,"onUpdate:error":e[1]||(e[1]=t=>a(s).name=t),autofocus:""},null,8,["modelValue","label","error"])]),d("div",H,[n(m,{type:"text",modelValue:o.alias,"onUpdate:modelValue":e[2]||(e[2]=t=>o.alias=t),name:"alias",label:r.$trans("academic.subject.props.alias"),error:a(s).alias,"onUpdate:error":e[3]||(e[3]=t=>a(s).alias=t)},null,8,["modelValue","label","error"])]),d("div",O,[n(m,{type:"text",modelValue:o.code,"onUpdate:modelValue":e[4]||(e[4]=t=>o.code=t),name:"code",label:r.$trans("academic.subject.props.code"),error:a(s).code,"onUpdate:error":e[5]||(e[5]=t=>a(s).code=t)},null,8,["modelValue","label","error"])]),d("div",R,[n(m,{type:"text",modelValue:o.shortcode,"onUpdate:modelValue":e[6]||(e[6]=t=>o.shortcode=t),name:"shortcode",label:r.$trans("academic.subject.props.shortcode"),error:a(s).shortcode,"onUpdate:error":e[7]||(e[7]=t=>a(s).shortcode=t)},null,8,["modelValue","label","error"])]),d("div",T,[n(_,{modelValue:o.type,"onUpdate:modelValue":e[8]||(e[8]=t=>o.type=t),name:"type",label:r.$trans("academic.subject.props.type"),options:p.types,"label-prop":"name","value-prop":"uuid",error:a(s).type,"onUpdate:error":e[9]||(e[9]=t=>a(s).type=t)},null,8,["modelValue","label","options","error"])]),d("div",k,[n(g,{modelValue:o.description,"onUpdate:modelValue":e[10]||(e[10]=t=>o.description=t),name:"description",label:r.$trans("academic.subject.props.description"),error:a(s).description,"onUpdate:error":e[11]||(e[11]=t=>a(s).description=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),C={name:"AcademicSubjectAction"},N=Object.assign(C,{setup(f){const i=B();return(l,s)=>{const p=c("PageHeaderAction"),o=c("PageHeader"),u=c("ParentTransition");return V(),F(P,null,[n(o,{title:l.$trans(a(i).meta.trans,{attribute:l.$trans(a(i).meta.label)}),navs:[{label:l.$trans("academic.academic"),path:"Academic"},{label:l.$trans("academic.subject.subject"),path:"AcademicSubjectList"}]},{default:b(()=>[n(p,{name:"AcademicSubject",title:l.$trans("academic.subject.subject"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(u,{appear:"",visibility:!0},{default:b(()=>[n(E)]),_:1})],64)}}});export{N as default};
