import{r as g,a as r,o as i,d as l,e as m,b as u,s as p,t as c,F as f}from"./app-BAwPsakn.js";const b={class:"overflow-hidden bg-black lg:rounded-t-lg"},v={name:"SitePageAsset"},k=Object.assign(v,{props:{page:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},emits:["refreshItem"],setup(s,{emit:o}){const a=o;return(t,e)=>{const d=g("ImageUpload");return i(),r("div",b,[l("div",null,[m(d,{class:"h-32 w-full lg:h-48",disabled:s.disabled,label:t.$trans("site.assets.cover"),src:s.page.assets.cover,"upload-path":`site/pages/${s.page.uuid}/assets/cover`,"remove-path":`site/pages/${s.page.uuid}/assets/cover`,design:"modern","show-label":!1,onUploaded:e[0]||(e[0]=n=>a("refreshItem")),onRemoved:e[1]||(e[1]=n=>a("refreshItem"))},null,8,["disabled","label","src","upload-path","remove-path"])])])}}}),h={class:"overflow-hidden bg-black"},$={key:0,class:"mx-2 mt-4 text-sm text-gray-600 dark:text-gray-400"},I={name:"SitePageOg"},w=Object.assign(I,{props:{page:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},emits:["refreshItem"],setup(s,{emit:o}){const a=o;return(t,e)=>{const d=g("ImageUpload");return i(),r(f,null,[l("div",h,[l("div",null,[m(d,{class:"h-32 w-full lg:h-48",disabled:s.disabled,label:t.$trans("site.assets.og"),src:s.page.assets.og,"upload-path":`site/pages/${s.page.uuid}/assets/og`,"remove-path":`site/pages/${s.page.uuid}/assets/og`,design:"modern","show-label":!1,onUploaded:e[0]||(e[0]=n=>a("refreshItem")),onRemoved:e[1]||(e[1]=n=>a("refreshItem"))},null,8,["disabled","label","src","upload-path","remove-path"])])]),s.page.assets.defaultOg?(i(),r("p",$,[e[2]||(e[2]=l("i",{class:"fas fa-circle-info"},null,-1)),p(" "+c(t.$trans("site.assets.og_info")),1)])):u("",!0)],64)}}});export{k as _,w as a};
