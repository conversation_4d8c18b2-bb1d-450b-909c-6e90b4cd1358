import{u as F,j as V,g as b,y as x,r as d,a as j,o as r,q as s,b as m,e as n,w as e,f as a,d as v,s as o,t as u,F as D}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},S={name:"ContactShowBasic"},E=Object.assign(S,{props:{contact:{type:Object,default(){return{}}}},setup(t){const h=F(),l=V("$trans"),g=t,N=b("contact.uniqueIdNumber1Label"),q=b("contact.uniqueIdNumber2Label"),y=b("contact.uniqueIdNumber3Label"),I=b("contact.uniqueIdNumber4Label"),C=b("contact.uniqueIdNumber5Label"),p=b("contact.enableCategoryField"),B=b("contact.enableCasteField");let f=[];return x("contact:edit")&&(f.push({label:l("general.edit"),path:{name:"ContactEditBasic",params:{uuid:g.contact.uuid}}}),f.push({label:l("global.edit",{attribute:l("contact.props.photo")}),path:{name:"ContactEditPhoto",params:{uuid:g.contact.uuid}}})),(T,_)=>{const L=d("PageHeaderAction"),P=d("PageHeader"),c=d("BaseDataView"),k=d("BaseCard"),w=d("ParentTransition");return r(),j(D,null,[t.contact.uuid?(r(),s(P,{key:0,title:a(l)(a(h).meta.label),navs:[{label:a(l)("contact.contact"),path:"Contact"},{label:t.contact.name,path:{name:"ContactShow",params:{uuid:t.contact.uuid}}}]},{default:e(()=>[n(L,{"additional-actions":a(f)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):m("",!0),n(w,{appear:"",visibility:!0},{default:e(()=>[t.contact.uuid?(r(),s(k,{key:0},{default:e(()=>[v("dl",H,[n(c,{label:a(l)("contact.props.father_name")},{default:e(()=>[o(u(t.contact.fatherName),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.mother_name")},{default:e(()=>[o(u(t.contact.motherName),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.birth_date")},{default:e(()=>[o(u(t.contact.birthDate.formatted),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.gender")},{default:e(()=>[o(u(t.contact.gender.label),1)]),_:1},8,["label"]),n(c,{label:a(N)},{default:e(()=>[o(u(t.contact.uniqueIdNumber1),1)]),_:1},8,["label"]),n(c,{label:a(q)},{default:e(()=>[o(u(t.contact.uniqueIdNumber2),1)]),_:1},8,["label"]),n(c,{label:a(y)},{default:e(()=>[o(u(t.contact.uniqueIdNumber3),1)]),_:1},8,["label"]),n(c,{label:a(I)},{default:e(()=>[o(u(t.contact.uniqueIdNumber4),1)]),_:1},8,["label"]),n(c,{label:a(C)},{default:e(()=>[o(u(t.contact.uniqueIdNumber5),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.birth_place")},{default:e(()=>[o(u(t.contact.birthPlace),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.nationality")},{default:e(()=>[o(u(t.contact.nationality),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.mother_tongue")},{default:e(()=>[o(u(t.contact.motherTongue),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.blood_group")},{default:e(()=>{var i;return[o(u(((i=t.contact.bloodGroup)==null?void 0:i.label)||"-"),1)]}),_:1},8,["label"]),n(c,{label:a(l)("contact.props.marital_status")},{default:e(()=>{var i;return[o(u(((i=t.contact.maritalStatus)==null?void 0:i.label)||"-"),1)]}),_:1},8,["label"]),n(c,{label:a(l)("contact.religion.religion")},{default:e(()=>{var i;return[o(u(((i=t.contact.religion)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"]),a(B)?(r(),s(c,{key:0,label:a(l)("contact.caste.caste")},{default:e(()=>{var i;return[o(u(((i=t.contact.caste)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"])):m("",!0),a(p)?(r(),s(c,{key:1,label:a(l)("contact.category.category")},{default:e(()=>{var i;return[o(u(((i=t.contact.category)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"])):m("",!0),n(c,{label:a(l)("contact.props.occupation")},{default:e(()=>[o(u(t.contact.occupation||"-"),1)]),_:1},8,["label"]),n(c,{label:a(l)("contact.props.annual_income")},{default:e(()=>[o(u(t.contact.annualIncome||"-"),1)]),_:1},8,["label"])])]),_:1})):m("",!0)]),_:1})],64)}}});export{E as default};
