import{H as y,l as F,r as e,a as V,o as h,e as t,w as n,d as m,f as u,s as c,t as p,F as k}from"./app-BAwPsakn.js";const q={class:"space-y-6"},E={class:"flex items-center justify-between"},d="auth/register/",w={__name:"EmailRequest",setup(L){const i=y(d),l={email:""},r=F({...l});return(o,a)=>{const _=e("GuestHeader"),f=e("BaseInput"),g=e("BaseLink"),B=e("BaseButton"),b=e("FormAction"),v=e("ParentTransition");return h(),V(k,null,[t(_,{label:o.$trans("auth.register.email_request")},null,8,["label"]),t(v,{appear:"",visibility:!0},{default:n(()=>[t(b,{"no-card":"","no-action-button":"","init-url":d,"init-form":l,form:r,action:"emailRequest",redirect:"Login"},{default:n(()=>[m("div",q,[t(f,{type:"text","leading-icon":"fas fa-envelope",modelValue:r.email,"onUpdate:modelValue":a[0]||(a[0]=s=>r.email=s),name:"email",label:o.$trans("auth.register.props.email"),error:u(i).email,"onUpdate:error":a[1]||(a[1]=s=>u(i).email=s),autofocus:""},null,8,["modelValue","label","error"]),m("div",E,[t(g,{to:"Login"},{default:n(()=>[c(p(o.$trans("auth.login.login_title")),1)]),_:1})]),t(B,{type:"submit",block:""},{default:n(()=>[c(p(o.$trans("auth.register.email_request")),1)]),_:1})])]),_:1},8,["form"])]),_:1})],64)}}};export{w as default};
