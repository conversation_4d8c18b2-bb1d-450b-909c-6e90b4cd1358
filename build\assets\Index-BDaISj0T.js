import{u as z,l as q,n as le,r as d,q as v,o as c,w as o,d as u,e as n,b as _,j as R,m as L,H as K,K as P,f as p,y as A,s as V,t as b,h as ne,a as T,F as O,v as x,V as ie}from"./app-BAwPsakn.js";import{i as re,t as de,a as ue}from"./table-FwhM-Z75.js";const me={class:"grid grid-cols-3 gap-6"},pe={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},_e={class:"col-span-3 sm:col-span-1"},$e={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},ke={class:"col-span-3 sm:col-span-1"},he={class:"col-span-3 sm:col-span-1"},Se={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(D,{emit:w}){const g=z(),h=w,k=D,S={codeNumber:"",name:"",joiningStartDate:"",joiningEndDate:"",leavingStartDate:"",leavingEndDate:"",department:"",designation:"",employmentStatus:"",types:[],tagsIncluded:[],tagsExcluded:[],groups:[],status:"active"},l=q({...S}),B=q({types:k.preRequisites.types,statuses:k.preRequisites.statuses,groups:k.preRequisites.groups}),r=q({department:"",designation:"",employmentStatus:"",isLoaded:!(g.query.department||g.query.designation||g.query.employmentStatus||g.query.tagsIncluded||g.query.tagsExcluded||g.query.groups)});return le(async()=>{r.department=g.query.department,r.designation=g.query.designation,r.employmentStatus=g.query.employmentStatus,r.tagsIncluded=g.query.tagsIncluded?g.query.tagsIncluded.split(","):[],r.tagsExcluded=g.query.tagsExcluded?g.query.tagsExcluded.split(","):[],r.groups=g.query.groups?g.query.groups.split(","):[],r.isLoaded=!0}),(a,e)=>{const $=d("BaseInput"),y=d("DatePicker"),m=d("BaseSelectSearch"),I=d("BaseSelect"),U=d("FilterForm");return c(),v(U,{"init-form":S,form:l,multiple:["types","tagsIncluded","tagsExcluded","groups"],onHide:e[14]||(e[14]=i=>h("hide"))},{default:o(()=>[u("div",me,[u("div",pe,[n($,{type:"text",modelValue:l.codeNumber,"onUpdate:modelValue":e[0]||(e[0]=i=>l.codeNumber=i),name:"codeNumber",label:a.$trans("employee.props.code_number")},null,8,["modelValue","label"])]),u("div",ce,[n($,{type:"text",modelValue:l.name,"onUpdate:modelValue":e[1]||(e[1]=i=>l.name=i),name:"name",label:a.$trans("contact.props.name")},null,8,["modelValue","label"])]),u("div",ge,[n(y,{start:l.joiningStartDate,"onUpdate:start":e[2]||(e[2]=i=>l.joiningStartDate=i),end:l.joiningEndDate,"onUpdate:end":e[3]||(e[3]=i=>l.joiningEndDate=i),name:"joiningDateBetween",as:"range",label:a.$trans("global.date_between",{attribute:a.$trans("employee.props.joining_date")})},null,8,["start","end","label"])]),u("div",ye,[n(y,{start:l.leavingStartDate,"onUpdate:start":e[4]||(e[4]=i=>l.leavingStartDate=i),end:l.leavingEndDate,"onUpdate:end":e[5]||(e[5]=i=>l.leavingEndDate=i),name:"leavingDateBetween",as:"range",label:a.$trans("global.date_between",{attribute:a.$trans("employee.props.leaving_date")})},null,8,["start","end","label"])]),u("div",fe,[r.isLoaded?(c(),v(m,{key:0,name:"department",label:a.$trans("global.select",{attribute:a.$trans("employee.department.department")}),modelValue:l.department,"onUpdate:modelValue":e[6]||(e[6]=i=>l.department=i),"value-prop":"uuid","init-search":r.department,"search-action":"employee/department/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),u("div",be,[r.isLoaded?(c(),v(m,{key:0,name:"designation",label:a.$trans("global.select",{attribute:a.$trans("employee.designation.designation")}),modelValue:l.designation,"onUpdate:modelValue":e[7]||(e[7]=i=>l.designation=i),"value-prop":"uuid","init-search":r.designation,"search-action":"employee/designation/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),u("div",ve,[r.isLoaded?(c(),v(m,{key:0,name:"employmentStatus",label:a.$trans("global.select",{attribute:a.$trans("employee.employment_status.employment_status")}),modelValue:l.employmentStatus,"onUpdate:modelValue":e[8]||(e[8]=i=>l.employmentStatus=i),"value-prop":"uuid","init-search":r.employmentStatus,"search-action":"option/list","additional-search-query":{type:"employment_status"}},null,8,["label","modelValue","init-search"])):_("",!0)]),u("div",_e,[n(I,{multiple:"",modelValue:l.types,"onUpdate:modelValue":e[9]||(e[9]=i=>l.types=i),name:"types",label:a.$trans("employee.type"),options:B.types},null,8,["modelValue","label","options"])]),u("div",$e,[n(I,{modelValue:l.status,"onUpdate:modelValue":e[10]||(e[10]=i=>l.status=i),name:"status",label:a.$trans("employee.status"),options:B.statuses},null,8,["modelValue","label","options"])]),u("div",Ve,[r.isLoaded?(c(),v(m,{key:0,tags:"",name:"tagsIncluded",label:a.$trans("general.tags_included"),modelValue:l.tagsIncluded,"onUpdate:modelValue":e[11]||(e[11]=i=>l.tagsIncluded=i),"init-search":r.tagsIncluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),u("div",ke,[r.isLoaded?(c(),v(m,{key:0,tags:"",name:"tagsExcluded",label:a.$trans("general.tags_excluded"),modelValue:l.tagsExcluded,"onUpdate:modelValue":e[12]||(e[12]=i=>l.tagsExcluded=i),"init-search":r.tagsExcluded,"search-action":"tag/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),u("div",he,[n(I,{multiple:"",modelValue:l.groups,"onUpdate:modelValue":e[13]||(e[13]=i=>l.groups=i),name:"groups","label-prop":"name","value-prop":"uuid",label:a.$trans("employee.group.group"),options:B.groups},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},Be={class:"grid grid-cols-3 gap-6"},Ee={class:"col-span-3 sm:col-span-1"},qe={class:"mt-4 grid grid-cols-3 gap-6"},Ie={class:"col-span-3 sm:col-span-1"},Ce={name:"EmployeeUpdateBulkTag"},Ae=Object.assign(Ce,{props:{selected:{type:Object,required:!0},selectAll:{type:Boolean,required:!0},selectedEmployees:{type:Array,required:!0}},emits:["completed"],setup(D,{emit:w}){const g=R("emitter"),h=w,k=D,S={action:"assign",tags:[],selectAll:!1,employees:[]},l="employee/",B=L(!1),r=K(l),a=q({...S}),e=q({isLoaded:!0,tags:[]}),$=async()=>{a.selectAll=!1,g.emit("listItems"),h("completed")};return P([()=>k.selectAll,()=>k.selectedEmployees],([y,m])=>{a.selectAll=y,a.employees=m},{immediate:!0}),(y,m)=>{const I=d("BaseRadioGroup"),U=d("BaseSelectSearch"),i=d("FormAction"),F=d("BaseCard"),f=d("ParentTransition");return D.selected.items.length>0&&p(A)("employee:edit")?(c(),v(f,{key:0,appear:"",visibility:!0},{default:o(()=>[n(F,{class:"mt-4","is-loading":B.value},{title:o(()=>[V(b(y.$trans("global.update",{attribute:y.$trans("general.tag")})),1)]),default:o(()=>[n(i,{"no-card":"","keep-adding":!1,"init-url":l,action:"updateBulkTag","init-form":S,form:a,"after-submit":$},{default:o(()=>[u("div",Be,[u("div",Ee,[n(I,{options:[{label:y.$trans("general.assign"),value:"assign"},{label:y.$trans("general.remove"),value:"remove"}],name:"action",modelValue:a.action,"onUpdate:modelValue":m[0]||(m[0]=t=>a.action=t),error:p(r).action,"onUpdate:error":m[1]||(m[1]=t=>p(r).action=t),horizontal:""},null,8,["options","modelValue","error"])])]),u("div",qe,[u("div",Ie,[e.isLoaded?(c(),v(U,{key:0,tags:"",name:"tags",placeholder:y.$trans("global.select",{attribute:y.$trans("general.tag")}),modelValue:a.tags,"onUpdate:modelValue":m[2]||(m[2]=t=>a.tags=t),error:p(r).tags,"onUpdate:error":m[3]||(m[3]=t=>p(r).tags=t),"init-search":e.tags,"search-action":"tag/list"},null,8,["placeholder","modelValue","error","init-search"])):_("",!0)])])]),_:1},8,["form"])]),_:1},8,["is-loading"])]),_:1})):_("",!0)}}}),De={class:"grid grid-cols-3 gap-6"},we={class:"col-span-3 sm:col-span-1"},Ue={class:"mt-4 grid grid-cols-3 gap-6"},Fe={class:"col-span-3 sm:col-span-1"},Te={name:"EmployeeUpdateBulkGroup"},je=Object.assign(Te,{props:{selected:{type:Object,required:!0},selectAll:{type:Boolean,required:!0},selectedEmployees:{type:Array,required:!0}},emits:["completed"],setup(D,{emit:w}){const g=R("emitter"),h=w,k=D,S={action:"assign",groups:[],selectAll:!1,employees:[]},l="employee/",B=L(!1),r=K(l),a=q({...S}),e=q({isLoaded:!0,groups:[]}),$=async()=>{a.selectAll=!1,g.emit("listItems"),h("completed")};return P([()=>k.selectAll,()=>k.selectedEmployees],([y,m])=>{a.selectAll=y,a.employees=m},{immediate:!0}),(y,m)=>{const I=d("BaseRadioGroup"),U=d("BaseSelectSearch"),i=d("FormAction"),F=d("BaseCard"),f=d("ParentTransition");return D.selected.items.length>0&&p(A)("employee:edit")?(c(),v(f,{key:0,appear:"",visibility:!0},{default:o(()=>[n(F,{class:"mt-4","is-loading":B.value},{title:o(()=>[V(b(y.$trans("global.update",{attribute:y.$trans("employee.group.group")})),1)]),default:o(()=>[n(i,{"no-card":"","keep-adding":!1,"init-url":l,action:"updateBulkGroup","init-form":S,form:a,"after-submit":$},{default:o(()=>[u("div",De,[u("div",we,[n(I,{options:[{label:y.$trans("general.assign"),value:"assign"},{label:y.$trans("general.remove"),value:"remove"}],name:"action",modelValue:a.action,"onUpdate:modelValue":m[0]||(m[0]=t=>a.action=t),error:p(r).action,"onUpdate:error":m[1]||(m[1]=t=>p(r).action=t),horizontal:""},null,8,["options","modelValue","error"])])]),u("div",Ue,[u("div",Fe,[e.isLoaded?(c(),v(U,{key:0,multiple:"",name:"groups",placeholder:y.$trans("global.select",{attribute:y.$trans("employee.group.group")}),modelValue:a.groups,"onUpdate:modelValue":m[2]||(m[2]=t=>a.groups=t),error:p(r).groups,"onUpdate:error":m[3]||(m[3]=t=>p(r).groups=t),"init-search":e.groups,"search-action":"option/list","additional-search-query":{type:"employee_group"}},null,8,["placeholder","modelValue","error","init-search"])):_("",!0)])])]),_:1},8,["form"])]),_:1},8,["is-loading"])]),_:1})):_("",!0)}}}),Le={class:"grid grid-cols-1 gap-4 px-4 pt-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},Re={class:""},Pe=["onClick"],Ne={key:0,class:"ml-1 text-danger"},Me={class:"text-xs text-gray-500"},Ge={class:"truncate text-sm text-gray-500"},He={class:"text-sm text-gray-500"},Oe={key:0,class:"text-xs text-info"},xe=["onClick"],ze={key:0,class:"text-xs text-info"},Ke={name:"EmployeeList"},We=Object.assign(Ke,{setup(D){const w=z(),g=ne(),h=R("emitter");let k=["filter","view"];A("employee:create")&&k.unshift("create"),A("employee:config")&&k.unshift("config");let S=[];A("employee:export")&&(S=["print","pdf","excel"]),A("employee:create")&&S.unshift("import");const l="employee/",B=L(!1),r=L(!1),a=q({selectAll:!1,selectedEmployees:[]}),e=q({...re}),$=q({}),y=q({statuses:[]}),m=f=>{Object.assign($,f),e.pageItems=$.data.map(t=>t.uuid)},I=()=>{e.items=[],e.pageItems=[],e.all=!1},U=f=>{Object.assign(y,f)},i=()=>{e.global=!e.global,a.selectAll=e.global},F=f=>{window.open(`/app/employees/${f}`,"_blank")};return P(()=>[e.items,e.pageItems],([f,t],[N,M])=>{a.selectedEmployees=f,e.all=ue(e)}),(f,t)=>{const N=d("PageHeaderAction"),M=d("PageHeader"),J=d("BaseAlert"),Q=d("BaseImport"),j=d("ParentTransition"),W=d("CardView"),X=d("Pagination"),G=d("BaseButton"),Y=d("CardList"),Z=d("BaseArrayCheckbox"),ee=d("BaseCheckbox"),C=d("DataCell"),H=d("FloatingMenuItem"),te=d("FloatingMenu"),se=d("DataRow"),ae=d("DataTable"),oe=d("ListItem");return c(),v(oe,{"pre-requisites":!0,onSetPreRequisites:U,"init-url":l,onSetItems:m},{header:o(()=>[n(M,{title:f.$trans("employee.employee"),navs:[]},{default:o(()=>[n(N,{url:"employees/",name:"Employee",title:f.$trans("employee.employee"),actions:p(k),"dropdown-actions":p(S),headers:$.headers,onToggleFilter:t[0]||(t[0]=s=>B.value=!B.value),onToggleImport:t[1]||(t[1]=s=>r.value=!r.value),onRefresh:t[2]||(t[2]=s=>p(h).emit("listItems"))},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title"])]),import:o(()=>[n(j,{appear:"",visibility:r.value},{default:o(()=>[n(Q,{path:"employees/import",onCancelled:t[3]||(t[3]=s=>r.value=!1),onHide:t[4]||(t[4]=s=>r.value=!1),onCompleted:t[5]||(t[5]=s=>p(h).emit("listItems"))},{header:o(()=>[n(J,{size:"xs",design:"info"},{default:o(()=>[V(b(f.$trans("general.import_info")),1)]),_:1})]),option:o(()=>t[15]||(t[15]=[])),_:1})]),_:1},8,["visibility"])]),filter:o(()=>[n(j,{appear:"",visibility:B.value},{default:o(()=>[n(Se,{"pre-requisites":y,onRefresh:t[6]||(t[6]=s=>p(h).emit("listItems")),onHide:t[7]||(t[7]=s=>B.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:o(()=>[p(w).query.view!="list"?(c(),v(j,{key:0,appear:"",visibility:!0},{default:o(()=>[n(Y,{header:$.headers,meta:$.meta,module:"employee"},{actionButton:o(()=>[p(A)("employee:create")?(c(),v(G,{key:0,onClick:t[9]||(t[9]=s=>p(g).push({name:"EmployeeCreate"}))},{default:o(()=>[V(b(f.$trans("global.add",{attribute:f.$trans("employee.employee")})),1)]),_:1})):_("",!0)]),default:o(()=>[u("div",Le,[(c(!0),T(O,null,x($.data,s=>(c(),v(W,{key:s.uuid,"img-src":s.photo,path:{name:"EmployeeShow",params:{uuid:s.uuid}}},{title:o(()=>[V(b(s.name)+" ",1),u("span",Re,"("+b(s.codeNumber)+")",1),u("i",{class:"fas fa-up-right-from-square ml-2 cursor-pointer",onClick:ie(E=>F(s.uuid),["stop"])},null,8,Pe),s.otherTeamMember?(c(),T("span",Ne,t[16]||(t[16]=[u("i",{class:"fas fa-location-arrow"},null,-1)]))):_("",!0)]),default:o(()=>{var E;return[u("p",Me,b((E=s.age)==null?void 0:E.short),1),u("p",Ge,b(s.designation),1),u("p",He,b(s.period),1),s.otherTeamMember?(c(),T("p",Oe,b(s.otherTeamName),1)):_("",!0)]}),_:2},1032,["img-src","path"]))),128))]),u("div",null,[n(X,{"card-view":"",meta:$.meta,onRefresh:t[8]||(t[8]=s=>p(h).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):_("",!0),p(w).query.view=="list"?(c(),v(j,{key:1,appear:"",visibility:!0},{default:o(()=>[n(ae,{onToggleSelectAll:t[13]||(t[13]=s=>e.items=p(de)(s,e)),onToggleGlobalSelect:i,selected:e,header:$.headers,meta:$.meta,module:"employee",onRefresh:t[14]||(t[14]=s=>p(h).emit("listItems"))},{actionButton:o(()=>[p(A)("employee:create")?(c(),v(G,{key:0,onClick:t[12]||(t[12]=s=>p(g).push({name:"EmployeeCreate"}))},{default:o(()=>[V(b(f.$trans("global.add",{attribute:f.$trans("employee.employee")})),1)]),_:1})):_("",!0)]),default:o(()=>[(c(!0),T(O,null,x($.data,s=>(c(),v(se,{key:s.uuid,onDoubleClick:E=>p(g).push({name:"EmployeeShow",params:{uuid:s.uuid}})},{default:o(()=>[n(C,{name:"selectAll"},{default:o(()=>[e.global?_("",!0):(c(),v(Z,{key:0,items:e.items,"onUpdate:items":t[10]||(t[10]=E=>e.items=E),value:s.uuid},null,8,["items","value"])),e.global?(c(),v(ee,{key:1,modelValue:e.global,"onUpdate:modelValue":t[11]||(t[11]=E=>e.global=E)},null,8,["modelValue"])):_("",!0)]),_:2},1024),n(C,{name:"codeNumber"},{default:o(()=>[V(b(s.codeNumber),1)]),_:2},1024),n(C,{name:"name"},{default:o(()=>[u("span",{class:"cursor-pointer",onClick:E=>F(s.uuid)},b(s.name),9,xe),s.otherTeamMember?(c(),T("p",ze,b(s.otherTeamName),1)):_("",!0)]),_:2},1024),n(C,{name:"joiningDate"},{default:o(()=>[V(b(s.joiningDate.formatted),1)]),_:2},1024),n(C,{name:"employmentStatus"},{default:o(()=>[V(b(s.employmentStatus),1)]),_:2},1024),n(C,{name:"department"},{default:o(()=>[V(b(s.department),1)]),_:2},1024),n(C,{name:"designation"},{default:o(()=>[V(b(s.designation),1)]),_:2},1024),n(C,{name:"createdAt"},{default:o(()=>[V(b(s.createdAt.formatted),1)]),_:2},1024),n(C,{name:"action"},{default:o(()=>[n(te,null,{default:o(()=>[n(H,{icon:"fas fa-arrow-circle-right",onClick:E=>p(g).push({name:"EmployeeShow",params:{uuid:s.uuid}})},{default:o(()=>[V(b(f.$trans("general.show")),1)]),_:2},1032,["onClick"]),p(A)("employee:delete")&&!s.self&&!s.isDefault?(c(),v(H,{key:0,icon:"fas fa-trash",onClick:E=>p(h).emit("deleteItem",{uuid:s.uuid})},{default:o(()=>[V(b(f.$trans("general.delete")),1)]),_:2},1032,["onClick"])):_("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["selected","header","meta"])]),_:1})):_("",!0),n(Ae,{selected:e,"select-all":a.selectAll,"selected-employees":a.selectedEmployees,onCompleted:I},null,8,["selected","select-all","selected-employees"]),n(je,{selected:e,"select-all":a.selectAll,"selected-employees":a.selectedEmployees,onCompleted:I},null,8,["selected","select-all","selected-employees"])]),_:1})}}});export{We as default};
