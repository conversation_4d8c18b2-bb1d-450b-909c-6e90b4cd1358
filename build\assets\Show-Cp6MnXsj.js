import{i as C,u as L,h as I,j as S,l as T,r,a as D,o as g,e as o,w as e,f as a,q as j,b as H,d as b,s as i,t as c,F as N}from"./app-BAwPsakn.js";const R={class:"space-y-2"},$={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},F={name:"AcademicProgramShow"},q=Object.assign(F,{setup(M){C();const d=L(),f=I(),t=S("$trans"),y={},V="academic/program/";t("academic.period.period");const n=T({...y}),P=p=>{Object.assign(n,p)};return(p,s)=>{const v=r("PageHeaderAction"),w=r("PageHeader"),u=r("TextMuted"),l=r("ListItemView"),A=r("ListContainerVertical"),_=r("BaseCard"),h=r("BaseDataView"),x=r("DetailLayoutVertical"),B=r("ShowItem"),k=r("ParentTransition");return g(),D(N,null,[o(w,{title:a(t)(a(d).meta.trans,{attribute:a(t)(a(d).meta.label)}),navs:[{label:a(t)("academic.academic"),path:"Academic"},{label:a(t)("academic.program.program"),path:"AcademicProgramList"}]},{default:e(()=>[o(v,{name:"AcademicProgram",title:a(t)("academic.program.program"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(k,{appear:"",visibility:!0},{default:e(()=>[o(B,{"init-url":V,uuid:a(d).params.uuid,onSetItem:P,onRedirectTo:s[0]||(s[0]=m=>a(f).push({name:"AcademicProgram"}))},{default:e(()=>[n.uuid?(g(),j(x,{key:0},{detail:e(()=>[b("div",R,[o(_,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(c(a(t)("academic.program.program")),1)]),action:e(()=>s[1]||(s[1]=[])),default:e(()=>[o(A,null,{default:e(()=>[o(l,{dynamic:"",label:a(t)("academic.program.props.name")},{default:e(()=>[i(c(n.name)+" ",1),o(u,null,{default:e(()=>{var m;return[i(c((m=n.type)==null?void 0:m.name),1)]}),_:1})]),_:1},8,["label"]),o(l,{label:a(t)("academic.program.props.code")},{default:e(()=>[i(c(n.code)+" ",1),o(u,{block:""},{default:e(()=>[i(c(n.shortcode),1)]),_:1})]),_:1},8,["label"]),o(l,{label:a(t)("academic.program.props.alias")},{default:e(()=>[i(c(n.alias),1)]),_:1},8,["label"]),o(l,{label:a(t)("general.created_at")},{default:e(()=>[i(c(n.createdAt.formatted),1)]),_:1},8,["label"]),o(l,{label:a(t)("general.updated_at")},{default:e(()=>[i(c(n.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[o(_,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(c(a(t)("global.detail",{attribute:a(t)("academic.program.program")})),1)]),default:e(()=>[b("dl",$,[o(h,{class:"col-span-1 sm:col-span-2",label:a(t)("academic.program.props.description")},{default:e(()=>[i(c(n.description),1)]),_:1},8,["label"])])]),_:1})]),_:1})):H("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{q as default};
