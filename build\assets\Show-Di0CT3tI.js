import{i as P,u as T,h as V,l as k,r as o,a as p,o as m,e as n,w as e,f as l,q as A,b as H,d as N,F as v,v as D,s,t as r}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"InventoryShow"},M=Object.assign(j,{setup(E){P();const i=T(),_=V(),y={},f="inventory/",a=k({...y}),g=t=>{Object.assign(a,t)};return(t,u)=>{const b=o("PageHeaderAction"),B=o("PageHeader"),$=o("TextMuted"),d=o("BaseDataView"),h=o("BaseButton"),I=o("ShowButton"),w=o("BaseCard"),S=o("ShowItem"),C=o("ParentTransition");return m(),p(v,null,[n(B,{title:t.$trans(l(i).meta.trans,{attribute:t.$trans(l(i).meta.label)}),navs:[{label:t.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[n(b,{name:"Inventory",title:t.$trans("inventory.inventory"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(C,{appear:"",visibility:!0},{default:e(()=>[n(S,{"init-url":f,uuid:l(i).params.uuid,"module-uuid":l(i).params.muuid,onSetItem:g,onRedirectTo:u[1]||(u[1]=c=>l(_).push({name:"Inventory",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(m(),A(w,{key:0},{title:e(()=>[s(r(a.name),1)]),footer:e(()=>[n(I,null,{default:e(()=>[n(h,{design:"primary",onClick:u[0]||(u[0]=c=>l(_).push({name:"InventoryEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(r(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[N("dl",R,[n(d,{label:t.$trans("inventory.incharge.incharge")},{default:e(()=>[(m(!0),p(v,null,D(a.incharges,c=>(m(),p("div",null,[s(r(c.employee.name)+" ",1),n($,null,{default:e(()=>[s(r(c.employee.designation),1)]),_:2},1024)]))),256))]),_:1},8,["label"]),n(d,{class:"col-span-1 sm:col-span-2",label:t.$trans("inventory.props.description")},{default:e(()=>[s(r(a.description),1)]),_:1},8,["label"]),n(d,{label:t.$trans("general.created_at")},{default:e(()=>[s(r(a.createdAt.formatted),1)]),_:1},8,["label"]),n(d,{label:t.$trans("general.updated_at")},{default:e(()=>[s(r(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):H("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{M as default};
