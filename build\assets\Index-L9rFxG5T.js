import{u as A,l as h,n as H,r as a,q as B,o as D,w as e,d as k,e as t,b as R,h as q,j as N,y as U,m as j,f as u,a as E,F as O,v as z,s as l,t as r}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",emits:["hide"],setup(w,{emit:f}){const g=A(),b=f,$={title:"",categories:[],startDate:"",endDate:""},d=h({...$}),p=h({categories:[],isLoaded:!g.query.categories});return H(async()=>{p.categories=g.query.categories?g.query.categories.split(","):[],p.isLoaded=!0}),(m,s)=>{const i=a("BaseInput"),o=a("BaseSelectSearch"),C=a("DatePicker"),I=a("FilterForm");return D(),B(I,{"init-form":$,form:d,multiple:["categories"],onHide:s[4]||(s[4]=c=>b("hide"))},{default:e(()=>[k("div",G,[k("div",J,[t(i,{type:"text",modelValue:d.title,"onUpdate:modelValue":s[0]||(s[0]=c=>d.title=c),name:"title",label:m.$trans("discipline.incident.props.title")},null,8,["modelValue","label"])]),k("div",K,[p.isLoaded?(D(),B(o,{key:0,multiple:"",name:"categories",label:m.$trans("global.select",{attribute:m.$trans("discipline.incident.category.category")}),modelValue:d.categories,"onUpdate:modelValue":s[1]||(s[1]=c=>d.categories=c),"value-prop":"uuid","init-search":p.categories,"search-action":"option/list","additional-search-query":{type:"incident_category"}},null,8,["label","modelValue","init-search"])):R("",!0)]),k("div",Q,[t(C,{start:d.startDate,"onUpdate:start":s[2]||(s[2]=c=>d.startDate=c),end:d.endDate,"onUpdate:end":s[3]||(s[3]=c=>d.endDate=c),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},X={name:"DisciplineIncidentList"},Z=Object.assign(X,{setup(w){const f=q(),g=N("emitter");let b=["create","filter"];U("discipline:config")&&b.push("config");let $=["print","pdf","excel"];const d="discipline/incident/",p=j(!1),m=h({}),s=i=>{Object.assign(m,i)};return(i,o)=>{const C=a("PageHeaderAction"),I=a("PageHeader"),c=a("ParentTransition"),v=a("DataCell"),F=a("BaseBadge"),V=a("TextMuted"),y=a("FloatingMenuItem"),P=a("FloatingMenu"),S=a("DataRow"),T=a("BaseButton"),L=a("DataTable"),M=a("ListItem");return D(),B(M,{"init-url":d,onSetItems:s},{header:e(()=>[t(I,{title:i.$trans("discipline.incident.incident"),navs:[{label:i.$trans("discipline.discipline"),path:"Discipline"}]},{default:e(()=>[t(C,{url:"discipline/incidents/",name:"DisciplineIncident",title:i.$trans("discipline.incident.incident"),actions:u(b),"dropdown-actions":u($),"config-path":"DisciplineConfig",onToggleFilter:o[0]||(o[0]=n=>p.value=!p.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(c,{appear:"",visibility:p.value},{default:e(()=>[t(W,{onRefresh:o[1]||(o[1]=n=>u(g).emit("listItems")),onHide:o[2]||(o[2]=n=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(c,{appear:"",visibility:!0},{default:e(()=>[t(L,{header:m.headers,meta:m.meta,module:"discipline.incident",onRefresh:o[4]||(o[4]=n=>u(g).emit("listItems"))},{actionButton:e(()=>[t(T,{onClick:o[3]||(o[3]=n=>u(f).push({name:"DisciplineIncidentCreate"}))},{default:e(()=>[l(r(i.$trans("global.add",{attribute:i.$trans("discipline.incident.incident")})),1)]),_:1})]),default:e(()=>[(D(!0),E(O,null,z(m.data,n=>(D(),B(S,{key:n.uuid,onDoubleClick:_=>u(f).push({name:"DisciplineIncidentShow",params:{uuid:n.uuid}})},{default:e(()=>[t(v,{name:"category"},{default:e(()=>[l(r(n.category.name),1)]),_:2},1024),t(v,{name:"title"},{default:e(()=>[l(r(n.title)+" ",1),t(F,{design:n.nature.color},{default:e(()=>[l(r(n.nature.label),1)]),_:2},1032,["design"])]),_:2},1024),t(v,{name:"name"},{default:e(()=>[l(r(n.name)+" ",1),t(V,{block:""},{default:e(()=>[l(r(n.contactNumber),1)]),_:2},1024)]),_:2},1024),t(v,{name:"date"},{default:e(()=>{var _;return[l(r(((_=n.date)==null?void 0:_.formatted)||"-"),1)]}),_:2},1024),t(v,{name:"createdAt"},{default:e(()=>[l(r(n.createdAt.formatted),1)]),_:2},1024),t(v,{name:"action"},{default:e(()=>[t(P,null,{default:e(()=>[t(y,{icon:"fas fa-arrow-circle-right",onClick:_=>u(f).push({name:"DisciplineIncidentShow",params:{uuid:n.uuid}})},{default:e(()=>[l(r(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(y,{icon:"fas fa-edit",onClick:_=>u(f).push({name:"DisciplineIncidentEdit",params:{uuid:n.uuid}})},{default:e(()=>[l(r(i.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(y,{icon:"fas fa-copy",onClick:_=>u(f).push({name:"DisciplineIncidentDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[l(r(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(y,{icon:"fas fa-trash",onClick:_=>u(g).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[l(r(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Z as default};
