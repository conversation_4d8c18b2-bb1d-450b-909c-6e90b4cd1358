import{u as B,j as v,H as F,l as C,r as t,a as H,o as V,e as n,f as e,w as g,d,F as h}from"./app-BAwPsakn.js";const w={class:"mt-4 grid grid-cols-3 gap-4"},P={class:"col-span-3"},j={name:"BlogConfigGeneral"},U=Object.assign(j,{setup(x){const m=B(),o=v("$trans"),s="config/",i=F(s),c={enableBlog:!1,type:"blog"},l=C({...c});return(y,a)=>{const _=t("PageHeader"),b=t("CardHeader"),f=t("BaseSwitch"),p=t("FormAction"),u=t("ParentTransition");return V(),H(h,null,[n(_,{title:e(o)(e(m).meta.label),navs:[{label:e(o)("blog.blog"),path:"Blog"}]},null,8,["title","navs"]),n(u,{appear:"",visibility:!0},{default:g(()=>[n(p,{"init-url":s,"data-fetch":"blog",action:"store","init-form":c,form:l,"stay-on":"",redirect:"Blog"},{default:g(()=>[n(b,{first:"",title:e(o)("blog.config.general_config"),description:e(o)("blog.config.general_info")},null,8,["title","description"]),d("div",w,[d("div",P,[n(f,{vertical:"",modelValue:l.enableBlog,"onUpdate:modelValue":a[0]||(a[0]=r=>l.enableBlog=r),name:"enableBlog",label:e(o)("global.enable",{attribute:e(o)("blog.blog")}),error:e(i).enableBlog,"onUpdate:error":a[1]||(a[1]=r=>e(i).enableBlog=r)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{U as default};
