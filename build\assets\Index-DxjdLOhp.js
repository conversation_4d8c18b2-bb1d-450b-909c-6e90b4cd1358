import{u as j,l as M,n as z,r as l,q as y,o,w as t,d as w,a as d,b as v,e as n,s as m,t as u,h as G,j as R,c as J,m as K,f as i,F as I,v as H,y as Q}from"./app-BAwPsakn.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={key:0,class:"col-span-3 sm:col-span-1"},ee={key:1,class:"col-span-3 sm:col-span-1"},te={key:2,class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},ne={__name:"Filter",emits:["hide"],setup(c,{emit:S}){const g=j(),f=S,$={type:"",admissionNumber:"",employeeCode:"",batches:[],startDate:"",endDate:""},s=M({...$}),k=M({isLoaded:!g.query.batches});return z(async()=>{k.batches=g.query.batches?g.query.batches.split(","):[],k.isLoaded=!0}),(p,r)=>{const F=l("BaseSelect"),h=l("BaseInput"),T=l("BaseSelectSearch"),C=l("DatePicker"),_=l("FilterForm");return o(),y(_,{"init-form":$,form:s,multiple:["batches"],onHide:r[6]||(r[6]=e=>f("hide"))},{default:t(()=>[w("div",X,[w("div",Y,[n(F,{name:"type",label:p.$trans("general.type"),modelValue:s.type,"onUpdate:modelValue":r[0]||(r[0]=e=>s.type=e),options:[{label:p.$trans("employee.employee"),value:"employee"},{label:p.$trans("student.student"),value:"student"}]},null,8,["label","modelValue","options"])]),s.type=="student"?(o(),d("div",Z,[n(h,{type:"text",modelValue:s.admissionNumber,"onUpdate:modelValue":r[1]||(r[1]=e=>s.admissionNumber=e),name:"admissionNumber",label:p.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])])):v("",!0),s.type=="employee"?(o(),d("div",ee,[n(h,{type:"text",modelValue:s.employeeCode,"onUpdate:modelValue":r[2]||(r[2]=e=>s.employeeCode=e),name:"employeeCode",label:p.$trans("employee.props.code_number")},null,8,["modelValue","label"])])):v("",!0),s.type=="student"?(o(),d("div",te,[k.isLoaded?(o(),y(T,{key:0,multiple:"",name:"batches",label:p.$trans("global.select",{attribute:p.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":r[3]||(r[3]=e=>s.batches=e),"value-prop":"uuid","init-search":k.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(e=>[m(u(e.value.course.nameWithTerm)+" "+u(e.value.name),1)]),listOption:t(e=>[m(u(e.option.course.nameWithTerm)+" "+u(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)])):v("",!0),w("div",ae,[n(C,{start:s.startDate,"onUpdate:start":r[4]||(r[4]=e=>s.startDate=e),end:s.endDate,"onUpdate:end":r[5]||(r[5]=e=>s.endDate=e),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},oe={key:0},se=["innerHTML"],le={key:1},re={key:2},ue={key:0},ie={name:"FormSubmission"},de=Object.assign(ie,{props:{form:{type:Object,default(){return{}}}},setup(c){const S=j(),g=G(),f=R("$trans"),$=R("emitter"),s=c;let k=["filter"];const p="form/submission/",r=J(()=>s.form.fields.filter(_=>_.showValue)),F=K(!1),h=M({}),T=_=>{Object.assign(h,_)},C=(_,e)=>{let D=_.responses.find(V=>V.uuid===e.uuid);return D?D.type==="camera_image"?D.value.map(V=>`<a href="${V}" target="_blank">${f("general.image")}</a>`).join("<br>"):D.value:"-"};return(_,e)=>{const D=l("PageHeaderAction"),V=l("PageHeader"),N=l("ParentTransition"),B=l("DataCell"),L=l("TextMuted"),U=l("FloatingMenuItem"),P=l("FloatingMenu"),q=l("DataRow"),A=l("DataTable"),O=l("BaseHeading"),x=l("BaseDataView"),W=l("BaseCard"),E=l("ListItem");return o(),y(E,{"init-url":p,uuid:i(S).params.uuid,onSetItems:T},{header:t(()=>[c.form.uuid?(o(),y(V,{key:0,title:i(f)("form.submission.submission"),navs:[{label:i(f)("form.form"),path:"Form"},{label:c.form.name,path:{name:"FormShow",params:{uuid:c.form.uuid}}}]},{default:t(()=>[n(D,{url:`forms/${c.form.uuid}/submissions/`,name:"FormSubmission",title:i(f)("form.submission.submission"),actions:i(k),"dropdown-actions":["print","pdf","excel"],onToggleFilter:e[0]||(e[0]=a=>F.value=!F.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):v("",!0)]),filter:t(()=>[n(N,{appear:"",visibility:F.value},{default:t(()=>[n(ne,{onRefresh:e[1]||(e[1]=a=>i($).emit("listItems")),onHide:e[2]||(e[2]=a=>F.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[n(N,{appear:"",visibility:!0},{default:t(()=>[n(A,{header:h.headers,meta:h.meta,module:"form.submission",onRefresh:e[3]||(e[3]=a=>i($).emit("listItems"))},{actionButton:t(()=>e[4]||(e[4]=[])),default:t(()=>[(o(!0),d(I,null,H(h.data,a=>(o(),y(q,{key:a.uuid,onDoubleClick:b=>i(g).push({name:"FormSubmissionShow",params:{uuid:c.form.uuid,muuid:a.uuid}})},{default:t(()=>[n(B,{name:"name"},{default:t(()=>[m(u(a.type.label),1)]),_:2},1024),n(B,{name:"name"},{default:t(()=>[m(u(a.name)+" ",1),n(L,{block:""},{default:t(()=>[m(u(a.codeNumber),1)]),_:2},1024),a.detail?(o(),y(L,{key:0},{default:t(()=>[m(u(a.detail),1)]),_:2},1024)):v("",!0)]),_:2},1024),(o(!0),d(I,null,H(r.value,b=>(o(),y(B,null,{default:t(()=>[b.type.value==="camera_image"?(o(),d("div",oe,[w("div",{innerHTML:C(a,b)},null,8,se)])):b.type.value==="file_upload"?(o(),d("div",le," - ")):(o(),d("div",re,u(C(a,b)),1))]),_:2},1024))),256)),n(B,{name:"submittedAt"},{default:t(()=>[m(u(a.submittedAt.formatted),1)]),_:2},1024),n(B,{name:"action"},{default:t(()=>[n(P,null,{default:t(()=>[n(U,{icon:"fas fa-arrow-circle-right",onClick:b=>i(g).push({name:"FormSubmissionShow",params:{uuid:c.form.uuid,muuid:a.uuid}})},{default:t(()=>[m(u(i(f)("general.show")),1)]),_:2},1032,["onClick"]),i(Q)("form:edit")?(o(),y(U,{key:0,icon:"fas fa-trash",onClick:b=>i($).emit("deleteItem",{uuid:c.form.uuid,moduleUuid:a.uuid})},{default:t(()=>[m(u(i(f)("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"]),n(W,{class:"mt-4"},{title:t(()=>[m(u(c.form.name),1)]),default:t(()=>[n(O,null,{default:t(()=>[m(u(i(f)("form.props.question")),1)]),_:1}),(o(!0),d(I,null,H(r.value,(a,b)=>(o(),d("div",null,[n(x,null,{default:t(()=>[m("("+u(b+1)+") "+u(a.label)+" ",1),a.type.value=="file_upload"||a.type.value=="camera_image"?(o(),d("span",ue," ("+u(a.name)+") ",1)):v("",!0)]),_:2},1024)]))),256))]),_:1})]),_:1})]),_:1},8,["uuid"])}}});export{de as default};
