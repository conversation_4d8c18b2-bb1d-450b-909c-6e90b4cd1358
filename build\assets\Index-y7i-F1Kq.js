import{l as B,r as o,q as b,o as k,w as e,d as C,e as t,h as R,j,m as w,f as l,a as L,F as M,v as S,s as r,t as d}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},O={__name:"Filter",emits:["hide"],setup(I,{emit:u}){const p=u,_={name:"",code:""},m=B({..._});return(v,s)=>{const c=o("BaseInput"),D=o("FilterForm");return k(),b(D,{"init-form":_,form:m,onHide:s[2]||(s[2]=n=>p("hide"))},{default:e(()=>[C("div",N,[C("div",U,[t(c,{type:"text",modelValue:m.name,"onUpdate:modelValue":s[0]||(s[0]=n=>m.name=n),name:"name",label:v.$trans("device.props.name")},null,8,["modelValue","label"])]),C("div",E,[t(c,{type:"text",modelValue:m.code,"onUpdate:modelValue":s[1]||(s[1]=n=>m.code=n),name:"code",label:v.$trans("device.props.code")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},q={name:"DeviceList"},G=Object.assign(q,{setup(I){const u=R(),p=j("emitter");let _=["create","filter"],m=["print","pdf","excel"];const v="device/",s=w(!1);w(!1);const c=B({}),D=n=>{Object.assign(c,n)};return(n,i)=>{const h=o("PageHeaderAction"),V=o("PageHeader"),F=o("ParentTransition"),f=o("DataCell"),g=o("FloatingMenuItem"),y=o("FloatingMenu"),A=o("DataRow"),H=o("BaseButton"),P=o("DataTable"),T=o("ListItem");return k(),b(T,{"init-url":v,onSetItems:D},{header:e(()=>[t(V,{title:n.$trans("device.device"),navs:[]},{default:e(()=>[t(h,{url:"devices/",name:"Device",title:n.$trans("device.device"),actions:l(_),"dropdown-actions":l(m),onToggleFilter:i[0]||(i[0]=a=>s.value=!s.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title"])]),filter:e(()=>[t(F,{appear:"",visibility:s.value},{default:e(()=>[t(O,{onRefresh:i[1]||(i[1]=a=>l(p).emit("listItems")),onHide:i[2]||(i[2]=a=>s.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(F,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:c.headers,meta:c.meta,module:"device",onRefresh:i[4]||(i[4]=a=>l(p).emit("listItems"))},{actionButton:e(()=>[t(H,{onClick:i[3]||(i[3]=a=>l(u).push({name:"DeviceCreate"}))},{default:e(()=>[r(d(n.$trans("global.add",{attribute:n.$trans("device.device")})),1)]),_:1})]),default:e(()=>[(k(!0),L(M,null,S(c.data,a=>(k(),b(A,{key:a.uuid,onDoubleClick:$=>l(u).push({name:"DeviceShow",params:{uuid:a.uuid}})},{default:e(()=>[t(f,{name:"name"},{default:e(()=>[r(d(a.name),1)]),_:2},1024),t(f,{name:"code"},{default:e(()=>[r(d(a.code),1)]),_:2},1024),t(f,{name:"token"},{default:e(()=>[r(d(a.token),1)]),_:2},1024),t(f,{name:"createdAt"},{default:e(()=>[r(d(a.createdAt.formatted),1)]),_:2},1024),t(f,{name:"action"},{default:e(()=>[t(y,null,{default:e(()=>[t(g,{icon:"fas fa-arrow-circle-right",onClick:$=>l(u).push({name:"DeviceShow",params:{uuid:a.uuid}})},{default:e(()=>[r(d(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-edit",onClick:$=>l(u).push({name:"DeviceEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(d(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-copy",onClick:$=>l(u).push({name:"DeviceDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(d(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-trash",onClick:$=>l(p).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(d(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{G as default};
