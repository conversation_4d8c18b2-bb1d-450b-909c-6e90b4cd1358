import{i as d,m as l,l as i,g as u,n as _,r as m,a as o,o as a,q as f,b as r,d as C}from"./app-BAwPsakn.js";const h={class:"flex items-center justify-center h-screen"},p={key:1,class:"w-96 h-96"},v=["src"],k={__name:"QrCode",setup(q){const n=d(),e=l(!1),t=i({qrCode:null});return u("system.qrCodeAttendanceEnabled").value,_(()=>{e.value=!0,n.dispatch("attendanceAssistant/fetchQrCode").then(s=>{t.qrCode=s}).catch(s=>{}).finally(()=>{e.value=!1})}),(s,B)=>{const c=m("BaseLoader");return a(),o("div",h,[e.value?(a(),f(c,{key:0})):r("",!0),t.qrCode?(a(),o("div",p,[C("img",{src:t.qrCode,alt:"QR Code"},null,8,v)])):r("",!0)])}}};export{k as default};
