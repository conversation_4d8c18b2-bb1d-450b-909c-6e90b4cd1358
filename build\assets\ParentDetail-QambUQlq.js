import{r as a,q as d,o as i,w as t,b as p,d as _,e as n,s as o,t as s}from"./app-BAwPsakn.js";const f={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},h={__name:"ParentDetail",props:{student:{type:Object,required:!0}},setup(e){return(r,C)=>{const c=a("CopyContent"),l=a("BaseDataView"),m=a("BaseCard"),u=a("ParentTransition");return i(),d(u,{appear:"",visibility:!0},{default:t(()=>[e.student.contact?(i(),d(m,{key:0,class:"mb-4"},{title:t(()=>[o(s(e.student.contact.name),1)]),default:t(()=>[_("dl",f,[n(l,{label:r.$trans("contact.props.father_name")},{default:t(()=>[n(c,null,{default:t(()=>[o(s(e.student.contact.fatherName),1)]),_:1})]),_:1},8,["label"]),n(l,{label:r.$trans("contact.props.mother_name")},{default:t(()=>[n(c,null,{default:t(()=>[o(s(e.student.contact.motherName),1)]),_:1})]),_:1},8,["label"])])]),_:1})):p("",!0)]),_:1})}}};export{h as _};
