import{u as f,l as _,H as q,r as l,q as F,o as b,w as u,d as p,e as i,f as e,a as U,F as y}from"./app-BAwPsakn.js";const k={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 flex items-end sm:col-span-2"},O={class:"col-span-3"},T={name:"OptionForm"},x=Object.assign(T,{setup(g){const t=f(),s={name:"",color:"",description:""},c="option/",d=_({colors:[]}),a=q(c),n=_({...s}),$=m=>{Object.assign(d,m)};return(m,o)=>{const V=l("BaseInput"),v=l("SelectedColorPicker"),P=l("BaseTextarea"),B=l("FormAction");return b(),F(B,{"pre-requisites":{data:e(t).meta.queryType},onSetPreRequisites:$,"init-url":c,"init-form":s,form:n,redirect:e(t).meta.prefix},{default:u(()=>[p("div",k,[p("div",A,[i(V,{type:"text",modelValue:n.name,"onUpdate:modelValue":o[0]||(o[0]=r=>n.name=r),name:"name",label:m.$trans(`${e(t).meta.transKey}.props.name`),error:e(a).name,"onUpdate:error":o[1]||(o[1]=r=>e(a).name=r),autofocus:""},null,8,["modelValue","label","error"])]),p("div",H,[i(v,{modelValue:n.color,"onUpdate:modelValue":o[2]||(o[2]=r=>n.color=r),label:m.$trans("general.color"),error:e(a).color,"onUpdate:error":o[3]||(o[3]=r=>e(a).color=r)},null,8,["modelValue","label","error"])]),p("div",O,[i(P,{modelValue:n.description,"onUpdate:modelValue":o[4]||(o[4]=r=>n.description=r),name:"description",label:m.$trans(`${e(t).meta.transKey}.props.description`),error:e(a).description,"onUpdate:error":o[5]||(o[5]=r=>e(a).description=r)},null,8,["modelValue","label","error"])])])]),_:1},8,["pre-requisites","form","redirect"])}}}),C={name:"OptionAction"},j=Object.assign(C,{setup(g){const t=f();return(s,c)=>{const d=l("PageHeaderAction"),a=l("PageHeader"),n=l("ParentTransition");return b(),U(y,null,[i(a,{title:s.$trans(e(t).meta.trans,{attribute:s.$trans(e(t).meta.label)}),navs:[...e(t).meta.navs,{label:s.$trans(e(t).meta.label),path:`${e(t).meta.prefix}List`}]},{default:u(()=>[i(d,{name:e(t).meta.prefix,title:s.$trans(e(t).meta.label),actions:["list"]},null,8,["name","title"])]),_:1},8,["title","navs"]),i(n,{appear:"",visibility:!0},{default:u(()=>[i(x)]),_:1})],64)}}});export{j as default};
