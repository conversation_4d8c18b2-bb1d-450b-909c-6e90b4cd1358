import{u as Y,j as Z,H as h,g as N,l as P,n as x,r as p,a as $,o as m,q as y,b,e as s,f as a,w as L,d as u,s as B,t as E,I as D,F as _,J as ee}from"./app-BAwPsakn.js";import{u as oe}from"./useCustomFields-C7JPVoj8.js";const te={class:"grid grid-cols-3 gap-6"},ae={class:"col-span-3 sm:col-span-2"},le={class:"flex"},re={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Ie={key:0,class:"col-span-3 sm:col-span-1"},fe={key:1,class:"col-span-3 sm:col-span-1"},Ue={key:2,class:"col-span-3 sm:col-span-1"},ve={name:"EmployeeEditBasic"},Le=Object.assign(ve,{props:{employee:{type:Object,default(){return{}}}},setup(c){const S=Y(),C=Z("emitter"),n=c,q={firstName:"",middleName:"",thirdName:"",lastName:"",gender:"",birthDate:"",fatherName:"",motherName:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",uniqueIdNumber4:"",uniqueIdNumber5:"",birthPlace:"",nationality:"",motherTongue:"",maritalStatus:"",bloodGroup:"",religion:"",category:"",caste:"",type:"",customFields:[]},k="employee/",l=h(k),G=N("employee.uniqueIdNumber1Label"),T=N("employee.uniqueIdNumber2Label"),j=N("employee.uniqueIdNumber3Label"),R=N("employee.uniqueIdNumber4Label"),O=N("employee.uniqueIdNumber5Label"),w=N("contact.enableCategoryField"),H=N("contact.enableCasteField"),d=P({types:[],genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[],customFields:[]}),t=P({...q}),g=P({isLoaded:!S.params.uuid}),{customFields:A,setCustomFields:z}=oe(),J=r=>{Object.assign(d,r),M()},M=()=>{var r,e,f,I,U,v,F;Object.assign(q,{firstName:n.employee.contact.firstName,middleName:n.employee.contact.middleName,thirdName:n.employee.contact.thirdName,lastName:n.employee.contact.lastName,gender:(r=n.employee.contact.gender)==null?void 0:r.value,birthDate:(e=n.employee.contact.birthDate)==null?void 0:e.value,fatherName:n.employee.contact.fatherName,motherName:n.employee.contact.motherName,uniqueIdNumber1:n.employee.contact.uniqueIdNumber1,uniqueIdNumber2:n.employee.contact.uniqueIdNumber2,uniqueIdNumber3:n.employee.contact.uniqueIdNumber3,uniqueIdNumber4:n.employee.contact.uniqueIdNumber4,uniqueIdNumber5:n.employee.contact.uniqueIdNumber5,birthPlace:n.employee.contact.birthPlace,nationality:n.employee.contact.nationality,motherTongue:n.employee.contact.motherTongue,bloodGroup:((f=n.employee.contact.bloodGroup)==null?void 0:f.value)||"",maritalStatus:((I=n.employee.contact.maritalStatus)==null?void 0:I.value)||"",religion:((U=n.employee.contact.religion)==null?void 0:U.uuid)||"",category:((v=n.employee.contact.category)==null?void 0:v.uuid)||"",caste:((F=n.employee.contact.caste)==null?void 0:F.uuid)||"",type:n.employee.type.value}),z(d.customFields,n.employee.contact.customFields),q.customFields=A.value,Object.assign(t,ee(q)),g.isLoaded=!0};x(async()=>{});const K=()=>{C.emit("employeeUpdated")};return(r,e)=>{const f=p("PageHeader"),I=p("BaseLabel"),U=p("NameInput"),v=p("BaseRadioGroup"),F=p("DatePicker"),i=p("BaseInput"),V=p("BaseSelect"),Q=p("CustomField"),W=p("FormAction"),X=p("ParentTransition");return m(),$(_,null,[c.employee.uuid?(m(),y(f,{key:0,title:r.$trans(a(S).meta.trans,{attribute:r.$trans(a(S).meta.label)}),navs:[{label:r.$trans("employee.employee"),path:"Employee"},{label:c.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:c.employee.uuid}}}]},null,8,["title","navs"])):b("",!0),s(X,{appear:"",visibility:!0},{default:L(()=>[c.employee.uuid?(m(),y(W,{key:0,"pre-requisites":!0,onSetPreRequisites:J,"init-url":k,"no-data-fetch":"","init-form":q,form:t,"stay-on":"","after-submit":K,redirect:{name:"EmployeeShowBasic",params:{uuid:c.employee.uuid}}},{default:L(()=>[u("div",te,[u("div",ae,[s(I,null,{default:L(()=>[B(E(r.$trans("employee.props.name")),1)]),_:1}),u("div",le,[s(U,{firstName:t.firstName,"onUpdate:firstName":e[0]||(e[0]=o=>t.firstName=o),middleName:t.middleName,"onUpdate:middleName":e[1]||(e[1]=o=>t.middleName=o),thirdName:t.thirdName,"onUpdate:thirdName":e[2]||(e[2]=o=>t.thirdName=o),lastName:t.lastName,"onUpdate:lastName":e[3]||(e[3]=o=>t.lastName=o),formErrors:a(l),"onUpdate:formErrors":e[4]||(e[4]=o=>D(l)?l.value=o:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),u("div",re,[s(I,null,{default:L(()=>[B(E(r.$trans("contact.props.gender")),1)]),_:1}),s(v,{"top-margin":"",options:d.genders,name:"gender",modelValue:t.gender,"onUpdate:modelValue":e[5]||(e[5]=o=>t.gender=o),error:a(l).gender,"onUpdate:error":e[6]||(e[6]=o=>a(l).gender=o),horizontal:""},null,8,["options","modelValue","error"])]),u("div",ne,[s(F,{modelValue:t.birthDate,"onUpdate:modelValue":e[7]||(e[7]=o=>t.birthDate=o),name:"birthDate",label:r.$trans("contact.props.birth_date"),"no-clear":"",error:a(l).birthDate,"onUpdate:error":e[8]||(e[8]=o=>a(l).birthDate=o)},null,8,["modelValue","label","error"])]),u("div",se,[s(i,{type:"text",modelValue:t.fatherName,"onUpdate:modelValue":e[9]||(e[9]=o=>t.fatherName=o),name:"fatherName",label:r.$trans("contact.props.father_name"),error:a(l).fatherName,"onUpdate:error":e[10]||(e[10]=o=>a(l).fatherName=o)},null,8,["modelValue","label","error"])]),u("div",ue,[s(i,{type:"text",modelValue:t.motherName,"onUpdate:modelValue":e[11]||(e[11]=o=>t.motherName=o),name:"motherName",label:r.$trans("contact.props.mother_name"),error:a(l).motherName,"onUpdate:error":e[12]||(e[12]=o=>a(l).motherName=o)},null,8,["modelValue","label","error"])]),u("div",me,[s(i,{type:"text",modelValue:t.uniqueIdNumber1,"onUpdate:modelValue":e[13]||(e[13]=o=>t.uniqueIdNumber1=o),name:"uniqueIdNumber1",label:a(G),error:a(l).uniqueIdNumber1,"onUpdate:error":e[14]||(e[14]=o=>a(l).uniqueIdNumber1=o)},null,8,["modelValue","label","error"])]),u("div",de,[s(i,{type:"text",modelValue:t.uniqueIdNumber2,"onUpdate:modelValue":e[15]||(e[15]=o=>t.uniqueIdNumber2=o),name:"uniqueIdNumber2",label:a(T),error:a(l).uniqueIdNumber2,"onUpdate:error":e[16]||(e[16]=o=>a(l).uniqueIdNumber2=o)},null,8,["modelValue","label","error"])]),u("div",ie,[s(i,{type:"text",modelValue:t.uniqueIdNumber3,"onUpdate:modelValue":e[17]||(e[17]=o=>t.uniqueIdNumber3=o),name:"uniqueIdNumber3",label:a(j),error:a(l).uniqueIdNumber3,"onUpdate:error":e[18]||(e[18]=o=>a(l).uniqueIdNumber3=o)},null,8,["modelValue","label","error"])]),u("div",pe,[s(i,{type:"text",modelValue:t.uniqueIdNumber4,"onUpdate:modelValue":e[19]||(e[19]=o=>t.uniqueIdNumber4=o),name:"uniqueIdNumber4",label:a(R),error:a(l).uniqueIdNumber4,"onUpdate:error":e[20]||(e[20]=o=>a(l).uniqueIdNumber4=o)},null,8,["modelValue","label","error"])]),u("div",be,[s(i,{type:"text",modelValue:t.uniqueIdNumber5,"onUpdate:modelValue":e[21]||(e[21]=o=>t.uniqueIdNumber5=o),name:"uniqueIdNumber5",label:a(O),error:a(l).uniqueIdNumber5,"onUpdate:error":e[22]||(e[22]=o=>a(l).uniqueIdNumber5=o)},null,8,["modelValue","label","error"])]),u("div",Ne,[s(i,{type:"text",modelValue:t.birthPlace,"onUpdate:modelValue":e[23]||(e[23]=o=>t.birthPlace=o),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:a(l).birthPlace,"onUpdate:error":e[24]||(e[24]=o=>a(l).birthPlace=o)},null,8,["modelValue","label","error"])]),u("div",ye,[s(i,{type:"text",modelValue:t.nationality,"onUpdate:modelValue":e[25]||(e[25]=o=>t.nationality=o),name:"nationality",label:r.$trans("contact.props.nationality"),error:a(l).nationality,"onUpdate:error":e[26]||(e[26]=o=>a(l).nationality=o)},null,8,["modelValue","label","error"])]),u("div",ce,[s(i,{type:"text",modelValue:t.motherTongue,"onUpdate:modelValue":e[27]||(e[27]=o=>t.motherTongue=o),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:a(l).motherTongue,"onUpdate:error":e[28]||(e[28]=o=>a(l).motherTongue=o)},null,8,["modelValue","label","error"])]),u("div",ge,[g.isLoaded?(m(),y(V,{key:0,modelValue:t.bloodGroup,"onUpdate:modelValue":e[29]||(e[29]=o=>t.bloodGroup=o),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:d.bloodGroups,error:a(l).bloodGroup,"onUpdate:error":e[30]||(e[30]=o=>a(l).bloodGroup=o)},null,8,["modelValue","label","options","error"])):b("",!0)]),u("div",Ve,[g.isLoaded?(m(),y(V,{key:0,modelValue:t.maritalStatus,"onUpdate:modelValue":e[31]||(e[31]=o=>t.maritalStatus=o),name:"maritalStatus",label:r.$trans("contact.props.marital_status"),options:d.maritalStatuses,error:a(l).maritalStatus,"onUpdate:error":e[32]||(e[32]=o=>a(l).maritalStatus=o)},null,8,["modelValue","label","options","error"])):b("",!0)]),u("div",qe,[g.isLoaded?(m(),y(V,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:t.religion,"onUpdate:modelValue":e[33]||(e[33]=o=>t.religion=o),error:a(l).religion,"onUpdate:error":e[34]||(e[34]=o=>a(l).religion=o),options:d.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):b("",!0)]),a(w)?(m(),$("div",Ie,[g.isLoaded?(m(),y(V,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:t.category,"onUpdate:modelValue":e[35]||(e[35]=o=>t.category=o),error:a(l).category,"onUpdate:error":e[36]||(e[36]=o=>a(l).category=o),options:d.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):b("",!0)])):b("",!0),a(H)?(m(),$("div",fe,[g.isLoaded?(m(),y(V,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:t.caste,"onUpdate:modelValue":e[37]||(e[37]=o=>t.caste=o),error:a(l).caste,"onUpdate:error":e[38]||(e[38]=o=>a(l).caste=o),options:d.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):b("",!0)])):b("",!0),d.types.length?(m(),$("div",Ue,[s(V,{name:"type",label:r.$trans("global.select",{attribute:r.$trans("employee.type")}),options:d.types,modelValue:t.type,"onUpdate:modelValue":e[39]||(e[39]=o=>t.type=o),error:a(l).type,"onUpdate:error":e[40]||(e[40]=o=>a(l).type=o)},null,8,["label","options","modelValue","error"])])):b("",!0)]),s(Q,{customFields:t.customFields,"onUpdate:customFields":e[41]||(e[41]=o=>t.customFields=o),formErrors:a(l),"onUpdate:formErrors":e[42]||(e[42]=o=>D(l)?l.value=o:null)},null,8,["customFields","formErrors"])]),_:1},8,["form","redirect"])):b("",!0)]),_:1})],64)}}});export{Le as default};
