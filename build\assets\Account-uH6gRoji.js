import{i as $,H as F,k as p,c as U,m as d,l as N,r as c,q as C,o as f,w as v,e as s,d as E,a as w,b as x,f as r,s as I,t as T}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},D={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},j={key:0,class:"col-span-3 sm:col-span-2"},H={key:1,class:"col-span-3 sm:col-span-2"},R={name:"UserAccount"},J=Object.assign(R,{setup(z){$();const b="user/profile/",i=F(b),V=p("email"),y=p("username"),A=p("isSuperAdmin"),g=U(()=>!!(n.value||u.value)),_=U(()=>o.value?"verifyAccount":"updateAccount"),o=d(!1),n=d(!1),u=d(!1),O={username:y.value,email:V.value,existingEmailOtp:"",newEmailOtp:""},t=N({...O}),k=l=>{l.existingEmailVerification&&(o.value=!0,n.value=!0),l.newEmailVerification&&(o.value=!0,u.value=!0),l.profileUpdated&&(o.value=!1,n.value=!1,u.value=!1,t.existingEmailOtp="",t.newEmailOtp="")};return(l,e)=>{const m=c("BaseInput"),B=c("FormAction"),S=c("ParentTransition");return f(),C(S,{appear:"",visibility:!0},{default:v(()=>[s(B,{"no-card":"","init-url":b,action:_.value,"init-form":O,form:t,"after-submit":k,"stay-on":"",redirect:"Dashboard"},{title:v(()=>[I(T(l.$trans("user.profile.account")),1)]),default:v(()=>[E("div",q,[E("div",D,[s(m,{disabled:g.value||r(A),type:"text",modelValue:t.username,"onUpdate:modelValue":e[0]||(e[0]=a=>t.username=a),name:"username",label:l.$trans("user.profile.props.username"),error:r(i).username,"onUpdate:error":e[1]||(e[1]=a=>r(i).username=a)},null,8,["disabled","modelValue","label","error"])]),E("div",P,[s(m,{disabled:g.value,type:"text",modelValue:t.email,"onUpdate:modelValue":e[2]||(e[2]=a=>t.email=a),name:"email",label:l.$trans("user.profile.props.email"),error:r(i).email,"onUpdate:error":e[3]||(e[3]=a=>r(i).email=a)},null,8,["disabled","modelValue","label","error"])]),n.value?(f(),w("div",j,[s(m,{type:"password",modelValue:t.existingEmailOtp,"onUpdate:modelValue":e[4]||(e[4]=a=>t.existingEmailOtp=a),name:"existingEmailOtp",label:l.$trans("user.profile.verification_otp",{attribute:r(V)}),error:r(i).existingEmailOtp,"onUpdate:error":e[5]||(e[5]=a=>r(i).existingEmailOtp=a)},null,8,["modelValue","label","error"])])):x("",!0),u.value?(f(),w("div",H,[s(m,{type:"password",modelValue:t.newEmailOtp,"onUpdate:modelValue":e[6]||(e[6]=a=>t.newEmailOtp=a),name:"newEmailOtp",label:l.$trans("user.profile.verification_otp",{attribute:t.email}),error:r(i).newEmailOtp,"onUpdate:error":e[7]||(e[7]=a=>r(i).newEmailOtp=a)},null,8,["modelValue","label","error"])])):x("",!0)])]),_:1},8,["action","form"])]),_:1})}}});export{J as default};
