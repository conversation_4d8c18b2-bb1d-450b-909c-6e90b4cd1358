import{u as L,l as B,H as T,n as P,r as u,q as F,o as _,w as c,d as n,e as r,f as d,b as U,s as b,t as h,h as R,i as M,j as I,m as W,a as V,F as E,v as z,a9 as D,J}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"mt-4 grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(A,{emit:g}){const k=L(),m=g,f=A,y={date:"",batch:"",vision:!1,dental:!1},o=B({...y}),p=T(f.initUrl),$=B({batch:"",isLoaded:!k.query.batch});return P(async()=>{$.batch=k.query.batch,$.isLoaded=!0}),(e,t)=>{const H=u("DatePicker"),S=u("BaseSelectSearch"),w=u("BaseSwitch"),q=u("FilterForm");return _(),F(q,{"init-form":y,form:o,onHide:t[6]||(t[6]=s=>m("hide"))},{default:c(()=>[n("div",G,[n("div",K,[r(H,{modelValue:o.date,"onUpdate:modelValue":t[0]||(t[0]=s=>o.date=s),name:"date",label:e.$trans("student.health_record.props.date"),"no-clear":"",error:d(p).date,"onUpdate:error":t[1]||(t[1]=s=>d(p).date=s)},null,8,["modelValue","label","error"])]),n("div",Q,[$.isLoaded?(_(),F(S,{key:0,name:"batch",label:e.$trans("global.select",{attribute:e.$trans("academic.batch.batch")}),modelValue:o.batch,"onUpdate:modelValue":t[2]||(t[2]=s=>o.batch=s),error:d(p).batch,"onUpdate:error":t[3]||(t[3]=s=>d(p).batch=s),"value-prop":"uuid","init-search":$.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:c(s=>[b(h(s.value.course.nameWithTerm)+" "+h(s.value.name),1)]),listOption:c(s=>[b(h(s.option.course.nameWithTerm)+" "+h(s.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):U("",!0)])]),n("div",X,[n("div",Y,[r(w,{vertical:"",modelValue:o.vision,"onUpdate:modelValue":t[4]||(t[4]=s=>o.vision=s),name:"vision",label:e.$trans("global.record",{attribute:e.$trans("student.health_record.props.vision")})},null,8,["modelValue","label"])]),n("div",Z,[r(w,{vertical:"",modelValue:o.dental,"onUpdate:modelValue":t[5]||(t[5]=s=>o.dental=s),name:"dental",label:e.$trans("global.record",{attribute:e.$trans("student.health_record.props.dental")})},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ee={class:"p-2"},te={class:"divide-y divide-gray-200 dark:divide-gray-700"},ae={class:"grid grid-cols-4 gap-6 px-4 py-2"},oe={class:"col-span-4 sm:col-span-1"},se={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},le={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-1"},de={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},ce={class:"col-span-4 sm:col-span-1"},ue={class:"col-span-4 sm:col-span-3"},pe={name:"StudentHealthRecord"},he=Object.assign(pe,{setup(A){const g=L();R();const k=M();I("emitter");const m={date:"",batch:"",students:[],vision:!1,dental:!1},f="student/healthRecord/",y=W(!1);B({});const o=T(f),p=B({...m});B({});const $=async()=>{y.value=!0,await k.dispatch(f+"fetch",{params:g.query}).then(e=>{y.value=!1,m.date=g.query.date,m.batch=g.query.batch,m.vision=D(g.query.vision),m.dental=D(g.query.dental),m.students=e.data,Object.assign(p,J(m))}).catch(e=>{y.value=!1})};return P(async()=>{g.query.batch&&await $()}),(e,t)=>{const H=u("PageHeaderAction"),S=u("PageHeader"),w=u("ParentTransition"),q=u("BaseAlert"),s=u("BaseLabel"),C=u("TextMuted"),N=u("BaseDataView"),v=u("BaseInput"),j=u("FormAction"),O=u("BaseCard");return _(),V(E,null,[r(S,{title:e.$trans(d(g).meta.label),navs:[{label:e.$trans("student.student"),path:"Student"}]},{default:c(()=>[r(H)]),_:1},8,["title","navs"]),r(w,{appear:"",visibility:!0},{default:c(()=>[r(x,{onAfterFilter:$,"init-url":f})]),_:1}),r(O,{"no-padding":"","no-content-padding":"","is-loading":y.value},{title:c(()=>[b(h(e.$trans("global.update",{attribute:e.$trans("student.health_record.health_record")})),1)]),action:c(()=>t[0]||(t[0]=[])),default:c(()=>[n("div",ee,[p.students.length==0?(_(),F(q,{key:0,size:"xs",design:"error"},{default:c(()=>[b(h(e.$trans("general.errors.record_not_found")),1)]),_:1})):U("",!0)]),p.students.length?(_(),F(j,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":f,action:"store","init-form":m,form:p},{default:c(()=>[n("div",te,[n("div",ae,[n("div",oe,[r(s,null,{default:c(()=>[b(h(e.$trans("student.student")),1)]),_:1})]),n("div",se,[r(s,null,{default:c(()=>[b(h(e.$trans("student.health_record.health_record")),1)]),_:1})])]),(_(!0),V(E,null,z(p.students,(i,l)=>(_(),V("div",{class:"grid grid-cols-4 gap-6 px-4 py-2",key:i.uuid},[n("div",ne,[r(N,null,{default:c(()=>[b(h(i.name)+" ",1),r(C,{block:""},{default:c(()=>[b(h(i.codeNumber),1)]),_:2},1024)]),_:2},1024)]),n("div",le,[r(v,{type:"number",min:1,"leading-text":e.$trans("list.distances.cm_short"),modelValue:i.height,"onUpdate:modelValue":a=>i.height=a,name:`student.${l}.height`,placeholder:e.$trans("student.health_record.props.height"),error:d(o)[`students.${l}.height`],"onUpdate:error":a=>d(o)[`students.${l}.height`]=a},null,8,["leading-text","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),n("div",re,[r(v,{type:"number",min:1,"leading-text":e.$trans("list.unit.kg"),modelValue:i.weight,"onUpdate:modelValue":a=>i.weight=a,name:`student.${l}.weight`,placeholder:e.$trans("student.health_record.props.weight"),error:d(o)[`students.${l}.weight`],"onUpdate:error":a=>d(o)[`students.${l}.weight`]=a},null,8,["leading-text","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),n("div",de,[r(v,{type:"number",min:1,"leading-text":e.$trans("list.distances.in"),modelValue:i.chest,"onUpdate:modelValue":a=>i.chest=a,name:`student.${l}.chest`,placeholder:e.$trans("student.health_record.props.chest"),error:d(o)[`students.${l}.chest`],"onUpdate:error":a=>d(o)[`students.${l}.chest`]=a},null,8,["leading-text","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),p.vision?(_(),V(E,{key:0},[t[1]||(t[1]=n("div",{class:"col-span-4 sm:col-span-1"},null,-1)),n("div",ie,[r(v,{type:"number","leading-text":e.$trans("list.unit.diopters"),modelValue:i.leftEye,"onUpdate:modelValue":a=>i.leftEye=a,name:`student.${l}.leftEye`,placeholder:e.$trans("student.health_record.props.left_eye"),error:d(o)[`students.${l}.leftEye`],"onUpdate:error":a=>d(o)[`students.${l}.leftEye`]=a},null,8,["leading-text","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),n("div",ce,[r(v,{type:"number","leading-text":e.$trans("list.unit.diopters"),modelValue:i.rightEye,"onUpdate:modelValue":a=>i.rightEye=a,name:`student.${l}.rightEye`,placeholder:e.$trans("student.health_record.props.right_eye"),error:d(o)[`students.${l}.rightEye`],"onUpdate:error":a=>d(o)[`students.${l}.rightEye`]=a},null,8,["leading-text","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),t[2]||(t[2]=n("div",{class:"col-span-4 sm:col-span-1"},null,-1))],64)):U("",!0),p.dental?(_(),V(E,{key:1},[t[3]||(t[3]=n("div",{class:"col-span-4 sm:col-span-1"},null,-1)),n("div",ue,[r(v,{type:"text",modelValue:i.dentalHygiene,"onUpdate:modelValue":a=>i.dentalHygiene=a,name:`student.${l}.dentalHygiene`,placeholder:e.$trans("student.health_record.props.dental_hygiene"),error:d(o)[`students.${l}.dentalHygiene`],"onUpdate:error":a=>d(o)[`students.${l}.dentalHygiene`]=a},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])],64)):U("",!0)]))),128))])]),_:1},8,["form"])):U("",!0)]),_:1},8,["is-loading"])],64)}}});export{he as default};
