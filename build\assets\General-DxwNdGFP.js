import{u as v,j as S,H as D,c as U,l as g,r as a,a as _,o as L,e as l,f as o,w as m,d as s,s as B,t as R,F}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3"},W={name:"ReceptionConfigGeneral"},Z=Object.assign(W,{setup(X){const N=v(),n=S("$trans"),p="config/",t=D(p),x=U(()=>n("global.placeholder_info",{attribute:b.datePlaceholders})),b=g({datePlaceholders:""}),d={enquiryNumberPrefix:"",enquiryNumberSuffix:"",enquiryNumberDigit:0,visitorLogNumberPrefix:"",visitorLogNumberSuffix:"",visitorLogNumberDigit:0,gatePassNumberPrefix:"",gatePassNumberSuffix:"",gatePassNumberDigit:0,complaintNumberPrefix:"",complaintNumberSuffix:"",complaintNumberDigit:0,queryNumberPrefix:"",queryNumberSuffix:"",queryNumberDigit:0,type:"reception"},i=g({...d}),y=f=>{Object.assign(b,{datePlaceholders:f.datePlaceholders.map(e=>e.value).join(", ")})};return(f,e)=>{const P=a("PageHeader"),u=a("BaseInput"),c=a("BaseAlert"),V=a("FormAction"),q=a("ParentTransition");return L(),_(F,null,[l(P,{title:o(n)(o(N).meta.label),navs:[{label:o(n)("reception.reception"),path:"Reception"}]},null,8,["title","navs"]),l(q,{appear:"",visibility:!0},{default:m(()=>[l(V,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:y,"init-url":p,"data-fetch":"reception","init-form":d,form:i,action:"store","stay-on":"",redirect:"Reception"},{default:m(()=>[s("div",j,[s("div",A,[l(u,{type:"text",modelValue:i.enquiryNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=r=>i.enquiryNumberPrefix=r),name:"enquiryNumberPrefix",label:o(n)("reception.enquiry.config.props.number_prefix"),error:o(t).enquiryNumberPrefix,"onUpdate:error":e[1]||(e[1]=r=>o(t).enquiryNumberPrefix=r)},null,8,["modelValue","label","error"])]),s("div",C,[l(u,{type:"number",modelValue:i.enquiryNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=r=>i.enquiryNumberDigit=r),name:"enquiryNumberDigit",label:o(n)("reception.enquiry.config.props.number_digit"),error:o(t).enquiryNumberDigit,"onUpdate:error":e[3]||(e[3]=r=>o(t).enquiryNumberDigit=r)},null,8,["modelValue","label","error"])]),s("div",E,[l(u,{type:"text",modelValue:i.enquiryNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=r=>i.enquiryNumberSuffix=r),name:"enquiryNumberSuffix",label:o(n)("reception.enquiry.config.props.number_suffix"),error:o(t).enquiryNumberSuffix,"onUpdate:error":e[5]||(e[5]=r=>o(t).enquiryNumberSuffix=r)},null,8,["modelValue","label","error"])]),s("div",H,[l(u,{type:"text",modelValue:i.visitorLogNumberPrefix,"onUpdate:modelValue":e[6]||(e[6]=r=>i.visitorLogNumberPrefix=r),name:"visitorLogNumberPrefix",label:o(n)("reception.visitor_log.config.props.number_prefix"),error:o(t).visitorLogNumberPrefix,"onUpdate:error":e[7]||(e[7]=r=>o(t).visitorLogNumberPrefix=r)},null,8,["modelValue","label","error"])]),s("div",I,[l(u,{type:"number",modelValue:i.visitorLogNumberDigit,"onUpdate:modelValue":e[8]||(e[8]=r=>i.visitorLogNumberDigit=r),name:"visitorLogNumberDigit",label:o(n)("reception.visitor_log.config.props.number_digit"),error:o(t).visitorLogNumberDigit,"onUpdate:error":e[9]||(e[9]=r=>o(t).visitorLogNumberDigit=r)},null,8,["modelValue","label","error"])]),s("div",T,[l(u,{type:"text",modelValue:i.visitorLogNumberSuffix,"onUpdate:modelValue":e[10]||(e[10]=r=>i.visitorLogNumberSuffix=r),name:"visitorLogNumberSuffix",label:o(n)("reception.visitor_log.config.props.number_suffix"),error:o(t).visitorLogNumberSuffix,"onUpdate:error":e[11]||(e[11]=r=>o(t).visitorLogNumberSuffix=r)},null,8,["modelValue","label","error"])]),s("div",k,[l(u,{type:"text",modelValue:i.gatePassNumberPrefix,"onUpdate:modelValue":e[12]||(e[12]=r=>i.gatePassNumberPrefix=r),name:"gatePassNumberPrefix",label:o(n)("reception.gate_pass.config.props.number_prefix"),error:o(t).gatePassNumberPrefix,"onUpdate:error":e[13]||(e[13]=r=>o(t).gatePassNumberPrefix=r)},null,8,["modelValue","label","error"])]),s("div",w,[l(u,{type:"number",modelValue:i.gatePassNumberDigit,"onUpdate:modelValue":e[14]||(e[14]=r=>i.gatePassNumberDigit=r),name:"gatePassNumberDigit",label:o(n)("reception.gate_pass.config.props.number_digit"),error:o(t).gatePassNumberDigit,"onUpdate:error":e[15]||(e[15]=r=>o(t).gatePassNumberDigit=r)},null,8,["modelValue","label","error"])]),s("div",O,[l(u,{type:"text",modelValue:i.gatePassNumberSuffix,"onUpdate:modelValue":e[16]||(e[16]=r=>i.gatePassNumberSuffix=r),name:"gatePassNumberSuffix",label:o(n)("reception.gate_pass.config.props.number_suffix"),error:o(t).gatePassNumberSuffix,"onUpdate:error":e[17]||(e[17]=r=>o(t).gatePassNumberSuffix=r)},null,8,["modelValue","label","error"])]),s("div",$,[l(u,{type:"text",modelValue:i.complaintNumberPrefix,"onUpdate:modelValue":e[18]||(e[18]=r=>i.complaintNumberPrefix=r),name:"complaintNumberPrefix",label:o(n)("reception.complaint.config.props.number_prefix"),error:o(t).complaintNumberPrefix,"onUpdate:error":e[19]||(e[19]=r=>o(t).complaintNumberPrefix=r)},null,8,["modelValue","label","error"])]),s("div",z,[l(u,{type:"number",modelValue:i.complaintNumberDigit,"onUpdate:modelValue":e[20]||(e[20]=r=>i.complaintNumberDigit=r),name:"complaintNumberDigit",label:o(n)("reception.complaint.config.props.number_digit"),error:o(t).complaintNumberDigit,"onUpdate:error":e[21]||(e[21]=r=>o(t).complaintNumberDigit=r)},null,8,["modelValue","label","error"])]),s("div",G,[l(u,{type:"text",modelValue:i.complaintNumberSuffix,"onUpdate:modelValue":e[22]||(e[22]=r=>i.complaintNumberSuffix=r),name:"complaintNumberSuffix",label:o(n)("reception.complaint.config.props.number_suffix"),error:o(t).complaintNumberSuffix,"onUpdate:error":e[23]||(e[23]=r=>o(t).complaintNumberSuffix=r)},null,8,["modelValue","label","error"])]),s("div",J,[l(u,{type:"text",modelValue:i.queryNumberPrefix,"onUpdate:modelValue":e[24]||(e[24]=r=>i.queryNumberPrefix=r),name:"queryNumberPrefix",label:o(n)("reception.query.config.props.number_prefix"),error:o(t).queryNumberPrefix,"onUpdate:error":e[25]||(e[25]=r=>o(t).queryNumberPrefix=r)},null,8,["modelValue","label","error"])]),s("div",K,[l(u,{type:"number",modelValue:i.queryNumberDigit,"onUpdate:modelValue":e[26]||(e[26]=r=>i.queryNumberDigit=r),name:"queryNumberDigit",label:o(n)("reception.query.config.props.number_digit"),error:o(t).queryNumberDigit,"onUpdate:error":e[27]||(e[27]=r=>o(t).queryNumberDigit=r)},null,8,["modelValue","label","error"])]),s("div",M,[l(u,{type:"text",modelValue:i.queryNumberSuffix,"onUpdate:modelValue":e[28]||(e[28]=r=>i.queryNumberSuffix=r),name:"queryNumberSuffix",label:o(n)("reception.query.config.props.number_suffix"),error:o(t).queryNumberSuffix,"onUpdate:error":e[29]||(e[29]=r=>o(t).queryNumberSuffix=r)},null,8,["modelValue","label","error"])]),s("div",Q,[l(c,{size:"xs",design:"info"},{default:m(()=>[B(R(x.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{Z as default};
