import{H as y,l as _,r as m,q as B,o as b,w as c,d,e as i,f as s,J as E,u as F,a as P,F as T}from"./app-BAwPsakn.js";const U={class:"grid grid-cols-3 gap-6"},N={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3"},H={name:"ExamTermForm"},O=Object.assign(H,{setup(f){const l={name:"",division:"",displayName:"",description:""},r="exam/term/",t=y(r),p=_({divisions:[]}),a=_({...l}),u=n=>{Object.assign(p,n)},g=n=>{var e;Object.assign(l,{...n,division:((e=n.division)==null?void 0:e.uuid)||""}),Object.assign(a,E(l))};return(n,e)=>{const v=m("BaseInput"),V=m("BaseSelect"),x=m("BaseTextarea"),$=m("FormAction");return b(),B($,{"pre-requisites":!0,onSetPreRequisites:u,"init-url":r,"init-form":l,form:a,setForm:g,redirect:"ExamTerm"},{default:c(()=>[d("div",U,[d("div",N,[i(v,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=o=>a.name=o),name:"name",label:n.$trans("exam.term.props.name"),error:s(t).name,"onUpdate:error":e[1]||(e[1]=o=>s(t).name=o),autofocus:""},null,8,["modelValue","label","error"])]),d("div",j,[i(V,{modelValue:a.division,"onUpdate:modelValue":e[2]||(e[2]=o=>a.division=o),name:"division",label:n.$trans("academic.division.division"),"label-prop":"nameWithProgram","value-prop":"uuid",options:p.divisions,error:s(t).division,"onUpdate:error":e[3]||(e[3]=o=>s(t).division=o)},null,8,["modelValue","label","options","error"])]),d("div",q,[i(v,{type:"text",modelValue:a.displayName,"onUpdate:modelValue":e[4]||(e[4]=o=>a.displayName=o),name:"displayName",label:n.$trans("exam.term.props.display_name"),error:s(t).displayName,"onUpdate:error":e[5]||(e[5]=o=>s(t).displayName=o)},null,8,["modelValue","label","error"])]),d("div",A,[i(x,{modelValue:a.description,"onUpdate:modelValue":e[6]||(e[6]=o=>a.description=o),name:"description",label:n.$trans("exam.term.props.description"),error:s(t).description,"onUpdate:error":e[7]||(e[7]=o=>s(t).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),R={name:"ExamTermAction"},S=Object.assign(R,{setup(f){const l=F();return(r,t)=>{const p=m("PageHeaderAction"),a=m("PageHeader"),u=m("ParentTransition");return b(),P(T,null,[i(a,{title:r.$trans(s(l).meta.trans,{attribute:r.$trans(s(l).meta.label)}),navs:[{label:r.$trans("exam.exam"),path:"Exam"},{label:r.$trans("exam.term.term"),path:"ExamTermList"}]},{default:c(()=>[i(p,{name:"ExamTerm",title:r.$trans("exam.term.term"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(u,{appear:"",visibility:!0},{default:c(()=>[i(O)]),_:1})],64)}}});export{S as default};
