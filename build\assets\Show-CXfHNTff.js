import{u as F,H as O,l as A,r as p,q as f,o as c,w as e,a as k,b as v,d as b,e as n,f as o,s as m,t as r,i as G,h as J,j as K,F as U,v as w,y as Q}from"./app-BAwPsakn.js";const W={key:0,class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3"},Z={class:"ml-1 text-sm"},P={class:"ml-1 text-sm"},ee={name:"TransportRouteStudentForm"},te=Object.assign(ee,{props:{transportRoute:{type:Object,default(){return{}}}},emits:["refresh"],setup(T,{emit:y}){F();const V=y,i=T,h={students:[]},S="transport/route/",_=O(S),t=A({...h}),g=()=>{V("refresh")},B=()=>i.transportRoute.stoppages.map(u=>({name:u.stoppage.name,uuid:u.stoppage.uuid}));return(u,a)=>{const C=p("BaseSelect"),N=p("BaseSelectSearch"),$=p("FormAction");return c(),f($,{"no-card":"","no-data-fetch":"",action:"addStudent","keep-adding":!1,"init-url":S,"init-form":h,form:t,"after-submit":g},{default:e(()=>[T.transportRoute.uuid?(c(),k("div",W,[b("div",X,[n(C,{modelValue:t.stoppage,"onUpdate:modelValue":a[0]||(a[0]=s=>t.stoppage=s),name:"stoppage",label:u.$trans("transport.stoppage.stoppage"),options:B(),"label-prop":"name","value-prop":"uuid",error:o(_).stoppage,"onUpdate:error":a[1]||(a[1]=s=>o(_).stoppage=s)},null,8,["modelValue","label","options","error"])]),b("div",Y,[n(N,{multiple:"",name:"students",label:u.$trans("student.student"),placeholder:u.$trans("global.select",{attribute:u.$trans("student.student")}),modelValue:t.students,"onUpdate:modelValue":a[2]||(a[2]=s=>t.students=s),error:o(_).students,"onUpdate:error":a[3]||(a[3]=s=>o(_).students=s),"value-prop":"uuid","search-key":"name","search-action":"student/summary"},{selectedOption:e(s=>[m(r(s.value.name)+" ("+r(s.value.courseName+" "+s.value.batchName)+") ",1),b("span",Z,r(s.value.codeNumber),1)]),listOption:e(s=>[m(r(s.option.name)+" ("+r(s.option.courseName+" "+s.option.batchName)+") ",1),b("span",P,r(s.option.codeNumber),1)]),_:1},8,["label","placeholder","modelValue","error"])])])):v("",!0)]),_:1},8,["form"])}}}),ae={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3"},le={name:"TransportRouteEmployeeForm"},ie=Object.assign(le,{props:{transportRoute:{type:Object,default(){return{}}}},emits:["refresh"],setup(T,{emit:y}){F();const V=y,i=T,h={stoppage:"",title:"",publishContactNumber:!1,employees:[]},S="transport/route/",_=O(S),t=A({...h}),g=()=>{V("refresh")},B=()=>i.transportRoute.stoppages.map(u=>({name:u.stoppage.name,uuid:u.stoppage.uuid}));return(u,a)=>{const C=p("BaseSelect"),N=p("BaseInput"),$=p("BaseSwitch"),s=p("BaseSelectSearch"),I=p("FormAction");return c(),f(I,{"no-card":"","no-data-fetch":"",action:"addEmployee","keep-adding":!1,"init-url":S,"init-form":h,form:t,"after-submit":g},{default:e(()=>[b("div",ae,[b("div",oe,[n(C,{modelValue:t.stoppage,"onUpdate:modelValue":a[0]||(a[0]=l=>t.stoppage=l),name:"stoppage",label:u.$trans("transport.stoppage.stoppage"),options:B(),"label-prop":"name","value-prop":"uuid",error:o(_).stoppage,"onUpdate:error":a[1]||(a[1]=l=>o(_).stoppage=l)},null,8,["modelValue","label","options","error"])]),b("div",ne,[n(N,{type:"text",modelValue:t.title,"onUpdate:modelValue":a[2]||(a[2]=l=>t.title=l),name:"title",label:u.$trans("transport.route.props.title"),error:o(_).title,"onUpdate:error":a[3]||(a[3]=l=>o(_).title=l)},null,8,["modelValue","label","error"])]),b("div",re,[n($,{vertical:"",modelValue:t.publishContactNumber,"onUpdate:modelValue":a[4]||(a[4]=l=>t.publishContactNumber=l),name:"publishContactNumber",label:u.$trans("global.publish",{attribute:u.$trans("contact.props.contact_number")}),error:o(_).publishContactNumber,"onUpdate:error":a[5]||(a[5]=l=>o(_).publishContactNumber=l)},null,8,["modelValue","label","error"])]),b("div",se,[n(s,{multiple:"",name:"employees",label:u.$trans("employee.employee"),placeholder:u.$trans("global.select",{attribute:u.$trans("employee.employee")}),modelValue:t.employees,"onUpdate:modelValue":a[6]||(a[6]=l=>t.employees=l),error:o(_).employees,"onUpdate:error":a[7]||(a[7]=l=>o(_).employees=l),"value-prop":"uuid","search-key":"name","search-action":"employee/summary","additional-search-query":{type:"all"}},{selectedOption:e(l=>[m(r(l.value.name)+" ("+r(l.value.designation)+") ",1)]),listOption:e(l=>[m(r(l.option.name)+" ("+r(l.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error"])])])]),_:1},8,["form"])}}}),ue={class:"space-y-2"},pe={class:"text-xs"},de=["innerHTML"],me={class:"ml-1"},ce={class:"ml-1"},be={class:"space-y-4"},_e={key:1,class:"px-4 py-2"},fe={name:"TransportRouteShow"},ye=Object.assign(fe,{setup(T){G();const y=F(),V=J(),i=K("$trans"),h={},S="transport/route/",_=[{key:"stoppage",label:i("transport.stoppage.stoppage"),visibility:!0},{key:"name",label:i("contact.props.name"),visibility:!0},{key:"detail",label:i("general.detail"),visibility:!0},{key:"action",label:"",visibility:!0}],t=A({...h}),g=A({activeTab:"student"}),B=u=>{Object.assign(t,u)};return(u,a)=>{const C=p("PageHeaderAction"),N=p("PageHeader"),$=p("TextMuted"),s=p("ListItemView"),I=p("ListContainerVertical"),l=p("BaseCard"),L=p("TabItem"),j=p("TabGroup"),R=p("DataCell"),H=p("BaseButton"),D=p("DataRow"),E=p("SimpleTable"),x=p("BaseAlert"),M=p("DetailLayoutVertical"),q=p("ShowItem"),z=p("ParentTransition");return c(),k(U,null,[n(N,{title:o(i)(o(y).meta.trans,{attribute:o(i)(o(y).meta.label)}),navs:[{label:o(i)("transport.transport"),path:"Transport"},{label:o(i)("transport.route.route"),path:"TransportRouteList"}]},{default:e(()=>[n(C,{name:"TransportRoute",title:o(i)("transport.route.route"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(z,{appear:"",visibility:!0},{default:e(()=>[n(q,{"init-url":S,uuid:o(y).params.uuid,onSetItem:B,onRedirectTo:a[4]||(a[4]=d=>o(V).push({name:"TransportRoute"}))},{default:e(()=>[t.uuid?(c(),f(M,{key:0},{detail:e(()=>[b("div",ue,[n(l,{"no-padding":"","no-content-padding":""},{title:e(()=>[m(r(t.name),1)]),action:e(()=>a[5]||(a[5]=[])),default:e(()=>[n(I,null,{default:e(()=>[n(s,{label:o(i)("transport.vehicle.vehicle")},{default:e(()=>[m(r(t.vehicle.name)+" ",1),n($,{block:""},{default:e(()=>{var d;return[m(r(((d=t.vehicle)==null?void 0:d.registrationNumber)||"-"),1)]}),_:1})]),_:1},8,["label"]),n(s,{label:o(i)("transport.route.props.max_capacity")},{default:e(()=>[b("span",pe,r(t.passengers.length),1),m("/"+r(t.maxCapacity),1)]),_:1},8,["label"]),n(s,{class:"col-span-1 sm:col-span-2",label:o(i)("transport.route.props.description")},{default:e(()=>[b("div",{innerHTML:t.description},null,8,de)]),_:1},8,["label"]),n(s,{label:o(i)("general.created_at")},{default:e(()=>[m(r(t.createdAt.formatted),1)]),_:1},8,["label"]),n(s,{label:o(i)("general.updated_at")},{default:e(()=>[m(r(t.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1}),t.direction.value=="arrival"||t.direction.value=="roundtrip"?(c(),f(l,{key:0,"no-padding":"","no-content-padding":""},{title:e(()=>[m(r(o(i)("transport.route.arrival_stoppages"))+" ",1),n($,{block:""},{default:e(()=>[m(r(t.arrivalStartsAt.formatted),1)]),_:1})]),default:e(()=>[(c(!0),k(U,null,w(t.arrivalStoppages,d=>(c(),f(s,{label:d.name},{default:e(()=>[b("span",me,r(d.arrivalTime.formatted),1)]),_:2},1032,["label"]))),256))]),_:1})):v("",!0),t.direction.value=="departure"||t.direction.value=="roundtrip"?(c(),f(l,{key:1,"no-padding":"","no-content-padding":""},{title:e(()=>[m(r(o(i)("transport.route.departure_stoppages"))+" ",1),n($,{block:""},{default:e(()=>[m(r(t.departureStartsAt.formatted),1)]),_:1})]),default:e(()=>[(c(!0),k(U,null,w(t.departureStoppages,d=>(c(),f(s,{label:d.name},{default:e(()=>[b("span",ce,r(d.arrivalTime.formatted),1)]),_:2},1032,["label"]))),256))]),_:1})):v("",!0)])]),default:e(()=>[b("div",be,[n(l,null,{title:e(()=>[n(j,null,{default:e(()=>[n(L,{first:"",label:o(i)("global.add",{attribute:o(i)("student.student")}),onClick:a[0]||(a[0]=d=>g.activeTab="student"),"is-active":g.activeTab=="student"},null,8,["label","is-active"]),n(L,{last:"",label:o(i)("global.add",{attribute:o(i)("employee.employee")}),onClick:a[1]||(a[1]=d=>g.activeTab="employee"),"is-active":g.activeTab=="employee"},null,8,["label","is-active"])]),_:1})]),default:e(()=>[g.activeTab=="student"?(c(),f(te,{key:0,uuid:o(y).params.uuid,"transport-route":t,onRefresh:a[2]||(a[2]=d=>u.emitter.emit("refreshItem"))},null,8,["uuid","transport-route"])):v("",!0),g.activeTab=="employee"?(c(),f(ie,{key:1,uuid:o(y).params.uuid,"transport-route":t,onRefresh:a[3]||(a[3]=d=>u.emitter.emit("refreshItem"))},null,8,["uuid","transport-route"])):v("",!0)]),_:1}),n(l,{"no-padding":"","no-content-padding":""},{title:e(()=>[m(r(o(i)("transport.route.passengers")),1)]),footer:e(()=>a[7]||(a[7]=[])),default:e(()=>[t.passengers.length>0?(c(),f(E,{key:0,header:_},{default:e(()=>[(c(!0),k(U,null,w(t.passengers,d=>(c(),f(D,{key:d.uuid},{default:e(()=>[n(R,{name:"stoppage"},{default:e(()=>[m(r(d.stoppage||"-"),1)]),_:2},1024),n(R,{name:"name"},{default:e(()=>[m(r(d.name)+" ",1),n($,null,{default:e(()=>[m("("+r(d.type.label)+")",1)]),_:2},1024)]),_:2},1024),n(R,{name:"detail"},{default:e(()=>[m(r(d.detail)+" ",1),d.contactNumber?(c(),f($,{key:0,block:""},{default:e(()=>[m(r(d.contactNumber),1)]),_:2},1024)):v("",!0)]),_:2},1024),n(R,{name:"action"},{default:e(()=>[o(Q)("transport-route:create")?(c(),f(H,{key:0,size:"xs",design:"danger",onClick:ge=>u.emitter.emit("showActionItem",{uuid:t==null?void 0:t.uuid,moduleUuid:d.uuid,action:"removePassenger",confirmation:!0})},{default:e(()=>a[6]||(a[6]=[b("i",{class:"fas fa-trash"},null,-1)])),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})):v("",!0),t.passengers.length===0?(c(),k("div",_e,[n(x,{design:"info",size:"xs"},{default:e(()=>[m(r(o(i)("general.errors.record_not_found")),1)]),_:1})])):v("",!0)]),_:1})])]),_:1})):v("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{ye as default};
