import{u as k,j as w,H as L,c as Q,l as v,r as d,a as m,o as u,e as r,f as l,w as c,d as i,b as g,s as q,t as f,F as I}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},R={class:"mt-4 grid grid-cols-3 gap-4"},B={class:"col-span-3 sm:col-span-1"},F={key:0,class:"col-span-3 sm:col-span-1"},j={key:1,class:"col-span-3 sm:col-span-1"},H={class:"mt-4 grid grid-cols-3 gap-4"},N={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},z={class:"flex items-center gap-2"},J={key:0,class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={name:"EmployeeAttendanceConfigGeneral"},h=Object.assign(X,{setup(Y){const C=k(),n=w("$trans"),y="config/",t=L(y);Q(()=>n("global.placeholder_info",{attribute:_.datePlaceholders}));const _=v({datePlaceholders:""}),b={allowEmployeeClockInOut:!1,allowEmployeeClockInOutViaDevice:!1,enableQrCodeAttendance:!1,useDynamicQrCode:!1,qrCodeExpiryDuration:"",lateGracePeriod:"",earlyLeavingGracePeriod:"",presentGracePeriod:"",enableGeolocationTimesheet:!1,geolocationLatitude:"",geolocationLongitude:"",geolocationRadius:"",type:"employee"},a=v({...b}),P=V=>{Object.assign(_,{datePlaceholders:V.datePlaceholders.map(e=>e.value).join(", ")})};return(V,e)=>{const G=d("PageHeader"),p=d("BaseSwitch"),E=d("HelperText"),s=d("BaseInput"),U=d("BaseFieldset"),x=d("FormAction"),D=d("ParentTransition");return u(),m(I,null,[r(G,{title:l(n)(l(C).meta.label),navs:[{label:l(n)("employee.employee"),path:"Employee"},{label:l(n)("employee.attendance.attendance"),path:"EmployeeAttendance"}]},null,8,["title","navs"]),r(D,{appear:"",visibility:!0},{default:c(()=>[r(x,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:P,"init-url":y,"data-fetch":"employee",action:"store","init-form":b,form:a,"stay-on":"",redirect:"EmployeeAttendance"},{default:c(()=>[i("div",O,[i("div",A,[r(p,{vertical:"",modelValue:a.allowEmployeeClockInOut,"onUpdate:modelValue":e[0]||(e[0]=o=>a.allowEmployeeClockInOut=o),name:"allowEmployeeClockInOut",label:l(n)("employee.attendance.config.props.allow_employee_clock_in_out"),error:l(t).allowEmployeeClockInOut,"onUpdate:error":e[1]||(e[1]=o=>l(t).allowEmployeeClockInOut=o)},null,8,["modelValue","label","error"])]),i("div",T,[r(p,{vertical:"",modelValue:a.allowEmployeeClockInOutViaDevice,"onUpdate:modelValue":e[2]||(e[2]=o=>a.allowEmployeeClockInOutViaDevice=o),name:"allowEmployeeClockInOutViaDevice",label:l(n)("employee.attendance.config.props.allow_employee_clock_in_out_via_device"),error:l(t).allowEmployeeClockInOutViaDevice,"onUpdate:error":e[3]||(e[3]=o=>l(t).allowEmployeeClockInOutViaDevice=o)},null,8,["modelValue","label","error"])])]),i("div",R,[i("div",B,[r(p,{vertical:"",modelValue:a.enableQrCodeAttendance,"onUpdate:modelValue":e[4]||(e[4]=o=>a.enableQrCodeAttendance=o),name:"enableQrCodeAttendance",label:l(n)("employee.attendance.config.props.enable_qr_code_attendance"),error:l(t).enableQrCodeAttendance,"onUpdate:error":e[5]||(e[5]=o=>l(t).enableQrCodeAttendance=o)},null,8,["modelValue","label","error"])]),a.enableQrCodeAttendance?(u(),m("div",F,[r(p,{vertical:"",modelValue:a.useDynamicQrCode,"onUpdate:modelValue":e[6]||(e[6]=o=>a.useDynamicQrCode=o),name:"useDynamicQrCode",label:l(n)("employee.attendance.config.props.use_dynamic_qr_code"),error:l(t).useDynamicQrCode,"onUpdate:error":e[7]||(e[7]=o=>l(t).useDynamicQrCode=o)},null,8,["modelValue","label","error"]),r(E,null,{default:c(()=>[q(f(l(n)("employee.attendance.config.props.dynamic_qr_code_tip")),1)]),_:1})])):g("",!0),a.enableQrCodeAttendance&&a.useDynamicQrCode?(u(),m("div",j,[r(s,{type:"text",modelValue:a.qrCodeExpiryDuration,"onUpdate:modelValue":e[8]||(e[8]=o=>a.qrCodeExpiryDuration=o),name:"qrCodeExpiryDuration",label:l(n)("employee.attendance.config.props.qr_code_expiry_duration"),error:l(t).qrCodeExpiryDuration,"onUpdate:error":e[9]||(e[9]=o=>l(t).qrCodeExpiryDuration=o)},null,8,["modelValue","label","error"])])):g("",!0)]),i("div",H,[i("div",N,[r(s,{type:"text",modelValue:a.lateGracePeriod,"onUpdate:modelValue":e[10]||(e[10]=o=>a.lateGracePeriod=o),name:"lateGracePeriod",label:l(n)("employee.attendance.config.props.late_grace_period"),"label-hint":l(n)("employee.attendance.config.props.late_grace_period_tip"),error:l(t).lateGracePeriod,"onUpdate:error":e[11]||(e[11]=o=>l(t).lateGracePeriod=o)},null,8,["modelValue","label","label-hint","error"])]),i("div",S,[r(s,{type:"text",modelValue:a.earlyLeavingGracePeriod,"onUpdate:modelValue":e[12]||(e[12]=o=>a.earlyLeavingGracePeriod=o),name:"earlyLeavingGracePeriod",label:l(n)("employee.attendance.config.props.early_leaving_grace_period"),"label-hint":l(n)("employee.attendance.config.props.early_leaving_grace_period_tip"),error:l(t).earlyLeavingGracePeriod,"onUpdate:error":e[13]||(e[13]=o=>l(t).earlyLeavingGracePeriod=o)},null,8,["modelValue","label","label-hint","error"])]),i("div",$,[r(s,{type:"text",modelValue:a.presentGracePeriod,"onUpdate:modelValue":e[14]||(e[14]=o=>a.presentGracePeriod=o),name:"presentGracePeriod",label:l(n)("employee.attendance.config.props.present_grace_period"),"label-hint":l(n)("employee.attendance.config.props.present_grace_period_tip"),error:l(t).presentGracePeriod,"onUpdate:error":e[15]||(e[15]=o=>l(t).presentGracePeriod=o)},null,8,["modelValue","label","label-hint","error"])])]),r(U,{class:"mt-4"},{legend:c(()=>[i("div",z,[i("span",null,f(l(n)("employee.attendance.config.props.enable_geolocation_timesheet")),1),r(p,{modelValue:a.enableGeolocationTimesheet,"onUpdate:modelValue":e[16]||(e[16]=o=>a.enableGeolocationTimesheet=o),name:"enableGeolocationTimesheet",error:l(t).enableGeolocationTimesheet,"onUpdate:error":e[17]||(e[17]=o=>l(t).enableGeolocationTimesheet=o)},null,8,["modelValue","error"])])]),default:c(()=>[a.enableGeolocationTimesheet?(u(),m("div",J,[i("div",K,[r(s,{type:"text",modelValue:a.geolocationLatitude,"onUpdate:modelValue":e[18]||(e[18]=o=>a.geolocationLatitude=o),name:"geolocationLatitude",label:l(n)("employee.attendance.config.props.geolocation_latitude"),error:l(t).geolocationLatitude,"onUpdate:error":e[19]||(e[19]=o=>l(t).geolocationLatitude=o)},null,8,["modelValue","label","error"])]),i("div",M,[r(s,{type:"text",modelValue:a.geolocationLongitude,"onUpdate:modelValue":e[20]||(e[20]=o=>a.geolocationLongitude=o),name:"geolocationLongitude",label:l(n)("employee.attendance.config.props.geolocation_longitude"),error:l(t).geolocationLongitude,"onUpdate:error":e[21]||(e[21]=o=>l(t).geolocationLongitude=o)},null,8,["modelValue","label","error"])]),i("div",W,[r(s,{type:"text",modelValue:a.geolocationRadius,"onUpdate:modelValue":e[22]||(e[22]=o=>a.geolocationRadius=o),name:"geolocationRadius","trailing-text":l(n)("list.distances.mtr"),label:l(n)("employee.attendance.config.props.geolocation_radius"),error:l(t).geolocationRadius,"onUpdate:error":e[23]||(e[23]=o=>l(t).geolocationRadius=o)},null,8,["modelValue","trailing-text","label","error"])])])):g("",!0)]),_:1})]),_:1},8,["form"])]),_:1})],64)}}});export{h as default};
