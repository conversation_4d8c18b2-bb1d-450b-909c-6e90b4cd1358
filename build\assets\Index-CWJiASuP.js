import{u as q,l as I,n as M,r as s,q as v,o as _,w as e,d as k,b as w,s as l,t as n,e as t,h as N,j as H,m as O,f as g,a as R,F as U,v as j}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(F,{emit:f}){const u=q(),D=f,b={programs:[],employees:[],startDate:"",endDate:""},c=I({...b}),i=I({programs:[],employees:[],isLoaded:!(u.query.programs||u.query.employees)});return M(async()=>{i.programs=u.query.programs?u.query.programs.split(","):[],i.employees=u.query.employees?u.query.employees.split(","):[],i.isLoaded=!0}),(p,m)=>{const r=s("BaseSelectSearch"),d=s("DatePicker"),C=s("FilterForm");return _(),v(C,{"init-form":b,form:c,multiple:["programs","employees"],onHide:m[4]||(m[4]=a=>D("hide"))},{default:e(()=>[k("div",E,[k("div",z,[i.isLoaded?(_(),v(r,{key:0,multiple:"",name:"programs",label:p.$trans("global.select",{attribute:p.$trans("academic.program.program")}),modelValue:c.programs,"onUpdate:modelValue":m[0]||(m[0]=a=>c.programs=a),"value-prop":"uuid","init-search":i.programs,"search-key":"course_program","search-action":"academic/program/list"},{selectedOption:e(a=>[l(n(a.value.name),1)]),listOption:e(a=>[l(n(a.option.name),1)]),_:1},8,["label","modelValue","init-search"])):w("",!0)]),k("div",G,[i.isLoaded?(_(),v(r,{key:0,multiple:"",name:"employees",label:p.$trans("global.select",{attribute:p.$trans("employee.employee")}),modelValue:c.employees,"onUpdate:modelValue":m[1]||(m[1]=a=>c.employees=a),"value-prop":"uuid","init-search":i.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(a=>[l(n(a.value.name)+" ("+n(a.value.codeNumber)+") ",1)]),listOption:e(a=>[l(n(a.option.name)+" ("+n(a.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):w("",!0)]),k("div",J,[t(d,{start:c.startDate,"onUpdate:start":m[2]||(m[2]=a=>c.startDate=a),end:c.endDate,"onUpdate:end":m[3]||(m[3]=a=>c.endDate=a),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"AcademicProgramInchargeList"},X=Object.assign(Q,{setup(F){const f=N(),u=H("emitter");let D=["create","filter"],b=["print","pdf","excel"];const c="academic/programIncharge/",i=O(!1),p=I({}),m=r=>{Object.assign(p,r)};return(r,d)=>{const C=s("PageHeaderAction"),a=s("PageHeader"),A=s("ParentTransition"),y=s("DataCell"),P=s("TextMuted"),h=s("FloatingMenuItem"),B=s("FloatingMenu"),V=s("DataRow"),L=s("BaseButton"),S=s("DataTable"),T=s("ListItem");return _(),v(T,{"init-url":c,onSetItems:m},{header:e(()=>[t(a,{title:r.$trans("academic.program_incharge.program_incharge"),navs:[{label:r.$trans("academic.academic"),path:"Academic"},{label:r.$trans("academic.program.program"),path:"AcademicProgram"}]},{default:e(()=>[t(C,{url:"academic/program-incharges/",name:"AcademicProgramIncharge",title:r.$trans("academic.program_incharge.program_incharge"),actions:g(D),"dropdown-actions":g(b),onToggleFilter:d[0]||(d[0]=o=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(A,{appear:"",visibility:i.value},{default:e(()=>[t(K,{onRefresh:d[1]||(d[1]=o=>g(u).emit("listItems")),onHide:d[2]||(d[2]=o=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(A,{appear:"",visibility:!0},{default:e(()=>[t(S,{header:p.headers,meta:p.meta,module:"academic.program_incharge",onRefresh:d[4]||(d[4]=o=>g(u).emit("listItems"))},{actionButton:e(()=>[t(L,{onClick:d[3]||(d[3]=o=>g(f).push({name:"AcademicProgramInchargeCreate"}))},{default:e(()=>[l(n(r.$trans("global.add",{attribute:r.$trans("academic.program_incharge.program_incharge")})),1)]),_:1})]),default:e(()=>[(_(!0),R(U,null,j(p.data,o=>(_(),v(V,{key:o.uuid,onDoubleClick:$=>g(f).push({name:"AcademicProgramInchargeShow",params:{uuid:o.uuid}})},{default:e(()=>[t(y,{name:"program"},{default:e(()=>[l(n(o.program.name),1)]),_:2},1024),t(y,{name:"employee"},{default:e(()=>[l(n(o.employee.name)+" ",1),t(P,{block:""},{default:e(()=>[l(n(o.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),t(y,{name:"period"},{default:e(()=>[l(n(o.period),1)]),_:2},1024),t(y,{name:"createdAt"},{default:e(()=>[l(n(o.createdAt.formatted),1)]),_:2},1024),t(y,{name:"action"},{default:e(()=>[t(B,null,{default:e(()=>[t(h,{icon:"fas fa-arrow-circle-right",onClick:$=>g(f).push({name:"AcademicProgramInchargeShow",params:{uuid:o.uuid}})},{default:e(()=>[l(n(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-edit",onClick:$=>g(f).push({name:"AcademicProgramInchargeEdit",params:{uuid:o.uuid}})},{default:e(()=>[l(n(r.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-copy",onClick:$=>g(f).push({name:"AcademicProgramInchargeDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[l(n(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-trash",onClick:$=>g(u).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[l(n(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
