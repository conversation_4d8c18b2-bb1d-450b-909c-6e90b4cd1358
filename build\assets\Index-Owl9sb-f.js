import{l as B,r as o,q as _,o as y,w as e,d as C,e as a,u as R,h as T,j,y as D,m as A,f as i,a as L,F as M,v as N,s as m,t as u,b as F}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",emits:["hide"],setup(s,{emit:v}){const d=v,p={name:""},c=B({...p});return($,r)=>{const f=o("BaseInput"),b=o("FilterForm");return y(),_(b,{"init-form":p,form:c,onHide:r[1]||(r[1]=n=>d("hide"))},{default:e(()=>[C("div",O,[C("div",U,[a(f,{type:"text",modelValue:c.name,"onUpdate:modelValue":r[0]||(r[0]=n=>c.name=n),name:"name",label:$.$trans("employee.attendance.work_shift.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"EmployeeWorkShiftList"},J=Object.assign(z,{props:{employee:{type:Object,default(){return{}}}},setup(s){const v=R(),d=T(),p=j("emitter");let c=["filter"];D("work-shift:assign")&&c.unshift("create");const $="employee/workShift/",r=A(!1),f=B({}),b=n=>{Object.assign(f,n)};return(n,l)=>{const E=o("PageHeaderAction"),I=o("PageHeader"),w=o("ParentTransition"),k=o("DataCell"),h=o("FloatingMenuItem"),S=o("FloatingMenu"),V=o("DataRow"),W=o("BaseButton"),H=o("DataTable"),P=o("ListItem");return y(),_(P,{"init-url":$,uuid:i(v).params.uuid,onSetItems:b},{header:e(()=>[s.employee.uuid?(y(),_(I,{key:0,title:n.$trans("employee.attendance.work_shift.work_shift"),navs:[{label:n.$trans("employee.employee"),path:"Employee"},{label:s.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:s.employee.uuid}}}]},{default:e(()=>[a(E,{url:`employees/${s.employee.uuid}/work-shifts/`,name:"EmployeeWorkShift",title:n.$trans("employee.attendance.work_shift.work_shift"),actions:i(c),"dropdown-actions":["print","pdf","excel"],onToggleFilter:l[0]||(l[0]=t=>r.value=!r.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):F("",!0)]),filter:e(()=>[a(w,{appear:"",visibility:r.value},{default:e(()=>[a(q,{onRefresh:l[1]||(l[1]=t=>i(p).emit("listItems")),onHide:l[2]||(l[2]=t=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(w,{appear:"",visibility:!0},{default:e(()=>[a(H,{header:f.headers,meta:f.meta,module:"employee.attendance.work_shift",onRefresh:l[4]||(l[4]=t=>i(p).emit("listItems"))},{actionButton:e(()=>[i(D)("work-shift:assign")?(y(),_(W,{key:0,onClick:l[3]||(l[3]=t=>i(d).push({name:"EmployeeWorkShiftCreate"}))},{default:e(()=>[m(u(n.$trans("global.assign",{attribute:n.$trans("employee.attendance.work_shift.work_shift")})),1)]),_:1})):F("",!0)]),default:e(()=>[(y(!0),L(M,null,N(f.data,t=>(y(),_(V,{key:t.uuid,onDoubleClick:g=>i(d).push({name:"EmployeeWorkShiftShow",params:{uuid:s.employee.uuid,muuid:t.uuid}})},{default:e(()=>[a(k,{name:"workShift"},{default:e(()=>[m(u(t.workShift.name)+" ("+u(t.workShift.code)+") ",1)]),_:2},1024),a(k,{name:"startDate"},{default:e(()=>[m(u(t.startDate.formatted),1)]),_:2},1024),a(k,{name:"endDate"},{default:e(()=>[m(u(t.endDate.formatted),1)]),_:2},1024),a(k,{name:"createdAt"},{default:e(()=>[m(u(t.createdAt.formatted),1)]),_:2},1024),a(k,{name:"action"},{default:e(()=>[a(S,null,{default:e(()=>[a(h,{icon:"fas fa-arrow-circle-right",onClick:g=>i(d).push({name:"EmployeeWorkShiftShow",params:{uuid:s.employee.uuid,muuid:t.uuid}})},{default:e(()=>[m(u(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-edit",onClick:g=>i(d).push({name:"EmployeeWorkShiftEdit",params:{uuid:s.employee.uuid,muuid:t.uuid}})},{default:e(()=>[m(u(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-copy",onClick:g=>i(d).push({name:"EmployeeWorkShiftDuplicate",params:{uuid:s.employee.uuid,muuid:t.uuid}})},{default:e(()=>[m(u(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(h,{icon:"fas fa-trash",onClick:g=>i(p).emit("deleteItem",{uuid:s.employee.uuid,moduleUuid:t.uuid})},{default:e(()=>[m(u(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{J as default};
