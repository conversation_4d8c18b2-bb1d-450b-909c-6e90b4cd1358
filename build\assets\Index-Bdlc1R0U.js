import{H as $,l as U,r as u,q as F,o as S,w as b,e as l,d as t,a as z,b as C,f as n,s as c,t as m}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-4"},N={class:"col-span-3"},k={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3"},A={class:"grid grid-cols-3"},I={class:"col-span-3 sm:col-span-1"},L={class:"mt-6 grid grid-cols-1"},_={class:"sm:col-span-1"},G={class:"grid grid-cols-3"},J={class:"col-span-3 sm:col-span-1"},K={key:0,class:"mt-6 grid grid-cols-1"},Q={class:"sm:col-span-1"},X={name:"ConfigSystem"},h=Object.assign(X,{setup(Y){const f="config/",s=$(f),i=U({}),V={showSetupWizard:!1,dateFormat:"",timeFormat:"",locale:"",timezone:"",colorScheme:"",enableDarkTheme:!1,enableMiniSidebar:!1,enableStrongPassword:!1,perPage:"",currency:"",currencies:[],showVersionNumber:!1,footerCredit:"",enableMaintenanceMode:!1,maintenanceModeMessage:"",type:"system"},r=U({...V}),v=a=>{Object.assign(i,a)};return(a,o)=>{const g=u("CardHeader"),p=u("BaseSwitch"),d=u("BaseSelect"),y=u("BaseInput"),M=u("FormAction"),w=u("ConfigPage");return S(),F(w,null,{default:b(()=>[l(M,{"no-card":"","init-url":f,"pre-requisites":{data:["dateFormats","timeFormats","locales","timezones","perPageLengths","currencies"]},onSetPreRequisites:v,"data-fetch":"system","init-form":V,form:r,action:"store","stay-on":"",redirect:"Config"},{default:b(()=>[l(g,{first:"",title:a.$trans("config.system.system_config"),description:a.$trans("config.system.system_info")},null,8,["title","description"]),t("div",P,[t("div",N,[l(p,{vertical:"",modelValue:r.showSetupWizard,"onUpdate:modelValue":o[0]||(o[0]=e=>r.showSetupWizard=e),name:"showSetupWizard",label:a.$trans("global.show",{attribute:a.$trans("config.system.props.setup_wizard")}),error:n(s).showSetupWizard,"onUpdate:error":o[1]||(o[1]=e=>n(s).showSetupWizard=e)},null,8,["modelValue","label","error"])]),t("div",k,[l(d,{modelValue:r.locale,"onUpdate:modelValue":o[2]||(o[2]=e=>r.locale=e),name:"locale",label:a.$trans("config.system.props.locale"),"label-prop":"name","value-prop":"uuid",options:i.locales,error:n(s).locale,"onUpdate:error":o[3]||(o[3]=e=>n(s).locale=e)},null,8,["modelValue","label","options","error"])]),t("div",B,[l(d,{modelValue:r.timezone,"onUpdate:modelValue":o[4]||(o[4]=e=>r.timezone=e),name:"timezone",label:a.$trans("config.system.props.timezone"),options:i.timezones,error:n(s).timezone,"onUpdate:error":o[5]||(o[5]=e=>n(s).timezone=e)},null,8,["modelValue","label","options","error"])]),t("div",D,[l(d,{modelValue:r.dateFormat,"onUpdate:modelValue":o[6]||(o[6]=e=>r.dateFormat=e),name:"dateFormat",label:a.$trans("config.system.props.date_format"),options:i.dateFormats,error:n(s).dateFormat,"onUpdate:error":o[7]||(o[7]=e=>n(s).dateFormat=e)},null,8,["modelValue","label","options","error"])]),t("div",T,[l(d,{modelValue:r.timeFormat,"onUpdate:modelValue":o[8]||(o[8]=e=>r.timeFormat=e),name:"timeFormat",label:a.$trans("config.system.props.time_format"),options:i.timeFormats,error:n(s).timeFormat,"onUpdate:error":o[9]||(o[9]=e=>n(s).timeFormat=e)},null,8,["modelValue","label","options","error"])]),t("div",O,[l(d,{modelValue:r.colorScheme,"onUpdate:modelValue":o[10]||(o[10]=e=>r.colorScheme=e),name:"colorScheme",label:a.$trans("config.system.props.color_scheme"),options:i.colorSchemes,error:n(s).colorScheme,"onUpdate:error":o[11]||(o[11]=e=>n(s).colorScheme=e)},null,8,["modelValue","label","options","error"])]),t("div",W,[l(d,{modelValue:r.perPage,"onUpdate:modelValue":o[12]||(o[12]=e=>r.perPage=e),name:"perPage",label:a.$trans("config.system.props.per_page_length"),options:i.perPageLengths,error:n(s).perPage,"onUpdate:error":o[13]||(o[13]=e=>n(s).perPage=e)},null,8,["modelValue","label","options","error"])]),t("div",q,[l(d,{multiple:"",modelValue:r.currencies,"onUpdate:modelValue":o[14]||(o[14]=e=>r.currencies=e),name:"currencies",label:a.$trans("config.system.props.allowed_currency"),options:i.currencies,error:n(s).currencies,"onUpdate:error":o[15]||(o[15]=e=>n(s).currencies=e)},{selectedOption:b(e=>[c(m(e.value.description)+" ("+m(e.value.symbol)+") ",1)]),listOption:b(e=>[c(m(e.option.description)+" ("+m(e.option.symbol)+") ",1)]),_:1},8,["modelValue","label","options","error"])]),t("div",E,[l(d,{modelValue:r.currency,"onUpdate:modelValue":o[16]||(o[16]=e=>r.currency=e),name:"currency",label:a.$trans("config.system.props.default_currency"),options:i.currencies,error:n(s).currency,"onUpdate:error":o[17]||(o[17]=e=>n(s).currency=e)},{selectedOption:b(e=>[c(m(e.value.description)+" ("+m(e.value.symbol)+") ",1)]),listOption:b(e=>[c(m(e.option.description)+" ("+m(e.option.symbol)+") ",1)]),_:1},8,["modelValue","label","options","error"])]),t("div",H,[l(p,{modelValue:r.enableDarkTheme,"onUpdate:modelValue":o[18]||(o[18]=e=>r.enableDarkTheme=e),name:"enableDarkTheme",label:a.$trans("global.enable",{attribute:a.$trans("config.system.props.dark_theme")}),error:n(s).enableDarkTheme,"onUpdate:error":o[19]||(o[19]=e=>n(s).enableDarkTheme=e)},null,8,["modelValue","label","error"])]),t("div",R,[l(p,{modelValue:r.enableMiniSidebar,"onUpdate:modelValue":o[20]||(o[20]=e=>r.enableMiniSidebar=e),name:"enableMiniSidebar",label:a.$trans("global.enable",{attribute:a.$trans("config.system.props.mini_sidebar")}),error:n(s).enableMiniSidebar,"onUpdate:error":o[21]||(o[21]=e=>n(s).enableMiniSidebar=e)},null,8,["modelValue","label","error"])]),t("div",j,[l(p,{vertical:"",modelValue:r.enableStrongPassword,"onUpdate:modelValue":o[22]||(o[22]=e=>r.enableStrongPassword=e),name:"enableStrongPassword",label:a.$trans("global.enable",{attribute:a.$trans("config.system.props.strong_password")}),error:n(s).enableStrongPassword,"onUpdate:error":o[23]||(o[23]=e=>n(s).enableStrongPassword=e)},null,8,["modelValue","label","error"])])]),l(g,{title:a.$trans("config.system.credit"),description:a.$trans("config.system.credit_info")},null,8,["title","description"]),t("div",A,[t("div",I,[l(p,{modelValue:r.showVersionNumber,"onUpdate:modelValue":o[24]||(o[24]=e=>r.showVersionNumber=e),name:"showVersionNumber",label:a.$trans("config.system.props.show_version_number"),error:n(s).showVersionNumber,"onUpdate:error":o[25]||(o[25]=e=>n(s).showVersionNumber=e)},null,8,["modelValue","label","error"])])]),t("div",L,[t("div",_,[l(y,{type:"text",modelValue:r.footerCredit,"onUpdate:modelValue":o[26]||(o[26]=e=>r.footerCredit=e),name:"appName",label:a.$trans("config.system.props.footer_credit"),error:n(s).footerCredit,"onUpdate:error":o[27]||(o[27]=e=>n(s).footerCredit=e)},null,8,["modelValue","label","error"])])]),l(g,{title:a.$trans("config.system.maintenance_mode"),description:a.$trans("config.system.maintenance_mode_info")},null,8,["title","description"]),t("div",G,[t("div",J,[l(p,{modelValue:r.enableMaintenanceMode,"onUpdate:modelValue":o[28]||(o[28]=e=>r.enableMaintenanceMode=e),name:"enableMaintenanceMode",label:a.$trans("config.system.props.maintenance_mode"),error:n(s).enableMaintenanceMode,"onUpdate:error":o[29]||(o[29]=e=>n(s).enableMaintenanceMode=e)},null,8,["modelValue","label","error"])])]),r.enableMaintenanceMode?(S(),z("div",K,[t("div",Q,[l(y,{type:"text",modelValue:r.maintenanceModeMessage,"onUpdate:modelValue":o[30]||(o[30]=e=>r.maintenanceModeMessage=e),name:"appName",label:a.$trans("config.system.props.maintenance_mode_message"),error:n(s).maintenanceModeMessage,"onUpdate:error":o[31]||(o[31]=e=>n(s).maintenanceModeMessage=e)},null,8,["modelValue","label","error"])])])):C("",!0)]),_:1},8,["form"])]),_:1})}}});export{h as default};
