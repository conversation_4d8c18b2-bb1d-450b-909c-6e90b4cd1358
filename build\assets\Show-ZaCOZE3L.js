import{u as b,h as B,i as j,j as x,m as y,l as C,n as G,p as S,r as o,q as l,o as c,w as i,b as d,e as k,a as L,d as a,t as m,s as N}from"./app-BAwPsakn.js";const V={key:0,class:"mb-4"},M={class:"-mt-12 flex w-full justify-center sm:-mt-16"},U=["src"],O={class:"mt-4 text-center text-white"},R={class:"font-bold"},q={name:"GuardianShow"},T=Object.assign(q,{setup(D){const g=b(),f=B(),h=j(),r=x("emitter"),_=[{name:"GuardianShowBasic",icon:"fas fa-chevron-right",label:"general.basic"},{name:"GuardianShowContact",icon:"fas fa-chevron-right",label:"contact.contact"},{name:"GuardianShowLogin",icon:"fas fa-chevron-right",label:"contact.login.login"}],n=y(!1),e=C({}),u=async()=>{n.value=!0,await h.dispatch("guardian/get",{uuid:g.params.uuid}).then(s=>{Object.assign(e,s),n.value=!1}).catch(s=>{n.value=!1,f.push({name:"GuardianList"})})};return G(async()=>{r.on("guardianUpdated",()=>{u()}),await u()}),S(()=>{r.all.delete("guardianUpdated")}),(s,t)=>{const p=o("BaseLoader"),v=o("router-view"),w=o("ModuleConfig");return c(),l(w,{navigations:_,"use-uuid":""},{header:i(()=>[k(p,null,{default:i(()=>[e.uuid?(c(),L("div",V,[t[1]||(t[1]=a("img",{class:"h-32 w-full object-cover lg:h-48",src:"/images/user-cover.jpeg",alt:""},null,-1)),a("div",M,[a("img",{class:"h-24 w-24 rounded-full ring-4 ring-white sm:h-32 sm:w-32",src:e.contact.photo,alt:""},null,8,U)]),a("div",O,[a("h1",R,m(e.contact.name),1),a("p",null,[t[0]||(t[0]=a("i",{class:"fas fa-mobile-alt"},null,-1)),N(" "+m(e.contact.contactNumber),1)])])])):d("",!0)]),_:1})]),default:i(()=>[e.uuid?(c(),l(v,{key:0,guardian:e},null,8,["guardian"])):d("",!0)]),_:1})}}});export{T as default};
