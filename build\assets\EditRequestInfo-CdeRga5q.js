import{h as l,g as i,r as u,a as m,b as p,f as o,o as c,e as d,w as f,s as y,t as E}from"./app-BAwPsakn.js";const g={__name:"EditRequestInfo",props:{employee:{type:Object,default(){return{}}}},setup(e){const s=l(),a=i("employee.allowEmployeeToSubmitContactEditRequest");return(r,t)=>{const n=u("BaseAlert");return o(a)&&e.employee.self?(c(),m("div",{key:0,class:"mb-4 cursor-pointer",onClick:t[0]||(t[0]=_=>o(s).push({name:"EmployeeProfileEditRequestNew",params:{uuid:e.employee.uuid}}))},[d(n,{design:"info",size:"xs"},{default:f(()=>[y(E(r.$trans("employee.edit_request.edit_info")),1)]),_:1})])):p("",!0)}}};export{g as _};
