import{H as P,l as v,r as l,z as R,q as w,o as g,w as m,d as i,e as n,f as s,A as k,a as f,b as T,s as V,t as _,J as W,u as q,F as H}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},D={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},J={class:"fas fa-question-circle"},L={class:"col-span-3"},G={class:"grid grid-cols-3 gap-6"},K={class:"col-span-2 sm:col-span-1"},Q={key:0,class:"col-span-2 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3"},Z={name:"AcademicCourseForm"},x=Object.assign(Z,{setup(U){const c={name:"",term:"",division:"",code:"",shortcode:"",position:"",pgAccount:"",enableRegistration:!1,registrationFee:"",batchWithSameSubject:!1,description:""},d="academic/course/",a=P(d),p=v({divisions:[]}),t=v({...c}),b=r=>{Object.assign(p,r)},$=r=>{Object.assign(c,{...r,registrationFee:r.registrationFee.value,division:r.division.uuid}),Object.assign(t,W(c))};return(r,e)=>{const u=l("BaseInput"),F=l("BaseSelect"),A=l("BaseCheckbox"),S=l("BaseFieldset"),B=l("BaseSwitch"),j=l("TextMuted"),h=l("BaseTextarea"),C=l("FormAction"),y=R("tooltip");return g(),w(C,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:b,"init-url":d,"init-form":c,form:t,setForm:$,redirect:"AcademicCourse"},{default:m(()=>[i("div",O,[i("div",D,[n(u,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),name:"name",label:r.$trans("academic.course.props.name"),error:s(a).name,"onUpdate:error":e[1]||(e[1]=o=>s(a).name=o),autofocus:""},null,8,["modelValue","label","error"])]),i("div",N,[n(u,{type:"text",modelValue:t.term,"onUpdate:modelValue":e[2]||(e[2]=o=>t.term=o),name:"term",label:r.$trans("academic.course.props.term"),error:s(a).term,"onUpdate:error":e[3]||(e[3]=o=>s(a).term=o)},null,8,["modelValue","label","error"])]),i("div",E,[n(F,{modelValue:t.division,"onUpdate:modelValue":e[4]||(e[4]=o=>t.division=o),name:"division",label:r.$trans("academic.division.division"),"label-prop":"nameWithProgram","value-prop":"uuid",options:p.divisions,error:s(a).division,"onUpdate:error":e[5]||(e[5]=o=>s(a).division=o)},null,8,["modelValue","label","options","error"])]),i("div",z,[n(u,{type:"text",modelValue:t.code,"onUpdate:modelValue":e[6]||(e[6]=o=>t.code=o),name:"code",label:r.$trans("academic.course.props.code"),error:s(a).code,"onUpdate:error":e[7]||(e[7]=o=>s(a).code=o)},null,8,["modelValue","label","error"])]),i("div",I,[n(u,{type:"text",modelValue:t.shortcode,"onUpdate:modelValue":e[8]||(e[8]=o=>t.shortcode=o),name:"shortcode",label:r.$trans("academic.course.props.shortcode"),error:s(a).shortcode,"onUpdate:error":e[9]||(e[9]=o=>s(a).shortcode=o)},null,8,["modelValue","label","error"])]),i("div",M,[n(u,{type:"text",modelValue:t.pgAccount,"onUpdate:modelValue":e[10]||(e[10]=o=>t.pgAccount=o),name:"pgAccount",label:r.$trans("finance.config.props.pg_account"),error:s(a).pgAccount,"onUpdate:error":e[11]||(e[11]=o=>s(a).pgAccount=o)},{"additional-label":m(()=>[k(i("i",J,null,512),[[y,r.$trans("finance.config.props.pg_account_info")]])]),_:1},8,["modelValue","label","error"])]),i("div",L,[n(S,null,{legend:m(()=>[V(_(r.$trans("academic.course.registration")),1)]),default:m(()=>[i("div",G,[i("div",K,[n(A,{modelValue:t.enableRegistration,"onUpdate:modelValue":e[12]||(e[12]=o=>t.enableRegistration=o),name:"enableRegistration",label:r.$trans("academic.course.props.enable_registration")},null,8,["modelValue","label"])]),t.enableRegistration?(g(),f("div",Q,[n(u,{currency:"",modelValue:t.registrationFee,"onUpdate:modelValue":e[13]||(e[13]=o=>t.registrationFee=o),name:"registrationFee",label:r.$trans("academic.course.props.registration_fee"),error:s(a).registrationFee,"onUpdate:error":e[14]||(e[14]=o=>s(a).registrationFee=o)},null,8,["modelValue","label","error"])])):T("",!0)])]),_:1})]),i("div",X,[n(B,{modelValue:t.batchWithSameSubject,"onUpdate:modelValue":e[15]||(e[15]=o=>t.batchWithSameSubject=o),name:"batchWithSameSubject",label:r.$trans("academic.course.props.batch_with_same_subject"),error:s(a).batchWithSameSubject,"onUpdate:error":e[16]||(e[16]=o=>s(a).batchWithSameSubject=o)},null,8,["modelValue","label","error"]),n(j,{block:""},{default:m(()=>[V(_(r.$trans("academic.course.batch_with_same_subject_info")),1)]),_:1})]),i("div",Y,[n(h,{modelValue:t.description,"onUpdate:modelValue":e[17]||(e[17]=o=>t.description=o),name:"description",label:r.$trans("academic.course.props.description"),error:s(a).description,"onUpdate:error":e[18]||(e[18]=o=>s(a).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),ee={name:"AcademicCourseAction"},te=Object.assign(ee,{setup(U){const c=q();return(d,a)=>{const p=l("PageHeaderAction"),t=l("PageHeader"),b=l("ParentTransition");return g(),f(H,null,[n(t,{title:d.$trans(s(c).meta.trans,{attribute:d.$trans(s(c).meta.label)}),navs:[{label:d.$trans("academic.academic"),path:"Academic"},{label:d.$trans("academic.course.course"),path:"AcademicCourseList"}]},{default:m(()=>[n(p,{name:"AcademicCourse",title:d.$trans("academic.course.course"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(b,{appear:"",visibility:!0},{default:m(()=>[n(x)]),_:1})],64)}}});export{te as default};
