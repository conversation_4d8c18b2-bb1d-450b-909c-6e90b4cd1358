import{l as A,r as i,q as F,o as f,w as e,d as y,e as t,u as q,i as E,H as O,m as T,n as z,a as w,b as G,f as u,s as o,t as l,h as J,j as K,z as Q,F as R,v as L,A as W}from"./app-BAwPsakn.js";import{d as X}from"./vuedraggable.umd-BRYqknf6.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",emits:["hide"],setup(V,{emit:_}){const v=_,g={name:"",code:"",shortcode:"",alias:""},m=A({...g});return(k,n)=>{const d=i("BaseInput"),b=i("FilterForm");return f(),F(b,{"init-form":g,form:m,onHide:n[4]||(n[4]=c=>v("hide"))},{default:e(()=>[y("div",Y,[y("div",Z,[t(d,{type:"text",modelValue:m.name,"onUpdate:modelValue":n[0]||(n[0]=c=>m.name=c),name:"name",label:k.$trans("academic.department.props.name")},null,8,["modelValue","label"])]),y("div",x,[t(d,{type:"text",modelValue:m.code,"onUpdate:modelValue":n[1]||(n[1]=c=>m.code=c),name:"code",label:k.$trans("academic.department.props.code")},null,8,["modelValue","label"])]),y("div",ee,[t(d,{type:"text",modelValue:m.shortcode,"onUpdate:modelValue":n[2]||(n[2]=c=>m.shortcode=c),name:"shortcode",label:k.$trans("academic.department.props.shortcode")},null,8,["modelValue","label"])]),y("div",te,[t(d,{type:"text",modelValue:m.alias,"onUpdate:modelValue":n[3]||(n[3]=c=>m.alias=c),name:"alias",label:k.$trans("academic.department.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ne={key:0},se={class:"flex border rounded-xl px-4 py-2"},oe={key:1},le={key:2,class:"mt-4 flex justify-end"},ie={name:"AcademicDepartmentReorder"},re=Object.assign(ie,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(V,{emit:_}){q();const v=E(),g=_,m={departments:[]};O("academic/department/");const n=T(!1),d=A({departments:[]});A({...m});const b=async()=>{n.value=!0,await v.dispatch("academic/department/list",{params:{all:!0}}).then(a=>{n.value=!1,d.departments=a}).catch(a=>{n.value=!1})},c=async()=>{n.value=!0,await v.dispatch("academic/department/reorder",{data:{departments:d.departments}}).then(a=>{n.value=!1,g("refresh"),g("close")}).catch(a=>{n.value=!1})},r=()=>{g("close")};return z(()=>{b()}),(a,C)=>{const I=i("BaseLabel"),M=i("BaseAlert"),B=i("BaseButton"),$=i("BaseModal");return f(),F($,{show:V.visibility,onClose:r},{title:e(()=>[o(l(a.$trans("global.reorder",{attribute:a.$trans("academic.department.department")})),1)]),default:e(()=>[d.departments.length?(f(),w("div",ne,[t(u(X),{class:"space-y-2",list:d.departments,"item-key":"uuid"},{item:e(({element:D,index:h})=>[y("div",se,[C[0]||(C[0]=y("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(I,null,{default:e(()=>[o(l(D.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(f(),w("div",oe,[t(M,{design:"info",size:"xs"},{default:e(()=>[o(l(a.$trans("general.errors.record_not_found")),1)]),_:1})])),d.departments.length?(f(),w("div",le,[t(B,{onClick:c},{default:e(()=>[o(l(a.$trans("general.reorder")),1)]),_:1})])):G("",!0)]),_:1},8,["show"])}}}),de={name:"AcademicDepartmentList"},ce=Object.assign(de,{setup(V){const _=J(),v=K("emitter");let g=["create","filter"],m=["print","pdf","excel"];const k="academic/department/",n=T(!1),d=T(!1),b=A({}),c=r=>{Object.assign(b,r)};return(r,a)=>{const C=i("BaseButton"),I=i("PageHeaderAction"),M=i("PageHeader"),B=i("ParentTransition"),$=i("DataCell"),D=i("TextMuted"),h=i("FloatingMenuItem"),U=i("FloatingMenu"),j=i("DataRow"),P=i("DataTable"),S=i("ListItem"),N=Q("tooltip");return f(),w(R,null,[t(S,{"init-url":k,"additional-query":{details:!0},onSetItems:c},{header:e(()=>[t(M,{title:r.$trans("academic.department.department"),navs:[{label:r.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(I,{url:"academic/departments/",name:"AcademicDepartment",title:r.$trans("academic.department.department"),actions:u(g),"dropdown-actions":u(m),"additional-dropdown-actions-query":{details:!0},onToggleFilter:a[2]||(a[2]=s=>n.value=!n.value)},{default:e(()=>[W((f(),F(C,{design:"white",onClick:a[0]||(a[0]=s=>d.value=!d.value)},{default:e(()=>a[9]||(a[9]=[y("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[N,r.$trans("global.reorder",{attribute:r.$trans("academic.department.department")})]]),t(C,{design:"white",onClick:a[1]||(a[1]=s=>u(_).push({name:"AcademicDepartmentIncharge"}))},{default:e(()=>[o(l(r.$trans("employee.incharge.incharge")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(B,{appear:"",visibility:n.value},{default:e(()=>[t(ae,{onRefresh:a[3]||(a[3]=s=>u(v).emit("listItems")),onHide:a[4]||(a[4]=s=>n.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(B,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:b.headers,meta:b.meta,module:"academic.department",onRefresh:a[6]||(a[6]=s=>u(v).emit("listItems"))},{actionButton:e(()=>[t(C,{onClick:a[5]||(a[5]=s=>u(_).push({name:"AcademicDepartmentCreate"}))},{default:e(()=>[o(l(r.$trans("global.add",{attribute:r.$trans("academic.department.department")})),1)]),_:1})]),default:e(()=>[(f(!0),w(R,null,L(b.data,s=>(f(),F(j,{key:s.uuid,onDoubleClick:p=>u(_).push({name:"AcademicDepartmentShow",params:{uuid:s.uuid}})},{default:e(()=>[t($,{name:"name"},{default:e(()=>[o(l(s.name),1)]),_:2},1024),t($,{name:"programCount"},{default:e(()=>[o(l(s.programsCount),1)]),_:2},1024),t($,{name:"code"},{default:e(()=>[o(l(s.code)+" ",1),t(D,{block:""},{default:e(()=>[o(l(s.shortcode),1)]),_:2},1024)]),_:2},1024),t($,{name:"alias"},{default:e(()=>[o(l(s.alias),1)]),_:2},1024),t($,{name:"incharge"},{default:e(()=>[(f(!0),w(R,null,L(s.incharges,p=>{var H;return f(),w("div",null,[o(l(((H=p==null?void 0:p.employee)==null?void 0:H.name)||"-")+" ",1),t(D,{block:""},{default:e(()=>[o(l(p==null?void 0:p.period),1)]),_:2},1024)])}),256))]),_:2},1024),t($,{name:"createdAt"},{default:e(()=>[o(l(s.createdAt.formatted),1)]),_:2},1024),t($,{name:"action"},{default:e(()=>[t(U,null,{default:e(()=>[t(h,{icon:"fas fa-arrow-circle-right",onClick:p=>u(_).push({name:"AcademicDepartmentShow",params:{uuid:s.uuid}})},{default:e(()=>[o(l(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-edit",onClick:p=>u(_).push({name:"AcademicDepartmentEdit",params:{uuid:s.uuid}})},{default:e(()=>[o(l(r.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-copy",onClick:p=>u(_).push({name:"AcademicDepartmentDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[o(l(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-trash",onClick:p=>u(v).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[o(l(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),t(re,{visibility:d.value,onClose:a[7]||(a[7]=s=>d.value=!1),onRefresh:a[8]||(a[8]=s=>u(v).emit("listItems"))},null,8,["visibility"])],64)}}});export{ce as default};
