import{H as b,l as T,r as s,q as $,o as d,w as c,d as m,e as r,f as n,u as v,a as B,F as V}from"./app-BAwPsakn.js";const F={class:"grid grid-cols-3 gap-6"},P={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3"},H={name:"TransportStoppageForm"},S=Object.assign(H,{setup(u){const p={name:"",description:""},e="transport/stoppage/",l=b(e),o=T({...p});return(i,t)=>{const _=s("BaseInput"),g=s("BaseTextarea"),f=s("FormAction");return d(),$(f,{"init-url":e,"init-form":p,form:o,redirect:"TransportStoppage"},{default:c(()=>[m("div",F,[m("div",P,[r(_,{type:"text",modelValue:o.name,"onUpdate:modelValue":t[0]||(t[0]=a=>o.name=a),name:"name",label:i.$trans("transport.stoppage.props.name"),error:n(l).name,"onUpdate:error":t[1]||(t[1]=a=>n(l).name=a),autofocus:""},null,8,["modelValue","label","error"])]),m("div",A,[r(g,{modelValue:o.description,"onUpdate:modelValue":t[2]||(t[2]=a=>o.description=a),name:"description",label:i.$trans("transport.stoppage.props.description"),error:n(l).description,"onUpdate:error":t[3]||(t[3]=a=>n(l).description=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),U={name:"TransportStoppageAction"},E=Object.assign(U,{setup(u){const p=v();return(e,l)=>{const o=s("PageHeaderAction"),i=s("PageHeader"),t=s("ParentTransition");return d(),B(V,null,[r(i,{title:e.$trans(n(p).meta.trans,{attribute:e.$trans(n(p).meta.label)}),navs:[{label:e.$trans("transport.transport"),path:"Transport"},{label:e.$trans("transport.stoppage.stoppage"),path:"TransportStoppageList"}]},{default:c(()=>[r(o,{name:"TransportStoppage",title:e.$trans("transport.stoppage.stoppage"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(t,{appear:"",visibility:!0},{default:c(()=>[r(S)]),_:1})],64)}}});export{E as default};
