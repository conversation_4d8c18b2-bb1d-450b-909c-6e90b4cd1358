import{i as R,u as j,h as N,j as $,l as E,r as i,a as y,o as u,e as n,w as t,f as e,q as _,b as p,d as F,F as g,v as O,s as l,t as s,y as z}from"./app-BAwPsakn.js";const U={class:"space-y-4"},G={key:1,class:"px-4 py-2"},J={name:"InventoryStockItemShow"},Q=Object.assign(J,{setup(K){R();const d=j(),k=N(),o=$("$trans"),I={},S="inventory/stockItem/",a=E({...I}),B=[{key:"room",label:o("asset.building.room.room"),visibility:!0},{key:"quantity",label:o("inventory.stock_item.props.quantity"),visibility:!0}],h=b=>{Object.assign(a,b)};return(b,m)=>{const w=i("PageHeaderAction"),C=i("PageHeader"),c=i("ListItemView"),V=i("ListContainerVertical"),f=i("BaseCard"),v=i("DataCell"),D=i("DataRow"),L=i("SimpleTable"),x=i("BaseAlert"),A=i("BaseButton"),P=i("ShowButton"),T=i("DetailLayoutVertical"),q=i("ShowItem"),H=i("ParentTransition");return u(),y(g,null,[n(C,{title:e(o)(e(d).meta.trans,{attribute:e(o)(e(d).meta.label)}),navs:[{label:e(o)("inventory.inventory"),path:"Inventory"},{label:e(o)("inventory.stock_item.stock_item"),path:"InventoryStockItem"}]},{default:t(()=>[n(w,{name:"InventoryStockItem",title:e(o)("inventory.stock_item.stock_item"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(H,{appear:"",visibility:!0},{default:t(()=>[n(q,{"init-url":S,uuid:e(d).params.uuid,"module-uuid":e(d).params.muuid,onSetItem:h,onRedirectTo:m[1]||(m[1]=r=>e(k).push({name:"InventoryStockItem",params:{uuid:a.uuid}}))},{default:t(()=>[a.uuid?(u(),_(T,{key:0},{detail:t(()=>[n(f,{"no-padding":"","no-content-padding":""},{title:t(()=>[l(s(e(o)("global.detail",{attribute:e(o)("inventory.stock_item.stock_item")})),1)]),default:t(()=>[n(V,null,{default:t(()=>[n(c,{label:e(o)("inventory.stock_item.props.name")},{default:t(()=>[l(s(a.name),1)]),_:1},8,["label"]),n(c,{label:e(o)("inventory.stock_item.props.code")},{default:t(()=>[l(s(a.code),1)]),_:1},8,["label"]),n(c,{label:e(o)("inventory.stock_item.props.unit")},{default:t(()=>[l(s(a.unit),1)]),_:1},8,["label"]),n(c,{label:e(o)("inventory.stock_category.stock_category")},{default:t(()=>{var r;return[l(s((r=a.category)==null?void 0:r.name),1)]}),_:1},8,["label"]),n(c,{label:e(o)("inventory.stock_item.props.description")},{default:t(()=>[l(s(a.description),1)]),_:1},8,["label"]),n(c,{label:e(o)("general.created_at")},{default:t(()=>[l(s(a.createdAt.formatted),1)]),_:1},8,["label"]),n(c,{label:e(o)("general.updated_at")},{default:t(()=>[l(s(a.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:t(()=>[F("div",U,[n(f,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:t(()=>[l(s(e(o)("inventory.stock_item.stock_item")),1)]),footer:t(()=>[n(P,null,{default:t(()=>[e(z)("stock-item:edit")?(u(),_(A,{key:0,design:"primary",onClick:m[0]||(m[0]=r=>e(k).push({name:"InventoryStockItemEdit",params:{uuid:a.uuid}}))},{default:t(()=>[l(s(e(o)("general.edit")),1)]),_:1})):p("",!0)]),_:1})]),default:t(()=>[a.balances.length>0?(u(),_(L,{key:0,header:B},{default:t(()=>[(u(!0),y(g,null,O(a.balances,r=>(u(),_(D,{key:r.uuid},{default:t(()=>[n(v,{name:"room"},{default:t(()=>[l(s(r.room),1)]),_:2},1024),n(v,{name:"quantity"},{default:t(()=>[l(s(r.quantity)+" "+s(a.unit),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):p("",!0),a.balances.length===0?(u(),y("div",G,[n(x,{design:"info",size:"xs"},{default:t(()=>[l(s(e(o)("general.errors.record_not_found")),1)]),_:1})])):p("",!0)]),_:1})])]),_:1})):p("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{Q as default};
