import{u as A,G as B,H as E,l as y,r as i,q as I,o as b,w as c,d,a as g,b as R,f as o,B as $,e as l,s as U,t as m,F as _,J as L}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-6"},G={key:0,class:"col-span-3 sm:col-span-1"},J={key:1,class:"col-span-3 sm:col-span-1"},z={key:2,class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-2"},x={class:"col-span-3"},h={class:"grid grid-cols-1"},ee={class:"col"},te={name:"ReceptionComplaintForm"},ne=Object.assign(te,{setup(F){const N=A(),r={codeNumber:"",student:"",type:"",subject:"",date:"",complainantName:"",complainantContactNumber:"",complainantAddress:"",description:"",media:[],mediaUpdated:!1,mediaToken:B(),mediaHash:[]},f="reception/complaint/",s=E(f),V=y({students:[],types:[]}),p=y({selectedStudent:null}),n=y({...r}),S=y({student:"",isLoaded:!N.params.uuid}),H=a=>{Object.assign(V,a)},O=()=>{n.mediaToken=B(),n.mediaHash=[]},D=a=>{var u,v;let t=a.student;t&&(p.selectedStudent={uuid:t.uuid,name:t.name,codeNumber:t.codeNumber,courseName:t.courseName,batchName:t.batchName,joiningDate:t.joiningDate}),Object.assign(r,{...a,student:(u=a.student)==null?void 0:u.uuid,date:a.date.value,type:(v=a.type)==null?void 0:v.uuid}),Object.assign(n,L(r)),S.isLoaded=!0},j=a=>{n.student=a?a.uuid:""},k=()=>{p.selectedStudent=null,n.student=""};return(a,t)=>{const u=i("BaseInput"),v=i("BaseSelect"),T=i("BaseSelectSearch"),q=i("DatePicker"),C=i("BaseTextarea"),M=i("MediaUpload"),w=i("FormAction");return b(),I(w,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":f,"init-form":r,form:n,"set-form":D,redirect:"ReceptionComplaint",onResetMediaFiles:O},{default:c(()=>[d("div",P,[o($)(["student","guardian"],"any")?R("",!0):(b(),g("div",G,[l(u,{type:"text",modelValue:n.codeNumber,"onUpdate:modelValue":t[0]||(t[0]=e=>n.codeNumber=e),name:"codeNumber",label:a.$trans("reception.complaint.props.code_number"),error:o(s).codeNumber,"onUpdate:error":t[1]||(t[1]=e=>o(s).codeNumber=e),disabled:!!o(N).params.uuid},null,8,["modelValue","label","error","disabled"])])),o($)(["student","guardian"],"any")?(b(),g("div",J,[l(v,{name:"student",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:p.selectedStudent,"onUpdate:modelValue":t[2]||(t[2]=e=>p.selectedStudent=e),error:o(s).student,"onUpdate:error":t[3]||(t[3]=e=>o(s).student=e),options:V.students,"value-prop":"uuid","object-prop":!0,onSelected:j,onRemoved:k},{selectedOption:c(e=>[U(m(e.value.name)+" ("+m(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:c(e=>[U(m(e.option.name)+" ("+m(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","options"])])):(b(),g("div",z,[l(T,{name:"student",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:p.selectedStudent,"onUpdate:modelValue":t[4]||(t[4]=e=>p.selectedStudent=e),error:o(s).student,"onUpdate:error":t[5]||(t[5]=e=>o(s).student=e),"value-prop":"uuid","object-prop":!0,"init-search":S.student,"search-key":"name","search-action":"student/summary",onSelected:j,onRemoved:k},{selectedOption:c(e=>[U(m(e.value.name)+" ("+m(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:c(e=>[U(m(e.option.name)+" ("+m(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])])),d("div",K,[l(v,{modelValue:n.type,"onUpdate:modelValue":t[6]||(t[6]=e=>n.type=e),name:"type",label:a.$trans("reception.complaint.props.type"),options:V.types,"label-prop":"name","value-prop":"uuid",error:o(s).type,"onUpdate:error":t[7]||(t[7]=e=>o(s).type=e)},null,8,["modelValue","label","options","error"])]),d("div",Q,[l(u,{type:"text",modelValue:n.subject,"onUpdate:modelValue":t[8]||(t[8]=e=>n.subject=e),name:"subject",label:a.$trans("reception.complaint.props.subject"),error:o(s).subject,"onUpdate:error":t[9]||(t[9]=e=>o(s).subject=e),autofocus:""},null,8,["modelValue","label","error"])]),o($)(["student","guardian"],"any")?R("",!0):(b(),g(_,{key:3},[d("div",W,[l(q,{modelValue:n.date,"onUpdate:modelValue":t[10]||(t[10]=e=>n.date=e),name:"date",label:a.$trans("reception.complaint.props.date"),"no-clear":"",error:o(s).date,"onUpdate:error":t[11]||(t[11]=e=>o(s).date=e)},null,8,["modelValue","label","error"])]),d("div",X,[l(u,{type:"text",modelValue:n.complainantName,"onUpdate:modelValue":t[12]||(t[12]=e=>n.complainantName=e),name:"complainantName",label:a.$trans("reception.complaint.props.name"),error:o(s).complainantName,"onUpdate:error":t[13]||(t[13]=e=>o(s).complainantName=e)},null,8,["modelValue","label","error"])]),d("div",Y,[l(u,{type:"text",modelValue:n.complainantContactNumber,"onUpdate:modelValue":t[14]||(t[14]=e=>n.complainantContactNumber=e),name:"complainantContactNumber",label:a.$trans("reception.complaint.props.contact_number"),error:o(s).complainantContactNumber,"onUpdate:error":t[15]||(t[15]=e=>o(s).complainantContactNumber=e)},null,8,["modelValue","label","error"])]),d("div",Z,[l(C,{rows:1,modelValue:n.complainantAddress,"onUpdate:modelValue":t[16]||(t[16]=e=>n.complainantAddress=e),name:"complainantAddress",label:a.$trans("reception.complaint.props.address"),error:o(s).complainantAddress,"onUpdate:error":t[17]||(t[17]=e=>o(s).complainantAddress=e)},null,8,["modelValue","label","error"])])],64)),d("div",x,[l(C,{modelValue:n.description,"onUpdate:modelValue":t[18]||(t[18]=e=>n.description=e),name:"description",label:a.$trans("reception.complaint.props.description"),error:o(s).description,"onUpdate:error":t[19]||(t[19]=e=>o(s).description=e)},null,8,["modelValue","label","error"])])]),d("div",h,[d("div",ee,[l(M,{multiple:"",label:a.$trans("general.file"),module:"complaint",media:n.media,"media-token":n.mediaToken,onIsUpdated:t[20]||(t[20]=e=>n.mediaUpdated=!0),onSetHash:t[21]||(t[21]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),oe={name:"ReceptionComplaintAction"},se=Object.assign(oe,{setup(F){const N=A();return(r,f)=>{const s=i("PageHeaderAction"),V=i("PageHeader"),p=i("ParentTransition");return b(),g(_,null,[l(V,{title:r.$trans(o(N).meta.trans,{attribute:r.$trans(o(N).meta.label)}),navs:[{label:r.$trans("reception.reception"),path:"Reception"},{label:r.$trans("reception.complaint.complaint"),path:"ReceptionComplaintList"}]},{default:c(()=>[l(s,{name:"ReceptionComplaint",title:r.$trans("reception.complaint.complaint"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(p,{appear:"",visibility:!0},{default:c(()=>[l(ne)]),_:1})],64)}}});export{se as default};
