import{i as k,u as S,h as C,l as P,r as o,a as T,o as m,e as a,w as e,f as i,q as V,b as N,d as A,s as r,t as s,F as H}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"InventoryInchargeShow"},F=Object.assign(R,{setup(j){k();const d=S(),c=C(),p={},g="inventory/incharge/",n=P({...p}),_=t=>{Object.assign(n,t)};return(t,u)=>{const y=o("PageHeaderAction"),h=o("PageHeader"),f=o("TextMuted"),l=o("BaseDataView"),b=o("BaseButton"),v=o("ShowButton"),I=o("BaseCard"),B=o("ShowItem"),$=o("ParentTransition");return m(),T(H,null,[a(h,{title:t.$trans(i(d).meta.trans,{attribute:t.$trans(i(d).meta.label)}),navs:[{label:t.$trans("inventory.inventory"),path:"Inventory"},{label:t.$trans("inventory.incharge.incharge"),path:"InventoryInchargeList"}]},{default:e(()=>[a(y,{name:"InventoryIncharge",title:t.$trans("inventory.incharge.incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a($,{appear:"",visibility:!0},{default:e(()=>[a(B,{"init-url":g,uuid:i(d).params.uuid,onSetItem:_,onRedirectTo:u[1]||(u[1]=w=>i(c).push({name:"InventoryIncharge"}))},{default:e(()=>[n.uuid?(m(),V(I,{key:0},{title:e(()=>[r(s(n.inventory.name),1)]),footer:e(()=>[a(v,null,{default:e(()=>[a(b,{design:"primary",onClick:u[0]||(u[0]=w=>i(c).push({name:"InventoryInchargeEdit",params:{uuid:n.uuid}}))},{default:e(()=>[r(s(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[A("dl",D,[a(l,{label:t.$trans("employee.employee")},{default:e(()=>[r(s(n.employee.name)+" ",1),a(f,{block:""},{default:e(()=>[r(s(n.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),a(l,{label:t.$trans("employee.incharge.props.period")},{default:e(()=>[r(s(n.period),1)]),_:1},8,["label"]),a(l,{class:"col-span-1 sm:col-span-2",label:t.$trans("employee.incharge.props.remarks")},{default:e(()=>[r(s(n.remarks),1)]),_:1},8,["label"]),a(l,{label:t.$trans("general.created_at")},{default:e(()=>[r(s(n.createdAt.formatted),1)]),_:1},8,["label"]),a(l,{label:t.$trans("general.updated_at")},{default:e(()=>[r(s(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):N("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
