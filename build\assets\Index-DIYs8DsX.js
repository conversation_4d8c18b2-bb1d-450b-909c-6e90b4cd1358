import{u as R,l as L,n as j,r as n,q as f,o as u,w as e,d as C,e as a,b as $,h as q,j as N,y as v,m as U,f as o,a as E,F as O,v as z,s as r,t as d}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",emits:["hide"],setup(D,{emit:_}){const y=R(),F=_,b={name:"",alias:"",ledgerTypes:[]},c=L({...b}),m=L({ledgerTypes:[],isLoaded:!y.query.ledgerTypes});return j(async()=>{m.ledgerTypes=y.query.ledgerTypes?y.query.ledgerTypes.split(","):[],m.isLoaded=!0}),(p,i)=>{const l=n("BaseInput"),s=n("BaseSelectSearch"),B=n("FilterForm");return u(),f(B,{"init-form":b,form:c,multiple:["ledgerTypes"],onHide:i[3]||(i[3]=g=>F("hide"))},{default:e(()=>[C("div",G,[C("div",J,[a(l,{type:"text",modelValue:c.name,"onUpdate:modelValue":i[0]||(i[0]=g=>c.name=g),name:"name",label:p.$trans("finance.ledger.props.name")},null,8,["modelValue","label"])]),C("div",K,[a(l,{type:"text",modelValue:c.alias,"onUpdate:modelValue":i[1]||(i[1]=g=>c.alias=g),name:"alias",label:p.$trans("finance.ledger.props.alias")},null,8,["modelValue","label"])]),C("div",Q,[m.isLoaded?(u(),f(s,{key:0,multiple:"",name:"ledgerTypes",label:p.$trans("global.select",{attribute:p.$trans("finance.ledger_type.ledger_type")}),modelValue:c.ledgerTypes,"onUpdate:modelValue":i[2]||(i[2]=g=>c.ledgerTypes=g),"value-prop":"uuid","init-search":m.ledgerTypes,"search-action":"finance/ledgerType/list"},null,8,["label","modelValue","init-search"])):$("",!0)])])]),_:1},8,["form"])}}},X={name:"FinanceLedgerList"},Z=Object.assign(X,{setup(D){const _=q(),y=N("emitter");let F=["filter"];v("ledger:create")&&F.unshift("create");let b=[];v("ledger:export")&&(b=["print","pdf","excel"]);const c="finance/ledger/",m=U(!1),p=L({}),i=l=>{Object.assign(p,l)};return(l,s)=>{const B=n("PageHeaderAction"),g=n("PageHeader"),V=n("ParentTransition"),w=n("TextMuted"),k=n("DataCell"),I=n("LedgerBalance"),h=n("FloatingMenuItem"),S=n("FloatingMenu"),M=n("DataRow"),A=n("BaseButton"),H=n("DataTable"),P=n("ListItem");return u(),f(P,{"init-url":c,onSetItems:i},{header:e(()=>[a(g,{title:l.$trans("finance.ledger.ledger"),navs:[{label:l.$trans("finance.finance"),path:"Finance"}]},{default:e(()=>[a(B,{url:"finance/ledgers/",name:"FinanceLedger",title:l.$trans("finance.ledger.ledger"),actions:o(F),"dropdown-actions":o(b),onToggleFilter:s[0]||(s[0]=t=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(V,{appear:"",visibility:m.value},{default:e(()=>[a(W,{onRefresh:s[1]||(s[1]=t=>o(y).emit("listItems")),onHide:s[2]||(s[2]=t=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(V,{appear:"",visibility:!0},{default:e(()=>[a(H,{header:p.headers,meta:p.meta,module:"finance.ledger",onRefresh:s[4]||(s[4]=t=>o(y).emit("listItems"))},{actionButton:e(()=>[o(v)("ledger:create")?(u(),f(A,{key:0,onClick:s[3]||(s[3]=t=>o(_).push({name:"FinanceLedgerCreate"}))},{default:e(()=>[r(d(l.$trans("global.add",{attribute:l.$trans("finance.ledger.ledger")})),1)]),_:1})):$("",!0)]),default:e(()=>[(u(!0),E(O,null,z(p.data,t=>(u(),f(M,{key:t.uuid,onDoubleClick:T=>o(_).push({name:"FinanceLedgerShow",params:{uuid:t.uuid}})},{default:e(()=>[a(k,{name:"name"},{default:e(()=>[r(d(t.name)+" ",1),a(w,{block:""},{default:e(()=>[r(d(t.alias),1)]),_:2},1024)]),_:2},1024),a(k,{name:"type"},{default:e(()=>[r(d(t.type.name)+" ",1),t.code?(u(),f(w,{key:0,block:""},{default:e(()=>[r(d(t.code),1)]),_:2},1024)):$("",!0)]),_:2},1024),a(k,{name:"netBalance"},{default:e(()=>[a(I,{highlight:"",ledger:t},null,8,["ledger"])]),_:2},1024),a(k,{name:"createdAt"},{default:e(()=>[r(d(t.createdAt.formatted),1)]),_:2},1024),a(k,{name:"action"},{default:e(()=>[a(S,null,{default:e(()=>[a(h,{icon:"fas fa-arrow-circle-right",onClick:T=>o(_).push({name:"FinanceLedgerShow",params:{uuid:t.uuid}})},{default:e(()=>[r(d(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(v)("ledger:edit")?(u(),f(h,{key:0,icon:"fas fa-edit",onClick:T=>o(_).push({name:"FinanceLedgerEdit",params:{uuid:t.uuid}})},{default:e(()=>[r(d(l.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),o(v)("ledger:create")?(u(),f(h,{key:1,icon:"fas fa-copy",onClick:T=>o(_).push({name:"FinanceLedgerDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[r(d(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),o(v)("ledger:delete")?(u(),f(h,{key:2,icon:"fas fa-trash",onClick:T=>o(y).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[r(d(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Z as default};
