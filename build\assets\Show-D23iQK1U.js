import{i as v,u as k,h as P,l as V,r as l,a as D,o as c,e as t,w as e,f as d,q as m,b as _,d as A,s as o,t as s,y as I,F as N}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"CalendarHolidayShow"},F=Object.assign(R,{setup(j){v();const u=k(),p=P(),f={},y="calendar/holiday/",n=V({...f}),b=a=>{Object.assign(n,a)};return(a,i)=>{const h=l("PageHeaderAction"),g=l("PageHeader"),r=l("BaseDataView"),B=l("BaseButton"),$=l("ShowButton"),C=l("BaseCard"),H=l("ShowItem"),w=l("ParentTransition");return c(),D(N,null,[t(g,{title:a.$trans(d(u).meta.trans,{attribute:a.$trans(d(u).meta.label)}),navs:[{label:a.$trans("calendar.calendar"),path:"Calendar"},{label:a.$trans("calendar.holiday.holiday"),path:"CalendarHolidayList"}]},{default:e(()=>[t(h,{name:"CalendarHoliday",title:a.$trans("calendar.holiday.holiday"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(w,{appear:"",visibility:!0},{default:e(()=>[t(H,{"init-url":y,uuid:d(u).params.uuid,onSetItem:b,onRedirectTo:i[1]||(i[1]=S=>d(p).push({name:"Holiday"}))},{default:e(()=>[n.uuid?(c(),m(C,{key:0},{title:e(()=>[o(s(n.name),1)]),footer:e(()=>[t($,null,{default:e(()=>[d(I)("holiday:edit")?(c(),m(B,{key:0,design:"primary",onClick:i[0]||(i[0]=S=>d(p).push({name:"CalendarHolidayEdit",params:{uuid:n.uuid}}))},{default:e(()=>[o(s(a.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[A("dl",T,[t(r,{label:a.$trans("calendar.holiday.props.name")},{default:e(()=>[o(s(n.name),1)]),_:1},8,["label"]),t(r,{label:a.$trans("calendar.holiday.props.start_date")},{default:e(()=>[o(s(n.startDate.formatted),1)]),_:1},8,["label"]),t(r,{label:a.$trans("calendar.holiday.props.end_date")},{default:e(()=>[o(s(n.endDate.formatted),1)]),_:1},8,["label"]),t(r,{class:"col-span-1 sm:col-span-2",label:a.$trans("calendar.holiday.props.description")},{default:e(()=>[o(s(n.description),1)]),_:1},8,["label"]),t(r,{label:a.$trans("general.created_at")},{default:e(()=>[o(s(n.createdAt.formatted),1)]),_:1},8,["label"]),t(r,{label:a.$trans("general.updated_at")},{default:e(()=>[o(s(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
