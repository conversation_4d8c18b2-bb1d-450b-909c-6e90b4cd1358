import{u as g,c as u,g as e,r as _,a as s,o as t,b as r,d as n,e as m,f as c}from"./app-BAwPsakn.js";const p={class:"dark:bg-dark-body relative flex min-h-screen flex-col justify-center overflow-hidden bg-gray-50 py-6 sm:py-12"},h={key:0,class:"bg-site-background absolute inset-0 bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"},b={class:"dark:bg-dark-header container relative bg-white px-6 pb-8 pt-10 shadow-xl ring-1 ring-gray-900/5 dark:ring-gray-400/5 sm:mx-auto sm:rounded-lg sm:px-10"},k={class:"mx-auto max-w-full"},f=["src"],v={},C=Object.assign(v,{__name:"Blank",setup(y){const i=g(),a=u(()=>e("layout.display").value=="dark"),l=a.value?e("assets.logoLight"):e("assets.logo");return(x,w)=>{var o;const d=_("router-view");return t(),s("div",p,[a.value?r("",!0):(t(),s("div",h)),n("div",b,[n("div",k,[(o=c(i).meta)!=null&&o.showLogo?(t(),s("img",{key:0,src:c(l),class:"mb-4 h-20",alt:"ScriptMint"},null,8,f)):r("",!0),m(d)])])])}}});export{C as default};
