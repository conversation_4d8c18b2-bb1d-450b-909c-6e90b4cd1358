import{H as b,l as y,r as d,q as U,o as $,w as g,e as n,d as t,f as r}from"./app-BAwPsakn.js";const v={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-2"},D={class:"col-span-3"},c={class:"col-span-3"},L={class:"col-span-3"},w={class:"grid grid-cols-3 gap-4"},F={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},W={class:"grid grid-cols-3 gap-4"},Z={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},H={name:"ConfigGeneral"},q=Object.assign(H,{setup(I){const m="config/",p=b(m),u={appName:"",appDescription:"",metaAuthor:"",metaDescription:"",metaKeywords:"",appEmail:"",appPhone:"",appFax:"",appWebsite:"",appAddressLine1:"",appAddressLine2:"",appCity:"",appState:"",appZipcode:"",appCountry:"",type:"general"},a=y({...u});return(l,e)=>{const i=d("CardHeader"),s=d("BaseInput"),V=d("FormAction"),f=d("ConfigPage");return $(),U(f,null,{default:g(()=>[n(V,{"no-card":"","init-url":m,"data-fetch":"general","init-form":u,form:a,action:"store","stay-on":"",redirect:"Dashboard"},{default:g(()=>[n(i,{first:"",title:l.$trans("config.general.general_config"),description:l.$trans("config.general.general_info")},null,8,["title","description"]),t("div",v,[t("div",A,[n(s,{type:"text",modelValue:a.appName,"onUpdate:modelValue":e[0]||(e[0]=o=>a.appName=o),name:"appName",label:l.$trans("config.general.props.app_name"),error:r(p).appName,"onUpdate:error":e[1]||(e[1]=o=>r(p).appName=o)},null,8,["modelValue","label","error"])]),t("div",C,[n(s,{type:"text",modelValue:a.appDescription,"onUpdate:modelValue":e[2]||(e[2]=o=>a.appDescription=o),name:"appDescription",label:l.$trans("config.general.props.app_description"),error:r(p).appDescription,"onUpdate:error":e[3]||(e[3]=o=>r(p).appDescription=o)},null,8,["modelValue","label","error"])]),t("div",D,[n(s,{type:"text",modelValue:a.metaAuthor,"onUpdate:modelValue":e[4]||(e[4]=o=>a.metaAuthor=o),name:"metaAuthor",label:l.$trans("config.general.props.meta_author"),error:r(p).metaAuthor,"onUpdate:error":e[5]||(e[5]=o=>r(p).metaAuthor=o)},null,8,["modelValue","label","error"])]),t("div",c,[n(s,{type:"text",modelValue:a.metaDescription,"onUpdate:modelValue":e[6]||(e[6]=o=>a.metaDescription=o),name:"metaDescription",label:l.$trans("config.general.props.meta_description"),error:r(p).metaDescription,"onUpdate:error":e[7]||(e[7]=o=>r(p).metaDescription=o)},null,8,["modelValue","label","error"])]),t("div",L,[n(s,{type:"text",modelValue:a.metaKeywords,"onUpdate:modelValue":e[8]||(e[8]=o=>a.metaKeywords=o),name:"metaKeywords",label:l.$trans("config.general.props.meta_keywords"),error:r(p).metaKeywords,"onUpdate:error":e[9]||(e[9]=o=>r(p).metaKeywords=o)},null,8,["modelValue","label","error"])])]),n(i,{title:l.$trans("config.general.address"),description:l.$trans("config.general.address_info")},null,8,["title","description"]),t("div",w,[t("div",F,[n(s,{type:"text",modelValue:a.appAddressLine1,"onUpdate:modelValue":e[10]||(e[10]=o=>a.appAddressLine1=o),name:"appAddressLine1",label:l.$trans("config.general.props.app_address_line1"),error:r(p).appAddressLine1,"onUpdate:error":e[11]||(e[11]=o=>r(p).appAddressLine1=o)},null,8,["modelValue","label","error"])]),t("div",E,[n(s,{type:"text",modelValue:a.appAddressLine2,"onUpdate:modelValue":e[12]||(e[12]=o=>a.appAddressLine2=o),name:"appAddressLine2",label:l.$trans("config.general.props.app_address_line2"),error:r(p).appAddressLine2,"onUpdate:error":e[13]||(e[13]=o=>r(p).appAddressLine2=o)},null,8,["modelValue","label","error"])]),t("div",N,[n(s,{type:"text",modelValue:a.appCity,"onUpdate:modelValue":e[14]||(e[14]=o=>a.appCity=o),name:"appCity",label:l.$trans("config.general.props.app_city"),error:r(p).appCity,"onUpdate:error":e[15]||(e[15]=o=>r(p).appCity=o)},null,8,["modelValue","label","error"])]),t("div",P,[n(s,{type:"text",modelValue:a.appState,"onUpdate:modelValue":e[16]||(e[16]=o=>a.appState=o),name:"appState",label:l.$trans("config.general.props.app_state"),error:r(p).appState,"onUpdate:error":e[17]||(e[17]=o=>r(p).appState=o)},null,8,["modelValue","label","error"])]),t("div",K,[n(s,{type:"text",modelValue:a.appZipcode,"onUpdate:modelValue":e[18]||(e[18]=o=>a.appZipcode=o),name:"appZipcode",label:l.$trans("config.general.props.app_zipcode"),error:r(p).appZipcode,"onUpdate:error":e[19]||(e[19]=o=>r(p).appZipcode=o)},null,8,["modelValue","label","error"])]),t("div",S,[n(s,{type:"text",modelValue:a.appCountry,"onUpdate:modelValue":e[20]||(e[20]=o=>a.appCountry=o),name:"appCountry",label:l.$trans("config.general.props.app_country"),error:r(p).appCountry,"onUpdate:error":e[21]||(e[21]=o=>r(p).appCountry=o)},null,8,["modelValue","label","error"])])]),n(i,{title:l.$trans("config.general.contact"),description:l.$trans("config.general.contact_info")},null,8,["title","description"]),t("div",W,[t("div",Z,[n(s,{type:"text",modelValue:a.appEmail,"onUpdate:modelValue":e[22]||(e[22]=o=>a.appEmail=o),name:"appEmail",label:l.$trans("config.general.props.app_email"),error:r(p).appEmail,"onUpdate:error":e[23]||(e[23]=o=>r(p).appEmail=o)},null,8,["modelValue","label","error"])]),t("div",B,[n(s,{type:"text",modelValue:a.appPhone,"onUpdate:modelValue":e[24]||(e[24]=o=>a.appPhone=o),name:"appPhone",label:l.$trans("config.general.props.app_phone"),error:r(p).appPhone,"onUpdate:error":e[25]||(e[25]=o=>r(p).appPhone=o)},null,8,["modelValue","label","error"])]),t("div",x,[n(s,{type:"text",modelValue:a.appFax,"onUpdate:modelValue":e[26]||(e[26]=o=>a.appFax=o),name:"appFax",label:l.$trans("config.general.props.app_fax"),error:r(p).appFax,"onUpdate:error":e[27]||(e[27]=o=>r(p).appFax=o)},null,8,["modelValue","label","error"])]),t("div",k,[n(s,{type:"text",modelValue:a.appWebsite,"onUpdate:modelValue":e[28]||(e[28]=o=>a.appWebsite=o),name:"appWebsite",label:l.$trans("config.general.props.app_website"),error:r(p).appWebsite,"onUpdate:error":e[29]||(e[29]=o=>r(p).appWebsite=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})}}});export{q as default};
