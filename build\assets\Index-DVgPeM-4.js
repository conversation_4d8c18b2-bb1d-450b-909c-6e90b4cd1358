import{l as S,r as i,q as _,o as p,w as e,d as $,e as t,u as O,i as z,H as q,m as T,n as x,a as C,b as k,f as l,s as r,t as c,h as G,j as J,y as j,z as K,F as H,v as Q,A as W}from"./app-BAwPsakn.js";import{d as X}from"./vuedraggable.umd-BRYqknf6.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",emits:["hide"],setup(V,{emit:B}){const f=B,b={name:""},u=S({...b});return(v,o)=>{const d=i("BaseInput"),g=i("FilterForm");return p(),_(g,{"init-form":b,form:u,onHide:o[4]||(o[4]=m=>f("hide"))},{default:e(()=>[$("div",Y,[$("div",Z,[t(d,{type:"text",modelValue:u.name,"onUpdate:modelValue":o[0]||(o[0]=m=>u.name=m),name:"name",label:v.$trans("academic.subject.props.name")},null,8,["modelValue","label"])]),$("div",ee,[t(d,{type:"text",modelValue:u.alias,"onUpdate:modelValue":o[1]||(o[1]=m=>u.alias=m),name:"alias",label:v.$trans("academic.subject.props.alias")},null,8,["modelValue","label"])]),$("div",te,[t(d,{type:"text",modelValue:u.code,"onUpdate:modelValue":o[2]||(o[2]=m=>u.code=m),name:"code",label:v.$trans("academic.subject.props.code")},null,8,["modelValue","label"])]),$("div",ae,[t(d,{type:"text",modelValue:u.shortcode,"onUpdate:modelValue":o[3]||(o[3]=m=>u.shortcode=m),name:"shortcode",label:v.$trans("academic.subject.props.shortcode")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ne={key:0},oe={class:"flex border rounded-xl px-4 py-2"},le={key:1},ie={key:2,class:"mt-4 flex justify-end"},re={name:"AcademicSubjectReorder"},ce=Object.assign(re,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(V,{emit:B}){O();const f=z(),b=B,u={subjects:[]};q("academic/subject/");const o=T(!1),d=S({subjects:[]});S({...u});const g=async()=>{o.value=!0,await f.dispatch("academic/subject/list",{params:{all:!0}}).then(a=>{o.value=!1,d.subjects=a}).catch(a=>{o.value=!1})},m=async()=>{o.value=!0,await f.dispatch("academic/subject/reorder",{data:{subjects:d.subjects}}).then(a=>{o.value=!1,b("refresh"),b("close")}).catch(a=>{o.value=!1})},I=()=>{b("close")};return x(()=>{g()}),(a,s)=>{const w=i("BaseLabel"),D=i("BaseAlert"),M=i("BaseButton"),A=i("BaseModal");return p(),_(A,{show:V.visibility,onClose:I},{title:e(()=>[r(c(a.$trans("global.reorder",{attribute:a.$trans("academic.subject.subject")})),1)]),default:e(()=>[d.subjects.length?(p(),C("div",ne,[t(l(X),{class:"space-y-2",list:d.subjects,"item-key":"uuid"},{item:e(({element:R,index:h})=>[$("div",oe,[s[0]||(s[0]=$("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(w,null,{default:e(()=>[r(c(R.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(p(),C("div",le,[t(D,{design:"info",size:"xs"},{default:e(()=>[r(c(a.$trans("general.errors.record_not_found")),1)]),_:1})])),d.subjects.length?(p(),C("div",ie,[t(M,{onClick:m},{default:e(()=>[r(c(a.$trans("general.reorder")),1)]),_:1})])):k("",!0)]),_:1},8,["show"])}}}),ue={name:"AcademicSubjectList"},pe=Object.assign(ue,{emits:["refresh"],setup(V,{emit:B}){const f=G(),b=J("emitter");let u=["filter"];j("academic:config")&&u.push("config"),j("subject:create")&&u.unshift("create");let v=[];j("subject:export")&&(v=["print","pdf","excel"]);const o="academic/subject/",d=T(!1),g=T(!1),m=S({}),I=a=>{Object.assign(m,a)};return(a,s)=>{const w=i("BaseButton"),D=i("PageHeaderAction"),M=i("PageHeader"),A=i("ParentTransition"),R=i("TextMuted"),h=i("DataCell"),F=i("FloatingMenuItem"),L=i("FloatingMenu"),U=i("DataRow"),P=i("DataTable"),N=i("ListItem"),E=K("tooltip");return p(),C(H,null,[t(N,{"init-url":o,onSetItems:I},{header:e(()=>[t(M,{title:a.$trans("academic.subject.subject"),navs:[{label:a.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(D,{url:"academic/subjects/",name:"AcademicSubject",title:a.$trans("academic.subject.subject"),actions:l(u),"dropdown-actions":l(v),"config-path":"AcademicConfigSubjectType",onToggleFilter:s[2]||(s[2]=n=>d.value=!d.value)},{default:e(()=>[W((p(),_(w,{design:"white",onClick:s[0]||(s[0]=n=>g.value=!g.value)},{default:e(()=>s[9]||(s[9]=[$("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[E,a.$trans("global.reorder",{attribute:a.$trans("academic.subject.subject")})]]),l(j)("subject-incharge:read")?(p(),_(w,{key:0,design:"white",onClick:s[1]||(s[1]=n=>l(f).push({name:"AcademicSubjectIncharge"}))},{default:e(()=>[r(c(a.$trans("employee.incharge.incharge")),1)]),_:1})):k("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(A,{appear:"",visibility:d.value},{default:e(()=>[t(se,{onRefresh:s[3]||(s[3]=n=>l(b).emit("listItems")),onHide:s[4]||(s[4]=n=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(A,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:m.headers,meta:m.meta,module:"academic.subject",onRefresh:s[6]||(s[6]=n=>l(b).emit("listItems"))},{actionButton:e(()=>[l(j)("subject:create")?(p(),_(w,{key:0,onClick:s[5]||(s[5]=n=>l(f).push({name:"AcademicSubjectCreate"}))},{default:e(()=>[r(c(a.$trans("global.add",{attribute:a.$trans("academic.subject.subject")})),1)]),_:1})):k("",!0)]),default:e(()=>[(p(!0),C(H,null,Q(m.data,n=>(p(),_(U,{key:n.uuid,onDoubleClick:y=>l(f).push({name:"AcademicSubjectShow",params:{uuid:n.uuid}})},{default:e(()=>[t(h,{name:"name"},{default:e(()=>[r(c(n.name)+" ",1),t(R,{block:""},{default:e(()=>{var y;return[r(c((y=n.type)==null?void 0:y.name),1)]}),_:2},1024)]),_:2},1024),t(h,{name:"alias"},{default:e(()=>[r(c(n.alias),1)]),_:2},1024),t(h,{name:"code"},{default:e(()=>[r(c(n.code),1)]),_:2},1024),t(h,{name:"shortcode"},{default:e(()=>[r(c(n.shortcode),1)]),_:2},1024),t(h,{name:"createdAt"},{default:e(()=>[r(c(n.createdAt.formatted),1)]),_:2},1024),t(h,{name:"action"},{default:e(()=>[t(L,null,{default:e(()=>[t(F,{icon:"fas fa-arrow-circle-right",onClick:y=>l(f).push({name:"AcademicSubjectShow",params:{uuid:n.uuid}})},{default:e(()=>[r(c(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),l(j)("subject:edit")?(p(),_(F,{key:0,icon:"fas fa-edit",onClick:y=>l(f).push({name:"AcademicSubjectEdit",params:{uuid:n.uuid}})},{default:e(()=>[r(c(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),l(j)("subject:create")?(p(),_(F,{key:1,icon:"fas fa-copy",onClick:y=>l(f).push({name:"AcademicSubjectDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[r(c(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),l(j)("subject:delete")?(p(),_(F,{key:2,icon:"fas fa-trash",onClick:y=>l(b).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[r(c(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),t(ce,{visibility:g.value,onClose:s[7]||(s[7]=n=>g.value=!1),onRefresh:s[8]||(s[8]=n=>l(b).emit("listItems"))},null,8,["visibility"])],64)}}});export{pe as default};
