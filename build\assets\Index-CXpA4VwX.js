import{u as A,h as I,i as j,l as w,n as L,r as s,q as p,o as d,w as e,d as F,e as a,s as i,t as l,j as O,m as S,f as B,a as N,F as H,v as U,b as y}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},T={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["refresh","hide"],setup(M,{emit:$}){const r=A(),k=I();j();const u=$,c={name:""},m=w({...c}),_=()=>{Object.assign(m,c),t(),u("hide")};L(async()=>{Object.assign(m,{search:r.query.search})});const t=async()=>{await k.push({name:r.name,query:{...r.query,...m}}),u("refresh")};return(n,b)=>{const v=s("BaseInput"),C=s("BaseButton"),g=s("BaseCard");return d(),p(g,null,{footer:e(()=>[a(C,{design:"error",class:"mr-4",onClick:_},{default:e(()=>[i(l(n.$trans("general.cancel")),1)]),_:1}),a(C,{onClick:t},{default:e(()=>[i(l(n.$trans("general.filter")),1)]),_:1})]),default:e(()=>[F("div",E,[F("div",T,[a(v,{type:"text",modelValue:m.name,"onUpdate:modelValue":b[0]||(b[0]=f=>m.name=f),name:"name",label:n.$trans("config.mail.template.props.name")},null,8,["modelValue","label"])])])]),_:1})}}},G={name:"ConfigMailTemplateList"},K=Object.assign(G,{setup(M){const $=I();j();const r=O("emitter"),k="config/mailTemplate/",u=S(!1),c=w({}),m=t=>{Object.assign(c,t)},_=async t=>{r.emit("actionItem",{uuid:t.uuid,action:"toggleStatus",confirmation:!0})};return(t,n)=>{const b=s("PageHeaderAction"),v=s("ParentTransition"),C=s("BaseBadge"),g=s("DataCell"),f=s("FloatingMenuItem"),R=s("FloatingMenu"),D=s("DataRow"),V=s("DataTable"),P=s("ListItem"),q=s("ConfigPage");return d(),p(q,{"no-background":""},{action:e(()=>[a(b,{name:"ConfigMailTemplate",title:t.$trans("config.mail.template.template"),actions:["filter"],onToggleFilter:n[0]||(n[0]=o=>u.value=!u.value)},null,8,["title"])]),default:e(()=>[a(P,{class:"sm:-mt-4","init-url":k,onSetItems:m},{filter:e(()=>[a(v,{appear:"",visibility:u.value},{default:e(()=>[a(z,{onRefresh:n[1]||(n[1]=o=>B(r).emit("listItems")),onHide:n[2]||(n[2]=o=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(v,{appear:"",visibility:!0},{default:e(()=>[a(V,{header:c.headers,meta:c.meta,module:"config.mail.template",onRefresh:n[3]||(n[3]=o=>B(r).emit("listItems"))},{actionButton:e(()=>n[4]||(n[4]=[])),default:e(()=>[(d(!0),N(H,null,U(c.data,o=>(d(),p(D,{key:o.uuid},{default:e(()=>[a(g,{name:"name"},{default:e(()=>[i(l(o.name)+" ",1),o.enabledAt.value?y("",!0):(d(),p(C,{key:0,design:"danger"},{default:e(()=>[i(l(t.$trans("config.template.statuses.disabled")),1)]),_:1}))]),_:2},1024),a(g,{name:"subject"},{default:e(()=>[i(l(o.subject),1)]),_:2},1024),a(g,{name:"action"},{default:e(()=>[a(R,null,{default:e(()=>[o.enabledAt.value?(d(),p(f,{key:0,icon:"far fa-times-circle",onClick:h=>_(o)},{default:e(()=>[i(l(t.$trans("global.disable",{attribute:t.$trans("config.template.template")})),1)]),_:2},1032,["onClick"])):y("",!0),o.enabledAt.value?y("",!0):(d(),p(f,{key:1,icon:"far fa-check-circle",onClick:h=>_(o)},{default:e(()=>[i(l(t.$trans("global.enable",{attribute:t.$trans("config.template.template")})),1)]),_:2},1032,["onClick"])),a(f,{icon:"fas fa-arrow-circle-right",as:"link",target:"_blank",href:`/app/config/mail-template/${o.uuid}`},{default:e(()=>[i(l(t.$trans("general.show")),1)]),_:2},1032,["href"]),a(f,{icon:"fas fa-edit",onClick:h=>B($).push({name:"ConfigMailTemplateEdit",params:{uuid:o.uuid}})},{default:e(()=>[i(l(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})]),_:1})}}});export{K as default};
