import{u as _,H as h,l as j,r as a,q as f,o as d,w as p,d as i,e as r,f as c,t as m}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-1 gap-6"},C={class:"col-span-1"},V={class:"col-span-1"},B={class:"col-span-1"},N={class:"text-sm"},$={name:"ConfigPushNotificationTemplateForm"},y=Object.assign($,{setup(g){_();const l={subject:"",content:"",variablesDisplay:""},u="config/pushNotificationTemplate/",n=h(u),t=j({...l});return(s,e)=>{const b=a("BaseInput"),v=a("BaseTextarea"),P=a("FormAction");return d(),f(P,{"init-url":u,"init-form":l,form:t,redirect:"ConfigPushNotificationTemplate"},{default:p(()=>[i("div",T,[i("div",C,[r(b,{type:"text",modelValue:t.subject,"onUpdate:modelValue":e[0]||(e[0]=o=>t.subject=o),name:"subject",label:s.$trans("config.push_notification.template.props.subject"),error:c(n).subject,"onUpdate:error":e[1]||(e[1]=o=>c(n).subject=o),autofocus:""},null,8,["modelValue","label","error"])]),i("div",V,[r(v,{modelValue:t.content,"onUpdate:modelValue":e[2]||(e[2]=o=>t.content=o),name:"description",label:s.$trans("config.push_notification.template.props.content"),error:c(n).content,"onUpdate:error":e[3]||(e[3]=o=>c(n).content=o)},null,8,["modelValue","label","error"])]),i("div",B,[i("p",N,m(s.$trans("config.push_notification.template.available_variables"))+": "+m(t.variablesDisplay),1)])])]),_:1},8,["form"])}}}),A={name:"ConfigPushNotificationTemplateAction"},U=Object.assign(A,{setup(g){return _(),(l,u)=>{const n=a("PageHeaderAction"),t=a("ParentTransition"),s=a("ConfigPage");return d(),f(s,{"no-background":""},{action:p(()=>[r(n,{name:"ConfigPushNotificationTemplate",title:l.$trans("config.push_notification.template.template"),actions:["list"]},null,8,["title"])]),default:p(()=>[r(t,{appear:"",visibility:!0},{default:p(()=>[r(y)]),_:1})]),_:1})}}});export{U as default};
