import{i as j,j as S,H as B,c as $,l as _,r as F,q as y,o as D,w as i,e as p,d as r,f as l,s as d,t as a,J as R}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-2 gap-6"},T={class:"col-span-2 sm:col-span-1"},k={class:"col-span-2 sm:col-span-1"},w={class:"pl-2 text-xs"},A={class:"pl-2 text-xs"},C={class:"col-span-2 sm:col-span-1"},E={class:"pl-2 text-xs"},H={class:"pl-2 text-xs"},J={class:"col-span-2 sm:col-span-1"},P={name:"UserPreference"},K=Object.assign(P,{setup(G){const v=j(),V=S("moment"),b="user/profile/",n=B(b),g=$(()=>v.getters["auth/user/preference"]),U="config/",c={dateFormat:"",timeFormat:"",locale:"",timezone:""},m=_({}),t=_({...c}),u=s=>V().format(s),z=()=>{Object.assign(c,g.value.system),Object.assign(t,R(c))},O=s=>{Object.assign(m,s)};return(s,o)=>{const f=F("BaseSelect"),q=F("FormAction"),x=F("ParentTransition");return D(),y(x,{appear:"",visibility:!0},{default:i(()=>[p(q,{"no-card":"","init-url":b,"pre-requisite-url":U,"pre-requisites":{data:["dateFormats","timeFormats","locales","timezones"]},onSetPreRequisites:O,"data-fetch":"user",action:"preference",form:t,"init-form":c,setForm:z,"stay-on":"",redirect:"Dashboard"},{title:i(()=>[d(a(s.$trans("user.preference.preference")),1)]),default:i(()=>[r("div",N,[r("div",T,[p(f,{modelValue:t.locale,"onUpdate:modelValue":o[0]||(o[0]=e=>t.locale=e),name:"locale",label:s.$trans("user.preference.props.locale"),"label-prop":"name","value-prop":"uuid",options:m.locales,error:l(n).locale,"onUpdate:error":o[1]||(o[1]=e=>l(n).locale=e)},null,8,["modelValue","label","options","error"])]),r("div",k,[p(f,{modelValue:t.dateFormat,"onUpdate:modelValue":o[2]||(o[2]=e=>t.dateFormat=e),name:"dateFormat",label:s.$trans("user.preference.props.date_format"),options:m.dateFormats,error:l(n).dateFormat,"onUpdate:error":o[3]||(o[3]=e=>l(n).dateFormat=e)},{selectedOption:i(e=>[d(a(u(e.value.value))+" ",1),r("span",w," ("+a(e.value.label)+")",1)]),listOption:i(e=>[d(a(u(e.option.value))+" ",1),r("span",A," ("+a(e.option.label)+")",1)]),_:1},8,["modelValue","label","options","error"])]),r("div",C,[p(f,{modelValue:t.timeFormat,"onUpdate:modelValue":o[4]||(o[4]=e=>t.timeFormat=e),name:"timeFormat",label:s.$trans("user.preference.props.time_format"),options:m.timeFormats,error:l(n).timeFormat,"onUpdate:error":o[5]||(o[5]=e=>l(n).timeFormat=e)},{selectedOption:i(e=>[d(a(u(e.value.value))+" ",1),r("span",E," ("+a(e.value.label)+")",1)]),listOption:i(e=>[d(a(u(e.option.value))+" ",1),r("span",H," ("+a(e.option.label)+")",1)]),_:1},8,["modelValue","label","options","error"])]),r("div",J,[p(f,{modelValue:t.timezone,"onUpdate:modelValue":o[6]||(o[6]=e=>t.timezone=e),name:"timezone",label:s.$trans("user.preference.props.timezone"),options:m.timezones,error:l(n).timezone,"onUpdate:error":o[7]||(o[7]=e=>l(n).timezone=e)},null,8,["modelValue","label","options","error"])])])]),_:1},8,["form"])]),_:1})}}});export{K as default};
