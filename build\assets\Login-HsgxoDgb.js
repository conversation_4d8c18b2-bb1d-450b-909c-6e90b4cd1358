import{u as A,h as D,i as N,H as S,g as i,l as q,n as H,r as d,z as I,a as w,o as n,e as l,w as o,d as f,b as u,f as a,q as p,s as _,t as k,A as b,F as J}from"./app-BAwPsakn.js";const K={class:"space-y-6"},Q={class:"flex items-center justify-between"},W={key:0,class:"flex justify-center space-x-2"},X={key:1,class:"flex items-center justify-center"},O="auth/user/",ee={__name:"Login",setup(Y){const y=A(),V=D(),v=N(),c=S(O),$=i("auth.enableOauthLogin"),F=i("auth.enableRegistration"),G=i("auth.enableResetPassword"),M=i("auth.enableGoogleOauthLogin"),R=i("auth.enableFacebookOauthLogin"),C=i("auth.enableTwitterOauthLogin"),z=i("auth.enableGithubOauthLogin"),E=i("auth.enableMicrosoftOauthLogin"),h={email:"",password:"",rememberMe:!1},r=q({...h});H(async()=>{await v.dispatch("auth/user/setCSRF")});const T=()=>{Object.assign(r,h),V.push(y.query.ref?y.query.ref:{name:"Dashboard"})};return(t,e)=>{const U=d("GuestHeader"),L=d("BaseInput"),P=d("BaseCheckbox"),B=d("BaseLink"),m=d("BaseButton"),j=d("FormAction"),x=d("ParentTransition"),g=I("tooltip");return n(),w(J,null,[l(U,{label:t.$trans("auth.login.login_title")},null,8,["label"]),l(x,{appear:"",visibility:!0},{default:o(()=>[l(j,{"no-card":"","no-action-button":"","init-url":O,"init-form":h,form:r,action:"login","after-submit":T},{default:o(()=>[f("div",K,[l(L,{type:"text","leading-icon":"fas fa-envelope",modelValue:r.email,"onUpdate:modelValue":e[0]||(e[0]=s=>r.email=s),name:"email",label:t.$trans("auth.login.props.email_or_username"),error:a(c).email,"onUpdate:error":e[1]||(e[1]=s=>a(c).email=s),autofocus:""},null,8,["modelValue","label","error"]),l(L,{type:"password","leading-icon":"fas fa-key",modelValue:r.password,"onUpdate:modelValue":e[2]||(e[2]=s=>r.password=s),name:"password",label:t.$trans("auth.login.props.password"),error:a(c).password,"onUpdate:error":e[3]||(e[3]=s=>a(c).password=s)},null,8,["modelValue","label","error"]),f("div",Q,[l(P,{modelValue:r.rememberMe,"onUpdate:modelValue":e[4]||(e[4]=s=>r.rememberMe=s),name:"rememberMe",label:t.$trans("auth.login.props.remember_me")},null,8,["modelValue","label"]),a(G)?(n(),p(B,{key:0,to:"Password"},{default:o(()=>[_(k(t.$trans("auth.password.forgot_password")),1)]),_:1})):u("",!0)]),a($)?(n(),w("div",W,[a(M)?b((n(),p(m,{key:0,as:"a",href:"/auth/google/redirect",design:"danger",size:"xs"},{default:o(()=>e[5]||(e[5]=[f("i",{class:"fab fa-google"},null,-1)])),_:1})),[[g,t.$trans("config.auth.props.login_with_client",{attribute:"Google"})]]):u("",!0),a(R)?b((n(),p(m,{key:1,as:"a",href:"/auth/facebook/redirect",design:"facebook",size:"xs"},{default:o(()=>e[6]||(e[6]=[f("i",{class:"fab fa-facebook"},null,-1)])),_:1})),[[g,t.$trans("config.auth.props.login_with_client",{attribute:"Facebook"})]]):u("",!0),a(C)?b((n(),p(m,{key:2,as:"a",href:"/auth/twitter/redirect",design:"info",size:"xs"},{default:o(()=>e[7]||(e[7]=[f("i",{class:"fab fa-twitter"},null,-1)])),_:1})),[[g,t.$trans("config.auth.props.login_with_client",{attribute:"Twitter"})]]):u("",!0),a(z)?b((n(),p(m,{key:3,as:"a",href:"/auth/github/redirect",size:"xs"},{default:o(()=>e[8]||(e[8]=[f("i",{class:"fab fa-github"},null,-1)])),_:1})),[[g,t.$trans("config.auth.props.login_with_client",{attribute:"Github"})]]):u("",!0),a(E)?b((n(),p(m,{key:4,as:"a",href:"/auth/microsoft/redirect",size:"xs"},{default:o(()=>e[9]||(e[9]=[f("i",{class:"fab fa-microsoft"},null,-1)])),_:1})),[[g,t.$trans("config.auth.props.login_with_client",{attribute:"Microsoft"})]]):u("",!0)])):u("",!0),a(F)?(n(),w("div",X,[l(B,{to:"Register"},{default:o(()=>[_(k(t.$trans("auth.register.register_title")),1)]),_:1})])):u("",!0),l(m,{type:"submit",block:""},{default:o(()=>[_(k(t.$trans("auth.login.login")),1)]),_:1})])]),_:1},8,["form"])]),_:1})],64)}}};export{ee as default};
