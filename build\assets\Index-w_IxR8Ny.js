import{u as j,l as w,n as O,r as l,q as f,o as c,w as e,d as V,e as a,h as U,j as E,y as g,m as R,f as o,a as q,F as x,v as z,s,t as i,b as D}from"./app-BAwPsakn.js";import{_ as G}from"./ModuleDropdown-sYr-p8uC.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(B,{emit:m}){j();const h=m,k={startDate:"",endDate:""},u=w({...k}),b=w({isLoaded:!0});return O(async()=>{b.isLoaded=!0}),(_,d)=>{const C=l("DatePicker"),r=l("FilterForm");return c(),f(r,{"init-form":k,form:u,multiple:[],onHide:d[2]||(d[2]=n=>h("hide"))},{default:e(()=>[V("div",J,[V("div",K,[a(C,{start:u.startDate,"onUpdate:start":d[0]||(d[0]=n=>u.startDate=n),end:u.endDate,"onUpdate:end":d[1]||(d[1]=n=>u.endDate=n),name:"dateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={name:"TransportVehicleServiceRecordList"},Z=Object.assign(W,{setup(B){const m=U(),h=E("emitter");let k=["filter"];g("vehicle-service-record:create")&&k.unshift("create");let u=[];g("vehicle-service-record:export")&&(u=["print","pdf","excel"]);const b="transport/vehicle/serviceRecord/",_=R(!1),d=w({}),C=r=>{Object.assign(d,r)};return(r,n)=>{const I=l("PageHeaderAction"),P=l("PageHeader"),F=l("ParentTransition"),T=l("TextMuted"),v=l("DataCell"),$=l("FloatingMenuItem"),L=l("FloatingMenu"),M=l("DataRow"),A=l("BaseButton"),H=l("DataTable"),N=l("ListItem");return c(),f(N,{"init-url":b,onSetItems:C},{header:e(()=>[a(P,{title:r.$trans("transport.vehicle.service_record.service_record"),navs:[{label:r.$trans("transport.transport"),path:"Transport"},{label:r.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"}]},{default:e(()=>[a(I,{url:"transport/vehicle/service-records/",name:"TransportVehicleServiceRecord",title:r.$trans("transport.vehicle.service_record.service_record"),actions:o(k),"dropdown-actions":o(u),onToggleFilter:n[0]||(n[0]=t=>_.value=!_.value)},{moduleOption:e(()=>[a(G)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(F,{appear:"",visibility:_.value},{default:e(()=>[a(Q,{onRefresh:n[1]||(n[1]=t=>o(h).emit("listItems")),onHide:n[2]||(n[2]=t=>_.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a(H,{header:d.headers,meta:d.meta,module:"transport.vehicle.service_record",onRefresh:n[4]||(n[4]=t=>o(h).emit("listItems"))},{actionButton:e(()=>[o(g)("vehicle-service-record:create")?(c(),f(A,{key:0,onClick:n[3]||(n[3]=t=>o(m).push({name:"TransportVehicleServiceRecordCreate"}))},{default:e(()=>[s(i(r.$trans("global.add",{attribute:r.$trans("transport.vehicle.service_record.service_record")})),1)]),_:1})):D("",!0)]),default:e(()=>[(c(!0),q(x,null,z(d.data,t=>(c(),f(M,{key:t.uuid,onDoubleClick:p=>o(m).push({name:"TransportVehicleServiceRecordShow",params:{uuid:t.uuid}})},{default:e(()=>[a(v,{name:"vehicle"},{default:e(()=>{var p;return[s(i((p=t.vehicle)==null?void 0:p.name)+" ",1),a(T,{block:""},{default:e(()=>{var y;return[s(i((y=t.vehicle)==null?void 0:y.registrationNumber),1)]}),_:2},1024)]}),_:2},1024),a(v,{name:"date"},{default:e(()=>[s(i(t.date.formatted)+" ",1),a(T,{block:""},{default:e(()=>[s(i(t.nextDueDate.formatted),1)]),_:2},1024)]),_:2},1024),a(v,{name:"log"},{default:e(()=>[s(i(t.log)+" ",1),a(T,{block:""},{default:e(()=>[s(i(t.nextDueLog),1)]),_:2},1024)]),_:2},1024),a(v,{name:"amount"},{default:e(()=>[s(i(t.amount.formatted),1)]),_:2},1024),a(v,{name:"createdAt"},{default:e(()=>[s(i(t.createdAt.formatted),1)]),_:2},1024),a(v,{name:"action"},{default:e(()=>[a(L,null,{default:e(()=>[a($,{icon:"fas fa-arrow-circle-right",onClick:p=>o(m).push({name:"TransportVehicleServiceRecordShow",params:{uuid:t.uuid}})},{default:e(()=>[s(i(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(g)("vehicle-service-record:edit")?(c(),f($,{key:0,icon:"fas fa-edit",onClick:p=>o(m).push({name:"TransportVehicleServiceRecordEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(i(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):D("",!0),o(g)("vehicle-service-record:create")?(c(),f($,{key:1,icon:"fas fa-copy",onClick:p=>o(m).push({name:"TransportVehicleServiceRecordDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[s(i(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):D("",!0),o(g)("vehicle-service-record:delete")?(c(),f($,{key:2,icon:"fas fa-trash",onClick:p=>o(h).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(i(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Z as default};
