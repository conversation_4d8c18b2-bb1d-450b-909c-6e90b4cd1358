import{u as U,j,H as B,l as _,n as $,J as k,r as e,a as P,o as n,q as l,b as i,e as d,f as u,w as m,d as p,F}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-1"},w={class:"col-span-1"},E={name:"ContactEditPhoto"},I=Object.assign(E,{props:{contact:{type:Object,default(){return{}}}},setup(t){const c=U(),s=j("emitter"),h=t,o={photo:""};B("contact/");const r=_({...o}),b=async()=>{s.emit("contactUpdated")},g=async()=>{s.emit("contactUpdated")};return $(async()=>{Object.assign(o,{photo:h.contact.photo}),Object.assign(r,k(o))}),(a,N)=>{const v=e("PageHeader"),f=e("ImageUpload"),C=e("BaseCard"),y=e("ParentTransition");return n(),P(F,null,[t.contact.uuid?(n(),l(v,{key:0,title:a.$trans(u(c).meta.trans,{attribute:a.$trans(u(c).meta.label)}),navs:[{label:a.$trans("contact.contact"),path:"Contact"},{label:t.contact.name,path:{name:"ContactShow",params:{uuid:t.contact.uuid}}}]},null,8,["title","navs"])):i("",!0),d(y,{appear:"",visibility:!0},{default:m(()=>[t.contact.uuid?(n(),l(C,{key:0},{default:m(()=>[p("div",O,[p("div",w,[d(f,{label:a.$trans("contact.props.photo"),src:r.photo,"upload-path":`contacts/${t.contact.uuid}/photo`,"remove-path":`contacts/${t.contact.uuid}/photo`,onUploaded:b,onRemoved:g},null,8,["label","src","upload-path","remove-path"])])])]),_:1})):i("",!0)]),_:1})],64)}}});export{I as default};
