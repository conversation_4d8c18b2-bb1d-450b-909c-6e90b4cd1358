import{u as E,l as j,n as O,r as l,q as b,o as s,w as e,d as h,e as n,b as C,h as z,j as G,y as k,m as J,f as u,a as $,F as D,v as P,s as a,t as o}from"./app-BAwPsakn.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={__name:"Filter",emits:["hide"],setup(S,{emit:v}){const g=E(),B=v,N={codeNumber:"",subject:"",types:[],startDate:"",endDate:""},d=j({...N}),y=j({types:[],isLoaded:!g.query.types});return O(async()=>{y.types=g.query.types?g.query.types.split(","):[],y.isLoaded=!0}),(f,r)=>{const i=l("BaseInput"),c=l("BaseSelectSearch"),F=l("DatePicker"),V=l("FilterForm");return s(),b(V,{"init-form":N,form:d,multiple:["types"],onHide:r[5]||(r[5]=p=>B("hide"))},{default:e(()=>[h("div",K,[h("div",Q,[n(i,{type:"text",modelValue:d.codeNumber,"onUpdate:modelValue":r[0]||(r[0]=p=>d.codeNumber=p),name:"codeNumber",label:f.$trans("reception.complaint.props.code_number")},null,8,["modelValue","label"])]),h("div",W,[n(i,{type:"text",modelValue:d.subject,"onUpdate:modelValue":r[1]||(r[1]=p=>d.subject=p),name:"subject",label:f.$trans("reception.complaint.props.subject")},null,8,["modelValue","label"])]),h("div",X,[y.isLoaded?(s(),b(c,{key:0,multiple:"",name:"types",label:f.$trans("global.select",{attribute:f.$trans("reception.complaint.type.type")}),modelValue:d.types,"onUpdate:modelValue":r[2]||(r[2]=p=>d.types=p),"value-prop":"uuid","init-search":y.types,"search-action":"option/list","additional-search-query":{type:"complaint_type"}},null,8,["label","modelValue","init-search"])):C("",!0)]),h("div",Y,[n(F,{start:d.startDate,"onUpdate:start":r[3]||(r[3]=p=>d.startDate=p),end:d.endDate,"onUpdate:end":r[4]||(r[4]=p=>d.endDate=p),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},x={name:"ReceptionComplaintList"},te=Object.assign(x,{setup(S){const v=z(),g=G("emitter");let B=["filter"];k("reception:config")&&B.push("config"),k("complaint:create")&&B.unshift("create");let N=[];k("complaint:export")&&(N=["print","pdf","excel"]);const d="reception/complaint/",y=J(!1),f=j({}),r=i=>{Object.assign(f,i)};return(i,c)=>{const F=l("PageHeaderAction"),V=l("PageHeader"),p=l("ParentTransition"),I=l("BaseBadge"),_=l("DataCell"),R=l("TextMuted"),w=l("FloatingMenuItem"),L=l("FloatingMenu"),M=l("DataRow"),q=l("BaseButton"),H=l("DataTable"),U=l("ListItem");return s(),b(U,{"init-url":d,"additional-query":{},onSetItems:r},{header:e(()=>[n(V,{title:i.$trans("reception.complaint.complaint"),navs:[{label:i.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[n(F,{url:"reception/complaints/",name:"ReceptionComplaint",title:i.$trans("reception.complaint.complaint"),actions:u(B),"dropdown-actions":u(N),"config-path":"ReceptionConfig",onToggleFilter:c[0]||(c[0]=t=>y.value=!y.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(p,{appear:"",visibility:y.value},{default:e(()=>[n(Z,{onRefresh:c[1]||(c[1]=t=>u(g).emit("listItems")),onHide:c[2]||(c[2]=t=>y.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(p,{appear:"",visibility:!0},{default:e(()=>[n(H,{header:f.headers,meta:f.meta,module:"reception.complaint",onRefresh:c[4]||(c[4]=t=>u(g).emit("listItems"))},{actionButton:e(()=>[u(k)("complaint:create")?(s(),b(q,{key:0,onClick:c[3]||(c[3]=t=>u(v).push({name:"ReceptionComplaintCreate"}))},{default:e(()=>[a(o(i.$trans("global.add",{attribute:i.$trans("reception.complaint.complaint")})),1)]),_:1})):C("",!0)]),default:e(()=>[(s(!0),$(D,null,P(f.data,t=>(s(),b(M,{key:t.uuid,onDoubleClick:m=>u(v).push({name:"ReceptionComplaintShow",params:{uuid:t.uuid}})},{default:e(()=>[n(_,{name:"codeNumber"},{default:e(()=>[a(o(t.codeNumber)+" ",1),t.isOnline?(s(),b(I,{key:0,design:"info"},{default:e(()=>[a(o(i.$trans("reception.complaint.online")),1)]),_:1})):C("",!0),n(I,{design:t.status.color},{default:e(()=>[a(o(t.status.label),1)]),_:2},1032,["design"])]),_:2},1024),n(_,{name:"name"},{default:e(()=>[t.student?(s(),$(D,{key:0},[a(o(t.student.name)+" ",1),n(R,{block:""},{default:e(()=>[a(o(t.student.contactNumber),1)]),_:2},1024)],64)):(s(),$(D,{key:1},[a("-")],64))]),_:2},1024),n(_,{name:"course"},{default:e(()=>[t.student?(s(),$(D,{key:0},[a(o(t.student.courseName)+" ",1),n(R,{block:""},{default:e(()=>[a(o(t.student.batchName),1)]),_:2},1024)],64)):(s(),$(D,{key:1},[a("-")],64))]),_:2},1024),n(_,{name:"type"},{default:e(()=>{var m;return[a(o(((m=t.type)==null?void 0:m.name)||"-"),1)]}),_:2},1024),n(_,{name:"subject"},{default:e(()=>[a(o(t.subjectExcerpt),1)]),_:2},1024),n(_,{name:"complainant"},{default:e(()=>[a(o(t.complainantName)+" ",1),n(R,{block:""},{default:e(()=>[a(o(t.complainantContactNumber),1)]),_:2},1024)]),_:2},1024),n(_,{name:"date"},{default:e(()=>[a(o(t.date.formatted)+" ",1),t.resolvedAt.value?(s(),b(R,{key:0,block:""},{default:e(()=>[a(o(t.resolvedAt.formatted),1)]),_:2},1024)):C("",!0)]),_:2},1024),n(_,{name:"assignedTo"},{default:e(()=>[(s(!0),$(D,null,P(t.incharges,m=>{var T;return s(),$("div",null,[a(o(((T=m==null?void 0:m.employee)==null?void 0:T.name)||"-")+" ",1),n(R,null,{default:e(()=>{var A;return[a(o((A=m.employee)==null?void 0:A.codeNumber),1)]}),_:2},1024)])}),256))]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[a(o(t.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(L,null,{default:e(()=>[n(w,{icon:"fas fa-arrow-circle-right",onClick:m=>u(v).push({name:"ReceptionComplaintShow",params:{uuid:t.uuid}})},{default:e(()=>[a(o(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),u(k)("complaint:edit")&&t.isEditable?(s(),b(w,{key:0,icon:"fas fa-edit",onClick:m=>u(v).push({name:"ReceptionComplaintEdit",params:{uuid:t.uuid}})},{default:e(()=>[a(o(i.$trans("general.edit")),1)]),_:2},1032,["onClick"])):C("",!0),u(k)("complaint:create")?(s(),b(w,{key:1,icon:"fas fa-copy",onClick:m=>u(v).push({name:"ReceptionComplaintDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[a(o(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):C("",!0),u(k)("complaint:delete")&&t.isEditable?(s(),b(w,{key:2,icon:"fas fa-trash",onClick:m=>u(g).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[a(o(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])):C("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{te as default};
