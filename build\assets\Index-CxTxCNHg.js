import{l as V,r as n,q as p,o as r,w as e,d as w,e as a,u as M,h as N,j as U,y as B,m as O,f as i,a as I,F as S,v as q,s,b as _,t as u}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(o,{emit:h}){const d=h,c={title:""},$=V({...c});return(D,f)=>{const y=n("BaseInput"),v=n("FilterForm");return r(),p(v,{"init-form":c,form:$,onHide:f[1]||(f[1]=k=>d("hide"))},{default:e(()=>[w("div",z,[w("div",G,[a(y,{type:"text",modelValue:$.title,"onUpdate:modelValue":f[0]||(f[0]=k=>$.title=k),name:"title",label:D.$trans("employee.document.props.title")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={name:"EmployeeDocumentList"},W=Object.assign(K,{props:{employee:{type:Object,default(){return{}}}},setup(o){const h=M(),d=N(),c=U("emitter"),$=o;let D=["filter"];(B("employee:edit")||$.employee.selfService)&&D.unshift("create");const f="employee/document/",y=O(!1),v=V({}),k=l=>{Object.assign(v,l)};return(l,m)=>{const H=n("PageHeaderAction"),P=n("PageHeader"),F=n("ParentTransition"),E=n("BaseBadge"),g=n("DataCell"),b=n("FloatingMenuItem"),R=n("FloatingMenu"),T=n("DataRow"),j=n("BaseButton"),A=n("DataTable"),L=n("ListItem");return r(),p(L,{"init-url":f,uuid:i(h).params.uuid,onSetItems:k},{header:e(()=>[o.employee.uuid?(r(),p(P,{key:0,title:l.$trans("employee.document.document"),navs:[{label:l.$trans("employee.employee"),path:"Employee"},{label:o.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:o.employee.uuid}}}]},{default:e(()=>[a(H,{url:`employees/${o.employee.uuid}/documents/`,name:"EmployeeDocument",title:l.$trans("employee.document.document"),actions:i(D),"dropdown-actions":["print","pdf","excel"],onToggleFilter:m[0]||(m[0]=t=>y.value=!y.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):_("",!0)]),filter:e(()=>[a(F,{appear:"",visibility:y.value},{default:e(()=>[a(J,{onRefresh:m[1]||(m[1]=t=>i(c).emit("listItems")),onHide:m[2]||(m[2]=t=>y.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a(A,{header:v.headers,meta:v.meta,module:"employee.document",onRefresh:m[4]||(m[4]=t=>i(c).emit("listItems"))},{actionButton:e(()=>[i(B)("employee:edit")||o.employee.selfService?(r(),p(j,{key:0,onClick:m[3]||(m[3]=t=>i(d).push({name:"EmployeeDocumentCreate"}))},{default:e(()=>[s(u(l.$trans("global.add",{attribute:l.$trans("employee.document.document")})),1)]),_:1})):_("",!0)]),default:e(()=>[(r(!0),I(S,null,q(v.data,t=>(r(),p(T,{key:t.uuid,onDoubleClick:C=>i(d).push({name:"EmployeeDocumentShow",params:{uuid:t.uuid}})},{default:e(()=>[a(g,{name:"title"},{default:e(()=>[s(u(t.title)+" ",1),t.selfUpload?(r(),p(E,{key:0,design:t.verificationStatus.color},{default:e(()=>[s(u(t.verificationStatus.label),1)]),_:2},1032,["design"])):_("",!0)]),_:2},1024),a(g,{name:"type"},{default:e(()=>[s(u(t.type.name),1)]),_:2},1024),a(g,{name:"startDate"},{default:e(()=>[s(u(t.startDate.formatted),1)]),_:2},1024),a(g,{name:"endDate"},{default:e(()=>[s(u(t.endDate.formatted)+" ",1),t.isExpired?(r(),p(E,{key:0,design:"danger"},{default:e(()=>[s(u(l.$trans("employee.document.expired")),1)]),_:1})):_("",!0)]),_:2},1024),a(g,{name:"createdAt"},{default:e(()=>[s(u(t.createdAt.formatted),1)]),_:2},1024),a(g,{name:"action"},{default:e(()=>[a(R,null,{default:e(()=>[a(b,{icon:"fas fa-arrow-circle-right",onClick:C=>i(d).push({name:"EmployeeDocumentShow",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[s(u(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(B)("employee:edit")||o.employee.selfService?(r(),I(S,{key:0},[a(b,{icon:"fas fa-edit",onClick:C=>i(d).push({name:"EmployeeDocumentEdit",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[s(u(l.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(b,{icon:"fas fa-copy",onClick:C=>i(d).push({name:"EmployeeDocumentDuplicate",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[s(u(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(b,{icon:"fas fa-trash",onClick:C=>i(c).emit("deleteItem",{uuid:o.employee.uuid,moduleUuid:t.uuid})},{default:e(()=>[s(u(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):_("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{W as default};
