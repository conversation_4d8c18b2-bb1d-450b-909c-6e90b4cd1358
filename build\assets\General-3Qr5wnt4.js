import{u as D,j as B,H as F,c as O,l as N,r as m,a as x,o as g,e as i,f as r,w as u,d as l,b as G,F as V,s as j,t as q}from"./app-BAwPsakn.js";const w={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3"},K={class:"col-span-3"},L={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3"},X={name:"FinanceConfigGeneral"},h=Object.assign(X,{setup(Y){const y=D(),o=B("$trans"),p="config/",a=F(p),P=O(()=>o("global.placeholder_info",{attribute:f.datePlaceholders})),f=N({datePlaceholders:""}),b={paymentNumberPrefix:"",paymentNumberDigit:"",paymentNumberSuffix:"",receiptNumberPrefix:"",receiptNumberDigit:"",receiptNumberSuffix:"",transferNumberPrefix:"",transferNumberDigit:"",transferNumberSuffix:"",enableOnlineTransactionNumber:!1,onlineTransactionNumberPrefix:"",onlineTransactionNumberSuffix:"",onlineTransactionNumberDigit:0,type:"finance"},t=N({...b}),U=d=>{Object.assign(f,{datePlaceholders:d.datePlaceholders.map(e=>e.value).join(", ")})};return(d,e)=>{const _=m("PageHeader"),s=m("BaseInput"),c=m("BaseSwitch"),S=m("BaseAlert"),T=m("FormAction"),v=m("ParentTransition");return g(),x(V,null,[i(_,{title:r(o)(r(y).meta.label),navs:[{label:r(o)("finance.finance"),path:"Finance"}]},null,8,["title","navs"]),i(v,{appear:"",visibility:!0},{default:u(()=>[i(T,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:U,"init-url":p,"data-fetch":"finance","init-form":b,form:t,action:"store","stay-on":"",redirect:"Finance"},{default:u(()=>[l("div",w,[l("div",A,[i(s,{type:"text",modelValue:t.paymentNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=n=>t.paymentNumberPrefix=n),name:"paymentNumberPrefix",label:r(o)("finance.transaction.config.props.payment_number_prefix"),error:r(a).paymentNumberPrefix,"onUpdate:error":e[1]||(e[1]=n=>r(a).paymentNumberPrefix=n)},null,8,["modelValue","label","error"])]),l("div",C,[i(s,{type:"number",modelValue:t.paymentNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=n=>t.paymentNumberDigit=n),name:"paymentNumberDigit",label:r(o)("finance.transaction.config.props.payment_number_digit"),error:r(a).paymentNumberDigit,"onUpdate:error":e[3]||(e[3]=n=>r(a).paymentNumberDigit=n)},null,8,["modelValue","label","error"])]),l("div",R,[i(s,{type:"text",modelValue:t.paymentNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=n=>t.paymentNumberSuffix=n),name:"paymentNumberSuffix",label:r(o)("finance.transaction.config.props.payment_number_suffix"),error:r(a).paymentNumberSuffix,"onUpdate:error":e[5]||(e[5]=n=>r(a).paymentNumberSuffix=n)},null,8,["modelValue","label","error"])]),l("div",k,[i(s,{type:"text",modelValue:t.receiptNumberPrefix,"onUpdate:modelValue":e[6]||(e[6]=n=>t.receiptNumberPrefix=n),name:"receiptNumberPrefix",label:r(o)("finance.transaction.config.props.receipt_number_prefix"),error:r(a).receiptNumberPrefix,"onUpdate:error":e[7]||(e[7]=n=>r(a).receiptNumberPrefix=n)},null,8,["modelValue","label","error"])]),l("div",E,[i(s,{type:"number",modelValue:t.receiptNumberDigit,"onUpdate:modelValue":e[8]||(e[8]=n=>t.receiptNumberDigit=n),name:"receiptNumberDigit",label:r(o)("finance.transaction.config.props.receipt_number_digit"),error:r(a).receiptNumberDigit,"onUpdate:error":e[9]||(e[9]=n=>r(a).receiptNumberDigit=n)},null,8,["modelValue","label","error"])]),l("div",H,[i(s,{type:"text",modelValue:t.receiptNumberSuffix,"onUpdate:modelValue":e[10]||(e[10]=n=>t.receiptNumberSuffix=n),name:"receiptNumberSuffix",label:r(o)("finance.transaction.config.props.receipt_number_suffix"),error:r(a).receiptNumberSuffix,"onUpdate:error":e[11]||(e[11]=n=>r(a).receiptNumberSuffix=n)},null,8,["modelValue","label","error"])]),l("div",I,[i(s,{type:"text",modelValue:t.transferNumberPrefix,"onUpdate:modelValue":e[12]||(e[12]=n=>t.transferNumberPrefix=n),name:"transferNumberPrefix",label:r(o)("finance.transaction.config.props.transfer_number_prefix"),error:r(a).transferNumberPrefix,"onUpdate:error":e[13]||(e[13]=n=>r(a).transferNumberPrefix=n)},null,8,["modelValue","label","error"])]),l("div",$,[i(s,{type:"number",modelValue:t.transferNumberDigit,"onUpdate:modelValue":e[14]||(e[14]=n=>t.transferNumberDigit=n),name:"transferNumberDigit",label:r(o)("finance.transaction.config.props.transfer_number_digit"),error:r(a).transferNumberDigit,"onUpdate:error":e[15]||(e[15]=n=>r(a).transferNumberDigit=n)},null,8,["modelValue","label","error"])]),l("div",z,[i(s,{type:"text",modelValue:t.transferNumberSuffix,"onUpdate:modelValue":e[16]||(e[16]=n=>t.transferNumberSuffix=n),name:"transferNumberSuffix",label:r(o)("finance.transaction.config.props.transfer_number_suffix"),error:r(a).transferNumberSuffix,"onUpdate:error":e[17]||(e[17]=n=>r(a).transferNumberSuffix=n)},null,8,["modelValue","label","error"])]),l("div",J,[i(c,{vertical:"",modelValue:t.enableGuestPayment,"onUpdate:modelValue":e[18]||(e[18]=n=>t.enableGuestPayment=n),name:"enableGuestPayment",label:r(o)("global.enable",{attribute:r(o)("finance.transaction.config.props.guest_payment")}),error:r(a).enableGuestPayment,"onUpdate:error":e[19]||(e[19]=n=>r(a).enableGuestPayment=n)},null,8,["modelValue","label","error"])]),l("div",K,[i(c,{vertical:"",modelValue:t.enableOnlineTransactionNumber,"onUpdate:modelValue":e[20]||(e[20]=n=>t.enableOnlineTransactionNumber=n),name:"enableOnlineTransactionNumber",label:r(o)("global.enable",{attribute:r(o)("finance.transaction.config.props.online_transaction_number")}),error:r(a).enableOnlineTransactionNumber,"onUpdate:error":e[21]||(e[21]=n=>r(a).enableOnlineTransactionNumber=n)},null,8,["modelValue","label","error"])]),t.enableOnlineTransactionNumber?(g(),x(V,{key:0},[l("div",L,[i(s,{type:"text",modelValue:t.onlineTransactionNumberPrefix,"onUpdate:modelValue":e[22]||(e[22]=n=>t.onlineTransactionNumberPrefix=n),name:"onlineTransactionNumberPrefix",label:r(o)("finance.transaction.config.props.online_number_prefix"),error:r(a).onlineTransactionNumberPrefix,"onUpdate:error":e[23]||(e[23]=n=>r(a).onlineTransactionNumberPrefix=n)},null,8,["modelValue","label","error"])]),l("div",M,[i(s,{type:"number",modelValue:t.onlineTransactionNumberDigit,"onUpdate:modelValue":e[24]||(e[24]=n=>t.onlineTransactionNumberDigit=n),name:"onlineTransactionNumberDigit",label:r(o)("finance.transaction.config.props.online_number_digit"),error:r(a).onlineTransactionNumberDigit,"onUpdate:error":e[25]||(e[25]=n=>r(a).onlineTransactionNumberDigit=n)},null,8,["modelValue","label","error"])]),l("div",Q,[i(s,{type:"text",modelValue:t.onlineTransactionNumberSuffix,"onUpdate:modelValue":e[26]||(e[26]=n=>t.onlineTransactionNumberSuffix=n),name:"onlineTransactionNumberSuffix",label:r(o)("finance.transaction.config.props.online_number_suffix"),error:r(a).onlineTransactionNumberSuffix,"onUpdate:error":e[27]||(e[27]=n=>r(a).onlineTransactionNumberSuffix=n)},null,8,["modelValue","label","error"])])],64)):G("",!0),l("div",W,[i(S,{size:"xs",design:"info"},{default:u(()=>[j(q(P.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{h as default};
