import{i as R,H as U,m as G,l as _,K as J,n as K,r as b,q as d,o as m,w as l,a as N,b as p,F,v as D,s,t as c,f as t,y as O,J as M,u as z,j as P,g,e as u,d as x}from"./app-BAwPsakn.js";import{_ as Q}from"./EditRequestInfo-CdeRga5q.js";const W={key:0,class:"space-x-1"},X={name:"EmployeeTagList"},Y=Object.assign(X,{props:{employee:{type:Object,default(){return{}}}},emits:["refresh"],setup(e,{emit:C}){R();const n=C,k=e,y={tags:[]},q="employee/",I=U(q),f=G(!1),h=_({...y}),B=_({tags:[],isLoaded:!1}),L=r=>{Object.assign(y,{tags:r.map(i=>i.uuid)}),Object.assign(h,M(y)),B.tags=r.map(i=>i.uuid),B.isLoaded=!0},E=()=>{f.value=!1,n("refresh")};return J(()=>k.employee.tags,r=>{L(r)}),K(()=>{var r;L(((r=k.employee)==null?void 0:r.tags)||[])}),(r,i)=>{const $=b("BaseBadge"),j=b("BaseSelectSearch"),V=b("FormAction"),w=b("BaseDataView");return m(),d(w,{class:"col-span-1 sm:col-span-4"},{label:l(()=>[s(c(r.$trans("general.tags"))+" ",1),t(O)("employee:edit")?(m(),N("i",{key:0,class:"fas fa-edit cursor-pointer",onClick:i[0]||(i[0]=a=>f.value=!0)})):p("",!0)]),default:l(()=>[f.value?p("",!0):(m(),N("div",W,[(m(!0),N(F,null,D(e.employee.tags||[],a=>(m(),d($,{design:"primary"},{default:l(()=>[s(c(a.name),1)]),_:2},1024))),256))])),f.value?(m(),d(V,{key:1,"no-card":"","no-data-fetch":"","cancel-action":"",action:"updateTags","keep-adding":!1,"init-url":q,"init-form":y,form:h,"after-submit":E,onCancelled:i[3]||(i[3]=a=>f.value=!1)},{default:l(()=>[B.isLoaded?(m(),d(j,{key:0,tags:"",name:"tags",placeholder:r.$trans("global.select",{attribute:r.$trans("general.tag")}),modelValue:h.tags,"onUpdate:modelValue":i[1]||(i[1]=a=>h.tags=a),error:t(I).tags,"onUpdate:error":i[2]||(i[2]=a=>t(I).tags=a),"init-search":B.tags,"search-action":"tag/list"},null,8,["placeholder","modelValue","error","init-search"])):p("",!0)]),_:1},8,["form"])):p("",!0)]),_:1})}}}),Z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},ee={class:"flex flex-wrap gap-2"},ae={name:"EmployeeShowBasic"},oe=Object.assign(ae,{props:{employee:{type:Object,default(){return{}}}},setup(e){const C=z(),n=P("$trans"),k=P("emitter"),y=e,q=g("employee.uniqueIdNumber1Label"),I=g("employee.uniqueIdNumber2Label"),f=g("employee.uniqueIdNumber3Label"),h=g("employee.uniqueIdNumber4Label"),B=g("employee.uniqueIdNumber5Label"),L=g("contact.enableCategoryField"),E=g("contact.enableCasteField");let r=[];O("employee:edit")&&!y.employee.self&&(r.push({label:n("global.edit",{attribute:n("employee.employee")}),path:{name:"EmployeeEditBasic",params:{uuid:y.employee.uuid}}}),r.push({label:n("global.edit",{attribute:n("contact.props.photo")}),path:{name:"EmployeeEditPhoto",params:{uuid:y.employee.uuid}}}));const i=()=>{k.emit("employeeUpdated")};return($,j)=>{const V=b("PageHeaderAction"),w=b("PageHeader"),a=b("BaseDataView"),T=b("BaseBadge"),A=b("BaseCard"),H=b("ParentTransition");return m(),N(F,null,[e.employee.uuid?(m(),d(w,{key:0,title:t(n)(t(C).meta.label),navs:[{label:t(n)("employee.employee"),path:"Employee"},{label:e.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:e.employee.uuid}}}]},{default:l(()=>[u(V,{"additional-actions":t(r)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):p("",!0),u(H,{appear:"",visibility:!0},{default:l(()=>[e.employee.uuid?(m(),d(A,{key:0},{default:l(()=>{var S;return[u(Q,{employee:e.employee},null,8,["employee"]),x("dl",Z,[u(a,{label:t(n)("employee.props.code_number")},{default:l(()=>[s(c(e.employee.codeNumber),1)]),_:1},8,["label"]),u(a,{label:t(n)("employee.props.joining_date")},{default:l(()=>[s(c(e.employee.joiningDate.formatted),1)]),_:1},8,["label"]),e.employee.leavingDate.value?(m(),d(a,{key:0,label:t(n)("employee.props.leaving_date")},{default:l(()=>[s(c(e.employee.leavingDate.formatted),1)]),_:1},8,["label"])):p("",!0),u(a,{label:t(n)("employee.department.department")},{default:l(()=>{var o,v;return[s(c((v=(o=e.employee.lastRecord)==null?void 0:o.department)==null?void 0:v.name),1)]}),_:1},8,["label"]),u(a,{label:t(n)("employee.designation.designation")},{default:l(()=>{var o,v;return[s(c((v=(o=e.employee.lastRecord)==null?void 0:o.designation)==null?void 0:v.name),1)]}),_:1},8,["label"]),u(a,{label:t(n)("contact.props.birth_date")},{default:l(()=>[s(c(e.employee.contact.birthDate.formatted),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.gender")},{default:l(()=>[s(c(e.employee.contact.gender.label),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.father_name")},{default:l(()=>[s(c(e.employee.contact.fatherName),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.mother_name")},{default:l(()=>[s(c(e.employee.contact.motherName),1)]),_:1},8,["label"]),u(a,{label:t(q)},{default:l(()=>[s(c(e.employee.contact.uniqueIdNumber1),1)]),_:1},8,["label"]),u(a,{label:t(I)},{default:l(()=>[s(c(e.employee.contact.uniqueIdNumber2),1)]),_:1},8,["label"]),u(a,{label:t(f)},{default:l(()=>[s(c(e.employee.contact.uniqueIdNumber3),1)]),_:1},8,["label"]),u(a,{label:t(h)},{default:l(()=>[s(c(e.employee.contact.uniqueIdNumber4),1)]),_:1},8,["label"]),u(a,{label:t(B)},{default:l(()=>[s(c(e.employee.contact.uniqueIdNumber5),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.birth_place")},{default:l(()=>[s(c(e.employee.contact.birthPlace),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.nationality")},{default:l(()=>[s(c(e.employee.contact.nationality),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.mother_tongue")},{default:l(()=>[s(c(e.employee.contact.motherTongue),1)]),_:1},8,["label"]),u(a,{label:t(n)("contact.props.blood_group")},{default:l(()=>{var o;return[s(c(((o=e.employee.contact.bloodGroup)==null?void 0:o.label)||"-"),1)]}),_:1},8,["label"]),u(a,{label:t(n)("contact.props.marital_status")},{default:l(()=>{var o;return[s(c(((o=e.employee.contact.maritalStatus)==null?void 0:o.label)||"-"),1)]}),_:1},8,["label"]),u(a,{label:t(n)("contact.religion.religion")},{default:l(()=>{var o;return[s(c(((o=e.employee.contact.religion)==null?void 0:o.name)||"-"),1)]}),_:1},8,["label"]),t(E)?(m(),d(a,{key:1,label:t(n)("contact.caste.caste")},{default:l(()=>{var o;return[s(c(((o=e.employee.contact.caste)==null?void 0:o.name)||"-"),1)]}),_:1},8,["label"])):p("",!0),t(L)?(m(),d(a,{key:2,label:t(n)("contact.category.category")},{default:l(()=>{var o;return[s(c(((o=e.employee.contact.category)==null?void 0:o.name)||"-"),1)]}),_:1},8,["label"])):p("",!0),u(a,{label:t(n)("employee.type")},{default:l(()=>{var o;return[s(c(((o=e.employee.type)==null?void 0:o.label)||"-"),1)]}),_:1},8,["label"]),(m(!0),N(F,null,D(((S=e.employee.contact)==null?void 0:S.customFields)||[],o=>(m(),d(a,{key:o.uuid,label:o.label},{default:l(()=>[s(c(o.formattedValue),1)]),_:2},1032,["label"]))),128)),u(Y,{employee:e.employee,onRefresh:i},null,8,["employee"]),u(a,{label:t(n)("employee.group.group")},{default:l(()=>[x("div",ee,[(m(!0),N(F,null,D(e.employee.groups,o=>(m(),d(T,{design:"primary",key:o.uuid},{default:l(()=>[s(c(o.name),1)]),_:2},1024))),128))])]),_:1},8,["label"])])]}),_:1})):p("",!0)]),_:1})],64)}}});export{oe as default};
