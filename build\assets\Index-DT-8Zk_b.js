import{r as e,q as a,o as s,w as c,e as r}from"./app-BAwPsakn.js";const i={name:"ResourceConfig"},l=Object.assign(i,{setup(f){const o=[{name:"ResourceConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"ResourceConfigAssignmentType",icon:"fas fa-chevron-right",label:"resource.assignment.type.type"}];return(_,g)=>{const n=e("router-view"),t=e("ModuleConfig");return s(),a(t,{navigations:o},{default:c(()=>[r(n)]),_:1})}}});export{l as default};
