import{u as P,j as h,H as B,l as b,r as n,a as E,o as F,e as a,f as o,w as g,d as i,F as M}from"./app-BAwPsakn.js";const x={class:"grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},y={name:"SiteConfigGeneral"},k=Object.assign(y,{setup(C){const _=P(),r=h("$trans"),c="config/",s=B(c),m=b({colorSchemes:[]}),p={enableSite:!1,showPublicView:!1,colorScheme:"default",googleMapEmbedUrl:"",type:"site"},t=b({...p}),f=d=>{Object.assign(m,d)};return(d,e)=>{const S=n("PageHeader"),u=n("BaseSwitch"),V=n("BaseSelect"),w=n("BaseTextarea"),U=n("FormAction"),v=n("ParentTransition");return F(),E(M,null,[a(S,{title:o(r)(o(_).meta.label),navs:[{label:o(r)("site.site"),path:"Site"}]},null,8,["title","navs"]),a(v,{appear:"",visibility:!0},{default:g(()=>[a(U,{"pre-requisites":!0,onSetPreRequisites:f,"init-url":c,"data-fetch":"site","init-form":p,form:t,action:"store","stay-on":"",redirect:"Site"},{default:g(()=>[i("div",x,[i("div",j,[a(u,{vertical:"",modelValue:t.enableSite,"onUpdate:modelValue":e[0]||(e[0]=l=>t.enableSite=l),name:"enableSite",label:o(r)("global.enable",{attribute:o(r)("site.site")}),error:o(s).enableSite,"onUpdate:error":e[1]||(e[1]=l=>o(s).enableSite=l)},null,8,["modelValue","label","error"])]),i("div",q,[a(u,{vertical:"",modelValue:t.showPublicView,"onUpdate:modelValue":e[2]||(e[2]=l=>t.showPublicView=l),name:"showPublicView",label:o(r)("site.config.props.public_view"),error:o(s).showPublicView,"onUpdate:error":e[3]||(e[3]=l=>o(s).showPublicView=l)},null,8,["modelValue","label","error"])]),i("div",R,[a(V,{modelValue:t.colorScheme,"onUpdate:modelValue":e[4]||(e[4]=l=>t.colorScheme=l),name:"colorScheme",label:o(r)("site.config.props.color_scheme"),options:m.colorSchemes,error:o(s).colorScheme,"onUpdate:error":e[5]||(e[5]=l=>o(s).colorScheme=l)},null,8,["modelValue","label","options","error"])]),i("div",T,[a(w,{modelValue:t.googleMapEmbedUrl,"onUpdate:modelValue":e[6]||(e[6]=l=>t.googleMapEmbedUrl=l),name:"googleMapEmbedUrl",label:o(r)("site.config.props.google_map_embed_url"),error:o(s).googleMapEmbedUrl,"onUpdate:error":e[7]||(e[7]=l=>o(s).googleMapEmbedUrl=l)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{k as default};
