import{i as K,u as W,h as X,H as Y,m as H,G as Z,l as V,c as S,n as q,C as ee,r as m,a as i,o as n,e as f,q as M,b as d,w as u,d as o,s as C,t as c,F as L,v as I,f as x,M as te,J as ae}from"./app-BAwPsakn.js";const ne={key:0,class:"text-sm"},se={class:"text-lg text-danger font-semibold"},oe={key:0},ie=["innerHTML"],re={key:1},le={class:"grid grid-cols-1 gap-6"},de={key:0,class:"mb-4"},me=["innerHTML"],ue={class:"dark:text-gray-400 flex items-center"},ce={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},_e=["innerHTML"],pe={key:2},he={class:"grid grid-cols-1 gap-6"},fe={key:0,class:"mb-4"},xe=["innerHTML"],ve={class:"dark:text-gray-400 flex items-center"},ge={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},be=["innerHTML"],ye={class:"dark:text-gray-300 font-semibold text-sm italic"},ke={key:3,class:"mt-4"},we={class:"text-sm my-2 dark:text-gray-300"},Te={name:"ExamOnlineExamSubmit"},Ae=Object.assign(Te,{setup(Me){const g=K(),b=W(),O=X(),U="exam/onlineExam/",p=Y(U),r=H(!1),y={questions:[],mediaUpdated:!1,mediaToken:Z(),mediaHash:[]},h=V({...y}),a=V({}),_=V({instructions:"",media:[],submittedAnswerCount:0}),$=H(new Date),A=H(null);S(()=>{const s=new Date(a.date.value+" "+a.endTime.at)-$.value;return Math.max(0,Math.floor(s/(1e3*60)))});const D=S(()=>{const s=new Date(a.date.value+" "+a.endTime.at)-$.value;if(s<=0)return"0:00:00";const v=Math.floor(s/(1e3*60*60)),k=Math.floor(s%(1e3*60*60)/(1e3*60)),w=Math.floor(s%(1e3*60)/1e3),E=k.toString().padStart(2,"0"),T=w.toString().padStart(2,"0");return v>0?`${v}:${E}:${T}`:`${k}:${T}`}),F=async()=>{await g.dispatch("exam/onlineExam/get",{uuid:b.params.uuid,params:{submission:!0}}).then(t=>{Object.assign(a,t),a.isLive||O.push({name:"ExamOnlineExam"}),P(),r.value=!1}).catch(t=>{r.value=!1})},P=async()=>{await g.dispatch("exam/onlineExam/getLiveQuestions",{uuid:b.params.uuid}).then(t=>{_.instructions=t.instructions,_.media=t.media,_.submittedAnswerCount=t.submittedAnswerCount,t.questions.forEach(s=>{y.questions.push({uuid:s.uuid,name:s.name,header:s.header,title:s.title,type:s.type,options:s.options,answer:s.answer??""})}),Object.assign(h,ae(y)),r.value=!1}).catch(t=>{r.value=!1})},N=()=>{r.value=!0,g.dispatch("exam/onlineExam/startSubmission",{uuid:b.params.uuid}).then(t=>{a.startedAt=t.startedAt,r.value=!1}).catch(t=>{r.value=!1})},R=async()=>{await te()&&(r.value=!0,g.dispatch("exam/onlineExam/finishSubmission",{uuid:b.params.uuid}).then(t=>{a.submittedAt=t.submittedAt,r.value=!1}).catch(t=>{r.value=!1}))},j=t=>{_.submittedAnswerCount=t.submittedAnswerCount};return q(async()=>{await F(),A.value=setInterval(()=>{$.value=new Date},1e3)}),ee(()=>{A.value&&clearInterval(A.value)}),(t,s)=>{const v=m("PageHeaderAction"),k=m("PageHeader"),w=m("BaseButton"),E=m("BaseInput"),T=m("BaseTextarea"),G=m("BaseRadioGroup"),Q=m("FormAction"),z=m("BaseCard"),J=m("ParentTransition");return n(),i(L,null,[f(k,{title:a.title,navs:[{label:t.$trans("exam.exam"),path:"Exam"},{label:t.$trans("exam.online_exam.online_exam"),path:"ExamOnlineExam"}]},{default:u(()=>[f(v,{name:"ExamOnlineExam",title:t.$trans("exam.online_exam.online_exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a.uuid?(n(),M(J,{key:0,appear:"",visibility:!0},{default:u(()=>[f(z,{"is-loading":r.value},{title:u(()=>[C(c(a.title),1)]),action:u(()=>[a.submittedAt.value?d("",!0):(n(),i("span",ne,[o("span",se,c(D.value),1)]))]),default:u(()=>[_.instructions&&!a.startedAt.value?(n(),i("div",oe,[o("div",{class:"dark:text-gray-400",innerHTML:_.instructions},null,8,ie),f(w,{design:"info",block:"",class:"mt-4",onClick:N},{default:u(()=>[C(c(t.$trans("exam.online_exam.start_submission")),1)]),_:1})])):d("",!0),h.questions.length&&a.startedAt.value&&!a.submittedAt.value?(n(),i("div",re,[f(Q,{"no-card":"","no-data-fetch":"",action:"submit","keep-adding":!1,"init-url":U,"init-form":y,form:h,"stay-on":!0,"after-submit":j},{default:u(()=>[o("div",le,[(n(!0),i(L,null,I(h.questions,(e,B)=>(n(),i("div",{class:"col-span-3 sm:col-span-1",key:e.uuid},[e.header?(n(),i("div",de,[o("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:e.header},null,8,me)])):d("",!0),o("div",ue,[o("span",ce,c(B+1)+".",1),o("div",{innerHTML:e.title},null,8,_e)]),e.type.value=="single_line_question"?(n(),M(E,{key:1,type:"text",modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,name:`questions.${e.name}`,placeholder:t.$trans("exam.online_exam.props.answer"),error:x(p)[`questions.${e.name}`],"onUpdate:error":l=>x(p)[`questions.${e.name}`]=l},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])):d("",!0),e.type.value=="multi_line_question"?(n(),M(T,{key:2,modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,name:`questions.${e.name}`,placeholder:t.$trans("exam.online_exam.props.answer"),error:x(p)[`questions.${e.name}`],"onUpdate:error":l=>x(p)[`questions.${e.name}`]=l},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])):d("",!0),e.type.value=="mcq"?(n(),M(G,{key:3,"top-margin":"",options:e.options,modelValue:e.answer,"onUpdate:modelValue":l=>e.answer=l,name:`questions.${e.name}`,placeholder:e.title,error:x(p)[`questions.${e.name}`],"onUpdate:error":l=>x(p)[`questions.${e.name}`]=l,horizontal:""},null,8,["options","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])):d("",!0)]))),128))])]),_:1},8,["form"])])):d("",!0),a.submittedAt.value?(n(),i("div",pe,[o("div",he,[(n(!0),i(L,null,I(h.questions,(e,B)=>(n(),i("div",{class:"col-span-3 sm:col-span-1",key:e.uuid},[e.header?(n(),i("div",fe,[o("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:e.header},null,8,xe)])):d("",!0),o("div",ve,[o("span",ge,c(B+1)+".",1),o("div",{innerHTML:e.title},null,8,be)]),o("div",ye,c(e.answer),1)]))),128))])])):d("",!0),_.submittedAnswerCount===h.questions.length&&!a.submittedAt.value?(n(),i("div",ke,[o("p",we,c(t.$trans("exam.online_exam.finish_submission_info")),1),f(w,{design:"success",block:"",onClick:R},{default:u(()=>[C(c(t.$trans("exam.online_exam.finish_submission")),1)]),_:1})])):d("",!0)]),_:1},8,["is-loading"])]),_:1})):d("",!0)],64)}}});export{Ae as default};
