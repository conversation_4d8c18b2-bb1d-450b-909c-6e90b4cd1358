import{u as D,i as I,m as F,H as J,l as A,n as K,a9 as M,K as Q,r as h,q as $,o as d,w as u,a as k,b as y,d as b,e as m,f as n,s as g,t as i,B as X,h as Y,y as j,F as Z}from"./app-BAwPsakn.js";const P={key:0,class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},te={key:0,class:"col-span-3 sm:col-span-1"},se={class:"ml-1"},ae={class:"ml-1"},ne={key:1,class:"col-span-3 sm:col-span-1"},oe={key:0,class:"ml-1"},re={key:0,class:"ml-1"},le={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},de={class:"flex items-center gap-2"},me={key:0,class:"grid grid-cols-3 gap-6"},pe={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},be={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(B,{emit:C}){const l=D(),R=I(),O=C,q=B,U={type:"",term:"",exam:"",attempt:"first",batch:"",students:[],showCourseWise:!1,showAllStudent:!1,column:1,marginTop:0},V=F(!1),p=F(!1),r=J(q.initUrl),s=A({...U}),f=A({selectedType:"",terms:q.preRequisites.terms,exams:q.preRequisites.exams,students:[]}),o=A({term:"",exam:"",batch:"",students:[],isLoaded:!(l.query.term||l.query.exam||l.query.batch||l.query.students)}),c=a=>{f.selectedType=q.preRequisites.types.find(t=>t.value===a)},S=async a=>{f.students=[],s.students=[];let t="studying";s.showAllStudent&&(t="all"),V.value=!0,await R.dispatch("student/listAll",{params:{batch:a.uuid,status:t}}).then(w=>{f.students=w,V.value=!1}).catch(w=>{V.value=!1}),o.students=[]},L=()=>{f.students=[],s.students=[],o.students=[]};return K(async()=>{if(_.isEmpty(l.query)){o.isLoaded=!0;return}o.term=l.query.term,s.term=l.query.term,o.exam=l.query.exam,s.exam=l.query.exam,o.batch=l.query.batch,s.batch=l.query.batch,s.showCourseWise=M(l.query.showCourseWise||""),s.showAllStudent=M(l.query.showAllStudent||""),l.query.batch&&(await S({uuid:l.query.batch}),s.students=l.query.students?l.query.students.split(","):[]),o.isLoaded=!0}),Q(q.preRequisites,a=>{a&&(f.selectedType=a.types.find(t=>t.value===l.query.type))},{deep:!0}),(a,t)=>{const w=h("BaseSelect"),x=h("BaseSelectSearch"),E=h("BaseSwitch"),N=h("BaseInput"),z=h("BaseFieldset"),G=h("FilterForm");return d(),$(G,{"init-form":U,multiple:["students"],form:s,onHide:t[21]||(t[21]=W=>O("hide"))},{default:u(()=>{var W,H;return[o.isLoaded?(d(),k("div",P,[b("div",ee,[m(w,{modelValue:s.type,"onUpdate:modelValue":t[0]||(t[0]=e=>s.type=e),name:"type",label:a.$trans("exam.marksheet.type"),options:B.preRequisites.types,error:n(r).type,"onUpdate:error":t[1]||(t[1]=e=>n(r).type=e),onChange:c},null,8,["modelValue","label","options","error"])]),(W=f.selectedType)!=null&&W.requiresTerm?(d(),k("div",te,[m(w,{modelValue:s.term,"onUpdate:modelValue":t[2]||(t[2]=e=>s.term=e),name:"term",label:a.$trans("exam.term.term"),options:B.preRequisites.terms,"value-prop":"uuid",error:n(r).term,"onUpdate:error":t[3]||(t[3]=e=>n(r).term=e)},{selectedOption:u(e=>{var v;return[g(i(e.value.name)+" ",1),b("span",se,"("+i(((v=e.value.division)==null?void 0:v.name)||a.$trans("general.all"))+")",1)]}),listOption:u(e=>{var v;return[g(i(e.option.name)+" ",1),b("span",ae,"("+i(((v=e.option.division)==null?void 0:v.name)||a.$trans("general.all"))+")",1)]}),_:1},8,["modelValue","label","options","error"])])):y("",!0),(H=f.selectedType)!=null&&H.requiresExam?(d(),k("div",ne,[m(w,{modelValue:s.exam,"onUpdate:modelValue":t[4]||(t[4]=e=>s.exam=e),name:"exam",label:a.$trans("exam.exam"),"value-prop":"uuid",options:B.preRequisites.exams,error:n(r).exam,"onUpdate:error":t[5]||(t[5]=e=>n(r).exam=e)},{selectedOption:u(e=>{var v,T;return[g(i(e.value.name)+" ",1),e.value.term?(d(),k("span",oe,"("+i(((T=(v=e.value.term)==null?void 0:v.division)==null?void 0:T.name)||a.$trans("general.all"))+")",1)):y("",!0)]}),listOption:u(e=>{var v,T;return[g(i(e.option.name)+" ",1),e.option.term?(d(),k("span",re,"("+i(((T=(v=e.option.term)==null?void 0:v.division)==null?void 0:T.name)||a.$trans("general.all"))+")",1)):y("",!0)]}),_:1},8,["modelValue","label","options","error"])])):y("",!0),b("div",le,[o.isLoaded?(d(),$(w,{key:0,modelValue:s.attempt,"onUpdate:modelValue":t[6]||(t[6]=e=>s.attempt=e),name:"attempt",label:a.$trans("exam.schedule.props.attempt"),options:B.preRequisites.attempts,error:n(r).attempt,"onUpdate:error":t[7]||(t[7]=e=>n(r).attempt=e)},null,8,["modelValue","label","options","error"])):y("",!0)]),b("div",ie,[o.isLoaded?(d(),$(x,{key:0,name:"batch",label:a.$trans("global.select",{attribute:a.$trans("academic.batch.batch")}),modelValue:s.batch,"onUpdate:modelValue":t[8]||(t[8]=e=>s.batch=e),error:n(r).batch,"onUpdate:error":t[9]||(t[9]=e=>n(r).batch=e),"value-prop":"uuid","init-search":o.batch,"search-key":"course_batch","search-action":"academic/batch/list",onSelected:S,onRemoved:L},{selectedOption:u(e=>[g(i(e.value.course.name)+" "+i(e.value.name),1)]),listOption:u(e=>[g(i(e.option.course.nameWithTerm)+" "+i(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):y("",!0)]),b("div",ue,[s.batch?(d(),$(w,{key:0,multiple:"",name:"students",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),options:f.students,modelValue:s.students,"onUpdate:modelValue":t[10]||(t[10]=e=>s.students=e),error:n(r).students,"onUpdate:error":t[11]||(t[11]=e=>n(r).students=e),"track-by":"name","value-prop":"uuid"},{selectedOption:u(e=>[g(i(e.value.name)+" ("+i(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:u(e=>[g(i(e.option.name)+" ("+i(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","options","modelValue","error"])):y("",!0)])])):y("",!0),o.isLoaded&&!n(X)(["student","guardian"],"any")?(d(),$(z,{key:1,class:"mt-4"},{legend:u(()=>[b("div",de,[g(i(a.$trans("global.show",{attribute:a.$trans("general.options")}))+" ",1),m(E,{reverse:"",modelValue:p.value,"onUpdate:modelValue":t[12]||(t[12]=e=>p.value=e),name:"showOptions"},null,8,["modelValue"])])]),default:u(()=>[p.value?(d(),k("div",me,[b("div",pe,[m(E,{vertical:"",modelValue:s.showCourseWise,"onUpdate:modelValue":t[13]||(t[13]=e=>s.showCourseWise=e),name:"showCourseWise",label:a.$trans("exam.report.course_wise"),error:n(r).showCourseWise,"onUpdate:error":t[14]||(t[14]=e=>n(r).showCourseWise=e)},null,8,["modelValue","label","error"])]),b("div",ce,[m(E,{vertical:"",modelValue:s.showAllStudent,"onUpdate:modelValue":t[15]||(t[15]=e=>s.showAllStudent=e),name:"showAllStudent",label:a.$trans("global.list_all",{attribute:a.$trans("student.student")}),error:n(r).showAllStudent,"onUpdate:error":t[16]||(t[16]=e=>n(r).showAllStudent=e)},null,8,["modelValue","label","error"])]),b("div",ye,[m(N,{type:"number",modelValue:s.column,"onUpdate:modelValue":t[17]||(t[17]=e=>s.column=e),name:"column",label:a.$trans("print.column"),error:n(r).column,"onUpdate:error":t[18]||(t[18]=e=>n(r).column=e)},null,8,["modelValue","label","error"])]),b("div",ve,[m(N,{"leading-text":a.$trans("list.unit.mm"),type:"number",modelValue:s.marginTop,"onUpdate:modelValue":t[19]||(t[19]=e=>s.marginTop=e),name:"marginTop",label:a.$trans("print.margin_top"),error:n(r).marginTop,"onUpdate:error":t[20]||(t[20]=e=>n(r).marginTop=e)},null,8,["leading-text","modelValue","label","error"])])])):y("",!0)]),_:1})):y("",!0)]}),_:1},8,["form"])}}},ge={name:"ExamMarksheet"},fe=Object.assign(ge,{setup(B){const C=D(),l=Y(),R=I();let O=[],q=[];const U="exam/marksheet/",V=F(!0),p=F(!1),r=A({types:[],terms:[],exams:[],attempts:[]}),s=async()=>{p.value=!0,await R.dispatch(U+"printPreRequisite").then(o=>{p.value=!1,Object.assign(r,o)}).catch(o=>{p.value=!1})},f=async()=>{p.value=!0,await R.dispatch(U+"print",{params:C.query}).then(o=>{p.value=!1,window.open("/print").document.write(o)}).catch(o=>{p.value=!1})};return K(async()=>{await s()}),(o,c)=>{const S=h("BaseButton"),L=h("PageHeaderAction"),a=h("PageHeader"),t=h("ParentTransition"),w=h("BaseCard");return d(),k(Z,null,[m(a,{title:o.$trans(n(C).meta.label),navs:[{label:o.$trans("exam.exam"),path:"Exam"}]},{default:u(()=>[m(L,{name:"ExamMarksheet",title:o.$trans("exam.marksheet.marksheet"),actions:n(O),"dropdown-actions":n(q),onToggleFilter:c[2]||(c[2]=x=>V.value=!V.value)},{default:u(()=>[n(j)("exam-marksheet:process")?(d(),$(S,{key:0,design:"white",onClick:c[0]||(c[0]=x=>n(l).push({name:"ExamMarksheetProcess"}))},{default:u(()=>[g(i(o.$trans("global.process",{attribute:o.$trans("exam.marksheet.marksheet")})),1)]),_:1})):y("",!0),n(j)("exam-marksheet:access")?(d(),$(S,{key:1,design:"white",onClick:c[1]||(c[1]=x=>n(l).push({name:"ExamMarksheetLegacy"}))},{default:u(()=>c[4]||(c[4]=[g("Legacy Version")])),_:1})):y("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),m(t,{appear:"",visibility:V.value},{default:u(()=>[m(be,{"is-loading":p.value,onAfterFilter:f,"init-url":U,"pre-requisites":r,onHide:c[3]||(c[3]=x=>V.value=!1)},null,8,["is-loading","pre-requisites"])]),_:1},8,["visibility"]),m(t,{appear:"",visibility:!0},{default:u(()=>[m(w,{"no-padding":"","no-content-padding":"","is-loading":p.value},null,8,["is-loading"])]),_:1})],64)}}});export{fe as default};
