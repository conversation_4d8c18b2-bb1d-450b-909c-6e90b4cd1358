import{u as M,i as K,H as Q,l as R,m as X,n as Y,r as $,q as k,o as u,w as h,d,a as V,b as g,e as i,f as r,s as j,t as b,F as w,v as H,M as Z,G as ee,J as se}from"./app-BAwPsakn.js";const ae={class:"grid grid-cols-4 gap-6"},te={class:"col-span-4 sm:col-span-1"},oe={key:0,class:"ml-1"},re={key:0,class:"ml-1"},le={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},de={class:"mt-4 grid grid-cols-4 gap-6"},ie={class:"col-span-4 sm:col-span-1"},me={class:"col-span-4 sm:col-span-1"},ue={class:"col-span-4 sm:col-span-1"},ce={class:"col-span-4"},pe={class:"flex items-center space-x-4"},be={key:0,class:"mt-4 grid grid-cols-4 gap-4"},he={class:"col-span-4 sm:col-span-1"},$e={class:"col-span-4 sm:col-span-1"},Ve={key:1,class:"mt-4 grid grid-cols-4 gap-6"},ve={class:"col-span-4 sm:col-span-1"},ge={class:"col-span-4 sm:col-span-1"},Ue={class:"col-span-4 sm:col-span-1"},_e={class:"flex items-end space-x-2"},fe=["onClick"],je={class:"col-span-4 sm:col-span-1"},Se={class:"col-span-4 sm:col-span-1"},ke={class:"col-span-4 sm:col-span-1"},Ee={class:"col-span-4 sm:col-span-1"},ye={class:"mt-4"},Be={name:"ExamScheduleForm"},Te=Object.assign(Be,{setup(G){const S=M(),_=K(),E={exam:"",batch:"",isReassessment:!1,grade:"",assessment:"",observation:"",description:"",records:[],additionalSubjects:[]},L={name:"",date:""},F="exam/schedule/",l=Q(F),y=R({exams:[],assessments:[],observations:[],grades:[]}),q=X(!1),v=R({selectedBatch:null,selectedAssessment:null,subjects:[]}),n=R({...E}),O=R({isLoaded:!S.params.uuid}),J=s=>{Object.assign(y,s)},N=async s=>{if(!(s!=null&&s.uuid)){n.batch="",n.assessment="",n.records=[],v.subjects=[];return}n.batch=(s==null?void 0:s.uuid)||"",v.selectedBatch=s,v.subjects=[];let o=[];q.value=!0,await _.dispatch("academic/batch/listSubjects",{uuid:(s==null?void 0:s.uuid)||""}).then(p=>{v.subjects=p,o=p.map(U=>({uuid:U.uuid,subject:U,date:"",startTime:"",duration:"",assessments:[],hasExam:!U.hasNoExam})),q.value=!1}).catch(p=>{q.value=!1}),n.records=o},P=async s=>{if(v.selectedAssessment=s,n.records.forEach(o=>{o.assessments=[]}),!(s!=null&&s.uuid)){n.assessment="";return}n.assessment=(s==null?void 0:s.uuid)||"",s.records.forEach(o=>{n.records.forEach(p=>{p.assessments.push({code:o.code,isEnabled:!0,name:o.name,marks:o.marks})})})},W=()=>{n.additionalSubjects.push({...L}),O.isLoaded=!0},z=async s=>{await Z()&&n.additionalSubjects.splice(s,1)},I=async s=>{var U,B,T,C,f;await N(s.batch),await P(s.assessment);let o=[];v.subjects.filter(m=>!m.isAdditionalSubject).forEach(m=>{var e;let c=s.records.find(t=>t.subject.uuid===m.uuid),A=[];s.assessment.records.forEach(t=>{A.push({code:t.code,isEnabled:!0,name:t.name,marks:t.marks})}),c?o.push({uuid:c.uuid,hasExam:c.hasExam,subject:c.subject,date:c.date.value,startTime:((e=c.startTime)==null?void 0:e.at)||"",duration:c.duration||"",assessments:c.assessments.length?c.assessments:A}):o.push({uuid:ee(),hasExam:!1,subject:m,date:"",assessments:A})});let p=[];s.records.filter(m=>m.isAdditionalSubject).forEach(m=>{var c;p.push({name:m.subject.name,code:m.subject.code,date:m.date.value||"",startTime:((c=m.startTime)==null?void 0:c.at)||"",duration:m.duration||""})}),Object.assign(E,{...s,exam:((U=s.exam)==null?void 0:U.uuid)||"",batch:((B=s.batch)==null?void 0:B.uuid)||"",isReassessment:s.isReassessment||!1,attempt:s.attempt.value||"",grade:((T=s.grade)==null?void 0:T.uuid)||"",assessment:((C=s.assessment)==null?void 0:C.uuid)||"",observation:((f=s.observation)==null?void 0:f.uuid)||"",records:o,additionalSubjects:p}),Object.assign(n,se(E)),O.isLoaded=!0};return Y(async()=>{}),(s,o)=>{const p=$("BaseSelect"),U=$("BaseSelectSearch"),B=$("BaseSwitch"),T=$("BaseBadge"),C=$("BaseTextarea"),f=$("DatePicker"),m=$("BaseInput"),c=$("BaseFieldset"),A=$("FormAction");return u(),k(A,{"pre-requisites":!0,onSetPreRequisites:J,"init-url":F,"init-form":E,form:n,"set-form":I,redirect:"ExamSchedule"},{default:h(()=>[d("div",ae,[d("div",te,[i(p,{modelValue:n.exam,"onUpdate:modelValue":o[0]||(o[0]=e=>n.exam=e),name:"exam",label:s.$trans("exam.exam"),options:y.exams,"value-prop":"uuid",error:r(l).exam,"onUpdate:error":o[1]||(o[1]=e=>r(l).exam=e)},{selectedOption:h(e=>{var t,a;return[j(b(e.value.name)+" ",1),e.value.term?(u(),V("span",oe,"("+b(((a=(t=e.value.term)==null?void 0:t.division)==null?void 0:a.name)||s.$trans("general.all"))+")",1)):g("",!0)]}),listOption:h(e=>{var t,a;return[j(b(e.option.name)+" ",1),e.option.term?(u(),V("span",re,"("+b(((a=(t=e.option.term)==null?void 0:t.division)==null?void 0:a.name)||s.$trans("general.all"))+")",1)):g("",!0)]}),_:1},8,["modelValue","label","options","error"])]),d("div",le,[O.isLoaded?(u(),k(U,{key:0,name:"batch",label:s.$trans("academic.batch.batch"),modelValue:v.selectedBatch,"onUpdate:modelValue":o[2]||(o[2]=e=>v.selectedBatch=e),error:r(l).batch,"onUpdate:error":o[3]||(o[3]=e=>r(l).batch=e),"value-prop":"uuid","object-prop":!0,"init-search":O.batch,"search-key":"course_batch","search-action":"academic/batch/list","additional-search-query":{withSubjects:!0},onChange:N},{selectedOption:h(e=>[j(b(e.value.course.name)+" - "+b(e.value.name),1)]),listOption:h(e=>[j(b(e.option.course.nameWithTerm)+" - "+b(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):g("",!0)]),d("div",ne,[r(S).params.uuid?g("",!0):(u(),k(B,{key:0,vertical:"",modelValue:n.isReassessment,"onUpdate:modelValue":o[4]||(o[4]=e=>n.isReassessment=e),name:"isReassessment",label:s.$trans("global.is",{attribute:s.$trans("exam.schedule.reassessment")}),error:r(l).isReassessment,"onUpdate:error":o[5]||(o[5]=e=>r(l).isReassessment=e)},null,8,["modelValue","label","error"])),r(S).params.uuid&&n.isReassessment?(u(),k(T,{key:1},{default:h(()=>[j(b(s.$trans("exam.schedule.reassessment")),1)]),_:1})):g("",!0)])]),d("div",de,[n.batch?(u(),V(w,{key:0},[d("div",ie,[i(p,{modelValue:n.grade,"onUpdate:modelValue":o[6]||(o[6]=e=>n.grade=e),name:"grade",label:s.$trans("exam.grade.grade"),options:y.grades,"label-prop":"name","value-prop":"uuid",error:r(l).grade,"onUpdate:error":o[7]||(o[7]=e=>r(l).grade=e)},null,8,["modelValue","label","options","error"])]),d("div",me,[i(p,{modelValue:v.selectedAssessment,"onUpdate:modelValue":o[8]||(o[8]=e=>v.selectedAssessment=e),name:"assessment",label:s.$trans("exam.assessment.assessment"),options:y.assessments,"label-prop":"name","value-prop":"uuid","object-prop":!0,error:r(l).assessment,"onUpdate:error":o[9]||(o[9]=e=>r(l).assessment=e),onChange:P},null,8,["modelValue","label","options","error"])]),d("div",ue,[i(p,{modelValue:n.observation,"onUpdate:modelValue":o[10]||(o[10]=e=>n.observation=e),name:"observation",label:s.$trans("exam.observation.observation"),options:y.observations,"label-prop":"name","value-prop":"uuid",error:r(l).observation,"onUpdate:error":o[11]||(o[11]=e=>r(l).observation=e)},null,8,["modelValue","label","options","error"])])],64)):g("",!0),d("div",ce,[i(C,{rows:1,modelValue:n.description,"onUpdate:modelValue":o[12]||(o[12]=e=>n.description=e),name:"description",label:s.$trans("exam.schedule.props.description"),error:r(l).description,"onUpdate:error":o[13]||(o[13]=e=>r(l).description=e)},null,8,["modelValue","label","error"])])]),(u(!0),V(w,null,H(n.records,(e,t)=>(u(),k(c,{class:"mt-4",key:e.uuid},{legend:h(()=>[d("div",pe,[i(B,{modelValue:e.hasExam,"onUpdate:modelValue":a=>e.hasExam=a,name:`records.${t}.hasExam`,error:r(l)[`records.${t}.hasExam`],"onUpdate:error":a=>r(l)[`records.${t}.hasExam`]=a},null,8,["modelValue","onUpdate:modelValue","name","error","onUpdate:error"]),d("span",null,b(e.subject.name)+" ("+b(e.subject.code)+")",1)])]),default:h(()=>[e.hasExam?(u(),V("div",be,[d("div",he,[i(f,{modelValue:e.date,"onUpdate:modelValue":a=>e.date=a,name:`records.${t}.date`,label:s.$trans("exam.schedule.props.date"),"no-clear":"",error:r(l)[`records.${t}.date`],"onUpdate:error":a=>r(l)[`records.${t}.date`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),(u(!0),V(w,null,H(e.assessments,(a,x)=>(u(),V("div",$e,[i(m,{type:"text",modelValue:a.marks,"onUpdate:modelValue":D=>a.marks=D,name:`records.${t}.assessments.${x}.marks`,label:a.name+" ("+a.code+")",error:r(l)[`records.${t}.assessments.${x}.marks`],"onUpdate:error":D=>r(l)[`records.${t}.assessments.${x}.marks`]=D},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]))),256))])):g("",!0),e.hasExam?(u(),V("div",Ve,[d("div",ve,[i(f,{modelValue:e.startTime,"onUpdate:modelValue":a=>e.startTime=a,name:`records.${t}.startTime`,label:s.$trans("exam.schedule.props.start_time"),as:"time",error:r(l)[`records.${t}.startTime`],"onUpdate:error":a=>r(l)[`records.${t}.startTime`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",ge,[i(m,{type:"number","trailing-text":s.$trans("list.durations.m"),modelValue:e.duration,"onUpdate:modelValue":a=>e.duration=a,name:`records.${t}.duration`,label:s.$trans("exam.schedule.props.duration"),error:r(l)[`records.${t}.duration`],"onUpdate:error":a=>r(l)[`records.${t}.duration`]=a},null,8,["trailing-text","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])):g("",!0)]),_:2},1024))),128)),n.batch?(u(),k(c,{key:0,class:"mt-4"},{legend:h(()=>[j(b(s.$trans("academic.subject.additional_subject")),1)]),default:h(()=>[(u(!0),V(w,null,H(n.additionalSubjects,(e,t)=>(u(),V("div",{class:"mt-4 grid grid-cols-4 gap-6",key:`additionalSubject_${t}`},[d("div",Ue,[d("div",_e,[d("span",{class:"text-danger ml-2 cursor-pointer",onClick:a=>z(t)},o[14]||(o[14]=[d("i",{class:"fas fa-times-circle"},null,-1)]),8,fe),i(m,{type:"text",modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,name:`additionalSubjects.${t}.name`,label:s.$trans("academic.subject.props.name"),error:r(l)[`additionalSubjects.${t}.name`],"onUpdate:error":a=>r(l)[`additionalSubjects.${t}.name`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])]),d("div",je,[i(m,{type:"text",modelValue:e.code,"onUpdate:modelValue":a=>e.code=a,name:`additionalSubjects.${t}.code`,label:s.$trans("academic.subject.props.code"),error:r(l)[`additionalSubjects.${t}.code`],"onUpdate:error":a=>r(l)[`additionalSubjects.${t}.code`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",Se,[i(f,{modelValue:e.date,"onUpdate:modelValue":a=>e.date=a,name:`additionalSubjects.${t}.date`,label:s.$trans("exam.schedule.props.date"),"no-clear":"",error:r(l)[`additionalSubjects.${t}.date`],"onUpdate:error":a=>r(l)[`additionalSubjects.${t}.date`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",ke,[i(f,{modelValue:e.startTime,"onUpdate:modelValue":a=>e.startTime=a,name:`additionalSubjects.${t}.startTime`,label:s.$trans("exam.schedule.props.start_time"),as:"time",error:r(l)[`additionalSubjects.${t}.startTime`],"onUpdate:error":a=>r(l)[`additionalSubjects.${t}.startTime`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",Ee,[i(m,{type:"number","trailing-text":s.$trans("list.durations.m"),modelValue:e.duration,"onUpdate:modelValue":a=>e.duration=a,name:`additionalSubjects.${t}.duration`,label:s.$trans("exam.schedule.props.duration"),error:r(l)[`additionalSubjects.${t}.duration`],"onUpdate:error":a=>r(l)[`additionalSubjects.${t}.duration`]=a},null,8,["trailing-text","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])]))),128)),d("div",ye,[i(T,{design:"primary",onClick:W,class:"cursor-pointer"},{default:h(()=>[j(b(s.$trans("global.add",{attribute:s.$trans("academic.subject.additional_subject")})),1)]),_:1})])]),_:1})):g("",!0)]),_:1},8,["form"])}}}),Ae={name:"ExamScheduleAction"},Fe=Object.assign(Ae,{setup(G){const S=M();return(_,E)=>{const L=$("PageHeaderAction"),F=$("PageHeader"),l=$("ParentTransition");return u(),V(w,null,[i(F,{title:_.$trans(r(S).meta.trans,{attribute:_.$trans(r(S).meta.label)}),navs:[{label:_.$trans("exam.exam"),path:"Exam"},{label:_.$trans("exam.schedule.schedule"),path:"ExamScheduleList"}]},{default:h(()=>[i(L,{name:"ExamSchedule",title:_.$trans("exam.schedule.schedule"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(l,{appear:"",visibility:!0},{default:h(()=>[i(Te)]),_:1})],64)}}});export{Fe as default};
