import{l as I,r as a,q as c,o as m,w as e,d as C,e as n,u as w,h as H,j as P,m as A,f as u,a as L,F as M,v as N,s as p,t as _,b as B}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(g,{emit:f}){const v=f,l={search:""},d=I({...l});return(i,s)=>{const b=a("BaseInput"),r=a("FilterForm");return m(),c(r,{"init-form":l,form:d,onHide:s[1]||(s[1]=t=>v("hide"))},{default:e(()=>[C("div",O,[C("div",U,[n(b,{type:"text",modelValue:d.search,"onUpdate:modelValue":s[0]||(s[0]=t=>d.search=t),name:"search",label:i.$trans("general.search")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},S={name:"TeamConfigRoleList"},z=Object.assign(S,{props:{team:{type:Object,default(){return{name:""}}}},setup(g){const f=w(),v=H(),l=P("emitter"),d="team/role/",i=A(!1),s=I({}),b=r=>{Object.assign(s,r)};return(r,t)=>{const y=a("PageHeaderAction"),R=a("PageHeader"),F=a("ParentTransition"),$=a("DataCell"),k=a("FloatingMenuItem"),T=a("FloatingMenu"),h=a("DataRow"),D=a("BaseButton"),V=a("DataTable"),j=a("ListItem");return m(),c(j,{"init-url":d,uuid:u(f).params.uuid,onSetItems:b},{header:e(()=>[g.team.uuid?(m(),c(R,{key:0,title:r.$trans("team.config.role.role"),navs:[]},{default:e(()=>[n(y,{url:`teams/${g.team.uuid}/roles/`,name:"TeamConfigRole",title:r.$trans("team.config.role.role"),actions:["create","filter"],"dropdown-actions":["print","pdf","excel"],onToggleFilter:t[0]||(t[0]=o=>i.value=!i.value)},null,8,["url","title"])]),_:1},8,["title"])):B("",!0)]),filter:e(()=>[n(F,{appear:"",visibility:i.value},{default:e(()=>[n(q,{onRefresh:t[1]||(t[1]=o=>u(l).emit("listItems")),onHide:t[2]||(t[2]=o=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(V,{header:s.headers,meta:s.meta,module:"team.config.role",onRefresh:t[4]||(t[4]=o=>u(l).emit("listItems"))},{actionButton:e(()=>[n(D,{onClick:t[3]||(t[3]=o=>u(v).push({name:"TeamConfigRoleCreate"}))},{default:e(()=>[p(_(r.$trans("global.add",{attribute:r.$trans("team.config.role.role")})),1)]),_:1})]),default:e(()=>[(m(!0),L(M,null,N(s.data,o=>(m(),c(h,{key:o.uuid},{default:e(()=>[n($,{name:"name"},{default:e(()=>[p(_(o.label),1)]),_:2},1024),n($,{name:"createdAt"},{default:e(()=>[p(_(o.createdAt.formatted),1)]),_:2},1024),n($,{name:"action"},{default:e(()=>[o.isDefault?B("",!0):(m(),c(T,{key:0},{default:e(()=>[n(k,{icon:"fas fa-trash",onClick:x=>u(l).emit("deleteItem",{uuid:u(f).params.uuid,moduleUuid:o.uuid})},{default:e(()=>[p(_(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{z as default};
