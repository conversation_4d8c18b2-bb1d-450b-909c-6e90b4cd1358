import{u as w,l as d,H,n as $,r as u,q as R,o as S,w as m,d as q,b as U,f as s,s as A,t as h,e as l,h as D,i as T,j as V,D as C,y as L,m as N,a as x,F as E,J as W}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-4 gap-6"},J={class:"col-span-4 sm:col-span-1"},M={class:"col-span-4 sm:col-span-1"},G={__name:"AbsenteeFilter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(F,{emit:i}){const y=w(),_=i,o=F,b={date:"",batch:""},t=d({...b}),r=H(o.initUrl);d({sessions:o.preRequisites.sessions});const n=d({batch:"",isLoaded:!y.query.batch});return $(async()=>{n.batch=y.query.batch,n.isLoaded=!0}),(p,a)=>{const v=u("BaseSelectSearch"),f=u("DatePicker"),g=u("FilterForm");return S(),R(g,{"init-form":b,form:t,onHide:a[4]||(a[4]=e=>_("hide"))},{default:m(()=>[q("div",z,[q("div",J,[n.isLoaded?(S(),R(v,{key:0,name:"batch",label:p.$trans("global.select",{attribute:p.$trans("academic.batch.batch")}),modelValue:t.batch,"onUpdate:modelValue":a[0]||(a[0]=e=>t.batch=e),error:s(r).batch,"onUpdate:error":a[1]||(a[1]=e=>s(r).batch=e),"value-prop":"uuid","init-search":n.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:m(e=>[A(h(e.value.course.nameWithTerm)+" "+h(e.value.name),1)]),listOption:m(e=>[A(h(e.option.course.nameWithTerm)+" "+h(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):U("",!0)]),q("div",M,[l(f,{modelValue:t.date,"onUpdate:modelValue":a[2]||(a[2]=e=>t.date=e),name:"date",label:p.$trans("student.attendance.props.date"),"no-clear":"",error:s(r).date,"onUpdate:error":a[3]||(a[3]=e=>s(r).date=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}},I={name:"StudentAttendanceAbsentee"},X=Object.assign(I,{setup(F){const i=w(),y=D(),_=T();V("emitter");const o=V("$trans");C();let b=[];L("student:list-attendance")&&(b=["print","pdf","excel"]);const t={batch:"",date:"",students:[]},r="student/attendance/",n=N(!1),p=d({});H(r);const a=d({headers:[],data:[],meta:{total:0,isActionable:!1}}),v=d({...t}),f=d({meta:{},defaultAttendanceType:{code:"P",label:o("student.attendance.types.present")}}),g=async()=>{n.value=!0,await _.dispatch(r+"preRequisite").then(c=>{n.value=!1,Object.assign(p,c)}).catch(c=>{n.value=!1})},e=async()=>{n.value=!0,await _.dispatch(r+"fetch",{params:i.query}).then(c=>{n.value=!1,t.batch=i.query.batch,t.date=i.query.date,t.students=c.data,f.meta=c.meta,f.meta.isForceHoliday?(t.markAsHoliday=!0,t.holidayReason=f.meta.holidayReason):(t.markAsHoliday=!1,t.holidayReason=""),Object.assign(v,W(t)),Object.assign(a,c)}).catch(c=>{n.value=!1})};return $(async()=>{await g(),i.query.batch&&i.query.date&&await e()}),(c,k)=>{const j=u("BaseButton"),O=u("PageHeaderAction"),P=u("PageHeader"),B=u("ParentTransition");return S(),x(E,null,[l(P,{title:s(o)(s(i).meta.label),navs:[{label:s(o)("student.student"),path:"Student"}]},{default:m(()=>[l(O,{url:"student/attendance/",name:"StudentAttendance",title:s(o)("student.attendance.absentees"),actions:[],"dropdown-actions":s(b)},{default:m(()=>[l(j,{design:"primary",onClick:k[0]||(k[0]=K=>s(y).push({name:"StudentAttendance"}))},{default:m(()=>[A(h(s(o)("global.list",{attribute:s(o)("student.attendance.attendance")})),1)]),_:1})]),_:1},8,["title","dropdown-actions"])]),_:1},8,["title","navs"]),l(B,{appear:"",visibility:!0},{default:m(()=>[l(G,{onAfterFilter:e,"pre-requisites":p,"init-url":r},null,8,["pre-requisites"])]),_:1}),l(B,{appear:"",visibility:!0})],64)}}});export{X as default};
