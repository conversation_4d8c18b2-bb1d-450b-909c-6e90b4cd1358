import{u as L,h as J,i as K,H as P,l as R,r as p,q as d,o as s,w as u,d as A,b as m,a as U,e as c,f as o,s as y,t as i,F as j,v as Q,J as W}from"./app-BAwPsakn.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-2 sm:col-span-1"},F={key:0,class:"col-span-2 sm:col-span-1"},ee={key:1,class:"col-span-2 sm:col-span-1"},ae={key:2,class:"col-span-2 sm:col-span-1"},te={class:"col-span-2 sm:col-span-1"},ne={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},le={name:"AcademicCertificateForm"},re=Object.assign(le,{setup(E){const S=L();J();const b=K(),$={template:"",student:"",employee:"",date:"",name:"",isDuplicate:!1,customFields:[]},D="academic/certificate/",n=P(D),O=R({templates:[]}),r=R({...$}),f=R({template:{},for:""}),H=a=>{Object.assign(O,a)},v=R({isLoaded:!S.params.uuid}),M=async a=>{var h,B,V,T;f.for=a.template.for.value,await w(a.template);let t="",k="";a.template.for.value=="student"?(t=a.to.uuid,v.student=(h=a.to)==null?void 0:h.uuid):a.template.for.value=="employee"&&(k=a.to.uuid,v.employee=(B=a.to)==null?void 0:B.uuid);let g=r.customFields;g.forEach(C=>{var N;C.value=(N=a.customFields.find(q=>q.key==C.name))==null?void 0:N.value}),Object.assign($,{...a,date:((V=a.date)==null?void 0:V.value)||"",template:((T=a.template)==null?void 0:T.uuid)||"",name:a.isAnonymous?a.to.name:"",student:t,employee:k,customFields:g}),Object.assign(r,W($)),v.isLoaded=!0},w=async a=>{if(r.student="",r.employee="",a==null||_.isEmpty(a)){f.template={};return}await b.dispatch("academic/certificateTemplate/get",{uuid:a.uuid}).then(t=>{f.template=t,r.customFields=t.customFields}).catch(t=>{})},z=a=>{f.for=a.for.value,w(a)},G=()=>{f.for=""};return(a,t)=>{const k=p("BaseSelect"),g=p("DatePicker"),h=p("TextMuted"),B=p("BaseSelectSearch"),V=p("BaseInput"),T=p("BaseSwitch"),C=p("BaseTextarea"),N=p("BaseMultiCheckbox"),q=p("BaseRadioGroup"),I=p("BaseFieldset"),x=p("FormAction");return s(),d(x,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":D,"init-form":$,form:r,setForm:M,redirect:"AcademicCertificate"},{default:u(()=>[A("div",X,[A("div",Y,[c(k,{modelValue:r.template,"onUpdate:modelValue":t[0]||(t[0]=e=>r.template=e),name:"template",label:a.$trans("academic.certificate.template.template"),options:O.templates,"value-prop":"uuid","track-by":"name",error:o(n).template,"onUpdate:error":t[1]||(t[1]=e=>o(n).template=e),onSelected:z,onRemoved:G},{selectedOption:u(e=>[y(i(e.value.name)+" ("+i(e.value.for.label)+") ",1)]),listOption:u(e=>[y(i(e.option.name)+" ("+i(e.option.for.label)+") ",1)]),_:1},8,["modelValue","label","options","error"])]),A("div",Z,[c(g,{modelValue:r.date,"onUpdate:modelValue":t[2]||(t[2]=e=>r.date=e),name:"date",label:a.$trans("academic.certificate.props.date"),"no-clear":"",error:o(n).date,"onUpdate:error":t[3]||(t[3]=e=>o(n).date=e)},null,8,["modelValue","label","error"])]),f.for=="student"?(s(),U("div",F,[v.isLoaded?(s(),d(B,{key:0,name:"student",label:a.$trans("student.student"),placeholder:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:r.student,"onUpdate:modelValue":t[4]||(t[4]=e=>r.student=e),error:o(n).student,"onUpdate:error":t[5]||(t[5]=e=>o(n).student=e),"value-prop":"uuid","init-search":v.student,"search-key":"name","search-action":"student/summary"},{selectedOption:u(e=>[y(i(e.value.name)+" ("+i(e.value.courseName+" "+e.value.batchName)+") ",1),c(h,null,{default:u(()=>[y(i(e.value.codeNumber),1)]),_:2},1024)]),listOption:u(e=>[y(i(e.option.name)+" ("+i(e.option.courseName+" "+e.option.batchName)+") ",1),c(h,null,{default:u(()=>[y(i(e.option.codeNumber),1)]),_:2},1024)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):m("",!0)])):m("",!0),f.for=="employee"?(s(),U("div",ee,[v.isLoaded?(s(),d(B,{key:0,name:"employee",label:a.$trans("employee.employee"),placeholder:a.$trans("global.select",{attribute:a.$trans("employee.employee")}),modelValue:r.employee,"onUpdate:modelValue":t[6]||(t[6]=e=>r.employee=e),error:o(n).employee,"onUpdate:error":t[7]||(t[7]=e=>o(n).employee=e),"value-prop":"uuid","init-search":v.employee,"search-key":"name","search-action":"employee/summary"},{selectedOption:u(e=>[y(i(e.value.name)+" ("+i(e.value.designation)+") ",1)]),listOption:u(e=>[y(i(e.option.name)+" ("+i(e.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):m("",!0)])):m("",!0),!r.student&&!r.employee?(s(),U("div",ae,[c(V,{type:"text",modelValue:r.name,"onUpdate:modelValue":t[8]||(t[8]=e=>r.name=e),name:"name",label:a.$trans("contact.props.name"),error:o(n).name,"onUpdate:error":t[9]||(t[9]=e=>o(n).name=e)},null,8,["modelValue","label","error"])])):m("",!0),A("div",te,[c(T,{vertical:"",modelValue:r.isDuplicate,"onUpdate:modelValue":t[10]||(t[10]=e=>r.isDuplicate=e),name:"isDuplicate",label:a.$trans("general.duplicate"),error:o(n).isDuplicate,"onUpdate:error":t[11]||(t[11]=e=>o(n).isDuplicate=e)},null,8,["modelValue","label","error"])])]),r.customFields.length?(s(),d(I,{key:0,class:"mt-4"},{legend:u(()=>[y(i(a.$trans("custom_field.custom_field")),1)]),default:u(()=>[A("div",ne,[(s(!0),U(j,null,Q(r.customFields,(e,ie)=>(s(),U("div",oe,[e.type=="text_input"||e.type=="email_input"||e.type=="currency_input"?(s(),d(V,{key:0,type:"text",currency:e.type=="currency_input",modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,name:`customFields.${e.name}`,label:e.label,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l},null,8,["currency","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):m("",!0),e.type=="number_input"?(s(),d(V,{key:1,type:"number",modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,name:`customFields.${e.name}`,label:e.label,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):m("",!0),e.type=="date_picker"||e.type=="time_picker"||e.type=="date_time_picker"?(s(),d(g,{key:2,as:e.type=="date_picker"?"date":e.type=="time_picker"?"time":"datetime",modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,name:`customFields.${e.name}`,label:e.label,"no-clear":!!e.isRequired,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l},null,8,["as","modelValue","onUpdate:modelValue","name","label","no-clear","error","onUpdate:error"])):m("",!0),e.type=="multi_line_text_input"?(s(),d(C,{key:3,modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,name:`customFields.${e.name}`,label:e.label,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):m("",!0),e.type=="select_input"||e.type=="multi_select_input"?(s(),d(k,{key:4,multiple:e.type=="multi_select_input",modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,options:e.optionArray,name:`customFields.${e.name}`,label:e.label,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l},null,8,["multiple","modelValue","onUpdate:modelValue","options","name","label","error","onUpdate:error"])):m("",!0),e.type=="checkbox_input"?(s(),d(N,{key:5,horizontal:"",options:e.optionArray,modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,name:`customFields.${e.name}`,label:e.label,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l},null,8,["options","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):m("",!0),e.type=="radio_input"?(s(),d(q,{key:6,"top-margin":"",options:e.optionArray,modelValue:e.value,"onUpdate:modelValue":l=>e.value=l,name:`customFields.${e.name}`,label:e.label,error:o(n)[`customFields.${e.name}`],"onUpdate:error":l=>o(n)[`customFields.${e.name}`]=l,horizontal:""},null,8,["options","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])):m("",!0)]))),256))])]),_:1})):m("",!0)]),_:1},8,["form"])}}}),se={name:"AcademicCertificateAction"},me=Object.assign(se,{setup(E){const S=L();return(b,$)=>{const D=p("PageHeaderAction"),n=p("PageHeader"),O=p("ParentTransition");return s(),U(j,null,[c(n,{title:b.$trans(o(S).meta.trans,{attribute:b.$trans(o(S).meta.label)}),navs:[{label:b.$trans("academic.academic"),path:"Academic"},{label:b.$trans("academic.certificate.certificate"),path:"AcademicCertificateList"}]},{default:u(()=>[c(D,{name:"AcademicCertificate",title:b.$trans("academic.certificate.certificate"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),c(O,{appear:"",visibility:!0},{default:u(()=>[c(re)]),_:1})],64)}}});export{me as default};
