import{u as E,l as k,n as U,r as o,q as f,o as c,w as e,d as w,b as v,s as d,t as u,e as n,h as W,j as z,y,m as G,f as a,a as D,F as L,v as M}from"./app-BAwPsakn.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(V,{emit:h}){const m=E(),g=h,R=V,C={batches:[],subjects:[]},p=k({...C}),S=k({subjects:R.preRequisites.subjects}),b=k({batches:[],subjects:[],isLoaded:!(m.query.batches||m.query.subjects)});return U(async()=>{b.batches=m.query.batches?m.query.batches.split(","):[],b.subjects=m.query.subjects?m.query.subjects.split(","):[],b.isLoaded=!0}),(j,_)=>{const s=o("BaseSelectSearch"),l=o("BaseSelect"),B=o("FilterForm");return c(),f(B,{"init-form":C,form:p,multiple:["batches","subjects"],onHide:_[2]||(_[2]=r=>g("hide"))},{default:e(()=>[w("div",J,[w("div",K,[b.isLoaded?(c(),f(s,{key:0,multiple:"",name:"batches",label:j.$trans("global.select",{attribute:j.$trans("academic.batch.batch")}),modelValue:p.batches,"onUpdate:modelValue":_[0]||(_[0]=r=>p.batches=r),"value-prop":"uuid","init-search":b.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(r=>[d(u(r.value.course.name)+" "+u(r.value.name),1)]),listOption:e(r=>[d(u(r.option.course.nameWithTerm)+" "+u(r.option.name),1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),w("div",Q,[n(l,{multiple:"",modelValue:p.subjects,"onUpdate:modelValue":_[1]||(_[1]=r=>p.subjects=r),name:"subjects",label:j.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:S.subjects},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},Y={name:"ResourceSyllabusList"},x=Object.assign(Y,{setup(V){const h=W(),m=z("emitter");let g=["filter"];y("resource:config")&&g.push("config"),y("syllabus:create")&&g.unshift("create");let R=[];y("syllabus:export")&&(R=["print","pdf","excel"]);const C="resource/syllabus/",p=G(!1),S=k({subjects:[]}),b=k({}),j=s=>{Object.assign(S,s)},_=s=>{Object.assign(b,s)};return(s,l)=>{const B=o("PageHeaderAction"),r=o("PageHeader"),I=o("ParentTransition"),T=o("TextMuted"),q=o("DataCell"),F=o("FloatingMenuItem"),A=o("FloatingMenu"),H=o("DataRow"),O=o("BaseButton"),P=o("DataTable"),N=o("ListItem");return c(),f(N,{"init-url":C,"pre-requisites":!0,onSetPreRequisites:j,"additional-query":{},onSetItems:_},{header:e(()=>[n(r,{title:s.$trans("resource.syllabus.syllabus"),navs:[{label:s.$trans("resource.resource"),path:"Resource"}]},{default:e(()=>[n(B,{url:"resource/syllabuses/",name:"ResourceSyllabus",title:s.$trans("resource.syllabus.syllabus"),actions:a(g),"dropdown-actions":a(R),"config-path":"ResourceConfig",onToggleFilter:l[0]||(l[0]=t=>p.value=!p.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(I,{appear:"",visibility:p.value},{default:e(()=>[n(X,{onRefresh:l[1]||(l[1]=t=>a(m).emit("listItems")),"pre-requisites":S,onHide:l[2]||(l[2]=t=>p.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[n(I,{appear:"",visibility:!0},{default:e(()=>[n(P,{header:b.headers,meta:b.meta,module:"resource.syllabus",onRefresh:l[4]||(l[4]=t=>a(m).emit("listItems"))},{actionButton:e(()=>[a(y)("syllabus:create")?(c(),f(O,{key:0,onClick:l[3]||(l[3]=t=>a(h).push({name:"ResourceSyllabusCreate"}))},{default:e(()=>[d(u(s.$trans("global.add",{attribute:s.$trans("resource.syllabus.syllabus")})),1)]),_:1})):v("",!0)]),default:e(()=>[(c(!0),D(L,null,M(b.data,t=>(c(),f(H,{key:t.uuid,onDoubleClick:i=>a(h).push({name:"ResourceSyllabusShow",params:{uuid:t.uuid}})},{default:e(()=>[n(q,{name:"records"},{default:e(()=>[(c(!0),D(L,null,M(t.records,i=>{var $;return c(),D("div",null,[d(u((($=i.batch.course)==null?void 0:$.name)+" "+i.batch.name)+" ",1),i.subject?(c(),f(T,{key:0},{default:e(()=>[d(u(i.subject.name),1)]),_:2},1024)):v("",!0)])}),256))]),_:2},1024),n(q,{name:"employee"},{default:e(()=>{var i;return[d(u(((i=t.employee)==null?void 0:i.name)||"-")+" ",1),n(T,{block:""},{default:e(()=>{var $;return[d(u(($=t.employee)==null?void 0:$.codeNumber),1)]}),_:2},1024)]}),_:2},1024),n(q,{name:"createdAt"},{default:e(()=>[d(u(t.createdAt.formatted),1)]),_:2},1024),n(q,{name:"action"},{default:e(()=>[n(A,null,{default:e(()=>[n(F,{icon:"fas fa-arrow-circle-right",onClick:i=>a(h).push({name:"ResourceSyllabusShow",params:{uuid:t.uuid}})},{default:e(()=>[d(u(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(y)("syllabus:edit")&&t.isEditable?(c(),f(F,{key:0,icon:"fas fa-edit",onClick:i=>a(h).push({name:"ResourceSyllabusEdit",params:{uuid:t.uuid}})},{default:e(()=>[d(u(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):v("",!0),a(y)("syllabus:create")?(c(),f(F,{key:1,icon:"fas fa-copy",onClick:i=>a(h).push({name:"ResourceSyllabusDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[d(u(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):v("",!0),a(y)("syllabus:delete")&&t.isDeletable?(c(),f(F,{key:2,icon:"fas fa-trash",onClick:i=>a(m).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[d(u(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
