import{u as I,G as q,H as w,l as E,r as i,q as S,o as y,w as V,d as r,b as P,e as a,f as l,s as O,t as k,a as F,F as C,I as M,J as _}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},z={class:"col-span-2 sm:col-span-2"},K={class:"col-span-2 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"mt-4 grid grid-cols-4 gap-6"},h={class:"col-span-4 sm:col-span-1"},ee={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},se={class:"col-span-4 sm:col-span-1"},te={class:"mt-4 grid grid-cols-1 gap-4"},oe={class:"col"},le={class:"col"},re={name:"CalendarEventForm"},ae=Object.assign(re,{setup(N){const b=I(),d={type:"",title:"",startDate:"",startTime:"",endDate:"",endTime:"",incharge:"",isPublic:!1,forAlumni:!1,sessions:[],periods:[],studentAudienceType:"",employeeAudienceType:"",studentAudiences:[],employeeAudiences:[],venue:"",description:"",media:[],mediaUpdated:!1,mediaToken:q(),mediaHash:[]},B="calendar/event/",o=w(B),u=E({types:[],sessions:[],periods:[],studentAudienceTypes:[],employeeAudienceTypes:[]}),s=E({...d}),p=E({studentAudiences:[],employeeAudiences:[],isLoaded:!b.params.uuid}),R=t=>{Object.assign(u,t)},j=()=>{s.mediaToken=q(),s.mediaHash=[]},L=t=>{var A,m,T,f,U,g,$,D,e,H;let n=t.audiences.filter(c=>c.type=="student").map(c=>c.uuid),v=t.audiences.filter(c=>c.type=="employee").map(c=>c.uuid);Object.assign(d,{...t,type:((A=t.type)==null?void 0:A.uuid)||"",startDate:t.startDate.value,startTime:((m=t.startTime)==null?void 0:m.at)||"",endDate:((T=t.endDate)==null?void 0:T.value)||"",endTime:((f=t.endTime)==null?void 0:f.at)||"",studentAudienceType:((U=t.studentAudienceType)==null?void 0:U.value)||"",employeeAudienceType:((g=t.employeeAudienceType)==null?void 0:g.value)||"",studentAudiences:n,employeeAudiences:v,incharge:((D=($=t.incharges[0])==null?void 0:$.employee)==null?void 0:D.uuid)||"",forAlumni:t.forAlumni||!1,sessions:t.sessions||[],periods:t.periods||[]}),Object.assign(s,_(d)),p.studentAudiences=n,p.employeeAudiences=v,p.incharge=((H=(e=t.incharges[0])==null?void 0:e.employee)==null?void 0:H.uuid)||"",p.isLoaded=!0};return(t,n)=>{const v=i("BaseSelect"),A=i("BaseInput"),m=i("DatePicker"),T=i("BaseSelectSearch"),f=i("BaseSwitch"),U=i("AudienceInput"),g=i("BaseEditor"),$=i("MediaUpload"),D=i("FormAction");return y(),S(D,{"pre-requisites":!0,onSetPreRequisites:R,"init-url":B,"init-form":d,form:s,"set-form":L,redirect:"CalendarEvent",onResetMediaFiles:j},{default:V(()=>[r("div",G,[r("div",J,[a(v,{modelValue:s.type,"onUpdate:modelValue":n[0]||(n[0]=e=>s.type=e),name:"type",label:t.$trans("calendar.event.props.type"),options:u.types,"label-prop":"name","value-prop":"uuid",error:l(o).type,"onUpdate:error":n[1]||(n[1]=e=>l(o).type=e)},null,8,["modelValue","label","options","error"])]),r("div",z,[a(A,{type:"text",modelValue:s.title,"onUpdate:modelValue":n[2]||(n[2]=e=>s.title=e),name:"title",label:t.$trans("calendar.event.props.title"),error:l(o).title,"onUpdate:error":n[3]||(n[3]=e=>l(o).title=e),autofocus:""},null,8,["modelValue","label","error"])]),r("div",K,[a(A,{type:"text",modelValue:s.venue,"onUpdate:modelValue":n[4]||(n[4]=e=>s.venue=e),name:"venue",label:t.$trans("calendar.event.props.venue"),error:l(o).venue,"onUpdate:error":n[5]||(n[5]=e=>l(o).venue=e),autofocus:""},null,8,["modelValue","label","error"])]),r("div",Q,[a(m,{as:"date",modelValue:s.startDate,"onUpdate:modelValue":n[6]||(n[6]=e=>s.startDate=e),name:"startDate",label:t.$trans("calendar.event.props.start_date"),"no-clear":"",error:l(o).startDate,"onUpdate:error":n[7]||(n[7]=e=>l(o).startDate=e)},null,8,["modelValue","label","error"])]),r("div",W,[a(m,{as:"time",modelValue:s.startTime,"onUpdate:modelValue":n[8]||(n[8]=e=>s.startTime=e),name:"startTime",label:t.$trans("calendar.event.props.start_time"),error:l(o).startTime,"onUpdate:error":n[9]||(n[9]=e=>l(o).startTime=e)},null,8,["modelValue","label","error"])]),r("div",X,[a(m,{as:"date",modelValue:s.endDate,"onUpdate:modelValue":n[10]||(n[10]=e=>s.endDate=e),name:"endDate",label:t.$trans("calendar.event.props.end_date"),error:l(o).endDate,"onUpdate:error":n[11]||(n[11]=e=>l(o).endDate=e)},null,8,["modelValue","label","error"])]),r("div",Y,[a(m,{as:"time",modelValue:s.endTime,"onUpdate:modelValue":n[12]||(n[12]=e=>s.endTime=e),name:"endTime",label:t.$trans("calendar.event.props.end_time"),error:l(o).endTime,"onUpdate:error":n[13]||(n[13]=e=>l(o).endTime=e)},null,8,["modelValue","label","error"])]),r("div",Z,[p.isLoaded?(y(),S(T,{key:0,name:"incharge",label:t.$trans("global.select",{attribute:t.$trans("employee.incharge.incharge")}),modelValue:s.incharge,"onUpdate:modelValue":n[14]||(n[14]=e=>s.incharge=e),error:l(o).incharge,"onUpdate:error":n[15]||(n[15]=e=>l(o).incharge=e),"value-prop":"uuid","init-search":p.incharge,"search-key":"name","search-action":"employee/list"},{selectedOption:V(e=>[O(k(e.value.name)+" ("+k(e.value.codeNumber)+") ",1)]),listOption:V(e=>[O(k(e.option.name)+" ("+k(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):P("",!0)])]),r("div",x,[r("div",h,[a(f,{vertical:"",modelValue:s.isPublic,"onUpdate:modelValue":n[16]||(n[16]=e=>s.isPublic=e),name:"isPublic",label:t.$trans("calendar.event.props.is_public"),error:l(o).isPublic,"onUpdate:error":n[17]||(n[17]=e=>l(o).isPublic=e)},null,8,["modelValue","label","error"])]),s.isPublic?P("",!0):(y(),F(C,{key:0},[r("div",ee,[a(f,{vertical:"",modelValue:s.forAlumni,"onUpdate:modelValue":n[18]||(n[18]=e=>s.forAlumni=e),name:"forAlumni",label:t.$trans("calendar.event.props.for_alumni"),error:l(o).forAlumni,"onUpdate:error":n[19]||(n[19]=e=>l(o).forAlumni=e)},null,8,["modelValue","label","error"])]),s.forAlumni?(y(),F(C,{key:0},[r("div",ne,[a(v,{multiple:"",modelValue:s.sessions,"onUpdate:modelValue":n[20]||(n[20]=e=>s.sessions=e),name:"sessions",label:t.$trans("academic.session.session"),"label-prop":"name","value-prop":"uuid",options:u.sessions,error:l(o).sessions,"onUpdate:error":n[21]||(n[21]=e=>l(o).sessions=e)},null,8,["modelValue","label","options","error"])]),r("div",se,[a(v,{multiple:"",modelValue:s.periods,"onUpdate:modelValue":n[22]||(n[22]=e=>s.periods=e),name:"periods",label:t.$trans("academic.period.period"),"label-prop":"name","value-prop":"uuid",options:u.periods,error:l(o).periods,"onUpdate:error":n[23]||(n[23]=e=>l(o).periods=e)},null,8,["modelValue","label","options","error"])])],64)):P("",!0)],64))]),!s.isPublic&&p.isLoaded?(y(),S(U,{key:0,"pre-requisites":u,studentAudienceType:s.studentAudienceType,"onUpdate:studentAudienceType":n[24]||(n[24]=e=>s.studentAudienceType=e),employeeAudienceType:s.employeeAudienceType,"onUpdate:employeeAudienceType":n[25]||(n[25]=e=>s.employeeAudienceType=e),studentAudiences:s.studentAudiences,"onUpdate:studentAudiences":n[26]||(n[26]=e=>s.studentAudiences=e),employeeAudiences:s.employeeAudiences,"onUpdate:employeeAudiences":n[27]||(n[27]=e=>s.employeeAudiences=e),formErrors:l(o),"onUpdate:formErrors":n[28]||(n[28]=e=>M(o)?o.value=e:null)},null,8,["pre-requisites","studentAudienceType","employeeAudienceType","studentAudiences","employeeAudiences","formErrors"])):P("",!0),r("div",te,[r("div",oe,[a(g,{modelValue:s.description,"onUpdate:modelValue":n[29]||(n[29]=e=>s.description=e),name:"description",edit:!!l(b).params.uuid,label:t.$trans("calendar.event.props.description"),error:l(o).description,"onUpdate:error":n[30]||(n[30]=e=>l(o).description=e)},null,8,["modelValue","edit","label","error"])]),r("div",le,[a($,{multiple:"",label:t.$trans("general.file"),module:"event",media:s.media,"media-token":s.mediaToken,onIsUpdated:n[31]||(n[31]=e=>s.mediaUpdated=!0),onSetHash:n[32]||(n[32]=e=>s.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),ie={name:"CalendarEventAction"},ue=Object.assign(ie,{setup(N){const b=I();return(d,B)=>{const o=i("PageHeaderAction"),u=i("PageHeader"),s=i("ParentTransition");return y(),F(C,null,[a(u,{title:d.$trans(l(b).meta.trans,{attribute:d.$trans(l(b).meta.label)}),navs:[{label:d.$trans("calendar.calendar"),path:"Calendar"},{label:d.$trans("calendar.event.event"),path:"CalendarEventList"}]},{default:V(()=>[a(o,{name:"CalendarEvent",title:d.$trans("calendar.event.event"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(s,{appear:"",visibility:!0},{default:V(()=>[a(ae)]),_:1})],64)}}});export{ue as default};
