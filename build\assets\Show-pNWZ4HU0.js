import{j as C,u as z,h as G,i as J,G as re,H as Y,m as K,l as x,r,q as d,o as n,w as e,d as v,e as o,f as t,a as h,v as D,F as R,b as g,s as u,t as s,n as le,J as ue,y as W,B as P,ai as de}from"./app-BAwPsakn.js";const me={class:"grid grid-cols-3 gap-6"},ce={class:"col-span-3"},pe={class:"mt-4 grid grid-cols-1"},ge={class:"col"},_e={name:"ResourceAssignmentSubmissionForm"},be=Object.assign(_e,{props:{assignment:{type:Object,required:!0}},emits:["completed"],setup(k,{emit:B}){C("emitter");const M=z();G(),J();const $=B,a={description:"",media:[],mediaUpdated:!1,mediaToken:re(),mediaHash:[]},w="resource/assignment/",f=Y(w);K(!1);const V=x({});x({});const m=x({...a});x({isLoaded:!M.params.uuid});const A=l=>{Object.assign(V,l)},i=()=>{$("completed")};return(l,c)=>{const T=r("BaseTextarea"),F=r("MediaUpload"),j=r("FormAction");return n(),d(j,{"no-card":"","pre-requisites":!1,onSetPreRequisites:A,"init-url":w,uuid:k.assignment.uuid,"no-data-fetch":"",action:"submitAssignment","init-form":a,form:m,"keep-adding":!1,"after-submit":i,"submit-text":l.$trans("general.submit")},{default:e(()=>[v("div",me,[v("div",ce,[o(T,{rows:1,modelValue:m.description,"onUpdate:modelValue":c[0]||(c[0]=p=>m.description=p),name:"description",label:l.$trans("resource.assignment.props.description"),error:t(f).description,"onUpdate:error":c[1]||(c[1]=p=>t(f).description=p)},null,8,["modelValue","label","error"])])]),v("div",pe,[v("div",ge,[o(F,{multiple:"",label:l.$trans("general.file"),module:"assignment_submission",media:m.media,"media-token":m.mediaToken,onIsUpdated:c[2]||(c[2]=p=>m.mediaUpdated=!0),onSetHash:c[3]||(c[3]=p=>m.mediaHash.push(p))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","form","submit-text"])}}}),fe={key:0,class:"text-xs"},ye={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},X={__name:"SubmissionList",props:{assignment:{type:Object,required:!0},students:{type:Array,required:!0}},setup(k){return(B,M)=>{const $=r("BaseDataView"),a=r("ListMedia"),w=r("BaseFieldset");return n(!0),h(R,null,D(k.students,(f,V)=>(n(),d(w,null,{legend:e(()=>{var m,A;return[u(s(f.name)+" "+s(f.courseName+" "+f.batchName)+" ",1),f.submissions[0]?(n(),h("span",fe,s(B.$trans("global.submitted_at",{datetime:(A=(m=f.submissions[0])==null?void 0:m.submittedAt)==null?void 0:A.formatted})),1)):g("",!0)]}),default:e(()=>[(n(!0),h(R,null,D(f.submissions,m=>(n(),h("dl",ye,[o($,{class:"col-span-1 sm:col-span-2",label:B.$trans("resource.assignment.props.obtained_mark")},{default:e(()=>[u(s(m.obtainedMark)+" / "+s(k.assignment.maxMark),1)]),_:2},1032,["label"]),m.comment?(n(),d($,{key:0,class:"col-span-1 sm:col-span-2",label:B.$trans("resource.assignment.props.comment")},{default:e(()=>[u(s(m.comment),1)]),_:2},1032,["label"])):g("",!0),k.assignment.description?(n(),d($,{key:1,class:"col-span-1 sm:col-span-2",label:B.$trans("resource.assignment.props.description")},{default:e(()=>[u(s(m.description),1)]),_:2},1032,["label"])):g("",!0),o($,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(a,{media:m.media,url:`/app/resource/assignments/${k.assignment.uuid}/submissions/${m.uuid}/`},null,8,["media","url"])]),_:2},1024)]))),256))]),_:2},1024))),256)}}},he={key:0,class:"text-xs"},ke={class:"grid grid-cols-3 gap-6"},Se={class:"col-span-3 sm:col-span-1"},$e={class:"col-span-3 sm:col-span-2"},we={class:"mt-4 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ve={name:"ResourceAssignmentEvaluationForm"},Be=Object.assign(ve,{props:{assignment:{type:Object,required:!0},students:{type:Array,required:!0}},emits:["completed"],setup(k,{emit:B}){C("emitter");const M=z();G(),J();const $=B,a=k,w={students:[]},f="resource/assignment/",V=Y(f);K(!1);const m=x({});x({});const A=x({...w});x({isLoaded:!M.params.uuid});const i=c=>{Object.assign(m,c)},l=()=>{$("completed")};return le(()=>{a.students.forEach(c=>{w.students.push({...c})}),Object.assign(A,ue(w))}),(c,T)=>{const F=r("BaseInput"),j=r("BaseTextarea"),p=r("BaseDataView"),U=r("ListMedia"),E=r("BaseFieldset"),N=r("FormAction");return n(),d(N,{"no-card":"","pre-requisites":!1,onSetPreRequisites:i,"init-url":f,uuid:k.assignment.uuid,"no-data-fetch":"",action:"evaluateAssignment","init-form":w,form:A,"stay-on":!0,"keep-adding":!1,"after-submit":l,"submit-text":c.$trans("general.submit")},{default:e(()=>[(n(!0),h(R,null,D(A.students,(S,y)=>(n(),d(E,null,{legend:e(()=>{var b,L;return[u(s(S.name)+" "+s(S.courseName+" "+S.batchName)+" ",1),S.submissions[0]?(n(),h("span",he,s(c.$trans("global.submitted_at",{datetime:(L=(b=S.submissions[0])==null?void 0:b.submittedAt)==null?void 0:L.formatted})),1)):g("",!0)]}),default:e(()=>[v("div",ke,[v("div",Se,[o(F,{type:"text","trailing-text":`/ ${k.assignment.maxMark}`,modelValue:S.obtainedMark,"onUpdate:modelValue":b=>S.obtainedMark=b,name:`students.${y}.obtainedMark`,label:c.$trans("resource.assignment.props.obtained_mark"),error:t(V)[`students.${y}.obtainedMark`],"onUpdate:error":b=>t(V)[`students.${y}.obtainedMark`]=b},null,8,["trailing-text","modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),v("div",$e,[o(j,{rows:1,modelValue:S.comment,"onUpdate:modelValue":b=>S.comment=b,name:`students.${y}.comment`,label:c.$trans("resource.assignment.props.comment"),error:t(V)[`students.${y}.comment`],"onUpdate:error":b=>t(V)[`students.${y}.comment`]=b},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])]),(n(!0),h(R,null,D(S.submissions,b=>(n(),h("dl",we,[k.assignment.description?(n(),d(p,{key:0,class:"col-span-1 sm:col-span-2",label:c.$trans("resource.assignment.props.description")},{default:e(()=>[u(s(b.description),1)]),_:2},1032,["label"])):g("",!0),o(p,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(U,{media:b.media,url:`/app/resource/assignments/${k.assignment.uuid}/submissions/${b.uuid}/`},null,8,["media","url"])]),_:2},1024)]))),256))]),_:2},1024))),256))]),_:1},8,["uuid","form","submit-text"])}}}),Ve={class:"space-y-4"},Ae={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},xe=["src"],Me={name:"ResourceAssignmentShow"},Re=Object.assign(Me,{setup(k){const B=J(),M=z(),$=G(),a=C("$trans"),w=C("emitter"),f={},V="resource/assignment/",m=K(!1),A=[{key:"photo",label:"",visibility:!0},{key:"name",label:a("student.student"),visibility:!0},{key:"batch",label:a("academic.batch.batch"),visibility:!0}],i=x({...f}),l=x({showStudentSubmission:!0,showPendingSubmission:!1,hasSubmitted:!1,canEvaluate:!1,students:[],pendingStudents:[]}),c=()=>{l.showPendingSubmission=!0,l.showStudentSubmission=!1},T=()=>{l.showPendingSubmission=!1,l.showStudentSubmission=!0},F=async p=>{Object.assign(i,p),await j()},j=async()=>{m.value=!0,B.dispatch(V+"getSubmissions",{uuid:i.uuid}).then(p=>{l.canEvaluate=p.canEvaluate,l.students=p.students,l.pendingStudents=p.pendingStudents,P("student")&&(l.hasSubmitted=p.students[0].submissions.length>0)}).catch(p=>{console.log(p)}).finally(()=>{m.value=!1})};return(p,U)=>{const E=r("PageHeaderAction"),N=r("PageHeader"),S=r("TextMuted"),y=r("ListItemView"),b=r("ListContainerVertical"),L=r("BaseCard"),I=r("BaseDataView"),Z=r("ListMedia"),ee=r("ViewLog"),O=r("BaseButton"),te=r("ShowButton"),Q=r("BaseAlert"),H=r("DataCell"),se=r("DataRow"),ne=r("SimpleTable"),ae=r("DetailLayoutVertical"),oe=r("ShowItem"),ie=r("ParentTransition");return n(),h(R,null,[o(N,{title:t(a)(t(M).meta.trans,{attribute:t(a)(t(M).meta.label)}),navs:[{label:t(a)("resource.resource"),path:"Resource"},{label:t(a)("resource.assignment.assignment"),path:"ResourceAssignment"}]},{default:e(()=>[o(E,{name:"ResourceAssignment",title:t(a)("resource.assignment.assignment"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(ie,{appear:"",visibility:!0},{default:e(()=>[o(oe,{"init-url":V,uuid:t(M).params.uuid,"module-uuid":t(M).params.muuid,onSetItem:F,onRedirectTo:U[2]||(U[2]=_=>t($).push({name:"ResourceAssignment",params:{uuid:i.uuid}}))},{default:e(()=>[i.uuid?(n(),d(ae,{key:0},{detail:e(()=>[o(L,{"no-padding":"","no-content-padding":""},{title:e(()=>[u(s(t(a)("global.detail",{attribute:t(a)("resource.assignment.assignment")}))+" ",1),v("span",{class:"px-2 py-1 text-gray-50 text-xs rounded-lg mr-2",style:de(`background-color: ${i.type.color}`)},s(i.type.name),5)]),default:e(()=>[o(b,null,{default:e(()=>[o(y,{label:t(a)("academic.course.course")},{default:e(()=>[(n(!0),h(R,null,D(i.records,_=>{var q;return n(),h("div",null,[u(s(((q=_.batch.course)==null?void 0:q.name)+" "+_.batch.name)+" ",1),_.subject?(n(),d(S,{key:0},{default:e(()=>[u(s(_.subject.name),1)]),_:2},1024)):g("",!0)])}),256))]),_:1},8,["label"]),o(y,{label:t(a)("employee.employee")},{default:e(()=>{var _;return[u(s(((_=i.employee)==null?void 0:_.name)||"-")+" ",1),o(S,{block:""},{default:e(()=>{var q;return[u(s(((q=i.employee)==null?void 0:q.designation)||""),1)]}),_:1})]}),_:1},8,["label"]),o(y,{label:t(a)("resource.assignment.props.date")},{default:e(()=>[u(s(i.date.formatted),1)]),_:1},8,["label"]),o(y,{label:t(a)("resource.assignment.props.due_date")},{default:e(()=>[u(s(i.dueDate.formatted),1)]),_:1},8,["label"]),i.enableMarking?(n(),d(y,{key:0,label:t(a)("resource.assignment.props.max_mark")},{default:e(()=>[u(s(t(a)("resource.assignment.props.max_mark"))+": "+s(i.maxMark),1)]),_:1},8,["label"])):g("",!0),t(P)(["student","guardian"],"any")?g("",!0):(n(),h(R,{key:1},[o(y,{label:t(a)("general.created_at")},{default:e(()=>[u(s(i.createdAt.formatted),1)]),_:1},8,["label"]),o(y,{label:t(a)("general.updated_at")},{default:e(()=>[u(s(i.updatedAt.formatted),1)]),_:1},8,["label"])],64))]),_:1})]),_:1})]),default:e(()=>[v("div",Ve,[o(L,null,{title:e(()=>[u(s(t(a)("resource.assignment.assignment")),1)]),footer:e(()=>[o(te,null,{default:e(()=>[t(W)("assignment:edit")&&i.isEditable?(n(),d(O,{key:0,design:"primary",onClick:U[0]||(U[0]=_=>t($).push({name:"ResourceAssignmentEdit",params:{uuid:i.uuid}}))},{default:e(()=>[u(s(t(a)("general.edit")),1)]),_:1})):g("",!0)]),_:1})]),default:e(()=>[v("dl",Ae,[o(I,{class:"col-span-1 sm:col-span-2",label:t(a)("resource.assignment.props.description"),html:""},{default:e(()=>[u(s(i.description),1)]),_:1},8,["label"]),o(I,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(Z,{media:i.media,url:`/app/resource/assignments/${i.uuid}/`},null,8,["media","url"])]),_:1}),t(W)("assignment:view-log")?(n(),d(I,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(ee,{"view-logs":i.viewLogs},null,8,["view-logs"])]),_:1})):g("",!0)])]),_:1}),i.enableMarking?(n(),h(R,{key:0},[t(P)("student")&&!l.hasSubmitted?(n(),d(L,{key:0},{title:e(()=>[u(s(t(a)("resource.assignment.submission")),1)]),default:e(()=>[i.canSubmit?(n(),d(be,{key:0,assignment:i,onCompleted:U[1]||(U[1]=_=>t(w).emit("refreshItem"))},null,8,["assignment"])):(n(),d(Q,{key:1,design:"error",size:"xs"},{default:e(()=>[u(s(t(a)("resource.assignment.submission_closed")),1)]),_:1}))]),_:1})):g("",!0),t(P)("student")&&l.hasSubmitted?(n(),d(L,{key:1},{title:e(()=>[u(s(t(a)("resource.assignment.submission")),1)]),default:e(()=>[o(X,{assignment:i,students:l.students},null,8,["assignment","students"])]),_:1})):g("",!0),!t(P)(["student","guardian"],"any")&&l.showStudentSubmission?(n(),d(L,{key:2},{title:e(()=>[u(s(t(a)("resource.assignment.submissions")),1)]),action:e(()=>[l.showPendingSubmission==!1?(n(),d(O,{key:0,size:"xs",design:"primary",onClick:c},{default:e(()=>[u(s(t(a)("global.show",{attribute:t(a)("resource.assignment.pending_submissions")})),1)]),_:1})):g("",!0)]),default:e(()=>[l.students.length==0?(n(),d(Q,{key:0,design:"error",size:"xs"},{default:e(()=>[u(s(t(a)("general.errors.record_not_found")),1)]),_:1})):g("",!0),l.canEvaluate?(n(),d(Be,{key:1,assignment:i,students:l.students},null,8,["assignment","students"])):(n(),d(X,{key:2,assignment:i,students:l.students},null,8,["assignment","students"]))]),_:1})):g("",!0),l.showPendingSubmission&&!t(P)(["student","guardian"],"any")&&l.pendingStudents.length>0?(n(),d(L,{key:3,"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[u(s(t(a)("resource.assignment.pending_submissions")),1)]),action:e(()=>[l.showStudentSubmission==!1?(n(),d(O,{key:0,size:"xs",design:"primary",onClick:T},{default:e(()=>[u(s(t(a)("global.show",{attribute:t(a)("resource.assignment.submissions")})),1)]),_:1})):g("",!0)]),default:e(()=>[o(ne,{header:A},{default:e(()=>[(n(!0),h(R,null,D(l.pendingStudents,_=>(n(),d(se,null,{default:e(()=>[o(H,{name:"photo"},{default:e(()=>[v("img",{class:"h-10 w-10 rounded-full",src:_.photo,alt:""},null,8,xe)]),_:2},1024),o(H,{name:"name"},{default:e(()=>[u(s(_.name)+" ("+s(_.rollNumber||_.codeNumber)+") ",1)]),_:2},1024),o(H,{name:"batch"},{default:e(()=>[u(s(_.courseName)+" "+s(_.batchName),1)]),_:2},1024)]),_:2},1024))),256))]),_:1})]),_:1})):g("",!0)],64)):g("",!0)])]),_:1})):g("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{Re as default};
