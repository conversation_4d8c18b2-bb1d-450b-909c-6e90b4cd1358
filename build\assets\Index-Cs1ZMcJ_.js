import{u as R,j as B,l as w,H as S,n as U,r,q as F,o as V,w as d,d as p,e as n,b as A,s as k,t as f,f as l,i as H,m as C,a as q,F as D,aN as P}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-6"},N={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},E={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(y,{emit:m}){const c=R();B("moment");const v=m,_=y,i={date:"",batches:[],status:"all",output:"print"},s=w({...i}),u=S(_.initUrl),o=w({isLoaded:!c.query.batches});return U(async()=>{o.batches=c.query.batches?c.query.batches.split(","):[],o.isLoaded=!0}),(t,a)=>{const h=r("DatePicker"),b=r("BaseSelectSearch"),g=r("CustomCheckbox"),$=r("FilterForm");return V(),F($,{"init-form":i,multiple:["batches"],form:s,onHide:a[6]||(a[6]=e=>v("hide"))},{default:d(()=>[p("div",T,[p("div",N,[n(h,{modelValue:s.date,"onUpdate:modelValue":a[0]||(a[0]=e=>s.date=e),name:"date",as:"date",label:t.$trans("general.date")},null,8,["modelValue","label"])]),p("div",L,[o.isLoaded?(V(),F(b,{key:0,multiple:"",name:"batches",label:t.$trans("global.select",{attribute:t.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":a[1]||(a[1]=e=>s.batches=e),"value-prop":"uuid","init-search":o.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:d(e=>[k(f(e.value.course.nameWithTerm)+" "+f(e.value.name),1)]),listOption:d(e=>[k(f(e.option.course.nameWithTerm)+" "+f(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):A("",!0)]),p("div",W,[n(g,{label:t.$trans("resource.status"),options:[{label:t.$trans("general.all"),value:"all"},{label:t.$trans("resource.submitted"),value:"submitted"},{label:t.$trans("resource.not_submitted"),value:"not_submitted"}],modelValue:s.status,"onUpdate:modelValue":a[2]||(a[2]=e=>s.status=e),error:l(u).status,"onUpdate:error":a[3]||(a[3]=e=>l(u).status=e)},null,8,["label","options","modelValue","error"])]),p("div",j,[n(g,{label:t.$trans("general.action"),options:[{label:t.$trans("general.print"),value:"print"},{label:t.$trans("general.pdf"),value:"pdf"}],modelValue:s.output,"onUpdate:modelValue":a[4]||(a[4]=e=>s.output=e),error:l(u).output,"onUpdate:error":a[5]||(a[5]=e=>l(u).output=e)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},O={name:"ResourceReportDateWiseAssignment"},Q=Object.assign(O,{setup(y){const m=R();H();let c=["filter"],v=[];const _="resource/report/",i=C(!0),s=C(!1),u=async()=>{let o="/app/resource/reports/date-wise-assignment/export",t=m.query;window.open(P(o,t),"_blank").focus()};return U(async()=>{}),(o,t)=>{const a=r("PageHeaderAction"),h=r("PageHeader"),b=r("ParentTransition"),g=r("BaseCard");return V(),q(D,null,[n(h,{title:o.$trans(l(m).meta.label),navs:[{label:o.$trans("resource.resource"),path:"Resource"},{label:o.$trans("resource.report.report"),path:"ResourceReport"}]},{default:d(()=>[n(a,{url:"resource/reports/date-wise-assignment/",name:"ResourceReportDateWiseAssignment",title:o.$trans("resource.report.date_wise_assignment.date_wise_assignment"),actions:l(c),"dropdown-actions":l(v),onToggleFilter:t[0]||(t[0]=$=>i.value=!0)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),n(b,{appear:"",visibility:i.value},{default:d(()=>[n(E,{onAfterFilter:u,"init-url":_,onHide:t[1]||(t[1]=$=>i.value=!1)})]),_:1},8,["visibility"]),n(b,{appear:"",visibility:!0},{default:d(()=>[n(g,{"no-padding":"","no-content-padding":"","is-loading":s.value},null,8,["is-loading"])]),_:1})],64)}}});export{Q as default};
