import{h as L,i as A,j as H,m as _,l as M,r as t,q as f,o as m,w as e,e as a,d as g,f as k,a as R,F as z,v as N,s as l,t as r}from"./app-BAwPsakn.js";const S={class:"mt-4"},V={name:"BackupList"},x=Object.assign(V,{setup(O){L();const b=A(),u=H("emitter"),h="utility/backup/",c=_(!1),p=_(!1),i=M({}),v=n=>{Object.assign(i,n)},y=()=>{c.value=!0,b.dispatch("utility/backup/generateBackup").then(()=>{c.value=!1,u.emit("listItems")}).catch(n=>{c.value=!1})};return(n,s)=>{const B=t("BaseButton"),I=t("PageHeaderAction"),$=t("PageHeader"),d=t("DataCell"),w=t("FloatingMenuItem"),F=t("FloatingMenu"),C=t("DataRow"),D=t("DataTable"),P=t("BaseAlert"),T=t("ParentTransition"),j=t("ListItem");return m(),f(j,{"init-url":h,onSetItems:v},{header:e(()=>[a($,{title:n.$trans("utility.backup.backup")},{default:e(()=>[a(I,{url:"utility/backups/",name:"UtilityBackup",title:n.$trans("utility.backup.backup"),actions:[],"dropdown-actions":["print","pdf","excel"],headers:i.headers,onToggleFilter:s[0]||(s[0]=o=>p.value=!p.value)},{default:e(()=>[a(B,{design:"white",onClick:y},{default:e(()=>[l(r(n.$trans("global.generate",{attribute:n.$trans("utility.backup.backup")})),1)]),_:1})]),_:1},8,["title","headers"])]),_:1},8,["title"])]),default:e(()=>[a(T,{appear:"",visibility:!0},{default:e(()=>[a(D,{header:i.headers,meta:i.meta,module:"utility.backup",onRefresh:s[1]||(s[1]=o=>k(u).emit("listItems"))},{default:e(()=>[(m(!0),R(z,null,N(i.data,o=>(m(),f(C,{key:o.uuid},{default:e(()=>[a(d,{name:"name"},{default:e(()=>[l(r(o.name),1)]),_:2},1024),a(d,{name:"size"},{default:e(()=>[l(r(o.size),1)]),_:2},1024),a(d,{name:"action"},{default:e(()=>[a(F,null,{default:e(()=>[a(w,{icon:"fas fa-trash",onClick:U=>k(u).emit("deleteItem",{uuid:o.name})},{default:e(()=>[l(r(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"]),g("div",S,[a(P,{size:"xs",design:"info"},{default:e(()=>[l(r(n.$trans("utility.backup.info")),1)]),_:1}),s[2]||(s[2]=g("pre",{class:"mt-2 bg-primary dark:bg-dark-header p-2 rounded-md text-white"},"* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1",-1))])]),_:1})]),_:1})}}});export{x as default};
