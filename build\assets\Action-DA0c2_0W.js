import{u as v,h as F,H as S,l as _,r as l,q as V,o as y,w as c,d as i,e as m,f as s,b as I,s as f,t as g,J as N,a as O,F as q}from"./app-BAwPsakn.js";const H={class:"grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-2 sm:col-span-1"},j={class:"col-span-2 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},C={class:"col-span-3"},E={name:"AcademicProgramInchargeForm"},w=Object.assign(E,{setup(h){const d=v();F();const n={program:"",employee:"",startDate:"",endDate:"",remarks:""},b="academic/programIncharge/",t=S(b),u=_({programs:[]}),o=_({...n}),p=_({employee:"",isLoaded:!d.params.uuid}),k=r=>{u.programs=r.programs},P=r=>{Object.assign(n,{...r,startDate:r.startDate.value,endDate:r.endDate.value,program:r.program.uuid,employee:r.employee.uuid}),Object.assign(o,N(n)),p.program=r.program.uuid,p.employee=r.employee.uuid,p.isLoaded=!0};return(r,a)=>{const $=l("BaseSelect"),A=l("BaseSelectSearch"),D=l("DatePicker"),U=l("BaseTextarea"),B=l("FormAction");return y(),V(B,{"pre-requisites":!0,onSetPreRequisites:k,"init-url":b,"init-form":n,form:o,setForm:P,redirect:"AcademicProgramIncharge"},{default:c(()=>[i("div",H,[i("div",R,[m($,{name:"program",label:r.$trans("academic.program.program"),modelValue:o.program,"onUpdate:modelValue":a[0]||(a[0]=e=>o.program=e),error:s(t).program,"onUpdate:error":a[1]||(a[1]=e=>s(t).program=e),"value-prop":"uuid","label-prop":"nameWithDepartment",options:u.programs},null,8,["label","modelValue","error","options"])]),i("div",T,[p.isLoaded?(y(),V(A,{key:0,name:"employee",label:r.$trans("global.select",{attribute:r.$trans("employee.employee")}),modelValue:o.employee,"onUpdate:modelValue":a[2]||(a[2]=e=>o.employee=e),error:s(t).employee,"onUpdate:error":a[3]||(a[3]=e=>s(t).employee=e),"value-prop":"uuid","init-search":p.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:c(e=>[f(g(e.value.name)+" ("+g(e.value.codeNumber)+") ",1)]),listOption:c(e=>[f(g(e.option.name)+" ("+g(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):I("",!0)]),i("div",j,[m(D,{modelValue:o.startDate,"onUpdate:modelValue":a[4]||(a[4]=e=>o.startDate=e),name:"startDate",label:r.$trans("employee.incharge.props.start_date"),"no-clear":"",error:s(t).startDate,"onUpdate:error":a[5]||(a[5]=e=>s(t).startDate=e)},null,8,["modelValue","label","error"])]),i("div",L,[m(D,{modelValue:o.endDate,"onUpdate:modelValue":a[6]||(a[6]=e=>o.endDate=e),name:"endDate",label:r.$trans("employee.incharge.props.end_date"),"no-clear":"",error:s(t).endDate,"onUpdate:error":a[7]||(a[7]=e=>s(t).endDate=e)},null,8,["modelValue","label","error"])]),i("div",C,[m(U,{modelValue:o.remarks,"onUpdate:modelValue":a[8]||(a[8]=e=>o.remarks=e),name:"remarks",label:r.$trans("employee.incharge.props.remarks"),error:s(t).remarks,"onUpdate:error":a[9]||(a[9]=e=>s(t).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),J={name:"AcademicProgramInchargeAction"},z=Object.assign(J,{setup(h){const d=v();return(n,b)=>{const t=l("PageHeaderAction"),u=l("PageHeader"),o=l("ParentTransition");return y(),O(q,null,[m(u,{title:n.$trans(s(d).meta.trans,{attribute:n.$trans(s(d).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.program.program"),path:"AcademicProgram"},{label:n.$trans("academic.program_incharge.program_incharge"),path:"AcademicProgramInchargeList"}]},{default:c(()=>[m(t,{name:"AcademicProgramIncharge",title:n.$trans("academic.program_incharge.program_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),m(o,{appear:"",visibility:!0},{default:c(()=>[m(w)]),_:1})],64)}}});export{z as default};
