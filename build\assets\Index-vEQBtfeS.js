import{l as F,r as d,q as A,o as p,w as e,d as $,e as t,u as O,i as z,H as G,m as R,n as J,a as k,b as j,f as u,s as n,t as l,h as K,j as Q,z as W,F as M,v as L,A as U}from"./app-BAwPsakn.js";import{d as X}from"./vuedraggable.umd-BRYqknf6.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},te={__name:"Filter",emits:["hide"],setup(V,{emit:g}){const v=g,b={name:"",code:"",shortcode:"",alias:""},c=F({...b});return(w,s)=>{const m=d("BaseInput"),y=d("FilterForm");return p(),A(y,{"init-form":b,form:c,onHide:s[4]||(s[4]=f=>v("hide"))},{default:e(()=>[$("div",Y,[$("div",Z,[t(m,{type:"text",modelValue:c.name,"onUpdate:modelValue":s[0]||(s[0]=f=>c.name=f),name:"name",label:w.$trans("academic.program.props.name")},null,8,["modelValue","label"])]),$("div",x,[t(m,{type:"text",modelValue:c.code,"onUpdate:modelValue":s[1]||(s[1]=f=>c.code=f),name:"code",label:w.$trans("academic.program.props.code")},null,8,["modelValue","label"])]),$("div",ee,[t(m,{type:"text",modelValue:c.shortcode,"onUpdate:modelValue":s[2]||(s[2]=f=>c.shortcode=f),name:"shortcode",label:w.$trans("academic.program.props.shortcode")},null,8,["modelValue","label"])]),$("div",ae,[t(m,{type:"text",modelValue:c.alias,"onUpdate:modelValue":s[3]||(s[3]=f=>c.alias=f),name:"alias",label:w.$trans("academic.program.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},oe={key:0},se={class:"flex border rounded-xl px-4 py-2"},ne={key:1},le={key:2,class:"mt-4 flex justify-end"},re={name:"AcademicProgramReorder"},ie=Object.assign(re,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(V,{emit:g}){O();const v=z(),b=g,c={programs:[]};G("academic/program/");const s=R(!1),m=F({programs:[]});F({...c});const y=async()=>{s.value=!0,await v.dispatch("academic/program/list",{params:{all:!0}}).then(a=>{s.value=!1,m.programs=a}).catch(a=>{s.value=!1})},f=async()=>{s.value=!0,await v.dispatch("academic/program/reorder",{data:{programs:m.programs}}).then(a=>{s.value=!1,b("refresh"),b("close")}).catch(a=>{s.value=!1})},r=()=>{b("close")};return J(()=>{y()}),(a,h)=>{const I=d("BaseLabel"),D=d("BaseAlert"),P=d("BaseButton"),C=d("BaseModal");return p(),A(C,{show:V.visibility,onClose:r},{title:e(()=>[n(l(a.$trans("global.reorder",{attribute:a.$trans("academic.program.program")})),1)]),default:e(()=>[m.programs.length?(p(),k("div",oe,[t(u(X),{class:"space-y-2",list:m.programs,"item-key":"uuid"},{item:e(({element:_,index:B})=>[$("div",se,[h[0]||(h[0]=$("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(I,null,{default:e(()=>[n(l(_.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(p(),k("div",ne,[t(D,{design:"info",size:"xs"},{default:e(()=>[n(l(a.$trans("general.errors.record_not_found")),1)]),_:1})])),m.programs.length?(p(),k("div",le,[t(P,{onClick:f},{default:e(()=>[n(l(a.$trans("general.reorder")),1)]),_:1})])):j("",!0)]),_:1},8,["show"])}}}),de={key:0,class:"far fa-lg fa-check-circle text-success"},me={name:"AcademicProgramList"},pe=Object.assign(me,{setup(V){const g=K(),v=Q("emitter");let b=["create","filter"],c=["print","pdf","excel"];const w="academic/program/",s=R(!1),m=R(!1),y=F({}),f=r=>{Object.assign(y,r)};return(r,a)=>{const h=d("BaseButton"),I=d("PageHeaderAction"),D=d("PageHeader"),P=d("ParentTransition"),C=d("TextMuted"),_=d("DataCell"),B=d("FloatingMenuItem"),S=d("FloatingMenu"),N=d("DataRow"),q=d("DataTable"),E=d("ListItem"),T=W("tooltip");return p(),k(M,null,[t(E,{"init-url":w,"additional-query":{details:!0},onSetItems:f},{header:e(()=>[t(D,{title:r.$trans("academic.program.program"),navs:[{label:r.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(I,{url:"academic/programs/",name:"AcademicProgram",title:r.$trans("academic.program.program"),actions:u(b),"dropdown-actions":u(c),"additional-dropdown-actions-query":{details:!0},onToggleFilter:a[3]||(a[3]=o=>s.value=!s.value)},{default:e(()=>[U((p(),A(h,{design:"white",onClick:a[0]||(a[0]=o=>m.value=!m.value)},{default:e(()=>a[10]||(a[10]=[$("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[T,r.$trans("global.reorder",{attribute:r.$trans("academic.program.program")})]]),U((p(),A(h,{design:"white",onClick:a[1]||(a[1]=o=>u(g).push({name:"AcademicProgramType"}))},{default:e(()=>a[11]||(a[11]=[$("i",{class:"fas fa-sitemap"},null,-1)])),_:1})),[[T,r.$trans("academic.program_type.program_type")]]),t(h,{design:"white",onClick:a[2]||(a[2]=o=>u(g).push({name:"AcademicProgramIncharge"}))},{default:e(()=>[n(l(r.$trans("employee.incharge.incharge")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(P,{appear:"",visibility:s.value},{default:e(()=>[t(te,{onRefresh:a[4]||(a[4]=o=>u(v).emit("listItems")),onHide:a[5]||(a[5]=o=>s.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(P,{appear:"",visibility:!0},{default:e(()=>[t(q,{header:y.headers,meta:y.meta,module:"academic.program",onRefresh:a[7]||(a[7]=o=>u(v).emit("listItems"))},{actionButton:e(()=>[t(h,{onClick:a[6]||(a[6]=o=>u(g).push({name:"AcademicProgramCreate"}))},{default:e(()=>[n(l(r.$trans("global.add",{attribute:r.$trans("academic.program.program")})),1)]),_:1})]),default:e(()=>[(p(!0),k(M,null,L(y.data,o=>(p(),A(N,{key:o.uuid,onDoubleClick:i=>u(g).push({name:"AcademicProgramShow",params:{uuid:o.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[n(l(o.name)+" ",1),t(C,{block:""},{default:e(()=>{var i;return[n(l((i=o.type)==null?void 0:i.name),1)]}),_:2},1024)]),_:2},1024),t(_,{name:"department"},{default:e(()=>{var i;return[n(l(((i=o.department)==null?void 0:i.name)||"-"),1)]}),_:2},1024),t(_,null,{default:e(()=>[o.enableRegistration?(p(),k("i",de)):j("",!0)]),_:2},1024),t(_,{name:"code"},{default:e(()=>[n(l(o.code)+" ",1),t(C,{block:""},{default:e(()=>[n(l(o.shortcode),1)]),_:2},1024)]),_:2},1024),t(_,{name:"alias"},{default:e(()=>[n(l(o.alias),1)]),_:2},1024),t(_,{name:"incharge"},{default:e(()=>[(p(!0),k(M,null,L(o.incharges,i=>{var H;return p(),k("div",null,[n(l(((H=i==null?void 0:i.employee)==null?void 0:H.name)||"-")+" ",1),t(C,{block:""},{default:e(()=>[n(l(i==null?void 0:i.period),1)]),_:2},1024)])}),256))]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[n(l(o.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(S,null,{default:e(()=>[t(B,{icon:"fas fa-arrow-circle-right",onClick:i=>u(g).push({name:"AcademicProgramShow",params:{uuid:o.uuid}})},{default:e(()=>[n(l(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-edit",onClick:i=>u(g).push({name:"AcademicProgramEdit",params:{uuid:o.uuid}})},{default:e(()=>[n(l(r.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-copy",onClick:i=>u(g).push({name:"AcademicProgramDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[n(l(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-trash",onClick:i=>u(v).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[n(l(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),t(ie,{visibility:m.value,onClose:a[8]||(a[8]=o=>m.value=!1),onRefresh:a[9]||(a[9]=o=>u(v).emit("listItems"))},null,8,["visibility"])],64)}}});export{pe as default};
