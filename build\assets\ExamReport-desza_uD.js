import{u as E,i as H,j as A,m as N,l as V,n as F,r as e,a as i,o as t,q as l,b as f,e as c,w as a,f as n,F as m,v as h,s as g,t as b}from"./app-BAwPsakn.js";const L={name:"StudentShowExamReport"},q=Object.assign(L,{props:{student:{type:Object,default(){return{}}}},setup(o){const v=E(),w=H(),s=A("$trans"),y=o,x="student/",u=N(!1),r=V({rows:[],header:[]});let S=[];const k=async()=>{u.value=!0,await w.dispatch(x+"fetchExamReport",{uuid:y.student.uuid}).then(d=>{u.value=!1,r.rows=d.rows,r.header=d.header}).catch(d=>{u.value=!1})};return F(async()=>{await k()}),(d,O)=>{const C=e("PageHeaderAction"),P=e("PageHeader"),R=e("DataCell"),B=e("DataRow"),D=e("SimpleTable"),T=e("BaseCard"),j=e("ParentTransition");return t(),i(m,null,[o.student.uuid?(t(),l(P,{key:0,title:n(s)(n(v).meta.label),navs:[{label:n(s)("student.student"),path:"Student"},{label:o.student.contact.name,path:{name:"StudentShow",params:{uuid:o.student.uuid}}}]},{default:a(()=>[c(C,{"additional-actions":n(S)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):f("",!0),c(j,{appear:"",visibility:!0},{default:a(()=>[o.student.uuid?(t(),l(T,{key:0,"is-loading":u.value,"no-padding":"","no-content-padding":""},{title:a(()=>[g(b(n(s)("global.overview",{attribute:n(s)("exam.exam")})),1)]),default:a(()=>[c(D,{header:r.header},{default:a(()=>[(t(!0),i(m,null,h(r.rows,p=>(t(),l(B,{key:p.uuid},{default:a(()=>[(t(!0),i(m,null,h(p,_=>(t(),l(R,{key:_.key},{default:a(()=>[g(b(_.label),1)]),_:2},1024))),128))]),_:2},1024))),128))]),_:1},8,["header"])]),_:1},8,["is-loading"])):f("",!0)]),_:1})],64)}}});export{q as default};
