import{u as f,h as U,H as F,l as v,r as m,q as _,o as h,w as i,d as p,b as V,f as n,s as b,t as c,e as d,J as O,a as I,F as N}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},S={class:"col-span-2 sm:col-span-1"},j={class:"col-span-2 sm:col-span-1"},P={class:"col-span-3"},C={name:"AcademicBatchInchargeForm"},E=Object.assign(C,{setup(k){const u=f();U();const s={batch:"",employee:"",startDate:"",endDate:"",remarks:""},g="academic/batchIncharge/",o=F(g),t=v({...s}),l=v({batch:"",employee:"",isLoaded:!u.params.uuid}),B=r=>{Object.assign(s,{...r,startDate:r.startDate.value,endDate:r.endDate.value,batch:r.batch.uuid,employee:r.employee.uuid}),Object.assign(t,O(s)),l.batch=r.batch.uuid,l.employee=r.employee.uuid,l.isLoaded=!0};return(r,a)=>{const y=m("BaseSelectSearch"),D=m("DatePicker"),$=m("BaseTextarea"),A=m("FormAction");return h(),_(A,{"init-url":g,"init-form":s,form:t,setForm:B,redirect:"AcademicBatchIncharge"},{default:i(()=>[p("div",T,[p("div",H,[l.isLoaded?(h(),_(y,{key:0,name:"batch",label:r.$trans("academic.batch.batch"),modelValue:t.batch,"onUpdate:modelValue":a[0]||(a[0]=e=>t.batch=e),error:n(o).batch,"onUpdate:error":a[1]||(a[1]=e=>n(o).batch=e),"value-prop":"uuid","init-search":l.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:i(e=>[b(c(e.value.course.name)+" - "+c(e.value.name),1)]),listOption:i(e=>[b(c(e.option.course.nameWithTerm)+" - "+c(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):V("",!0)]),p("div",L,[l.isLoaded?(h(),_(y,{key:0,name:"employee",label:r.$trans("global.select",{attribute:r.$trans("employee.employee")}),modelValue:t.employee,"onUpdate:modelValue":a[2]||(a[2]=e=>t.employee=e),error:n(o).employee,"onUpdate:error":a[3]||(a[3]=e=>n(o).employee=e),"value-prop":"uuid","init-search":l.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:i(e=>[b(c(e.value.name)+" ("+c(e.value.codeNumber)+") ",1)]),listOption:i(e=>[b(c(e.option.name)+" ("+c(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):V("",!0)]),p("div",S,[d(D,{modelValue:t.startDate,"onUpdate:modelValue":a[4]||(a[4]=e=>t.startDate=e),name:"startDate",label:r.$trans("employee.incharge.props.start_date"),"no-clear":"",error:n(o).startDate,"onUpdate:error":a[5]||(a[5]=e=>n(o).startDate=e)},null,8,["modelValue","label","error"])]),p("div",j,[d(D,{modelValue:t.endDate,"onUpdate:modelValue":a[6]||(a[6]=e=>t.endDate=e),name:"endDate",label:r.$trans("employee.incharge.props.end_date"),"no-clear":"",error:n(o).endDate,"onUpdate:error":a[7]||(a[7]=e=>n(o).endDate=e)},null,8,["modelValue","label","error"])]),p("div",P,[d($,{modelValue:t.remarks,"onUpdate:modelValue":a[8]||(a[8]=e=>t.remarks=e),name:"remarks",label:r.$trans("employee.incharge.props.remarks"),error:n(o).remarks,"onUpdate:error":a[9]||(a[9]=e=>n(o).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),w={name:"AcademicBatchInchargeAction"},q=Object.assign(w,{setup(k){const u=f();return(s,g)=>{const o=m("PageHeaderAction"),t=m("PageHeader"),l=m("ParentTransition");return h(),I(N,null,[d(t,{title:s.$trans(n(u).meta.trans,{attribute:s.$trans(n(u).meta.label)}),navs:[{label:s.$trans("academic.academic"),path:"Academic"},{label:s.$trans("academic.batch.batch"),path:"AcademicBatch"},{label:s.$trans("academic.batch_incharge.batch_incharge"),path:"AcademicBatchInchargeList"}]},{default:i(()=>[d(o,{name:"AcademicBatchIncharge",title:s.$trans("academic.batch_incharge.batch_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(l,{appear:"",visibility:!0},{default:i(()=>[d(E)]),_:1})],64)}}});export{q as default};
