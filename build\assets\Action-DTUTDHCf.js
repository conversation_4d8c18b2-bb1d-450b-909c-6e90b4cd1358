import{u as $,H as C,l as _,r as p,q as P,o as d,w as y,d as i,a as b,b as c,e as r,f as s,F as g,J as U}from"./app-BAwPsakn.js";const k={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},D={key:2,class:"col-span-3 sm:col-span-2"},I={class:"col-span-3 sm:col-span-1"},T={name:"CustomFieldForm"},J=Object.assign(T,{setup(F){const f=$(),a={form:"",type:"",label:"",minLength:"",maxLength:"",minValue:"",maxValue:"",options:"",isRequired:!1,position:0},V="customField/",l=C(V),u=_({forms:[],types:[]}),t=_({...a}),L=_({isLoaded:!f.params.uuid}),q=n=>{Object.assign(u,n),Object.assign(t,U(a))},x=n=>{Object.assign(a,{...n,form:n.form.value,type:n.type.value}),Object.assign(t,U(a)),L.isLoaded=!0};return(n,e)=>{const v=p("BaseSelect"),B=p("BaseSwitch"),m=p("BaseInput"),R=p("FormAction");return d(),P(R,{"pre-requisites":!0,onSetPreRequisites:q,"init-url":V,"init-form":a,form:t,"set-form":x,redirect:"CustomField"},{default:y(()=>[i("div",k,[i("div",j,[r(v,{modelValue:t.form,"onUpdate:modelValue":e[0]||(e[0]=o=>t.form=o),name:"form",options:u.forms,label:n.$trans("custom_field.props.form"),error:s(l).form,"onUpdate:error":e[1]||(e[1]=o=>s(l).form=o)},null,8,["modelValue","options","label","error"])]),i("div",O,[r(v,{modelValue:t.type,"onUpdate:modelValue":e[2]||(e[2]=o=>t.type=o),name:"type",options:u.types,label:n.$trans("custom_field.props.type"),error:s(l).type,"onUpdate:error":e[3]||(e[3]=o=>s(l).type=o)},null,8,["modelValue","options","label","error"])]),i("div",A,[r(B,{vertical:"",modelValue:t.isRequired,"onUpdate:modelValue":e[4]||(e[4]=o=>t.isRequired=o),name:"is_required",label:n.$trans("custom_field.props.is_required"),error:s(l).isRequired,"onUpdate:error":e[5]||(e[5]=o=>s(l).isRequired=o)},null,8,["modelValue","label","error"])]),i("div",H,[r(m,{type:"text",modelValue:t.label,"onUpdate:modelValue":e[6]||(e[6]=o=>t.label=o),name:"label",label:n.$trans("custom_field.props.label"),error:s(l).label,"onUpdate:error":e[7]||(e[7]=o=>s(l).label=o)},null,8,["modelValue","label","error"])]),t.type=="text_input"||t.type=="multi_line_text_input"?(d(),b(g,{key:0},[i("div",S,[r(m,{type:"text",modelValue:t.minLength,"onUpdate:modelValue":e[8]||(e[8]=o=>t.minLength=o),name:"minLength",label:n.$trans("custom_field.props.min_length"),error:s(l).minLength,"onUpdate:error":e[9]||(e[9]=o=>s(l).minLength=o)},null,8,["modelValue","label","error"])]),i("div",w,[r(m,{type:"text",modelValue:t.maxLength,"onUpdate:modelValue":e[10]||(e[10]=o=>t.maxLength=o),name:"maxLength",label:n.$trans("custom_field.props.max_length"),error:s(l).maxLength,"onUpdate:error":e[11]||(e[11]=o=>s(l).maxLength=o)},null,8,["modelValue","label","error"])])],64)):c("",!0),t.type=="number_input"||t.type=="currency_input"?(d(),b(g,{key:1},[i("div",E,[r(m,{type:"number",modelValue:t.minValue,"onUpdate:modelValue":e[12]||(e[12]=o=>t.minValue=o),name:"minValue",label:n.$trans("custom_field.props.min_value"),error:s(l).minValue,"onUpdate:error":e[13]||(e[13]=o=>s(l).minValue=o)},null,8,["modelValue","label","error"])]),i("div",N,[r(m,{type:"number",modelValue:t.maxValue,"onUpdate:modelValue":e[14]||(e[14]=o=>t.maxValue=o),name:"maxValue",label:n.$trans("custom_field.props.max_value"),error:s(l).maxValue,"onUpdate:error":e[15]||(e[15]=o=>s(l).maxValue=o)},null,8,["modelValue","label","error"])])],64)):c("",!0),t.type=="select_input"||t.type=="multi_select_input"||t.type=="checkbox_input"||t.type=="radio_input"?(d(),b("div",D,[r(m,{type:"text",modelValue:t.options,"onUpdate:modelValue":e[16]||(e[16]=o=>t.options=o),name:"options",label:n.$trans("custom_field.props.options"),error:s(l).options,"onUpdate:error":e[17]||(e[17]=o=>s(l).options=o),"label-hint":n.$trans("custom_field.option_info")},null,8,["modelValue","label","error","label-hint"])])):c("",!0),i("div",I,[r(m,{type:"number",modelValue:t.position,"onUpdate:modelValue":e[18]||(e[18]=o=>t.position=o),name:"position",label:n.$trans("custom_field.props.position"),error:s(l).position,"onUpdate:error":e[19]||(e[19]=o=>s(l).position=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),z={name:"CustomFieldAction"},K=Object.assign(z,{setup(F){const f=$();return(a,V)=>{const l=p("PageHeaderAction"),u=p("PageHeader"),t=p("ParentTransition");return d(),b(g,null,[r(u,{title:a.$trans(s(f).meta.trans,{attribute:a.$trans(s(f).meta.label)}),navs:[{label:a.$trans("custom_field.custom_field"),path:"CustomField"}]},{default:y(()=>[r(l,{name:"CustomField",title:a.$trans("custom_field.custom_field"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(t,{appear:"",visibility:!0},{default:y(()=>[r(J)]),_:1})],64)}}});export{K as default};
