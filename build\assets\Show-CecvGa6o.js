import{i as A,u as H,h as I,l as M,r as i,a as u,o,e as s,w as a,f,q as m,b as c,d as p,s as l,t as n,F as g,v,y as z}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},O={class:"flex"},L={class:"font-bold"},q={class:"ml-2"},U={class:"flex"},G={class:"font-bold"},J={class:"ml-2"},K={key:0},Q={name:"FinanceTransactionShow"},Y=Object.assign(Q,{setup(W){A();const h=H(),$=I(),T={},j="finance/transaction/",e=M({...T}),w=t=>{Object.assign(e,t)};return(t,b)=>{const C=i("PageHeaderAction"),F=i("PageHeader"),y=i("BaseBadge"),k=i("TextMuted"),r=i("BaseDataView"),P=i("PaymentMethodDetail"),R=i("BaseButton"),S=i("ShowButton"),D=i("BaseCard"),V=i("ShowItem"),N=i("ParentTransition");return o(),u(g,null,[s(F,{title:t.$trans(f(h).meta.trans,{attribute:t.$trans(f(h).meta.label)}),navs:[{label:t.$trans("finance.finance"),path:"Finance"},{label:t.$trans("finance.transaction.transaction"),path:"FinanceTransactionList"}]},{default:a(()=>[s(C,{name:"FinanceTransaction",title:t.$trans("finance.transaction.transaction"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(N,{appear:"",visibility:!0},{default:a(()=>[s(V,{"init-url":j,uuid:f(h).params.uuid,onSetItem:w,onRedirectTo:b[1]||(b[1]=d=>f($).push({name:"FinanceTransaction"}))},{default:a(()=>[e.uuid?(o(),m(D,{key:0},{title:a(()=>[l(n(e.codeNumber)+" ("+n(e.type.label)+") ",1),e.isRejected?(o(),m(y,{key:0,design:"warning",size:"sm"},{default:a(()=>[l(n(t.$trans("general.rejected")),1)]),_:1})):c("",!0),e.isCancelled?(o(),m(y,{key:1,design:"danger",size:"sm"},{default:a(()=>[l(n(t.$trans("general.cancelled")),1)]),_:1})):c("",!0),e.isOnline&&!e.isCompleted?(o(),m(y,{key:2,design:"danger",size:"sm"},{default:a(()=>[l(n(t.$trans("general.failed")),1)]),_:1})):c("",!0)]),footer:a(()=>[s(S,null,{default:a(()=>[f(z)("transaction:edit")&&e.isEditable?(o(),m(R,{key:0,design:"primary",onClick:b[0]||(b[0]=d=>f($).push({name:"FinanceTransactionEdit",params:{uuid:e.uuid}}))},{default:a(()=>[l(n(t.$trans("general.edit")),1)]),_:1})):c("",!0)]),_:1})]),default:a(()=>[p("dl",E,[s(r,{label:t.$trans("finance.transaction.props.date")},{default:a(()=>[l(n(e.date.formatted)+" ",1),e.head?(o(),m(k,{key:0,block:""},{default:a(()=>[l(n(e.head),1)]),_:1})):c("",!0)]),_:1},8,["label"]),s(r,{label:t.$trans("finance.transaction.props.amount")},{default:a(()=>[l(n(e.amount.formatted),1)]),_:1},8,["label"]),s(r,{label:t.$trans("finance.ledger.ledger")},{default:a(()=>[(o(!0),u(g,null,v(e.payments,d=>{var _;return o(),u("div",null,[p("div",O,[p("span",L,n((_=d.ledger)==null?void 0:_.name),1),p("span",q,n(d.amount.formatted),1)])])}),256))]),_:1},8,["label"]),s(r,{label:t.$trans("finance.payment_method.payment_method")},{default:a(()=>[s(P,{payments:e.payments},null,8,["payments"])]),_:1},8,["label"]),s(r,{class:"col-span-1 sm:col-span-2",label:t.$trans("finance.ledger.secondary_ledger")},{default:a(()=>{var d;return[(o(!0),u(g,null,v(e.records,_=>{var B;return o(),u("div",null,[p("div",U,[p("span",G,n((B=_.ledger)==null?void 0:B.name),1),p("span",J,n(_.amount.formatted),1)])])}),256)),(d=e.transactionable)!=null&&d.name?(o(),u("span",K,[l(n(e.transactionable.name)+" ",1),s(k,{block:""},{default:a(()=>[l(n(e.transactionable.contact),1)]),_:1})])):c("",!0)]}),_:1},8,["label"]),s(r,{class:"col-span-1 sm:col-span-3",label:t.$trans("finance.transaction.props.remarks")},{default:a(()=>[l(n(e.remarks),1)]),_:1},8,["label"]),e.isRejected?(o(),u(g,{key:0},[s(r,{label:t.$trans("finance.transaction.props.rejected_date")},{default:a(()=>[l(n(e.rejectedDate.formatted),1)]),_:1},8,["label"]),s(r,{label:t.$trans("finance.transaction.props.rejection_charge")},{default:a(()=>[l(n(e.rejectionCharge.formatted),1)]),_:1},8,["label"]),s(r,{label:t.$trans("finance.transaction.props.rejection_remarks"),class:"col-span-1 sm:col-span-3"},{default:a(()=>[l(n(e.rejectionRemarks),1)]),_:1},8,["label"])],64)):c("",!0),e.cancellationRemarks?(o(),m(r,{key:1,label:t.$trans("finance.transaction.props.cancellation_remarks"),class:"col-span-1 sm:col-span-3"},{default:a(()=>[l(n(e.cancellationRemarks),1)]),_:1},8,["label"])):c("",!0),s(r,{label:t.$trans("general.created_at")},{default:a(()=>[l(n(e.createdAt.formatted),1)]),_:1},8,["label"]),s(r,{label:t.$trans("general.updated_at")},{default:a(()=>[l(n(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):c("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Y as default};
