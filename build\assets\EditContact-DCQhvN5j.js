import{u as k,H as w,l as I,n as S,J as j,r as m,a as O,o as g,q as v,b as N,e as l,f as o,w as c,d,I as R,s as C,t as F,F as H}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-3 gap-6"},D={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3"},K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3"},W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"mt-4 grid grid-cols-3 gap-6"},Z={name:"EmployeeEditContact"},_=Object.assign(Z,{props:{employee:{type:Object,default(){return{}}}},setup(i){const V=k(),r=i,A={contactNumber:"",email:"",alternateRecords:{},presentAddress:{},permanentAddress:{},type:""},E="employee/",n=w(E),t=I({...A});return S(async()=>{var a,e,b,p,u,y,L,f,U,s,z,P,$,B;Object.assign(A,{contactNumber:r.employee.contact.contactNumber,email:r.employee.contact.email,alternateRecords:{contactNumber:(a=r.employee.contact.alternateRecords)==null?void 0:a.contactNumber,email:(e=r.employee.contact.alternateRecords)==null?void 0:e.email},presentAddress:{addressLine1:(b=r.employee.contact.presentAddress)==null?void 0:b.addressLine1,addressLine2:(p=r.employee.contact.presentAddress)==null?void 0:p.addressLine2,city:(u=r.employee.contact.presentAddress)==null?void 0:u.city,state:(y=r.employee.contact.presentAddress)==null?void 0:y.state,zipcode:(L=r.employee.contact.presentAddress)==null?void 0:L.zipcode,country:(f=r.employee.contact.presentAddress)==null?void 0:f.country},permanentAddress:{sameAsPresentAddress:r.employee.contact.sameAsPresentAddress,addressLine1:(U=r.employee.contact.permanentAddress)==null?void 0:U.addressLine1,addressLine2:(s=r.employee.contact.permanentAddress)==null?void 0:s.addressLine2,city:(z=r.employee.contact.permanentAddress)==null?void 0:z.city,state:(P=r.employee.contact.permanentAddress)==null?void 0:P.state,zipcode:($=r.employee.contact.permanentAddress)==null?void 0:$.zipcode,country:(B=r.employee.contact.permanentAddress)==null?void 0:B.country},type:r.employee.type.value}),Object.assign(t,j(A))}),(a,e)=>{const b=m("PageHeader"),p=m("BaseInput"),u=m("AddressInput"),y=m("BaseFieldset"),L=m("BaseSwitch"),f=m("FormAction"),U=m("ParentTransition");return g(),O(H,null,[i.employee.uuid?(g(),v(b,{key:0,title:a.$trans(o(V).meta.trans,{attribute:a.$trans(o(V).meta.label)}),navs:[{label:a.$trans("employee.employee"),path:"Employee"},{label:i.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:i.employee.uuid}}}]},null,8,["title","navs"])):N("",!0),l(U,{appear:"",visibility:!0},{default:c(()=>[i.employee.uuid?(g(),v(f,{key:0,"init-url":E,"no-data-fetch":"","init-form":A,form:t,"stay-on":"",redirect:{name:"EmployeeShowContact",params:{uuid:i.employee.uuid}}},{default:c(()=>[d("div",T,[d("div",D,[l(p,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=s=>t.contactNumber=s),name:"contactNumber",label:a.$trans("contact.props.contact_number"),error:o(n).contactNumber,"onUpdate:error":e[1]||(e[1]=s=>o(n).contactNumber=s)},null,8,["modelValue","label","error"])]),d("div",q,[l(p,{type:"text",modelValue:t.alternateRecords.contactNumber,"onUpdate:modelValue":e[2]||(e[2]=s=>t.alternateRecords.contactNumber=s),name:"alternateContactNumber",label:a.$trans("global.alternate",{attribute:a.$trans("contact.props.contact_number")}),error:o(n).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=s=>o(n).alternateContactNumber=s)},null,8,["modelValue","label","error"])]),e[24]||(e[24]=d("div",{class:"col-span-3 sm:col-span-1"},null,-1)),d("div",J,[l(p,{type:"text",modelValue:t.email,"onUpdate:modelValue":e[4]||(e[4]=s=>t.email=s),name:"email",label:a.$trans("contact.props.email"),error:o(n).email,"onUpdate:error":e[5]||(e[5]=s=>o(n).email=s)},null,8,["modelValue","label","error"])]),d("div",M,[l(p,{type:"text",modelValue:t.alternateRecords.email,"onUpdate:modelValue":e[6]||(e[6]=s=>t.alternateRecords.email=s),name:"alternateEmail",label:a.$trans("global.alternate",{attribute:a.$trans("contact.props.email")}),error:o(n).alternateEmail,"onUpdate:error":e[7]||(e[7]=s=>o(n).alternateEmail=s)},null,8,["modelValue","label","error"])]),e[25]||(e[25]=d("div",{class:"col-span-3 sm:col-span-1"},null,-1)),d("div",G,[l(y,null,{legend:c(()=>[C(F(a.$trans("contact.props.present_address")),1)]),default:c(()=>[d("div",K,[l(u,{prefix:"presentAddress",addressLine1:t.presentAddress.addressLine1,"onUpdate:addressLine1":e[8]||(e[8]=s=>t.presentAddress.addressLine1=s),addressLine2:t.presentAddress.addressLine2,"onUpdate:addressLine2":e[9]||(e[9]=s=>t.presentAddress.addressLine2=s),city:t.presentAddress.city,"onUpdate:city":e[10]||(e[10]=s=>t.presentAddress.city=s),state:t.presentAddress.state,"onUpdate:state":e[11]||(e[11]=s=>t.presentAddress.state=s),zipcode:t.presentAddress.zipcode,"onUpdate:zipcode":e[12]||(e[12]=s=>t.presentAddress.zipcode=s),country:t.presentAddress.country,"onUpdate:country":e[13]||(e[13]=s=>t.presentAddress.country=s),formErrors:o(n),"onUpdate:formErrors":e[14]||(e[14]=s=>R(n)?n.value=s:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1})]),d("div",Q,[l(y,null,{legend:c(()=>[C(F(a.$trans("contact.props.permanent_address")),1)]),default:c(()=>[d("div",W,[d("div",X,[l(L,{modelValue:t.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[15]||(e[15]=s=>t.permanentAddress.sameAsPresentAddress=s),name:"sameAsPresentAddress",label:a.$trans("contact.props.same_as_present_address"),error:o(n).sameAsPresentAddress,"onUpdate:error":e[16]||(e[16]=s=>o(n).sameAsPresentAddress=s)},null,8,["modelValue","label","error"])])]),d("div",Y,[t.permanentAddress.sameAsPresentAddress?N("",!0):(g(),v(u,{key:0,prefix:"permanentAddress",addressLine1:t.permanentAddress.addressLine1,"onUpdate:addressLine1":e[17]||(e[17]=s=>t.permanentAddress.addressLine1=s),addressLine2:t.permanentAddress.addressLine2,"onUpdate:addressLine2":e[18]||(e[18]=s=>t.permanentAddress.addressLine2=s),city:t.permanentAddress.city,"onUpdate:city":e[19]||(e[19]=s=>t.permanentAddress.city=s),state:t.permanentAddress.state,"onUpdate:state":e[20]||(e[20]=s=>t.permanentAddress.state=s),zipcode:t.permanentAddress.zipcode,"onUpdate:zipcode":e[21]||(e[21]=s=>t.permanentAddress.zipcode=s),country:t.permanentAddress.country,"onUpdate:country":e[22]||(e[22]=s=>t.permanentAddress.country=s),formErrors:o(n),"onUpdate:formErrors":e[23]||(e[23]=s=>R(n)?n.value=s:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1})])])]),_:1},8,["form","redirect"])):N("",!0)]),_:1})],64)}}});export{_ as default};
