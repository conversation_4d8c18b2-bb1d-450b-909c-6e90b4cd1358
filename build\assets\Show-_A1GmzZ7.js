import{i as V,u as j,h as H,l as I,r as o,a as c,o as u,e as t,w as a,f as d,q as g,b as m,d as y,F as h,v as M,s as l,t as n,B as N,y as U}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},E={key:0},L=["href"],F={key:0,class:"ml-1"},q={name:"ResourceOnlineClassShow"},J=Object.assign(q,{setup(z){V();const _=j(),$=H(),B={},C="resource/onlineClass/",e=I({...B}),k=s=>{Object.assign(e,s)};return(s,b)=>{const w=o("PageHeaderAction"),v=o("PageHeader"),f=o("TextMuted"),i=o("BaseDataView"),R=o("ListMedia"),O=o("BaseButton"),S=o("ShowButton"),A=o("BaseCard"),P=o("ShowItem"),T=o("ParentTransition");return u(),c(h,null,[t(v,{title:s.$trans(d(_).meta.trans,{attribute:s.$trans(d(_).meta.label)}),navs:[{label:s.$trans("resource.resource"),path:"Resource"},{label:s.$trans("resource.online_class.online_class"),path:"ResourceOnlineClass"}]},{default:a(()=>[t(w,{name:"ResourceOnlineClass",title:s.$trans("resource.online_class.online_class"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(T,{appear:"",visibility:!0},{default:a(()=>[t(P,{"init-url":C,uuid:d(_).params.uuid,"module-uuid":d(_).params.muuid,onSetItem:k,onRedirectTo:b[1]||(b[1]=r=>d($).push({name:"ResourceOnlineClass",params:{uuid:e.uuid}}))},{default:a(()=>[e.uuid?(u(),g(A,{key:0},{title:a(()=>[l(n(e.topic),1)]),footer:a(()=>[t(S,null,{default:a(()=>[d(U)("online-class:edit")&&e.isEditable?(u(),g(O,{key:0,design:"primary",onClick:b[0]||(b[0]=r=>d($).push({name:"ResourceOnlineClassEdit",params:{uuid:e.uuid}}))},{default:a(()=>[l(n(s.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:a(()=>[y("dl",D,[t(i,{label:s.$trans("academic.course.course")},{default:a(()=>[(u(!0),c(h,null,M(e.records,r=>{var p;return u(),c("div",null,[l(n(((p=r.batch.course)==null?void 0:p.name)+" "+r.batch.name)+" ",1),r.subject?(u(),g(f,{key:0},{default:a(()=>[l(n(r.subject.name),1)]),_:2},1024)):m("",!0)])}),256))]),_:1},8,["label"]),t(i,{label:s.$trans("employee.employee")},{default:a(()=>{var r;return[l(n(((r=e.employee)==null?void 0:r.name)||"-")+" ",1),t(f,{block:""},{default:a(()=>{var p;return[l(n(((p=e.employee)==null?void 0:p.designation)||""),1)]}),_:1})]}),_:1},8,["label"]),t(i,{label:s.$trans("resource.online_class.props.start_at")},{default:a(()=>[l(n(e.startAt.formatted)+" ",1),t(f,{block:""},{default:a(()=>[l(n(e.duration)+" "+n(s.$trans("list.durations.m")),1)]),_:1})]),_:1},8,["label"]),t(i,{label:s.$trans("resource.online_class.props.url"),class:"col-span-1 sm:col-span-2"},{default:a(()=>[e.showUrl?(u(),c("span",E,[y("a",{href:e.meetingUrl,target:"_blank"},[l(n(e.meetingUrl)+" ",1),e.password?(u(),c("span",F,"("+n(e.password)+")",1)):m("",!0)],8,L)])):m("",!0)]),_:1},8,["label"]),t(i,{label:s.$trans("resource.online_class.props.description"),class:"col-span-1 sm:col-span-2",html:""},{default:a(()=>[l(n(e.description),1)]),_:1},8,["label"]),t(i,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[t(R,{media:e.media,url:`/app/resource/online-classes/${e.uuid}/`},null,8,["media","url"])]),_:1}),d(N)(["student","guardian"],"any")?m("",!0):(u(),c(h,{key:0},[t(i,{label:s.$trans("general.created_at")},{default:a(()=>[l(n(e.createdAt.formatted),1)]),_:1},8,["label"]),t(i,{label:s.$trans("general.updated_at")},{default:a(()=>[l(n(e.updatedAt.formatted),1)]),_:1},8,["label"])],64))])]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{J as default};
