import{u as V,H as v,l as C,r as a,a as y,o as F,e as o,f as n,w as c,d as i,s as U,t as P,F as $}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},T={class:"mt-4"},k={name:"License"},A=Object.assign(k,{setup(w){const u=V(),m={accessCode:"",email:""},d="product/",r=v(d),s=C({...m}),_=()=>{location.reload()};return(l,e)=>{const f=a("PageHeader"),p=a("BaseInput"),g=a("BaseButton"),b=a("FormAction"),B=a("ParentTransition");return F(),y($,null,[o(f,{title:l.$trans(n(u).meta.title),navs:[]},null,8,["title"]),o(B,{appear:"",visibility:!0},{default:c(()=>[o(b,{"no-action-button":"",action:"license","init-url":d,"init-form":m,form:s,"after-submit":_},{default:c(()=>[i("div",E,[i("div",H,[o(p,{type:"text",modelValue:s.accessCode,"onUpdate:modelValue":e[0]||(e[0]=t=>s.accessCode=t),name:"accessCode",label:l.$trans("setup.license.props.access_code"),error:n(r).accessCode,"onUpdate:error":e[1]||(e[1]=t=>n(r).accessCode=t)},null,8,["modelValue","label","error"])]),i("div",N,[o(p,{type:"text",modelValue:s.email,"onUpdate:modelValue":e[2]||(e[2]=t=>s.email=t),name:"email",label:l.$trans("setup.license.props.registered_email"),error:n(r).email,"onUpdate:error":e[3]||(e[3]=t=>n(r).email=t)},null,8,["modelValue","label","error"])])]),i("div",T,[o(g,{design:"primary",type:"submit"},{default:c(()=>[U(P(l.$trans("general.proceed")),1)]),_:1})])]),_:1},8,["form"])]),_:1})],64)}}});export{A as default};
