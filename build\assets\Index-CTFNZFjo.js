import{u as W,l as q,n as G,r as m,q as _,o as r,w as t,d as c,b as h,s as d,t as s,e as l,h as J,j as K,y as $,m as Q,z as X,f as b,B as Y,a as D,F as L,v as M,ai as Z,A as ee}from"./app-BAwPsakn.js";const te={class:"grid grid-cols-3 gap-6"},se={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},ie={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(I,{emit:v}){const p=W(),C=v,R=I,V={employees:[],types:[],batches:[],subjects:[],startDate:"",endDate:"",startDueDate:"",endDueDate:""},n=q({...V}),A=q({subjects:R.preRequisites.subjects}),i=q({employees:[],types:[],batches:[],subjects:[],isLoaded:!(p.query.employees||p.query.types||p.query.batches||p.query.subjects)});return G(async()=>{i.employees=p.query.employees?p.query.employees.split(","):[],i.types=p.query.types?p.query.types.split(","):[],i.batches=p.query.batches?p.query.batches.split(","):[],i.subjects=p.query.subjects?p.query.subjects.split(","):[],i.isLoaded=!0}),(g,u)=>{const o=m("BaseSelectSearch"),y=m("BaseSelect"),B=m("DatePicker"),S=m("FilterForm");return r(),_(S,{"init-form":V,form:n,multiple:["employees","types","batches","subjects"],onHide:u[8]||(u[8]=a=>C("hide"))},{default:t(()=>[c("div",te,[c("div",se,[i.isLoaded?(r(),_(o,{key:0,multiple:"",name:"employees",label:g.$trans("global.select",{attribute:g.$trans("employee.employee")}),modelValue:n.employees,"onUpdate:modelValue":u[0]||(u[0]=a=>n.employees=a),"value-prop":"uuid","init-search":i.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:t(a=>[d(s(a.value.name)+" ("+s(a.value.codeNumber)+") ",1)]),listOption:t(a=>[d(s(a.option.name)+" ("+s(a.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),c("div",ae,[i.isLoaded?(r(),_(o,{key:0,multiple:"",name:"types",label:g.$trans("global.select",{attribute:g.$trans("resource.assignment.type.type")}),modelValue:n.types,"onUpdate:modelValue":u[1]||(u[1]=a=>n.types=a),"value-prop":"uuid","init-search":i.types,"search-action":"option/list","additional-search-query":{type:"assignment_type"}},null,8,["label","modelValue","init-search"])):h("",!0)]),c("div",ne,[i.isLoaded?(r(),_(o,{key:0,multiple:"",name:"batches",label:g.$trans("global.select",{attribute:g.$trans("academic.batch.batch")}),modelValue:n.batches,"onUpdate:modelValue":u[2]||(u[2]=a=>n.batches=a),"value-prop":"uuid","init-search":i.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(a=>[d(s(a.value.course.name)+" "+s(a.value.name),1)]),listOption:t(a=>[d(s(a.option.course.nameWithTerm)+" "+s(a.option.name),1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),c("div",oe,[l(y,{multiple:"",modelValue:n.subjects,"onUpdate:modelValue":u[3]||(u[3]=a=>n.subjects=a),name:"subjects",label:g.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:A.subjects},null,8,["modelValue","label","options"])]),c("div",le,[l(B,{start:n.startDate,"onUpdate:start":u[4]||(u[4]=a=>n.startDate=a),end:n.endDate,"onUpdate:end":u[5]||(u[5]=a=>n.endDate=a),name:"dateBetween",as:"range",label:g.$trans("general.date_between")},null,8,["start","end","label"])]),c("div",re,[l(B,{start:n.startDueDate,"onUpdate:start":u[6]||(u[6]=a=>n.startDueDate=a),end:n.endDueDate,"onUpdate:end":u[7]||(u[7]=a=>n.endDueDate=a),name:"dueDateBetween",as:"range",label:g.$trans("global.date_between",{attribute:g.$trans("resource.assignment.props.due_date")})},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ue={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},de=["onClick"],ce={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},me={class:"flex items-center justify-between"},pe={class:"font-medium"},be={class:"mt-2 flex justify-between items-center"},ye={class:"text-sm font-medium"},fe={class:"flex justify-between items-center mt-2"},_e={key:0},ge={key:0,class:"mt-2 flex justify-between"},he={class:"text-xs"},ve={class:"text-xl font-semibold"},ke={name:"ResourceAssignmentList"},De=Object.assign(ke,{setup(I){const v=J(),p=K("emitter");let C=["filter"];$("resource:config")&&C.push("config"),$("assignment:create")&&C.unshift("create");let R=[];$("assignment:export")&&(R=["print","pdf","excel"]);const V="resource/assignment/",n=Q(!1),A=q({subjects:[]}),i=q({}),g=o=>{Object.assign(A,o)},u=o=>{Object.assign(i,o)};return(o,y)=>{const B=m("PageHeaderAction"),S=m("PageHeader"),a=m("ParentTransition"),j=m("TextMuted"),T=m("CardView"),U=m("Pagination"),x=m("CardList"),w=m("DataCell"),F=m("FloatingMenuItem"),O=m("FloatingMenu"),P=m("DataRow"),N=m("BaseButton"),H=m("DataTable"),E=m("ListItem"),z=X("tooltip");return r(),_(E,{"init-url":V,"pre-requisites":!0,onSetPreRequisites:g,"additional-query":{},onSetItems:u},{header:t(()=>[l(S,{title:o.$trans("resource.assignment.assignment"),navs:[{label:o.$trans("resource.resource"),path:"Resource"}]},{default:t(()=>[l(B,{url:"resource/assignments/",name:"ResourceAssignment",title:o.$trans("resource.assignment.assignment"),actions:b(C),"dropdown-actions":b(R),"config-path":"ResourceConfig",onToggleFilter:y[0]||(y[0]=e=>n.value=!n.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[l(a,{appear:"",visibility:n.value},{default:t(()=>[l(ie,{onRefresh:y[1]||(y[1]=e=>b(p).emit("listItems")),"pre-requisites":A,onHide:y[2]||(y[2]=e=>n.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:t(()=>[b(Y)(["student","guardian"],"any")?(r(),_(a,{key:0,appear:"",visibility:!0},{default:t(()=>[l(x,{header:i.headers,meta:i.meta},{content:t(()=>[c("div",ve,s(o.$trans("dashboard.nothing_to_show")),1)]),default:t(()=>[c("div",ue,[(r(!0),D(L,null,M(i.data,e=>(r(),D("div",{key:e.uuid,class:"cursor-pointer",onClick:f=>b(v).push({name:"ResourceAssignmentShow",params:{uuid:e.uuid}})},[l(T,{"no-padding":""},{default:t(()=>{var f,k;return[c("div",ce,[c("div",me,[c("span",pe,s(e.title),1),e.enableMarking?(r(),_(j,{key:0,block:""},{default:t(()=>[d(s(o.$trans("resource.assignment.props.max_mark"))+": "+s(e.maxMark),1)]),_:2},1024)):h("",!0)]),c("div",be,[c("span",ye,s(e.date.formatted),1),c("span",{class:"px-2 py-1 text-gray-50 text-xs rounded-lg mr-2",style:Z(`background-color: ${e.type.color}`)},s(e.type.name),5)]),c("div",fe,[l(j,{block:""},{default:t(()=>[d(s(o.$trans("resource.assignment.props.due_date"))+" "+s(e.dueDate.formatted),1)]),_:2},1024),e.hasSubmitted?ee((r(),D("span",_e,y[6]||(y[6]=[c("i",{class:"far fa-check-circle fa-lg text-success"},null,-1)]))),[[z,o.$trans("resource.assignment.submitted")]]):h("",!0)]),e.employee?(r(),D("div",ge,[c("span",he,s(((f=e.employee)==null?void 0:f.name)||"-")+" "+s(((k=e.employee)==null?void 0:k.designation)||""),1)])):h("",!0)])]}),_:2},1024)],8,de))),128))]),c("div",null,[l(U,{"card-view":"",meta:i.meta,onRefresh:y[3]||(y[3]=e=>b(p).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(r(),_(a,{key:1,appear:"",visibility:!0},{default:t(()=>[l(H,{header:i.headers,meta:i.meta,module:"resource.assignment",onRefresh:y[5]||(y[5]=e=>b(p).emit("listItems"))},{actionButton:t(()=>[b($)("assignment:create")?(r(),_(N,{key:0,onClick:y[4]||(y[4]=e=>b(v).push({name:"ResourceAssignmentCreate"}))},{default:t(()=>[d(s(o.$trans("global.add",{attribute:o.$trans("resource.assignment.assignment")})),1)]),_:1})):h("",!0)]),default:t(()=>[(r(!0),D(L,null,M(i.data,e=>(r(),_(P,{key:e.uuid,onDoubleClick:f=>b(v).push({name:"ResourceAssignmentShow",params:{uuid:e.uuid}})},{default:t(()=>[l(w,{name:"title"},{default:t(()=>[d(s(e.titleExcerpt)+" ",1),e.enableMarking?(r(),_(j,{key:0,block:""},{default:t(()=>[d(s(o.$trans("resource.assignment.props.max_mark"))+": "+s(e.maxMark),1)]),_:2},1024)):h("",!0)]),_:2},1024),l(w,{name:"records"},{default:t(()=>[(r(!0),D(L,null,M(e.records,f=>{var k;return r(),D("div",null,[d(s(((k=f.batch.course)==null?void 0:k.name)+" "+f.batch.name)+" ",1),f.subject?(r(),_(j,{key:0},{default:t(()=>[d(s(f.subject.name),1)]),_:2},1024)):h("",!0)])}),256))]),_:2},1024),l(w,{name:"employee"},{default:t(()=>{var f;return[d(s(((f=e.employee)==null?void 0:f.name)||"-")+" ",1),l(j,{block:""},{default:t(()=>{var k;return[d(s((k=e.employee)==null?void 0:k.codeNumber),1)]}),_:2},1024)]}),_:2},1024),l(w,{name:"date"},{default:t(()=>[d(s(e.date.formatted)+" ",1),l(j,{block:""},{default:t(()=>[d(s(e.dueDate.formatted),1)]),_:2},1024)]),_:2},1024),l(w,{name:"createdAt"},{default:t(()=>[d(s(e.createdAt.formatted),1)]),_:2},1024),l(w,{name:"action"},{default:t(()=>[l(O,null,{default:t(()=>[l(F,{icon:"fas fa-arrow-circle-right",onClick:f=>b(v).push({name:"ResourceAssignmentShow",params:{uuid:e.uuid}})},{default:t(()=>[d(s(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),b($)("assignment:edit")&&e.isEditable?(r(),_(F,{key:0,icon:"fas fa-edit",onClick:f=>b(v).push({name:"ResourceAssignmentEdit",params:{uuid:e.uuid}})},{default:t(()=>[d(s(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):h("",!0),b($)("assignment:create")?(r(),_(F,{key:1,icon:"fas fa-copy",onClick:f=>b(v).push({name:"ResourceAssignmentDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[d(s(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):h("",!0),b($)("assignment:delete")&&e.isDeletable?(r(),_(F,{key:2,icon:"fas fa-trash",onClick:f=>b(p).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[d(s(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):h("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{De as default};
