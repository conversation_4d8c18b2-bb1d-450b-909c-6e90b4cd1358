import{u as w,l as k,H as P,r as p,q as v,o as u,w as f,d as c,a as _,e as i,f as t,F as V,v as S,b as U,s as F,t as j,J as T}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-2 gap-6"},W={class:"col-span-2 sm:col-span-1"},D={class:"col-span-2 sm:col-span-1"},L={class:"col-span-2"},N={class:"col-span-4 flex items-end sm:col-span-1"},R={class:"col-span-4 flex items-end space-x-4 sm:col-span-1"},C={class:"col-span-4 sm:col-span-1"},I={class:"col-span-4 sm:col-span-1"},J={name:"EmployeeAttendanceWorkShiftForm"},z=Object.assign(J,{setup(H){w();const d={name:"",code:"",records:[],description:""},m="employee/attendance/workShift/",y=k({days:[]}),o=P(m),n=k({...d}),h=r=>{Object.assign(y,r),y.days.forEach(a=>{d.records.push({day:a.value,label:a.label,isHoliday:!1,isOvernight:!1,startTime:"",endTime:""})}),Object.assign(n,T(d))},B=r=>{Object.assign(d,{name:r.name,code:r.code,records:r.records,description:r.description}),Object.assign(n,T(d))};return(r,a)=>{const b=p("BaseInput"),O=p("BaseTextarea"),A=p("BaseLabel"),$=p("BaseSwitch"),g=p("DatePicker"),E=p("FormAction");return u(),v(E,{"pre-requisites":!0,onSetPreRequisites:h,"init-url":m,"init-form":d,form:n,"set-form":B,redirect:"EmployeeAttendanceWorkShift"},{default:f(()=>[c("div",q,[c("div",W,[i(b,{type:"text",modelValue:n.name,"onUpdate:modelValue":a[0]||(a[0]=e=>n.name=e),name:"name",label:r.$trans("employee.attendance.work_shift.props.name"),error:t(o).name,"onUpdate:error":a[1]||(a[1]=e=>t(o).name=e),autofocus:""},null,8,["modelValue","label","error"])]),c("div",D,[i(b,{type:"text",modelValue:n.code,"onUpdate:modelValue":a[2]||(a[2]=e=>n.code=e),name:"code",label:r.$trans("employee.attendance.work_shift.props.code"),error:t(o).code,"onUpdate:error":a[3]||(a[3]=e=>t(o).code=e),autofocus:""},null,8,["modelValue","label","error"])]),c("div",L,[i(O,{modelValue:n.description,"onUpdate:modelValue":a[4]||(a[4]=e=>n.description=e),name:"description",label:r.$trans("employee.attendance.work_shift.props.description"),error:t(o).description,"onUpdate:error":a[5]||(a[5]=e=>t(o).description=e)},null,8,["modelValue","label","error"])])]),(u(!0),_(V,null,S(n.records,(e,l)=>(u(),_("div",{class:"mt-4 grid grid-cols-4 gap-3",key:e.day},[c("div",N,[i(A,{class:"mt-4"},{default:f(()=>[F(j(e.label),1)]),_:2},1024)]),c("div",R,[i($,{reverse:"",modelValue:e.isHoliday,"onUpdate:modelValue":s=>e.isHoliday=s,name:`records.${l}.isHoliday`,label:r.$trans("employee.attendance.work_shift.props.is_holiday"),error:t(o)[`records.${l}.isHoliday`],"onUpdate:error":s=>t(o)[`records.${l}.isHoliday`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"]),e.isHoliday?U("",!0):(u(),v($,{key:0,reverse:"",modelValue:e.isOvernight,"onUpdate:modelValue":s=>e.isOvernight=s,name:`records.${l}.isOvernight`,label:r.$trans("employee.attendance.work_shift.props.is_overnight"),error:t(o)[`records.${l}.isOvernight`],"onUpdate:error":s=>t(o)[`records.${l}.isOvernight`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"]))]),e.isHoliday?U("",!0):(u(),_(V,{key:0},[c("div",C,[i(g,{name:`records.${l}.startTime`,modelValue:e.startTime,"onUpdate:modelValue":s=>e.startTime=s,placeholder:r.$trans("employee.attendance.work_shift.props.start_time"),as:"time",error:t(o)[`records.${l}.startTime`],"onUpdate:error":s=>t(o)[`records.${l}.startTime`]=s},null,8,["name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])]),c("div",I,[i(g,{name:`records.${l}.endTime`,modelValue:e.endTime,"onUpdate:modelValue":s=>e.endTime=s,placeholder:r.$trans("employee.attendance.work_shift.props.end_time"),as:"time",error:t(o)[`records.${l}.endTime`],"onUpdate:error":s=>t(o)[`records.${l}.endTime`]=s},null,8,["name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])])],64))]))),128))]),_:1},8,["form"])}}}),G={name:"EmployeeAttendanceWorkShiftAction"},M=Object.assign(G,{setup(H){const d=w();return(m,y)=>{const o=p("PageHeaderAction"),n=p("PageHeader"),h=p("ParentTransition");return u(),_(V,null,[i(n,{title:m.$trans(t(d).meta.trans,{attribute:m.$trans(t(d).meta.label)}),navs:[{label:m.$trans("employee.employee"),path:"Employee"},{label:m.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"},{label:m.$trans("employee.attendance.work_shift.work_shift"),path:"EmployeeAttendanceWorkShiftList"}]},{default:f(()=>[i(o,{name:"EmployeeAttendanceWorkShift",title:m.$trans("employee.attendance.work_shift.work_shift"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(h,{appear:"",visibility:!0},{default:f(()=>[i(z)]),_:1})],64)}}});export{M as default};
