import{u as _,H as v,l as C,r,q as f,o as g,w as c,d as m,e as s,f as a}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3 sm:col-span-1"},F={name:"LocaleForm"},U=Object.assign(F,{setup(b){const i=_(),d={name:"",code:""},l="config/locale/",t=v(l),e=C({...d});return(p,o)=>{const u=r("BaseInput"),V=r("FormAction");return g(),f(V,{"init-url":l,"init-form":d,form:e,redirect:"ConfigLocale"},{default:c(()=>[m("div",P,[m("div",A,[s(u,{type:"text",modelValue:e.name,"onUpdate:modelValue":o[0]||(o[0]=n=>e.name=n),name:"name",label:p.$trans("config.locale.props.name"),error:a(t).name,"onUpdate:error":o[1]||(o[1]=n=>a(t).name=n),autofocus:""},null,8,["modelValue","label","error"])]),m("div",B,[s(u,{disabled:a(i).meta.type==="edit",type:"text",modelValue:e.code,"onUpdate:modelValue":o[2]||(o[2]=n=>e.code=n),name:"code",label:p.$trans("config.locale.props.code"),error:a(t).code,"onUpdate:error":o[3]||(o[3]=n=>a(t).code=n),autofocus:""},null,8,["disabled","modelValue","label","error"])])])]),_:1},8,["form"])}}}),$={name:"LocaleAction"},L=Object.assign($,{setup(b){return _(),(i,d)=>{const l=r("PageHeaderAction"),t=r("ParentTransition"),e=r("ConfigPage");return g(),f(e,{"no-background":""},{action:c(()=>[s(l,{name:"ConfigLocale",title:i.$trans("config.locale.locale"),actions:["list"]},null,8,["title"])]),default:c(()=>[s(t,{appear:"",visibility:!0},{default:c(()=>[s(U)]),_:1})]),_:1})}}});export{L as default};
