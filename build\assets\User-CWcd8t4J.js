import{u as H,h as T,i as j,j as N,H as P,l as b,r as a,a as S,o as V,q as A,b as E,e as r,w as l,d as u,f as o,s as f,t as m,F as $}from"./app-BAwPsakn.js";const G={class:"space-x-4"},L={class:"grid grid-cols-2 gap-6"},q={class:"col-span-2"},z={class:"col-span-2 sm:col-span-1"},D={class:"col-span-2 sm:col-span-1"},W={name:"TeamConfigUserPermission"},J=Object.assign(W,{props:{team:{type:Object,default(){return{name:""}}}},setup(B){const c=H(),y=T(),_=j(),t=N("$trans"),p="team/permission/",i=P(p),g={action:"",users:[],permissions:[]};b({});const n=b({...g}),C=[{label:t("general.assign"),value:"assign"},{label:t("general.revoke"),value:"revoke"}],U=async d=>await _.dispatch(p+"search",{query:d,uuid:c.params.uuid}).then(s=>s).catch(s=>{}),h=async d=>await _.dispatch(p+"searchUser",{query:d,uuid:c.params.uuid}).then(s=>s).catch(s=>{});return(d,s)=>{const k=a("BaseButton"),O=a("PageHeader"),w=a("CardHeader"),F=a("BaseRadioGroup"),v=a("BaseSelect"),R=a("FormAction"),x=a("ParentTransition");return V(),S($,null,[B.team.uuid?(V(),A(O,{key:0,title:o(t)(o(c).meta.label),navs:[]},{default:l(()=>[u("div",G,[r(k,{onClick:s[0]||(s[0]=e=>o(y).push({name:"TeamConfigPermission"}))},{default:l(()=>[f(m(o(t)("team.config.permission.role_permission")),1)]),_:1})])]),_:1},8,["title"])):E("",!0),r(x,{appear:"",visibility:!0},{default:l(()=>[r(R,{"no-data-fetch":"","init-url":p,uuid:o(c).params.uuid,action:"userWiseAssign","init-form":g,form:n,"reset-form":"","keep-adding":!1},{default:l(()=>[r(w,{first:"",title:o(t)("team.config.permission.user_permission_config"),description:o(t)("team.config.permission.user_permission_info")},null,8,["title","description"]),u("div",L,[u("div",q,[r(F,{options:C,name:"action",modelValue:n.action,"onUpdate:modelValue":s[1]||(s[1]=e=>n.action=e),error:o(i).action,"onUpdate:error":s[2]||(s[2]=e=>o(i).action=e),horizontal:""},null,8,["modelValue","error"])]),u("div",z,[r(v,{modelValue:n.users,"onUpdate:modelValue":s[3]||(s[3]=e=>n.users=e),filterResults:!1,minChars:1,resolveOnLoad:!1,delay:500,multiple:"",name:"users","value-prop":"uuid",options:async function(e){return await h(e)},label:o(t)("global.select",{attribute:o(t)("user.user")}),error:o(i).users,"onUpdate:error":s[4]||(s[4]=e=>o(i).users=e)},{selectedOption:l(e=>[f(m(e.value.profile.name)+" ("+m(e.value.email)+") ",1)]),listOption:l(e=>[f(m(e.option.profile.name)+" ("+m(e.option.email)+") ",1)]),_:1},8,["modelValue","options","label","error"])]),u("div",D,[r(v,{modelValue:n.permissions,"onUpdate:modelValue":s[5]||(s[5]=e=>n.permissions=e),filterResults:!1,minChars:1,resolveOnLoad:!1,delay:500,multiple:"",name:"permissions",options:async function(e){return await U(e)},label:o(t)("global.select",{attribute:o(t)("team.config.permission.permission")}),error:o(i).permissions,"onUpdate:error":s[6]||(s[6]=e=>o(i).permissions=e)},null,8,["modelValue","options","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})],64)}}});export{J as default};
