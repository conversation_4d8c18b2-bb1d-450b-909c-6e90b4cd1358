import{u as A,G as T,H as O,l as g,r as d,q as $,o as k,w as c,d as l,e as a,f as t,b as j,s as _,t as y,J as q,a as D,F as L}from"./app-BAwPsakn.js";const M={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},w={class:"mt-6 grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-2"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-2"},Y={class:"col-span-3"},Z={class:"grid grid-cols-1"},x={class:"col"},h={name:"ReceptionCorrespondenceForm"},ee=Object.assign(h,{setup(N){const u=A(),i={type:"",mode:"",letterNumber:"",senderTitle:"",senderAddress:"",receiverTitle:"",receiverAddress:"",date:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:T(),mediaHash:[]},f="reception/correspondence/",s=O(f),m=g({types:[],modes:[]}),o=g({...i}),b=g({reference:"",isLoaded:!u.params.uuid}),B=n=>{Object.assign(m,n)},C=()=>{o.mediaToken=T(),o.mediaHash=[]},R=n=>{var e,v,V,p;Object.assign(i,{...n,date:n.date.value,type:(e=n.type)==null?void 0:e.value,mode:(v=n.mode)==null?void 0:v.value,reference:(V=n.reference)==null?void 0:V.uuid}),Object.assign(o,q(i)),b.reference=(p=n.reference)==null?void 0:p.letterNumber,b.isLoaded=!0};return(n,e)=>{const v=d("CustomCheckbox"),V=d("DatePicker"),p=d("BaseInput"),F=d("BaseSelect"),P=d("BaseSelectSearch"),U=d("BaseTextarea"),H=d("MediaUpload"),S=d("FormAction");return k(),$(S,{"pre-requisites":!0,onSetPreRequisites:B,"init-url":f,"init-form":i,form:o,"set-form":R,redirect:"ReceptionCorrespondence",onResetMediaFiles:C},{default:c(()=>[l("div",M,[l("div",E,[a(v,{label:n.$trans("reception.correspondence.props.type"),options:m.types,modelValue:o.type,"onUpdate:modelValue":e[0]||(e[0]=r=>o.type=r),error:t(s).type,"onUpdate:error":e[1]||(e[1]=r=>t(s).type=r)},null,8,["label","options","modelValue","error"])]),l("div",I,[a(V,{modelValue:o.date,"onUpdate:modelValue":e[2]||(e[2]=r=>o.date=r),name:"date",label:n.$trans("reception.correspondence.props.date"),"no-clear":"",error:t(s).date,"onUpdate:error":e[3]||(e[3]=r=>t(s).date=r)},null,8,["modelValue","label","error"])])]),l("div",w,[l("div",G,[a(p,{type:"text",modelValue:o.letterNumber,"onUpdate:modelValue":e[4]||(e[4]=r=>o.letterNumber=r),name:"letterNumber",label:n.$trans("reception.correspondence.props.letter_number"),error:t(s).letterNumber,"onUpdate:error":e[5]||(e[5]=r=>t(s).letterNumber=r)},null,8,["modelValue","label","error"])]),l("div",J,[a(F,{modelValue:o.mode,"onUpdate:modelValue":e[6]||(e[6]=r=>o.mode=r),name:"mode",label:n.$trans("reception.correspondence.props.mode"),options:m.modes,error:t(s).mode,"onUpdate:error":e[7]||(e[7]=r=>t(s).mode=r)},null,8,["modelValue","label","options","error"])]),l("div",z,[b.isLoaded?(k(),$(P,{key:0,name:"reference",label:n.$trans("global.select",{attribute:n.$trans("reception.correspondence.props.reference")}),modelValue:o.reference,"onUpdate:modelValue":e[8]||(e[8]=r=>o.reference=r),error:t(s).reference,"onUpdate:error":e[9]||(e[9]=r=>t(s).reference=r),"value-prop":"uuid","init-search":b.reference,"init-search-key":"letterNumber","search-action":"reception/correspondence/list"},{selectedOption:c(r=>[_(y(r.value.letterNumber),1)]),listOption:c(r=>[_(y(r.option.letterNumber),1)]),_:1},8,["label","modelValue","error","init-search"])):j("",!0)]),l("div",K,[a(p,{type:"text",modelValue:o.senderTitle,"onUpdate:modelValue":e[10]||(e[10]=r=>o.senderTitle=r),name:"senderTitle",label:n.$trans("reception.correspondence.props.sender_title"),error:t(s).senderTitle,"onUpdate:error":e[11]||(e[11]=r=>t(s).senderTitle=r)},null,8,["modelValue","label","error"])]),l("div",Q,[a(U,{modelValue:o.senderAddress,"onUpdate:modelValue":e[12]||(e[12]=r=>o.senderAddress=r),name:"senderAddress",label:n.$trans("reception.correspondence.props.sender_address"),error:t(s).senderAddress,"onUpdate:error":e[13]||(e[13]=r=>t(s).senderAddress=r)},null,8,["modelValue","label","error"])]),l("div",W,[a(p,{type:"text",modelValue:o.receiverTitle,"onUpdate:modelValue":e[14]||(e[14]=r=>o.receiverTitle=r),name:"receiverTitle",label:n.$trans("reception.correspondence.props.receiver_title"),error:t(s).receiverTitle,"onUpdate:error":e[15]||(e[15]=r=>t(s).receiverTitle=r)},null,8,["modelValue","label","error"])]),l("div",X,[a(U,{modelValue:o.receiverAddress,"onUpdate:modelValue":e[16]||(e[16]=r=>o.receiverAddress=r),name:"receiverAddress",label:n.$trans("reception.correspondence.props.receiver_address"),error:t(s).receiverAddress,"onUpdate:error":e[17]||(e[17]=r=>t(s).receiverAddress=r)},null,8,["modelValue","label","error"])]),l("div",Y,[a(U,{modelValue:o.remarks,"onUpdate:modelValue":e[18]||(e[18]=r=>o.remarks=r),name:"remarks",label:n.$trans("reception.correspondence.props.remarks"),error:t(s).remarks,"onUpdate:error":e[19]||(e[19]=r=>t(s).remarks=r)},null,8,["modelValue","label","error"])])]),l("div",Z,[l("div",x,[a(H,{multiple:"",label:n.$trans("general.file"),module:"correspondence",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[20]||(e[20]=r=>o.mediaUpdated=!0),onSetHash:e[21]||(e[21]=r=>o.mediaHash.push(r))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),re={name:"ReceptionCorrespondenceAction"},se=Object.assign(re,{setup(N){const u=A();return(i,f)=>{const s=d("PageHeaderAction"),m=d("PageHeader"),o=d("ParentTransition");return k(),D(L,null,[a(m,{title:i.$trans(t(u).meta.trans,{attribute:i.$trans(t(u).meta.label)}),navs:[{label:i.$trans("reception.reception"),path:"Reception"},{label:i.$trans("reception.correspondence.correspondence"),path:"ReceptionCorrespondenceList"}]},{default:c(()=>[a(s,{name:"ReceptionCorrespondence",title:i.$trans("reception.correspondence.correspondence"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(o,{appear:"",visibility:!0},{default:c(()=>[a(ee)]),_:1})],64)}}});export{se as default};
