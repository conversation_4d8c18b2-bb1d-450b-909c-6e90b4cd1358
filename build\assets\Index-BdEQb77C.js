import{j as K,u as te,i as ae,m as se,l as D,H as oe,n as ne,r as k,q as h,o as a,w as s,d as p,e as n,f as t,b as y,s as g,t as d,a as c,F as q,h as _e,D as be,y as fe,c as he,a9 as ke,z as ve,v as S,x as ge,A as ee,J as xe,M as Ae}from"./app-BAwPsakn.js";import"./lodash-CyHJH6Xs.js";const je={class:"grid grid-cols-4 gap-6"},Ve={class:"col-span-4 sm:col-span-1"},we={class:"col-span-4 sm:col-span-1"},He={class:"col-span-4 sm:col-span-1"},qe={class:"mt-4 grid grid-cols-4 gap-6"},Be={class:"col-span-4 sm:col-span-1"},Re={class:"col-span-4 sm:col-span-1"},Se={class:"col-span-4 sm:col-span-1"},$e={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(J,{emit:v}){const L=K("moment"),H=te(),r=ae(),P=v,U=J,$={method:"batch_wise",date:L().format("YYYY-MM-DD"),batch:"",subject:"",session:"",detail:!1},x=se(!1),m=D({...$}),_=oe(U.initUrl),w=D({methods:U.preRequisites.methods,sessions:U.preRequisites.sessions,subjects:[]}),j=D({batch:"",subject:"",isLoaded:!H.query.batch}),f=async i=>{if(!m.method!="batch_wise"){if(!i){m.batch="",m.subject="",w.subjects=[];return}m.batch=i||"",w.subjects=[],m.subject="",x.value=!0,await r.dispatch("academic/batch/listSubjects",{uuid:i||""}).then(o=>{w.subjects=o,x.value=!1}).catch(o=>{x.value=!1})}};return ne(async()=>{j.batch=H.query.batch,m.batch=H.query.batch,H.query.batch&&(await f(H.query.batch),j.subject=H.query.subject,m.subject=H.query.subject),j.isLoaded=!0}),(i,o)=>{const C=k("BaseSelect"),T=k("BaseSelectSearch"),R=k("DatePicker"),E=k("BaseSwitch"),O=k("FilterForm");return a(),h(O,{"init-form":$,form:m,onHide:o[11]||(o[11]=u=>P("hide"))},{default:s(()=>[p("div",je,[p("div",Ve,[n(C,{modelValue:m.method,"onUpdate:modelValue":o[0]||(o[0]=u=>m.method=u),name:"method",label:i.$trans("student.attendance.method"),options:w.methods,error:t(_).method,"onUpdate:error":o[1]||(o[1]=u=>t(_).method=u)},null,8,["modelValue","label","options","error"])]),p("div",we,[j.isLoaded?(a(),h(T,{key:0,name:"batch",label:i.$trans("global.select",{attribute:i.$trans("academic.batch.batch")}),modelValue:m.batch,"onUpdate:modelValue":o[2]||(o[2]=u=>m.batch=u),error:t(_).batch,"onUpdate:error":o[3]||(o[3]=u=>t(_).batch=u),"value-prop":"uuid","init-search":j.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:f},{selectedOption:s(u=>[g(d(u.value.course.nameWithTerm)+" "+d(u.value.name),1)]),listOption:s(u=>[g(d(u.option.course.nameWithTerm)+" "+d(u.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):y("",!0)]),p("div",He,[n(R,{modelValue:m.date,"onUpdate:modelValue":o[4]||(o[4]=u=>m.date=u),name:"date",label:i.$trans("student.attendance.props.date"),"no-clear":"",error:t(_).date,"onUpdate:error":o[5]||(o[5]=u=>t(_).date=u)},null,8,["modelValue","label","error"])])]),p("div",qe,[m.method=="subject_wise"?(a(),c(q,{key:0},[p("div",Be,[n(C,{modelValue:m.subject,"onUpdate:modelValue":o[6]||(o[6]=u=>m.subject=u),name:"subject",label:i.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:w.subjects,error:t(_).subject,"onUpdate:error":o[7]||(o[7]=u=>t(_).subject=u)},null,8,["modelValue","label","options","error"])]),p("div",Re,[n(C,{modelValue:m.session,"onUpdate:modelValue":o[8]||(o[8]=u=>m.session=u),name:"session",label:i.$trans("student.attendance.session"),options:w.sessions,error:t(_).subject,"onUpdate:error":o[9]||(o[9]=u=>t(_).subject=u)},null,8,["modelValue","label","options","error"])])],64)):y("",!0),p("div",Se,[n(E,{vertical:"",modelValue:m.detail,"onUpdate:modelValue":o[10]||(o[10]=u=>m.detail=u),name:"detail",label:i.$trans("general.detail")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},De={class:"flex flex-wrap"},Ue={class:"p-2"},Ce={class:"divide-y divide-gray-200 dark:divide-gray-700"},Fe={class:"col-span-3 sm:col-span-1"},Me={class:"flex"},Pe={class:"mr-1"},Ne={class:"col-span-3 sm:col-span-1"},ze={key:0,class:"px-2"},Le={key:1,class:"px-2"},Te={key:0,class:"py-4 px-2 flex justify-between space-x-2"},Ee={key:0,class:"flex-1"},Oe={class:"py-4 px-2"},We={key:0,class:"fas fa-circle-h text-gray-600"},Ie={key:1,class:"fas fa-circle-h text-red-600"},Ye={key:2,class:"fas fa-check text-success"},Ke={key:3,class:"fas fa-history text-info"},Je={key:4,class:"fas fa-coffee text-warning"},Ge={key:5,class:"fas fa-plane text-primary"},Qe={key:6,class:"fas fa-times text-danger"},Xe={class:"text-xs"},Ze={class:"text-xs"},et={class:"text-xs"},tt={class:"text-xs"},at={class:"text-xs"},st={class:"text-xs"},ot={class:"text-xs"},nt={class:"text-xs"},lt={class:"text-xs"},dt={class:"text-xs"},rt={key:0,class:"px-2"},it={key:1,class:"px-2"},ct={key:2,class:"py-4 px-2 flex justify-between space-x-2"},ut={key:0,class:"flex-1"},mt={key:3,class:"py-4 px-2 flex justify-between"},pt={class:"flex space-x-4"},yt={class:"flex space-x-4"},_t={name:"StudentAttendance"},kt=Object.assign(_t,{setup(J){const v=te(),L=_e(),H=ae();K("emitter");const r=K("$trans"),{screenSize:P}=be();let U=[];fe("student:list-attendance")&&(U=["print","pdf","excel"]);const $=he(()=>ke(v.query.detail)),x={method:"batch_wise",batch:"",subject:"",session:"",date:"",markAsHoliday:!1,holidayReason:"",students:[]},m="student/attendance/",_=se(!1),w=D({sessions:[],types:[]}),j=oe(m),f=D({headers:[],data:[],meta:{total:0,isActionable:!1}}),i=D({...x}),o=D({meta:{},defaultAttendanceType:{code:"P",label:r("student.attendance.types.present")}}),C=b=>{o.defaultAttendanceType=b,i.students.forEach(l=>{l.attendance=b.code})},T=async()=>{_.value=!0,await H.dispatch(m+"preRequisite").then(b=>{_.value=!1,Object.assign(w,b)}).catch(b=>{_.value=!1})},R=async()=>{_.value=!0,await H.dispatch(m+"fetch",{params:v.query}).then(b=>{_.value=!1,x.method=v.query.method,x.batch=v.query.batch,x.subject=v.query.subject,x.session=v.query.session,x.date=v.query.date,x.students=b.data,o.meta=b.meta,o.meta.isForceHoliday?(x.markAsHoliday=!0,x.holidayReason=o.meta.holidayReason):(x.markAsHoliday=!1,x.holidayReason=""),Object.assign(i,xe(x)),Object.assign(f,b)}).catch(b=>{_.value=!1})},E=b=>{if(b.isDisabled||!b.isActionable)return;let l=w.types.map(W=>W.code);l.push("");let B=l.indexOf(b.code);B=++B%l.length,b.code=l[B]},O=()=>{f.data.forEach(b=>{let l=b.attendances[f.meta.dateKey];l.isActionable&&(l.code="P")})},u=()=>{f.data.forEach(b=>{let l=b.attendances[f.meta.dateKey];l.isActionable&&(l.code="A")})},le=async()=>{let b=f.data.map(l=>({...l,attendance:l.attendances[f.meta.dateKey].code}));_.value=!0,await H.dispatch(m+"store",{form:{method:i.method,markAsHoliday:i.markAsHoliday,holidayReason:i.holidayReason,students:b,batch:v.query.batch,subject:v.query.subject,session:v.query.session,date:v.query.date}}).then(l=>{_.value=!1,R()}).catch(l=>{_.value=!1})},G=async()=>{await Ae()&&(_.value=!0,await H.dispatch(m+"remove",{form:{method:i.method,batch:v.query.batch,subject:v.query.subject,session:v.query.session,date:v.query.date}}).then(b=>{_.value=!1,R()}).catch(b=>{_.value=!1}))},de=async()=>{await R()};return ne(async()=>{await T(),v.query.batch&&v.query.date&&await R()}),(b,l)=>{const B=k("BaseButton"),W=k("PageHeaderAction"),re=k("PageHeader"),I=k("ParentTransition"),Y=k("BaseCard"),ie=k("DropdownItem"),ce=k("DropdownButton"),F=k("BaseAlert"),N=k("BaseDataView"),ue=k("BaseLabel"),me=k("BaseSelect"),Q=k("BaseSwitch"),X=k("BaseInput"),pe=k("FormAction"),V=k("DataCell"),z=k("DataRow"),ye=k("DataTable"),Z=ve("tooltip");return a(),c(q,null,[n(re,{title:t(r)(t(v).meta.label),navs:[{label:t(r)("student.student"),path:"Student"}]},{default:s(()=>[n(W,{url:"student/attendance/",name:"StudentAttendance",title:t(r)("student.attendance.attendance"),actions:[],"dropdown-actions":t(U)},{default:s(()=>[n(B,{design:"primary",onClick:l[0]||(l[0]=e=>t(L).push({name:"Student"}))},{default:s(()=>[g(d(t(r)("global.list",{attribute:t(r)("student.student")})),1)]),_:1})]),_:1},8,["title","dropdown-actions"])]),_:1},8,["title","navs"]),n(I,{appear:"",visibility:!0},{default:s(()=>[w.sessions.length?(a(),h($e,{key:0,onAfterFilter:R,"pre-requisites":w,"init-url":m},null,8,["pre-requisites"])):y("",!0)]),_:1}),n(I,{appear:"",visibility:!0},{default:s(()=>[t(P).small&&o.meta.monthName?(a(),h(Y,{key:0,class:"mt-4"},{title:s(()=>[g(d(o.meta.monthName),1)]),default:s(()=>[p("div",De,[(a(!0),c(q,null,S(o.meta.currentMonthAttendances,e=>(a(),c("span",{class:ge(["m-1 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border-2 text-sm",{"text-white dark:text-gray-50":e.isHoliday||e.hasAttendanceMarked,"bg-info border-info":e.hasAttendanceMarked,"bg-gray-500 border-gray-500":e.isHoliday,"border-info text-gray-800 dark:text-gray-400":!e.isHoliday&&!e.hasAttendanceMarked}])},d(e.day),3))),256))])]),_:1})):y("",!0)]),_:1}),n(I,{appear:"",visibility:!0},{default:s(()=>[t(P).small?(a(),c(q,{key:0},[t(v).query.date?(a(),h(Y,{key:0,"no-padding":"","no-content-padding":"","is-loading":_.value},{title:s(()=>[g(d(t(r)("student.attendance.attendance")),1)]),action:s(()=>[w.types&&o.meta.isActionable?(a(),h(ce,{key:0,label:t(r)("student.attendance.mark")},{default:s(()=>[(a(!0),c(q,null,S(w.types,e=>(a(),c("div",{key:e.uuid},[n(ie,{as:"span",onClick:A=>C(e)},{default:s(()=>[g(d(e.label),1)]),_:2},1032,["onClick"])]))),128))]),_:1},8,["label"])):y("",!0)]),default:s(()=>[p("div",Ue,[i.students.length==0?(a(),h(F,{key:0,size:"xs",design:"error"},{default:s(()=>[g(d(t(r)("general.errors.record_not_found")),1)]),_:1})):y("",!0)]),i.students.length?(a(),h(pe,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":m,action:"store","init-form":x,form:i,"after-submit":de},{additionalAction:s(()=>[f.meta.isActionable&&!f.meta.isHoliday?(a(),c("div",Te,[n(Q,{vertical:"",modelValue:i.markAsHoliday,"onUpdate:modelValue":l[1]||(l[1]=e=>i.markAsHoliday=e),name:"markAsHoliday",label:t(r)("student.attendance.mark_as_holiday"),error:t(j).markAsHoliday,"onUpdate:error":l[2]||(l[2]=e=>t(j).markAsHoliday=e)},null,8,["modelValue","label","error"]),i.markAsHoliday?(a(),c("div",Ee,[n(X,{type:"text",modelValue:i.holidayReason,"onUpdate:modelValue":l[3]||(l[3]=e=>i.holidayReason=e),name:"holidayReason",label:t(r)("student.attendance.holiday_reason"),error:t(j).holidayReason,"onUpdate:error":l[4]||(l[4]=e=>t(j).holidayReason=e)},null,8,["modelValue","label","error"])])):y("",!0)])):y("",!0),p("div",Oe,[f.meta.isMarked?(a(),h(B,{key:0,design:"danger",onClick:G},{default:s(()=>[g(d(t(r)("global.remove",{attribute:t(r)("student.attendance.attendance")})),1)]),_:1})):y("",!0)])]),default:s(()=>[p("div",Ce,[(a(!0),c(q,null,S(i.students,(e,A)=>(a(),c("div",{class:"grid grid-cols-3 gap-6 px-4 py-2",key:e.uuid},[p("div",Fe,[p("div",Me,[p("span",Pe,[n(N,null,{default:s(()=>[g(d(e.sno)+".",1)]),_:2},1024)]),n(N,null,{default:s(()=>[g(d(e.name+" ("+(e.rollNumber?e.rollNumber:e.codeNumber)+")"),1)]),_:2},1024)])]),p("div",Ne,[e.attendance=="H"?(a(),h(ue,{key:0},{default:s(()=>[g(d(t(r)("calendar.holiday.holiday")),1)]),_:1})):(a(),h(me,{key:1,disabled:!o.meta.isActionable,modelValue:e.attendance,"onUpdate:modelValue":M=>e.attendance=M,name:`students.${A}.attendanceType`,placeholder:t(r)("student.attendance.props.type"),options:w.types,"value-prop":"code","label-prop":"label",error:t(j)[`students.${A}.attendanceType`],"onUpdate:error":M=>t(j)[`students.${A}.attendanceType`]=M},null,8,["disabled","modelValue","onUpdate:modelValue","name","placeholder","options","error","onUpdate:error"]))])]))),128))]),o.meta.isHoliday&&o.meta.isMarked?(a(),c("div",ze,[n(F,{size:"xs",design:"info"},{default:s(()=>[g(d(t(r)("student.attendance.holiday_force_attendance_marked",{attribute:o.meta.holidayReason})),1)]),_:1})])):y("",!0),o.meta.isForceHoliday?(a(),c("div",Le,[n(F,{size:"xs",design:"error"},{default:s(()=>[g(d(t(r)("student.attendance.attendance_force_holiday_marked")),1)]),_:1})])):y("",!0)]),_:1},8,["form"])):y("",!0)]),_:1},8,["is-loading"])):y("",!0)],64)):(a(),h(Y,{key:1,"no-padding":"","no-content-padding":"","is-loading":_.value},{default:s(()=>[n(ye,{"sticky-columns":["student"],header:f.headers,meta:f.meta,scroll:!0,module:"student.attendance",onRefresh:R},{actions:s(()=>[o.meta.isHoliday&&o.meta.isMarked?(a(),c("div",rt,[n(F,{size:"xs",design:"info"},{default:s(()=>[g(d(t(r)("student.attendance.holiday_force_attendance_marked",{attribute:o.meta.holidayReason})),1)]),_:1})])):y("",!0),o.meta.isForceHoliday?(a(),c("div",it,[n(F,{size:"xs",design:"error"},{default:s(()=>[g(d(t(r)("student.attendance.attendance_force_holiday_marked")),1)]),_:1})])):y("",!0),f.meta.isActionable&&!f.meta.isHoliday?(a(),c("div",ct,[n(Q,{vertical:"",modelValue:i.markAsHoliday,"onUpdate:modelValue":l[5]||(l[5]=e=>i.markAsHoliday=e),name:"markAsHoliday",label:t(r)("student.attendance.mark_as_holiday"),error:t(j).markAsHoliday,"onUpdate:error":l[6]||(l[6]=e=>t(j).markAsHoliday=e)},null,8,["modelValue","label","error"]),i.markAsHoliday?(a(),c("div",ut,[n(X,{type:"text",modelValue:i.holidayReason,"onUpdate:modelValue":l[7]||(l[7]=e=>i.holidayReason=e),name:"holidayReason",label:t(r)("student.attendance.holiday_reason"),error:t(j).holidayReason,"onUpdate:error":l[8]||(l[8]=e=>t(j).holidayReason=e)},null,8,["modelValue","label","error"])])):y("",!0)])):y("",!0),f.meta.isActionable?(a(),c("div",mt,[p("div",pt,[i.markAsHoliday?y("",!0):(a(),h(B,{key:0,design:"success",onClick:O},{default:s(()=>[g(d(t(r)("student.attendance.mark_all",{attribute:t(r)("student.attendance.types.present")})),1)]),_:1})),i.markAsHoliday?y("",!0):(a(),h(B,{key:1,design:"danger",onClick:u},{default:s(()=>[g(d(t(r)("student.attendance.mark_all",{attribute:t(r)("student.attendance.types.absent")})),1)]),_:1}))]),p("div",yt,[f.meta.isMarked?(a(),h(B,{key:0,design:"danger",onClick:G},{default:s(()=>[g(d(t(r)("global.remove",{attribute:t(r)("student.attendance.attendance")})),1)]),_:1})):y("",!0),n(B,{design:"primary",onClick:le},{default:s(()=>[g(d(t(r)("global.mark",{attribute:t(r)("student.attendance.attendance")})),1)]),_:1})])])):y("",!0)]),default:s(()=>[(a(!0),c(q,null,S(f.data,e=>(a(),h(z,{key:e.uuid},{default:s(()=>[n(V,{name:"sno","sticky-column":""},{default:s(()=>[n(N,{fontSize:"xs"},{default:s(()=>[g(d(e.sno),1)]),_:2},1024)]),_:2},1024),n(V,{name:"student","sticky-column":""},{default:s(()=>[n(N,{revert:""},{default:s(()=>[g(d(e.name)+" ("+d(e.rollNumber||e.codeNumber)+") ",1)]),_:2},1024)]),_:2},1024),(a(!0),c(q,null,S(e.attendances,(A,M)=>(a(),h(V,{bordered:"","vertical-align":"middle",clickable:A.isActionable,name:`day${M}`,disabled:!A.isAvailable,onClick:bt=>E(A)},{default:s(()=>[A.isHoliday?ee((a(),c("i",We,null,512)),[[Z,A.holidayReason]]):y("",!0),A.isForceHoliday?ee((a(),c("i",Ie,null,512)),[[Z,A.holidayReason]]):A.code=="P"?(a(),c("i",Ye)):A.code=="L"?(a(),c("i",Ke)):A.code=="HD"?(a(),c("i",Je)):A.code=="EL"?(a(),c("i",Ge)):A.code=="A"?(a(),c("i",Qe)):y("",!0)]),_:2},1032,["clickable","name","disabled","onClick"]))),256)),$.value?(a(),c(q,{key:0},[n(V,{bordered:""},{default:s(()=>[p("span",Xe,d(e.summary.cumulative),1)]),_:2},1024),n(V,{bordered:""},{default:s(()=>[p("span",Ze,d(e.summary.present),1)]),_:2},1024),n(V,{bordered:""},{default:s(()=>[p("span",et,d(e.summary.total),1)]),_:2},1024),n(V,{bordered:""},{default:s(()=>[p("span",tt,d(e.summary.presentPercentage),1)]),_:2},1024)],64)):y("",!0)]),_:2},1024))),128)),n(z,null,{default:s(()=>[n(V,{colspan:2,bordered:"",name:"student","sticky-column":""},{default:s(()=>[p("span",at,d(t(r)("student.attendance.types.present")),1)]),_:1}),(a(!0),c(q,null,S(f.meta.dateWiseSummary,e=>(a(),h(V,{bordered:"","vertical-align":"middle",key:e.date},{default:s(()=>[p("span",st,d(e.present),1)]),_:2},1024))),128)),$.value?(a(),h(V,{key:0,bordered:"",colspan:4})):y("",!0)]),_:1}),n(z,null,{default:s(()=>[n(V,{colspan:2,bordered:"",name:"student","sticky-column":""},{default:s(()=>[p("span",ot,d(t(r)("student.attendance.types.absent")),1)]),_:1}),(a(!0),c(q,null,S(f.meta.dateWiseSummary,e=>(a(),h(V,{bordered:"","vertical-align":"middle",key:e.date},{default:s(()=>[p("span",nt,d(e.absent),1)]),_:2},1024))),128)),$.value?(a(),h(V,{key:0,bordered:"",colspan:4})):y("",!0)]),_:1}),n(z,null,{default:s(()=>[n(V,{colspan:2,bordered:"",name:"student","sticky-column":""},{default:s(()=>[p("span",lt,d(t(r)("general.percent"))+" (%)",1)]),_:1}),(a(!0),c(q,null,S(f.meta.dateWiseSummary,e=>(a(),h(V,{bordered:"","vertical-align":"middle",key:e.date},{default:s(()=>[p("span",dt,d(e.presentPercentage),1)]),_:2},1024))),128)),$.value?(a(),h(V,{key:0,bordered:"",colspan:4})):y("",!0)]),_:1})]),_:1},8,["header","meta"])]),_:1},8,["is-loading"]))]),_:1})],64)}}});export{kt as default};
