import{l as F,r as s,q as h,o as y,w as e,d as b,e as t,h as M,j as R,m as j,f as i,a as L,F as S,v as U,s as r,t as d}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(P,{emit:u}){const p=u,f={name:"",code:"",shortcode:""},m=F({...f});return(_,n)=>{const c=s("BaseInput"),k=s("FilterForm");return y(),h(k,{"init-form":f,form:m,onHide:n[3]||(n[3]=a=>p("hide"))},{default:e(()=>[b("div",N,[b("div",E,[t(c,{type:"text",modelValue:m.name,"onUpdate:modelValue":n[0]||(n[0]=a=>m.name=a),name:"name",label:_.$trans("academic.program_type.props.name")},null,8,["modelValue","label"])]),b("div",O,[t(c,{type:"text",modelValue:m.code,"onUpdate:modelValue":n[1]||(n[1]=a=>m.code=a),name:"code",label:_.$trans("academic.program_type.props.code")},null,8,["modelValue","label"])]),b("div",q,[t(c,{type:"text",modelValue:m.shortcode,"onUpdate:modelValue":n[2]||(n[2]=a=>m.shortcode=a),name:"shortcode",label:_.$trans("academic.program_type.props.shortcode")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={name:"AcademicProgramTypeList"},K=Object.assign(G,{setup(P){const u=M(),p=R("emitter");let f=["create","filter"],m=["print","pdf","excel"];const _="academic/programType/",n=j(!1),c=F({}),k=a=>{Object.assign(c,a)};return(a,l)=>{const w=s("PageHeaderAction"),V=s("PageHeader"),C=s("ParentTransition"),A=s("TextMuted"),g=s("DataCell"),v=s("FloatingMenuItem"),B=s("FloatingMenu"),I=s("DataRow"),D=s("BaseButton"),T=s("DataTable"),H=s("ListItem");return y(),h(H,{"init-url":_,onSetItems:k},{header:e(()=>[t(V,{title:a.$trans("academic.program_type.program_type"),navs:[{label:a.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(w,{url:"academic/program-types/",name:"AcademicProgramType",title:a.$trans("academic.program_type.program_type"),actions:i(f),"dropdown-actions":i(m),onToggleFilter:l[0]||(l[0]=o=>n.value=!n.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(C,{appear:"",visibility:n.value},{default:e(()=>[t(z,{onRefresh:l[1]||(l[1]=o=>i(p).emit("listItems")),onHide:l[2]||(l[2]=o=>n.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(C,{appear:"",visibility:!0},{default:e(()=>[t(T,{header:c.headers,meta:c.meta,module:"academic.program_type",onRefresh:l[4]||(l[4]=o=>i(p).emit("listItems"))},{actionButton:e(()=>[t(D,{onClick:l[3]||(l[3]=o=>i(u).push({name:"AcademicProgramTypeCreate"}))},{default:e(()=>[r(d(a.$trans("global.add",{attribute:a.$trans("academic.program_type.program_type")})),1)]),_:1})]),default:e(()=>[(y(!0),L(S,null,U(c.data,o=>(y(),h(I,{key:o.uuid,onDoubleClick:$=>i(u).push({name:"AcademicProgramTypeShow",params:{uuid:o.uuid}})},{default:e(()=>[t(g,{name:"name"},{default:e(()=>[r(d(o.name)+" ",1),t(A,{block:""},{default:e(()=>[r(d(o.alias),1)]),_:2},1024)]),_:2},1024),t(g,{name:"code"},{default:e(()=>[r(d(o.code)+" ",1),t(A,{block:""},{default:e(()=>[r(d(o.shortcode),1)]),_:2},1024)]),_:2},1024),t(g,{name:"createdAt"},{default:e(()=>[r(d(o.createdAt.formatted),1)]),_:2},1024),t(g,{name:"action"},{default:e(()=>[t(B,null,{default:e(()=>[t(v,{icon:"fas fa-arrow-circle-right",onClick:$=>i(u).push({name:"AcademicProgramTypeShow",params:{uuid:o.uuid}})},{default:e(()=>[r(d(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-edit",onClick:$=>i(u).push({name:"AcademicProgramTypeEdit",params:{uuid:o.uuid}})},{default:e(()=>[r(d(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-copy",onClick:$=>i(u).push({name:"AcademicProgramTypeDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[r(d(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-trash",onClick:$=>i(p).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[r(d(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
