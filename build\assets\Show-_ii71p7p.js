import{i as y,u as k,h as C,l as P,r as s,a as V,o as p,e as t,w as a,f as l,q as m,b as _,d as A,s as o,t as r,y as I,F as N}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},D={name:"FinanceFeeHeadShow"},E=Object.assign(D,{setup(R){y();const c=k(),f=C(),g={},b="finance/feeHead/",n=P({...g}),h=e=>{Object.assign(n,e)};return(e,d)=>{const B=s("PageHeaderAction"),F=s("PageHeader"),i=s("BaseDataView"),$=s("BaseButton"),H=s("ShowButton"),w=s("BaseCard"),S=s("ShowItem"),v=s("ParentTransition");return p(),V(N,null,[t(F,{title:e.$trans(l(c).meta.trans,{attribute:e.$trans(l(c).meta.label)}),navs:[{label:e.$trans("finance.finance"),path:"Finance"},{label:e.$trans("finance.fee_head.fee_head"),path:"FinanceFeeHeadList"}]},{default:a(()=>[t(B,{name:"FinanceFeeHead",title:e.$trans("finance.fee_head.fee_head"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(v,{appear:"",visibility:!0},{default:a(()=>[t(S,{"init-url":b,uuid:l(c).params.uuid,onSetItem:h,onRedirectTo:d[1]||(d[1]=u=>l(f).push({name:"FinanceFeeHead"}))},{default:a(()=>[n.uuid?(p(),m(w,{key:0},{title:a(()=>[o(r(n.name),1)]),footer:a(()=>[t(H,null,{default:a(()=>[l(I)("fee-head:edit")?(p(),m($,{key:0,design:"primary",onClick:d[0]||(d[0]=u=>l(f).push({name:"FinanceFeeHeadEdit",params:{uuid:n.uuid}}))},{default:a(()=>[o(r(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:a(()=>[A("dl",T,[t(i,{label:e.$trans("finance.fee_head.props.name")},{default:a(()=>[o(r(n.name),1)]),_:1},8,["label"]),t(i,{label:e.$trans("finance.fee_group.fee_group")},{default:a(()=>{var u;return[o(r(((u=n.group)==null?void 0:u.name)||"-"),1)]}),_:1},8,["label"]),t(i,{class:"col-span-1 sm:col-span-2",label:e.$trans("finance.fee_head.props.description")},{default:a(()=>[o(r(n.description),1)]),_:1},8,["label"]),t(i,{label:e.$trans("general.created_at")},{default:a(()=>[o(r(n.createdAt.formatted),1)]),_:1},8,["label"]),t(i,{label:e.$trans("general.updated_at")},{default:a(()=>[o(r(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
