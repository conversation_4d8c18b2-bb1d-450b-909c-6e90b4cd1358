import{i as N,u as z,h as R,j as E,l as O,r as i,a as p,o as r,e as l,w as e,q as c,b as _,f as d,y as $,s as o,t as s,d as g,F as f,v as B}from"./app-BAwPsakn.js";const q={class:"flex items-center"},U={class:"mr-2"},G={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},J={class:"space-y-4"},K={class:"mt-4 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Q={name:"FormShow"},Y=Object.assign(Q,{setup(W){N();const b=z(),k=R(),F=E("emitter"),S={},A="form/",t=O({...S}),C=a=>{Object.assign(t,a)},v=a=>t.audiences.filter(u=>u.type===a),h=async a=>{F.emit("showActionItem",{uuid:b.params.uuid,action:"updateStatus",data:{status:a},confirmation:!0})};return(a,u)=>{const y=i("BaseButton"),T=i("PageHeaderAction"),P=i("PageHeader"),V=i("BaseBadge"),m=i("BaseDataView"),w=i("TextMuted"),I=i("BaseFieldset"),j=i("ListMedia"),D=i("ShowButton"),H=i("BaseCard"),L=i("ShowItem"),M=i("ParentTransition");return r(),p(f,null,[l(P,{title:a.$trans(d(b).meta.trans,{attribute:a.$trans(d(b).meta.label)}),navs:[{label:a.$trans("form.form"),path:"Form"}]},{default:e(()=>[l(T,{name:"Form",title:a.$trans("form.form"),actions:["list"]},{after:e(()=>[d($)("form-submission:manage")?(r(),c(y,{key:0,design:"success",size:"sm",onClick:u[0]||(u[0]=n=>d(k).push({name:"FormSubmission",params:{uuid:t.uuid}}))},{default:e(()=>[o(s(a.$trans("form.submission.submission")),1)]),_:1})):_("",!0)]),_:1},8,["title"])]),_:1},8,["title","navs"]),l(M,{appear:"",visibility:!0},{default:e(()=>[l(L,{"init-url":A,uuid:d(b).params.uuid,"module-uuid":d(b).params.muuid,onSetItem:C,onRedirectTo:u[4]||(u[4]=n=>d(k).push({name:"Form",params:{uuid:t.uuid}}))},{default:e(()=>[t.uuid?(r(),c(H,{key:0},{title:e(()=>[g("div",q,[g("span",U,s(t.name),1),l(V,{design:t.status.color},{default:e(()=>[o(s(t.status.label),1)]),_:1},8,["design"])])]),action:e(()=>[t.status.value=="draft"&&d($)("form:edit")?(r(),c(y,{key:0,design:"primary",size:"sm",onClick:u[1]||(u[1]=n=>h("publish"))},{default:e(()=>[o(s(a.$trans("form.action.publish")),1)]),_:1})):_("",!0),t.status.value=="published"?(r(),c(y,{key:1,design:"danger",size:"sm",onClick:u[2]||(u[2]=n=>h("draft")&&d($)("form:edit"))},{default:e(()=>[o(s(a.$trans("form.action.unpublish")),1)]),_:1})):_("",!0)]),footer:e(()=>[l(D,null,{default:e(()=>[d($)("form:edit")?(r(),c(y,{key:0,design:"primary",onClick:u[3]||(u[3]=n=>d(k).push({name:"FormEdit",params:{uuid:t.uuid}}))},{default:e(()=>[o(s(a.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[g("dl",G,[l(m,{label:a.$trans("form.props.due_date")},{default:e(()=>[o(s(t.dueDate.formatted),1)]),_:1},8,["label"]),l(m,{label:a.$trans("form.props.summary")},{default:e(()=>[o(s(t.summary),1)]),_:1},8,["label"]),l(m,{label:a.$trans("form.props.audience")},{default:e(()=>[o(s(t.studentAudienceType.label)+" ",1),(r(!0),p(f,null,B(v("student"),n=>(r(),c(w,{block:""},{default:e(()=>[o(s(n.name),1)]),_:2},1024))),256))]),_:1},8,["label"]),l(m,{label:a.$trans("form.props.audience")},{default:e(()=>[t.employeeAudienceType.value?(r(),p(f,{key:0},[o(s(t.employeeAudienceType.label)+" ",1),(r(!0),p(f,null,B(v("employee"),n=>(r(),c(w,{block:""},{default:e(()=>[o(s(n.name),1)]),_:2},1024))),256))],64)):(r(),p(f,{key:1},[o("-")],64))]),_:1},8,["label"])]),l(I,{class:"mt-4"},{legend:e(()=>[o(s(a.$trans("form.props.fields")),1)]),default:e(()=>[g("div",J,[(r(!0),p(f,null,B(t.fields,n=>(r(),c(m,{key:n.uuid,label:n.showLabel?n.label:"",class:"col-span-1 sm:col-span-2"},{default:e(()=>[n.type.value=="paragraph"?(r(),p(f,{key:0},[o(s(n.content),1)],64)):n.type.value=="camera_image"||n.type.value=="file_upload"?(r(),p(f,{key:1},[o(s(n.name)+" ("+s(n.type.label)+") ",1)],64)):(r(),p(f,{key:2},[o(s(n.type.label),1)],64))]),_:2},1032,["label"]))),128))])]),_:1}),g("dl",K,[l(m,{label:a.$trans("form.props.description"),class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(s(t.description),1)]),_:1},8,["label"]),t.media.length>0?(r(),c(m,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[l(j,{media:t.media,url:`/app/forms/${t.uuid}/`},null,8,["media","url"])]),_:1})):_("",!0),l(m,{label:a.$trans("general.created_at")},{default:e(()=>[o(s(t.createdAt.formatted),1)]),_:1},8,["label"]),l(m,{label:a.$trans("general.updated_at")},{default:e(()=>[o(s(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{Y as default};
