import{l as F,r,q as p,o as m,w as e,d as h,e as n,h as j,j as L,y as v,m as M,f as o,a as N,F as S,v as U,s as d,t as u,b as k}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(H,{emit:c}){const y=c,g={name:"",startDate:"",endDate:""},i=F({...g});return(C,l)=>{const f=r("BaseInput"),b=r("DatePicker"),s=r("FilterForm");return m(),p(s,{"init-form":g,form:i,onHide:l[3]||(l[3]=t=>y("hide"))},{default:e(()=>[h("div",E,[h("div",O,[n(f,{type:"text",modelValue:i.name,"onUpdate:modelValue":l[0]||(l[0]=t=>i.name=t),name:"name",label:C.$trans("calendar.holiday.props.name")},null,8,["modelValue","label"])]),h("div",q,[n(b,{start:i.startDate,"onUpdate:start":l[1]||(l[1]=t=>i.startDate=t),end:i.endDate,"onUpdate:end":l[2]||(l[2]=t=>i.endDate=t),name:"dateBetween",as:"range",label:C.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},G={name:"CalendarHolidayList"},K=Object.assign(G,{setup(H){const c=j(),y=L("emitter");let g=["filter"];v("holiday:create")&&g.unshift("create");let i=[];v("holiday:export")&&(i=["print","pdf","excel"]);const C="calendar/holiday/",l=M(!1),f=F({}),b=s=>{Object.assign(f,s)};return(s,t)=>{const B=r("PageHeaderAction"),I=r("PageHeader"),w=r("ParentTransition"),_=r("DataCell"),$=r("FloatingMenuItem"),P=r("FloatingMenu"),V=r("DataRow"),A=r("BaseButton"),T=r("DataTable"),R=r("ListItem");return m(),p(R,{"init-url":C,onSetItems:b},{header:e(()=>[n(I,{title:s.$trans("calendar.holiday.holiday"),navs:[{label:s.$trans("calendar.calendar"),path:"Calendar"}]},{default:e(()=>[n(B,{url:"calendar/holidays/",name:"CalendarHoliday",title:s.$trans("calendar.holiday.holiday"),actions:o(g),"dropdown-actions":o(i),onToggleFilter:t[0]||(t[0]=a=>l.value=!l.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(w,{appear:"",visibility:l.value},{default:e(()=>[n(z,{onRefresh:t[1]||(t[1]=a=>o(y).emit("listItems")),onHide:t[2]||(t[2]=a=>l.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(w,{appear:"",visibility:!0},{default:e(()=>[n(T,{header:f.headers,meta:f.meta,module:"calendar.holiday",onRefresh:t[4]||(t[4]=a=>o(y).emit("listItems"))},{actionButton:e(()=>[o(v)("holiday:create")?(m(),p(A,{key:0,onClick:t[3]||(t[3]=a=>o(c).push({name:"CalendarHolidayCreate"}))},{default:e(()=>[d(u(s.$trans("global.add",{attribute:s.$trans("calendar.holiday.holiday")})),1)]),_:1})):k("",!0)]),default:e(()=>[(m(!0),N(S,null,U(f.data,a=>(m(),p(V,{key:a.uuid,onDoubleClick:D=>o(c).push({name:"CalendarHolidayShow",params:{uuid:a.uuid}})},{default:e(()=>[n(_,{name:"name"},{default:e(()=>[d(u(a.name),1)]),_:2},1024),n(_,{name:"startDate"},{default:e(()=>[d(u(a.startDate.formatted),1)]),_:2},1024),n(_,{name:"endDate"},{default:e(()=>[d(u(a.endDate.formatted),1)]),_:2},1024),n(_,{name:"duration"},{default:e(()=>[d(u(a.duration),1)]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[d(u(a.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(P,null,{default:e(()=>[n($,{icon:"fas fa-arrow-circle-right",onClick:D=>o(c).push({name:"CalendarHolidayShow",params:{uuid:a.uuid}})},{default:e(()=>[d(u(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(v)("holiday:edit")?(m(),p($,{key:0,icon:"fas fa-edit",onClick:D=>o(c).push({name:"CalendarHolidayEdit",params:{uuid:a.uuid}})},{default:e(()=>[d(u(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),o(v)("holiday:create")?(m(),p($,{key:1,icon:"fas fa-copy",onClick:D=>o(c).push({name:"CalendarHolidayDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[d(u(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),o(v)("holiday:delete")?(m(),p($,{key:2,icon:"fas fa-trash",onClick:D=>o(y).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[d(u(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
