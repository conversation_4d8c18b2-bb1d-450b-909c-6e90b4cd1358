import{u as j,i as H,m as L,H as D,l as U,n as M,r as l,q as w,o as p,w as t,d as q,b as k,f as d,s as b,a as C,t as n,h as P,j as Q,e as u,F as E,v as X,J as R,M as Y}from"./app-BAwPsakn.js";const Z={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},ae={key:0,class:"ml-1"},te={key:0,class:"ml-1"},ne={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide","cancel"],setup(S,{emit:y}){const i=j();H();const B=y,r=S,_={exam:"",batch:""};L(!1);const s=D(r.initUrl),c=U({..._});U({exams:r.preRequisites.exams});const h=U({exam:"",batch:"",isLoaded:!(i.query.exam&&i.query.batch)}),$=()=>{B("cancel")},F=async f=>{if(!f){c.batch="";return}c.batch=f||""};return M(async()=>{h.exam=i.query.exam,c.exam=i.query.exam,h.batch=i.query.batch,c.batch=i.query.batch,i.query.batch&&await F(i.query.batch),h.isLoaded=!0}),(f,m)=>{const A=l("BaseSelect"),N=l("BaseSelectSearch"),a=l("FilterForm");return p(),w(a,{"init-form":_,form:c,onCancel:$,onHide:m[4]||(m[4]=e=>B("hide"))},{default:t(()=>[q("div",Z,[q("div",ee,[h.isLoaded?(p(),w(A,{key:0,modelValue:c.exam,"onUpdate:modelValue":m[0]||(m[0]=e=>c.exam=e),name:"exam",label:f.$trans("exam.exam"),"value-prop":"uuid",options:S.preRequisites.exams,error:d(s).exam,"onUpdate:error":m[1]||(m[1]=e=>d(s).exam=e)},{selectedOption:t(e=>{var v,x;return[b(n(e.value.name)+" ",1),e.value.term?(p(),C("span",ae,"("+n(((x=(v=e.value.term)==null?void 0:v.division)==null?void 0:x.name)||f.$trans("general.all"))+")",1)):k("",!0)]}),listOption:t(e=>{var v,x;return[b(n(e.option.name)+" ",1),e.option.term?(p(),C("span",te,"("+n(((x=(v=e.option.term)==null?void 0:v.division)==null?void 0:x.name)||f.$trans("general.all"))+")",1)):k("",!0)]}),_:1},8,["modelValue","label","options","error"])):k("",!0)]),q("div",ne,[h.isLoaded?(p(),w(N,{key:0,name:"batch",label:f.$trans("global.select",{attribute:f.$trans("academic.batch.batch")}),modelValue:c.batch,"onUpdate:modelValue":m[2]||(m[2]=e=>c.batch=e),error:d(s).batch,"onUpdate:error":m[3]||(m[3]=e=>d(s).batch=e),"value-prop":"uuid","init-search":h.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:F},{selectedOption:t(e=>[b(n(e.value.course.name)+" "+n(e.value.name),1)]),listOption:t(e=>[b(n(e.option.course.nameWithTerm)+" "+n(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):k("",!0)])])]),_:1},8,["form"])}}},oe={class:"p-2"},re={class:"divide-y divide-gray-200 dark:divide-gray-700"},le={class:"col-span-4 sm:col-span-1"},ie={class:"mt-1"},ce={key:0,class:"col-span-4 sm:col-span-3"},me={name:"ExamComment"},de=Object.assign(me,{setup(S){const y=j(),i=P(),B=H();Q("emitter");const r={exam:"",batch:"",students:[]},_="exam/comment/",s=L(!1),c=U({exams:[]}),h=D(_),$=U({...r}),F=U({meta:{},showComment:!0}),f=async()=>{s.value=!0,await B.dispatch(_+"preRequisite").then(a=>{s.value=!1,Object.assign(c,a)}).catch(a=>{s.value=!1})},m=()=>{r.exam="",r.batch="",r.students=[],Object.assign($,R(r))},A=async()=>{y.query.batch&&(s.value=!0,await B.dispatch(_+"fetch",{params:y.query}).then(a=>{s.value=!1,r.exam=y.query.exam,r.batch=y.query.batch,r.students=a.data,F.meta=a.meta,Object.assign($,R(r))}).catch(a=>{s.value=!1}))},N=async()=>{await Y()&&(s.value=!0,await B.dispatch(_+"remove",{form:y.query}).then(a=>{s.value=!1,A()}).catch(a=>{s.value=!1}))};return M(async()=>{await f(),await A()}),(a,e)=>{const v=l("BaseButton"),x=l("PageHeaderAction"),T=l("PageHeader"),I=l("ParentTransition"),z=l("BaseAlert"),J=l("BaseCheckbox"),W=l("BaseDataView"),O=l("BaseInput"),G=l("FormAction"),K=l("BaseCard");return p(),C(E,null,[u(T,{title:a.$trans(d(y).meta.label),navs:[{label:a.$trans("exam.exam"),path:"Exam"}]},{default:t(()=>[u(x,null,{default:t(()=>[u(v,{design:"white",onClick:e[0]||(e[0]=o=>d(i).push({name:"ExamMark"}))},{default:t(()=>[b(n(a.$trans("exam.mark")),1)]),_:1}),u(v,{design:"white",onClick:e[1]||(e[1]=o=>d(i).push({name:"ExamObservationMark"}))},{default:t(()=>[b(n(a.$trans("exam.observation_mark")),1)]),_:1}),u(v,{design:"white",onClick:e[2]||(e[2]=o=>d(i).push({name:"ExamAttendance"}))},{default:t(()=>[b(n(a.$trans("student.attendance.attendance")),1)]),_:1})]),_:1})]),_:1},8,["title","navs"]),u(I,{appear:"",visibility:!0},{default:t(()=>[u(se,{onAfterFilter:A,onCancel:m,"init-url":_,"pre-requisites":c},null,8,["pre-requisites"])]),_:1}),u(K,{"no-padding":"","no-content-padding":"","is-loading":s.value},{title:t(()=>[b(n(a.$trans("exam.record")),1)]),action:t(()=>[F.meta.commentRecorded?(p(),w(v,{key:0,design:"error",onClick:N},{default:t(()=>[b(n(a.$trans("global.remove",{attribute:a.$trans("exam.comment")})),1)]),_:1})):k("",!0)]),default:t(()=>[q("div",oe,[$.students.length==0?(p(),w(z,{key:0,size:"xs",design:"error"},{default:t(()=>[b(n(a.$trans("general.errors.record_not_found")),1)]),_:1})):k("",!0)]),$.students.length?(p(),w(G,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":_,action:"store","init-form":r,form:$},{default:t(()=>[q("div",re,[(p(!0),C(E,null,X($.students,(o,V)=>(p(),C("div",{class:"grid grid-cols-4 gap-6 px-4 py-2",key:o.uuid},[q("div",le,[u(W,null,{default:t(()=>[b(n(o.name)+" ("+n(o.rollNumber||o.codeNumber)+") ",1),q("div",ie,[u(J,{modelValue:o.isNotApplicable,"onUpdate:modelValue":g=>o.isNotApplicable=g,name:`students.${V}.isNotApplicable`,label:a.$trans("global.is_not",{attribute:a.$trans("exam.schedule.props.applicable")})},null,8,["modelValue","onUpdate:modelValue","name","label"])])]),_:2},1024)]),o.isNotApplicable?k("",!0):(p(),C("div",ce,[u(O,{type:"text",modelValue:o.result,"onUpdate:modelValue":g=>o.result=g,name:`students.${V}.result`,placeholder:a.$trans("exam.result"),error:d(h)[`students.${V}.result`],"onUpdate:error":g=>d(h)[`students.${V}.result`]=g},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"]),u(O,{type:"text",modelValue:o.comment,"onUpdate:modelValue":g=>o.comment=g,name:`students.${V}.comment`,placeholder:a.$trans("exam.comment"),error:d(h)[`students.${V}.comment`],"onUpdate:error":g=>d(h)[`students.${V}.comment`]=g},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))]))),128))])]),_:1},8,["form"])):k("",!0)]),_:1},8,["is-loading"])],64)}}});export{de as default};
