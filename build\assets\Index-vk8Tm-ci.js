import{l as F,r as l,q as B,o as h,w as e,d as g,e as t,h as j,j as A,m as T,f as i,a as L,F as M,v as N,s as r,t as u}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(V,{emit:d}){const c=d,C=V,b={name:"",alias:"",block:""},m=F({...b}),_=F({blocks:C.preRequisites.blocks});return(p,n)=>{const k=l("BaseInput"),H=l("BaseSelect"),a=l("FilterForm");return h(),B(a,{"init-form":b,form:m,onHide:n[3]||(n[3]=o=>c("hide"))},{default:e(()=>[g("div",O,[g("div",U,[t(k,{type:"text",modelValue:m.name,"onUpdate:modelValue":n[0]||(n[0]=o=>m.name=o),name:"name",label:p.$trans("hostel.floor.props.name")},null,8,["modelValue","label"])]),g("div",E,[t(k,{type:"text",modelValue:m.alias,"onUpdate:modelValue":n[1]||(n[1]=o=>m.alias=o),name:"alias",label:p.$trans("hostel.floor.props.alias")},null,8,["modelValue","label"])]),g("div",z,[t(H,{modelValue:m.block,"onUpdate:modelValue":n[2]||(n[2]=o=>m.block=o),name:"block","label-prop":"name","value-prop":"uuid",label:p.$trans("hostel.block.block"),options:_.blocks},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},J={name:"HostelFloorList"},Q=Object.assign(J,{setup(V){const d=j(),c=A("emitter");let C=["create","filter"],b=["print","pdf","excel"];const m="hostel/floor/",_=F({blocks:[]}),p=T(!1),n=F({}),k=a=>{Object.assign(n,a)},H=a=>{Object.assign(_,a)};return(a,o)=>{const w=l("BaseButton"),R=l("PageHeaderAction"),D=l("PageHeader"),I=l("ParentTransition"),f=l("DataCell"),v=l("FloatingMenuItem"),q=l("FloatingMenu"),y=l("DataRow"),P=l("DataTable"),S=l("ListItem");return h(),B(S,{"init-url":m,"pre-requisites":!0,onSetPreRequisites:H,onSetItems:k},{header:e(()=>[t(D,{title:a.$trans("hostel.floor.floor"),navs:[{label:a.$trans("hostel.hostel"),path:"Hostel"}]},{default:e(()=>[t(R,{url:"hostel/floors/",name:"HostelFloor",title:a.$trans("hostel.floor.floor"),actions:i(C),"dropdown-actions":i(b),onToggleFilter:o[2]||(o[2]=s=>p.value=!p.value)},{default:e(()=>[t(w,{design:"white",onClick:o[0]||(o[0]=s=>i(d).push({name:"HostelBlock"}))},{default:e(()=>[r(u(a.$trans("hostel.block.block")),1)]),_:1}),t(w,{design:"white",onClick:o[1]||(o[1]=s=>i(d).push({name:"HostelRoom"}))},{default:e(()=>[r(u(a.$trans("hostel.room.room")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(I,{appear:"",visibility:p.value},{default:e(()=>[t(G,{onRefresh:o[3]||(o[3]=s=>i(c).emit("listItems")),"pre-requisites":_,onHide:o[4]||(o[4]=s=>p.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(I,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:n.headers,meta:n.meta,module:"hostel.floor",onRefresh:o[6]||(o[6]=s=>i(c).emit("listItems"))},{actionButton:e(()=>[t(w,{onClick:o[5]||(o[5]=s=>i(d).push({name:"HostelFloorCreate"}))},{default:e(()=>[r(u(a.$trans("global.add",{attribute:a.$trans("hostel.floor.floor")})),1)]),_:1})]),default:e(()=>[(h(!0),L(M,null,N(n.data,s=>(h(),B(y,{key:s.uuid,onDoubleClick:$=>i(d).push({name:"HostelFloorShow",params:{uuid:s.uuid}})},{default:e(()=>[t(f,{name:"name"},{default:e(()=>[r(u(s.name),1)]),_:2},1024),t(f,{name:"alias"},{default:e(()=>[r(u(s.alias),1)]),_:2},1024),t(f,{name:"block"},{default:e(()=>[r(u(s.blockName),1)]),_:2},1024),t(f,{name:"createdAt"},{default:e(()=>[r(u(s.createdAt.formatted),1)]),_:2},1024),t(f,{name:"action"},{default:e(()=>[t(q,null,{default:e(()=>[t(v,{icon:"fas fa-arrow-circle-right",onClick:$=>i(d).push({name:"HostelFloorShow",params:{uuid:s.uuid}})},{default:e(()=>[r(u(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-edit",onClick:$=>i(d).push({name:"HostelFloorEdit",params:{uuid:s.uuid}})},{default:e(()=>[r(u(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-copy",onClick:$=>i(d).push({name:"HostelFloorDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[r(u(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-trash",onClick:$=>i(c).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[r(u(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
