import{u as T,G as B,H as q,l as L,r as u,q as N,o as p,w as m,d as i,e as l,f as a,a as b,b as v,F as O,s as y,t as n,v as w,J as R}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-4 gap-6"},I={class:"col-span-4 sm:col-span-1"},W={class:"col-span-4 sm:col-span-1"},G={class:"col-span-4 sm:col-span-1"},J={class:"mt-4 grid grid-cols-4 gap-6"},z={class:"col-span-3 sm:col-span-1"},K={class:"col-span-4 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={key:1,class:"col-span-2 sm:col-span-1"},P={key:2,class:"col-span-2 sm:col-span-1"},x={key:3,class:"col-span-2 sm:col-span-1"},ee={class:"col-span-2 sm:col-span-1"},oe={class:"mt-4 grid grid-cols-1 gap-4"},te={class:"col"},re={class:"col"},se={class:"flex space-x-4"},ae=["src"],ne=["src"],le={class:"col"},ie={name:"ReceptionVisitorLogForm"},pe=Object.assign(ie,{setup(F){const _=T(),d={purpose:"",type:"",count:1,entryAt:"",exitAt:"",visitor:"",name:"",contactNumber:"",remarks:"",images:[],media:[],mediaUpdated:!1,mediaToken:B(),mediaHash:[]},A="reception/visitorLog/",s=q(A),k=L({types:[],purposes:[]}),t=L({...d}),c=L({isLoaded:!_.params.uuid}),H=r=>{_.params.uuid||(d.entryAt=r.currentDateTime,Object.assign(t,R(d))),Object.assign(k,r)},S=()=>{t.mediaToken=B(),t.mediaHash=[]},j=r=>{var o,U,$,g,f,V;Object.assign(d,{...r,type:r.type.value,entryAt:r.entryAt.at,exitAt:((o=r.exitAt)==null?void 0:o.at)||"",purpose:((U=r.purpose)==null?void 0:U.uuid)||"",employee:(($=r.employee)==null?void 0:$.uuid)||"",visitor:((g=r.visitor)==null?void 0:g.uuid)||"",images:r.images||[]}),Object.assign(t,R(d)),c.visitor=(f=r.visitor)==null?void 0:f.uuid,c.employee=(V=r.employee)==null?void 0:V.uuid,c.isLoaded=!0};return(r,o)=>{const U=u("BaseSelect"),$=u("DatePicker"),g=u("BaseInput"),f=u("TextMuted"),V=u("BaseSelectSearch"),C=u("BaseTextarea"),D=u("WebcamCapture"),M=u("MediaUpload"),h=u("FormAction");return p(),N(h,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":A,"init-form":d,form:t,"set-form":j,redirect:"ReceptionVisitorLog",onResetMediaFiles:S},{default:m(()=>[i("div",E,[i("div",I,[l(U,{modelValue:t.purpose,"onUpdate:modelValue":o[0]||(o[0]=e=>t.purpose=e),name:"purpose",label:r.$trans("reception.visitor_log.props.purpose"),options:k.purposes,"label-prop":"name","value-prop":"uuid",error:a(s).purpose,"onUpdate:error":o[1]||(o[1]=e=>a(s).purpose=e)},null,8,["modelValue","label","options","error"])]),i("div",W,[l($,{as:"datetime",modelValue:t.entryAt,"onUpdate:modelValue":o[2]||(o[2]=e=>t.entryAt=e),name:"entryAt",label:r.$trans("reception.visitor_log.props.entry_at"),"no-clear":"",error:a(s).entryAt,"onUpdate:error":o[3]||(o[3]=e=>a(s).entryAt=e)},null,8,["modelValue","label","error"])]),i("div",G,[l($,{as:"datetime",modelValue:t.exitAt,"onUpdate:modelValue":o[4]||(o[4]=e=>t.exitAt=e),name:"exitAt",label:r.$trans("reception.visitor_log.props.exit_at"),error:a(s).exitAt,"onUpdate:error":o[5]||(o[5]=e=>a(s).exitAt=e)},null,8,["modelValue","label","error"])])]),i("div",J,[i("div",z,[l(g,{type:"number",modelValue:t.count,"onUpdate:modelValue":o[6]||(o[6]=e=>t.count=e),name:"count",min:1,label:r.$trans("reception.visitor_log.props.count"),error:a(s).count,"onUpdate:error":o[7]||(o[7]=e=>a(s).count=e)},null,8,["modelValue","label","error"])]),i("div",K,[l(U,{modelValue:t.type,"onUpdate:modelValue":o[8]||(o[8]=e=>t.type=e),name:"type",label:r.$trans("reception.visitor_log.props.type"),options:k.types,error:a(s).type,"onUpdate:error":o[9]||(o[9]=e=>a(s).type=e)},null,8,["modelValue","label","options","error"])]),t.type=="other"?(p(),b(O,{key:0},[i("div",Q,[l(g,{type:"text",modelValue:t.name,"onUpdate:modelValue":o[10]||(o[10]=e=>t.name=e),name:"name",label:r.$trans("reception.visitor_log.props.name"),error:a(s).name,"onUpdate:error":o[11]||(o[11]=e=>a(s).name=e)},null,8,["modelValue","label","error"])]),i("div",X,[l(g,{type:"text",modelValue:t.companyName,"onUpdate:modelValue":o[12]||(o[12]=e=>t.companyName=e),name:"companyName",label:r.$trans("reception.visitor_log.props.company_name"),error:a(s).companyName,"onUpdate:error":o[13]||(o[13]=e=>a(s).companyName=e)},null,8,["modelValue","label","error"])]),i("div",Y,[l(g,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":o[14]||(o[14]=e=>t.contactNumber=e),name:"contactNumber",label:r.$trans("reception.visitor_log.props.contact_number"),error:a(s).contactNumber,"onUpdate:error":o[15]||(o[15]=e=>a(s).contactNumber=e)},null,8,["modelValue","label","error"])])],64)):v("",!0),t.type=="student"?(p(),b("div",Z,[c.isLoaded?(p(),N(V,{key:0,name:"visitor",label:r.$trans("student.student"),placeholder:r.$trans("global.select",{attribute:r.$trans("student.student")}),modelValue:t.visitor,"onUpdate:modelValue":o[16]||(o[16]=e=>t.visitor=e),error:a(s).visitor,"onUpdate:error":o[17]||(o[17]=e=>a(s).visitor=e),"value-prop":"uuid","init-search":c.visitor,"search-key":"name","search-action":"student/summary"},{selectedOption:m(e=>[y(n(e.value.name)+" ("+n(e.value.courseName+" "+e.value.batchName)+") ",1),l(f,null,{default:m(()=>[y(n(e.value.codeNumber),1)]),_:2},1024)]),listOption:m(e=>[y(n(e.option.name)+" ("+n(e.option.courseName+" "+e.option.batchName)+") ",1),l(f,null,{default:m(()=>[y(n(e.option.codeNumber),1)]),_:2},1024)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):v("",!0)])):v("",!0),t.type=="guardian"?(p(),b("div",P,[c.isLoaded?(p(),N(V,{key:0,name:"visitor",label:r.$trans("guardian.guardian"),placeholder:r.$trans("global.select",{attribute:r.$trans("guardian.guardian")}),modelValue:t.visitor,"onUpdate:modelValue":o[18]||(o[18]=e=>t.visitor=e),error:a(s).visitor,"onUpdate:error":o[19]||(o[19]=e=>a(s).visitor=e),"value-prop":"uuid","init-search":c.visitor,"search-key":"name","search-action":"guardian/list"},{selectedOption:m(e=>[y(n(e.value.name)+" ("+n(e.value.contactNumber)+") ",1)]),listOption:m(e=>[y(n(e.option.name)+" ("+n(e.option.contactNumber)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):v("",!0)])):v("",!0),t.type=="employee"?(p(),b("div",x,[c.isLoaded?(p(),N(V,{key:0,name:"visitor",label:r.$trans("employee.employee"),placeholder:r.$trans("global.select",{attribute:r.$trans("employee.employee")}),modelValue:t.visitor,"onUpdate:modelValue":o[20]||(o[20]=e=>t.visitor=e),error:a(s).visitor,"onUpdate:error":o[21]||(o[21]=e=>a(s).visitor=e),"value-prop":"uuid","init-search":c.visitor,"search-key":"name","search-action":"employee/summary"},{selectedOption:m(e=>[y(n(e.value.name)+" ("+n(e.value.designation)+") ",1)]),listOption:m(e=>[y(n(e.option.name)+" ("+n(e.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):v("",!0)])):v("",!0),i("div",ee,[c.isLoaded?(p(),N(V,{key:0,name:"employee",label:r.$trans("reception.visitor_log.props.whom_to_meet"),placeholder:r.$trans("global.select",{attribute:r.$trans("reception.visitor_log.props.whom_to_meet")}),modelValue:t.employee,"onUpdate:modelValue":o[22]||(o[22]=e=>t.employee=e),error:a(s).employee,"onUpdate:error":o[23]||(o[23]=e=>a(s).employee=e),"value-prop":"uuid","init-search":c.employee,"search-key":"name","search-action":"employee/summary"},{selectedOption:m(e=>[y(n(e.value.name)+" ("+n(e.value.designation)+") ",1)]),listOption:m(e=>[y(n(e.option.name)+" ("+n(e.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):v("",!0)])]),i("div",oe,[i("div",te,[l(C,{rows:1,modelValue:t.remarks,"onUpdate:modelValue":o[24]||(o[24]=e=>t.remarks=e),name:"remarks",label:r.$trans("reception.visitor_log.props.remarks"),error:a(s).remarks,"onUpdate:error":o[25]||(o[25]=e=>a(s).remarks=e)},null,8,["modelValue","label","error"])]),i("div",re,[l(D,{onCompleted:o[26]||(o[26]=e=>t.images=e)}),i("div",se,[(p(!0),b(O,null,w(t.images,e=>(p(),b("div",{key:e.id},[e.image?(p(),b("img",{key:0,src:e.image},null,8,ae)):v("",!0),e.url?(p(),b("img",{key:1,src:e.url},null,8,ne)):v("",!0)]))),128))])]),i("div",le,[l(M,{multiple:"",label:r.$trans("general.file"),module:"visitor_log",media:t.media,"media-token":t.mediaToken,onIsUpdated:o[27]||(o[27]=e=>t.mediaUpdated=!0),onSetHash:o[28]||(o[28]=e=>t.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),me={name:"ReceptionVisitorLogAction"},ue=Object.assign(me,{setup(F){const _=T();return(d,A)=>{const s=u("PageHeaderAction"),k=u("PageHeader"),t=u("ParentTransition");return p(),b(O,null,[l(k,{title:d.$trans(a(_).meta.trans,{attribute:d.$trans(a(_).meta.label)}),navs:[{label:d.$trans("reception.reception"),path:"Reception"},{label:d.$trans("reception.visitor_log.visitor_log"),path:"ReceptionVisitorLogList"}]},{default:m(()=>[l(s,{name:"ReceptionVisitorLog",title:d.$trans("reception.visitor_log.visitor_log"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(t,{appear:"",visibility:!0},{default:m(()=>[l(pe)]),_:1})],64)}}});export{ue as default};
