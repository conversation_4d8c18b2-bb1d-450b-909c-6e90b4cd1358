import{h as R,H as F,l as g,r as d,q as y,o as f,w as u,d as l,e as n,f as s,t as H,J as S,u as k,a as T,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},w={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3"},G={name:"AcademicPeriodForm"},K=Object.assign(G,{setup(v){const p=R(),i={session:"",name:"",code:"",shortcode:"",alias:"",startDate:"",endDate:"",enableRegistration:!1,description:""},b="academic/period/",t=F(b),m=g({sessions:[]}),a=g({...i}),D=r=>{Object.assign(m,r)},U=r=>{var e;Object.assign(i,{...r,startDate:r.startDate.value,endDate:r.endDate.value,session:((e=r.session)==null?void 0:e.uuid)||""}),Object.assign(a,S(i))};return(r,e)=>{const $=d("BaseSelect"),P=d("HelperText"),c=d("BaseInput"),V=d("DatePicker"),_=d("BaseSwitch"),A=d("BaseTextarea"),B=d("FormAction");return f(),y(B,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:D,"init-url":b,"init-form":i,form:a,"set-form":U,redirect:"AcademicPeriod"},{default:u(()=>[l("div",q,[l("div",w,[n($,{modelValue:a.session,"onUpdate:modelValue":e[0]||(e[0]=o=>a.session=o),name:"session",label:r.$trans("academic.session.session"),"label-prop":"name","value-prop":"uuid",options:m.sessions,error:s(t).session,"onUpdate:error":e[1]||(e[1]=o=>s(t).session=o)},null,8,["modelValue","label","options","error"]),n(P,null,{default:u(()=>[l("span",{class:"cursor-pointer text-primary",onClick:e[2]||(e[2]=o=>s(p).push({name:"AcademicSession"}))},H(r.$trans("academic.period.session_info")),1)]),_:1})]),l("div",O,[n(c,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[3]||(e[3]=o=>a.name=o),name:"name",label:r.$trans("academic.period.props.name"),error:s(t).name,"onUpdate:error":e[4]||(e[4]=o=>s(t).name=o)},null,8,["modelValue","label","error"])]),l("div",C,[n(c,{type:"text",modelValue:a.code,"onUpdate:modelValue":e[5]||(e[5]=o=>a.code=o),name:"code",label:r.$trans("academic.period.props.code"),error:s(t).code,"onUpdate:error":e[6]||(e[6]=o=>s(t).code=o)},null,8,["modelValue","label","error"])]),l("div",E,[n(c,{type:"text",modelValue:a.shortcode,"onUpdate:modelValue":e[7]||(e[7]=o=>a.shortcode=o),name:"shortcode",label:r.$trans("academic.period.props.shortcode"),error:s(t).shortcode,"onUpdate:error":e[8]||(e[8]=o=>s(t).shortcode=o)},null,8,["modelValue","label","error"])]),l("div",I,[n(c,{type:"text",modelValue:a.alias,"onUpdate:modelValue":e[9]||(e[9]=o=>a.alias=o),name:"alias",label:r.$trans("academic.period.props.alias"),error:s(t).alias,"onUpdate:error":e[10]||(e[10]=o=>s(t).alias=o)},null,8,["modelValue","label","error"])]),l("div",N,[n(V,{modelValue:a.startDate,"onUpdate:modelValue":e[11]||(e[11]=o=>a.startDate=o),name:"startDate",label:r.$trans("academic.period.props.start_date"),"no-clear":"",error:s(t).startDate,"onUpdate:error":e[12]||(e[12]=o=>s(t).startDate=o)},null,8,["modelValue","label","error"])]),l("div",z,[n(V,{modelValue:a.endDate,"onUpdate:modelValue":e[13]||(e[13]=o=>a.endDate=o),name:"endDate",label:r.$trans("academic.period.props.end_date"),"no-clear":"",error:s(t).endDate,"onUpdate:error":e[14]||(e[14]=o=>s(t).endDate=o)},null,8,["modelValue","label","error"])]),l("div",J,[n(_,{vertical:"",modelValue:a.enableRegistration,"onUpdate:modelValue":e[15]||(e[15]=o=>a.enableRegistration=o),name:"enableRegistration",label:r.$trans("global.enable",{attribute:r.$trans("student.registration.registration")}),error:s(t).enableRegistration,"onUpdate:error":e[16]||(e[16]=o=>s(t).enableRegistration=o)},null,8,["modelValue","label","error"])]),l("div",L,[n(A,{modelValue:a.description,"onUpdate:modelValue":e[17]||(e[17]=o=>a.description=o),name:"description",label:r.$trans("academic.period.props.description"),error:s(t).description,"onUpdate:error":e[18]||(e[18]=o=>s(t).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),M={name:"AcademicPeriodAction"},W=Object.assign(M,{setup(v){const p=k();return(i,b)=>{const t=d("PageHeaderAction"),m=d("PageHeader"),a=d("ParentTransition");return f(),T(j,null,[n(m,{title:i.$trans(s(p).meta.trans,{attribute:i.$trans(s(p).meta.label)}),navs:[{label:i.$trans("academic.academic"),path:"Academic"},{label:i.$trans("academic.period.period"),path:"AcademicPeriodList"}]},{default:u(()=>[n(t,{name:"AcademicPeriod",title:i.$trans("academic.period.period"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(a,{appear:"",visibility:!0},{default:u(()=>[n(K)]),_:1})],64)}}});export{W as default};
