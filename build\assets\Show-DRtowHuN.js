import{i as S,u as v,h as y,l as k,r as n,a as C,o as p,e as o,w as t,f as i,q as P,b as V,d as N,s,t as l,F as A}from"./app-BAwPsakn.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"HostelRoomShow"},E=Object.assign(T,{setup(D){S();const m=v(),d=y(),c={},_="hostel/room/",a=k({...c}),f=e=>{Object.assign(a,e)};return(e,u)=>{const b=n("PageHeaderAction"),h=n("PageHeader"),r=n("BaseDataView"),g=n("BaseButton"),B=n("ShowButton"),$=n("BaseCard"),H=n("ShowItem"),w=n("ParentTransition");return p(),C(A,null,[o(h,{title:e.$trans(i(m).meta.trans,{attribute:e.$trans(i(m).meta.label)}),navs:[{label:e.$trans("hostel.hostel"),path:"Hostel"},{label:e.$trans("hostel.room.room"),path:"HostelRoomList"}]},{default:t(()=>[o(b,{name:"HostelRoom",title:e.$trans("hostel.room.room"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(w,{appear:"",visibility:!0},{default:t(()=>[o(H,{"init-url":_,uuid:i(m).params.uuid,onSetItem:f,onRedirectTo:u[1]||(u[1]=R=>i(d).push({name:"HostelRoom"}))},{default:t(()=>[a.uuid?(p(),P($,{key:0},{title:t(()=>[s(l(a.name),1)]),footer:t(()=>[o(B,null,{default:t(()=>[o(g,{design:"primary",onClick:u[0]||(u[0]=R=>i(d).push({name:"HostelRoomEdit",params:{uuid:a.uuid}}))},{default:t(()=>[s(l(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[N("dl",I,[o(r,{label:e.$trans("hostel.room.props.name")},{default:t(()=>[s(l(a.name),1)]),_:1},8,["label"]),o(r,{label:e.$trans("hostel.room.props.number")},{default:t(()=>[s(l(a.number),1)]),_:1},8,["label"]),o(r,{label:e.$trans("hostel.floor.floor")},{default:t(()=>[s(l(a.floorNameWithBlock),1)]),_:1},8,["label"]),o(r,{label:e.$trans("hostel.room.props.capacity")},{default:t(()=>[s(l(a.capacity),1)]),_:1},8,["label"]),o(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("hostel.room.props.description")},{default:t(()=>[s(l(a.description),1)]),_:1},8,["label"]),o(r,{label:e.$trans("general.created_at")},{default:t(()=>[s(l(a.createdAt.formatted),1)]),_:1},8,["label"]),o(r,{label:e.$trans("general.updated_at")},{default:t(()=>[s(l(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
