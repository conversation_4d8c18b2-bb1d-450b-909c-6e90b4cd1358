import{u as y,j as V,H as w,l as b,r as e,a as B,o as F,e as a,f as t,w as r,d as u,s as U,t as x,F as P}from"./app-BAwPsakn.js";const h={class:"grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},C={name:"UtilityConfigGeneral"},N=Object.assign(C,{setup(E){const p=y(),o=V("$trans"),l="config/",c=w(l),d={todoView:"",type:"utility"},i=b({...d});return(G,n)=>{const m=e("PageHeader"),_=e("BaseLabel"),f=e("BaseRadioGroup"),g=e("FormAction"),v=e("ParentTransition");return F(),B(P,null,[a(m,{title:t(o)(t(p).meta.label),navs:[{label:t(o)("utility.utility"),path:"Utility"}]},null,8,["title","navs"]),a(v,{appear:"",visibility:!0},{default:r(()=>[a(g,{"init-url":l,"data-fetch":"utility",action:"store","init-form":d,form:i,"stay-on":"",redirect:"Utility"},{default:r(()=>[u("div",h,[u("div",j,[a(_,null,{default:r(()=>[U(x(t(o)("utility.config.props.todo_view")),1)]),_:1}),a(f,{"top-margin":"",options:[{label:t(o)("general.views.list"),value:"list"},{label:t(o)("general.views.board"),value:"board"}],name:"todoView",modelValue:i.todoView,"onUpdate:modelValue":n[0]||(n[0]=s=>i.todoView=s),error:t(c).todoView,"onUpdate:error":n[1]||(n[1]=s=>t(c).todoView=s),horizontal:""},null,8,["options","modelValue","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{N as default};
