import{u as D,j as Z,G as N,H as x,m as ee,l as j,n as oe,r as u,a as w,o as _,e as l,w as m,d as i,b as R,f as r,q as A,s as c,t as p,y as te,F as T,v as ne,M as re,J as ae}from"./app-BAwPsakn.js";import{_ as se}from"./VendorForm-Bvw37m0J.js";const le={class:"grid grid-cols-3 gap-6"},ie={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-2 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},ce=["onClick"],ve={class:"mt-4 grid grid-cols-4 gap-4"},ye={class:"col-span-4 sm:col-span-1"},be={class:"col-span-4 sm:col-span-1"},_e={class:"col-span-4 sm:col-span-1"},fe={class:"col-span-4 sm:col-span-1"},$e={class:"mt-4"},ke={class:"mt-4 grid grid-cols-1 gap-4"},Ve={class:"col"},ge={class:"col"},he={name:"InventoryStockPurchaseForm"},Ue=Object.assign(he,{setup(E){const f=D(),v=Z("emitter"),$={vendor:"",date:"",voucherNumber:"",place:"",items:[],description:"",media:[],mediaUpdated:!1,mediaToken:N(),mediaHash:[]},k={uuid:N(),item:"",quantity:1,unitPrice:0,amount:"",description:""},B="inventory/stockPurchase/",a=x(B),V=ee(!1),q=j({inventories:[],vendors:[],places:[]}),S=j({isLoaded:!f.params.uuid}),n=j({...$}),L=t=>{Object.assign(q,t)},G=()=>{n.mediaToken=N(),n.mediaHash=[]},M=()=>{n.items.push({...k,uuid:N()})},J=async t=>{await re()&&(n.items.length==1?n.items=[k]:n.items.splice(t,1))},z=t=>{var y,F,C,b,H,g,I;let o=[];t.items.forEach(h=>{o.push({...h,unitPrice:h.unitPrice.value,amount:h.amount.value})}),Object.assign($,{...t,items:o,date:(y=t.date)==null?void 0:y.value,inventory:((F=t.inventory)==null?void 0:F.uuid)||"",vendor:((C=t.vendor)==null?void 0:C.uuid)||"",place:((b=t.place)==null?void 0:b.uuid)||""}),Object.assign(n,ae($)),S.inventory=((H=t.inventory)==null?void 0:H.uuid)||"",S.vendor=((g=t.vendor)==null?void 0:g.uuid)||"",S.place=((I=t.place)==null?void 0:I.uuid)||"",S.isLoaded=!0},K=t=>{t!=n.inventory&&(n.items=[k])},Q=()=>{V.value=!1,v.emit("refreshPreRequisites")};return oe(async()=>{f.params.uuid||M()}),(t,o)=>{const y=u("BaseSelect"),F=u("HelperText"),C=u("DatePicker"),b=u("BaseInput"),H=u("BaseSelectSearch"),g=u("BaseTextarea"),I=u("BaseFieldset"),h=u("BaseBadge"),W=u("MediaUpload"),X=u("FormAction"),Y=u("BaseModal");return _(),w(T,null,[l(X,{"pre-requisites":!0,onSetPreRequisites:L,"init-url":B,"init-form":$,form:n,"set-form":z,redirect:"InventoryStockPurchase",onResetMediaFiles:G},{default:m(()=>[i("div",le,[i("div",ie,[l(y,{modelValue:n.inventory,"onUpdate:modelValue":o[0]||(o[0]=e=>n.inventory=e),name:"inventory",label:t.$trans("inventory.inventory"),options:q.inventories,"label-prop":"name","value-prop":"uuid",error:r(a).inventory,"onUpdate:error":o[1]||(o[1]=e=>r(a).inventory=e),onChange:K},null,8,["modelValue","label","options","error"])]),i("div",de,[l(y,{modelValue:n.vendor,"onUpdate:modelValue":o[2]||(o[2]=e=>n.vendor=e),name:"vendor",label:t.$trans("inventory.vendor"),options:q.vendors,"value-prop":"uuid",error:r(a).vendor,"onUpdate:error":o[3]||(o[3]=e=>r(a).vendor=e)},{selectedOption:m(e=>[c(p(e.value.name)+" ("+p(e.value.type.name)+") ",1)]),listOption:m(e=>[c(p(e.option.name)+" ("+p(e.option.type.name)+") ",1)]),_:1},8,["modelValue","label","options","error"]),r(te)("ledger:create")?(_(),A(F,{key:0,cursor:"","text-right":"",onClick:o[4]||(o[4]=e=>V.value=!0)},{default:m(()=>[c(p(t.$trans("global.add",{attribute:t.$trans("inventory.vendor")})),1)]),_:1})):R("",!0)]),i("div",ue,[l(y,{modelValue:n.place,"onUpdate:modelValue":o[5]||(o[5]=e=>n.place=e),name:"place",label:t.$trans("inventory.place"),options:q.places,"label-prop":"fullName","value-prop":"uuid",error:r(a).place,"onUpdate:error":o[6]||(o[6]=e=>r(a).place=e)},null,8,["modelValue","label","options","error"])]),i("div",pe,[l(C,{modelValue:n.date,"onUpdate:modelValue":o[7]||(o[7]=e=>n.date=e),name:"date",label:t.$trans("inventory.stock_purchase.props.date"),"no-clear":"",error:r(a).date,"onUpdate:error":o[8]||(o[8]=e=>r(a).date=e)},null,8,["modelValue","label","error"])]),i("div",me,[l(b,{type:"text",modelValue:n.voucherNumber,"onUpdate:modelValue":o[9]||(o[9]=e=>n.voucherNumber=e),name:"voucherNumber",label:t.$trans("inventory.stock_purchase.props.voucher_number"),error:r(a).voucherNumber,"onUpdate:error":o[10]||(o[10]=e=>r(a).voucherNumber=e)},null,8,["modelValue","label","error"])])]),n.inventory?(_(),w(T,{key:0},[(_(!0),w(T,null,ne(n.items,(e,d)=>(_(),A(I,{class:"mt-4",key:e.uuid},{legend:m(()=>[c(p(t.$trans("inventory.item"))+" "+p(d+1)+". ",1),i("span",{class:"text-danger ml-2 cursor-pointer",onClick:O=>J(d)},o[17]||(o[17]=[i("i",{class:"fas fa-times-circle"},null,-1)]),8,ce)]),default:m(()=>{var O;return[i("div",ve,[i("div",ye,[l(H,{name:`items.${d}.item`,label:t.$trans("global.select",{attribute:t.$trans("inventory.stock_item.stock_item")}),modelValue:e.item,"onUpdate:modelValue":s=>e.item=s,error:r(a)[`items.${d}.item`],"onUpdate:error":s=>r(a)[`items.${d}.item`]=s,"value-prop":"uuid","object-prop":!0,"init-search":(O=e==null?void 0:e.item)==null?void 0:O.name,"init-search-key":"name","search-action":"inventory/stockItem/list","additional-search-query":{inventory:n.inventory}},{selectedOption:m(s=>{var U,P;return[c(p(s.value.name)+" "+p((P=(U=s.value)==null?void 0:U.category)==null?void 0:P.name),1)]}),listOption:m(s=>{var U,P;return[c(p(s.option.name)+" "+p((P=(U=s.option)==null?void 0:U.category)==null?void 0:P.name),1)]}),_:2},1032,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","init-search","additional-search-query"])]),i("div",be,[l(b,{type:"number",step:.01,modelValue:e.quantity,"onUpdate:modelValue":s=>e.quantity=s,name:`items.${d}.quantity`,label:t.$trans("inventory.stock_purchase.props.quantity"),error:r(a)[`items.${d}.quantity`],"onUpdate:error":s=>r(a)[`items.${d}.quantity`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),i("div",_e,[l(b,{currency:"",modelValue:e.unitPrice,"onUpdate:modelValue":s=>e.unitPrice=s,name:`items.${d}.unitPrice`,label:t.$trans("inventory.stock_purchase.props.unit_price"),error:r(a)[`items.${d}.unitPrice`],"onUpdate:error":s=>r(a)[`items.${d}.unitPrice`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),i("div",fe,[l(g,{rows:1,modelValue:e.description,"onUpdate:modelValue":s=>e.description=s,name:`items.${d}.description`,label:t.$trans("inventory.stock_purchase.props.description"),error:r(a)[`items.${d}.description`],"onUpdate:error":s=>r(a)[`items.${d}.description`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]}),_:2},1024))),128)),i("div",$e,[l(h,{design:"primary",onClick:M,class:"cursor-pointer"},{default:m(()=>[c(p(t.$trans("global.add",{attribute:t.$trans("inventory.stock_item.stock_item")})),1)]),_:1})])],64)):R("",!0),i("div",ke,[i("div",Ve,[l(g,{rows:1,modelValue:n.description,"onUpdate:modelValue":o[11]||(o[11]=e=>n.description=e),name:"description",label:t.$trans("inventory.stock_purchase.props.description"),error:r(a).description,"onUpdate:error":o[12]||(o[12]=e=>r(a).description=e)},null,8,["modelValue","label","error"])]),i("div",ge,[l(W,{multiple:"",label:t.$trans("general.file"),module:"stock_purchase",media:n.media,"media-token":n.mediaToken,onIsUpdated:o[13]||(o[13]=e=>n.mediaUpdated=!0),onSetHash:o[14]||(o[14]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"]),l(Y,{show:V.value,onClose:o[16]||(o[16]=e=>V.value=!1)},{title:m(()=>[c(p(t.$trans("global.add",{attribute:t.$trans("inventory.vendor")})),1)]),default:m(()=>[l(se,{"is-modal":!0,"after-submit":Q,onCancelled:o[15]||(o[15]=e=>V.value=!1)})]),_:1},8,["show"])],64)}}}),Pe={name:"InventoryStockPurchaseAction"},Se=Object.assign(Pe,{setup(E){const f=D();return(v,$)=>{const k=u("PageHeaderAction"),B=u("PageHeader"),a=u("ParentTransition");return _(),w(T,null,[l(B,{title:v.$trans(r(f).meta.trans,{attribute:v.$trans(r(f).meta.label)}),navs:[{label:v.$trans("inventory.inventory"),path:"Inventory"},{label:v.$trans("inventory.stock_purchase.stock_purchase"),path:"InventoryStockPurchaseList"}]},{default:m(()=>[l(k,{name:"InventoryStockPurchase",title:v.$trans("inventory.stock_purchase.stock_purchase"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(a,{appear:"",visibility:!0},{default:m(()=>[l(Ue)]),_:1})],64)}}});export{Se as default};
