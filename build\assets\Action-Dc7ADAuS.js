import{u as f,h as P,H as S,l as g,r as l,q as D,o as b,w as p,d as m,e as i,f as a,b as O,s as V,t as y,J as N,a as j,F as q}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3"},E={name:"InventoryInchargeForm"},w=Object.assign(E,{setup(k){const d=f();P();const s={inventory:"",employee:"",startDate:"",endDate:"",remarks:""},v="inventory/incharge/",r=S(v),u=g({inventories:[]}),o=g({...s}),c=g({employee:"",isLoaded:!d.params.uuid}),$=n=>{Object.assign(u,n)},h=n=>{Object.assign(s,{...n,startDate:n.startDate.value,endDate:n.endDate.value,inventory:n.inventory.uuid,employee:n.employee.uuid}),Object.assign(o,N(s)),c.employee=n.employee.uuid,c.isLoaded=!0};return(n,t)=>{const I=l("BaseSelect"),U=l("BaseSelectSearch"),_=l("DatePicker"),B=l("BaseTextarea"),F=l("FormAction");return b(),D(F,{"pre-requisites":!0,onSetPreRequisites:$,"init-url":v,"init-form":s,form:o,setForm:h,redirect:"InventoryIncharge"},{default:p(()=>[m("div",A,[m("div",H,[i(I,{modelValue:o.inventory,"onUpdate:modelValue":t[0]||(t[0]=e=>o.inventory=e),name:"inventory",label:n.$trans("inventory.inventory"),options:u.inventories,"label-prop":"name","value-prop":"uuid",error:a(r).inventory,"onUpdate:error":t[1]||(t[1]=e=>a(r).inventory=e)},null,8,["modelValue","label","options","error"])]),m("div",R,[c.isLoaded?(b(),D(U,{key:0,name:"employee",label:n.$trans("global.select",{attribute:n.$trans("employee.employee")}),modelValue:o.employee,"onUpdate:modelValue":t[2]||(t[2]=e=>o.employee=e),error:a(r).employee,"onUpdate:error":t[3]||(t[3]=e=>a(r).employee=e),"value-prop":"uuid","init-search":c.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:p(e=>[V(y(e.value.name)+" ("+y(e.value.codeNumber)+") ",1)]),listOption:p(e=>[V(y(e.option.name)+" ("+y(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):O("",!0)]),m("div",T,[i(_,{modelValue:o.startDate,"onUpdate:modelValue":t[4]||(t[4]=e=>o.startDate=e),name:"startDate",label:n.$trans("employee.incharge.props.start_date"),"no-clear":"",error:a(r).startDate,"onUpdate:error":t[5]||(t[5]=e=>a(r).startDate=e)},null,8,["modelValue","label","error"])]),m("div",L,[i(_,{modelValue:o.endDate,"onUpdate:modelValue":t[6]||(t[6]=e=>o.endDate=e),name:"endDate",label:n.$trans("employee.incharge.props.end_date"),"no-clear":"",error:a(r).endDate,"onUpdate:error":t[7]||(t[7]=e=>a(r).endDate=e)},null,8,["modelValue","label","error"])]),m("div",C,[i(B,{modelValue:o.remarks,"onUpdate:modelValue":t[8]||(t[8]=e=>o.remarks=e),name:"remarks",label:n.$trans("employee.incharge.props.remarks"),error:a(r).remarks,"onUpdate:error":t[9]||(t[9]=e=>a(r).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),J={name:"InventoryInchargeAction"},G=Object.assign(J,{setup(k){const d=f();return(s,v)=>{const r=l("PageHeaderAction"),u=l("PageHeader"),o=l("ParentTransition");return b(),j(q,null,[i(u,{title:s.$trans(a(d).meta.trans,{attribute:s.$trans(a(d).meta.label)}),navs:[{label:s.$trans("inventory.inventory"),path:"Inventory"},{label:s.$trans("inventory.incharge.incharge"),path:"InventoryInchargeList"}]},{default:p(()=>[i(r,{name:"InventoryIncharge",title:s.$trans("inventory.incharge.incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(o,{appear:"",visibility:!0},{default:p(()=>[i(w)]),_:1})],64)}}});export{G as default};
