import{u as L,l as g,H as T,n as q,r as a,q as F,o as p,w as t,d as i,b as S,f as v,s as c,t as o,h as R,i as z,j as I,m as W,a as N,e as s,F as P,v as J,J as G}from"./app-BAwPsakn.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(U,{emit:_}){const y=L(),d=_,b=U,u={batch:""},h=g({...u}),r=T(b.initUrl),m=g({batch:"",isLoaded:!y.query.batch});return q(async()=>{m.batch=y.query.batch,m.isLoaded=!0}),(B,l)=>{const e=a("BaseSelectSearch"),$=a("FilterForm");return p(),F($,{"init-form":u,form:h,onHide:l[2]||(l[2]=n=>d("hide"))},{default:t(()=>[i("div",K,[i("div",Q,[m.isLoaded?(p(),F(e,{key:0,name:"batch",label:B.$trans("global.select",{attribute:B.$trans("academic.batch.batch")}),modelValue:h.batch,"onUpdate:modelValue":l[0]||(l[0]=n=>h.batch=n),error:v(r).batch,"onUpdate:error":l[1]||(l[1]=n=>v(r).batch=n),"value-prop":"uuid","init-search":m.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(n=>[c(o(n.value.course.nameWithTerm)+" "+o(n.value.name),1)]),listOption:t(n=>[c(o(n.option.course.nameWithTerm)+" "+o(n.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):S("",!0)])])]),_:1},8,["form"])}}},Y={class:"p-2"},Z={class:"divide-y divide-gray-200 dark:divide-gray-700"},ee={class:"grid grid-cols-4 gap-6 px-4 py-2"},te={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},se={class:"col-span-4 sm:col-span-1"},oe={name:"StudentRollNumber"},le=Object.assign(oe,{setup(U){const _=L();R();const y=z();I("emitter");const d={batch:"",students:[]},b="student/rollNumber/",u=W(!1);g({});const h=T(b),r=g({...d}),m=g({rollNumberPrefix:""}),B=()=>{let e=1;r.students.forEach($=>{$.number=e,e++})},l=async()=>{u.value=!0,await y.dispatch(b+"fetch",{params:_.query}).then(e=>{u.value=!1,d.batch=_.query.batch,d.students=e.data,m.rollNumberPrefix=e.meta.rollNumberPrefix,Object.assign(r,G(d))}).catch(e=>{u.value=!1})};return q(async()=>{_.query.batch&&await l()}),(e,$)=>{const n=a("PageHeaderAction"),w=a("PageHeader"),C=a("ParentTransition"),H=a("BaseButton"),D=a("BaseAlert"),A=a("BaseLabel"),E=a("TextMuted"),j=a("BaseDataView"),x=a("BaseInput"),O=a("FormAction"),M=a("BaseCard");return p(),N(P,null,[s(w,{title:e.$trans(v(_).meta.label),navs:[{label:e.$trans("student.student"),path:"Student"}]},{default:t(()=>[s(n)]),_:1},8,["title","navs"]),s(C,{appear:"",visibility:!0},{default:t(()=>[s(X,{onAfterFilter:l,"init-url":b})]),_:1}),s(M,{"no-padding":"","no-content-padding":"","is-loading":u.value},{title:t(()=>[c(o(e.$trans("global.update",{attribute:e.$trans("student.roll_number.roll_number")})),1)]),action:t(()=>[s(H,{design:"primary",size:"xs",onClick:B},{default:t(()=>[c(o(e.$trans("student.roll_number.auto_assign")),1)]),_:1})]),default:t(()=>[i("div",Y,[r.students.length==0?(p(),F(D,{key:0,size:"xs",design:"error"},{default:t(()=>[c(o(e.$trans("general.errors.record_not_found")),1)]),_:1})):S("",!0)]),r.students.length?(p(),F(O,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":b,action:"store","init-form":d,form:r},{default:t(()=>[i("div",Z,[i("div",ee,[i("div",te,[s(A,null,{default:t(()=>[c(o(e.$trans("student.student")),1)]),_:1})]),i("div",ae,[s(A,null,{default:t(()=>[c(o(e.$trans("student.roll_number.roll_number")),1)]),_:1})])]),(p(!0),N(P,null,J(r.students,(f,V)=>(p(),N("div",{class:"grid grid-cols-4 gap-6 px-4 py-2",key:f.uuid},[i("div",ne,[s(j,null,{default:t(()=>[c(o(f.name)+" ",1),s(E,{block:""},{default:t(()=>[c(o(f.codeNumber),1)]),_:2},1024)]),_:2},1024)]),i("div",se,[s(x,{type:"number",min:1,"leading-text":m.rollNumberPrefix,modelValue:f.number,"onUpdate:modelValue":k=>f.number=k,name:`student.${V}.number`,placeholder:e.$trans("student.roll_number.roll_number"),error:v(h)[`students.${V}.number`],"onUpdate:error":k=>v(h)[`students.${V}.number`]=k},null,8,["leading-text","modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])]))),128))])]),_:1},8,["form"])):S("",!0)]),_:1},8,["is-loading"])],64)}}});export{le as default};
