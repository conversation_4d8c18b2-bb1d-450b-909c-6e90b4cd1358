import{i as M,h as W,g as x,H as G,c as J,l as $,m as K,n as Q,r as g,a as N,o as m,d as r,e as d,f as a,s as b,t as u,w as p,b as c,q as y,F as T,I as X}from"./app-BAwPsakn.js";const Y={class:"flex justify-end"},Z={class:"flex justify-center"},ee={href:"/",class:"mb-6"},te=["src"],oe={class:"text-primary dark:text-darken-secondary text-center text-xl"},se={class:"text-dark-primary dark:text-darken-secondary text-center"},re={key:0,class:"mt-8 w-full rounded-lg"},ne={key:0},ae=["innerHTML"],ie={class:"mt-4 grid grid-cols-3 gap-4"},le={key:0,class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},ce={class:"flex"},ge={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},be={class:"mt-8"},Ne={class:"cursor-pointer"},ye={class:"mt-4 grid grid-cols-3 gap-4"},ke={class:"col-span-3 sm:col-span-1"},f="onlineRegistration/",Ve={__name:"Index",setup(_e){const k=M(),v=W(),R={team:"",period:"",program:"",course:"",firstName:"",middleName:"",lastName:"",contactNumber:"",email:""},U={token:"",code:""},w=x("layout.display").value=="dark"?x("assets.iconLight"):x("assets.icon"),i=G(f);J(()=>`${s.firstName} ${s.middleName} ${s.lastName}`.replace(/\s+/g," "));const F=K(null),s=$({...R}),V=$({token:"",code:""}),l=$({status:!1,response:"",selectedPeriod:null,selectedProgram:null,selectedCourse:null,hasVerificationToken:!1,submissionResponse:""}),n=$({instruction:"",genders:[],relations:[],teams:[],periods:[],programs:[],courses:[]}),S=async()=>{await k.dispatch(f+"preRequisite").then(t=>{n.instruction=t.instruction,n.teams=t.teams,n.genders=t.genders,n.relations=t.relations,t.teams.length==1&&(s.team=t.teams[0].uuid,C(s.team))}).catch(t=>{v.push({name:"Dashboard"})})},C=async t=>{if(s.program="",l.selectedProgram=null,n.programs=[],t==null||_.isEmpty(t)){s.team="";return}typeof t=="string"?s.team=t:s.team=t.uuid,await k.dispatch(f+"getPrograms",{team:s.team}).then(e=>{n.programs=e.programs}).catch(e=>{})},I=async t=>{if(s.period="",l.selectedPeriod=null,n.periods=[],t==null||_.isEmpty(t)){s.program={};return}s.program=t.uuid,await k.dispatch(f+"getPeriods",{team:s.team}).then(e=>{n.periods=e.periods}).catch(e=>{})},L=async t=>{if(s.course="",l.selectedCourse=null,n.courses=[],t==null||_.isEmpty(t)){s.period="";return}s.period=t.uuid,await k.dispatch(f+"getCourses",{period:s.period,program:l.selectedProgram.uuid}).then(e=>{n.courses=e.courses}).catch(e=>{})},j=async t=>{if(t==null||_.isEmpty(t)){s.course="",l.selectedCourse=null;return}s.course=t.uuid},O=()=>{n.teams.length==1&&(s.team=n.teams[0].uuid,C(s.team))},q=t=>{l.submissionResponse=t.message,U.token=t.token,Object.assign(V,U),l.hasVerificationToken=!0},A=t=>{localStorage.setItem("authToken",t.token),v.push({name:"OnlineRegistrationForm",params:{number:t.applicationNumber}})};return Q(async()=>{await S()}),(t,e)=>{const B=g("BaseAlert"),h=g("BaseSelect"),z=g("BaseLabel"),D=g("NameInput"),P=g("BaseInput"),E=g("FormAction"),H=g("BaseLoader");return m(),N(T,null,[r("div",Y,[r("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:e[0]||(e[0]=o=>a(v).push({name:"Dashboard"}))},[e[21]||(e[21]=r("i",{class:"fas fa-home mr-1"},null,-1)),b(" "+u(t.$trans("global.go_to",{attribute:t.$trans("dashboard.home")})),1)])]),r("div",Z,[r("a",ee,[r("img",{class:"h-16 w-auto",src:a(w),alt:""},null,8,te)])]),d(H,{"is-loading":F.value},{default:p(()=>[r("div",null,[r("p",oe,u(t.$trans("student.online_registration.title")),1),r("p",se,u(t.$trans("student.online_registration.subtitle")),1)]),n.teams.length>0?(m(),N("div",re,[l.hasVerificationToken?c("",!0):(m(),y(E,{key:0,"init-url":f,"init-form":R,form:s,action:"initiate","keep-adding":!1,"submit-text":t.$trans("general.proceed"),"after-reset":O,"after-submit":q},{default:p(()=>[n.instruction?(m(),N("div",ne,[d(B,{size:"xs",design:"info"},{default:p(()=>[b(u(t.$trans("student.registration.online_registration_instruction_alert")),1)]),_:1}),r("div",{class:"mt-4 text-sm",innerHTML:n.instruction},null,8,ae)])):c("",!0),r("div",ie,[n.teams.length>1||!s.team?(m(),N("div",le,[d(h,{modelValue:s.team,"onUpdate:modelValue":e[1]||(e[1]=o=>s.team=o),name:"team",label:t.$trans("team.team"),"label-prop":"name","value-prop":"uuid",options:n.teams,error:a(i).team,"onUpdate:error":e[2]||(e[2]=o=>a(i).team=o),onSelected:C},null,8,["modelValue","label","options","error"])])):c("",!0),r("div",me,[s.team?(m(),y(h,{key:0,modelValue:l.selectedProgram,"onUpdate:modelValue":e[3]||(e[3]=o=>l.selectedProgram=o),name:"program",label:t.$trans("academic.program.program"),"label-prop":"name","value-prop":"uuid",options:n.programs,"object-prop":!0,error:a(i).program,"onUpdate:error":e[4]||(e[4]=o=>a(i).program=o),onSelected:I},null,8,["modelValue","label","options","error"])):c("",!0)]),r("div",de,[s.program?(m(),y(h,{key:0,modelValue:l.selectedPeriod,"onUpdate:modelValue":e[5]||(e[5]=o=>l.selectedPeriod=o),name:"period",label:t.$trans("academic.period.period"),"label-prop":"name","value-prop":"uuid",options:n.periods,"object-prop":!0,error:a(i).period,"onUpdate:error":e[6]||(e[6]=o=>a(i).period=o),onSelected:L},null,8,["modelValue","label","options","error"])):c("",!0)]),r("div",ue,[s.period?(m(),y(h,{key:0,modelValue:l.selectedCourse,"onUpdate:modelValue":e[7]||(e[7]=o=>l.selectedCourse=o),name:"course",label:t.$trans("academic.course.course"),"value-prop":"uuid",options:n.courses,"object-prop":!0,error:a(i).course,"onUpdate:error":e[8]||(e[8]=o=>a(i).course=o),onChange:j},{selectedOption:p(o=>[b(u(o.value.nameWithTerm),1)]),listOption:p(o=>[b(u(o.option.nameWithTerm),1)]),_:1},8,["modelValue","label","options","error"])):c("",!0)]),s.period?(m(),N(T,{key:1},[r("div",pe,[d(z,null,{default:p(()=>[b(u(t.$trans("student.props.name")),1)]),_:1}),r("div",ce,[d(D,{firstName:s.firstName,"onUpdate:firstName":e[9]||(e[9]=o=>s.firstName=o),middleName:s.middleName,"onUpdate:middleName":e[10]||(e[10]=o=>s.middleName=o),thirdName:s.thirdName,"onUpdate:thirdName":e[11]||(e[11]=o=>s.thirdName=o),lastName:s.lastName,"onUpdate:lastName":e[12]||(e[12]=o=>s.lastName=o),formErrors:a(i),"onUpdate:formErrors":e[13]||(e[13]=o=>X(i)?i.value=o:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),r("div",ge,[d(P,{type:"text",modelValue:s.contactNumber,"onUpdate:modelValue":e[14]||(e[14]=o=>s.contactNumber=o),name:"contactNumber",label:t.$trans("contact.props.contact_number"),error:a(i).contactNumber,"onUpdate:error":e[15]||(e[15]=o=>a(i).contactNumber=o)},null,8,["modelValue","label","error"])]),r("div",fe,[d(P,{type:"text",modelValue:s.email,"onUpdate:modelValue":e[16]||(e[16]=o=>s.email=o),name:"email",label:t.$trans("contact.props.email"),error:a(i).email,"onUpdate:error":e[17]||(e[17]=o=>a(i).email=o)},null,8,["modelValue","label","error"])])],64)):c("",!0)]),r("div",be,[d(B,{size:"xs",design:"info",onClick:e[18]||(e[18]=o=>a(v).push({name:"OnlineRegistrationVerify"}))},{default:p(()=>[r("span",Ne,u(t.$trans("student.online_registration.already_have_application_number_info")),1)]),_:1})])]),_:1},8,["form","submit-text"])),l.hasVerificationToken?(m(),y(E,{key:1,"init-url":f,"init-form":U,form:V,action:"confirm","keep-adding":!1,"submit-text":t.$trans("student.online_registration.props.confirm"),"after-submit":A},{default:p(()=>[d(B,{size:"xs",design:"info"},{default:p(()=>[b(u(l.submissionResponse),1)]),_:1}),r("div",ye,[r("div",ke,[d(P,{type:"text",modelValue:V.code,"onUpdate:modelValue":e[19]||(e[19]=o=>V.code=o),name:"code",label:t.$trans("student.online_registration.props.code"),error:a(i).code,"onUpdate:error":e[20]||(e[20]=o=>a(i).code=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form","submit-text"])):c("",!0)])):c("",!0)]),_:1},8,["is-loading"])],64)}}};export{Ve as default};
