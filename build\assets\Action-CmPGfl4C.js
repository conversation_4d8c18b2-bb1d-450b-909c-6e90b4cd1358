import{u as B,G as T,H as C,l as U,r as u,q as $,o as p,w as g,d as l,a as c,b as _,e as i,f as o,s as q,t as m,F,v as D,J as R}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-4 gap-6"},L={class:"col-span-4 sm:col-span-1"},w={key:0,class:"col-span-2 sm:col-span-1"},M={key:1,class:"col-span-2 sm:col-span-1"},E={class:"mt-4 grid grid-cols-4 gap-6"},W={class:"col-span-4 sm:col-span-1"},I={class:"col-span-4 sm:col-span-1"},J={class:"mt-4 grid grid-cols-1 gap-4"},z={class:"col"},K={class:"col"},Q={class:"col"},X={class:"mt-4 flex space-x-4"},Y=["src"],Z=["src"],x={class:"col"},ee={name:"ReceptionGatePassForm"},se=Object.assign(ee,{setup(O){const v=B(),n={to:"",purpose:"",requesters:[],startAt:"",reason:"",remarks:"",images:[],media:[],mediaUpdated:!1,mediaToken:T(),mediaHash:[]},V="reception/gatePass/",a=C(V),y=U({to:[],purposes:[]}),t=U({...n}),b=U({requesters:[],isLoaded:!v.params.uuid}),P=r=>{v.params.uuid||(n.startAt=r.currentDateTime,Object.assign(t,R(n))),Object.assign(y,r)},h=()=>{t.mediaToken=T(),t.mediaHash=[]},H=r=>{var f;let s=r.audiences.filter(d=>d.type=="student").map(d=>d.uuid),k=r.audiences.filter(d=>d.type=="employee").map(d=>d.uuid);Object.assign(n,{...r,to:r.requesterType.value,startAt:r.startAt.at,purpose:((f=r.purpose)==null?void 0:f.uuid)||"",requesters:r.requesterType.value=="student"?s:k,images:r.images||[]}),Object.assign(t,R(n)),b.requesters=r.requesterType.value=="student"?s:k,b.isLoaded=!0};return(r,s)=>{const k=u("BaseSelect"),f=u("BaseSelectSearch"),d=u("DatePicker"),A=u("BaseTextarea"),S=u("WebcamCapture"),N=u("MediaUpload"),j=u("FormAction");return p(),$(j,{"pre-requisites":!0,onSetPreRequisites:P,"init-url":V,"init-form":n,form:t,"set-form":H,redirect:"ReceptionGatePass",onResetMediaFiles:h},{default:g(()=>[l("div",G,[l("div",L,[i(k,{modelValue:t.to,"onUpdate:modelValue":s[0]||(s[0]=e=>t.to=e),name:"to",label:r.$trans("reception.gate_pass.props.to"),options:y.to,error:o(a).to,"onUpdate:error":s[1]||(s[1]=e=>o(a).to=e)},null,8,["modelValue","label","options","error"])]),t.to=="student"?(p(),c("div",w,[b.isLoaded?(p(),$(f,{key:0,multiple:"",name:"requesters",label:r.$trans("student.student"),placeholder:r.$trans("global.select",{attribute:r.$trans("student.student")}),modelValue:t.requesters,"onUpdate:modelValue":s[2]||(s[2]=e=>t.requesters=e),error:o(a).requesters,"onUpdate:error":s[3]||(s[3]=e=>o(a).requesters=e),"value-prop":"uuid","init-search":b.requesters,"search-key":"name","search-action":"student/summary"},{selectedOption:g(e=>[q(m(e.value.name)+" ("+m(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:g(e=>[q(m(e.option.name)+" ("+m(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):_("",!0)])):_("",!0),t.to=="employee"?(p(),c("div",M,[b.isLoaded?(p(),$(f,{key:0,multiple:"",name:"requesters",label:r.$trans("employee.employee"),placeholder:r.$trans("global.select",{attribute:r.$trans("employee.employee")}),modelValue:t.requesters,"onUpdate:modelValue":s[4]||(s[4]=e=>t.requesters=e),error:o(a).requesters,"onUpdate:error":s[5]||(s[5]=e=>o(a).requesters=e),"value-prop":"uuid","init-search":b.requesters,"search-key":"name","search-action":"employee/summary","additional-search-query":{type:"all"}},{selectedOption:g(e=>[q(m(e.value.name)+" ("+m(e.value.designation)+") ",1)]),listOption:g(e=>[q(m(e.option.name)+" ("+m(e.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):_("",!0)])):_("",!0)]),l("div",E,[l("div",W,[i(k,{modelValue:t.purpose,"onUpdate:modelValue":s[6]||(s[6]=e=>t.purpose=e),name:"purpose",label:r.$trans("reception.gate_pass.props.purpose"),options:y.purposes,"label-prop":"name","value-prop":"uuid",error:o(a).purpose,"onUpdate:error":s[7]||(s[7]=e=>o(a).purpose=e)},null,8,["modelValue","label","options","error"])]),l("div",I,[i(d,{as:"datetime",modelValue:t.startAt,"onUpdate:modelValue":s[8]||(s[8]=e=>t.startAt=e),name:"startAt",label:r.$trans("reception.gate_pass.props.datetime"),"no-clear":"",error:o(a).startAt,"onUpdate:error":s[9]||(s[9]=e=>o(a).startAt=e)},null,8,["modelValue","label","error"])])]),l("div",J,[l("div",z,[i(A,{rows:1,modelValue:t.reason,"onUpdate:modelValue":s[10]||(s[10]=e=>t.reason=e),name:"reason",label:r.$trans("reception.gate_pass.props.reason"),error:o(a).reason,"onUpdate:error":s[11]||(s[11]=e=>o(a).reason=e)},null,8,["modelValue","label","error"])]),l("div",K,[i(A,{rows:1,modelValue:t.remarks,"onUpdate:modelValue":s[12]||(s[12]=e=>t.remarks=e),name:"remarks",label:r.$trans("reception.gate_pass.props.remarks"),error:o(a).remarks,"onUpdate:error":s[13]||(s[13]=e=>o(a).remarks=e)},null,8,["modelValue","label","error"])]),l("div",Q,[i(S,{onCompleted:s[14]||(s[14]=e=>t.images=e)}),l("div",X,[(p(!0),c(F,null,D(t.images,e=>(p(),c("div",{key:e.id},[e.image?(p(),c("img",{key:0,src:e.image},null,8,Y)):_("",!0),e.url?(p(),c("img",{key:1,src:e.url},null,8,Z)):_("",!0)]))),128))])]),l("div",x,[i(N,{multiple:"",label:r.$trans("general.file"),module:"gate_pass",media:t.media,"media-token":t.mediaToken,onIsUpdated:s[15]||(s[15]=e=>t.mediaUpdated=!0),onSetHash:s[16]||(s[16]=e=>t.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),te={name:"ReceptionGatePassAction"},ae=Object.assign(te,{setup(O){const v=B();return(n,V)=>{const a=u("PageHeaderAction"),y=u("PageHeader"),t=u("ParentTransition");return p(),c(F,null,[i(y,{title:n.$trans(o(v).meta.trans,{attribute:n.$trans(o(v).meta.label)}),navs:[{label:n.$trans("reception.reception"),path:"Reception"},{label:n.$trans("reception.gate_pass.gate_pass"),path:"ReceptionGatePassList"}]},{default:g(()=>[i(a,{name:"ReceptionGatePass",title:n.$trans("reception.gate_pass.gate_pass"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(t,{appear:"",visibility:!0},{default:g(()=>[i(se)]),_:1})],64)}}});export{ae as default};
