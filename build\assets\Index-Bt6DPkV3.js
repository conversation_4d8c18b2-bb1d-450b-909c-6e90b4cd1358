import{u as R,i as W,m as j,H,l as B,n as L,r as l,q as w,o as p,w as t,d as _,b as $,f as u,s as b,a as q,t as s,h as P,j as Q,e as m,F as O,v as X,J as E,M as Y}from"./app-BAwPsakn.js";const Z={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},ae={key:0,class:"ml-1"},te={key:0,class:"ml-1"},ne={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide","cancel"],setup(D,{emit:k}){const i=R();W();const V=k,r=D,v={exam:"",batch:""};j(!1);const o=H(r.initUrl),c=B({...v});B({exams:r.preRequisites.exams});const h=B({exam:"",batch:"",isLoaded:!(i.query.exam&&i.query.batch)}),f=()=>{V("cancel")},C=async g=>{if(!g){c.batch="";return}c.batch=g||""};return L(async()=>{h.exam=i.query.exam,c.exam=i.query.exam,h.batch=i.query.batch,c.batch=i.query.batch,i.query.batch&&await C(i.query.batch),h.isLoaded=!0}),(g,d)=>{const F=l("BaseSelect"),S=l("BaseSelectSearch"),a=l("FilterForm");return p(),w(a,{"init-form":v,form:c,onCancel:f,onHide:d[4]||(d[4]=e=>V("hide"))},{default:t(()=>[_("div",Z,[_("div",ee,[h.isLoaded?(p(),w(F,{key:0,modelValue:c.exam,"onUpdate:modelValue":d[0]||(d[0]=e=>c.exam=e),name:"exam",label:g.$trans("exam.exam"),"value-prop":"uuid",options:D.preRequisites.exams,error:u(o).exam,"onUpdate:error":d[1]||(d[1]=e=>u(o).exam=e)},{selectedOption:t(e=>{var y,x;return[b(s(e.value.name)+" ",1),e.value.term?(p(),q("span",ae,"("+s(((x=(y=e.value.term)==null?void 0:y.division)==null?void 0:x.name)||g.$trans("general.all"))+")",1)):$("",!0)]}),listOption:t(e=>{var y,x;return[b(s(e.option.name)+" ",1),e.option.term?(p(),q("span",te,"("+s(((x=(y=e.option.term)==null?void 0:y.division)==null?void 0:x.name)||g.$trans("general.all"))+")",1)):$("",!0)]}),_:1},8,["modelValue","label","options","error"])):$("",!0)]),_("div",ne,[h.isLoaded?(p(),w(S,{key:0,name:"batch",label:g.$trans("global.select",{attribute:g.$trans("academic.batch.batch")}),modelValue:c.batch,"onUpdate:modelValue":d[2]||(d[2]=e=>c.batch=e),error:u(o).batch,"onUpdate:error":d[3]||(d[3]=e=>u(o).batch=e),"value-prop":"uuid","init-search":h.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:C},{selectedOption:t(e=>[b(s(e.value.course.name)+" "+s(e.value.name),1)]),listOption:t(e=>[b(s(e.option.course.nameWithTerm)+" "+s(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)])])]),_:1},8,["form"])}}},oe={class:"p-2"},re={class:"px-4 grid grid-cols-1 gap-6"},le={class:"col"},ie={class:"divide-y divide-gray-200 dark:divide-gray-700"},ce={class:"col-span-4 sm:col-span-1"},de={class:"mt-1"},me={key:0,class:"col-span-4 sm:col-span-3"},ue={name:"ExamAttendance"},he=Object.assign(ue,{setup(D){const k=R(),i=P(),V=W();Q("emitter");const r={exam:"",batch:"",totalWorkingDays:"",students:[]},v="exam/attendance/",o=j(!1),c=B({exams:[]}),h=H(v),f=B({...r}),C=B({meta:{},showComment:!1}),g=async()=>{o.value=!0,await V.dispatch(v+"preRequisite").then(a=>{o.value=!1,Object.assign(c,a)}).catch(a=>{o.value=!1})},d=()=>{r.exam="",r.batch="",r.students=[],Object.assign(f,E(r))},F=async()=>{k.query.batch&&(o.value=!0,await V.dispatch(v+"fetch",{params:k.query}).then(a=>{o.value=!1,r.exam=k.query.exam,r.batch=k.query.batch,r.students=a.data,r.totalWorkingDays=a.meta.totalWorkingDays,C.meta=a.meta,Object.assign(f,E(r))}).catch(a=>{o.value=!1}))},S=async()=>{await Y()&&(o.value=!0,await V.dispatch(v+"remove",{form:k.query}).then(a=>{o.value=!1,F()}).catch(a=>{o.value=!1}))};return L(async()=>{await g(),await F()}),(a,e)=>{const y=l("BaseButton"),x=l("PageHeaderAction"),M=l("PageHeader"),T=l("ParentTransition"),I=l("BaseAlert"),N=l("BaseInput"),z=l("BaseCheckbox"),J=l("BaseDataView"),G=l("FormAction"),K=l("BaseCard");return p(),q(O,null,[m(M,{title:a.$trans(u(k).meta.label),navs:[{label:a.$trans("exam.exam"),path:"Exam"}]},{default:t(()=>[m(x,null,{default:t(()=>[m(y,{design:"white",onClick:e[0]||(e[0]=n=>u(i).push({name:"ExamMark"}))},{default:t(()=>[b(s(a.$trans("exam.mark")),1)]),_:1}),m(y,{design:"white",onClick:e[1]||(e[1]=n=>u(i).push({name:"ExamObservationMark"}))},{default:t(()=>[b(s(a.$trans("exam.observation_mark")),1)]),_:1}),m(y,{design:"white",onClick:e[2]||(e[2]=n=>u(i).push({name:"ExamComment"}))},{default:t(()=>[b(s(a.$trans("exam.comment")),1)]),_:1})]),_:1})]),_:1},8,["title","navs"]),m(T,{appear:"",visibility:!0},{default:t(()=>[m(se,{onAfterFilter:F,onCancel:d,"init-url":v,"pre-requisites":c},null,8,["pre-requisites"])]),_:1}),m(K,{"no-padding":"","no-content-padding":"","is-loading":o.value},{title:t(()=>[b(s(a.$trans("exam.record")),1)]),action:t(()=>[C.meta.attendanceRecorded?(p(),w(y,{key:0,design:"error",onClick:S},{default:t(()=>[b(s(a.$trans("global.remove",{attribute:a.$trans("student.attendance.attendance")})),1)]),_:1})):$("",!0)]),default:t(()=>[_("div",oe,[f.students.length==0?(p(),w(I,{key:0,size:"xs",design:"error"},{default:t(()=>[b(s(a.$trans("general.errors.record_not_found")),1)]),_:1})):$("",!0)]),f.students.length?(p(),w(G,{key:0,"no-card":"","button-padding":"","keep-adding":!1,"stay-on":!0,"init-url":v,action:"store","init-form":r,form:f},{default:t(()=>[_("div",re,[_("div",le,[m(N,{type:"text",modelValue:f.totalWorkingDays,"onUpdate:modelValue":e[3]||(e[3]=n=>f.totalWorkingDays=n),name:"totalWorkingDays",placeholder:a.$trans("student.attendance.total_working_days"),error:u(h).totalWorkingDays,"onUpdate:error":e[4]||(e[4]=n=>u(h).totalWorkingDays=n)},null,8,["modelValue","placeholder","error"])])]),_("div",ie,[(p(!0),q(O,null,X(f.students,(n,A)=>(p(),q("div",{class:"grid grid-cols-4 gap-6 px-4 py-2",key:n.uuid},[_("div",ce,[m(J,null,{default:t(()=>[b(s(n.name)+" ("+s(n.rollNumber||n.codeNumber)+") ",1),_("div",de,[m(z,{modelValue:n.isNotApplicable,"onUpdate:modelValue":U=>n.isNotApplicable=U,name:`students.${A}.isNotApplicable`,label:a.$trans("global.is_not",{attribute:a.$trans("exam.schedule.props.applicable")})},null,8,["modelValue","onUpdate:modelValue","name","label"])])]),_:2},1024)]),n.isNotApplicable?$("",!0):(p(),q("div",me,[m(N,{type:"text",modelValue:n.attendance,"onUpdate:modelValue":U=>n.attendance=U,name:`students.${A}.attendance`,placeholder:a.$trans("student.attendance.types.present"),error:u(h)[`students.${A}.attendance`],"onUpdate:error":U=>u(h)[`students.${A}.attendance`]=U},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))]))),128))])]),_:1},8,["form"])):$("",!0)]),_:1},8,["is-loading"])],64)}}});export{he as default};
