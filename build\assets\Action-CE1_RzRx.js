import{u as N,G as g,H as L,g as i,l as P,r as b,q as _,o as p,w as v,d,a as k,b as U,e as l,f as n,F as T}from"./app-BAwPsakn.js";const M={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={key:0,class:"col-span-3 sm:col-span-1"},q={key:1,class:"col-span-3 sm:col-span-1"},x={key:2,class:"col-span-3 sm:col-span-1"},G={class:"grid grid-cols-1"},z={class:"col"},D={name:"StudentAccountForm"},J=Object.assign(D,{setup(c){const u=N(),s={name:"",alias:"",number:"",bankName:"",branchName:"",bankCode1:"",bankCode2:"",bankCode3:"",media:[],mediaUpdated:!1,mediaToken:g(),mediaHash:[]},f="student/account/",t=L(f),C=i("finance.enableBankCode1"),V=i("finance.enableBankCode2"),$=i("finance.enableBankCode3"),y=i("finance.bankCode1Label"),B=i("finance.bankCode2Label"),A=i("finance.bankCode3Label"),o=P({...s}),F=()=>{o.mediaToken=g(),o.mediaHash=[]};return(m,e)=>{const r=b("BaseInput"),H=b("MediaUpload"),S=b("FormAction");return p(),_(S,{"no-data-fetch":"","init-url":f,uuid:n(u).params.uuid,"module-uuid":n(u).params.muuid,"init-form":s,form:o,redirect:{name:"StudentAccount",params:{uuid:n(u).params.uuid}},onResetMediaFiles:F},{default:v(()=>[d("div",M,[d("div",j,[l(r,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=a=>o.name=a),name:"name",label:m.$trans("finance.account.props.name"),error:n(t).name,"onUpdate:error":e[1]||(e[1]=a=>n(t).name=a),autofocus:""},null,8,["modelValue","label","error"])]),d("div",w,[l(r,{type:"text",modelValue:o.alias,"onUpdate:modelValue":e[2]||(e[2]=a=>o.alias=a),name:"alias",label:m.$trans("finance.account.props.alias"),error:n(t).alias,"onUpdate:error":e[3]||(e[3]=a=>n(t).alias=a)},null,8,["modelValue","label","error"])]),d("div",E,[l(r,{type:"text",modelValue:o.number,"onUpdate:modelValue":e[4]||(e[4]=a=>o.number=a),name:"number",label:m.$trans("finance.account.props.number"),error:n(t).number,"onUpdate:error":e[5]||(e[5]=a=>n(t).number=a)},null,8,["modelValue","label","error"])]),d("div",I,[l(r,{type:"text",modelValue:o.bankName,"onUpdate:modelValue":e[6]||(e[6]=a=>o.bankName=a),name:"bankName",label:m.$trans("finance.account.props.bank_name"),error:n(t).bankName,"onUpdate:error":e[7]||(e[7]=a=>n(t).bankName=a)},null,8,["modelValue","label","error"])]),d("div",O,[l(r,{type:"text",modelValue:o.branchName,"onUpdate:modelValue":e[8]||(e[8]=a=>o.branchName=a),name:"branchName",label:m.$trans("finance.account.props.branch_name"),error:n(t).branchName,"onUpdate:error":e[9]||(e[9]=a=>n(t).branchName=a)},null,8,["modelValue","label","error"])]),n(C)?(p(),k("div",R,[l(r,{type:"text",modelValue:o.bankCode1,"onUpdate:modelValue":e[10]||(e[10]=a=>o.bankCode1=a),name:"bankCode1",label:n(y),error:n(t).bankCode1,"onUpdate:error":e[11]||(e[11]=a=>n(t).bankCode1=a)},null,8,["modelValue","label","error"])])):U("",!0),n(V)?(p(),k("div",q,[l(r,{type:"text",modelValue:o.bankCode2,"onUpdate:modelValue":e[12]||(e[12]=a=>o.bankCode2=a),name:"bankCode2",label:n(B),error:n(t).bankCode2,"onUpdate:error":e[13]||(e[13]=a=>n(t).bankCode2=a)},null,8,["modelValue","label","error"])])):U("",!0),n($)?(p(),k("div",x,[l(r,{type:"text",modelValue:o.bankCode3,"onUpdate:modelValue":e[14]||(e[14]=a=>o.bankCode3=a),name:"bankCode3",label:n(A),error:n(t).bankCode3,"onUpdate:error":e[15]||(e[15]=a=>n(t).bankCode3=a)},null,8,["modelValue","label","error"])])):U("",!0)]),d("div",G,[d("div",z,[l(H,{multiple:"",label:m.$trans("general.file"),module:"account",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[16]||(e[16]=a=>o.mediaUpdated=!0),onSetHash:e[17]||(e[17]=a=>o.mediaHash.push(a))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),K={name:"StudentAccountAction"},W=Object.assign(K,{props:{student:{type:Object,default(){return{}}}},setup(c){const u=N();return(s,f)=>{const t=b("PageHeaderAction"),C=b("PageHeader"),V=b("ParentTransition");return p(),k(T,null,[l(C,{title:s.$trans(n(u).meta.trans,{attribute:s.$trans(n(u).meta.label)}),navs:[{label:s.$trans("student.student"),path:"StudentList"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}},{label:s.$trans("finance.account.account"),path:{name:"StudentAccount",params:{uuid:c.student.uuid}}}]},{default:v(()=>[l(t,{name:"StudentAccount",title:s.$trans("finance.account.account"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(V,{appear:"",visibility:!0},{default:v(()=>[l(J)]),_:1})],64)}}});export{W as default};
