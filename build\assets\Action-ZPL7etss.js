import{u as A,h as z,j as O,G as P,H as G,l as w,n as I,J as L,r as d,a as h,o as f,q as j,b as U,e as s,w as u,s as F,t as $,d as g,f as t,x as J,F as M}from"./app-BAwPsakn.js";const K={key:0,class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={key:0,class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"mt-4 grid grid-cols-1"},ae={class:"col"},oe={class:"grid grid-cols-1"},ne={class:"col"},le={name:"EmployeeRecordForm"},se=Object.assign(le,{props:{employee:{type:Object,default(){return{}}}},setup(i){const c=A(),v=z(),l=O("$trans");O("emitter");const y=i,H=[{key:"period",label:l("employee.record.props.period"),visibility:!0},{key:"department",label:l("employee.department.department"),visibility:!0},{key:"designation",label:l("employee.designation.designation"),visibility:!0},{key:"employmentStatus",label:l("employee.employment_status.employment_status"),visibility:!0}],k={end:!1,endDate:"",startDate:"",department:"",designation:"",employmentStatus:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:P(),mediaHash:[]},C="employee/record/",r=G(C),o=w({...k}),m=w({department:"",designation:"",employmentStatus:"",isLoaded:!1}),N=()=>{o.mediaToken=P(),o.mediaHash=[]},x=n=>{var e,p,_,D,V,R,S,b;Object.assign(k,{end:!!n.isEnded,startDate:(e=n.startDate)==null?void 0:e.value,endDate:n.isEnded?(p=n.endDate)==null?void 0:p.value:"",department:(_=n.department)==null?void 0:_.uuid,designation:(D=n.designation)==null?void 0:D.uuid,employmentStatus:(V=n.employmentStatus)==null?void 0:V.uuid,remarks:n.remarks,media:n.media,mediaToken:n.mediaToken}),Object.assign(o,L(k)),m.department=(R=n.department)==null?void 0:R.name,m.designation=(S=n.designation)==null?void 0:S.name,m.employmentStatus=(b=n.employmentStatus)==null?void 0:b.name,m.isLoaded=!0},q=()=>{v.push({name:"EmployeeRecord",params:{uuid:y.employee.uuid}})};return I(()=>{var n,e,p,_,D,V,R,S,b,B,E,T;c.params.muuid||(Object.assign(k,{department:(e=(n=y.employee.lastRecord)==null?void 0:n.department)==null?void 0:e.uuid,designation:(_=(p=y.employee.lastRecord)==null?void 0:p.designation)==null?void 0:_.uuid,employmentStatus:(V=(D=y.employee.lastRecord)==null?void 0:D.employmentStatus)==null?void 0:V.uuid}),Object.assign(o,L(k)),m.department=(S=(R=y.employee.lastRecord)==null?void 0:R.department)==null?void 0:S.name,m.designation=(B=(b=y.employee.lastRecord)==null?void 0:b.designation)==null?void 0:B.name,m.employmentStatus=(T=(E=y.employee.lastRecord)==null?void 0:E.employmentStatus)==null?void 0:T.name,m.isLoaded=!0)}),(n,e)=>{const p=d("DataCell"),_=d("DataRow"),D=d("SimpleTable"),V=d("BaseCard"),R=d("BaseSwitch"),S=d("DatePicker"),b=d("BaseSelectSearch"),B=d("BaseTextarea"),E=d("MediaUpload"),T=d("FormAction");return f(),h(M,null,[i.employee.lastRecord?(f(),j(V,{key:0,"no-padding":"","no-content-padding":"","bottom-content-padding":"",class:"mb-4"},{default:u(()=>[s(D,{header:H},{default:u(()=>[s(_,null,{default:u(()=>[s(p,{name:"period"},{default:u(()=>[F($(i.employee.lastRecord.period),1)]),_:1}),s(p,{name:"department"},{default:u(()=>[F($(i.employee.lastRecord.department.name),1)]),_:1}),s(p,{name:"designation"},{default:u(()=>[F($(i.employee.lastRecord.designation.name),1)]),_:1}),s(p,{name:"employmentStatus"},{default:u(()=>[F($(i.employee.lastRecord.employmentStatus.name),1)]),_:1})]),_:1})]),_:1})]),_:1})):U("",!0),s(T,{"no-data-fetch":"","init-url":C,uuid:t(c).params.uuid,"module-uuid":t(c).params.muuid,"init-form":k,form:o,"set-form":x,"after-submit":q,onResetMediaFiles:N},{default:u(()=>[t(c).params.muuid?(f(),h("div",K,[g("div",Q,[s(R,{modelValue:o.end,"onUpdate:modelValue":e[0]||(e[0]=a=>o.end=a),name:"end",label:t(l)("employee.record.props.end"),error:t(r).end,"onUpdate:error":e[1]||(e[1]=a=>t(r).end=a),vertical:""},null,8,["modelValue","label","error"])]),o.end?(f(),h("div",W,[s(S,{modelValue:o.endDate,"onUpdate:modelValue":e[2]||(e[2]=a=>o.endDate=a),name:"endDate",label:t(l)("employee.record.props.end_date"),error:t(r).endDate,"onUpdate:error":e[3]||(e[3]=a=>t(r).endDate=a)},null,8,["modelValue","label","error"])])):U("",!0)])):U("",!0),!o.end||t(c).params.muuid?(f(),h("div",{key:1,class:J(["grid grid-cols-3 gap-6",{"mt-4":t(c).params.muuid}])},[g("div",X,[s(S,{modelValue:o.startDate,"onUpdate:modelValue":e[4]||(e[4]=a=>o.startDate=a),name:"startDate",label:t(l)("employee.record.props.start_date"),error:t(r).startDate,"onUpdate:error":e[5]||(e[5]=a=>t(r).startDate=a)},null,8,["modelValue","label","error"])]),g("div",Y,[m.isLoaded?(f(),j(b,{key:0,name:"department",label:t(l)("global.select",{attribute:t(l)("employee.department.department")}),modelValue:o.department,"onUpdate:modelValue":e[6]||(e[6]=a=>o.department=a),error:t(r).department,"onUpdate:error":e[7]||(e[7]=a=>t(r).department=a),"label-prop":"name","value-prop":"uuid","init-search":m.department,"init-search-key":"name","search-action":"employee/department/list"},null,8,["label","modelValue","error","init-search"])):U("",!0)]),g("div",Z,[m.isLoaded?(f(),j(b,{key:0,name:"designation",label:t(l)("global.select",{attribute:t(l)("employee.designation.designation")}),modelValue:o.designation,"onUpdate:modelValue":e[8]||(e[8]=a=>o.designation=a),error:t(r).designation,"onUpdate:error":e[9]||(e[9]=a=>t(r).designation=a),"label-prop":"name","value-prop":"uuid","init-search":m.designation,"init-search-key":"name","search-action":"employee/designation/list"},null,8,["label","modelValue","error","init-search"])):U("",!0)]),g("div",ee,[m.isLoaded?(f(),j(b,{key:0,name:"employmentStatus",label:t(l)("global.select",{attribute:t(l)("employee.employment_status.employment_status")}),modelValue:o.employmentStatus,"onUpdate:modelValue":e[10]||(e[10]=a=>o.employmentStatus=a),error:t(r).employmentStatus,"onUpdate:error":e[11]||(e[11]=a=>t(r).employmentStatus=a),"label-prop":"name","value-prop":"uuid","init-search":m.employmentStatus,"init-search-key":"name","search-action":"option/list","additional-search-query":{type:"employment_status"}},null,8,["label","modelValue","error","init-search"])):U("",!0)])],2)):U("",!0),g("div",te,[g("div",ae,[s(B,{modelValue:o.remarks,"onUpdate:modelValue":e[12]||(e[12]=a=>o.remarks=a),name:"remarks",label:t(l)("employee.record.props.remarks"),error:t(r).remarks,"onUpdate:error":e[13]||(e[13]=a=>t(r).remarks=a)},null,8,["modelValue","label","error"])])]),g("div",oe,[g("div",ne,[s(E,{multiple:"",label:t(l)("general.file"),module:"employee_record",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[14]||(e[14]=a=>o.mediaUpdated=!0),onSetHash:e[15]||(e[15]=a=>o.mediaHash.push(a))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form"])],64)}}}),re={name:"EmployeeRecordAction"},de=Object.assign(re,{props:{employee:{type:Object,default(){return{}}}},setup(i){const c=A();return(v,l)=>{const y=d("PageHeaderAction"),H=d("PageHeader"),k=d("ParentTransition");return f(),h(M,null,[s(H,{title:v.$trans(t(c).meta.trans,{attribute:v.$trans(t(c).meta.label)}),navs:[{label:v.$trans("employee.employee"),path:"EmployeeList"},{label:i.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:i.employee.uuid}}},{label:v.$trans("employee.record.record"),path:{name:"EmployeeRecord",params:{uuid:i.employee.uuid}}}]},{default:u(()=>[s(y,{name:"EmployeeRecord",title:v.$trans("employee.record.record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(k,{appear:"",visibility:!0},{default:u(()=>[s(se,{employee:i.employee},null,8,["employee"])]),_:1})],64)}}});export{de as default};
