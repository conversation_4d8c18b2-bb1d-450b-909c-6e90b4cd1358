import{i as c,h as m,u as p,m as _,n as d,r as t,a as f,o as h,e as n,w as g,F as v}from"./app-BAwPsakn.js";const b={__name:"EmailVerification",setup(k){const s=c(),o=m(),r=p(),e=_(null);return d(()=>{e.value=!0,s.dispatch("auth/register/emailVerification",{token:r.params.token}).then(a=>{e.value=!1}).catch(a=>{e.value=!1}),o.push({name:"Login"})}),(a,B)=>{const i=t("GuestHeader"),l=t("BaseCard"),u=t("ParentTransition");return h(),f(v,null,[n(i,{label:a.$trans("auth.register.email_verification")},null,8,["label"]),n(u,{appear:"",visibility:!0},{default:g(()=>[n(l,{"is-loading":e.value},null,8,["is-loading"])]),_:1})],64)}}};export{b as default};
