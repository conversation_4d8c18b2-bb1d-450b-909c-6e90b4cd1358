import{u as T,i as $,j as C,y as P,c as F,g as I,m as O,r as l,a as g,o,q as d,b as y,e as n,w as e,f as t,s,t as u,d as w,F as b,v as E}from"./app-BAwPsakn.js";const q={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},z={class:"space-x-1"},R={class:"flex flex-wrap gap-2"},U={name:"EmployeeShowLogin"},K=Object.assign(U,{props:{employee:{type:Object,default(){return{}}}},setup(c){const V=T(),A=$(),a=C("$trans"),j=C("emitter"),_=c;let v=[];P("employee:edit")&&v.push({label:a("global.edit",{attribute:a("contact.login.login")}),path:{name:"EmployeeEditLogin",params:{uuid:_.employee.uuid}}});const i=F(()=>_.employee.contact.user),H=I("periods"),x=O(!1),L=f=>{var p;f.id!=((p=i.value)==null?void 0:p.currentPeriodId)&&(x.value=!0,A.dispatch("employee/updateCurrentPeriod",{uuid:_.employee.uuid,form:{period_id:f.id}}).then(()=>{j.emit("employeeUpdated")}).catch(()=>{}).finally(()=>{x.value=!1}))};return(f,p)=>{const N=l("PageHeaderAction"),S=l("PageHeader"),D=l("BaseAlert"),m=l("BaseDataView"),B=l("BaseBadge"),h=l("BaseCard"),k=l("ParentTransition");return o(),g(b,null,[c.employee.uuid?(o(),d(S,{key:0,title:t(a)(t(V).meta.label),navs:[{label:t(a)("employee.employee"),path:"Employee"},{label:c.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:c.employee.uuid}}}]},{default:e(()=>[n(N,{"additional-actions":t(v)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):y("",!0),n(k,{appear:"",visibility:!0},{default:e(()=>[c.employee.uuid?(o(),d(h,{key:0},{default:e(()=>[i.value?(o(),g("dl",q,[n(m,{label:t(a)("contact.login.props.email")},{default:e(()=>[s(u(i.value.email),1)]),_:1},8,["label"]),n(m,{label:t(a)("contact.login.props.username")},{default:e(()=>[s(u(i.value.username),1)]),_:1},8,["label"]),n(m,{label:t(a)("contact.login.props.password")},{default:e(()=>p[0]||(p[0]=[s(" xxxxxxxxx ")])),_:1},8,["label"]),n(m,{label:t(a)("team.config.role.role")},{default:e(()=>[w("div",z,[(o(!0),g(b,null,E(i.value.roles,r=>(o(),d(B,{design:"primary"},{default:e(()=>[s(u(r.label),1)]),_:2},1024))),256))])]),_:1},8,["label"])])):(o(),d(D,{key:0,design:"error"},{default:e(()=>[s(u(t(a)("contact.login.no_login_found")),1)]),_:1}))]),_:1})):y("",!0)]),_:1}),t(P)("employee:edit")?(o(),d(k,{key:1,appear:"",visibility:!0},{default:e(()=>[n(h,null,{title:e(()=>[s(u(t(a)("global.update",{attribute:t(a)("academic.period.current_period")})),1)]),default:e(()=>[w("div",R,[(o(!0),g(b,null,E(t(H),r=>(o(),d(B,{key:r.id,size:"md",design:r.id==i.value.currentPeriodId?"success":"info",class:"cursor-pointer",onClick:G=>L(r)},{default:e(()=>[s(u(r.name),1)]),_:2},1032,["design","onClick"]))),128))])]),_:1})]),_:1})):y("",!0)],64)}}});export{K as default};
