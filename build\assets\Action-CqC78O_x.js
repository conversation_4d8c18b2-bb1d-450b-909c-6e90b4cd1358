import{j as E,u as S,h as I,i as G,G as U,H as J,m as W,l as B,n as z,r as i,q as R,o as h,w as m,d as r,a as H,e as l,f as n,b as K,s as k,t as p,F as T,v as Q,M as X,J as Z}from"./app-BAwPsakn.js";const x={class:"grid grid-cols-4 gap-6"},ee={class:"col-span-4 sm:col-span-1"},se={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},te=["onClick"],oe={class:"mt-4 grid grid-cols-4 gap-4"},re={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-3"},ie={class:"mt-4"},le={class:"mt-4 grid grid-cols-1"},de={class:"col"},ce={name:"ResourceDiaryForm"},ue=Object.assign(ce,{setup(M){const f=E("moment"),d=S();I(),G();const b={batches:[],subject:"",date:f().format("YYYY-MM-DD"),details:[],media:[],mediaUpdated:!1,mediaToken:U(),mediaHash:[]},v={uuid:U(),heading:"",description:""},y="resource/diary/",o=J(y);W(!1);const D=B({subjects:[],statuses:[]});B({subjects:[]});const a=B({...b}),_=B({batches:[],subject:"",isLoaded:!d.params.uuid}),P=t=>{Object.assign(D,t)},O=()=>{a.mediaToken=U(),a.mediaHash=[]},F=()=>{a.details.push({...v,uuid:U()}),_.isLoaded=!0},w=async t=>{await X()&&(a.details.length==1?a.details=[v]:a.details.splice(t,1))},A=t=>{var j,V;let s=t.details.map(g=>({...g})),$=t.records.map(g=>g.batch.uuid)||[];Object.assign(b,{...t,details:s,date:t.date.value,batches:$,subject:((V=(j=t.records[0])==null?void 0:j.subject)==null?void 0:V.uuid)||""}),Object.assign(a,Z(b)),_.batches=$,_.isLoaded=!0};return z(async()=>{d.params.uuid||F()}),(t,s)=>{const $=i("DatePicker"),j=i("BaseSelectSearch"),V=i("BaseSelect"),g=i("BaseInput"),C=i("BaseTextarea"),L=i("BaseFieldset"),q=i("BaseBadge"),N=i("MediaUpload"),Y=i("FormAction");return h(),R(Y,{"pre-requisites":!0,onSetPreRequisites:P,"init-url":y,"init-form":b,form:a,setForm:A,redirect:"ResourceDiary",onResetMediaFiles:O},{default:m(()=>[r("div",x,[r("div",ee,[l($,{modelValue:a.date,"onUpdate:modelValue":s[0]||(s[0]=e=>a.date=e),name:"date",label:t.$trans("resource.diary.props.date"),"no-clear":"",error:n(o).date,"onUpdate:error":s[1]||(s[1]=e=>n(o).date=e)},null,8,["modelValue","label","error"])]),r("div",se,[_.isLoaded?(h(),R(j,{key:0,multiple:"",name:"batches",label:t.$trans("academic.batch.batch"),modelValue:a.batches,"onUpdate:modelValue":s[2]||(s[2]=e=>a.batches=e),error:n(o).batches,"onUpdate:error":s[3]||(s[3]=e=>n(o).batches=e),"value-prop":"uuid","init-search":_.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:m(e=>[k(p(e.value.course.name)+" - "+p(e.value.name),1)]),listOption:m(e=>[k(p(e.option.course.nameWithTerm)+" - "+p(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):K("",!0)]),r("div",ae,[l(V,{modelValue:a.subject,"onUpdate:modelValue":s[4]||(s[4]=e=>a.subject=e),name:"subject",label:t.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:D.subjects,error:n(o).subject,"onUpdate:error":s[5]||(s[5]=e=>n(o).subject=e)},null,8,["modelValue","label","options","error"])])]),(h(!0),H(T,null,Q(a.details,(e,c)=>(h(),R(L,{class:"mt-4",key:e.uuid},{legend:m(()=>[k(p(c+1)+". ",1),r("span",{class:"text-danger ml-2 cursor-pointer",onClick:u=>w(c)},s[8]||(s[8]=[r("i",{class:"fas fa-times-circle"},null,-1)]),8,te)]),default:m(()=>[r("div",oe,[r("div",re,[l(g,{type:"text",modelValue:e.heading,"onUpdate:modelValue":u=>e.heading=u,name:`details.${c}.heading`,label:t.$trans("resource.diary.props.heading"),error:n(o)[`details.${c}.heading`],"onUpdate:error":u=>n(o)[`details.${c}.heading`]=u},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),r("div",ne,[l(C,{rows:1,modelValue:e.description,"onUpdate:modelValue":u=>e.description=u,name:`details.${c}.description`,label:t.$trans("resource.diary.props.description"),error:n(o)[`details.${c}.description`],"onUpdate:error":u=>n(o)[`details.${c}.description`]=u},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)),r("div",ie,[l(q,{design:"primary",onClick:F,class:"cursor-pointer"},{default:m(()=>[k(p(t.$trans("global.add",{attribute:t.$trans("general.detail")})),1)]),_:1})]),r("div",le,[r("div",de,[l(N,{multiple:"",label:t.$trans("general.file"),module:"student_diary",media:a.media,"media-token":a.mediaToken,onIsUpdated:s[6]||(s[6]=e=>a.mediaUpdated=!0),onSetHash:s[7]||(s[7]=e=>a.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),me={name:"ResourceDiaryAction"},be=Object.assign(me,{setup(M){const f=S();return(d,b)=>{const v=i("PageHeaderAction"),y=i("PageHeader"),o=i("ParentTransition");return h(),H(T,null,[l(y,{title:d.$trans(n(f).meta.trans,{attribute:d.$trans(n(f).meta.label)}),navs:[{label:d.$trans("resource.resource"),path:"Resource"},{label:d.$trans("resource.diary.diary"),path:"ResourceDiaryList"}]},{default:m(()=>[l(v,{name:"ResourceDiary",title:d.$trans("resource.diary.diary"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(o,{appear:"",visibility:!0},{default:m(()=>[l(ue)]),_:1})],64)}}});export{be as default};
