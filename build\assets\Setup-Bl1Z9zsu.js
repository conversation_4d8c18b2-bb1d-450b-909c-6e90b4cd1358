import{_ as t,r as n,a as r,o as a,d as s,e as c}from"./app-BAwPsakn.js";const l={},d={class:"flex min-h-screen items-center overflow-hidden bg-slate-400"},i={class:"mx-auto w-full max-w-screen-xl px-4 py-4 sm:px-6"};function p(_,e,u,m,f,x){const o=n("router-view");return a(),r("div",d,[e[0]||(e[0]=s("div",{class:"bg-grid-background absolute inset-0 bg-top"},null,-1)),s("div",i,[c(o)])])}const b=t(l,[["render",p]]);export{b as default};
