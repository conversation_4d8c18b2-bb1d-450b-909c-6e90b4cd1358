import{i as M,u as H,h as w,H as C,m as O,l as _,n as P,r as l,a as n,o as t,e as m,q as T,b as c,w as r,d as a,F as x,v as B,t as o,x as L,s as u}from"./app-BAwPsakn.js";const A={class:"font-semibold text-sm dark:text-gray-400"},N={class:"grid grid-cols-1 gap-6"},V={key:0,class:"mb-4"},$=["innerHTML"],j={class:"dark:text-gray-400 flex justify-between"},F={class:"flex items-center"},S={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},R=["innerHTML"],U={class:"flex gap-2"},z={class:"dark:text-gray-300 font-semibold text-sm italic"},D={key:0},G={key:0,class:"text-success far fa-check-circle fa-lg"},I={key:1,class:"text-danger far fa-times-circle fa-lg"},J={key:1,class:"ml-1 text-success"},K={name:"ExamOnlineExamSubmission"},Z=Object.assign(K,{setup(Q){const p=M(),g=H(),h=w();C("exam/onlineExam/");const d=O(!1),s=_({});_({});const f=async()=>{await p.dispatch("exam/onlineExam/get",{uuid:g.params.uuid,params:{submission:!0}}).then(i=>{Object.assign(s,i),(!s.isCompleted||!s.resultPublishedAt.value)&&h.push({name:"ExamOnlineExam"}),d.value=!1}).catch(i=>{d.value=!1})};return P(async()=>{await f()}),(i,X)=>{const k=l("PageHeaderAction"),b=l("PageHeader"),v=l("BaseCard"),y=l("ParentTransition");return t(),n(x,null,[m(b,{title:s.title,navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.online_exam.online_exam"),path:"ExamOnlineExam"}]},{default:r(()=>[m(k,{name:"ExamOnlineExam",title:i.$trans("exam.online_exam.online_exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s.uuid?(t(),T(y,{key:0,appear:"",visibility:!0},{default:r(()=>[m(v,{"is-loading":d.value},{title:r(()=>[u(o(s.title),1)]),action:r(()=>[a("span",A,o(i.$trans("exam.online_exam.obtained_mark"))+": "+o(s.obtainedMark),1)]),default:r(()=>[a("div",N,[(t(!0),n(x,null,B(s.questions,(e,E)=>(t(),n("div",{class:"col-span-3 sm:col-span-1",key:e.uuid},[e.header?(t(),n("div",V,[a("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:e.header},null,8,$)])):c("",!0),a("div",j,[a("div",F,[a("span",S,o(E+1)+".",1),a("div",{innerHTML:e.title},null,8,R)]),a("div",U,[a("span",{class:L(["font-semibold",{"text-success":e.obtainedMark>0,"text-danger":e.obtainedMark<0}])},"("+o(e.obtainedMark)+")",3)])]),a("div",z,[e.type.value=="mcq"?(t(),n("span",D,[e.obtainedMark>0?(t(),n("i",G)):(t(),n("i",I))])):c("",!0),u(" "+o(e.answer)+" ",1),e.type.value=="mcq"&&e.obtainedMark<=0?(t(),n("span",J,o(e.correctAnswer),1)):c("",!0)])]))),128))])]),_:1},8,["is-loading"])]),_:1})):c("",!0)],64)}}});export{Z as default};
