import{l as w,r,q as p,o as c,w as t,d as C,e as n,h as N,j as S,y as g,m as E,f as l,a as P,F as O,v as q,s as i,b as v,t as d}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",emits:["hide"],setup(F,{emit:$}){const b=$,D={name:"",code:"",shortcode:"",alias:"",startDate:"",endDate:""},s=w({...D});return(k,o)=>{const m=r("BaseInput"),h=r("DatePicker"),A=r("FilterForm");return c(),p(A,{"init-form":D,form:s,onHide:o[6]||(o[6]=e=>b("hide"))},{default:t(()=>[C("div",z,[C("div",G,[n(m,{type:"text",modelValue:s.name,"onUpdate:modelValue":o[0]||(o[0]=e=>s.name=e),name:"name",label:k.$trans("academic.period.props.name")},null,8,["modelValue","label"])]),C("div",J,[n(m,{type:"text",modelValue:s.code,"onUpdate:modelValue":o[1]||(o[1]=e=>s.code=e),name:"code",label:k.$trans("academic.period.props.code")},null,8,["modelValue","label"])]),C("div",K,[n(m,{type:"text",modelValue:s.shortcode,"onUpdate:modelValue":o[2]||(o[2]=e=>s.shortcode=e),name:"shortcode",label:k.$trans("academic.period.props.shortcode")},null,8,["modelValue","label"])]),C("div",Q,[n(m,{type:"text",modelValue:s.alias,"onUpdate:modelValue":o[3]||(o[3]=e=>s.alias=e),name:"alias",label:k.$trans("academic.period.props.alias")},null,8,["modelValue","label"])]),C("div",W,[n(h,{start:s.startDate,"onUpdate:start":o[4]||(o[4]=e=>s.startDate=e),end:s.endDate,"onUpdate:end":o[5]||(o[5]=e=>s.endDate=e),name:"dateBetween",as:"range",label:k.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Y={key:0,class:"far fa-lg fa-check-circle text-success"},Z={name:"AcademicPeriodList"},ee=Object.assign(Z,{setup(F){const $=N(),b=S("emitter");let D=["filter"];g("period:create")&&D.unshift("create"),g("academic:config")&&D.push("config");let s=[];g("period:export")&&(s=["print","pdf","excel"]);const k="academic/period/",o=E(!1),m=w({}),h=async e=>{b.emit("actionItem",{uuid:e.uuid,action:"default",confirmation:!0})},A=e=>{Object.assign(m,e)};return(e,u)=>{const I=r("PageHeaderAction"),T=r("PageHeader"),B=r("ParentTransition"),V=r("TextMuted"),U=r("BaseBadge"),f=r("DataCell"),y=r("FloatingMenuItem"),H=r("FloatingMenu"),M=r("DataRow"),R=r("BaseButton"),j=r("DataTable"),L=r("ListItem");return c(),p(L,{"init-url":k,onSetItems:A},{header:t(()=>[n(T,{title:e.$trans("academic.period.period"),navs:[{label:e.$trans("academic.academic"),path:"Academic"}]},{default:t(()=>[n(I,{url:"academic/periods/",name:"AcademicPeriod",title:e.$trans("academic.period.period"),actions:l(D),"dropdown-actions":l(s),onToggleFilter:u[0]||(u[0]=a=>o.value=!o.value),"config-path":"AcademicConfig"},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[n(B,{appear:"",visibility:o.value},{default:t(()=>[n(X,{onRefresh:u[1]||(u[1]=a=>l(b).emit("listItems")),onHide:u[2]||(u[2]=a=>o.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[n(B,{appear:"",visibility:!0},{default:t(()=>[n(j,{header:m.headers,meta:m.meta,module:"academic.period",onRefresh:u[4]||(u[4]=a=>l(b).emit("listItems"))},{actionButton:t(()=>[l(g)("period:create")?(c(),p(R,{key:0,onClick:u[3]||(u[3]=a=>l($).push({name:"AcademicPeriodCreate"}))},{default:t(()=>[i(d(e.$trans("global.add",{attribute:e.$trans("academic.period.period")})),1)]),_:1})):v("",!0)]),default:t(()=>[(c(!0),P(O,null,q(m.data,a=>(c(),p(M,{key:a.uuid,onDoubleClick:_=>l($).push({name:"AcademicPeriodShow",params:{uuid:a.uuid}})},{default:t(()=>[n(f,{name:"name"},{default:t(()=>[i(d(a.name)+" ",1),n(V,{block:""},{default:t(()=>[i(d(a.alias),1)]),_:2},1024),a.isDefault?(c(),p(U,{key:0,color:"success",size:"sm"},{default:t(()=>[i(d(e.$trans("general.default")),1)]),_:1})):v("",!0)]),_:2},1024),n(f,null,{default:t(()=>[a.enableRegistration?(c(),P("i",Y)):v("",!0)]),_:2},1024),n(f,{name:"session"},{default:t(()=>{var _;return[i(d(((_=a.session)==null?void 0:_.name)||"-"),1)]}),_:2},1024),n(f,{name:"code"},{default:t(()=>[i(d(a.code)+" ",1),n(V,{block:""},{default:t(()=>[i(d(a.shortcode),1)]),_:2},1024)]),_:2},1024),n(f,{name:"startDate"},{default:t(()=>[i(d(a.startDate.formatted),1)]),_:2},1024),n(f,{name:"endDate"},{default:t(()=>[i(d(a.endDate.formatted),1)]),_:2},1024),n(f,{name:"createdAt"},{default:t(()=>[i(d(a.createdAt.formatted),1)]),_:2},1024),n(f,{name:"action"},{default:t(()=>[n(H,null,{default:t(()=>[n(y,{icon:"fas fa-arrow-circle-right",onClick:_=>l($).push({name:"AcademicPeriodShow",params:{uuid:a.uuid}})},{default:t(()=>[i(d(e.$trans("general.show")),1)]),_:2},1032,["onClick"]),l(g)("period:edit")&&!a.isDefault?(c(),p(y,{key:0,icon:"fas fa-check-circle",onClick:_=>h(a)},{default:t(()=>[i(d(e.$trans("global.set_default",{attribute:e.$trans("academic.period.period")})),1)]),_:2},1032,["onClick"])):v("",!0),l(g)("period:edit")?(c(),p(y,{key:1,icon:"fas fa-edit",onClick:_=>l($).push({name:"AcademicPeriodEdit",params:{uuid:a.uuid}})},{default:t(()=>[i(d(e.$trans("general.edit")),1)]),_:2},1032,["onClick"])):v("",!0),l(g)("period:create")?(c(),p(y,{key:2,icon:"fas fa-copy",onClick:_=>l($).push({name:"AcademicPeriodDuplicate",params:{uuid:a.uuid}})},{default:t(()=>[i(d(e.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):v("",!0),l(g)("period:delete")?(c(),p(y,{key:3,icon:"fas fa-trash",onClick:_=>l(b).emit("deleteItem",{uuid:a.uuid})},{default:t(()=>[i(d(e.$trans("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ee as default};
