import{u as A,j as B,y as C,r as m,a as P,o as d,q as u,b as y,e as l,w as o,f as t,d as w,s as c,t as s,F as D}from"./app-BAwPsakn.js";import{_ as E}from"./EditRequestInfo-CdeRga5q.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},V={name:"EmployeeShowContact"},H=Object.assign(V,{props:{employee:{type:Object,default(){return{}}}},setup(e){const b=A(),a=B("$trans"),i=e;let p=[];return C("employee:edit")&&!i.employee.self&&p.push({label:a("global.edit",{attribute:a("contact.contact")}),path:{name:"EmployeeEditContact",params:{uuid:i.employee.uuid}}}),(k,x)=>{const f=m("PageHeaderAction"),g=m("PageHeader"),n=m("BaseDataView"),_=m("BaseCard"),h=m("ParentTransition");return d(),P(D,null,[e.employee.uuid?(d(),u(g,{key:0,title:t(a)(t(b).meta.label),navs:[{label:t(a)("employee.employee"),path:"Employee"},{label:e.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:e.employee.uuid}}}]},{default:o(()=>[l(f,{"additional-actions":t(p)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):y("",!0),l(h,{appear:"",visibility:!0},{default:o(()=>[e.employee.uuid?(d(),u(_,{key:0},{default:o(()=>[l(E,{employee:e.employee},null,8,["employee"]),w("dl",N,[l(n,{label:t(a)("contact.props.contact_number")},{default:o(()=>[c(s(e.employee.contact.contactNumber),1)]),_:1},8,["label"]),l(n,{label:t(a)("global.alternate",{attribute:t(a)("contact.props.contact_number")})},{default:o(()=>{var r;return[c(s((r=e.employee.contact.alternateRecords)==null?void 0:r.contactNumber),1)]}),_:1},8,["label"]),l(n),l(n,{label:t(a)("contact.props.email")},{default:o(()=>[c(s(e.employee.contact.email),1)]),_:1},8,["label"]),l(n,{label:t(a)("global.alternate",{attribute:t(a)("contact.props.email")})},{default:o(()=>{var r;return[c(s((r=e.employee.contact.alternateRecords)==null?void 0:r.email),1)]}),_:1},8,["label"]),l(n),l(n,{class:"col-span-1 sm:col-span-3",label:t(a)("contact.props.present_address")},{default:o(()=>[c(s(e.employee.contact.presentAddressDisplay),1)]),_:1},8,["label"]),l(n,{class:"col-span-1 sm:col-span-3",label:t(a)("contact.props.permanent_address")},{default:o(()=>[c(s(e.employee.contact.sameAsPresentAddress?e.employee.contact.presentAddressDisplay:e.employee.contact.permanentAddressDisplay),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1})],64)}}});export{H as default};
