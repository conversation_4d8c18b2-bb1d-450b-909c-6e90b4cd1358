import{i as T,h as q,H as S,c as k,l as h,r as l,a as m,o as d,e as a,w as i,d as L,q as N,b as P,f as n,F as _,s as f,t as c}from"./app-BAwPsakn.js";const A={class:"space-y-6"},E={key:2},H={key:3},G={key:4},V="auth/password/",D={__name:"Password",setup(I){const v=T(),g=q(),t=S(V),p=k(()=>v.getters["auth/password/isPasswordRequestSent"]),u=k(()=>v.getters["auth/password/isResetTokenConfirmed"]),B=k(()=>u.value?"resetPassword":p.value?"resetTokenConfirm":"passwordRequest"),y={email:"",code:"",newPassword:"",newPasswordConfirmation:""},s=h({...y}),C=()=>{s.newPassword&&(Object.assign(s,y),g.push({name:"Login"}))};return(r,e)=>{const U=l("GuestHeader"),w=l("BaseInput"),$=l("BaseLink"),b=l("BaseButton"),F=l("FormAction"),R=l("ParentTransition");return d(),m(_,null,[a(U,{label:r.$trans("auth.password.password_title")},null,8,["label"]),a(R,{appear:"",visibility:!0},{default:i(()=>[a(F,{"no-card":"","no-action-button":"","init-url":V,"init-form":y,form:s,action:B.value,"stay-on":"","after-submit":C},{default:i(()=>[L("div",A,[a(w,{type:"text","leading-icon":"fas fa-envelope",modelValue:s.email,"onUpdate:modelValue":e[0]||(e[0]=o=>s.email=o),name:"email",label:r.$trans("auth.password.props.email"),error:n(t).email,"onUpdate:error":e[1]||(e[1]=o=>n(t).email=o),readonly:p.value||u.value,autofocus:""},null,8,["modelValue","label","error","readonly"]),p.value?(d(),N(w,{key:0,type:"text","leading-icon":"fas fa-keyboard",modelValue:s.code,"onUpdate:modelValue":e[2]||(e[2]=o=>s.code=o),name:"code",label:r.$trans("auth.password.props.code"),error:n(t).code,"onUpdate:error":e[3]||(e[3]=o=>n(t).code=o),readonly:u.value},null,8,["modelValue","label","error","readonly"])):P("",!0),u.value?(d(),m(_,{key:1},[a(w,{type:"password","leading-icon":"fas fa-key",modelValue:s.newPassword,"onUpdate:modelValue":e[4]||(e[4]=o=>s.newPassword=o),name:"newPassword",label:r.$trans("auth.password.props.new_password"),error:n(t).newPassword,"onUpdate:error":e[5]||(e[5]=o=>n(t).newPassword=o)},null,8,["modelValue","label","error"]),a(w,{type:"password","leading-icon":"fas fa-key",modelValue:s.newPasswordConfirmation,"onUpdate:modelValue":e[6]||(e[6]=o=>s.newPasswordConfirmation=o),name:"newPasswordConfirmation",label:r.$trans("auth.password.props.new_password_confirmation"),error:n(t).newPasswordConfirmation,"onUpdate:error":e[7]||(e[7]=o=>n(t).newPasswordConfirmation=o)},null,8,["modelValue","label","error"])],64)):P("",!0),a($,{to:"Login"},{default:i(()=>[f(c(r.$trans("auth.login.login_title")),1)]),_:1}),u.value?(d(),m("div",E,[a(b,{type:"submit",block:""},{default:i(()=>[f(c(r.$trans("auth.password.reset_password")),1)]),_:1})])):p.value?(d(),m("div",H,[a(b,{type:"submit",block:""},{default:i(()=>[f(c(r.$trans("auth.password.verify_token")),1)]),_:1})])):(d(),m("div",G,[a(b,{type:"submit",block:""},{default:i(()=>[f(c(r.$trans("auth.password.request_password")),1)]),_:1})]))])]),_:1},8,["form","action"])]),_:1})],64)}}};export{D as default};
