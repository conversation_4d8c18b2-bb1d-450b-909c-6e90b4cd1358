import{j as L,H as Q,l as P,r as u,q as _,o as d,w as e,d as m,e as o,f as t,i as W,u as X,h as Y,z as Z,a as q,b as y,F as V,v as I,s,t as r,A as ee,y as $}from"./app-BAwPsakn.js";const te={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3"},ie={name:"EnquiryFollowUpForm"},re=Object.assign(ie,{props:{uuid:{type:String,default:""}},emits:["completed"],setup(A,{emit:v}){const D=v,a=L("emitter"),k={followUpDate:null,nextFollowUpDate:null,status:"",remarks:""},x="reception/enquiryFollowUp/",b=Q(x),B=P({statuses:[]}),f=P({...k}),n=w=>{Object.assign(B,w)},R=()=>{D("completed"),a.emit("listItems")};return(w,i)=>{const F=u("DatePicker"),S=u("BaseSelect"),h=u("BaseTextarea"),c=u("FormAction");return d(),_(c,{"pre-requisites":!0,onSetPreRequisites:n,"init-url":x,uuid:A.uuid,"no-data-fetch":"",action:"create","init-form":k,form:f,"keep-adding":!1,"after-submit":R},{default:e(()=>[m("div",te,[m("div",oe,[o(F,{modelValue:f.followUpDate,"onUpdate:modelValue":i[0]||(i[0]=p=>f.followUpDate=p),name:"followUpDate",label:w.$trans("reception.enquiry.follow_up.props.follow_up_date"),"no-clear":"",error:t(b).followUpDate,"onUpdate:error":i[1]||(i[1]=p=>t(b).followUpDate=p)},null,8,["modelValue","label","error"])]),m("div",ne,[o(F,{modelValue:f.nextFollowUpDate,"onUpdate:modelValue":i[2]||(i[2]=p=>f.nextFollowUpDate=p),name:"nextFollowUpDate",label:w.$trans("reception.enquiry.follow_up.props.next_follow_up_date"),error:t(b).nextFollowUpDate,"onUpdate:error":i[3]||(i[3]=p=>t(b).nextFollowUpDate=p)},null,8,["modelValue","label","error"])]),m("div",ae,[o(S,{name:"status",label:w.$trans("reception.enquiry.follow_up.props.status"),modelValue:f.status,"onUpdate:modelValue":i[4]||(i[4]=p=>f.status=p),"label-prop":"label","value-prop":"value",options:B.statuses,error:t(b).status,"onUpdate:error":i[5]||(i[5]=p=>t(b).status=p)},null,8,["label","modelValue","options","error"])]),m("div",le,[o(h,{rows:1,modelValue:f.remarks,"onUpdate:modelValue":i[6]||(i[6]=p=>f.remarks=p),name:"remarks",label:w.$trans("reception.enquiry.follow_up.props.remarks"),error:t(b).remarks,"onUpdate:error":i[7]||(i[7]=p=>t(b).remarks=p)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),se={class:"space-y-4"},ue={class:"flex items-center gap-2"},de={key:0},pe=["onClick"],ce={class:"p-4"},me={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},_e={class:"text-xs"},fe={name:"ReceptionEnquiryShow"},be=Object.assign(fe,{setup(A){W();const v=X(),D=Y(),a=L("$trans"),k=L("emitter"),x={},b="reception/enquiry/",B=[{key:"studentName",label:a("reception.enquiry.props.student_name"),visibility:!0},{key:"birthDate",label:a("contact.props.birth_date"),visibility:!0},{key:"gender",label:a("contact.props.gender"),visibility:!0},{key:"course",label:a("academic.course.course"),visibility:!0},{key:"action",label:"",visibility:!0}],f=[{key:"followUpDate",label:a("reception.enquiry.follow_up.props.follow_up_date"),visibility:!0},{key:"nextFollowUpDate",label:a("reception.enquiry.follow_up.props.next_follow_up_date"),visibility:!0},{key:"status",label:a("reception.enquiry.follow_up.props.status"),visibility:!0},{key:"action",label:"",visibility:!0}],n=P({...x}),R=w=>{Object.assign(n,w)};return(w,i)=>{const F=u("PageHeaderAction"),S=u("PageHeader"),h=u("BaseBadge"),c=u("ListItemView"),p=u("TextMuted"),H=u("ListContainerVertical"),C=u("BaseCard"),g=u("DataCell"),T=u("BaseButton"),E=u("DataRow"),N=u("SimpleTable"),j=u("ListMedia"),M=u("BaseDataView"),O=u("ShowButton"),z=u("DetailLayoutVertical"),G=u("ShowItem"),J=u("ParentTransition"),K=Z("tooltip");return d(),q(V,null,[o(S,{title:t(a)(t(v).meta.trans,{attribute:t(a)(t(v).meta.label)}),navs:[{label:t(a)("reception.reception"),path:"Reception"},{label:t(a)("reception.enquiry.enquiry"),path:"ReceptionEnquiryList"}]},{default:e(()=>[o(F,{name:"ReceptionEnquiry",title:t(a)("reception.enquiry.enquiry"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(J,{appear:"",visibility:!0},{default:e(()=>[o(G,{"init-url":b,uuid:t(v).params.uuid,onSetItem:R,onRedirectTo:i[2]||(i[2]=l=>t(D).push({name:"ReceptionEnquiry"}))},{default:e(()=>[n.uuid?(d(),_(z,{key:0},{detail:e(()=>[o(C,{"no-padding":"","no-content-padding":""},{title:e(()=>[s(r(t(a)("global.detail",{attribute:t(a)("reception.enquiry.enquiry")}))+" ",1),o(h,{design:n.status.color},{default:e(()=>[s(r(n.status.label),1)]),_:1},8,["design"])]),default:e(()=>[o(H,null,{default:e(()=>[o(c,{label:t(a)("reception.enquiry.props.code_number")},{default:e(()=>[s(r(n.codeNumber),1)]),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.props.date")},{default:e(()=>[s(r(n.date.formatted),1)]),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.type.type")},{default:e(()=>{var l;return[s(r(((l=n.type)==null?void 0:l.name)||"-"),1)]}),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.source.source")},{default:e(()=>{var l;return[s(r(((l=n.source)==null?void 0:l.name)||"-"),1)]}),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.props.name")},{default:e(()=>[s(r(n.name),1)]),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.props.email")},{default:e(()=>[s(r(n.email),1)]),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.props.contact_number")},{default:e(()=>[s(r(n.contactNumber),1)]),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.props.assigned_to")},{default:e(()=>{var l;return[s(r(((l=n.employee)==null?void 0:l.name)||"-")+" ",1),n.employee?(d(),_(p,{key:0,block:""},{default:e(()=>{var U;return[s(r(((U=n.employee)==null?void 0:U.designation)||"-"),1)]}),_:1})):y("",!0)]}),_:1},8,["label"]),o(c,{label:t(a)("reception.enquiry.props.remarks")},{default:e(()=>[s(r(n.remarks),1)]),_:1},8,["label"]),(d(!0),q(V,null,I(n.customFields||[],l=>(d(),_(c,{key:l.uuid,label:l.label},{default:e(()=>[s(r(l.formattedValue),1)]),_:2},1032,["label"]))),128)),o(c,{label:t(a)("general.created_at")},{default:e(()=>[s(r(n.createdAt.formatted),1)]),_:1},8,["label"]),o(c,{label:t(a)("general.updated_at")},{default:e(()=>[s(r(n.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[m("div",se,[o(C,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[s(r(t(a)("reception.enquiry.enquiry")),1)]),footer:e(()=>[o(O,null,{default:e(()=>[t($)("enquiry:edit")?(d(),_(T,{key:0,design:"primary",onClick:i[0]||(i[0]=l=>t(D).push({name:"ReceptionEnquiryEdit",params:{uuid:n.uuid}}))},{default:e(()=>[s(r(t(a)("general.edit")),1)]),_:1})):y("",!0)]),_:1})]),default:e(()=>[n.records.length>0?(d(),_(N,{key:0,header:B},{default:e(()=>[(d(!0),q(V,null,I(n.records,l=>(d(),_(E,{key:l.uuid},{default:e(()=>[o(g,{name:"studentName"},{default:e(()=>[m("div",ue,[s(r(l.studentName)+" ",1),l.isConverted?(d(),q("span",de,i[3]||(i[3]=[m("i",{class:"far fa-check-circle fa-lg text-success"},null,-1)]))):y("",!0)]),l.isConverted?(d(),_(p,{key:0,block:""},{default:e(()=>[m("span",{class:"cursor-pointer",onClick:U=>t(D).push({name:"StudentRegistrationShow",params:{uuid:l.registrationUuid}})},r(t(a)("reception.enquiry.converted_to_registration")),9,pe)]),_:2},1024)):y("",!0)]),_:2},1024),o(g,{name:"birthDate"},{default:e(()=>[s(r(l.birthDate.formatted),1)]),_:2},1024),o(g,{name:"gender"},{default:e(()=>[s(r(l.gender.label),1)]),_:2},1024),o(g,{name:"course"},{default:e(()=>[s(r(l.course.name),1)]),_:2},1024),o(g,{name:"action"},{default:e(()=>[t($)("enquiry:action")&&!l.isConverted?ee((d(),_(T,{key:0,size:"xs",onClick:U=>t(k).emit("showActionItem",{uuid:n==null?void 0:n.uuid,moduleUuid:l.uuid,action:"convertToRegistration",confirmation:!0})},{default:e(()=>i[4]||(i[4]=[m("i",{class:"fas fa-share"},null,-1)])),_:2},1032,["onClick"])),[[K,t(a)("reception.enquiry.convert_to_registration")]]):y("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})):y("",!0),m("div",ce,[m("dl",me,[o(M,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(j,{media:n.media,url:`/app/reception/enquiries/${n.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1}),n.followUps.length>0?(d(),_(C,{key:0,"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[s(r(t(a)("reception.enquiry.follow_up.follow_up")),1)]),default:e(()=>[n.followUps.length>0?(d(),_(N,{key:0,header:f},{default:e(()=>[(d(!0),q(V,null,I(n.followUps,l=>(d(),q(V,{key:l.uuid},[o(E,null,{default:e(()=>[o(g,{name:"followUpDate"},{default:e(()=>[s(r(l.followUpDate.formatted),1)]),_:2},1024),o(g,{name:"nextFollowUpDate"},{default:e(()=>[s(r(l.nextFollowUpDate.formatted),1)]),_:2},1024),o(g,{name:"status"},{default:e(()=>[s(r(l.status.label),1)]),_:2},1024),o(g,{name:"action"},{default:e(()=>[t($)("enquiry:follow-up")?(d(),_(T,{key:0,size:"xs",design:"danger",onClick:U=>t(k).emit("showActionItem",{uuid:n==null?void 0:n.uuid,moduleUuid:l.uuid,action:"removeFollowUp",confirmation:!0})},{default:e(()=>i[5]||(i[5]=[m("i",{class:"fas fa-trash"},null,-1)])),_:2},1032,["onClick"])):y("",!0)]),_:2},1024)]),_:2},1024),o(E,null,{default:e(()=>[o(g,{colspan:100},{default:e(()=>[m("span",_e,r(l.remarks),1)]),_:2},1024)]),_:2},1024)],64))),128))]),_:1})):y("",!0)]),_:1})):y("",!0)]),t($)("enquiry:follow-up")?(d(),_(C,{key:0,"no-padding":"","no-content-padding":""},{title:e(()=>[s(r(t(a)("reception.enquiry.follow_up.follow_up")),1)]),default:e(()=>[o(re,{onCompleted:i[1]||(i[1]=l=>t(k).emit("refreshItem"))})]),_:1})):y("",!0)]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{be as default};
