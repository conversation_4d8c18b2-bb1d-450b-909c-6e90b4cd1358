import{u as M,l as F,n as S,r as s,q as b,o as d,w as e,d as C,e as n,h as U,j as E,y as v,m as O,f as a,a as h,F as B,v as j,s as c,t as l,b as D}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(I,{emit:p}){M();const $=p,g={codeNumber:"",startDate:"",endDate:""},u=F({...g}),w=F({isLoaded:!0});return S(async()=>{w.isLoaded=!0}),(f,o)=>{const R=s("BaseInput"),r=s("DatePicker"),i=s("FilterForm");return d(),b(i,{"init-form":g,form:u,multiple:[],onHide:o[3]||(o[3]=m=>$("hide"))},{default:e(()=>[C("div",q,[C("div",x,[n(R,{type:"text",modelValue:u.codeNumber,"onUpdate:modelValue":o[0]||(o[0]=m=>u.codeNumber=m),name:"codeNumber",label:f.$trans("recruitment.vacancy.props.code_number")},null,8,["modelValue","label"])]),C("div",z,[n(r,{start:u.startDate,"onUpdate:start":o[1]||(o[1]=m=>u.startDate=m),end:u.endDate,"onUpdate:end":o[2]||(o[2]=m=>u.endDate=m),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={class:"flex text-xs justify-between mr-4"},K={name:"RecruitmentVacancyList"},W=Object.assign(K,{setup(I){const p=U(),$=E("emitter");let g=["filter"];v("recruitment:config")&&g.push("config"),v("job-vacancy:create")&&g.unshift("create");let u=[];v("job-vacancy:export")&&(u=["print","pdf","excel"]);const w="recruitment/vacancy/",f=O(!1),o=F({}),R=r=>{Object.assign(o,r)};return(r,i)=>{const m=s("PageHeaderAction"),N=s("PageHeader"),V=s("ParentTransition"),y=s("DataCell"),k=s("FloatingMenuItem"),P=s("FloatingMenu"),A=s("DataRow"),H=s("BaseButton"),L=s("DataTable"),T=s("ListItem");return d(),b(T,{"init-url":w,"additional-query":{},onSetItems:R},{header:e(()=>[n(N,{title:r.$trans("recruitment.vacancy.vacancy"),navs:[{label:r.$trans("recruitment.recruitment"),path:"Recruitment"}]},{default:e(()=>[n(m,{url:"recruitment/vacancies/",name:"RecruitmentVacancy",title:r.$trans("recruitment.vacancy.vacancy"),actions:a(g),"dropdown-actions":a(u),"config-path":"RecruitmentConfig",onToggleFilter:i[0]||(i[0]=t=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(V,{appear:"",visibility:f.value},{default:e(()=>[n(G,{onRefresh:i[1]||(i[1]=t=>a($).emit("listItems")),onHide:i[2]||(i[2]=t=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(V,{appear:"",visibility:!0},{default:e(()=>[n(L,{header:o.headers,meta:o.meta,module:"recruitment.vacancy",onRefresh:i[4]||(i[4]=t=>a($).emit("listItems"))},{actionButton:e(()=>[a(v)("job-vacancy:create")?(d(),b(H,{key:0,onClick:i[3]||(i[3]=t=>a(p).push({name:"RecruitmentVacancyCreate"}))},{default:e(()=>[c(l(r.$trans("global.add",{attribute:r.$trans("recruitment.vacancy.vacancy")})),1)]),_:1})):D("",!0)]),default:e(()=>[(d(!0),h(B,null,j(o.data,t=>(d(),b(A,{key:t.uuid,onDoubleClick:_=>a(p).push({name:"RecruitmentVacancyShow",params:{uuid:t.uuid}})},{default:e(()=>[n(y,{name:"codeNumber"},{default:e(()=>[c(l(t.codeNumber),1)]),_:2},1024),n(y,{name:"title"},{default:e(()=>[c(l(t.titleExcerpt),1)]),_:2},1024),n(y,{name:"records",table:""},{default:e(()=>[(d(!0),h(B,null,j(t.records,_=>(d(),h("div",J,[c(l(_.designation.name)+" ",1),C("div",null,l(_.numberOfPositions),1)]))),256))]),_:2},1024),n(y,{name:"lastApplicationDate"},{default:e(()=>[c(l(t.lastApplicationDate.formatted),1)]),_:2},1024),n(y,{name:"createdAt"},{default:e(()=>[c(l(t.createdAt.formatted),1)]),_:2},1024),n(y,{name:"action"},{default:e(()=>[n(P,null,{default:e(()=>[n(k,{icon:"fas fa-arrow-circle-right",onClick:_=>a(p).push({name:"RecruitmentVacancyShow",params:{uuid:t.uuid}})},{default:e(()=>[c(l(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(v)("job-vacancy:edit")?(d(),b(k,{key:0,icon:"fas fa-edit",onClick:_=>a(p).push({name:"RecruitmentVacancyEdit",params:{uuid:t.uuid}})},{default:e(()=>[c(l(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):D("",!0),a(v)("job-vacancy:create")?(d(),b(k,{key:1,icon:"fas fa-copy",onClick:_=>a(p).push({name:"RecruitmentVacancyDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[c(l(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):D("",!0),a(v)("job-vacancy:delete")?(d(),b(k,{key:2,icon:"fas fa-trash",onClick:_=>a($).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[c(l(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
