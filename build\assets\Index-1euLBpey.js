import{u as h,i as v,m as y,l as R,n as B,r as s,a as r,o as n,e as t,w as a,d as i,F as u,v as P,t as d,s as $}from"./app-BAwPsakn.js";const w={class:"grid grid-cols-4 gap-6"},C={class:"col-span-4 sm:col-span-1"},H=["href"],L={class:"block border-2 border-primary dark:border-gray-400 rounded-xl px-4 py-2 cursor-pointer dark:text-gray-400"},q={name:"ResourceBookList"},V=Object.assign(q,{setup(x){h();const _=v(),p="resource/bookList/",o=y(!1),c=R({courses:[]}),b=async()=>{o.value=!0,await _.dispatch(p+"preRequisite",{}).then(e=>{o.value=!1,Object.assign(c,e)}).catch(e=>{o.value=!1})};return B(async()=>{await b()}),(e,N)=>{const m=s("PageHeaderAction"),g=s("PageHeader"),k=s("BaseCard"),f=s("ParentTransition");return n(),r(u,null,[t(g,{title:e.$trans("resource.book_list.book_list"),navs:[{label:e.$trans("resource.resource"),path:"Resource"}]},{default:a(()=>[t(m,{name:"Resource",title:e.$trans("resource.book_list.book_list"),actions:[]},null,8,["title"])]),_:1},8,["title","navs"]),t(f,{appear:"",visibility:!0},{default:a(()=>[t(k,null,{title:a(()=>[$(d(e.$trans("resource.book_list.book_list")),1)]),default:a(()=>[i("div",w,[(n(!0),r(u,null,P(c.courses,l=>(n(),r("div",C,[i("a",{href:`/app/resource/book-lists/${l.uuid}`,target:"_blank"},[i("span",L,d(l.name),1)],8,H)]))),256))])]),_:1})]),_:1})],64)}}});export{V as default};
