import{u as h,G as U,l as T,H as L,n as E,r as p,q as v,o as b,w as u,d as l,a as F,e as m,f as n,F as P,v as N,s as k,t as A,M as I,J as M}from"./app-BAwPsakn.js";const G={class:"grid grid-cols-2 gap-6"},J={class:"col-span-2 sm:col-span-1"},z={class:"col-span-2"},K=["onClick"],Q={class:"mt-4 grid grid-cols-4 gap-3"},W={class:"col-span-4 flex items-end sm:col-span-1"},X={class:"col-span-4 flex items-end space-x-4 sm:col-span-1"},Y={class:"col-span-4 sm:col-span-1"},Z={class:"col-span-4 sm:col-span-1"},x={class:"mt-4"},ee={name:"AcademicClassTimingForm"},se=Object.assign(ee,{setup(w){const _=h(),c={name:"",sessions:[],description:""},f={uuid:U(),name:"",startTime:"",endTime:"",isBreak:!1},g="academic/classTiming/",$=T({}),t=L(g),i=T({...c}),y=T({isLoaded:!_.params.uuid}),C=s=>{Object.assign($,s)},V=()=>{i.sessions.push({...f,uuid:U()}),y.isLoaded=!0},R=async s=>{await I()&&(i.sessions.length==1?i.sessions=[f]:i.sessions.splice(s,1))},j=s=>{let r=s.sessions.map(d=>({...d}));Object.assign(c,{name:s.name,sessions:r.map(d=>({uuid:d.uuid,name:d.name,startTime:d.startTime.at,endTime:d.endTime.at,isBreak:d.isBreak})),description:s.description}),Object.assign(i,M(c))};return E(async()=>{_.params.uuid||V()}),(s,r)=>{const d=p("BaseInput"),q=p("BaseTextarea"),D=p("BaseSwitch"),B=p("DatePicker"),H=p("BaseFieldset"),O=p("BaseBadge"),S=p("FormAction");return b(),v(S,{"pre-requisites":!1,onSetPreRequisites:C,"init-url":g,"init-form":c,form:i,"set-form":j,redirect:"AcademicClassTiming"},{default:u(()=>[l("div",G,[l("div",J,[m(d,{type:"text",modelValue:i.name,"onUpdate:modelValue":r[0]||(r[0]=e=>i.name=e),name:"name",label:s.$trans("academic.class_timing.props.name"),error:n(t).name,"onUpdate:error":r[1]||(r[1]=e=>n(t).name=e),autofocus:""},null,8,["modelValue","label","error"])]),l("div",z,[m(q,{rows:1,modelValue:i.description,"onUpdate:modelValue":r[2]||(r[2]=e=>i.description=e),name:"description",label:s.$trans("academic.class_timing.props.description"),error:n(t).description,"onUpdate:error":r[3]||(r[3]=e=>n(t).description=e)},null,8,["modelValue","label","error"])])]),(b(!0),F(P,null,N(i.sessions,(e,o)=>(b(),v(H,{class:"mt-4",key:e.uuid},{legend:u(()=>[k(A(o+1)+". ",1),l("span",{class:"text-danger ml-2 cursor-pointer",onClick:a=>R(o)},r[4]||(r[4]=[l("i",{class:"fas fa-times-circle"},null,-1)]),8,K)]),default:u(()=>[l("div",Q,[l("div",W,[m(d,{type:"text",modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,name:`sessions.${o}.name`,label:s.$trans("academic.class_timing.props.session"),error:n(t)[`sessions.${o}.name`],"onUpdate:error":a=>n(t)[`sessions.${o}.name`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),l("div",X,[m(D,{reverse:"",modelValue:e.isBreak,"onUpdate:modelValue":a=>e.isBreak=a,name:`sessions.${o}.isBreak`,label:s.$trans("academic.class_timing.props.is_break"),error:n(t)[`sessions.${o}.isBreak`],"onUpdate:error":a=>n(t)[`sessions.${o}.isBreak`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),l("div",Y,[m(B,{name:`sessions.${o}.startTime`,modelValue:e.startTime,"onUpdate:modelValue":a=>e.startTime=a,label:s.$trans("academic.class_timing.props.start_time"),as:"time",error:n(t)[`sessions.${o}.startTime`],"onUpdate:error":a=>n(t)[`sessions.${o}.startTime`]=a},null,8,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error"])]),l("div",Z,[m(B,{name:`sessions.${o}.endTime`,modelValue:e.endTime,"onUpdate:modelValue":a=>e.endTime=a,label:s.$trans("academic.class_timing.props.end_time"),as:"time",error:n(t)[`sessions.${o}.endTime`],"onUpdate:error":a=>n(t)[`sessions.${o}.endTime`]=a},null,8,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error"])])])]),_:2},1024))),128)),l("div",x,[m(O,{design:"primary",onClick:V,class:"cursor-pointer"},{default:u(()=>[k(A(s.$trans("global.add",{attribute:s.$trans("academic.class_timing.props.session")})),1)]),_:1})])]),_:1},8,["form"])}}}),ae={name:"AcademicTimetableAction"},oe=Object.assign(ae,{setup(w){const _=h();return(c,f)=>{const g=p("PageHeaderAction"),$=p("PageHeader"),t=p("ParentTransition");return b(),F(P,null,[m($,{title:c.$trans(n(_).meta.trans,{attribute:c.$trans(n(_).meta.label)}),navs:[{label:c.$trans("academic.academic"),path:"Academic"},{label:c.$trans("academic.timetable.timetable"),path:"AcademicTimetableList"}]},{default:u(()=>[m(g,{name:"AcademicTimetable",title:c.$trans("academic.timetable.timetable"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),m(t,{appear:"",visibility:!0},{default:u(()=>[m(se)]),_:1})],64)}}});export{oe as default};
