import{u as P,i as V,j as A,y as j,c as H,r as n,a as m,o,q as u,b as f,e as s,w as a,f as t,s as c,t as d,d as N,F as b,v as S}from"./app-BAwPsakn.js";const D={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},L={class:"space-x-1"},T={name:"ContactShowLogin"},O=Object.assign(T,{props:{contact:{type:Object,default(){return{}}}},setup(l){const x=P();V();const e=A("$trans"),p=l;let g=[];j("contact:edit")&&g.push({label:e("global.edit",{attribute:e("contact.login.login")}),path:{name:"ContactEditLogin",params:{uuid:p.contact.uuid}}});const r=H(()=>p.contact.user);return(E,_)=>{const B=n("PageHeaderAction"),v=n("PageHeader"),y=n("BaseAlert"),i=n("BaseDataView"),C=n("BaseBadge"),h=n("BaseCard"),k=n("ParentTransition");return o(),m(b,null,[l.contact.uuid?(o(),u(v,{key:0,title:t(e)(t(x).meta.label),navs:[{label:t(e)("contact.contact"),path:"Contact"},{label:l.contact.name,path:{name:"ContactShow",params:{uuid:l.contact.uuid}}}]},{default:a(()=>[s(B,{"additional-actions":t(g)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):f("",!0),s(k,{appear:"",visibility:!0},{default:a(()=>[l.contact.uuid?(o(),u(h,{key:0},{default:a(()=>[r.value?(o(),m("dl",D,[s(i,{label:t(e)("contact.login.props.email")},{default:a(()=>[c(d(r.value.email),1)]),_:1},8,["label"]),s(i,{label:t(e)("contact.login.props.username")},{default:a(()=>[c(d(r.value.username),1)]),_:1},8,["label"]),s(i,{label:t(e)("contact.login.props.password")},{default:a(()=>_[0]||(_[0]=[c(" xxxxxxxxx ")])),_:1},8,["label"]),s(i,{label:t(e)("team.config.role.role")},{default:a(()=>[N("div",L,[(o(!0),m(b,null,S(r.value.roles,w=>(o(),u(C,{design:"primary"},{default:a(()=>[c(d(w.label),1)]),_:2},1024))),256))])]),_:1},8,["label"])])):(o(),u(y,{key:0,design:"error"},{default:a(()=>[c(d(t(e)("contact.login.no_login_found")),1)]),_:1}))]),_:1})):f("",!0)]),_:1})],64)}}});export{O as default};
