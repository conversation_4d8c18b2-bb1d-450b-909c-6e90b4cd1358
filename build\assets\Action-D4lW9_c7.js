import{H as b,l as g,r as l,q as V,o as c,w as d,d as i,e as s,f as n,u as $,a as v,F as E}from"./app-BAwPsakn.js";const B={class:"grid grid-cols-3 gap-6"},F={class:"col-span-3 sm:col-span-1"},U={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},A={name:"EmployeeDepartmentForm"},D=Object.assign(A,{setup(_){const m={name:"",alias:"",description:""},o="employee/department/",r=b(o),a=g({...m});return(p,e)=>{const u=l("BaseInput"),f=l("BaseTextarea"),y=l("FormAction");return c(),V(y,{"init-url":o,"init-form":m,form:a,redirect:"EmployeeDepartment"},{default:d(()=>[i("div",B,[i("div",F,[s(u,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),name:"name",label:p.$trans("employee.department.props.name"),error:n(r).name,"onUpdate:error":e[1]||(e[1]=t=>n(r).name=t),autofocus:""},null,8,["modelValue","label","error"])]),i("div",U,[s(u,{type:"text",modelValue:a.alias,"onUpdate:modelValue":e[2]||(e[2]=t=>a.alias=t),name:"alias",label:p.$trans("employee.department.props.alias"),error:n(r).alias,"onUpdate:error":e[3]||(e[3]=t=>n(r).alias=t),autofocus:""},null,8,["modelValue","label","error"])]),i("div",P,[s(f,{modelValue:a.description,"onUpdate:modelValue":e[4]||(e[4]=t=>a.description=t),name:"description",label:p.$trans("employee.department.props.description"),error:n(r).description,"onUpdate:error":e[5]||(e[5]=t=>n(r).description=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),H={name:"EmployeeDepartmentAction"},k=Object.assign(H,{setup(_){const m=$();return(o,r)=>{const a=l("PageHeaderAction"),p=l("PageHeader"),e=l("ParentTransition");return c(),v(E,null,[s(p,{title:o.$trans(n(m).meta.trans,{attribute:o.$trans(n(m).meta.label)}),navs:[{label:o.$trans("employee.employee"),path:"Employee"},{label:o.$trans("employee.department.department"),path:"EmployeeDepartmentList"}]},{default:d(()=>[s(a,{name:"EmployeeDepartment",title:o.$trans("employee.department.department"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(e,{appear:"",visibility:!0},{default:d(()=>[s(D)]),_:1})],64)}}});export{k as default};
