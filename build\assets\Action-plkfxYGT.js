import{u as g,H as $,l as b,r as i,q as B,o as u,w as h,d as m,a as _,b as V,e as r,f as s,J as F,F as G}from"./app-BAwPsakn.js";const I={class:"grid grid-cols-3 gap-6"},k={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},M={key:0,class:"col-span-3 sm:col-span-1"},j={key:0,class:"mt-6 grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3"},L={name:"FinancePaymentMethodForm"},S=Object.assign(L,{setup(v){const p=g(),l={name:"",code:"",hasInstrumentDate:!1,hasInstrumentNumber:!1,hasClearingDate:!1,hasBankDetail:!1,hasReferenceNumber:!1,isPaymentGateway:!1,paymentGatewayName:"",description:""},c="finance/paymentMethod/",t=$(c),y=b({}),n=b({...l}),N=b({isLoaded:!p.params.uuid}),P=o=>{Object.assign(y,o)},U=o=>{Object.assign(l,{...o}),Object.assign(n,F(l)),N.isLoaded=!0};return(o,e)=>{const f=i("BaseInput"),d=i("BaseSwitch"),w=i("BaseTextarea"),D=i("FormAction");return u(),B(D,{"pre-requisites":!1,onSetPreRequisites:P,"init-url":c,"init-form":l,form:n,"set-form":U,redirect:"FinancePaymentMethod"},{default:h(()=>[m("div",I,[m("div",k,[r(f,{type:"text",modelValue:n.name,"onUpdate:modelValue":e[0]||(e[0]=a=>n.name=a),name:"name",label:o.$trans("finance.payment_method.props.name"),error:s(t).name,"onUpdate:error":e[1]||(e[1]=a=>s(t).name=a)},null,8,["modelValue","label","error"])]),m("div",R,[r(f,{type:"text",modelValue:n.code,"onUpdate:modelValue":e[2]||(e[2]=a=>n.code=a),name:"code",label:o.$trans("finance.payment_method.props.code"),error:s(t).code,"onUpdate:error":e[3]||(e[3]=a=>s(t).code=a)},null,8,["modelValue","label","error"])]),m("div",C,[r(d,{vertical:"",modelValue:n.isPaymentGateway,"onUpdate:modelValue":e[4]||(e[4]=a=>n.isPaymentGateway=a),name:"isPaymentGateway",label:o.$trans("finance.payment_method.props.is_payment_gateway"),error:s(t).isPaymentGateway,"onUpdate:error":e[5]||(e[5]=a=>s(t).isPaymentGateway=a)},null,8,["modelValue","label","error"])]),n.isPaymentGateway?(u(),_("div",M,[r(f,{type:"text",modelValue:n.paymentGatewayName,"onUpdate:modelValue":e[6]||(e[6]=a=>n.paymentGatewayName=a),name:"paymentGatewayName",label:o.$trans("finance.payment_method.props.payment_gateway_name"),error:s(t).paymentGatewayName,"onUpdate:error":e[7]||(e[7]=a=>s(t).paymentGatewayName=a)},null,8,["modelValue","label","error"])])):V("",!0)]),n.isPaymentGateway?V("",!0):(u(),_("div",j,[m("div",q,[r(d,{vertical:"",modelValue:n.hasInstrumentNumber,"onUpdate:modelValue":e[8]||(e[8]=a=>n.hasInstrumentNumber=a),name:"hasInstrumentNumber",label:o.$trans("finance.payment_method.props.has_instrument_number"),error:s(t).hasInstrumentNumber,"onUpdate:error":e[9]||(e[9]=a=>s(t).hasInstrumentNumber=a)},null,8,["modelValue","label","error"])]),m("div",A,[r(d,{vertical:"",modelValue:n.hasInstrumentDate,"onUpdate:modelValue":e[10]||(e[10]=a=>n.hasInstrumentDate=a),name:"hasInstrumentDate",label:o.$trans("finance.payment_method.props.has_instrument_date"),error:s(t).hasInstrumentDate,"onUpdate:error":e[11]||(e[11]=a=>s(t).hasInstrumentDate=a)},null,8,["modelValue","label","error"])]),m("div",H,[r(d,{vertical:"",modelValue:n.hasClearingDate,"onUpdate:modelValue":e[12]||(e[12]=a=>n.hasClearingDate=a),name:"hasClearingDate",label:o.$trans("finance.payment_method.props.has_clearing_date"),error:s(t).hasClearingDate,"onUpdate:error":e[13]||(e[13]=a=>s(t).hasClearingDate=a)},null,8,["modelValue","label","error"])]),m("div",O,[r(d,{vertical:"",modelValue:n.hasBankDetail,"onUpdate:modelValue":e[14]||(e[14]=a=>n.hasBankDetail=a),name:"hasBankDetail",label:o.$trans("finance.payment_method.props.has_bank_detail"),error:s(t).hasBankDetail,"onUpdate:error":e[15]||(e[15]=a=>s(t).hasBankDetail=a)},null,8,["modelValue","label","error"])]),m("div",T,[r(d,{vertical:"",modelValue:n.hasReferenceNumber,"onUpdate:modelValue":e[16]||(e[16]=a=>n.hasReferenceNumber=a),name:"hasReferenceNumber",label:o.$trans("finance.payment_method.props.has_reference_number"),error:s(t).hasReferenceNumber,"onUpdate:error":e[17]||(e[17]=a=>s(t).hasReferenceNumber=a)},null,8,["modelValue","label","error"])]),m("div",E,[r(w,{modelValue:n.description,"onUpdate:modelValue":e[18]||(e[18]=a=>n.description=a),name:"description",label:o.$trans("finance.payment_method.props.description"),error:s(t).description,"onUpdate:error":e[19]||(e[19]=a=>s(t).description=a)},null,8,["modelValue","label","error"])])]))]),_:1},8,["form"])}}}),J={name:"FinancePaymentMethodAction"},K=Object.assign(J,{setup(v){const p=g();return(l,c)=>{const t=i("PageHeaderAction"),y=i("PageHeader"),n=i("ParentTransition");return u(),_(G,null,[r(y,{title:l.$trans(s(p).meta.trans,{attribute:l.$trans(s(p).meta.label)}),navs:[{label:l.$trans("finance.finance"),path:"Finance"},{label:l.$trans("finance.payment_method.payment_method"),path:"FinancePaymentMethodList"}]},{default:h(()=>[r(t,{name:"FinancePaymentMethod",title:l.$trans("finance.payment_method.payment_method"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(n,{appear:"",visibility:!0},{default:h(()=>[r(S)]),_:1})],64)}}});export{K as default};
