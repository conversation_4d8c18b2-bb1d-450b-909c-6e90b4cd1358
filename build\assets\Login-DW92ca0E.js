import{u as $,i as E,j as C,y as P,c as F,g as I,m as O,r as o,a as _,o as n,q as c,b,e as l,w as e,f as t,s as i,t as p,d as w,F as v,v as S}from"./app-BAwPsakn.js";const q={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},z={class:"space-x-1"},R={class:"flex flex-wrap gap-2"},U={name:"StudentShowLogin"},K=Object.assign(U,{props:{student:{type:Object,default(){return{}}}},setup(m){const V=$(),A=E(),a=C("$trans"),j=C("emitter"),f=m;let x=[];P("student:edit")&&x.push({label:a("global.edit",{attribute:a("contact.login.login")}),path:{name:"StudentEditLogin",params:{uuid:f.student.uuid}}});const d=F(()=>{var r,s;return(s=(r=f.student)==null?void 0:r.contact)==null?void 0:s.user}),H=I("periods"),y=O(!1),L=r=>{var s;r.id!=((s=d.value)==null?void 0:s.currentPeriodId)&&(y.value=!0,A.dispatch("student/updateCurrentPeriod",{uuid:f.student.uuid,form:{period_id:r.id}}).then(()=>{j.emit("studentUpdated")}).catch(()=>{}).finally(()=>{y.value=!1}))};return(r,s)=>{const N=o("PageHeaderAction"),D=o("PageHeader"),T=o("BaseAlert"),g=o("BaseDataView"),B=o("BaseBadge"),h=o("BaseCard"),k=o("ParentTransition");return n(),_(v,null,[m.student.uuid?(n(),c(D,{key:0,title:t(a)(t(V).meta.label),navs:[{label:t(a)("student.student"),path:"Student"},{label:m.student.contact.name,path:{name:"StudentShow",params:{uuid:m.student.uuid}}}]},{default:e(()=>[l(N,{"additional-actions":t(x)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):b("",!0),l(k,{appear:"",visibility:!0},{default:e(()=>[m.student.uuid?(n(),c(h,{key:0},{default:e(()=>[d.value?(n(),_("dl",q,[l(g,{label:t(a)("contact.login.props.email")},{default:e(()=>[i(p(d.value.email),1)]),_:1},8,["label"]),l(g,{label:t(a)("contact.login.props.username")},{default:e(()=>[i(p(d.value.username),1)]),_:1},8,["label"]),l(g,{label:t(a)("contact.login.props.password")},{default:e(()=>s[0]||(s[0]=[i(" xxxxxxxxx ")])),_:1},8,["label"]),l(g,{label:t(a)("team.config.role.role")},{default:e(()=>[w("div",z,[(n(!0),_(v,null,S(d.value.roles,u=>(n(),c(B,{design:"primary"},{default:e(()=>[i(p(u.label),1)]),_:2},1024))),256))])]),_:1},8,["label"])])):(n(),c(T,{key:0,design:"error"},{default:e(()=>[i(p(t(a)("contact.login.no_login_found")),1)]),_:1}))]),_:1})):b("",!0)]),_:1}),t(P)("student:edit")?(n(),c(k,{key:1,appear:"",visibility:!0},{default:e(()=>[l(h,null,{title:e(()=>[i(p(t(a)("global.update",{attribute:t(a)("academic.period.current_period")})),1)]),default:e(()=>[w("div",R,[(n(!0),_(v,null,S(t(H),u=>(n(),c(B,{key:u.id,size:"md",design:u.id==d.value.currentPeriodId?"success":"info",class:"cursor-pointer",onClick:G=>L(u)},{default:e(()=>[i(p(u.name),1)]),_:2},1032,["design","onClick"]))),128))])]),_:1})]),_:1})):b("",!0)],64)}}});export{K as default};
