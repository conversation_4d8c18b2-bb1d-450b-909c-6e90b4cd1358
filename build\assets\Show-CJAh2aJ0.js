import{i as S,u as v,h as C,l as P,r as n,a as V,o as p,e as a,w as t,f as r,q as y,b as H,d as I,s as l,t as o,F as N}from"./app-BAwPsakn.js";const T={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},D={name:"AssetBuildingBlockShow"},E=Object.assign(D,{setup(R){S();const d=v(),c=C(),b={},m="asset/building/block/",s=P({...b}),g=e=>{Object.assign(s,e)};return(e,u)=>{const B=n("PageHeaderAction"),_=n("PageHeader"),i=n("BaseDataView"),f=n("BaseButton"),k=n("ShowButton"),$=n("BaseCard"),A=n("ShowItem"),h=n("ParentTransition");return p(),V(N,null,[a(_,{title:e.$trans(r(d).meta.trans,{attribute:e.$trans(r(d).meta.label)}),navs:[{label:e.$trans("asset.asset"),path:"Asset"},{label:e.$trans("asset.building.building"),path:"AssetBuilding"},{label:e.$trans("asset.building.block.block"),path:"AssetBuildingBlockList"}]},{default:t(()=>[a(B,{name:"AssetBuildingBlock",title:e.$trans("asset.building.block.block"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(h,{appear:"",visibility:!0},{default:t(()=>[a(A,{"init-url":m,uuid:r(d).params.uuid,onSetItem:g,onRedirectTo:u[1]||(u[1]=w=>r(c).push({name:"AssetBuildingBlock"}))},{default:t(()=>[s.uuid?(p(),y($,{key:0},{title:t(()=>[l(o(s.name),1)]),footer:t(()=>[a(k,null,{default:t(()=>[a(f,{design:"primary",onClick:u[0]||(u[0]=w=>r(c).push({name:"AssetBuildingBlockEdit",params:{uuid:s.uuid}}))},{default:t(()=>[l(o(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[I("dl",T,[a(i,{label:e.$trans("asset.building.block.props.name")},{default:t(()=>[l(o(s.name),1)]),_:1},8,["label"]),a(i,{label:e.$trans("asset.building.block.props.alias")},{default:t(()=>[l(o(s.alias),1)]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2",label:e.$trans("asset.building.block.props.description")},{default:t(()=>[l(o(s.description),1)]),_:1},8,["label"]),a(i,{label:e.$trans("general.created_at")},{default:t(()=>[l(o(s.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:e.$trans("general.updated_at")},{default:t(()=>[l(o(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):H("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
