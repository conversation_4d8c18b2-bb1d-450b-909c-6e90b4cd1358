import{i as C,H as N,l as I,r as i,q as k,o as p,w as u,e as t,d as n,a as g,b as f,f as o,F as V,s as H,t as M}from"./app-BAwPsakn.js";const F={class:"grid grid-cols-3 gap-4"},q={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},x={class:"grid grid-cols-3 gap-4"},D={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={class:"grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},G={class:"grid grid-cols-3 gap-4"},_={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},h={name:"ConfigSMS"},se=Object.assign(h,{setup(ee){const c=C(),b="config/",a=N(b),v=I({}),P={driver:"",senderId:"",testNumber:"",testTemplateId:"",apiKey:"",apiSecret:"",apiUrl:"",apiMethod:"",numberPrefix:"",senderIdParam:"",receiverParam:"",messageParam:"",templateIdParam:"",apiHeaders:"",additionalParams:"",type:"sms"},s=I({...P}),$=l=>{Object.assign(v,l)},S=()=>{c.dispatch("config/testSMS").catch(l=>{})};return(l,e)=>{const T=i("BaseButton"),m=i("CardHeader"),U=i("BaseSelect"),d=i("BaseInput"),y=i("BaseTextarea"),B=i("FormAction"),K=i("ConfigPage");return p(),k(K,null,{action:u(()=>[t(T,{design:"primary",onClick:S},{default:u(()=>[H(M(l.$trans("config.sms.test_sms")),1)]),_:1})]),default:u(()=>[t(B,{"no-card":"","init-url":b,"pre-requisites":{data:["smsDrivers"]},onSetPreRequisites:$,"data-fetch":"sms","init-form":P,form:s,action:"store","stay-on":"",redirect:"Config"},{default:u(()=>[t(m,{first:"",title:l.$trans("config.sms.sms_config"),description:l.$trans("config.sms.sms_info")},null,8,["title","description"]),n("div",F,[n("div",q,[t(U,{modelValue:s.driver,"onUpdate:modelValue":e[0]||(e[0]=r=>s.driver=r),name:"driver",label:l.$trans("config.sms.props.driver"),options:v.smsDrivers,error:o(a).driver,"onUpdate:error":e[1]||(e[1]=r=>o(a).driver=r)},null,8,["modelValue","label","options","error"])]),n("div",E,[t(d,{type:"text",modelValue:s.senderId,"onUpdate:modelValue":e[2]||(e[2]=r=>s.senderId=r),name:"senderId",label:l.$trans("config.sms.props.sender_id"),error:o(a).senderId,"onUpdate:error":e[3]||(e[3]=r=>o(a).senderId=r)},null,8,["modelValue","label","error"])]),n("div",w,[t(d,{type:"text",modelValue:s.testNumber,"onUpdate:modelValue":e[4]||(e[4]=r=>s.testNumber=r),name:"testNumber",label:l.$trans("config.sms.props.test_number"),error:o(a).testNumber,"onUpdate:error":e[5]||(e[5]=r=>o(a).testNumber=r)},null,8,["modelValue","label","error"])])]),s.driver=="twilio"?(p(),g(V,{key:0},[t(m,{title:l.$trans("config.sms.twilio")},null,8,["title"]),n("div",x,[n("div",D,[t(d,{type:"text",modelValue:s.apiKey,"onUpdate:modelValue":e[6]||(e[6]=r=>s.apiKey=r),name:"apiKey",label:l.$trans("config.sms.props.api_key"),error:o(a).apiKey,"onUpdate:error":e[7]||(e[7]=r=>o(a).apiKey=r)},null,8,["modelValue","label","error"])]),n("div",O,[t(d,{type:"text",modelValue:s.apiSecret,"onUpdate:modelValue":e[8]||(e[8]=r=>s.apiSecret=r),name:"apiSecret",label:l.$trans("config.sms.props.api_secret"),error:o(a).apiSecret,"onUpdate:error":e[9]||(e[9]=r=>o(a).apiSecret=r)},null,8,["modelValue","label","error"])])])],64)):f("",!0),s.driver=="msg91"?(p(),g(V,{key:1},[t(m,{title:l.$trans("config.sms.msg91")},null,8,["title"]),n("div",R,[n("div",j,[t(d,{type:"text",modelValue:s.apiKey,"onUpdate:modelValue":e[10]||(e[10]=r=>s.apiKey=r),name:"apiKey",label:l.$trans("config.sms.props.api_key"),error:o(a).apiKey,"onUpdate:error":e[11]||(e[11]=r=>o(a).apiKey=r)},null,8,["modelValue","label","error"])]),n("div",A,[t(d,{type:"text",modelValue:s.testTemplateId,"onUpdate:modelValue":e[12]||(e[12]=r=>s.testTemplateId=r),name:"testTemplateId",label:"Test Template ID",error:o(a).testTemplateId,"onUpdate:error":e[13]||(e[13]=r=>o(a).testTemplateId=r)},null,8,["modelValue","error"])])])],64)):f("",!0),s.driver=="custom"?(p(),g(V,{key:2},[t(m,{title:l.$trans("config.sms.custom")},null,8,["title"]),n("div",G,[n("div",_,[t(d,{type:"text",modelValue:s.apiUrl,"onUpdate:modelValue":e[14]||(e[14]=r=>s.apiUrl=r),name:"apiUrl",label:l.$trans("config.sms.props.api_url"),error:o(a).apiUrl,"onUpdate:error":e[15]||(e[15]=r=>o(a).apiUrl=r)},null,8,["modelValue","label","error"])]),n("div",z,[t(U,{modelValue:s.apiMethod,"onUpdate:modelValue":e[16]||(e[16]=r=>s.apiMethod=r),name:"apiMethod",label:l.$trans("config.sms.props.api_method"),options:[{label:"GET",value:"GET"},{label:"POST",value:"POST"}],error:o(a).apiMethod,"onUpdate:error":e[17]||(e[17]=r=>o(a).apiMethod=r)},null,8,["modelValue","label","error"])]),n("div",J,[t(d,{type:"text",modelValue:s.numberPrefix,"onUpdate:modelValue":e[18]||(e[18]=r=>s.numberPrefix=r),name:"numberPrefix",label:l.$trans("config.sms.props.number_prefix"),error:o(a).numberPrefix,"onUpdate:error":e[19]||(e[19]=r=>o(a).numberPrefix=r)},null,8,["modelValue","label","error"])]),n("div",L,[t(d,{type:"text",modelValue:s.senderIdParam,"onUpdate:modelValue":e[20]||(e[20]=r=>s.senderIdParam=r),name:"senderIdParam",label:l.$trans("config.sms.props.sender_id_param"),error:o(a).senderIdParam,"onUpdate:error":e[21]||(e[21]=r=>o(a).senderIdParam=r)},null,8,["modelValue","label","error"])]),n("div",Q,[t(d,{type:"text",modelValue:s.receiverParam,"onUpdate:modelValue":e[22]||(e[22]=r=>s.receiverParam=r),name:"receiverParam",label:l.$trans("config.sms.props.receiver_param"),error:o(a).receiverParam,"onUpdate:error":e[23]||(e[23]=r=>o(a).receiverParam=r)},null,8,["modelValue","label","error"])]),n("div",W,[t(d,{type:"text",modelValue:s.messageParam,"onUpdate:modelValue":e[24]||(e[24]=r=>s.messageParam=r),name:"messageParam",label:l.$trans("config.sms.props.message_param"),error:o(a).messageParam,"onUpdate:error":e[25]||(e[25]=r=>o(a).messageParam=r)},null,8,["modelValue","label","error"])]),n("div",X,[t(d,{type:"text",modelValue:s.templateIdParam,"onUpdate:modelValue":e[26]||(e[26]=r=>s.templateIdParam=r),name:"templateIdParam",label:l.$trans("config.sms.props.template_id_param"),error:o(a).templateIdParam,"onUpdate:error":e[27]||(e[27]=r=>o(a).templateIdParam=r)},null,8,["modelValue","label","error"])]),n("div",Y,[t(y,{modelValue:s.apiHeaders,"onUpdate:modelValue":e[28]||(e[28]=r=>s.apiHeaders=r),name:"apiHeaders",label:l.$trans("config.sms.props.api_headers"),error:o(a).apiHeaders,"onUpdate:error":e[29]||(e[29]=r=>o(a).apiHeaders=r)},null,8,["modelValue","label","error"])]),n("div",Z,[t(y,{modelValue:s.additionalParams,"onUpdate:modelValue":e[30]||(e[30]=r=>s.additionalParams=r),name:"additionalParams",label:l.$trans("config.sms.props.additional_params"),error:o(a).additionalParams,"onUpdate:error":e[31]||(e[31]=r=>o(a).additionalParams=r)},null,8,["modelValue","label","error"])])])],64)):f("",!0)]),_:1},8,["form"])]),_:1})}}});export{se as default};
