var bn=Object.defineProperty;var xn=(i,t,e)=>t in i?bn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var O=(i,t,e)=>xn(i,typeof t!="symbol"?t+"":t,e);import{W as Ps,X as Os,Y as Ye,Z as _n,m as yn,n as vn,C as kn,K as wn,$ as Ue,a0 as Ds,a1 as Mn}from"./app-BAwPsakn.js";/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka <PERSON>
 * Released under the MIT License
 */function te(i){return i+.5|0}const at=(i,t,e)=>Math.max(Math.min(i,e),t);function Nt(i){return at(te(i*2.55),0,255)}function ct(i){return at(te(i*255),0,255)}function ot(i){return at(te(i/2.55)/100,0,1)}function gi(i){return at(te(i*100),0,100)}const U={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ke=[..."0123456789ABCDEF"],Sn=i=>Ke[i&15],Pn=i=>Ke[(i&240)>>4]+Ke[i&15],ne=i=>(i&240)>>4===(i&15),On=i=>ne(i.r)&&ne(i.g)&&ne(i.b)&&ne(i.a);function Dn(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&U[i[1]]*17,g:255&U[i[2]]*17,b:255&U[i[3]]*17,a:t===5?U[i[4]]*17:255}:(t===7||t===9)&&(e={r:U[i[1]]<<4|U[i[2]],g:U[i[3]]<<4|U[i[4]],b:U[i[5]]<<4|U[i[6]],a:t===9?U[i[7]]<<4|U[i[8]]:255})),e}const Cn=(i,t)=>i<255?t(i):"";function Tn(i){var t=On(i)?Sn:Pn;return i?"#"+t(i.r)+t(i.g)+t(i.b)+Cn(i.a,t):void 0}const An=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Cs(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function Ln(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Fn(i,t,e){const s=Cs(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function Rn(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function Je(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Rn(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function ti(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(ct)}function ei(i,t,e){return ti(Cs,i,t,e)}function In(i,t,e){return ti(Fn,i,t,e)}function zn(i,t,e){return ti(Ln,i,t,e)}function Ts(i){return(i%360+360)%360}function En(i){const t=An.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?Nt(+t[5]):ct(+t[5]));const n=Ts(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=In(n,o,r):t[1]==="hsv"?s=zn(n,o,r):s=ei(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function Bn(i,t){var e=Je(i);e[0]=Ts(e[0]+t),e=ei(e),i.r=e[0],i.g=e[1],i.b=e[2]}function Hn(i){if(!i)return;const t=Je(i),e=t[0],s=gi(t[1]),n=gi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${ot(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const pi={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},mi={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Wn(){const i={},t=Object.keys(mi),e=Object.keys(pi);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,pi[o]);o=parseInt(mi[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let oe;function Vn(i){oe||(oe=Wn(),oe.transparent=[0,0,0,0]);const t=oe[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Nn=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function jn(i){const t=Nn.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?Nt(r):at(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?Nt(s):at(s,0,255)),n=255&(t[4]?Nt(n):at(n,0,255)),o=255&(t[6]?Nt(o):at(o,0,255)),{r:s,g:n,b:o,a:e}}}function $n(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${ot(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const Le=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Pt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function Yn(i,t,e){const s=Pt(ot(i.r)),n=Pt(ot(i.g)),o=Pt(ot(i.b));return{r:ct(Le(s+e*(Pt(ot(t.r))-s))),g:ct(Le(n+e*(Pt(ot(t.g))-n))),b:ct(Le(o+e*(Pt(ot(t.b))-o))),a:i.a+e*(t.a-i.a)}}function re(i,t,e){if(i){let s=Je(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=ei(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function As(i,t){return i&&Object.assign(t||{},i)}function bi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=ct(i[3]))):(t=As(i,{r:0,g:0,b:0,a:1}),t.a=ct(t.a)),t}function Un(i){return i.charAt(0)==="r"?jn(i):En(i)}class qt{constructor(t){if(t instanceof qt)return t;const e=typeof t;let s;e==="object"?s=bi(t):e==="string"&&(s=Dn(t)||Vn(t)||Un(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=As(this._rgb);return t&&(t.a=ot(t.a)),t}set rgb(t){this._rgb=bi(t)}rgbString(){return this._valid?$n(this._rgb):void 0}hexString(){return this._valid?Tn(this._rgb):void 0}hslString(){return this._valid?Hn(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=Yn(this._rgb,t._rgb,e)),this}clone(){return new qt(this.rgb)}alpha(t){return this._rgb.a=ct(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=te(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return re(this._rgb,2,t),this}darken(t){return re(this._rgb,2,-t),this}saturate(t){return re(this._rgb,1,t),this}desaturate(t){return re(this._rgb,1,-t),this}rotate(t){return Bn(this._rgb,t),this}}/*!
 * Chart.js v4.4.8
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function it(){}const Kn=(()=>{let i=0;return()=>i++})();function F(i){return i==null}function E(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function D(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function X(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function J(i,t){return X(i)?i:t}function A(i,t){return typeof i>"u"?t:i}const Xn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function R(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function T(i,t,e,s){let n,o,r;if(E(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(D(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function ve(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ke(i){if(E(i))return i.map(ke);if(D(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=ke(i[e[n]]);return t}return i}function Ls(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function qn(i,t,e,s){if(!Ls(i))return;const n=t[i],o=e[i];D(n)&&D(o)?Gt(n,o,s):t[i]=ke(o)}function Gt(i,t,e){const s=E(t)?t:[t],n=s.length;if(!D(i))return i;e=e||{};const o=e.merger||qn;let r;for(let a=0;a<n;++a){if(r=s[a],!D(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function Yt(i,t){return Gt(i,t,{merger:Gn})}function Gn(i,t,e){if(!Ls(i))return;const s=t[i],n=e[i];D(s)&&D(n)?Yt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=ke(n))}const xi={"":i=>i,x:i=>i.x,y:i=>i.y};function Zn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Qn(i){const t=Zn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function At(i,t){return(xi[t]||(xi[t]=Qn(t)))(i)}function ii(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Zt=i=>typeof i<"u",ft=i=>typeof i=="function",_i=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Jn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const V=Math.PI,ht=2*V,we=Number.POSITIVE_INFINITY,to=V/180,G=V/2,mt=V/4,yi=V*2/3,Fs=Math.log10,dt=Math.sign;function be(i,t,e){return Math.abs(i-t)<e}function vi(i){const t=Math.round(i);i=be(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(Fs(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function eo(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function io(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function Me(i){return!io(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function so(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function no(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function vt(i){return i*(V/180)}function oo(i){return i*(180/V)}function ki(i){if(!X(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function ro(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*V&&(o+=ht),{angle:o,distance:n}}function ao(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function bt(i){return(i%ht+ht)%ht}function lo(i,t,e,s){const n=bt(i),o=bt(t),r=bt(e),a=bt(o-n),l=bt(r-n),c=bt(n-o),h=bt(n-r);return n===o||n===r||s||a>l&&c<h}function Z(i,t,e){return Math.max(t,Math.min(e,i))}function co(i){return Z(i,-32768,32767)}function Dt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function si(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Xe=(i,t,e,s)=>si(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),ho=(i,t,e)=>si(i,e,s=>i[s][t]>=e);function fo(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Rs=["push","pop","shift","splice","unshift"];function uo(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Rs.forEach(e=>{const s="_onData"+ii(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function wi(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Rs.forEach(o=>{delete i[o]}),delete i._chartjs)}function Is(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const zs=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Es(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,zs.call(window,()=>{s=!1,i.apply(t,e)}))}}function go(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const ni=i=>i==="start"?"left":i==="end"?"right":"center",H=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,po=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t,ae=i=>i===0||i===1,Mi=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*ht/e)),Si=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*ht/e)+1,Ut={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*G)+1,easeOutSine:i=>Math.sin(i*G),easeInOutSine:i=>-.5*(Math.cos(V*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>ae(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>ae(i)?i:Mi(i,.075,.3),easeOutElastic:i=>ae(i)?i:Si(i,.075,.3),easeInOutElastic(i){return ae(i)?i:i<.5?.5*Mi(i*2,.1125,.45):.5+.5*Si(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Ut.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Ut.easeInBounce(i*2)*.5:Ut.easeOutBounce(i*2-1)*.5+.5};function Bs(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Pi(i){return Bs(i)?i:new qt(i)}function Fe(i){return Bs(i)?i:new qt(i).saturate(.5).darken(.1).hexString()}const mo=["x","y","borderWidth","radius","tension"],bo=["color","borderColor","backgroundColor"];function xo(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:bo},numbers:{type:"number",properties:mo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function _o(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Oi=new Map;function yo(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Oi.get(e);return s||(s=new Intl.NumberFormat(i,t),Oi.set(e,s)),s}function Hs(i,t,e){return yo(t,e).format(i)}const vo={values(i){return E(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=ko(i,e)}const r=Fs(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Hs(i,s,l)}};function ko(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var Ws={formatters:vo};function wo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ws.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const wt=Object.create(null),qe=Object.create(null);function Kt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Re(i,t,e){return typeof t=="string"?Gt(Kt(i,t),e):Gt(Kt(i,""),t)}class Mo{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Fe(n.backgroundColor),this.hoverBorderColor=(s,n)=>Fe(n.borderColor),this.hoverColor=(s,n)=>Fe(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Re(this,t,e)}get(t){return Kt(this,t)}describe(t,e){return Re(qe,t,e)}override(t,e){return Re(wt,t,e)}route(t,e,s,n){const o=Kt(this,t),r=Kt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return D(l)?Object.assign({},c,l):A(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var z=new Mo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[xo,_o,wo]);function So(i){return!i||F(i.size)||F(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Di(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function xt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Ci(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function Ti(i,t,e,s){Vs(i,t,e,s,null)}function Vs(i,t,e,s,n){let o,r,a,l,c,h,d,f;const u=t.pointStyle,m=t.rotation,g=t.radius;let p=(m||0)*to;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(p),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(g)||g<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,g,0,0,ht):i.arc(e,s,g,0,ht),i.closePath();break;case"triangle":h=n?n/2:g,i.moveTo(e+Math.sin(p)*h,s-Math.cos(p)*g),p+=yi,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*g),p+=yi,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*g),i.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(p+mt)*l,d=Math.cos(p+mt)*(n?n/2-c:l),a=Math.sin(p+mt)*l,f=Math.sin(p+mt)*(n?n/2-c:l),i.arc(e-d,s-a,c,p-V,p-G),i.arc(e+f,s-r,c,p-G,p),i.arc(e+d,s+a,c,p,p+G),i.arc(e-f,s+r,c,p+G,p+V),i.closePath();break;case"rect":if(!m){l=Math.SQRT1_2*g,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}p+=mt;case"rectRot":d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+f,s-r),i.lineTo(e+d,s+a),i.lineTo(e-f,s+r),i.closePath();break;case"crossRot":p+=mt;case"cross":d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"star":d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r),p+=mt,d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"line":r=n?n/2:Math.cos(p)*g,a=Math.sin(p)*g,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(p)*(n?n/2:g),s+Math.sin(p)*g);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function Ns(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function oi(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function ri(i){i.restore()}function Po(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),F(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Oo(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function Do(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Qt(i,t,e,s,n,o={}){const r=E(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,Po(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Do(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),F(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Oo(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function Se(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*V,V,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,V,G,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,G,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-G,!0),i.lineTo(e+r.topLeft,s)}const Co=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,To=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Ao(i,t){const e=(""+i).match(Co);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const Lo=i=>+i||0;function js(i,t){const e={},s=D(t),n=s?Object.keys(t):t,o=D(i)?s?r=>A(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=Lo(o(r));return e}function $s(i){return js(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Ct(i){return js(i,["topLeft","topRight","bottomLeft","bottomRight"])}function q(i){const t=$s(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function W(i,t){i=i||{},t=t||z.font;let e=A(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=A(i.style,t.style);s&&!(""+s).match(To)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:A(i.family,t.family),lineHeight:Ao(A(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:A(i.weight,t.weight),string:""};return n.string=So(n),n}function le(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function Fo(i,t,e){const{min:s,max:n}=i,o=Xn(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Ft(i,t){return Object.assign(Object.create(i),t)}function ai(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=Xs("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>ai([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Us(a,l,()=>Vo(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return Li(a).includes(l)},ownKeys(a){return Li(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Lt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Ys(i,s),setContext:o=>Lt(i,o,e,s),override:o=>Lt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Us(o,r,()=>Io(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function Ys(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:ft(e)?e:()=>e,isIndexable:ft(s)?s:()=>s}}const Ro=(i,t)=>i?i+ii(t):t,li=(i,t)=>D(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Us(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function Io(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return ft(a)&&r.isScriptable(t)&&(a=zo(t,a,i,e)),E(a)&&a.length&&(a=Eo(t,a,i,r.isIndexable)),li(t,a)&&(a=Lt(a,n,o&&o[t],r)),a}function zo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),li(i,l)&&(l=ci(n._scopes,n,i,l)),l}function Eo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(D(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=ci(c,n,i,h);t.push(Lt(d,o,r&&r[i],a))}}return t}function Ks(i,t,e){return ft(i)?i(t,e):i}const Bo=(i,t)=>i===!0?t:typeof i=="string"?At(t,i):void 0;function Ho(i,t,e,s,n){for(const o of t){const r=Bo(e,o);if(r){i.add(r);const a=Ks(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function ci(i,t,e,s){const n=t._rootScopes,o=Ks(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=Ai(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Ai(a,r,o,l,s),l===null)?!1:ai(Array.from(a),[""],n,o,()=>Wo(t,e,s))}function Ai(i,t,e,s,n){for(;e;)e=Ho(i,t,e,s,n);return e}function Wo(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return E(n)&&D(e)?e:n||{}}function Vo(i,t,e,s){let n;for(const o of t)if(n=Xs(Ro(o,i),e),typeof n<"u")return li(i,n)?ci(e,s,i,n):n}function Xs(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Li(i){let t=i._keys;return t||(t=i._keys=No(i._scopes)),t}function No(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function hi(){return typeof window<"u"&&typeof document<"u"}function di(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Pe(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Ce=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function jo(i,t){return Ce(i).getPropertyValue(t)}const $o=["top","right","bottom","left"];function kt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=$o[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Yo=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Uo(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(Yo(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function yt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Ce(e),o=n.boxSizing==="border-box",r=kt(n,"padding"),a=kt(n,"border","width"),{x:l,y:c,box:h}=Uo(i,e),d=r.left+(h&&a.left),f=r.top+(h&&a.top);let{width:u,height:m}=t;return o&&(u-=r.width+a.width,m-=r.height+a.height),{x:Math.round((l-d)/u*e.width/s),y:Math.round((c-f)/m*e.height/s)}}function Ko(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&di(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Ce(o),l=kt(a,"border","width"),c=kt(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=Pe(a.maxWidth,o,"clientWidth"),n=Pe(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||we,maxHeight:n||we}}const ce=i=>Math.round(i*10)/10;function Xo(i,t,e,s){const n=Ce(i),o=kt(n,"margin"),r=Pe(n.maxWidth,i,"clientWidth")||we,a=Pe(n.maxHeight,i,"clientHeight")||we,l=Ko(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const f=kt(n,"border","width"),u=kt(n,"padding");c-=u.width+f.width,h-=u.height+f.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=ce(Math.min(c,r,l.maxWidth)),h=ce(Math.min(h,a,l.maxHeight)),c&&!h&&(h=ce(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=ce(Math.floor(h*s))),{width:c,height:h}}function Fi(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const qo=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};hi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Ri(i,t){const e=jo(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}const Go=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Zo=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Tt(i,t,e){return i?Go(t,e):Zo()}function qs(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function Gs(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}/*!
 * Chart.js v4.4.8
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Qo{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=zs.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var st=new Qo;const Ii="transparent",Jo={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Pi(i||Ii),n=s.valid&&Pi(t||Ii);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class tr{constructor(t,e,s,n){const o=e[s];n=le([t.to,n,o,t.from]);const r=le([t.from,o,n]);this._active=!0,this._fn=t.fn||Jo[t.type||typeof r],this._easing=Ut[t.easing]||Ut.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=le([t.to,e,n,t.from]),this._from=le([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class Zs{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!D(t))return;const e=Object.keys(z.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!D(o))return;const r={};for(const a of e)r[a]=o[a];(E(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=ir(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&er(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const f=s.get(c);if(d)if(f&&d.active()){d.update(f,h,a);continue}else d.cancel();if(!f||!f.duration){t[c]=h;continue}o[c]=d=new tr(f,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return st.add(this._chart,s),!0}}function er(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function ir(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function zi(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function sr(i,t,e){if(e===!1)return!1;const s=zi(i,e),n=zi(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function nr(i){let t,e,s,n;return D(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Qs(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Ei(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],X(c)&&(o||t===0||dt(t)===dt(c))&&(t+=c)}return!h&&!s.all?0:t}function or(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function Ie(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function rr(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function ar(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function lr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Bi(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Hi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=rr(o,r,s),d=t.length;let f;for(let u=0;u<d;++u){const m=t[u],{[l]:g,[c]:p}=m,b=m._stacks||(m._stacks={});f=b[c]=lr(n,h,g),f[a]=p,f._top=Bi(f,r,!0,s.type),f._bottom=Bi(f,r,!1,s.type);const x=f._visualValues||(f._visualValues={});x[a]=p}}function ze(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function cr(i,t){return Ft(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function hr(i,t,e){return Ft(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Et(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Ee=i=>i==="reset"||i==="none",Wi=(i,t)=>t?i:Object.assign({},i),dr=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Qs(e,!0),values:null};class Xt{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ie(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Et(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,f,u,m)=>d==="x"?f:d==="r"?m:u,o=e.xAxisID=A(s.xAxisID,ze(t,"x")),r=e.yAxisID=A(s.yAxisID,ze(t,"y")),a=e.rAxisID=A(s.rAxisID,ze(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&wi(this._data,this),t._stacked&&Et(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(D(e)){const n=this._cachedMeta;this._data=or(e,n)}else if(s!==e){if(s){wi(s,this);const n=this._cachedMeta;Et(n),n._parsed=[]}e&&Object.isExtensible(e)&&uo(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Ie(e.vScale,e),e.stack!==s.stack&&(n=!0,Et(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(Hi(this,e._parsed),e._stacked=Ie(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,f;if(this._parsing===!1)s._parsed=n,s._sorted=!0,f=n;else{E(n[t])?f=this.parseArrayData(s,n,t,e):D(n[t])?f=this.parseObjectData(s,n,t,e):f=this.parsePrimitiveData(s,n,t,e);const u=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=f[h],l&&(u()&&(l=!1),c=d);s._sorted=l}r&&Hi(this,f)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let f,u,m;for(f=0,u=n;f<u;++f)m=f+s,d[f]={[a]:h||o.parse(c[m],m),[l]:r.parse(e[m],m)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,f,u;for(h=0,d=n;h<d;++h)f=h+s,u=e[f],c[h]={x:o.parse(At(u,a),f),y:r.parse(At(u,l),f)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Qs(n,!0),values:e._stacks[t.axis]._visualValues};return Ei(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Ei(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=dr(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=ar(a);let f,u;function m(){u=n[f];const g=u[a.axis];return!X(u[t.axis])||h>g||d<g}for(f=0;f<r&&!(!m()&&(this.updateRangeFromParsed(c,t,u,l),o));++f);if(o){for(f=r-1;f>=0;--f)if(!m()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],X(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=nr(A(this.options.clip,sr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=hr(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=cr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Zt(s);if(a)return Wi(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],f=c.getOptionScopes(this.getDataset(),h),u=Object.keys(z.elements[t]),m=()=>this.getContext(s,n,e),g=c.resolveNamedOptions(f,u,m,d);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(Wi(g,l))),g}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),f=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(f,this.getContext(t,s,e))}const c=new Zs(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Ee(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Ee(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Ee(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Et(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}O(Xt,"defaults",{}),O(Xt,"datasetElementType",null),O(Xt,"dataElementType",null);function fr(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Is(s.sort((n,o)=>n-o))}return i._cache.$bar}function ur(i){const t=i.iScale,e=fr(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(Zt(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function gr(i,t,e,s){const n=e.barThickness;let o,r;return F(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function pr(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function mr(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function Js(i,t,e,s){return E(i)?mr(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Vi(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,f;for(c=e,h=e+s;c<h;++c)f=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(Js(f,d,o,c));return l}function Be(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function br(i,t,e){return i!==0?dt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function xr(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function _r(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=xr(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Ni(h,r,a,l)]=!0,n=c)),o[Ni(n,r,a,l)]=!0,i.borderSkipped=o}function Ni(i,t,e,s){return s?(i=yr(i,t,e),i=ji(i,e,t)):i=ji(i,t,e),i}function yr(i,t,e){return i===t?e:i===e?t:i}function ji(i,t,e){return i==="start"?t:i==="end"?e:i}function vr(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class xe extends Xt{parsePrimitiveData(t,e,s,n){return Vi(t,e,s,n)}parseArrayData(t,e,s,n){return Vi(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let f,u,m,g;for(f=s,u=s+n;f<u;++f)g=e[f],m={},m[o.axis]=o.parse(At(g,c),f),d.push(Js(At(g,h),m,r,f));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=Be(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:f}=this._getSharedOptions(e,n);for(let u=e;u<e+s;u++){const m=this.getParsed(u),g=o||F(m[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(u),p=this._calculateBarIndexPixels(u,h),b=(m._stacks||{})[a.axis],x={horizontal:c,base:g.base,enableBorderRadius:!b||Be(m._custom)||r===b._top||r===b._bottom,x:c?g.head:p.center,y:c?p.center:g.head,height:c?p.size:Math.abs(g.size),width:c?Math.abs(g.size):p.size};f&&(x.options=d||this.resolveDataElementOptions(u,t[u].active?"active":n));const _=x.options||t[u].options;_r(x,_,b,r),vr(x,_,h.ratio),this.updateElement(t[u],u,x,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[s.axis],c=h=>{const d=h._parsed.find(u=>u[s.axis]===l),f=d&&d[h.vScale.axis];if(F(f)||isNaN(f))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||ur(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=Be(c);let d=l[e.axis],f=0,u=s?this.applyStack(e,l,s):d,m,g;u!==d&&(f=u-d,u=d),h&&(d=c.barStart,u=c.barEnd-c.barStart,d!==0&&dt(d)!==dt(c.barEnd)&&(f=0),f+=d);const p=!F(o)&&!h?o:f;let b=e.getPixelForValue(p);if(this.chart.getDataVisibility(t)?m=e.getPixelForValue(f+u):m=b,g=m-b,Math.abs(g)<r){g=br(g,e,a)*r,d===a&&(b-=g/2);const x=e.getPixelForDecimal(0),_=e.getPixelForDecimal(1),v=Math.min(x,_),y=Math.max(x,_);b=Math.max(Math.min(b,y),v),m=b+g,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(m)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const x=dt(g)*e.getLineWidthForValue(a)/2;b+=x,g-=x}return{size:g,base:b,head:m,center:m+g/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=A(n.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?pr(t,e,n,c):gr(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}O(xe,"id","bar"),O(xe,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),O(xe,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function _t(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class fi{constructor(t){O(this,"options");this.options=t||{}}static override(t){Object.assign(fi.prototype,t)}init(){}formats(){return _t()}parse(){return _t()}format(){return _t()}add(){return _t()}diff(){return _t()}startOf(){return _t()}endOf(){return _t()}}var kr={_date:fi};function wr(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?ho:Xe;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const f=c(o,t,e-d),u=c(o,t,e+d);return{lo:f.lo,hi:u.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:f}=i,u=f.slice(0,h.lo+1).reverse().findIndex(g=>!F(g[d.axis]));h.lo-=Math.max(0,u);const m=f.slice(h.hi).findIndex(g=>!F(g[d.axis]));h.hi+=Math.max(0,m)}return h}}return{lo:0,hi:o.length-1}}function Te(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:f}=wr(o[a],t,r,n);for(let u=d;u<=f;++u){const m=h[u];m.skip||s(m,c,u)}}}function Mr(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function He(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||Te(i,e,t,function(a,l,c){!n&&!Ns(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Sr(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=ro(r,{x:t.x,y:t.y});lo(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Te(i,e,t,o),n}function Pr(i,t,e,s,n,o){let r=[];const a=Mr(e);let l=Number.POSITIVE_INFINITY;function c(h,d,f){const u=h.inRange(t.x,t.y,n);if(s&&!u)return;const m=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(m))&&!u)return;const p=a(t,m);p<l?(r=[{element:h,datasetIndex:d,index:f}],l=p):p===l&&r.push({element:h,datasetIndex:d,index:f})}return Te(i,e,t,c),r}function We(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?Sr(i,t,e,n):Pr(i,t,e,s,n,o)}function $i(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return Te(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var Or={modes:{index(i,t,e,s){const n=yt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?He(i,n,o,s,r):We(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=yt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?He(i,n,o,s,r):We(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=yt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return He(i,n,o,s,r)},nearest(i,t,e,s){const n=yt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return We(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=yt(t,i);return $i(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=yt(t,i);return $i(i,n,"y",e.intersect,s)}}};const tn=["left","top","right","bottom"];function Bt(i,t){return i.filter(e=>e.pos===t)}function Yi(i,t){return i.filter(e=>tn.indexOf(e.pos)===-1&&e.box.axis===t)}function Ht(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Dr(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function Cr(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!tn.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Tr(i,t){const e=Cr(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function Ar(i){const t=Dr(i),e=Ht(t.filter(c=>c.box.fullSize),!0),s=Ht(Bt(t,"left"),!0),n=Ht(Bt(t,"right")),o=Ht(Bt(t,"top"),!0),r=Ht(Bt(t,"bottom")),a=Yi(t,"x"),l=Yi(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Bt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Ui(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function en(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Lr(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!D(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&en(r,o.getPadding());const a=Math.max(0,t.outerWidth-Ui(r,i,"left","right")),l=Math.max(0,t.outerHeight-Ui(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function Fr(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Rr(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function jt(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Rr(a.horizontal,t));const{same:d,other:f}=Lr(t,e,a,s);c|=d&&n.length,h=h||f,l.fullSize||n.push(a)}return c&&jt(n,t,e,s)||h}function he(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Ki(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,f=c.size||l.height;Zt(c.start)&&(r=c.start),l.fullSize?he(l,n.left,r,e.outerWidth-n.right-n.left,f):he(l,t.left+c.placed,r,d,f),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,f=c.size||l.width;Zt(c.start)&&(o=c.start),l.fullSize?he(l,o,n.top,f,e.outerHeight-n.bottom-n.top):he(l,o,t.top+c.placed,f,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var K={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=q(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Ar(i.boxes),l=a.vertical,c=a.horizontal;T(i.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const h=l.reduce((g,p)=>p.box.options&&p.box.options.display===!1?g:g+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),f=Object.assign({},n);en(f,q(s));const u=Object.assign({maxPadding:f,w:o,h:r,x:n.left,y:n.top},n),m=Tr(l.concat(c),d);jt(a.fullSize,u,d,m),jt(l,u,d,m),jt(c,u,d,m)&&jt(l,u,d,m),Fr(u),Ki(a.leftAndTop,u,d,m),u.x+=u.w,u.y+=u.h,Ki(a.rightAndBottom,u,d,m),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},T(a.chartArea,g=>{const p=g.box;Object.assign(p,i.chartArea),p.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class sn{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Ir extends sn{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const _e="$chartjs",zr={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Xi=i=>i===null||i==="";function Er(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[_e]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Xi(n)){const o=Ri(i,"width");o!==void 0&&(i.width=o)}if(Xi(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Ri(i,"height");o!==void 0&&(i.height=o)}return i}const nn=qo?{passive:!0}:!1;function Br(i,t,e){i&&i.addEventListener(t,e,nn)}function Hr(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,nn)}function Wr(i,t){const e=zr[i.type]||i.type,{x:s,y:n}=yt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Oe(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Vr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Oe(a.addedNodes,s),r=r&&!Oe(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Nr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Oe(a.removedNodes,s),r=r&&!Oe(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Jt=new Map;let qi=0;function on(){const i=window.devicePixelRatio;i!==qi&&(qi=i,Jt.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function jr(i,t){Jt.size||window.addEventListener("resize",on),Jt.set(i,t)}function $r(i){Jt.delete(i),Jt.size||window.removeEventListener("resize",on)}function Yr(i,t,e){const s=i.canvas,n=s&&di(s);if(!n)return;const o=Es((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),jr(i,o),r}function Ve(i,t,e){e&&e.disconnect(),t==="resize"&&$r(i)}function Ur(i,t,e){const s=i.canvas,n=Es(o=>{i.ctx!==null&&e(Wr(o,i))},i);return Br(s,t,n),n}class Kr extends sn{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Er(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[_e])return!1;const s=e[_e].initial;["height","width"].forEach(o=>{const r=s[o];F(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[_e],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Vr,detach:Nr,resize:Yr}[e]||Ur;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Ve,detach:Ve,resize:Ve}[e]||Hr)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Xo(t,e,s,n)}isAttached(t){const e=t&&di(t);return!!(e&&e.isConnected)}}function Xr(i){return!hi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Ir:Kr}class ut{constructor(){O(this,"x");O(this,"y");O(this,"active",!1);O(this,"options");O(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Me(this.x)&&Me(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}O(ut,"defaults",{}),O(ut,"defaultRoutes");function qr(i,t){const e=i.options.ticks,s=Gr(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?Qr(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return Jr(t,c,o,r/n),c;const h=Zr(o,t,n);if(r>0){let d,f;const u=r>1?Math.round((l-a)/(r-1)):null;for(de(t,c,h,F(u)?0:a-u,a),d=0,f=r-1;d<f;d++)de(t,c,h,o[d],o[d+1]);return de(t,c,h,l,F(u)?t.length:l+u),c}return de(t,c,h),c}function Gr(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Zr(i,t,e){const s=ta(i),n=t.length/e;if(!s)return Math.max(n,1);const o=eo(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function Qr(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function Jr(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function de(i,t,e,s,n){const o=A(s,0),r=Math.min(A(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function ta(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const ea=i=>i==="left"?"right":i==="right"?"left":i,Gi=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Zi=(i,t)=>Math.min(t||i,i);function Qi(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function ia(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function sa(i,t){T(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Wt(i){return i.drawTicks?i.tickLength:0}function Ji(i,t){if(!i.display)return 0;const e=W(i.font,t),s=q(i.padding);return(E(i.text)?i.text.length:1)*e.lineHeight+s.height}function na(i,t){return Ft(i,{scale:t,type:"scale"})}function oa(i,t,e){return Ft(i,{tick:e,index:t,type:"tick"})}function ra(i,t,e){let s=ni(i);return(e&&t!=="right"||!e&&t==="right")&&(s=ea(s)),s}function aa(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,f,u,m;const g=r-n,p=a-o;if(i.isHorizontal()){if(u=H(s,o,a),D(e)){const b=Object.keys(e)[0],x=e[b];m=h[b].getPixelForValue(x)+g-t}else e==="center"?m=(c.bottom+c.top)/2+g-t:m=Gi(i,e,t);f=a-o}else{if(D(e)){const b=Object.keys(e)[0],x=e[b];u=h[b].getPixelForValue(x)-p+t}else e==="center"?u=(c.left+c.right)/2-p+t:u=Gi(i,e,t);m=H(s,r,n),d=e==="left"?-G:G}return{titleX:u,titleY:m,maxWidth:f,rotation:d}}class Rt extends ut{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=J(t,Number.POSITIVE_INFINITY),e=J(e,Number.NEGATIVE_INFINITY),s=J(s,Number.POSITIVE_INFINITY),n=J(n,Number.NEGATIVE_INFINITY),{min:J(t,s),max:J(e,n),minDefined:X(t),maxDefined:X(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:J(e,J(s,e)),max:J(s,J(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){R(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Fo(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Qi(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=qr(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){R(this.options.afterUpdate,[this])}beforeSetDimensions(){R(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){R(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),R(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){R(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=R(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){R(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){R(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Zi(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,f=h.highest.height,u=Z(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1),d+6>a&&(a=u/(s-(t.offset?.5:1)),l=this.maxHeight-Wt(t.grid)-e.padding-Ji(t.title,this.chart.options.font),c=Math.sqrt(d*d+f*f),r=oo(Math.min(Math.asin(Z((h.highest.height+6)/a,-1,1)),Math.asin(Z(l/c,-1,1))-Math.asin(Z(f/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){R(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){R(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Ji(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Wt(o)+l):(t.height=this.maxHeight,t.width=Wt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,m=vt(this.labelRotation),g=Math.cos(m),p=Math.sin(m);if(a){const b=s.mirror?0:p*d.width+g*f.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:g*d.width+p*f.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(c,h,p,g)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,u=0;l?c?(f=n*t.width,u=s*e.height):(f=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?f=t.width:o!=="inner"&&(f=t.width/2,u=e.width/2),this.paddingLeft=Math.max((f-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){R(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)F(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=Qi(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Zi(e,s));let c=0,h=0,d,f,u,m,g,p,b,x,_,v,y;for(d=0;d<e;d+=l){if(m=t[d].label,g=this._resolveTickFontOptions(d),n.font=p=g.string,b=o[p]=o[p]||{data:{},gc:[]},x=g.lineHeight,_=v=0,!F(m)&&!E(m))_=Di(n,b.data,b.gc,_,m),v=x;else if(E(m))for(f=0,u=m.length;f<u;++f)y=m[f],!F(y)&&!E(y)&&(_=Di(n,b.data,b.gc,_,y),v+=x);r.push(_),a.push(v),c=Math.max(_,c),h=Math.max(v,h)}sa(o,e);const k=r.indexOf(c),M=a.indexOf(h),w=S=>({width:r[S]||0,height:a[S]||0});return{first:w(0),last:w(e-1),widest:w(k),highest:w(M),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return co(this._alignToPixels?xt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=oa(this.getContext(),t,s))}return this.$context||(this.$context=na(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=vt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=Wt(o),u=[],m=a.setContext(this.getContext()),g=m.display?m.width:0,p=g/2,b=function(B){return xt(s,B,g)};let x,_,v,y,k,M,w,S,L,P,C,N;if(r==="top")x=b(this.bottom),M=this.bottom-f,S=x-p,P=b(t.top)+p,N=t.bottom;else if(r==="bottom")x=b(this.top),P=t.top,N=b(t.bottom)-p,M=x+p,S=this.top+f;else if(r==="left")x=b(this.right),k=this.right-f,w=x-p,L=b(t.left)+p,C=t.right;else if(r==="right")x=b(this.left),L=t.left,C=b(t.right)-p,k=x+p,w=this.left+f;else if(e==="x"){if(r==="center")x=b((t.top+t.bottom)/2+.5);else if(D(r)){const B=Object.keys(r)[0],Y=r[B];x=b(this.chart.scales[B].getPixelForValue(Y))}P=t.top,N=t.bottom,M=x+p,S=M+f}else if(e==="y"){if(r==="center")x=b((t.left+t.right)/2);else if(D(r)){const B=Object.keys(r)[0],Y=r[B];x=b(this.chart.scales[B].getPixelForValue(Y))}k=x-p,w=k-f,L=t.left,C=t.right}const Q=A(n.ticks.maxTicksLimit,d),I=Math.max(1,Math.ceil(d/Q));for(_=0;_<d;_+=I){const B=this.getContext(_),Y=o.setContext(B),ee=a.setContext(B),ie=Y.lineWidth,Mt=Y.color,se=ee.dash||[],St=ee.dashOffset,It=Y.tickWidth,gt=Y.tickColor,zt=Y.tickBorderDash||[],pt=Y.tickBorderDashOffset;v=ia(this,_,l),v!==void 0&&(y=xt(s,v,ie),c?k=w=L=C=y:M=S=P=N=y,u.push({tx1:k,ty1:M,tx2:w,ty2:S,x1:L,y1:P,x2:C,y2:N,width:ie,color:Mt,borderDash:se,borderDashOffset:St,tickWidth:It,tickColor:gt,tickBorderDash:zt,tickBorderDashOffset:pt}))}return this._ticksLength=d,this._borderValue=x,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,f=Wt(s.grid),u=f+h,m=d?-h:u,g=-vt(this.labelRotation),p=[];let b,x,_,v,y,k,M,w,S,L,P,C,N="middle";if(n==="top")k=this.bottom-m,M=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+m,M=this._getXAxisLabelAlignment();else if(n==="left"){const I=this._getYAxisLabelAlignment(f);M=I.textAlign,y=I.x}else if(n==="right"){const I=this._getYAxisLabelAlignment(f);M=I.textAlign,y=I.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+u;else if(D(n)){const I=Object.keys(n)[0],B=n[I];k=this.chart.scales[I].getPixelForValue(B)+u}M=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")y=(t.left+t.right)/2-u;else if(D(n)){const I=Object.keys(n)[0],B=n[I];y=this.chart.scales[I].getPixelForValue(B)}M=this._getYAxisLabelAlignment(f).textAlign}e==="y"&&(l==="start"?N="top":l==="end"&&(N="bottom"));const Q=this._getLabelSizes();for(b=0,x=a.length;b<x;++b){_=a[b],v=_.label;const I=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,S=this._resolveTickFontOptions(b),L=S.lineHeight,P=E(v)?v.length:1;const B=P/2,Y=I.color,ee=I.textStrokeColor,ie=I.textStrokeWidth;let Mt=M;r?(y=w,M==="inner"&&(b===x-1?Mt=this.options.reverse?"left":"right":b===0?Mt=this.options.reverse?"right":"left":Mt="center"),n==="top"?c==="near"||g!==0?C=-P*L+L/2:c==="center"?C=-Q.highest.height/2-B*L+L:C=-Q.highest.height+L/2:c==="near"||g!==0?C=L/2:c==="center"?C=Q.highest.height/2-B*L:C=Q.highest.height-P*L,d&&(C*=-1),g!==0&&!I.showLabelBackdrop&&(y+=L/2*Math.sin(g))):(k=w,C=(1-P)*L/2);let se;if(I.showLabelBackdrop){const St=q(I.backdropPadding),It=Q.heights[b],gt=Q.widths[b];let zt=C-St.top,pt=0-St.left;switch(N){case"middle":zt-=It/2;break;case"bottom":zt-=It;break}switch(M){case"center":pt-=gt/2;break;case"right":pt-=gt;break;case"inner":b===x-1?pt-=gt:b>0&&(pt-=gt/2);break}se={left:pt,top:zt,width:gt+St.width,height:It+St.height,color:I.backdropColor}}p.push({label:v,font:S,textOffset:C,options:{rotation:g,color:Y,strokeColor:ee,strokeWidth:ie,textAlign:Mt,textBaseline:N,translation:[y,k],backdrop:se}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-vt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,f;this.isHorizontal()?(c=xt(t,this.left,r)-r/2,h=xt(t,this.right,a)+a/2,d=f=l):(d=xt(t,this.top,r)-r/2,f=xt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&oi(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Qt(s,c,0,h,l,a)}n&&ri(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=W(s.font),r=q(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||D(e)?(l+=r.bottom,E(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:f}=aa(this,l,e,a);Qt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:ra(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=A(t.grid&&t.grid.z,-1),n=A(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Rt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return W(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class fe{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;ha(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,la(t,r,s),this.override&&z.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in z[n]&&(delete z[n][s],this.override&&delete wt[s])}}function la(i,t,e){const s=Gt(Object.create(null),[e?z.get(e):{},z.get(t),i.defaults]);z.set(t,s),i.defaultRoutes&&ca(t,i.defaultRoutes),i.descriptors&&z.describe(t,i.descriptors)}function ca(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");z.route(o,n,l,a)})}function ha(i){return"id"in i&&"defaults"in i}class da{constructor(){this.controllers=new fe(Xt,"datasets",!0),this.elements=new fe(ut,"elements"),this.plugins=new fe(Object,"plugins"),this.scales=new fe(Rt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):T(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=ii(t);R(s["before"+n],[],s),e[t](s),R(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var et=new da;class fa{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(R(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){F(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=A(s.options&&s.options.plugins,{}),o=ua(s);return n===!1&&!e?[]:pa(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function ua(i){const t={},e=[],s=Object.keys(et.plugins.items);for(let o=0;o<s.length;o++)e.push(et.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function ga(i,t){return!t&&i===!1?null:i===!0?{}:i}function pa(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=ga(s[l],n);c!==null&&o.push({plugin:a,options:ma(i.config,{plugin:a,local:e[l]},c,r)})}return o}function ma(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ge(i,t){const e=z.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function ba(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function xa(i,t){return i===t?"_index_":"_value_"}function ts(i){if(i==="x"||i==="y"||i==="r")return i}function _a(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Ze(i,...t){if(ts(i))return i;for(const e of t){const s=e.axis||_a(e.position)||i.length>1&&ts(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function es(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function ya(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return es(i,"x",e[0])||es(i,"y",e[0])}return{}}function va(i,t){const e=wt[i.type]||{scales:{}},s=t.scales||{},n=Ge(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!D(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Ze(r,a,ya(r,i),z.scales[a.type]),c=xa(l,n),h=e.scales||{};o[r]=Yt(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||Ge(a,t),h=(wt[a]||{}).scales||{};Object.keys(h).forEach(d=>{const f=ba(d,l),u=r[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),Yt(o[u],[{axis:f},s[u],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];Yt(a,[z.scales[a.type],z.scale])}),o}function rn(i){const t=i.options||(i.options={});t.plugins=A(t.plugins,{}),t.scales=va(i,t)}function an(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function ka(i){return i=i||{},i.data=an(i.data),rn(i),i}const is=new Map,ln=new Set;function ue(i,t){let e=is.get(i);return e||(e=t(),is.set(i,e),ln.add(e)),e}const Vt=(i,t,e)=>{const s=At(t,e);s!==void 0&&i.add(s)};class wa{constructor(t){this._config=ka(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=an(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),rn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return ue(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return ue(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return ue(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return ue(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>Vt(l,t,d))),h.forEach(d=>Vt(l,n,d)),h.forEach(d=>Vt(l,wt[o]||{},d)),h.forEach(d=>Vt(l,z,d)),h.forEach(d=>Vt(l,qe,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),ln.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,wt[e]||{},z.datasets[e]||{},{type:e},z,qe]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ss(this._resolverCache,t,n);let l=r;if(Sa(r,e)){o.$shared=!1,s=ft(s)?s():s;const c=this.createResolver(t,s,a);l=Lt(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=ss(this._resolverCache,t,s);return D(e)?Lt(o,e,void 0,n):o}}function ss(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:ai(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Ma=i=>D(i)&&Object.getOwnPropertyNames(i).some(t=>ft(i[t]));function Sa(i,t){const{isScriptable:e,isIndexable:s}=Ys(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(ft(a)||Ma(a))||r&&E(a))return!0}return!1}var Pa="4.4.8";const Oa=["top","bottom","left","right","chartArea"];function ns(i,t){return i==="top"||i==="bottom"||Oa.indexOf(i)===-1&&t==="x"}function os(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function rs(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),R(e&&e.onComplete,[i],t)}function Da(i){const t=i.chart,e=t.options.animation;R(e&&e.onProgress,[i],t)}function cn(i){return hi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ye={},as=i=>{const t=cn(i);return Object.values(ye).filter(e=>e.canvas===t).pop()};function Ca(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Ta(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function ge(i,t,e){return i.options.clip?i[e]:t[e]}function Aa(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:ge(e,t,"left"),right:ge(e,t,"right"),top:ge(s,t,"top"),bottom:ge(s,t,"bottom")}:t}var rt;let ui=(rt=class{static register(...t){et.add(...t),ls()}static unregister(...t){et.remove(...t),ls()}constructor(t,e){const s=this.config=new wa(e),n=cn(t),o=as(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Xr(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Kn(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new fa,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=go(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],ye[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}st.listen(this,"complete",rs),st.listen(this,"progress",Da),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return F(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return et}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Fi(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ci(this.canvas,this.ctx),this}stop(){return st.stop(this),this}resize(t,e){st.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Fi(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),R(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};T(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=Ze(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),T(o,r=>{const a=r.options,l=a.id,c=Ze(l,a),h=A(a.type,r.dtype);(a.position===void 0||ns(a.position,c)!==ns(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const f=et.getScale(h);d=new f({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),T(n,(r,a)=>{r||delete s[a]}),T(s,r=>{K.configure(this,r,r.options),K.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(os("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||Ge(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=et.getController(a),{datasetElementType:c,dataElementType:h}=z.datasets[a];Object.assign(l,{dataElementType:et.getElement(h),datasetElementType:c&&et.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){T(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),f=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(f),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||T(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(os("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){T(this.scales,t=>{K.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!_i(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;Ca(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!_i(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;K.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],T(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,ft(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(st.has(this)?this.attached&&!st.running(this)&&st.start(this):(this.draw(),rs({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=Aa(t,this.chartArea),r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(n&&oi(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&ri(e),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return Ns(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=Or.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Ft(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Zt(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),st.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ci(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ye[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};T(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){T(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},T(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!ve(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=Jn(t),c=Ta(t,this._lastEvent,s,l);s&&(this._lastEvent=null,R(o.onHover,[t,a,this],this),l&&R(o.onClick,[t,a,this],this));const h=!ve(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}},O(rt,"defaults",z),O(rt,"instances",ye),O(rt,"overrides",wt),O(rt,"registry",et),O(rt,"version",Pa),O(rt,"getChart",as),rt);function ls(){return T(ui.instances,i=>i._plugins.invalidate())}function hn(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function lt(i,t,e,s){return i?0:Z(t,e,s)}function La(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=$s(s);return{t:lt(n.top,o.top,0,e),r:lt(n.right,o.right,0,t),b:lt(n.bottom,o.bottom,0,e),l:lt(n.left,o.left,0,t)}}function Fa(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Ct(n),r=Math.min(t,e),a=i.borderSkipped,l=s||D(n);return{topLeft:lt(!l||a.top||a.left,o.topLeft,0,r),topRight:lt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:lt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:lt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Ra(i){const t=hn(i),e=t.right-t.left,s=t.bottom-t.top,n=La(i,e/2,s/2),o=Fa(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Ne(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&hn(i,s);return a&&(n||Dt(t,a.left,a.right))&&(o||Dt(e,a.top,a.bottom))}function Ia(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function za(i,t){i.rect(t.x,t.y,t.w,t.h)}function je(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class $e extends ut{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=Ra(this),a=Ia(r.radius)?Se:za;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,je(r,e,o)),t.clip(),a(t,je(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,je(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return Ne(this,t,e,s)}inXRange(t,e){return Ne(this,t,null,e)}inYRange(t,e){return Ne(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}O($e,"id","bar"),O($e,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),O($e,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const cs=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},Ea=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class hs extends ut{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=R(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=W(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=cs(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-h;return this.legendItems.forEach((m,g)=>{const p=s+e/2+o.measureText(m.text).width;(g===0||c[c.length-1]+p+2*a>r)&&(d+=h,c[c.length-(g>0?0:1)]=0,u+=h,f++),l[g]={left:0,top:u,row:f,width:p,height:n},c[c.length-1]+=p+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,f=0,u=0,m=0,g=0;return this.legendItems.forEach((p,b)=>{const{itemWidth:x,itemHeight:_}=Ba(s,e,o,p,n);b>0&&u+_+2*a>h&&(d+=f+a,c.push({width:f,height:u}),m+=f+a,g++,f=u=0),l[b]={left:m,top:u,col:g,width:x,height:_},f=Math.max(f,x),u+=_+a}),d+=f,c.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Tt(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=H(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=H(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=H(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=H(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;oi(t,this),this._draw(),ri(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=z.color,l=Tt(t.rtl,this.left,this.width),c=W(r.font),{padding:h}=r,d=c.size,f=d/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:m,boxHeight:g,itemHeight:p}=cs(r,d),b=function(k,M,w){if(isNaN(m)||m<=0||isNaN(g)||g<0)return;n.save();const S=A(w.lineWidth,1);if(n.fillStyle=A(w.fillStyle,a),n.lineCap=A(w.lineCap,"butt"),n.lineDashOffset=A(w.lineDashOffset,0),n.lineJoin=A(w.lineJoin,"miter"),n.lineWidth=S,n.strokeStyle=A(w.strokeStyle,a),n.setLineDash(A(w.lineDash,[])),r.usePointStyle){const L={radius:g*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:S},P=l.xPlus(k,m/2),C=M+f;Vs(n,L,P,C,r.pointStyleWidth&&m)}else{const L=M+Math.max((d-g)/2,0),P=l.leftForLtr(k,m),C=Ct(w.borderRadius);n.beginPath(),Object.values(C).some(N=>N!==0)?Se(n,{x:P,y:L,w:m,h:g,radius:C}):n.rect(P,L,m,g),n.fill(),S!==0&&n.stroke()}n.restore()},x=function(k,M,w){Qt(n,w.text,k,M+p/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},_=this.isHorizontal(),v=this._computeTitleHeight();_?u={x:H(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:u={x:this.left+h,y:H(o,this.top+v+h,this.bottom-e[0].height),line:0},qs(this.ctx,t.textDirection);const y=p+h;this.legendItems.forEach((k,M)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;const w=n.measureText(k.text).width,S=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),L=m+f+w;let P=u.x,C=u.y;l.setWidth(this.width),_?M>0&&P+L+h>this.right&&(C=u.y+=y,u.line++,P=u.x=H(o,this.left+h,this.right-s[u.line])):M>0&&C+y>this.bottom&&(P=u.x=P+e[u.line].width+h,u.line++,C=u.y=H(o,this.top+v+h,this.bottom-e[u.line].height));const N=l.x(P);if(b(N,C,k),P=po(S,P+m+f,_?P+L:this.right,t.rtl),x(l.x(P),C,k),_)u.x+=L+h;else if(typeof k.text!="string"){const Q=c.lineHeight;u.y+=dn(k,Q)+h}else u.y+=y}),Gs(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=W(e.font),n=q(e.padding);if(!e.display)return;const o=Tt(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+c,d=H(t.align,d,this.right-f);else{const m=this.columnSizes.reduce((g,p)=>Math.max(g,p.height),0);h=c+H(t.align,this.top,this.bottom-m-t.labels.padding-this._computeTitleHeight())}const u=H(a,d,d+f);r.textAlign=o.textAlign(ni(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Qt(r,e.text,u,h,s)}_computeTitleHeight(){const t=this.options.title,e=W(t.font),s=q(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(Dt(t,this.left,this.right)&&Dt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],Dt(t,n.left,n.left+n.width)&&Dt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!Va(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=Ea(n,s);n&&!o&&R(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&R(e.onHover,[t,s,this],this)}else s&&R(e.onClick,[t,s,this],this)}}function Ba(i,t,e,s,n){const o=Ha(s,i,t,e),r=Wa(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Ha(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Wa(i,t,e){let s=i;return typeof t.text!="string"&&(s=dn(t,e)),s}function dn(i,t){const e=i.text?i.text.length:0;return t*e}function Va(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var pl={id:"legend",_element:hs,start(i,t,e){const s=i.legend=new hs({ctx:i.ctx,options:e,chart:i});K.configure(i,s,e),K.addBox(i,s)},stop(i){K.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;K.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=q(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class fn extends ut{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=E(s.text)?s.text.length:1;this._padding=q(s.padding);const o=n*W(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=H(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=H(a,n,e),l=V*-.5):(h=o-t,d=H(a,e,n),l=V*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=W(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Qt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:ni(e.align),textBaseline:"middle",translation:[r,a]})}}function Na(i,t){const e=new fn({ctx:i.ctx,options:t,chart:i});K.configure(i,e,t),K.addBox(i,e),i.titleBlock=e}var ml={id:"title",_element:fn,start(i,t,e){Na(i,e)},stop(i){const t=i.titleBlock;K.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;K.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const $t={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=ao(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function tt(i,t){return t&&(E(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function nt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function ja(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function ds(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=W(t.bodyFont),c=W(t.titleFont),h=W(t.footerFont),d=o.length,f=n.length,u=s.length,m=q(t.padding);let g=m.height,p=0,b=s.reduce((v,y)=>v+y.before.length+y.lines.length+y.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(g+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=u*v+(b-u)*l.lineHeight+(b-1)*t.bodySpacing}f&&(g+=t.footerMarginTop+f*h.lineHeight+(f-1)*t.footerSpacing);let x=0;const _=function(v){p=Math.max(p,e.measureText(v).width+x)};return e.save(),e.font=c.string,T(i.title,_),e.font=l.string,T(i.beforeBody.concat(i.afterBody),_),x=t.displayColors?r+2+t.boxPadding:0,T(s,v=>{T(v.before,_),T(v.lines,_),T(v.after,_)}),x=0,e.font=h.string,T(i.footer,_),e.restore(),p+=m.width,{width:p,height:g}}function $a(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Ya(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function Ua(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Ya(c,i,t,e)&&(c="center"),c}function fs(i,t,e){const s=e.yAlign||t.yAlign||$a(i,e);return{xAlign:e.xAlign||t.xAlign||Ua(i,t,e,s),yAlign:s}}function Ka(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function Xa(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function us(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:f,bottomRight:u}=Ct(r);let m=Ka(t,a);const g=Xa(t,l,c);return l==="center"?a==="left"?m+=c:a==="right"&&(m-=c):a==="left"?m-=Math.max(h,f)+n:a==="right"&&(m+=Math.max(d,u)+n),{x:Z(m,0,s.width-t.width),y:Z(g,0,s.height-t.height)}}function pe(i,t,e){const s=q(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function gs(i){return tt([],nt(i))}function qa(i,t,e){return Ft(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function ps(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const un={beforeTitle:it,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:it,beforeBody:it,beforeLabel:it,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return F(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:it,afterBody:it,beforeFooter:it,footer:it,afterFooter:it};function j(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?un[t].call(e,s):n}class Qe extends ut{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new Zs(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=qa(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=j(s,"beforeTitle",this,t),o=j(s,"title",this,t),r=j(s,"afterTitle",this,t);let a=[];return a=tt(a,nt(n)),a=tt(a,nt(o)),a=tt(a,nt(r)),a}getBeforeBody(t,e){return gs(j(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return T(t,o=>{const r={before:[],lines:[],after:[]},a=ps(s,o);tt(r.before,nt(j(a,"beforeLabel",this,o))),tt(r.lines,j(a,"label",this,o)),tt(r.after,nt(j(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return gs(j(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=j(s,"beforeFooter",this,t),o=j(s,"footer",this,t),r=j(s,"afterFooter",this,t);let a=[];return a=tt(a,nt(n)),a=tt(a,nt(o)),a=tt(a,nt(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(ja(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,f)=>t.filter(h,d,f,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),T(a,h=>{const d=ps(t.callbacks,h);n.push(j(d,"labelColor",this,h)),o.push(j(d,"labelPointStyle",this,h)),r.push(j(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=$t[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=ds(this,s),c=Object.assign({},a,l),h=fs(this.chart,s,c),d=us(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Ct(a),{x:f,y:u}=t,{width:m,height:g}=e;let p,b,x,_,v,y;return o==="center"?(v=u+g/2,n==="left"?(p=f,b=p-r,_=v+r,y=v-r):(p=f+m,b=p+r,_=v-r,y=v+r),x=p):(n==="left"?b=f+Math.max(l,h)+r:n==="right"?b=f+m-Math.max(c,d)-r:b=this.caretX,o==="top"?(_=u,v=_-r,p=b-r,x=b+r):(_=u+g,v=_+r,p=b+r,x=b-r),y=_),{x1:p,x2:b,x3:x,y1:_,y2:v,y3:y}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Tt(s.rtl,this.x,this.width);for(t.x=pe(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=W(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=W(o.bodyFont),d=pe(this,"left",o),f=n.x(d),u=l<h.lineHeight?(h.lineHeight-l)/2:0,m=e.y+u;if(o.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},p=n.leftForLtr(f,c)+c/2,b=m+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Ti(t,g,p,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,Ti(t,g,p,b)}else{t.lineWidth=D(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const g=n.leftForLtr(f,c),p=n.leftForLtr(n.xPlus(f,1),c-2),b=Ct(r.borderRadius);Object.values(b).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Se(t,{x:g,y:m,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Se(t,{x:p,y:m+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,m,c,l),t.strokeRect(g,m,c,l),t.fillStyle=r.backgroundColor,t.fillRect(p,m+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=W(s.bodyFont);let f=d.lineHeight,u=0;const m=Tt(s.rtl,this.x,this.width),g=function(w){e.fillText(w,m.x(t.x+u),t.y+f/2),t.y+=f+o},p=m.textAlign(r);let b,x,_,v,y,k,M;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=pe(this,p,s),e.fillStyle=s.bodyColor,T(this.beforeBody,g),u=a&&p!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,k=n.length;v<k;++v){for(b=n[v],x=this.labelTextColors[v],e.fillStyle=x,T(b.before,g),_=b.lines,a&&_.length&&(this._drawColorBox(e,t,v,m,s),f=Math.max(d.lineHeight,l)),y=0,M=_.length;y<M;++y)g(_[y]),f=d.lineHeight;T(b.after,g)}u=0,f=d.lineHeight,T(this.afterBody,g),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Tt(s.rtl,this.x,this.width);for(t.x=pe(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=W(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:f,bottomLeft:u,bottomRight:m}=Ct(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-f,l),e.quadraticCurveTo(a+c,l,a+c,l+f),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-m),e.quadraticCurveTo(a+c,l+h,a+c-m,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+u,l+h),e.quadraticCurveTo(a,l+h,a,l+h-u),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=$t[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=ds(this,t),l=Object.assign({},r,this._size),c=fs(e,t,l),h=us(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=q(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),qs(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),Gs(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!ve(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!ve(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=$t[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}O(Qe,"positioners",$t);var bl={id:"tooltip",_element:Qe,positioners:$t,afterInit(i,t,e){e&&(i.tooltip=new Qe({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:un},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const Ga=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function Za(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Ga(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Qa=(i,t)=>i===null?null:Z(Math.round(i),0,t);function ms(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class bs extends Rt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(F(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:Za(s,t,A(e,t),this._addedLabels),Qa(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return ms.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}O(bs,"id","category"),O(bs,"defaults",{ticks:{callback:ms}});function Ja(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:f}=i,u=o||1,m=h-1,{min:g,max:p}=t,b=!F(r),x=!F(a),_=!F(c),v=(p-g)/(d+1);let y=vi((p-g)/m/u)*u,k,M,w,S;if(y<1e-14&&!b&&!x)return[{value:g},{value:p}];S=Math.ceil(p/y)-Math.floor(g/y),S>m&&(y=vi(S*y/m/u)*u),F(l)||(k=Math.pow(10,l),y=Math.ceil(y*k)/k),n==="ticks"?(M=Math.floor(g/y)*y,w=Math.ceil(p/y)*y):(M=g,w=p),b&&x&&o&&so((a-r)/o,y/1e3)?(S=Math.round(Math.min((a-r)/y,h)),y=(a-r)/S,M=r,w=a):_?(M=b?r:M,w=x?a:w,S=c-1,y=(w-M)/S):(S=(w-M)/y,be(S,Math.round(S),y/1e3)?S=Math.round(S):S=Math.ceil(S));const L=Math.max(ki(y),ki(M));k=Math.pow(10,F(l)?L:l),M=Math.round(M*k)/k,w=Math.round(w*k)/k;let P=0;for(b&&(f&&M!==r?(e.push({value:r}),M<r&&P++,be(Math.round((M+P*y)*k)/k,r,xs(r,v,i))&&P++):M<r&&P++);P<S;++P){const C=Math.round((M+P*y)*k)/k;if(x&&C>a)break;e.push({value:C})}return x&&f&&w!==a?e.length&&be(e[e.length-1].value,a,xs(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!x||w===a)&&e.push({value:w}),e}function xs(i,t,{horizontal:e,minRotation:s}){const n=vt(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class tl extends Rt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return F(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=dt(n),c=dt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=Ja(n,o);return t.bounds==="ticks"&&no(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Hs(t,this.chart.options.locale,this.options.ticks.format)}}class _s extends tl{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=X(t)?t:0,this.max=X(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=vt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}O(_s,"id","linear"),O(_s,"defaults",{ticks:{callback:Ws.formatters.numeric}});const Ae={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},$=Object.keys(Ae);function ys(i,t){return i-t}function vs(i,t){if(F(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),X(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Me(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function ks(i,t,e,s){const n=$.length;for(let o=$.indexOf(i);o<n-1;++o){const r=Ae[$[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return $[o]}return $[n-1]}function el(i,t,e,s,n){for(let o=$.length-1;o>=$.indexOf(e);o--){const r=$[o];if(Ae[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return $[e?$.indexOf(e):0]}function il(i){for(let t=$.indexOf(i)+1,e=$.length;t<e;++t)if(Ae[$[t]].common)return $[t]}function ws(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=si(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function sl(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function Ms(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:sl(i,s,n,e)}class De extends Rt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new kr._date(t.adapters.date);n.init(e),Yt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:vs(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=X(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=X(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=fo(n,o,r);return this._unit=e.unit||(s.autoSkip?ks(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):el(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:il(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Ms(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=Z(e,0,r),s=Z(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||ks(o.minUnit,e,s,this._getLabelCapacity(e)),a=A(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Me(l)||l===!0,h={};let d=e,f,u;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const m=n.ticks.source==="data"&&this.getDataTimestamps();for(f=d,u=0;f<s;f=+t.add(f,a,r),u++)ws(h,f,m);return(f===s||n.bounds==="ticks"||u===1)&&ws(h,f,m),Object.keys(h).sort(ys).map(g=>+g)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return R(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],f=s[e],u=c&&d&&f&&f.major;return this._adapter.format(t,n||(u?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=vt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Ms(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(vs(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Is(t.sort(ys))}}O(De,"id","time"),O(De,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function me(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Xe(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Xe(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class Ss extends De{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=me(e,this.min),this._tableRange=me(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(me(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return me(this._table,s*this._tableRange+this._minPos,!0)}}O(Ss,"id","timeseries"),O(Ss,"defaults",De.defaults);const gn={data:{type:Object,required:!0},options:{type:Object,default:()=>({})},plugins:{type:Array,default:()=>[]},datasetIdKey:{type:String,default:"label"},updateMode:{type:String,default:void 0}},nl={ariaLabel:{type:String},ariaDescribedby:{type:String}},ol={type:{type:String,required:!0},destroyDelay:{type:Number,default:0},...gn,...nl},rl=_n[0]==="2"?(i,t)=>Object.assign(i,{attrs:t}):(i,t)=>Object.assign(i,t);function Ot(i){return Ds(i)?Ue(i):i}function al(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:i;return Ds(t)?new Proxy(i,{}):i}function ll(i,t){const e=i.options;e&&t&&Object.assign(e,t)}function pn(i,t){i.labels=t}function mn(i,t,e){const s=[];i.datasets=t.map(n=>{const o=i.datasets.find(r=>r[e]===n[e]);return!o||!n.data||s.includes(o)?{...n}:(s.push(o),Object.assign(o,n),o)})}function cl(i,t){const e={labels:[],datasets:[]};return pn(e,i.labels),mn(e,i.datasets,t),e}const hl=Ps({props:ol,setup(i,t){let{expose:e,slots:s}=t;const n=yn(null),o=Os(null);e({chart:o});const r=()=>{if(!n.value)return;const{type:c,data:h,options:d,plugins:f,datasetIdKey:u}=i,m=cl(h,u),g=al(m,h);o.value=new ui(n.value,{type:c,data:g,options:{...d},plugins:f})},a=()=>{const c=Ue(o.value);c&&(i.destroyDelay>0?setTimeout(()=>{c.destroy(),o.value=null},i.destroyDelay):(c.destroy(),o.value=null))},l=c=>{c.update(i.updateMode)};return vn(r),kn(a),wn([()=>i.options,()=>i.data],(c,h)=>{let[d,f]=c,[u,m]=h;const g=Ue(o.value);if(!g)return;let p=!1;if(d){const b=Ot(d),x=Ot(u);b&&b!==x&&(ll(g,b),p=!0)}if(f){const b=Ot(f.labels),x=Ot(m.labels),_=Ot(f.datasets),v=Ot(m.datasets);b!==x&&(pn(g.config.data,b),p=!0),_&&_!==v&&(mn(g.config.data,_,i.datasetIdKey),p=!0)}p&&Mn(()=>{l(g)})},{deep:!0}),()=>Ye("canvas",{role:"img",ariaLabel:i.ariaLabel,ariaDescribedby:i.ariaDescribedby,ref:n},[Ye("p",{},[s.default?s.default():""])])}});function dl(i,t){return ui.register(t),Ps({props:gn,setup(e,s){let{expose:n}=s;const o=Os(null),r=a=>{o.value=a==null?void 0:a.chart};return n({chart:o}),()=>Ye(hl,rl({ref:r},{type:i,...e}))}})}const xl=dl("bar",xe);export{$e as B,ui as C,_s as L,bs as a,bl as b,pl as c,xl as d,ml as p};
