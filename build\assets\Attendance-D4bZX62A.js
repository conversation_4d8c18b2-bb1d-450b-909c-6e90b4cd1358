import{u as V,i as x,m as R,l as j,n as z,r as o,a as d,o as e,q as i,b as h,e as p,w as n,f,d as v,F as g,v as _,s as b,t as r,x as m}from"./app-BAwPsakn.js";import{C as E,a as F,L as O,B as q,p as M,b as U,c as G,d as I}from"./index-D5FX_aO4.js";const J={class:"py-4 px-4 flex flex-wrap gap-2"},K=["title"],Q={name:"StudentShowAttendance"},Z=Object.assign(Q,{props:{student:{type:Object,default(){return{}}}},setup(c){E.register(F,O,q,M,U,G);const k=V(),B=x(),C=c,S="student/",u=R(!1),s=j({header:[],attendances:[],total:[],chartData:[]});let D=[];const A=async()=>{u.value=!0,await B.dispatch(S+"fetchAttendance",{uuid:C.student.uuid}).then(t=>{u.value=!1,s.header=t.header,s.attendances=t.rows,s.total=t.total,s.chartData=t.chartData}).catch(t=>{u.value=!1})};return z(async()=>{await A()}),(t,W)=>{const P=o("PageHeaderAction"),w=o("PageHeader"),T=o("BaseBadge"),$=o("DataCell"),H=o("DataRow"),L=o("SimpleTable"),y=o("BaseCard"),N=o("ParentTransition");return e(),d(g,null,[c.student.uuid?(e(),i(w,{key:0,title:t.$trans(f(k).meta.label),navs:[{label:t.$trans("student.student"),path:"Student"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}}]},{default:n(()=>[p(P,{"additional-actions":f(D)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):h("",!0),p(N,{appear:"",visibility:!0},{default:n(()=>[c.student.uuid?(e(),i(y,{key:0,"is-loading":u.value,"no-padding":"","no-content-padding":""},{title:n(()=>[b(r(t.$trans("global.overview",{attribute:t.$trans("student.attendance.attendance")})),1)]),default:n(()=>[v("div",J,[(e(!0),d(g,null,_(s.total,a=>(e(),i(T,{size:"lg",design:a.design,key:a.key},{default:n(()=>[b(r(a.description)+" ("+r(a.label)+") "+r(a.count),1)]),_:2},1032,["design"]))),128))]),p(L,{bordered:"",header:s.header},{default:n(()=>[(e(!0),d(g,null,_(s.attendances,a=>(e(),i(H,{key:a.key},{default:n(()=>[(e(!0),d(g,null,_(a.row,l=>(e(),i($,{bordered:"",key:l.key,align:"center","is-heading":a.type=="heading"||a.type=="footer","has-label":"",label:l.name},{default:n(()=>[l.icon?(e(),d("span",{key:0,class:m(l.color),title:l.label},[v("i",{class:m([l.icon,"fa-lg"])},null,2)],10,K)):(e(),d("span",{key:1,class:m({"font-bold":l.type=="footer"})},r(l.label),3))]),_:2},1032,["is-heading","label"]))),128))]),_:2},1024))),128))]),_:1},8,["header"])]),_:1},8,["is-loading"])):h("",!0),p(y,{"is-loading":u.value},{title:n(()=>[b(r(t.$trans("student.attendance.attendance_chart")),1)]),default:n(()=>[s.chartData.labels?(e(),i(f(I),{key:0,data:s.chartData,options:{responsive:!0}},null,8,["data"])):h("",!0)]),_:1},8,["is-loading"])]),_:1})],64)}}});export{Z as default};
