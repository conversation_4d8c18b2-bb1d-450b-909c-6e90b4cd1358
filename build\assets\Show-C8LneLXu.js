import{r as f,q as i,o as s,w as n,d as t,e as d,f as p,aa as C,t as a,ab as T,ac as A,a as _,F as L,v as M,ad as V,x as E,ae as F,u as U,h as q,i as z,j as O,m as D,l as I,K as G,n as K,p as Q,b as l,s as u}from"./app-BAwPsakn.js";const H={class:"flex w-full justify-between items-center"},J={class:"flex min-w-0 items-center justify-between space-x-3"},P={class:"flex-1 flex flex-col min-w-0"},W={class:"text-gray-400 text-sm font-medium truncate"},X={class:"py-1"},Y=["onClick"],Z={name:"StudentRecord"},ee=Object.assign(Z,{props:{records:{type:Array,default:[]}},emits:["updateRecord"],setup(x,{emit:m}){const g=m;return(h,b)=>{const v=f("EllipsisVerticalIcon");return s(),i(p(F),{as:"div",class:"px-3 relative inline-block text-left"},{default:n(()=>[t("div",null,[d(p(C),{class:"group w-full bg-black rounded-md px-3.5 py-2 text-sm text-left font-medium hover:bg-gray-900 focus:outline-none"},{default:n(()=>[t("span",H,[t("span",J,[t("span",P,[t("span",W,a(h.$trans("global.choose",{attribute:h.$trans("student.record.record")})),1)])]),d(v,{class:"flex-shrink-0 h-5 w-5 text-gray-400 group-hover:text-gray-500","aria-hidden":"true"})])]),_:1})]),d(T,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:n(()=>[d(p(A),{class:"z-10 mx-3 origin-top absolute right-0 left-0 mt-1 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-200 focus:outline-none"},{default:n(()=>[t("div",X,[(s(!0),_(L,null,M(x.records,c=>(s(),i(p(V),{key:c.uuid},{default:n(({active:e})=>[t("a",{href:"#",onClick:y=>g("updateRecord",c.uuid),class:E([e?"bg-gray-100 text-gray-900":"text-gray-700","block px-4 py-2 text-sm"])},a(c.periodName)+" "+a(c.courseName)+" "+a(c.batchName),11,Y)]),_:2},1024))),128))])]),_:1})]),_:1})]),_:1})}}}),te={key:0,class:"mb-4"},ae={class:"-mt-12 flex w-full justify-center sm:-mt-16"},ne=["src"],se={class:"mt-4 text-center text-white"},oe={class:"font-bold"},ce={key:0,class:"mt-2"},re={key:0,class:"mt-4 flex justify-center"},le={name:"StudentShow"},ue=Object.assign(le,{setup(x){const m=U(),g=q(),h=z(),b=O("emitter"),v=[{name:"StudentShowBasic",icon:"fas fa-chevron-right",label:"general.basic"},{name:"StudentShowContact",icon:"fas fa-chevron-right",label:"contact.contact"},{name:"StudentShowLogin",icon:"fas fa-chevron-right",label:"contact.login.login"},{name:"StudentGuardian",icon:"fas fa-chevron-right",label:"guardian.guardian"},{name:"StudentSibling",icon:"fas fa-chevron-right",label:"student.sibling.sibling"},{name:"StudentRecord",icon:"fas fa-chevron-right",label:"student.record.record"},{name:"StudentShowFee",icon:"fas fa-chevron-right",label:"student.fee.fee"},{name:"StudentFeeRefund",icon:"fas fa-chevron-right",label:"student.fee_refund.fee_refund"},{name:"StudentShowAttendance",icon:"fas fa-chevron-right",label:"student.attendance.attendance"},{name:"StudentShowExamReport",icon:"fas fa-chevron-right",label:"exam.exam_report"},{name:"StudentShowSubject",icon:"fas fa-chevron-right",label:"academic.subject.subject"},{name:"StudentDocument",icon:"fas fa-chevron-right",label:"student.document.document"},{name:"StudentQualification",icon:"fas fa-chevron-right",label:"student.qualification.qualification"},{name:"StudentAccount",icon:"fas fa-chevron-right",label:"student.account.account"}],c=D(!1),e=I({records:[]}),y=async()=>{c.value=!0,await h.dispatch("student/get",{uuid:m.params.uuid}).then(o=>{Object.assign(e,o),c.value=!1}).catch(o=>{c.value=!1,g.push({name:"StudentList"})})},$=async o=>{await g.push({params:{uuid:o}})};return G(()=>m.params.uuid,(o,r)=>{o!=null&&o!=r&&location.reload()},{deep:!0}),K(async()=>{b.on("studentUpdated",()=>{y()}),await y()}),Q(()=>{b.all.delete("studentUpdated")}),(o,r)=>{const S=f("TextMuted"),w=f("BaseBadge"),B=f("BaseLoader"),N=f("router-view"),R=f("ModuleConfig");return s(),i(R,{navigations:v,"use-uuid":""},{header:n(()=>[d(B,null,{default:n(()=>{var k;return[e.uuid?(s(),_("div",te,[r[2]||(r[2]=t("img",{class:"h-32 w-full object-cover lg:h-48",src:"/images/user-cover.jpeg",alt:""},null,-1)),t("div",ae,[t("img",{class:"h-24 w-24 rounded-full ring-4 ring-white sm:h-32 sm:w-32",src:e.contact.photo,alt:""},null,8,ne)]),t("div",se,[t("h1",oe,a(e.contact.name),1),t("p",null,[u(a(e.courseName)+" "+a(e.batchName)+" ",1),d(S,{black:"",block:""},{default:n(()=>[u(a(e.codeNumber),1)]),_:1}),(k=e.enrollmentType)!=null&&k.uuid?(s(),i(S,{key:0,black:"",block:""},{default:n(()=>{var j;return[u(a((j=e.enrollmentType)==null?void 0:j.name),1)]}),_:1})):l("",!0)]),t("p",null,[r[0]||(r[0]=t("i",{class:"fas fa-mobile-alt"},null,-1)),u(" "+a(e.contact.contactNumber),1)]),e.isAlumni||e.isTransferred||e.cancelledAt.value?(s(),_("div",ce,[e.isAlumni?(s(),i(w,{key:0,design:"info",size:"lg"},{default:n(()=>[r[1]||(r[1]=t("i",{class:"fas fa-graduation-cap fa-lg"},null,-1)),u(" "+a(o.$trans("student.alumni.alumni")),1)]),_:1})):l("",!0),e.isTransferred?(s(),i(w,{key:1,design:"danger"},{default:n(()=>[u(a(o.$trans("student.statuses.transferred")),1)]),_:1})):l("",!0),e.cancelledAt.value?(s(),i(w,{key:2,design:"danger"},{default:n(()=>[u(a(o.$trans("student.statuses.cancelled")),1)]),_:1})):l("",!0)])):l("",!0)]),e.records.length>0?(s(),_("div",re,[d(ee,{records:e.records,onUpdateRecord:$},null,8,["records"])])):l("",!0)])):l("",!0)]}),_:1})]),default:n(()=>[e.uuid?(s(),i(N,{key:0,student:e},null,8,["student"])):l("",!0)]),_:1})}}});export{ue as default};
