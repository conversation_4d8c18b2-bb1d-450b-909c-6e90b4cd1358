import{i as F,u as M,h as O,l as q,g as z,m as y,r as l,a as p,o as c,e as n,w as a,f as i,q as m,b as f,d as h,s as r,t as s,y as A,F as g,v as C}from"./app-BAwPsakn.js";const U={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},x={class:"flex flex-wrap gap-2"},G={class:"mt-4"},J={name:"AcademicBatchShow"},X=Object.assign(J,{setup(K){const w=F(),b=M(),B=O(),P={},$="academic/batch/",t=q({...P}),S=e=>{Object.assign(t,e)},T=z("periods"),v=y(!1),_=y(!1),V=e=>{v.value=!0,w.dispatch($+"updateCurrentPeriod",{uuid:t.uuid,form:{period_id:e.id}}).then(()=>{_.value=!0}).catch(()=>{}).finally(()=>{v.value=!1})};return(e,u)=>{const H=l("PageHeaderAction"),I=l("PageHeader"),N=l("TextMuted"),d=l("BaseDataView"),R=l("BaseButton"),D=l("ShowButton"),k=l("BaseCard"),L=l("BaseBadge"),j=l("ShowItem"),E=l("ParentTransition");return c(),p(g,null,[n(I,{title:e.$trans(i(b).meta.trans,{attribute:e.$trans(i(b).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.batch.batch"),path:"AcademicBatchList"}]},{default:a(()=>[n(H,{name:"AcademicBatch",title:e.$trans("academic.batch.batch"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(E,{appear:"",visibility:!0},{default:a(()=>[n(j,{"init-url":$,uuid:i(b).params.uuid,onSetItem:S,onRedirectTo:u[1]||(u[1]=o=>i(B).push({name:"AcademicBatch"})),refresh:_.value,onRefreshed:u[2]||(u[2]=o=>_.value=!1)},{default:a(()=>[t.uuid?(c(),m(k,{key:0},{title:a(()=>[r(s(t.name),1)]),footer:a(()=>[n(D,null,{default:a(()=>[i(A)("batch:edit")?(c(),m(R,{key:0,design:"primary",onClick:u[0]||(u[0]=o=>i(B).push({name:"AcademicBatchEdit",params:{uuid:t.uuid}}))},{default:a(()=>[r(s(e.$trans("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:a(()=>[h("dl",U,[n(d,{label:e.$trans("academic.batch.props.name")},{default:a(()=>[r(s(t.name)+" ",1),t.pgAccount?(c(),m(N,{key:0,block:""},{default:a(()=>[r(s(t.pgAccount),1)]),_:1})):f("",!0)]),_:1},8,["label"]),n(d,{label:e.$trans("academic.course.course")},{default:a(()=>[r(s(t.course.name),1)]),_:1},8,["label"]),n(d,{label:e.$trans("academic.batch.props.max_strength")},{default:a(()=>[r(s(t.maxStrength),1)]),_:1},8,["label"]),n(d,{label:e.$trans("academic.batch.props.roll_number_prefix")},{default:a(()=>[r(s(t.rollNumberPrefix),1)]),_:1},8,["label"]),n(d,{class:"col-span-1 sm:col-span-2",label:e.$trans("academic.batch.props.description")},{default:a(()=>[r(s(t.description),1)]),_:1},8,["label"]),n(d,{label:e.$trans("general.created_at")},{default:a(()=>[r(s(t.createdAt.formatted),1)]),_:1},8,["label"]),n(d,{label:e.$trans("general.updated_at")},{default:a(()=>[r(s(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0),i(A)("batch:edit")?(c(),m(k,{key:1},{title:a(()=>[r(s(e.$trans("global.update",{attribute:e.$trans("academic.period.current_period")})),1)]),default:a(()=>[h("div",x,[(c(!0),p(g,null,C(i(T),o=>(c(),m(L,{key:o.id,size:"md",design:"info",class:"cursor-pointer",onClick:Q=>V(o)},{default:a(()=>[r(s(o.name),1)]),_:2},1032,["onClick"]))),128))]),h("div",G,[(c(!0),p(g,null,C(t.periodHistory,o=>(c(),p("div",{class:"dark:text-gray-400 text-sm",key:o.id},s(o.name)+" - "+s(o.datetime.formatted),1))),128))])]),_:1})):f("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{X as default};
