import{u as _,H as R,l as f,r as i,q as j,o as U,w as d,d as m,e as s,s as g,t as V,I as E,f as r,J as v,a as O,F as k}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-2"},H={class:"flex"},I={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},z={name:"ContactForm"},J=Object.assign(z,{setup(D){const p=_(),l={firstName:"",middleName:"",thirdName:"",lastName:"",gender:"",birthDate:"",contactNumber:"",email:"",fatherName:"",motherName:""},N="contact/",o=R(N),u=f({genders:[]}),a=f({...l}),$=f({isLoaded:!p.params.uuid}),h=n=>{Object.assign(u,n),Object.assign(a,v(l))},B=n=>{Object.assign(l,{...n,birthDate:n.birthDate.value,anniversaryDate:n.anniversaryDate.value,gender:n.gender.value}),Object.assign(a,v(l)),$.isLoaded=!0};return(n,e)=>{const b=i("BaseLabel"),P=i("NameInput"),y=i("BaseRadioGroup"),F=i("DatePicker"),c=i("BaseInput"),C=i("FormAction");return U(),j(C,{"pre-requisites":!0,onSetPreRequisites:h,"init-url":N,"init-form":l,form:a,"set-form":B,redirect:"Contact"},{default:d(()=>[m("div",q,[m("div",A,[s(b,null,{default:d(()=>[g(V(n.$trans("contact.props.name")),1)]),_:1}),m("div",H,[s(P,{firstName:a.firstName,"onUpdate:firstName":e[0]||(e[0]=t=>a.firstName=t),middleName:a.middleName,"onUpdate:middleName":e[1]||(e[1]=t=>a.middleName=t),thirdName:a.thirdName,"onUpdate:thirdName":e[2]||(e[2]=t=>a.thirdName=t),lastName:a.lastName,"onUpdate:lastName":e[3]||(e[3]=t=>a.lastName=t),formErrors:r(o),"onUpdate:formErrors":e[4]||(e[4]=t=>E(o)?o.value=t:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),m("div",I,[s(b,null,{default:d(()=>[g(V(n.$trans("contact.props.gender")),1)]),_:1}),s(y,{"top-margin":"",options:u.genders,name:"gender",modelValue:a.gender,"onUpdate:modelValue":e[5]||(e[5]=t=>a.gender=t),error:r(o).gender,"onUpdate:error":e[6]||(e[6]=t=>r(o).gender=t),horizontal:""},null,8,["options","modelValue","error"])]),m("div",L,[s(F,{modelValue:a.birthDate,"onUpdate:modelValue":e[7]||(e[7]=t=>a.birthDate=t),name:"birthDate",label:n.$trans("contact.props.birth_date"),"no-clear":"",error:r(o).birthDate,"onUpdate:error":e[8]||(e[8]=t=>r(o).birthDate=t)},null,8,["modelValue","label","error"])]),m("div",T,[s(c,{type:"text",modelValue:a.contactNumber,"onUpdate:modelValue":e[9]||(e[9]=t=>a.contactNumber=t),name:"contactNumber",label:n.$trans("contact.props.contact_number"),error:r(o).contactNumber,"onUpdate:error":e[10]||(e[10]=t=>r(o).contactNumber=t)},null,8,["modelValue","label","error"])]),m("div",w,[s(c,{type:"text",modelValue:a.email,"onUpdate:modelValue":e[11]||(e[11]=t=>a.email=t),name:"email",label:n.$trans("contact.props.email"),error:r(o).email,"onUpdate:error":e[12]||(e[12]=t=>r(o).email=t)},null,8,["modelValue","label","error"])]),m("div",G,[s(c,{type:"text",modelValue:a.fatherName,"onUpdate:modelValue":e[13]||(e[13]=t=>a.fatherName=t),name:"fatherName",label:n.$trans("contact.props.father_name"),error:r(o).fatherName,"onUpdate:error":e[14]||(e[14]=t=>r(o).fatherName=t)},null,8,["modelValue","label","error"])]),m("div",S,[s(c,{type:"text",modelValue:a.motherName,"onUpdate:modelValue":e[15]||(e[15]=t=>a.motherName=t),name:"motherName",label:n.$trans("contact.props.mother_name"),error:r(o).motherName,"onUpdate:error":e[16]||(e[16]=t=>r(o).motherName=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),K={name:"ContactAction"},Q=Object.assign(K,{setup(D){const p=_();return(l,N)=>{const o=i("PageHeaderAction"),u=i("PageHeader"),a=i("ParentTransition");return U(),O(k,null,[s(u,{title:l.$trans(r(p).meta.trans,{attribute:l.$trans(r(p).meta.label)}),navs:[{label:l.$trans("contact.contact"),path:"Contact"}]},{default:d(()=>[s(o,{name:"Contact",title:l.$trans("contact.contact"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(a,{appear:"",visibility:!0},{default:d(()=>[s(J)]),_:1})],64)}}});export{Q as default};
