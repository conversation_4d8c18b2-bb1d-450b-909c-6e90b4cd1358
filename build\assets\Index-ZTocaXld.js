import{u as V,l as B,n as O,r as l,q as D,o as m,w as e,d as g,e as t,i as z,H as E,m as I,a as h,b as U,s as i,t as r,f as v,K as W,h as X,j as Y,z as Z,F as P,v as ee,A as te}from"./app-BAwPsakn.js";import{d as x}from"./vuedraggable.umd-BRYqknf6.js";const ne={class:"grid grid-cols-3 gap-6"},se={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",emits:["hide"],setup(L,{emit:k}){V();const _=k,f={name:""},p=B({...f}),w=B({isLoaded:!0});return O(async()=>{w.isLoaded=!0}),($,s)=>{const c=l("BaseInput"),M=l("FilterForm");return m(),D(M,{"init-form":f,form:p,multiple:[],onHide:s[1]||(s[1]=b=>_("hide"))},{default:e(()=>[g("div",ne,[g("div",se,[t(c,{type:"text",modelValue:p.name,"onUpdate:modelValue":s[0]||(s[0]=b=>p.name=b),name:"name",label:$.$trans("site.menu.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},oe={key:0},le={class:"flex border rounded-xl px-4 py-2"},ie={key:1},re={key:2,class:"mt-4"},ue={class:"flex border rounded-xl px-4 py-2"},de={key:3},me={key:4,class:"mt-4 flex justify-end"},ce={name:"SiteMenuReorder"},fe=Object.assign(ce,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(L,{emit:k}){V();const _=z(),f=k,p={menus:[]};E("site/menu/");const $=I(!1),s=B({headerMenus:[],footerMenus:[]});B({...p});const c=async()=>{$.value=!0,await _.dispatch("site/menu/list",{params:{parent:!0,all:!0}}).then(u=>{$.value=!1,s.headerMenus=u.filter(a=>a.placement.value==="header"),s.footerMenus=u.filter(a=>a.placement.value==="footer")}).catch(u=>{$.value=!1})},M=async()=>{$.value=!0,await _.dispatch("site/menu/reorder",{data:{headerMenus:s.headerMenus,footerMenus:s.footerMenus}}).then(u=>{$.value=!1,f("refresh"),f("close")}).catch(u=>{$.value=!1})},b=()=>{f("close")};return O(()=>{c()}),(u,a)=>{const C=l("BaseHeading"),R=l("BaseLabel"),d=l("BaseAlert"),n=l("BaseButton"),S=l("BaseModal");return m(),D(S,{show:L.visibility,onClose:b},{title:e(()=>[i(r(u.$trans("global.reorder",{attribute:u.$trans("site.menu.menu")})),1)]),default:e(()=>[s.headerMenus.length?(m(),h("div",oe,[t(C,null,{default:e(()=>[i(r(u.$trans("site.menu.placements.header")),1)]),_:1}),t(v(x),{class:"space-y-2",list:s.headerMenus,"item-key":"uuid"},{item:e(({element:F,index:H})=>[g("div",le,[a[0]||(a[0]=g("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(R,null,{default:e(()=>[i(r(F.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(m(),h("div",ie,[t(d,{design:"info",size:"xs"},{default:e(()=>[i(r(u.$trans("general.errors.record_not_found")),1)]),_:1})])),s.footerMenus.length?(m(),h("div",re,[t(C,null,{default:e(()=>[i(r(u.$trans("site.menu.placements.footer")),1)]),_:1}),t(v(x),{class:"space-y-2",list:s.footerMenus,"item-key":"uuid"},{item:e(({element:F,index:H})=>[g("div",ue,[a[1]||(a[1]=g("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(R,null,{default:e(()=>[i(r(F.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(m(),h("div",de,[t(d,{design:"info",size:"xs"},{default:e(()=>[i(r(u.$trans("general.errors.record_not_found")),1)]),_:1})])),s.headerMenus.length||s.footerMenus.length?(m(),h("div",me,[t(n,{onClick:M},{default:e(()=>[i(r(u.$trans("general.reorder")),1)]),_:1})])):U("",!0)]),_:1},8,["show"])}}}),pe={key:0},_e={class:"flex border rounded-xl px-4 py-2"},ve={key:1},ge={key:2,class:"mt-4 flex justify-end"},$e={name:"SiteMenuReorderSubMenu"},he=Object.assign($e,{props:{selectedMenu:{type:Object,default:()=>{}},visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(L,{emit:k}){V();const _=z(),f=k,p=L,w={menus:[]};E("site/menu/");const s=I(!1),c=B({menus:[]});B({...w});const M=async()=>{s.value=!0,await _.dispatch("site/menu/list",{params:{menu:p.selectedMenu.uuid}}).then(a=>{s.value=!1,c.menus=a}).catch(a=>{s.value=!1})},b=async()=>{s.value=!0,await _.dispatch("site/menu/reorderSubMenu",{data:{menu:p.selectedMenu.uuid,menus:c.menus}}).then(a=>{s.value=!1,f("refresh"),f("close")}).catch(a=>{s.value=!1})},u=()=>{f("close")};return W(()=>p.selectedMenu,a=>{a&&M()},{deep:!0}),(a,C)=>{const R=l("BaseHeading"),d=l("BaseLabel"),n=l("BaseAlert"),S=l("BaseButton"),F=l("BaseModal");return m(),D(F,{show:L.visibility,onClose:u},{title:e(()=>[i(r(a.$trans("global.reorder",{attribute:a.$trans("site.menu.menu")})),1)]),default:e(()=>[c.menus.length?(m(),h("div",pe,[t(R,null,{default:e(()=>[i(r(a.$trans("site.menu.menu")),1)]),_:1}),t(v(x),{class:"space-y-2",list:c.menus,"item-key":"uuid"},{item:e(({element:H,index:T})=>[g("div",_e,[C[0]||(C[0]=g("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(d,null,{default:e(()=>[i(r(H.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(m(),h("div",ve,[t(n,{design:"info",size:"xs"},{default:e(()=>[i(r(a.$trans("general.errors.record_not_found")),1)]),_:1})])),c.menus.length?(m(),h("div",ge,[t(S,{onClick:b},{default:e(()=>[i(r(a.$trans("general.reorder")),1)]),_:1})])):U("",!0)]),_:1},8,["show"])}}}),Me={key:0},be=["href"],ye={name:"SiteMenuList"},we=Object.assign(ye,{emits:["refresh"],setup(L,{emit:k}){const _=X(),f=k,p=Y("emitter");let w=["filter"];w.unshift("create");let $=["print","pdf","excel"];const s="site/menu/",c=I(!1),M=I(!1),b=I(!1),u=B({}),a=B({selectedMenu:null}),C=d=>{Object.assign(u,d)},R=d=>{a.selectedMenu=d,b.value=!0};return(d,n)=>{const S=l("BaseButton"),F=l("PageHeaderAction"),H=l("PageHeader"),T=l("ParentTransition"),N=l("TextMuted"),j=l("DataCell"),A=l("FloatingMenuItem"),q=l("FloatingMenu"),K=l("DataRow"),G=l("DataTable"),J=l("ListItem"),Q=Z("tooltip");return m(),h(P,null,[t(J,{"init-url":s,"additional-query":{},onSetItems:C},{header:e(()=>[t(H,{title:d.$trans("site.menu.menu"),navs:[{label:d.$trans("site.site"),path:"Site"}]},{default:e(()=>[t(F,{url:"site/menus/",name:"SiteMenu",title:d.$trans("site.menu.menu"),actions:v(w),"dropdown-actions":v($),"config-path":"SiteConfig",onToggleFilter:n[2]||(n[2]=o=>c.value=!c.value)},{after:e(()=>[t(S,{design:"white",onClick:n[1]||(n[1]=o=>v(_).push({name:"SiteConfig"}))},{default:e(()=>n[12]||(n[12]=[g("i",{class:"fas fa-cog"},null,-1)])),_:1})]),default:e(()=>[te((m(),D(S,{design:"white",onClick:n[0]||(n[0]=o=>M.value=!M.value)},{default:e(()=>n[11]||(n[11]=[g("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[Q,d.$trans("global.reorder",{attribute:d.$trans("site.menu.menu")})]])]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(T,{appear:"",visibility:c.value},{default:e(()=>[t(ae,{onRefresh:n[3]||(n[3]=o=>v(p).emit("listItems")),onHide:n[4]||(n[4]=o=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(T,{appear:"",visibility:!0},{default:e(()=>[t(G,{header:u.headers,meta:u.meta,module:"site.menu",onRefresh:n[6]||(n[6]=o=>v(p).emit("listItems"))},{actionButton:e(()=>[t(S,{onClick:n[5]||(n[5]=o=>v(_).push({name:"SiteMenuCreate"}))},{default:e(()=>[i(r(d.$trans("global.add",{attribute:d.$trans("site.menu.menu")})),1)]),_:1})]),default:e(()=>[(m(!0),h(P,null,ee(u.data,o=>(m(),D(K,{key:o.uuid},{default:e(()=>[t(j,{name:"name"},{default:e(()=>[i(r(o.name)+" ",1),t(N,{block:""},{default:e(()=>{var y;return[i(r((y=o.parent)==null?void 0:y.name),1)]}),_:2},1024)]),_:2},1024),t(j,{name:"placement"},{default:e(()=>[i(r(o.placement.label),1)]),_:2},1024),t(j,{name:"page"},{default:e(()=>{var y;return[i(r((y=o.page)==null?void 0:y.title)+" ",1),o.hasExternalUrl?(m(),h("span",Me,[g("a",{href:o.externalUrl,target:"_blank"},[i(r(o.externalUrl)+" ",1),n[13]||(n[13]=g("i",{class:"fas fa-up-right-from-square"},null,-1))],8,be)])):U("",!0)]}),_:2},1024),t(j,{name:"action"},{default:e(()=>[t(q,null,{default:e(()=>[o.parent==null?(m(),D(A,{key:0,icon:"fas fa-arrows-alt",onClick:y=>R(o)},{default:e(()=>[i(r(d.$trans("general.reorder")),1)]),_:2},1032,["onClick"])):U("",!0),t(A,{icon:"fas fa-edit",onClick:y=>v(_).push({name:"SiteMenuEdit",params:{uuid:o.uuid}})},{default:e(()=>[i(r(d.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(A,{icon:"fas fa-copy",onClick:y=>v(_).push({name:"SiteMenuDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[i(r(d.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(A,{icon:"fas fa-trash",onClick:y=>v(p).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[i(r(d.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),t(fe,{visibility:M.value,onClose:n[7]||(n[7]=o=>M.value=!1),onRefresh:n[8]||(n[8]=o=>f("refresh"))},null,8,["visibility"]),t(he,{"selected-menu":a.selectedMenu,visibility:b.value,onClose:n[9]||(n[9]=o=>b.value=!1),onRefresh:n[10]||(n[10]=o=>f("refresh"))},null,8,["selected-menu","visibility"])],64)}}});export{we as default};
