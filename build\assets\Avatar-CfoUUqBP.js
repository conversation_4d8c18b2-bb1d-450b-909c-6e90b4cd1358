import{i as d,c as i,r as e,q as p,o as u,w as _,d as t,e as h}from"./app-BAwPsakn.js";const m={class:"grid grid-cols-1 lg:grid-cols-3"},v={class:"col-span-1 lg:col-start-2"},g={},y=Object.assign(g,{__name:"Avatar",setup(f){const a=d(),s=i(()=>a.getters["auth/user/user"]("avatar")),o=async()=>{await a.dispatch("auth/user/fetch")},r=async()=>{await a.dispatch("auth/user/fetch")};return(c,w)=>{const n=e("ImageUpload"),l=e("ParentTransition");return u(),p(l,{appear:"",visibility:!0},{default:_(()=>[t("div",m,[t("div",v,[h(n,{design:"modern",shape:"circle",class:"h-64 w-64 rounded-full",label:c.$trans("user.avatar"),src:s.value,"upload-path":"user/profile/avatar","remove-path":"user/profile/avatar",onUploaded:o,onRemoved:r},null,8,["label","src"])])])]),_:1})}}});export{y as default};
