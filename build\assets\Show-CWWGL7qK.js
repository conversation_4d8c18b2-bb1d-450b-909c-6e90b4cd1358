import{j as H,i as N,u as T,h,l as D,r as i,a as d,o as r,e as s,w as t,f as m,q as b,b as c,d as R,s as l,t as o,F as _,v as k,y}from"./app-BAwPsakn.js";const E={key:0,class:"flex space-x-4"},F={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},O={name:"UserShow"},G=Object.assign(O,{setup(q){const w=H("emitter");N();const v=T(),B=h(),C={},S="user/",a=D({...C}),P=e=>{Object.assign(a,e)},g=async e=>{w.emit("showActionItem",{uuid:a.uuid,action:"status",data:{status:e},confirmation:!0})};return(e,n)=>{const U=i("PageHeaderAction"),V=i("PageHeader"),$=i("BaseBadge"),f=i("BaseButton"),p=i("BaseDataView"),A=i("ShowButton"),I=i("BaseCard"),j=i("ShowItem"),z=i("ParentTransition");return r(),d(_,null,[s(V,{title:e.$trans(m(v).meta.trans,{attribute:e.$trans(m(v).meta.label)}),navs:[{label:e.$trans("user.user"),path:"User"}]},{default:t(()=>[s(U,{name:"User",title:e.$trans("user.user"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(z,{appear:"",visibility:!0},{default:t(()=>[s(j,{"init-url":S,uuid:m(v).params.uuid,onSetItem:P,onRedirectTo:n[5]||(n[5]=u=>m(B).push({name:"User"}))},{default:t(()=>[a.uuid?(r(),b(I,{key:0},{title:t(()=>[l(o(a.profile.name)+" ",1),s($,{label:a.status.label,design:a.status.color},null,8,["label","design"])]),action:t(()=>[m(y)("user:edit")?(r(),d("div",E,[a.status.value=="pending_approval"?(r(),d(_,{key:0},[s(f,{size:"xs",design:"success",onClick:n[0]||(n[0]=u=>g("activated"))},{default:t(()=>[l(o(e.$trans("user.status_action.activate")),1)]),_:1}),s(f,{size:"xs",design:"danger",onClick:n[1]||(n[1]=u=>g("disapproved"))},{default:t(()=>[l(o(e.$trans("user.status_action.disapprove")),1)]),_:1})],64)):c("",!0),a.status.value=="activated"?(r(),b(f,{key:1,size:"xs",design:"danger",onClick:n[2]||(n[2]=u=>g("banned"))},{default:t(()=>[l(o(e.$trans("user.status_action.ban")),1)]),_:1})):c("",!0),a.status.value=="banned"?(r(),b(f,{key:2,size:"xs",design:"success",onClick:n[3]||(n[3]=u=>g("activated"))},{default:t(()=>[l(o(e.$trans("user.status_action.activate")),1)]),_:1})):c("",!0)])):c("",!0)]),footer:t(()=>[s(A,null,{default:t(()=>[m(y)("user:edit")?(r(),b(f,{key:0,design:"primary",onClick:n[4]||(n[4]=u=>m(B).push({name:"UserEdit",params:{uuid:a.uuid}}))},{default:t(()=>[l(o(e.$trans("general.edit")),1)]),_:1})):c("",!0)]),_:1})]),default:t(()=>[R("dl",F,[s(p,{label:e.$trans("user.props.email")},{default:t(()=>[l(o(a.email),1)]),_:1},8,["label"]),s(p,{label:e.$trans("user.props.username")},{default:t(()=>[l(o(a.username),1)]),_:1},8,["label"]),s(p,{label:e.$trans("team.config.role.role"),class:"col-span-1 sm:col-span-2"},{default:t(()=>[(r(!0),d(_,null,k(a.roles,u=>(r(),d("div",null,[s($,{label:u.label},null,8,["label"])]))),256))]),_:1},8,["label"]),a.showPermissions?(r(),b(p,{key:0,label:e.$trans("team.config.permission.permission"),class:"col-span-1 sm:col-span-2"},{default:t(()=>[(r(!0),d(_,null,k(a.permissions,u=>(r(),d("div",null,[s($,{label:u},null,8,["label"])]))),256))]),_:1},8,["label"])):c("",!0),s(p,{label:e.$trans("general.created_at")},{default:t(()=>[l(o(a.createdAt.formatted),1)]),_:1},8,["label"]),s(p,{label:e.$trans("general.updated_at")},{default:t(()=>[l(o(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):c("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{G as default};
