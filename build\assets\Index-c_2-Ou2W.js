import{u as E,h as R,i as V,l as F,n as z,r as s,q as _,o as u,w as e,d as S,e as t,s as i,t as l,j as G,m as j,a as M,f as w,F as D,v as J,b as k}from"./app-BAwPsakn.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["refresh","hide"],setup(P,{emit:B}){const c=E(),y=R();V();const d=B,m={name:""},r=F({...m}),f=()=>{Object.assign(r,m),b(),d("hide")};z(async()=>{Object.assign(r,{search:c.query.search})});const b=async()=>{await y.push({name:c.name,query:{...c.query,...r}}),d("refresh")};return(p,g)=>{const a=s("BaseInput"),o=s("BaseButton"),h=s("BaseCard");return u(),_(h,null,{footer:e(()=>[t(o,{design:"error",class:"mr-4",onClick:f},{default:e(()=>[i(l(p.$trans("general.cancel")),1)]),_:1}),t(o,{onClick:b},{default:e(()=>[i(l(p.$trans("general.filter")),1)]),_:1})]),default:e(()=>[S("div",K,[S("div",Q,[t(a,{type:"text",modelValue:r.name,"onUpdate:modelValue":g[0]||(g[0]=v=>r.name=v),name:"name",label:p.$trans("config.sms.template.props.name")},null,8,["modelValue","label"])])])]),_:1})}}},X={key:0,class:"mb-4"},Y={name:"ConfigSMSTemplateList"},x=Object.assign(Y,{setup(P){const B=R();V();const c=G("emitter"),y="config/smsTemplate/",d=j(!1),m=j(!1),r=F({}),f=F({selectedTemplate:null}),b=a=>{Object.assign(r,a)},p=a=>{f.selectedTemplate=a,m.value=!0},g=async a=>{c.emit("actionItem",{uuid:a.uuid,action:"toggleStatus",confirmation:!0})};return(a,o)=>{const h=s("PageHeaderAction"),v=s("ParentTransition"),T=s("BaseBadge"),$=s("DataCell"),q=s("TextMuted"),C=s("FloatingMenuItem"),A=s("FloatingMenu"),L=s("DataRow"),O=s("DataTable"),N=s("ListItem"),H=s("ConfigPage"),U=s("BaseModal");return u(),M(D,null,[t(H,{"no-background":""},{action:e(()=>[t(h,{name:"ConfigSMSTemplate",title:a.$trans("config.sms.template.template"),actions:["filter"],onToggleFilter:o[0]||(o[0]=n=>d.value=!d.value)},null,8,["title"])]),default:e(()=>[t(N,{class:"sm:-mt-4","init-url":y,onSetItems:b},{filter:e(()=>[t(v,{appear:"",visibility:d.value},{default:e(()=>[t(W,{onRefresh:o[1]||(o[1]=n=>w(c).emit("listItems")),onHide:o[2]||(o[2]=n=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(v,{appear:"",visibility:!0},{default:e(()=>[t(O,{header:r.headers,meta:r.meta,module:"config.sms.template",onRefresh:o[3]||(o[3]=n=>w(c).emit("listItems"))},{actionButton:e(()=>o[5]||(o[5]=[])),default:e(()=>[(u(!0),M(D,null,J(r.data,n=>(u(),_(L,{key:n.uuid},{default:e(()=>[t($,{name:"name"},{default:e(()=>[i(l(n.name)+" ",1),n.enabledAt.value?k("",!0):(u(),_(T,{key:0,design:"danger"},{default:e(()=>[i(l(a.$trans("config.template.statuses.disabled")),1)]),_:1}))]),_:2},1024),t($,{name:"templateId"},{default:e(()=>[i(l(n.templateId||"-"),1)]),_:2},1024),t($,{name:"subject"},{default:e(()=>[i(l(n.subject)+" ",1),t(q,{block:""},{default:e(()=>[i(l(n.content),1)]),_:2},1024)]),_:2},1024),t($,{name:"action"},{default:e(()=>[t(A,null,{default:e(()=>[n.enabledAt.value?(u(),_(C,{key:0,icon:"far fa-times-circle",onClick:I=>g(n)},{default:e(()=>[i(l(a.$trans("global.disable",{attribute:a.$trans("config.template.template")})),1)]),_:2},1032,["onClick"])):k("",!0),n.enabledAt.value?k("",!0):(u(),_(C,{key:1,icon:"far fa-check-circle",onClick:I=>g(n)},{default:e(()=>[i(l(a.$trans("global.enable",{attribute:a.$trans("config.template.template")})),1)]),_:2},1032,["onClick"])),t(C,{icon:"fas fa-arrow-circle-right",onClick:I=>p(n)},{default:e(()=>[i(l(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(C,{icon:"fas fa-edit",onClick:I=>w(B).push({name:"ConfigSMSTemplateEdit",params:{uuid:n.uuid}})},{default:e(()=>[i(l(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})]),_:1}),t(U,{show:m.value,onClose:o[4]||(o[4]=n=>m.value=!1)},{title:e(()=>[i(l(a.$trans("global.detail",{attribute:a.$trans("config.sms.template.template")})),1)]),default:e(()=>[f.selectedTemplate?(u(),M("div",X,l(f.selectedTemplate.content),1)):k("",!0)]),_:1},8,["show"])],64)}}});export{x as default};
