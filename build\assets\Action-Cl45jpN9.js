import{u as R,G as A,l as S,H as j,n as q,r as p,q as H,o as $,w as V,d as n,e as i,a as y,b as C,f as o,s as N,t as O,M as E,J as L,F as I}from"./app-BAwPsakn.js";import{d as M}from"./vuedraggable.umd-BRYqknf6.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},K={key:0,class:"col-span-3 sm:col-span-1"},Q={key:1,class:"col-span-3 sm:col-span-1"},X={class:"mt-4 grid grid-cols-3 gap-3"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"flex items-center space-x-4"},tt=["onClick"],et={class:"w-full"},rt={class:"col-span-3 sm:col-span-1"},at={class:"mt-4"},ot={class:"mt-4 grid grid-cols-3 gap-6"},st={class:"col-span-3 sm:col-span-1"},nt={class:"mt-4 grid grid-cols-3 gap-6"},it={class:"col-span-3"},lt={name:"TransportRouteForm"},pt=Object.assign(lt,{setup(k){const v=R(),l={name:"",maxCapacity:"",vehicle:"",direction:"",arrivalStartsAt:"",departureStartsAt:"",stoppages:[],durationToDestination:"",description:""},U={uuid:A(),stoppage:"",arrivalTime:""},f="transport/route/",c=S({stoppages:[],vehicles:[],directions:[]}),s=j(f),r=S({...l}),T=S({isLoaded:!v.params.uuid}),D=a=>{Object.assign(c,a)},B=()=>{r.stoppages.push({...U,uuid:A()}),T.isLoaded=!0},P=async a=>{await E()&&(r.stoppages.length==1?r.stoppages=[U]:r.stoppages.splice(a,1))},F=a=>{var u,g,b;let t=[];a.stoppages.forEach(_=>{t.push({uuid:A(),stoppage:_.stoppage.uuid,arrivalTime:_.arrivalTime})}),Object.assign(l,{...a,stoppages:t,vehicle:(u=a.vehicle)==null?void 0:u.uuid,direction:a.direction.value,arrivalStartsAt:(g=a.arrivalStartsAt)==null?void 0:g.at,departureStartsAt:(b=a.departureStartsAt)==null?void 0:b.at}),Object.assign(r,L(l)),T.vehicle=a.vehicle.name,T.isLoaded=!0};return q(()=>{v.params.uuid||B()}),(a,t)=>{const u=p("BaseInput"),g=p("BaseSelect"),b=p("DatePicker"),_=p("BaseBadge"),h=p("BaseTextarea"),w=p("FormAction");return $(),H(w,{"pre-requisites":!0,onSetPreRequisites:D,"init-url":f,"init-form":l,form:r,"set-form":F,redirect:"TransportRoute"},{default:V(()=>[n("div",G,[n("div",J,[i(u,{type:"text",modelValue:r.name,"onUpdate:modelValue":t[0]||(t[0]=e=>r.name=e),name:"name",label:a.$trans("transport.route.props.name"),error:o(s).name,"onUpdate:error":t[1]||(t[1]=e=>o(s).name=e),autofocus:""},null,8,["modelValue","label","error"])]),n("div",W,[i(g,{modelValue:r.vehicle,"onUpdate:modelValue":t[2]||(t[2]=e=>r.vehicle=e),name:"vehicle",label:a.$trans("transport.vehicle.vehicle"),"label-prop":"nameWithRegistrationNumber","value-prop":"uuid",options:c.vehicles,error:o(s).vehicle,"onUpdate:error":t[3]||(t[3]=e=>o(s).vehicle=e)},null,8,["modelValue","label","options","error"])]),n("div",x,[i(u,{type:"text",modelValue:r.maxCapacity,"onUpdate:modelValue":t[4]||(t[4]=e=>r.maxCapacity=e),name:"maxCapacity",label:a.$trans("transport.route.props.max_capacity"),error:o(s).maxCapacity,"onUpdate:error":t[5]||(t[5]=e=>o(s).maxCapacity=e),autofocus:""},null,8,["modelValue","label","error"])]),n("div",z,[i(g,{modelValue:r.direction,"onUpdate:modelValue":t[6]||(t[6]=e=>r.direction=e),name:"direction",label:a.$trans("transport.route.props.direction"),options:c.directions,error:o(s).direction,"onUpdate:error":t[7]||(t[7]=e=>o(s).direction=e)},null,8,["modelValue","label","options","error"])]),r.direction=="arrival"||r.direction=="roundtrip"?($(),y("div",K,[i(b,{as:"time",modelValue:r.arrivalStartsAt,"onUpdate:modelValue":t[8]||(t[8]=e=>r.arrivalStartsAt=e),name:"arrivalStartsAt",label:a.$trans("transport.route.props.arrival_starts_at"),error:o(s).arrivalStartsAt,"onUpdate:error":t[9]||(t[9]=e=>o(s).arrivalStartsAt=e)},null,8,["modelValue","label","error"])])):C("",!0),r.direction=="departure"||r.direction=="roundtrip"?($(),y("div",Q,[i(b,{as:"time",modelValue:r.departureStartsAt,"onUpdate:modelValue":t[10]||(t[10]=e=>r.departureStartsAt=e),name:"departureStartsAt",label:a.$trans("transport.route.props.departure_starts_at"),error:o(s).departureStartsAt,"onUpdate:error":t[11]||(t[11]=e=>o(s).departureStartsAt=e)},null,8,["modelValue","label","error"])])):C("",!0)]),i(o(M),{list:r.stoppages,"item-key":"uuid"},{item:V(({element:e,index:m})=>[n("div",X,[n("div",Y,[n("div",Z,[n("span",{class:"text-danger ml-2 cursor-pointer",onClick:d=>P(m)},t[16]||(t[16]=[n("i",{class:"fas fa-times-circle"},null,-1)]),8,tt),t[17]||(t[17]=n("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),n("div",et,[i(g,{modelValue:e.stoppage,"onUpdate:modelValue":d=>e.stoppage=d,name:`stoppages.${m}.stoppage`,label:a.$trans("transport.stoppage.stoppage"),options:c.stoppages,"label-prop":"name","value-prop":"uuid",error:o(s)[`stoppages.${m}.stoppage`],"onUpdate:error":d=>o(s)[`stoppages.${m}.stoppage`]=d},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error"])])])]),n("div",rt,[i(u,{type:"text",name:`stoppages.${m}.arrivalTime`,modelValue:e.arrivalTime,"onUpdate:modelValue":d=>e.arrivalTime=d,label:a.$trans("transport.route.props.arrival_time"),error:o(s)[`stoppages.${m}.arrivaltime`],"onUpdate:error":d=>o(s)[`stoppages.${m}.arrivaltime`]=d,"trailing-text":a.$trans("list.durations.minutes")},null,8,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error","trailing-text"])])])]),_:1},8,["list"]),n("div",at,[i(_,{design:"primary",onClick:B,class:"cursor-pointer"},{default:V(()=>[N(O(a.$trans("global.add",{attribute:a.$trans("transport.stoppage.stoppage")})),1)]),_:1})]),n("div",ot,[n("div",st,[i(u,{type:"text",modelValue:r.durationToDestination,"onUpdate:modelValue":t[12]||(t[12]=e=>r.durationToDestination=e),name:"durationToDestination",label:a.$trans("transport.route.props.duration_to_destination"),error:o(s).durationToDestination,"onUpdate:error":t[13]||(t[13]=e=>o(s).durationToDestination=e),"trailing-text":a.$trans("list.durations.minutes")},null,8,["modelValue","label","error","trailing-text"])])]),n("div",nt,[n("div",it,[i(h,{modelValue:r.description,"onUpdate:modelValue":t[14]||(t[14]=e=>r.description=e),name:"description",label:a.$trans("transport.route.props.description"),error:o(s).description,"onUpdate:error":t[15]||(t[15]=e=>o(s).description=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),dt={name:"TransportRouteAction"},ct=Object.assign(dt,{setup(k){const v=R();return(l,U)=>{const f=p("PageHeaderAction"),c=p("PageHeader"),s=p("ParentTransition");return $(),y(I,null,[i(c,{title:l.$trans(o(v).meta.trans,{attribute:l.$trans(o(v).meta.label)}),navs:[{label:l.$trans("transport.transport"),path:"Transport"},{label:l.$trans("transport.route.route"),path:"TransportRouteList"}]},{default:V(()=>[i(f,{name:"TransportRoute",title:l.$trans("transport.route.route"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(s,{appear:"",visibility:!0},{default:V(()=>[i(pt)]),_:1})],64)}}});export{ct as default};
