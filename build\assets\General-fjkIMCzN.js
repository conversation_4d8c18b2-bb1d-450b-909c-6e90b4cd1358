import{u as g,j as D,H as f,c as V,l as u,r as c,a as E,o as P,e as n,f as s,w as y,d as i,F as L}from"./app-BAwPsakn.js";const v={class:"grid grid-col-2 gap-4"},S={class:"col-span-3 sm:col-span-1"},C={class:"mt-4 grid grid-cols-2 gap-4"},O={class:"col-span-2 sm:col-span-1"},F={class:"col-span-2 sm:col-span-1"},j={class:"col-span-2 sm:col-span-1"},R={class:"col-span-2 sm:col-span-1"},q={class:"col-span-2 sm:col-span-1"},x={class:"col-span-2 sm:col-span-1"},H={class:"col-span-2 sm:col-span-1"},k={class:"col-span-2 sm:col-span-1"},M={class:"col-span-2 sm:col-span-1"},N={class:"col-span-2 sm:col-span-1"},T={class:"col-span-2 sm:col-span-1"},$={class:"col-span-2 sm:col-span-1"},G={name:"ResourceConfigGeneral"},J=Object.assign(G,{setup(z){const m=g(),r=D("$trans"),d="config/",a=f(d);V(()=>r("global.placeholder_info",{attribute:p.datePlaceholders}));const p=u({datePlaceholders:""}),b={enableFilterByAssignedSubject:!0,allowEditDiaryByAccessibleUser:!1,allowDeleteDiaryByAccessibleUser:!1,allowEditSyllabusByAccessibleUser:!1,allowDeleteSyllabusByAccessibleUser:!1,allowEditLessonPlanByAccessibleUser:!1,allowDeleteLessonPlanByAccessibleUser:!1,allowEditOnlineClassByAccessibleUser:!1,allowDeleteOnlineClassByAccessibleUser:!1,allowEditAssignmentByAccessibleUser:!1,allowDeleteAssignmentByAccessibleUser:!1,allowEditLearningMaterialByAccessibleUser:!1,allowDeleteLearningMaterialByAccessibleUser:!1,type:"resource"},o=u({...b}),U=A=>{};return(A,e)=>{const w=c("PageHeader"),t=c("BaseSwitch"),B=c("FormAction"),_=c("ParentTransition");return P(),E(L,null,[n(w,{title:s(r)(s(m).meta.label),navs:[{label:s(r)("resource.resource"),path:"Resource"}]},null,8,["title","navs"]),n(_,{appear:"",visibility:!0},{default:y(()=>[n(B,{"pre-requisites":"false",onSetPreRequisites:U,"init-url":d,"data-fetch":"resource","init-form":b,form:o,action:"store","stay-on":"",redirect:"Resource"},{default:y(()=>[i("div",v,[i("div",S,[n(t,{vertical:"",modelValue:o.enableFilterByAssignedSubject,"onUpdate:modelValue":e[0]||(e[0]=l=>o.enableFilterByAssignedSubject=l),name:"enableFilterByAssignedSubject",label:s(r)("global.enable",{attribute:s(r)("resource.config.props.filter_by_assigned_subject")}),error:s(a).enableFilterByAssignedSubject,"onUpdate:error":e[1]||(e[1]=l=>s(a).enableFilterByAssignedSubject=l)},null,8,["modelValue","label","error"])])]),i("div",C,[i("div",O,[n(t,{modelValue:o.allowEditDiaryByAccessibleUser,"onUpdate:modelValue":e[2]||(e[2]=l=>o.allowEditDiaryByAccessibleUser=l),name:"allowEditDiaryByAccessibleUser",label:s(r)("resource.config.props.allow_edit_diary_by_accessible_user"),error:s(a).allowEditDiaryByAccessibleUser,"onUpdate:error":e[3]||(e[3]=l=>s(a).allowEditDiaryByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",F,[n(t,{modelValue:o.allowDeleteDiaryByAccessibleUser,"onUpdate:modelValue":e[4]||(e[4]=l=>o.allowDeleteDiaryByAccessibleUser=l),name:"allowDeleteDiaryByAccessibleUser",label:s(r)("resource.config.props.allow_delete_diary_by_accessible_user"),error:s(a).allowDeleteDiaryByAccessibleUser,"onUpdate:error":e[5]||(e[5]=l=>s(a).allowDeleteDiaryByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",j,[n(t,{modelValue:o.allowEditSyllabusByAccessibleUser,"onUpdate:modelValue":e[6]||(e[6]=l=>o.allowEditSyllabusByAccessibleUser=l),name:"allowEditSyllabusByAccessibleUser",label:s(r)("resource.config.props.allow_edit_syllabus_by_accessible_user"),error:s(a).allowEditSyllabusByAccessibleUser,"onUpdate:error":e[7]||(e[7]=l=>s(a).allowEditSyllabusByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",R,[n(t,{modelValue:o.allowDeleteSyllabusByAccessibleUser,"onUpdate:modelValue":e[8]||(e[8]=l=>o.allowDeleteSyllabusByAccessibleUser=l),name:"allowDeleteSyllabusByAccessibleUser",label:s(r)("resource.config.props.allow_delete_syllabus_by_accessible_user"),error:s(a).allowDeleteSyllabusByAccessibleUser,"onUpdate:error":e[9]||(e[9]=l=>s(a).allowDeleteSyllabusByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",q,[n(t,{modelValue:o.allowEditLessonPlanByAccessibleUser,"onUpdate:modelValue":e[10]||(e[10]=l=>o.allowEditLessonPlanByAccessibleUser=l),name:"allowEditLessonPlanByAccessibleUser",label:s(r)("resource.config.props.allow_edit_lesson_plan_by_accessible_user"),error:s(a).allowEditLessonPlanByAccessibleUser,"onUpdate:error":e[11]||(e[11]=l=>s(a).allowEditLessonPlanByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",x,[n(t,{modelValue:o.allowDeleteLessonPlanByAccessibleUser,"onUpdate:modelValue":e[12]||(e[12]=l=>o.allowDeleteLessonPlanByAccessibleUser=l),name:"allowDeleteLessonPlanByAccessibleUser",label:s(r)("resource.config.props.allow_delete_lesson_plan_by_accessible_user"),error:s(a).allowDeleteLessonPlanByAccessibleUser,"onUpdate:error":e[13]||(e[13]=l=>s(a).allowDeleteLessonPlanByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",H,[n(t,{modelValue:o.allowEditOnlineClassByAccessibleUser,"onUpdate:modelValue":e[14]||(e[14]=l=>o.allowEditOnlineClassByAccessibleUser=l),name:"allowEditOnlineClassByAccessibleUser",label:s(r)("resource.config.props.allow_edit_assignment_by_accessible_user"),error:s(a).allowEditOnlineClassByAccessibleUser,"onUpdate:error":e[15]||(e[15]=l=>s(a).allowEditOnlineClassByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",k,[n(t,{modelValue:o.allowDeleteOnlineClassByAccessibleUser,"onUpdate:modelValue":e[16]||(e[16]=l=>o.allowDeleteOnlineClassByAccessibleUser=l),name:"allowDeleteOnlineClassByAccessibleUser",label:s(r)("resource.config.props.allow_delete_assignment_by_accessible_user"),error:s(a).allowDeleteOnlineClassByAccessibleUser,"onUpdate:error":e[17]||(e[17]=l=>s(a).allowDeleteOnlineClassByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",M,[n(t,{modelValue:o.allowEditAssignmentByAccessibleUser,"onUpdate:modelValue":e[18]||(e[18]=l=>o.allowEditAssignmentByAccessibleUser=l),name:"allowEditAssignmentByAccessibleUser",label:s(r)("resource.config.props.allow_edit_assignment_by_accessible_user"),error:s(a).allowEditAssignmentByAccessibleUser,"onUpdate:error":e[19]||(e[19]=l=>s(a).allowEditAssignmentByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",N,[n(t,{modelValue:o.allowDeleteAssignmentByAccessibleUser,"onUpdate:modelValue":e[20]||(e[20]=l=>o.allowDeleteAssignmentByAccessibleUser=l),name:"allowDeleteAssignmentByAccessibleUser",label:s(r)("resource.config.props.allow_delete_assignment_by_accessible_user"),error:s(a).allowDeleteAssignmentByAccessibleUser,"onUpdate:error":e[21]||(e[21]=l=>s(a).allowDeleteAssignmentByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",T,[n(t,{modelValue:o.allowEditLessonPlanByAccessibleUser,"onUpdate:modelValue":e[22]||(e[22]=l=>o.allowEditLessonPlanByAccessibleUser=l),name:"allowEditLessonPlanByAccessibleUser",label:s(r)("resource.config.props.allow_edit_lesson_plan_by_accessible_user"),error:s(a).allowEditLessonPlanByAccessibleUser,"onUpdate:error":e[23]||(e[23]=l=>s(a).allowEditLessonPlanByAccessibleUser=l)},null,8,["modelValue","label","error"])]),i("div",$,[n(t,{modelValue:o.allowDeleteLessonPlanByAccessibleUser,"onUpdate:modelValue":e[24]||(e[24]=l=>o.allowDeleteLessonPlanByAccessibleUser=l),name:"allowDeleteLessonPlanByAccessibleUser",label:s(r)("resource.config.props.allow_delete_lesson_plan_by_accessible_user"),error:s(a).allowDeleteLessonPlanByAccessibleUser,"onUpdate:error":e[25]||(e[25]=l=>s(a).allowDeleteLessonPlanByAccessibleUser=l)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{J as default};
