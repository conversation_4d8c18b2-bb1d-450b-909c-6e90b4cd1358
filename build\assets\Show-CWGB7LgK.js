import{u as I,j as U,H as P,l as D,n as j,J as F,r as d,q as v,b as k,o as m,w as s,d as c,a as h,e as o,f as t,h as M,m as N,t as b,s as y,F as q}from"./app-BAwPsakn.js";import{_ as J,a as K}from"./Og-kkzJmWoX.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},Q={key:0,class:"col-span-3 sm:col-span-2"},W={name:"SitePageSliderForm"},X=Object.assign(W,{props:{page:{type:Object,default:()=>({})}},emits:["completed"],setup(T,{emit:V}){const $=I(),r=V,C=U("emitter"),g=T,_={hasSlider:!1,slider:null},B="site/page/",e=P(B),l=D({..._}),S=D({slider:null,isLoaded:!$.params.uuid}),n=()=>{r("completed"),C.emit("listItems")};return j(()=>{var u,a;g.page.uuid&&(Object.assign(_,{hasSlider:g.page.hasSlider,slider:(u=g.page.slider)==null?void 0:u.uuid}),Object.assign(l,F(_)),S.slider=(a=g.page.slider)==null?void 0:a.uuid,S.isLoaded=!0)}),(u,a)=>{const L=d("BaseSwitch"),f=d("BaseSelectSearch"),x=d("FormAction");return T.page.uuid?(m(),v(x,{key:0,"init-url":B,uuid:t($).params.uuid,"no-data-fetch":"",action:"updateSlider","init-form":_,form:l,"keep-adding":!1,"stay-on":!0,"after-submit":n},{default:s(()=>[c("div",z,[c("div",G,[o(L,{vertical:"",modelValue:l.hasSlider,"onUpdate:modelValue":a[0]||(a[0]=p=>l.hasSlider=p),name:"hasSlider",label:u.$trans("global.show",{attribute:u.$trans("site.block.props.slider")}),error:t(e).hasSlider,"onUpdate:error":a[1]||(a[1]=p=>t(e).hasSlider=p)},null,8,["modelValue","label","error"])]),l.hasSlider?(m(),h("div",Q,[o(f,{name:"slider",label:u.$trans("global.select",{attribute:u.$trans("site.block.props.slider")}),modelValue:l.slider,"onUpdate:modelValue":a[2]||(a[2]=p=>l.slider=p),error:t(e).slider,"onUpdate:error":a[3]||(a[3]=p=>t(e).slider=p),"label-prop":"name","value-prop":"uuid","init-search":S.slider,"init-search-key":"name","search-action":"site/block/list","additional-search-query":{type:"slider"}},null,8,["label","modelValue","error","init-search"])])):k("",!0)])]),_:1},8,["uuid","form"])):k("",!0)}}}),Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},ee={key:0,class:"col-span-3 sm:col-span-2"},te={key:0,class:"mt-4 grid grid-cols-3 gap-6"},ae={class:"col-span-3"},se={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},ne={name:"SitePageCTAForm"},le=Object.assign(ne,{props:{page:{type:Object,default:()=>({})}},emits:["completed"],setup(T,{emit:V}){const $=I(),r=V,C=U("emitter"),g=T,_={hasCta:!1,ctaTitle:null,ctaDescription:null,ctaButtonText:null,ctaButtonLink:null},B="site/page/",e=P(B),l=D({..._}),S=D({isLoaded:!$.params.uuid}),n=()=>{r("completed"),C.emit("listItems")};return j(()=>{g.page.uuid&&(Object.assign(_,{hasCta:g.page.hasCta,ctaTitle:g.page.ctaTitle,ctaDescription:g.page.ctaDescription,ctaButtonText:g.page.ctaButtonText,ctaButtonLink:g.page.ctaButtonLink}),Object.assign(l,F(_)),S.isLoaded=!0)}),(u,a)=>{const L=d("BaseSwitch"),f=d("BaseInput"),x=d("BaseTextarea"),p=d("FormAction");return T.page.uuid?(m(),v(p,{key:0,"init-url":B,uuid:t($).params.uuid,"no-data-fetch":"",action:"updateCTA","init-form":_,form:l,"keep-adding":!1,"stay-on":!0,"after-submit":n},{default:s(()=>[c("div",Y,[c("div",Z,[o(L,{vertical:"",modelValue:l.hasCta,"onUpdate:modelValue":a[0]||(a[0]=i=>l.hasCta=i),name:"hasCta",label:u.$trans("global.show",{attribute:u.$trans("site.block.props.cta")}),error:t(e).hasCta,"onUpdate:error":a[1]||(a[1]=i=>t(e).hasCta=i)},null,8,["modelValue","label","error"])]),l.hasCta?(m(),h("div",ee,[o(f,{type:"text",modelValue:l.ctaTitle,"onUpdate:modelValue":a[2]||(a[2]=i=>l.ctaTitle=i),name:"ctaTitle",label:u.$trans("site.block.props.cta_title"),error:t(e).ctaTitle,"onUpdate:error":a[3]||(a[3]=i=>t(e).ctaTitle=i)},null,8,["modelValue","label","error"])])):k("",!0)]),l.hasCta?(m(),h("div",te,[c("div",ae,[o(x,{rows:1,modelValue:l.ctaDescription,"onUpdate:modelValue":a[4]||(a[4]=i=>l.ctaDescription=i),name:"ctaDescription",label:u.$trans("site.block.props.cta_description"),error:t(e).ctaDescription,"onUpdate:error":a[5]||(a[5]=i=>t(e).ctaDescription=i)},null,8,["modelValue","label","error"])]),c("div",se,[o(f,{type:"text",modelValue:l.ctaButtonText,"onUpdate:modelValue":a[6]||(a[6]=i=>l.ctaButtonText=i),name:"ctaButtonText",label:u.$trans("site.block.props.cta_button_text"),error:t(e).ctaButtonText,"onUpdate:error":a[7]||(a[7]=i=>t(e).ctaButtonText=i)},null,8,["modelValue","label","error"])]),c("div",oe,[o(f,{type:"text",modelValue:l.ctaButtonLink,"onUpdate:modelValue":a[8]||(a[8]=i=>l.ctaButtonLink=i),name:"ctaButtonLink",label:u.$trans("site.block.props.cta_button_link"),error:t(e).ctaButtonLink,"onUpdate:error":a[9]||(a[9]=i=>t(e).ctaButtonLink=i)},null,8,["modelValue","label","error"])])])):k("",!0)]),_:1},8,["uuid","form"])):k("",!0)}}}),ie={class:"space-y-2"},re={key:0},de={key:1},ue={class:"grid grid-cols-1 gap-x-4 gap-y-8"},pe={class:"space-y-4"},ce={class:"px-4 py-2"},me={class:"text-2xl font-bold text-gray-900 dark:text-gray-400"},ge={key:0,class:"mt-2 font-semibold text-gray-800 dark:text-gray-500"},fe={name:"SitePageShow"},ke=Object.assign(fe,{setup(T){const V=I(),$=M(),r=U("$trans"),C=U("emitter"),g={uuid:""},_="site/page/",B=N(!1),e=D({...g}),l=S=>{Object.assign(e,S)};return(S,n)=>{const u=d("BaseButton"),a=d("PageHeaderAction"),L=d("PageHeader"),f=d("ListItemView"),x=d("ListContainerVertical"),p=d("BaseCard"),i=d("ListMedia"),O=d("BaseDataView"),A=d("MarkdownContent"),H=d("DetailLayoutVertical"),R=d("ShowItem"),E=d("ParentTransition");return m(),h(q,null,[e.uuid?(m(),v(L,{key:0,title:t(r)(t(V).meta.trans,{attribute:t(r)(t(V).meta.label)}),navs:[{label:t(r)("site.site"),path:"Site"},{label:t(r)("site.page.page"),path:"SitePageList"}]},{default:s(()=>[o(a,{name:"SitePage",title:t(r)("site.page.page"),actions:["list"]},{default:s(()=>[o(u,{design:"white",onClick:n[0]||(n[0]=w=>t($).push({name:"SitePageEdit",params:{uuid:e.uuid}}))},{default:s(()=>n[7]||(n[7]=[c("i",{class:"fas fa-pen-to-square"},null,-1)])),_:1})]),_:1},8,["title"])]),_:1},8,["title","navs"])):k("",!0),o(E,{appear:"",visibility:!0},{default:s(()=>[o(R,{"init-url":_,uuid:t(V).params.uuid,onSetItem:l,onRedirectTo:n[5]||(n[5]=w=>t($).push({name:"SitePage"})),refresh:B.value,onRefreshed:n[6]||(n[6]=w=>B.value=!1)},{default:s(()=>[e.uuid?(m(),v(H,{key:0},{detail:s(()=>[c("div",ie,[o(p,{"no-padding":"","no-content-padding":""},{title:s(()=>[y(b(e.title),1)]),action:s(()=>n[8]||(n[8]=[])),default:s(()=>[o(x,null,{default:s(()=>[o(f,{label:t(r)("general.created_at")},{default:s(()=>[y(b(e.createdAt.formatted),1)]),_:1},8,["label"]),o(f,{label:t(r)("general.updated_at")},{default:s(()=>[y(b(e.updatedAt.formatted),1)]),_:1},8,["label"]),o(f,{label:t(r)("site.seo.robots")},{default:s(()=>[e.seo.robots?(m(),h("span",re,n[9]||(n[9]=[c("i",{class:"far fa-check-circle fa-lg text-success"},null,-1)]))):(m(),h("span",de,n[10]||(n[10]=[c("i",{class:"far fa-times-circle fa-lg text-danger"},null,-1)])))]),_:1},8,["label"]),o(f,{label:t(r)("site.seo.meta_title")},{default:s(()=>[y(b(e.seo.metaTitle),1)]),_:1},8,["label"]),o(f,{label:t(r)("site.seo.meta_description")},{default:s(()=>[y(b(e.seo.metaDescription),1)]),_:1},8,["label"]),o(f,{label:t(r)("site.seo.meta_keywords")},{default:s(()=>[y(b(e.seo.metaKeywords),1)]),_:1},8,["label"])]),_:1})]),_:1}),o(p,null,{title:s(()=>[y(b(t(r)("general.media")),1)]),default:s(()=>[c("dl",ue,[o(O,{class:"col-span-1 sm:col-span-2"},{default:s(()=>[o(i,{grid:1,media:e.media,url:`/app/site/pages/${e.uuid}/`},null,8,["media","url"])]),_:1})])]),_:1}),e.assets.defaultOg?k("",!0):(m(),v(p,{key:0,"no-padding":"","no-content-padding":""},{title:s(()=>[y(b(t(r)("site.assets.og")),1)]),default:s(()=>[e.uuid?(m(),v(K,{key:0,disabled:"",page:e,onRefreshItem:n[1]||(n[1]=w=>B.value=!0)},null,8,["page"])):k("",!0)]),_:1}))])]),default:s(()=>[c("div",pe,[o(p,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{default:s(()=>[e.uuid?(m(),v(J,{key:0,disabled:"",page:e,onRefreshItem:n[2]||(n[2]=w=>B.value=!0)},null,8,["page"])):k("",!0),c("div",ce,[c("h1",me,b(e.title),1),e.subTitle?(m(),h("h2",ge,b(e.subTitle),1)):k("",!0),o(A,{class:"mt-2",content:e.contentHtml},null,8,["content"])])]),_:1}),o(p,{"no-padding":"","no-content-padding":""},{title:s(()=>[y(b(t(r)("site.block.props.slider")),1)]),default:s(()=>[o(X,{page:e,onCompleted:n[3]||(n[3]=w=>t(C).emit("refreshItem"))},null,8,["page"])]),_:1}),o(p,{"no-padding":"","no-content-padding":""},{title:s(()=>[y(b(t(r)("site.block.props.cta")),1)]),default:s(()=>[o(le,{page:e,onCompleted:n[4]||(n[4]=w=>t(C).emit("refreshItem"))},null,8,["page"])]),_:1})])]),_:1})):k("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ke as default};
