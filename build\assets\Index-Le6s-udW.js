import{u as R,h as T,j as S,g as j,m as A,l as H,r as t,q as p,o as m,w as e,e as a,f as s,a as L,F as M,v as N,s as d,t as c,b as O}from"./app-BAwPsakn.js";const V={name:"EmployeeProfileEditRequestList"},x=Object.assign(V,{props:{employee:{type:Object,default(){return{}}}},setup(n){const g=R(),f=T(),h=S("emitter"),E=j("employee.allowEmployeeToSubmitContactEditRequest");let y=[];E.value&&y.unshift("new");const b="employee/profileEditRequest/",_=A(!1),u=H({}),v=l=>{Object.assign(u,l)};return(l,i)=>{const w=t("PageHeaderAction"),C=t("PageHeader"),r=t("DataCell"),P=t("BaseBadge"),k=t("FloatingMenuItem"),q=t("FloatingMenu"),D=t("DataRow"),$=t("DataTable"),F=t("ParentTransition"),B=t("ListItem");return m(),p(B,{"init-url":b,uuid:s(g).params.uuid,onSetItems:v},{header:e(()=>[n.employee.uuid?(m(),p(C,{key:0,title:l.$trans("employee.edit_request.edit_request"),navs:[{label:l.$trans("employee.employee"),path:"Employee"},{label:n.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:n.employee.uuid}}}]},{default:e(()=>[a(w,{url:`employees/${n.employee.uuid}/edit-requests/`,name:"EmployeeProfileEditRequest",title:l.$trans("employee.edit_request.edit_request"),actions:s(y),onToggleFilter:i[0]||(i[0]=o=>_.value=!_.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):O("",!0)]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a($,{header:u.headers,meta:u.meta,module:"employee.edit_request",onRefresh:i[1]||(i[1]=o=>s(h).emit("listItems"))},{default:e(()=>[(m(!0),L(M,null,N(u.data,o=>(m(),p(D,{key:o.uuid,onDoubleClick:I=>s(f).push({name:"EmployeeProfileEditRequestShow",params:{uuid:n.employee.uuid,muuid:o.uuid}})},{default:e(()=>[a(r,{name:"user"},{default:e(()=>[d(c(o.user.profile.name),1)]),_:2},1024),a(r,{name:"status"},{default:e(()=>[a(P,{design:o.status.color},{default:e(()=>[d(c(o.status.label),1)]),_:2},1032,["design"])]),_:2},1024),a(r,{name:"createdAt"},{default:e(()=>[d(c(o.createdAt.formatted),1)]),_:2},1024),a(r,{name:"action"},{default:e(()=>[a(q,null,{default:e(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:I=>s(f).push({name:"EmployeeProfileEditRequestShow",params:{uuid:n.employee.uuid,muuid:o.uuid}})},{default:e(()=>[d(c(l.$trans("general.show")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{x as default};
