import{u as V,h as E,G as U,H as q,l as g,r as u,q as P,o as _,w as v,d as r,b as R,e as d,f as i,I,J as S,a as j,F as O}from"./app-BAwPsakn.js";const w={class:"grid grid-cols-4 gap-6"},L={class:"col-span-4 sm:col-span-3"},M={class:"col-span-4 sm:col-span-1"},N={class:"mt-4 grid grid-cols-4 gap-6"},D={class:"col-span-4 sm:col-span-1"},G={class:"mt-4 grid grid-cols-1 gap-6"},J={class:"col-span-4"},z={class:"col"},K={name:"CommunicationAnnouncementForm"},Q=Object.assign(K,{setup($){const m=V();E();const a={title:"",type:"",batch:"",isPublic:!1,studentAudienceType:"",employeeAudienceType:"",studentAudiences:[],employeeAudiences:[],description:"",media:[],mediaUpdated:!1,mediaToken:U(),mediaHash:[]},T="communication/announcement/",s=q(T),p=g({types:[],studentAudienceTypes:[],employeeAudienceTypes:[]}),t=g({...a}),c=g({studentAudiences:[],employeeAudiences:[],isLoaded:!m.params.uuid}),B=o=>{Object.assign(p,o)},F=()=>{t.mediaToken=U(),t.mediaHash=[]},k=o=>{var A,b,f;let e=o.audiences.filter(l=>l.type=="student").map(l=>l.uuid),y=o.audiences.filter(l=>l.type=="employee").map(l=>l.uuid);Object.assign(a,{...o,type:((A=o.type)==null?void 0:A.uuid)||"",studentAudienceType:((b=o.studentAudienceType)==null?void 0:b.value)||"",employeeAudienceType:((f=o.employeeAudienceType)==null?void 0:f.value)||"",studentAudiences:e,employeeAudiences:y}),Object.assign(t,S(a)),c.studentAudiences=e,c.employeeAudiences=y,c.isLoaded=!0};return(o,e)=>{const y=u("BaseInput"),A=u("BaseSelect"),b=u("BaseSwitch"),f=u("AudienceInput"),l=u("BaseEditor"),C=u("MediaUpload"),H=u("FormAction");return _(),P(H,{"pre-requisites":!0,onSetPreRequisites:B,"init-url":T,"init-form":a,form:t,setForm:k,redirect:"CommunicationAnnouncement",onResetMediaFiles:F},{default:v(()=>[r("div",w,[r("div",L,[d(y,{type:"text",modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=n=>t.title=n),name:"title",label:o.$trans("communication.announcement.props.title"),error:i(s).title,"onUpdate:error":e[1]||(e[1]=n=>i(s).title=n),autofocus:""},null,8,["modelValue","label","error"])]),r("div",M,[d(A,{modelValue:t.type,"onUpdate:modelValue":e[2]||(e[2]=n=>t.type=n),name:"type",label:o.$trans("communication.announcement.props.type"),options:p.types,"label-prop":"name","value-prop":"uuid",error:i(s).type,"onUpdate:error":e[3]||(e[3]=n=>i(s).type=n)},null,8,["modelValue","label","options","error"])])]),r("div",N,[r("div",D,[d(b,{modelValue:t.isPublic,"onUpdate:modelValue":e[4]||(e[4]=n=>t.isPublic=n),name:"isPublic",label:o.$trans("communication.announcement.props.is_public"),error:i(s).isPublic,"onUpdate:error":e[5]||(e[5]=n=>i(s).isPublic=n)},null,8,["modelValue","label","error"])])]),!t.isPublic&&c.isLoaded?(_(),P(f,{key:0,"pre-requisites":p,studentAudienceType:t.studentAudienceType,"onUpdate:studentAudienceType":e[6]||(e[6]=n=>t.studentAudienceType=n),employeeAudienceType:t.employeeAudienceType,"onUpdate:employeeAudienceType":e[7]||(e[7]=n=>t.employeeAudienceType=n),studentAudiences:t.studentAudiences,"onUpdate:studentAudiences":e[8]||(e[8]=n=>t.studentAudiences=n),employeeAudiences:t.employeeAudiences,"onUpdate:employeeAudiences":e[9]||(e[9]=n=>t.employeeAudiences=n),formErrors:i(s),"onUpdate:formErrors":e[10]||(e[10]=n=>I(s)?s.value=n:null)},null,8,["pre-requisites","studentAudienceType","employeeAudienceType","studentAudiences","employeeAudiences","formErrors"])):R("",!0),r("div",G,[r("div",J,[d(l,{modelValue:t.description,"onUpdate:modelValue":e[11]||(e[11]=n=>t.description=n),name:"description",edit:!!i(m).params.uuid,label:o.$trans("communication.announcement.props.description"),error:i(s).description,"onUpdate:error":e[12]||(e[12]=n=>i(s).description=n)},null,8,["modelValue","edit","label","error"])]),r("div",z,[d(C,{multiple:"",label:o.$trans("general.file"),module:"announcement",media:t.media,"media-token":t.mediaToken,onIsUpdated:e[13]||(e[13]=n=>t.mediaUpdated=!0),onSetHash:e[14]||(e[14]=n=>t.mediaHash.push(n))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),W={name:"CommunicationAnnouncementAction"},Y=Object.assign(W,{setup($){const m=V();return(a,T)=>{const s=u("PageHeaderAction"),p=u("PageHeader"),t=u("ParentTransition");return _(),j(O,null,[d(p,{title:a.$trans(i(m).meta.trans,{attribute:a.$trans(i(m).meta.label)}),navs:[{label:a.$trans("communication.communication"),path:"Communication"},{label:a.$trans("communication.announcement.announcement"),path:"CommunicationAnnouncementList"}]},{default:v(()=>[d(s,{name:"CommunicationAnnouncement",title:a.$trans("communication.announcement.announcement"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(t,{appear:"",visibility:!0},{default:v(()=>[d(Q)]),_:1})],64)}}});export{Y as default};
