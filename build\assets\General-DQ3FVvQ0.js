import{u as y,j as b,H as A,c as F,l as d,r as e,a as j,o as x,e as t,f as s,w as o,d as _,s as B,t as q,F as R}from"./app-BAwPsakn.js";const C={class:"grid grid-cols-3 gap-4"},H={class:"col-span-3"},N={name:"ActivityConfigGeneral"},k=Object.assign(N,{setup(T){const u=y(),a=b("$trans"),n="config/";A(n);const p=F(()=>a("global.placeholder_info",{attribute:i.datePlaceholders})),i=d({datePlaceholders:""}),r={type:"activity"},m=d({...r}),f=c=>{Object.assign(i,{datePlaceholders:c.datePlaceholders.map(l=>l.value).join(", ")})};return(c,l)=>{const v=e("PageHeader"),g=e("BaseAlert"),h=e("FormAction"),P=e("ParentTransition");return x(),j(R,null,[t(v,{title:s(a)(s(u).meta.label),navs:[{label:s(a)("activity.activity"),path:"Activity"}]},null,8,["title","navs"]),t(P,{appear:"",visibility:!0},{default:o(()=>[t(h,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:f,"init-url":n,"data-fetch":"activity","init-form":r,form:m,action:"store","stay-on":"",redirect:"Activity"},{default:o(()=>[_("div",C,[_("div",H,[t(g,{size:"xs",design:"info"},{default:o(()=>[B(q(p.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{k as default};
