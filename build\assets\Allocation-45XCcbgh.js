import{u as X,h as Y,i as Z,D as I,m as x,l as R,H as ee,n as ae,r as y,a as l,o,q as U,b as f,e as m,w as p,d,f as r,F as h,v as C,s as w,t as v,J as oe,M as te}from"./app-BAwPsakn.js";const se={class:"divide-y divide-gray-200 dark:divide-gray-700"},le={key:0,class:"grid grid-cols-6 gap-2 px-4 py-2"},re={class:"col-span-6 sm:col-span-1"},ce={class:"col-span-6 sm:col-span-5"},ne={class:"flex flex-col sm:flex-row gap-2"},ue={class:"w-full"},me={key:0},ie={class:"col-span-6 sm:col-span-1"},pe={class:"text-info font-semibold"},de={class:"col-span-6 sm:col-span-5"},ye={key:0,class:"flex flex-col sm:flex-row gap-2"},be={class:"w-full"},_e={key:1,class:"flex justify-center"},$e=["onClick"],fe={class:"flex justify-center mt-2"},he=["onClick"],ve={key:1},ge={class:"flex justify-center"},je={name:"AcademicTimetableAllocation"},Ue=Object.assign(je,{setup(ke){const S=X();Y();const N=Z(),B={days:[]},{screenSize:D}=I(),A="academic/timetable/",g=x(!1),b=R({sessions:[],subjects:[],rooms:[],incharges:[]}),n=ee(A),k=R({...B}),T=R({}),H=e=>b.incharges.filter(_=>_.subjectUuid===e.uuid).map(_=>_.employee),q=async()=>{g.value=!0,await N.dispatch(A+"get",{uuid:S.params.uuid,params:{detail:!0}}).then(e=>{g.value=!1,b.sessions=e.days[0].sessions,Object.assign(T,e),Object.assign(B,e),Object.assign(k,oe(B))}).catch(e=>{g.value=!1})},L=async()=>{g.value=!0,await N.dispatch(A+"allocationPreRequisite",{uuid:S.params.uuid}).then(e=>{g.value=!1,Object.assign(b,e)}).catch(e=>{g.value=!1})},M=(e,_)=>{const V={subject:null,room:null,employee:null};k.days[e].sessions[_].allotments.push(V)},E=async(e,_,V)=>{await te()&&k.days[e].sessions[_].allotments.splice(V,1)};return ae(async()=>{await q(),await L()}),(e,_)=>{const V=y("PageHeaderAction"),z=y("PageHeader"),F=y("BaseDataView"),J=y("TextMuted"),G=y("BaseLabel"),j=y("BaseSelect"),K=y("BaseFieldset"),Q=y("FormAction"),W=y("ParentTransition");return o(),l(h,null,[T.batch?(o(),U(z,{key:0,title:e.$trans("academic.timetable.allocation")+" "+T.batch.course.name+" "+T.batch.name,navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.timetable.timetable"),path:"AcademicTimetable"}]},{default:p(()=>[m(V,{name:"AcademicTimetable",title:e.$trans("academic.timetable.timetable"),actions:[]},null,8,["title"])]),_:1},8,["title","navs"])):f("",!0),m(W,{appear:"",visibility:!0},{default:p(()=>[m(Q,{"no-data-fetch":!0,"init-url":A,"init-form":B,form:k,action:"allocation","keep-adding":!1,"stay-on":!0},{default:p(()=>[d("div",se,[r(D).small?f("",!0):(o(),l("div",le,[d("div",re,[m(F)]),d("div",ce,[d("div",ne,[(o(!0),l(h,null,C(b.sessions,$=>(o(),l("div",ue,[m(F,null,{default:p(()=>[w(v($.name)+" ",1),$.isBreak?(o(),l("span",me,"("+v(e.$trans("academic.class_timing.break"))+")",1)):f("",!0),m(J,{block:""},{default:p(()=>[w(v($.duration),1)]),_:2},1024)]),_:2},1024)]))),256))])])])),(o(!0),l(h,null,C(k.days,($,t)=>(o(),l("div",{class:"grid grid-cols-6 gap-2 px-2 py-2",key:$.value},[d("div",ie,[m(F,null,{default:p(()=>[d("span",pe,v($.label),1)]),_:2},1024)]),d("div",de,[$.sessions.length>0?(o(),l("div",ye,[(o(!0),l(h,null,C($.sessions,(c,s)=>{var O;return o(),l("div",be,[r(D).small?(o(),U(G,{key:0},{default:p(()=>[w(v(c.name),1)]),_:2},1024)):f("",!0),c.isBreak?(o(),l("div",_e,"-")):(o(),l(h,{key:2},[c.allotments.length>1?(o(!0),l(h,{key:0},C(c.allotments,(a,u)=>(o(),U(K,{key:u,class:"mt-2"},{legend:p(()=>[w(v(u+1)+" ",1),c.allotments.length>1?(o(),l("i",{key:0,class:"far fa-circle-xmark",onClick:P=>E(t,s,u)},null,8,$e)):f("",!0)]),default:p(()=>{var P;return[m(j,{modelValue:a.subject,"onUpdate:modelValue":i=>a.subject=i,name:`days.${t}.sessions.${s}.allotments.${u}.subject`,placeholder:e.$trans("academic.subject.subject"),"object-prop":!0,"label-prop":"name","value-prop":"uuid",options:b.subjects,error:r(n)[`days.${t}.sessions.${s}.allotments.${u}.subject`],"onUpdate:error":i=>r(n)[`days.${t}.sessions.${s}.allotments.${u}.subject`]=i},null,8,["modelValue","onUpdate:modelValue","name","placeholder","options","error","onUpdate:error"]),m(j,{name:`days.${t}.sessions.${s}.allotments.${u}.room`,placeholder:e.$trans("asset.building.room.room"),modelValue:a.room,"onUpdate:modelValue":i=>a.room=i,error:r(n)[`days.${t}.sessions.${s}.allotments.${u}.room`],"onUpdate:error":i=>r(n)[`days.${t}.sessions.${s}.allotments.${u}.room`]=i,"label-prop":"fullName","value-prop":"uuid",options:b.rooms},null,8,["name","placeholder","modelValue","onUpdate:modelValue","error","onUpdate:error","options"]),(P=a.subject)!=null&&P.uuid?(o(),U(j,{key:0,name:`days.${t}.sessions.${s}.allotments.${u}.employee`,placeholder:e.$trans("employee.employee"),modelValue:a.employee,"onUpdate:modelValue":i=>a.employee=i,error:r(n)[`days.${t}.sessions.${s}.allotments.${u}.employee`],"onUpdate:error":i=>r(n)[`days.${t}.sessions.${s}.allotments.${u}.employee`]=i,"object-prop":!0,"label-prop":"name","value-prop":"uuid",options:H(a.subject)},null,8,["name","placeholder","modelValue","onUpdate:modelValue","error","onUpdate:error","options"])):f("",!0)]}),_:2},1024))),128)):(o(),l(h,{key:1},[m(j,{modelValue:c.allotments[0].subject,"onUpdate:modelValue":a=>c.allotments[0].subject=a,name:`days.${t}.sessions.${s}.allotments.0.subject`,placeholder:e.$trans("academic.subject.subject"),"object-prop":!0,"label-prop":"name","value-prop":"uuid",options:b.subjects,error:r(n)[`days.${t}.sessions.${s}.allotments.0.subject`],"onUpdate:error":a=>r(n)[`days.${t}.sessions.${s}.allotments.0.subject`]=a},null,8,["modelValue","onUpdate:modelValue","name","placeholder","options","error","onUpdate:error"]),m(j,{name:`days.${t}.sessions.${s}.allotments.0.room`,placeholder:e.$trans("asset.building.room.room"),modelValue:c.allotments[0].room,"onUpdate:modelValue":a=>c.allotments[0].room=a,error:r(n)[`days.${t}.sessions.${s}.allotments.0.room`],"onUpdate:error":a=>r(n)[`days.${t}.sessions.${s}.allotments.0.room`]=a,"label-prop":"fullName","value-prop":"uuid",options:b.rooms},null,8,["name","placeholder","modelValue","onUpdate:modelValue","error","onUpdate:error","options"]),(O=c.allotments[0].subject)!=null&&O.uuid?(o(),U(j,{key:0,name:`days.${t}.sessions.${s}.allotments.0.employee`,placeholder:e.$trans("employee.employee"),modelValue:c.allotments[0].employee,"onUpdate:modelValue":a=>c.allotments[0].employee=a,error:r(n)[`days.${t}.sessions.${s}.allotments.0.employee`],"onUpdate:error":a=>r(n)[`days.${t}.sessions.${s}.allotments.0.employee`]=a,"object-prop":!0,"label-prop":"name","value-prop":"uuid",options:H(c.allotments[0].subject)},null,8,["name","placeholder","modelValue","onUpdate:modelValue","error","onUpdate:error","options"])):f("",!0)],64)),d("div",fe,[d("i",{class:"fas fa-plus-circle text-gray-800 dark:text-gray-200",onClick:a=>M(t,s)},null,8,he)])],64))])}),256))])):(o(),l("div",ve,[d("div",ge,[m(F,null,{default:p(()=>[w(v(e.$trans("academic.timetable.props.holiday")),1)]),_:1})])]))])]))),128))])]),_:1},8,["form"])]),_:1})],64)}}});export{Ue as default};
