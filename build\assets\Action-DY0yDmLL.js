import{H as $,l as b,c as B,r as p,q as U,o as f,w as _,d as m,a as g,b as P,e as i,f as a,J as j,u as q,F as A}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},T={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},C={key:0,class:"col-span-3 sm:col-span-1"},R={class:"col-span-3"},w={name:"FinanceFeeHeadForm"},E=Object.assign(w,{setup(y){const l={name:"",feeGroup:"",type:"",description:""},s="finance/feeHead/",r=$(s),u=b({feeGroups:[],types:[]}),o=b({...l}),c=B(()=>{if(!o.feeGroup)return!0;let t=u.feeGroups.find(e=>e.uuid===o.feeGroup);return!!(!t||t.isCustom)}),V=t=>{Object.assign(u,t)},v=t=>{var e,d;Object.assign(l,{...t,type:((e=t.type)==null?void 0:e.value)||"",feeGroup:((d=t.group)==null?void 0:d.uuid)||""}),Object.assign(o,j(l))};return(t,e)=>{const d=p("BaseInput"),F=p("BaseSelect"),G=p("BaseTextarea"),H=p("FormAction");return f(),U(H,{"pre-requisites":!0,onSetPreRequisites:V,"init-url":s,"init-form":l,form:o,setForm:v,redirect:"FinanceFeeHead"},{default:_(()=>[m("div",O,[m("div",T,[i(d,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=n=>o.name=n),name:"name",label:t.$trans("finance.fee_head.props.name"),error:a(r).name,"onUpdate:error":e[1]||(e[1]=n=>a(r).name=n),autofocus:""},null,8,["modelValue","label","error"])]),m("div",k,[i(F,{modelValue:o.feeGroup,"onUpdate:modelValue":e[2]||(e[2]=n=>o.feeGroup=n),name:"feeGroup",label:t.$trans("finance.fee_group.fee_group"),"label-prop":"name","value-prop":"uuid",options:u.feeGroups,error:a(r).feeGroup,"onUpdate:error":e[3]||(e[3]=n=>a(r).feeGroup=n)},null,8,["modelValue","label","options","error"])]),c.value?(f(),g("div",C,[i(F,{modelValue:o.type,"onUpdate:modelValue":e[4]||(e[4]=n=>o.type=n),name:"type",label:t.$trans("finance.fee_head.props.type"),options:u.types,error:a(r).type,"onUpdate:error":e[5]||(e[5]=n=>a(r).type=n)},null,8,["modelValue","label","options","error"])])):P("",!0),m("div",R,[i(G,{modelValue:o.description,"onUpdate:modelValue":e[6]||(e[6]=n=>o.description=n),name:"description",label:t.$trans("finance.fee_head.props.description"),error:a(r).description,"onUpdate:error":e[7]||(e[7]=n=>a(r).description=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),N={name:"FinanceFeeHeadAction"},h=Object.assign(N,{setup(y){const l=q();return(s,r)=>{const u=p("PageHeaderAction"),o=p("PageHeader"),c=p("ParentTransition");return f(),g(A,null,[i(o,{title:s.$trans(a(l).meta.trans,{attribute:s.$trans(a(l).meta.label)}),navs:[{label:s.$trans("finance.finance"),path:"Finance"},{label:s.$trans("finance.fee_head.fee_head"),path:"FinanceFeeHeadList"}]},{default:_(()=>[i(u,{name:"FinanceFeeHead",title:s.$trans("finance.fee_head.fee_head"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(c,{appear:"",visibility:!0},{default:_(()=>[i(E)]),_:1})],64)}}});export{h as default};
