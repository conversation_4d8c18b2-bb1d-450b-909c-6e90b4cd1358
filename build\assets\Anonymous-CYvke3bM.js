import{u as A,h as H,i as L,g as y,H as j,m as $,l as q,n as M,r as m,a as E,o as c,d as s,e as r,q as g,b as _,f as b,s as n,t as o,w as l,F as O}from"./app-BAwPsakn.js";import{_ as R}from"./OnlinePaymentForm-hfFN_8uD.js";import"./Billdesk-CH1h7WlK.js";const S={class:"flex justify-end"},x={class:"flex justify-center"},T={href:"/",class:"mb-6"},U=["src"],z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-4"},J={class:"mt-4 space-x-4"},K={name:"AnonymousPayment"},Z=Object.assign(K,{setup(Q){const h=A(),G=H(),k=L(),v="guestPayment/",N=y("layout.display").value=="dark"?y("assets.iconLight"):y("assets.icon");j(v);const i=$(!1),f=$(!1),e=q({date:{},amount:0,student:{},transaction:{},feeGroup:null,feeHead:null,feeInstallment:null,paymentGateways:[],hasCompleted:!1}),I=async()=>{i.value=!0,await k.dispatch(v+"getAnonymousPaymentDetail",{form:{uuid:h.params.uuid}}).then(t=>{e.student=t.student,e.amount=t.amount,e.date=t.date,e.transaction=t.transaction,e.feeGroup=t.feeGroup,e.feeHead=t.feeHead,e.feeInstallment=t.feeInstallment,e.paymentGateways=t.paymentGateways,i.value=!1}).catch(t=>{i.value=!1})},B=()=>{f.value=!1,e.feeInstallment={},e.feeGroup={}},D=()=>{e.feeInstallment={},e.feeGroup={},e.transaction.hasCompleted=!0,B()};return M(()=>{I()}),(t,u)=>{const d=m("BaseDataView"),P=m("BaseButton"),V=m("BaseAlert"),F=m("BaseLoader");return c(),E(O,null,[s("div",S,[s("span",{class:"text-sm text-gray-500 cursor-pointer",onClick:u[0]||(u[0]=p=>b(G).push({name:"Dashboard"}))},[u[2]||(u[2]=s("i",{class:"fas fa-home mr-1"},null,-1)),n(" "+o(t.$trans("global.go_to",{attribute:t.$trans("dashboard.home")})),1)])]),s("div",x,[s("a",T,[s("img",{class:"h-16 w-auto",src:b(N),alt:""},null,8,U)])]),r(F,{"is-loading":i.value},{default:l(()=>{var p,C;return[s("dl",z,[r(d,{label:t.$trans("student.admission.props.code_number")},{default:l(()=>{var a;return[n(o((a=e.student)==null?void 0:a.codeNumber),1)]}),_:1},8,["label"]),r(d,{label:t.$trans("student.props.name")},{default:l(()=>{var a;return[n(o((a=e.student)==null?void 0:a.name),1)]}),_:1},8,["label"]),r(d,{label:t.$trans("academic.course.course")},{default:l(()=>{var a,w;return[n(o(((a=e.student)==null?void 0:a.courseName)+" "+((w=e.student)==null?void 0:w.batchName)),1)]}),_:1},8,["label"]),r(d,{label:t.$trans("finance.transaction.props.amount")},{default:l(()=>[n(o(e.amount.formatted),1)]),_:1},8,["label"])]),s("div",J,[(p=e.transaction)!=null&&p.hasCompleted?_("",!0):(c(),g(P,{key:0,block:"",onClick:u[1]||(u[1]=a=>f.value=!0)},{default:l(()=>[n(o(t.$trans("general.proceed")),1)]),_:1})),(C=e.transaction)!=null&&C.hasCompleted?(c(),g(V,{key:1,design:"success"},{default:l(()=>[n(o(e.transaction.referenceNumber?t.$trans("student.fee.paid_online",{reference:e.transaction.referenceNumber,amount:e.amount.formatted}):t.$trans("student.payment.payment_completed")),1)]),_:1})):_("",!0)])]}),_:1},8,["is-loading"]),e.student.uuid?(c(),g(R,{key:0,"init-url":"guestPayment/",visibility:f.value,uuid:e.student.uuid,"pre-requisites":{paymentGateways:e.paymentGateways},"fee-group":e.feeGroup,fee:e.feeInstallment,date:e.date,"temp-payment-uuid":b(h).params.uuid,onClose:B,onCompleted:D},null,8,["visibility","uuid","pre-requisites","fee-group","fee","date","temp-payment-uuid"])):_("",!0)],64)}}});export{Z as default};
