import{u as H,l as D,n as L,r as o,q as k,o as f,w as e,d as I,e as t,h as U,j as A,y,m as O,f as r,a as E,F as z,v as G,s as m,t as c,b as B}from"./app-BAwPsakn.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(V,{emit:v}){H();const g=v,b=V,$={codeNumber:"",startDate:"",endDate:"",inventory:"",places:[]},i=D({...$}),_=D({inventories:b.preRequisites.inventories,places:b.preRequisites.places}),C=D({isLoaded:!0});return L(async()=>{C.isLoaded=!0}),(p,s)=>{const F=o("BaseInput"),n=o("BaseSelect"),l=o("DatePicker"),S=o("FilterForm");return f(),k(S,{"init-form":$,form:i,multiple:["places"],onHide:s[5]||(s[5]=u=>g("hide"))},{default:e(()=>[I("div",J,[I("div",K,[t(F,{type:"text",modelValue:i.codeNumber,"onUpdate:modelValue":s[0]||(s[0]=u=>i.codeNumber=u),name:"codeNumber",label:p.$trans("inventory.stock_adjustment.props.code_number")},null,8,["modelValue","label"])]),I("div",Q,[t(n,{modelValue:i.inventory,"onUpdate:modelValue":s[1]||(s[1]=u=>i.inventory=u),name:"inventory","label-prop":"name","value-prop":"uuid",label:p.$trans("inventory.inventory"),options:_.inventories},null,8,["modelValue","label","options"])]),I("div",W,[t(n,{multiple:"",modelValue:i.places,"onUpdate:modelValue":s[2]||(s[2]=u=>i.places=u),name:"places","label-prop":"fullName","value-prop":"uuid",label:p.$trans("inventory.place"),options:_.places},null,8,["modelValue","label","options"])]),I("div",X,[t(l,{start:i.startDate,"onUpdate:start":s[3]||(s[3]=u=>i.startDate=u),end:i.endDate,"onUpdate:end":s[4]||(s[4]=u=>i.endDate=u),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Z={name:"InventoryStockAdjustmentList"},ee=Object.assign(Z,{setup(V){const v=U(),g=A("emitter");let b=["filter"];y("inventory:config")&&b.push("config"),y("stock-adjustment:create")&&b.unshift("create");let $=[];y("stock-adjustment:export")&&($=["print","pdf","excel"]);const i="inventory/stockAdjustment/",_=O(!1),C=D({inventories:[],places:[]}),p=D({}),s=n=>{Object.assign(C,n)},F=n=>{Object.assign(p,n)};return(n,l)=>{const S=o("PageHeaderAction"),u=o("PageHeader"),N=o("ParentTransition"),R=o("TextMuted"),j=o("DataCell"),w=o("FloatingMenuItem"),h=o("FloatingMenu"),q=o("DataRow"),P=o("BaseButton"),T=o("DataTable"),M=o("ListItem");return f(),k(M,{"init-url":i,"pre-requisites":!0,onSetPreRequisites:s,onSetItems:F},{header:e(()=>[t(u,{title:n.$trans("inventory.stock_adjustment.stock_adjustment"),navs:[{label:n.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[t(S,{url:"inventory/stock-adjustments/",name:"InventoryStockAdjustment",title:n.$trans("inventory.stock_adjustment.stock_adjustment"),actions:r(b),"dropdown-actions":r($),"config-path":"InventoryConfig",onToggleFilter:l[0]||(l[0]=a=>_.value=!_.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(N,{appear:"",visibility:_.value},{default:e(()=>[t(Y,{onRefresh:l[1]||(l[1]=a=>r(g).emit("listItems")),"pre-requisites":C,onHide:l[2]||(l[2]=a=>_.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(N,{appear:"",visibility:!0},{default:e(()=>[t(T,{header:p.headers,meta:p.meta,module:"inventory.stock_adjustment",onRefresh:l[4]||(l[4]=a=>r(g).emit("listItems"))},{actionButton:e(()=>[r(y)("stock-adjustment:create")?(f(),k(P,{key:0,onClick:l[3]||(l[3]=a=>r(v).push({name:"InventoryStockAdjustmentCreate"}))},{default:e(()=>[m(c(n.$trans("global.add",{attribute:n.$trans("inventory.stock_adjustment.stock_adjustment")})),1)]),_:1})):B("",!0)]),default:e(()=>[(f(!0),E(z,null,G(p.data,a=>(f(),k(q,{key:a.uuid,onDoubleClick:d=>r(v).push({name:"InventoryStockAdjustmentShow",params:{uuid:a.uuid}})},{default:e(()=>[t(j,{name:"codeNumber"},{default:e(()=>[m(c(a.codeNumber)+" ",1),t(R,{block:""},{default:e(()=>{var d;return[m(c((d=a.inventory)==null?void 0:d.name),1)]}),_:2},1024)]),_:2},1024),t(j,{name:"date"},{default:e(()=>[m(c(a.date.formatted),1)]),_:2},1024),t(j,{name:"place"},{default:e(()=>{var d;return[m(c(((d=a.place)==null?void 0:d.fullName)||"-"),1)]}),_:2},1024),t(j,{name:"createdAt"},{default:e(()=>[m(c(a.createdAt.formatted),1)]),_:2},1024),t(j,{name:"action"},{default:e(()=>[t(h,null,{default:e(()=>[t(w,{icon:"fas fa-arrow-circle-right",onClick:d=>r(v).push({name:"InventoryStockAdjustmentShow",params:{uuid:a.uuid}})},{default:e(()=>[m(c(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),r(y)("stock-adjustment:edit")?(f(),k(w,{key:0,icon:"fas fa-edit",onClick:d=>r(v).push({name:"InventoryStockAdjustmentEdit",params:{uuid:a.uuid}})},{default:e(()=>[m(c(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):B("",!0),r(y)("stock-adjustment:create")?(f(),k(w,{key:1,icon:"fas fa-copy",onClick:d=>r(v).push({name:"InventoryStockAdjustmentDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[m(c(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):B("",!0),r(y)("stock-adjustment:delete")?(f(),k(w,{key:2,icon:"fas fa-trash",onClick:d=>r(g).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[m(c(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):B("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ee as default};
