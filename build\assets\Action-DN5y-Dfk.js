import{u as T,G as g,H as R,l as _,r as n,q as B,o as q,w as b,d as i,e as l,f as d,J as E,a as H,F as L}from"./app-BAwPsakn.js";const V={class:"grid grid-cols-2 gap-6"},j={class:"col-span-2 sm:col-span-1"},A={class:"col-span-2 sm:col-span-1"},O={class:"col-span-2"},w={class:"grid grid-cols-1"},M={class:"col"},S={name:"EmployeeLeaveRequestForm"},C=Object.assign(S,{setup(D){const p=T(),o={leaveType:"",startDate:"",endDate:"",reason:"",media:[],mediaUpdated:!1,mediaToken:g(),mediaHash:[]},y="employee/leave/request/",r=R(y),m=_({types:[]}),t=_({...o}),f=_({leaveType:"",isLoaded:!p.params.uuid}),$=s=>{Object.assign(m,s)},U=()=>{t.mediaToken=g(),t.mediaHash=[]},k=s=>{var e,u,c,v;Object.assign(o,{...s,startDate:((e=s.startDate)==null?void 0:e.value)||"",endDate:((u=s.endDate)==null?void 0:u.value)||"",leaveType:(c=s.leaveType)==null?void 0:c.uuid}),Object.assign(t,E(o)),f.leaveType=(v=s.leaveType)==null?void 0:v.uuid,f.isLoaded=!0};return(s,e)=>{const u=n("BaseSelect"),c=n("DatePicker"),v=n("BaseTextarea"),F=n("MediaUpload"),P=n("FormAction");return q(),B(P,{"pre-requisites":!0,onSetPreRequisites:$,"init-url":y,"init-form":o,form:t,"set-form":k,redirect:"EmployeeLeaveRequest",onResetMediaFiles:U},{default:b(()=>[i("div",V,[i("div",j,[l(u,{modelValue:t.leaveType,"onUpdate:modelValue":e[0]||(e[0]=a=>t.leaveType=a),name:"leaveType",label:s.$trans("global.select",{attribute:s.$trans("employee.leave.type.type")}),options:m.types,"label-prop":"name","value-prop":"uuid",error:d(r).leaveType,"onUpdate:error":e[1]||(e[1]=a=>d(r).leaveType=a)},null,8,["modelValue","label","options","error"])]),i("div",A,[l(c,{start:t.startDate,"onUpdate:start":e[2]||(e[2]=a=>t.startDate=a),end:t.endDate,"onUpdate:end":e[3]||(e[3]=a=>t.endDate=a),name:"dateBetween",as:"range",label:s.$trans("general.date_between")},null,8,["start","end","label"])]),i("div",O,[l(v,{modelValue:t.reason,"onUpdate:modelValue":e[4]||(e[4]=a=>t.reason=a),name:"reason",label:s.$trans("employee.leave.request.props.reason"),error:d(r).reason,"onUpdate:error":e[5]||(e[5]=a=>d(r).reason=a)},null,8,["modelValue","label","error"])])]),i("div",w,[i("div",M,[l(F,{multiple:"",label:s.$trans("general.file"),module:"leave_request",media:t.media,"media-token":t.mediaToken,onIsUpdated:e[6]||(e[6]=a=>t.mediaUpdated=!0),onSetHash:e[7]||(e[7]=a=>t.mediaHash.push(a))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),N={name:"EmployeeLeaveRequestAction"},G=Object.assign(N,{setup(D){const p=T();return(o,y)=>{const r=n("PageHeaderAction"),m=n("PageHeader"),t=n("ParentTransition");return q(),H(L,null,[l(m,{title:o.$trans(d(p).meta.trans,{attribute:o.$trans(d(p).meta.label)}),navs:[{label:o.$trans("employee.leave.leave"),path:"EmployeeLeave"},{label:o.$trans("employee.leave.request.request"),path:"EmployeeLeaveRequestList"}]},{default:b(()=>[l(r,{name:"EmployeeLeaveRequest",title:o.$trans("employee.leave.request.request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(t,{appear:"",visibility:!0},{default:b(()=>[l(C)]),_:1})],64)}}});export{G as default};
