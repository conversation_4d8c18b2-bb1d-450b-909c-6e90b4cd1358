import{u as k,j as v,H as b,l as c,r as a,a as E,o as P,e as o,f as e,w as p,d as u,F as B}from"./app-BAwPsakn.js";const V={class:"grid grid-cols-3 gap-4"},y={class:"col-span-3 sm:col-span-1"},j={name:"ExamConfigGeneral"},H=Object.assign(j,{setup(q){const d=k(),r=v("$trans"),m="config/",i=b(m);c({});const l={marksheetFormat:"",type:"exam"},s=c({...l}),_=f=>{};return(f,t)=>{const x=a("PageHeader"),F=a("BaseInput"),g=a("FormAction"),h=a("ParentTransition");return P(),E(B,null,[o(x,{title:e(r)(e(d).meta.label),navs:[{label:e(r)("exam.exam"),path:"Exam"}]},null,8,["title","navs"]),o(h,{appear:"",visibility:!0},{default:p(()=>[o(g,{"pre-requisites":!1,onSetPreRequisites:_,"init-url":m,"data-fetch":"exam","init-form":l,form:s,action:"store","stay-on":"",redirect:"Exam"},{default:p(()=>[u("div",V,[u("div",y,[o(F,{type:"text",modelValue:s.marksheetFormat,"onUpdate:modelValue":t[0]||(t[0]=n=>s.marksheetFormat=n),name:"marksheetFormat",label:e(r)("exam.config.props.marksheet_format"),error:e(i).marksheetFormat,"onUpdate:error":t[1]||(t[1]=n=>e(i).marksheetFormat=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{H as default};
