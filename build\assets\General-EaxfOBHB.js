import{u as S,j as y,H as B,c as D,l as b,r as i,a as U,o as C,e as a,f as n,w as l,d as m,s as F,t as j,F as h}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3"},I={name:"CommunicationConfigGeneral"},w=Object.assign(I,{setup(T){const _=S(),r=y("$trans"),c="config/",s=B(c),g=D(()=>r("global.placeholder_info",{attribute:d.datePlaceholders})),d=b({datePlaceholders:""}),p={announcementNumberPrefix:"",announcementNumberSuffix:"",announcementNumberDigit:0,type:"communication"},t=b({...p}),x=f=>{Object.assign(d,{datePlaceholders:f.datePlaceholders.map(e=>e.value).join(", ")})};return(f,e)=>{const N=i("PageHeader"),u=i("BaseInput"),P=i("BaseAlert"),v=i("FormAction"),V=i("ParentTransition");return C(),U(h,null,[a(N,{title:n(r)(n(_).meta.label),navs:[{label:n(r)("communication.communication"),path:"Communication"}]},null,8,["title","navs"]),a(V,{appear:"",visibility:!0},{default:l(()=>[a(v,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:x,"init-url":c,"data-fetch":"communication","init-form":p,form:t,action:"store","stay-on":"",redirect:"Communication"},{default:l(()=>[m("div",q,[m("div",A,[a(u,{type:"text",modelValue:t.announcementNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=o=>t.announcementNumberPrefix=o),name:"announcementNumberPrefix",label:n(r)("communication.announcement.config.props.number_prefix"),error:n(s).announcementNumberPrefix,"onUpdate:error":e[1]||(e[1]=o=>n(s).announcementNumberPrefix=o)},null,8,["modelValue","label","error"])]),m("div",R,[a(u,{type:"number",modelValue:t.announcementNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=o=>t.announcementNumberDigit=o),name:"announcementNumberDigit",label:n(r)("communication.announcement.config.props.number_digit"),error:n(s).announcementNumberDigit,"onUpdate:error":e[3]||(e[3]=o=>n(s).announcementNumberDigit=o)},null,8,["modelValue","label","error"])]),m("div",E,[a(u,{type:"text",modelValue:t.announcementNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=o=>t.announcementNumberSuffix=o),name:"announcementNumberSuffix",label:n(r)("communication.announcement.config.props.number_suffix"),error:n(s).announcementNumberSuffix,"onUpdate:error":e[5]||(e[5]=o=>n(s).announcementNumberSuffix=o)},null,8,["modelValue","label","error"])]),m("div",H,[a(P,{size:"xs",design:"info"},{default:l(()=>[F(j(g.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{w as default};
