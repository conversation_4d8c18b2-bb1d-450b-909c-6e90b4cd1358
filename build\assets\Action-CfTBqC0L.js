import{H as A,l as f,r as m,q as U,o as V,w as b,d as i,e as n,f as o,J as B,u as F,a as P,F as D}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},y={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3"},T={name:"AcademicDepartmentForm"},k=Object.assign(T,{setup(_){const d={name:"",code:"",shortcode:"",alias:"",description:""},l="academic/department/",r=A(l),c=f({}),t=f({...d}),u=s=>{Object.assign(c,s)},g=s=>{Object.assign(d,{...s}),Object.assign(t,B(d))};return(s,e)=>{const p=m("BaseInput"),$=m("BaseTextarea"),v=m("FormAction");return V(),U(v,{"has-setup-wizard":!1,"pre-requisites":!1,onSetPreRequisites:u,"init-url":l,"init-form":d,form:t,"set-form":g,redirect:"AcademicDepartment"},{default:b(()=>[i("div",j,[i("div",q,[n(p,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=a=>t.name=a),name:"name",label:s.$trans("academic.department.props.name"),error:o(r).name,"onUpdate:error":e[1]||(e[1]=a=>o(r).name=a)},null,8,["modelValue","label","error"])]),i("div",y,[n(p,{type:"text",modelValue:t.code,"onUpdate:modelValue":e[2]||(e[2]=a=>t.code=a),name:"code",label:s.$trans("academic.department.props.code"),error:o(r).code,"onUpdate:error":e[3]||(e[3]=a=>o(r).code=a)},null,8,["modelValue","label","error"])]),i("div",H,[n(p,{type:"text",modelValue:t.shortcode,"onUpdate:modelValue":e[4]||(e[4]=a=>t.shortcode=a),name:"shortcode",label:s.$trans("academic.department.props.shortcode"),error:o(r).shortcode,"onUpdate:error":e[5]||(e[5]=a=>o(r).shortcode=a)},null,8,["modelValue","label","error"])]),i("div",O,[n(p,{type:"text",modelValue:t.alias,"onUpdate:modelValue":e[6]||(e[6]=a=>t.alias=a),name:"alias",label:s.$trans("academic.department.props.alias"),error:o(r).alias,"onUpdate:error":e[7]||(e[7]=a=>o(r).alias=a)},null,8,["modelValue","label","error"])]),i("div",R,[n($,{modelValue:t.description,"onUpdate:modelValue":e[8]||(e[8]=a=>t.description=a),name:"description",label:s.$trans("academic.department.props.description"),error:o(r).description,"onUpdate:error":e[9]||(e[9]=a=>o(r).description=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),w={name:"AcademicDepartmentAction"},C=Object.assign(w,{setup(_){const d=F();return(l,r)=>{const c=m("PageHeaderAction"),t=m("PageHeader"),u=m("ParentTransition");return V(),P(D,null,[n(t,{title:l.$trans(o(d).meta.trans,{attribute:l.$trans(o(d).meta.label)}),navs:[{label:l.$trans("academic.academic"),path:"Academic"},{label:l.$trans("academic.department.department"),path:"AcademicDepartmentList"}]},{default:b(()=>[n(c,{name:"AcademicDepartment",title:l.$trans("academic.department.department"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(u,{appear:"",visibility:!0},{default:b(()=>[n(k)]),_:1})],64)}}});export{C as default};
