import{u as y,G as B,H as q,l as $,n as C,r as c,q as H,o as F,w as u,d,e as n,f as r,s as k,t as x,M as T,J as L,a as S,F as D}from"./app-BAwPsakn.js";import{d as N}from"./vuedraggable.umd-BRYqknf6.js";const h={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3"},z=["onClick"],K={class:"mt-4 grid grid-cols-4 gap-4"},Q={class:"col-span-4 sm:col-span-1"},W={class:"col-span-4 sm:col-span-1"},X={class:"col-span-4 sm:col-span-1"},Y={class:"mt-4"},Z={name:"ExamObservationForm"},ee=Object.assign(Z,{setup(O){const b=y(),m={name:"",description:"",records:[]},v={uuid:B(),name:"",code:"",maxMark:"",description:""},g="exam/observation/",s=q(g),_=$({grades:[]}),t=$({...m}),V=$({isLoaded:!b.params.uuid}),E=o=>{Object.assign(_,o)},U=()=>{t.records.push({...v,uuid:B()}),V.isLoaded=!0},M=async o=>{await T()&&(t.records.length==1?t.records=[v]:t.records.splice(o,1))},P=o=>{var p;let e=o.records.map(f=>({...f}));Object.assign(m,{...o,grade:((p=o.grade)==null?void 0:p.uuid)||"",records:e}),Object.assign(t,L(m)),V.isLoaded=!0};return C(async()=>{b.params.uuid||U()}),(o,e)=>{const p=c("BaseInput"),f=c("BaseSelect"),w=c("BaseTextarea"),R=c("BaseFieldset"),A=c("BaseBadge"),j=c("FormAction");return F(),H(j,{"pre-requisites":!0,onSetPreRequisites:E,"init-url":g,"init-form":m,form:t,"set-form":P,redirect:"ExamObservation"},{default:u(()=>[d("div",h,[d("div",I,[n(p,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=a=>t.name=a),name:"name",label:o.$trans("exam.observation.props.name"),error:r(s).name,"onUpdate:error":e[1]||(e[1]=a=>r(s).name=a)},null,8,["modelValue","label","error"])]),d("div",G,[n(f,{modelValue:t.grade,"onUpdate:modelValue":e[2]||(e[2]=a=>t.grade=a),name:"grade",label:o.$trans("exam.grade.grade"),options:_.grades,"label-prop":"name","value-prop":"uuid",error:r(s).grade,"onUpdate:error":e[3]||(e[3]=a=>r(s).grade=a)},null,8,["modelValue","label","options","error"])]),d("div",J,[n(w,{rows:1,modelValue:t.description,"onUpdate:modelValue":e[4]||(e[4]=a=>t.description=a),name:"description",label:o.$trans("exam.observation.props.description"),error:r(s).description,"onUpdate:error":e[5]||(e[5]=a=>r(s).description=a)},null,8,["modelValue","label","error"])])]),n(r(N),{list:t.records,"item-key":"uuid"},{item:u(({element:a,index:i})=>[n(R,{class:"mt-4"},{legend:u(()=>[e[7]||(e[7]=d("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),k(" "+x(i+1)+". ",1),d("span",{class:"text-danger ml-2 cursor-pointer",onClick:l=>M(i)},e[6]||(e[6]=[d("i",{class:"fas fa-times-circle"},null,-1)]),8,z)]),default:u(()=>[d("div",K,[d("div",Q,[n(p,{type:"text",modelValue:a.name,"onUpdate:modelValue":l=>a.name=l,name:`records.${i}.name`,label:o.$trans("exam.observation.props.name"),error:r(s)[`records.${i}.name`],"onUpdate:error":l=>r(s)[`records.${i}.name`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",W,[n(p,{type:"text",modelValue:a.code,"onUpdate:modelValue":l=>a.code=l,name:`records.${i}.code`,label:o.$trans("exam.observation.props.code"),error:r(s)[`records.${i}.code`],"onUpdate:error":l=>r(s)[`records.${i}.code`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",X,[n(p,{type:"number",modelValue:a.maxMark,"onUpdate:modelValue":l=>a.maxMark=l,name:`records.${i}.maxMark`,label:o.$trans("exam.observation.props.max_mark"),error:r(s)[`records.${i}.maxMark`],"onUpdate:error":l=>r(s)[`records.${i}.maxMark`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024)]),_:1},8,["list"]),d("div",Y,[n(A,{design:"primary",onClick:U,class:"cursor-pointer"},{default:u(()=>[k(x(o.$trans("global.add",{attribute:o.$trans("general.record")})),1)]),_:1})])]),_:1},8,["form"])}}}),ae={name:"ExamObservationAction"},se=Object.assign(ae,{setup(O){const b=y();return(m,v)=>{const g=c("PageHeaderAction"),s=c("PageHeader"),_=c("ParentTransition");return F(),S(D,null,[n(s,{title:m.$trans(r(b).meta.trans,{attribute:m.$trans(r(b).meta.label)}),navs:[{label:m.$trans("exam.exam"),path:"Exam"},{label:m.$trans("exam.observation.observation"),path:"ExamObservationList"}]},{default:u(()=>[n(g,{name:"ExamObservation",title:m.$trans("exam.observation.observation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(_,{appear:"",visibility:!0},{default:u(()=>[n(ee)]),_:1})],64)}}});export{se as default};
