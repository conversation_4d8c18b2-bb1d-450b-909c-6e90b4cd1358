import{a as s,o as t,d as e,h as O,i as U,j as V,k as q,c as S,l as X,m as H,n as Z,p as Y,r as m,q as T,w as x,b as i,s as P,t as u,F as w,v as ee,x as N,e as c,f as r,g as v,y as te,z as se,A as F,B as K,u as ne,C as ae,D as oe,E as re}from"./app-BAwPsakn.js";function ie(B,p){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"fill-rule":"evenodd",d:"M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Zm0 5.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z","clip-rule":"evenodd"})])}function le(B,p,d){window.Echo&&window.Echo.private(B).listen("."+p,y=>{d(y)})}const ce={class:"space-y-2"},de={key:1,class:"text-sm text-gray-800 dark:text-gray-400"},ue={"aria-label":"Progress"},pe={role:"list",class:"mt-4 overflow-hidden"},me={key:0,class:"absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-primary dark:bg-dark-body","aria-hidden":"true"},he=["onClick"],fe={class:"ml-4 flex min-w-0 flex-col"},_e={class:"text-sm font-medium dark:text-gray-400"},ye={key:0,class:"text-sm font-medium"},ge={class:"text-sm text-gray-500"},ve={class:"mt-2 flex justify-end"},be={key:0,class:"absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300","aria-hidden":"true"},ke=["onClick"],xe={class:"ml-4 flex min-w-0 flex-col"},we={class:"text-sm font-medium text-gray-800 dark:text-gray-200"},Se={key:0,class:"text-sm font-medium"},Ce={class:"text-sm text-gray-500"},$e={class:"mt-2 flex justify-end"},ze={key:0,class:"absolute left-4 top-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300","aria-hidden":"true"},Ae=["onClick"],Be={class:"ml-4 flex min-w-0 flex-col"},Me={class:"text-sm font-medium text-gray-500"},Pe={key:0,class:"text-sm font-medium"},je={class:"text-sm text-gray-500"},Le={class:"mt-2 flex justify-end"},Te={key:2},Ne={key:0,class:"mt-4 text-sm text-gray-800 dark:text-gray-400"},We={key:3},De={key:0,class:"mt-4 text-sm text-gray-800 dark:text-gray-400"},Ue={name:"SetupWizard"},Re=Object.assign(Ue,{setup(B){const p=O(),d=U(),y=V("emitter");q("profile.name");const C=S(()=>l.steps.every(o=>o.isCompleted)),h=H(null),b=H(!1),l=X({header:"",footer:"",completed:"",steps:[]}),k=o=>{h.value=o},$=async()=>{b.value=!0,await d.dispatch("setup/wizard/getSteps").then(o=>{b.value=!1,Object.assign(l,o),h.value=l.steps.find(g=>!g.isCompleted)}).catch(o=>{b.value=!1})},_=async()=>{await d.dispatch("config/store",{form:{type:"system",showSetupWizard:!1}}).then(o=>{d.dispatch("config/get"),p.push({name:"Dashboard"})}).catch(o=>{})};return Z(async()=>{await $(),y.on("refreshSetupWizard",()=>{$()})}),Y(()=>{y.all.delete("refreshSetupWizard")}),(o,g)=>{const j=m("BaseAlert"),z=m("BaseButton"),L=m("BaseCard");return t(),T(L,{"is-loading":b.value},{title:x(()=>[P(u(o.$trans("config.system.props.setup_wizard")),1)]),action:x(()=>[e("i",{class:"fas fa-times fa-lg text-gray-500 cursor-pointer",onClick:_})]),default:x(()=>[e("div",ce,[l.steps.length==0?(t(),T(j,{key:0,size:"xs",design:"info"},{default:x(()=>[P(u(o.$trans("config.system.setup_wizard_not_available")),1)]),_:1})):i("",!0),l.header?(t(),s("p",de,u(l.header),1)):i("",!0),e("nav",ue,[e("ol",pe,[(t(!0),s(w,null,ee(l.steps,(n,M)=>{var f,a,W,D;return t(),s("li",{key:n.name,class:N([M!==l.steps.length-1?"pb-10":"","relative"])},[n.isCompleted?(t(),s(w,{key:0},[M!==l.steps.length-1?(t(),s("div",me)):i("",!0),e("span",{onClick:A=>k(n),class:"group relative flex items-center cursor-pointer"},[g[0]||(g[0]=e("span",{class:"flex h-9 items-center"},[e("span",{class:"relative z-10 flex h-8 w-8 items-center justify-center rounded-full bg-primary dark:bg-dark-body group-hover:bg-primary dark:group-hover:bg-dark-body"},[e("i",{class:"fas fa-check text-white"})])],-1)),e("span",fe,[e("span",_e,u(n.title),1),n.name==((f=h.value)==null?void 0:f.name)?(t(),s(w,{key:0},[n.summary?(t(),s("span",ye,u(n.summary),1)):i("",!0),e("span",ge,u(n.description),1),e("div",ve,[c(z,{size:"xs",onClick:A=>r(p).push({name:n.route})},{default:x(()=>[P(u(o.$trans("general.proceed")),1)]),_:2},1032,["onClick"])])],64)):i("",!0)])],8,he)],64)):n.name==((a=h.value)==null?void 0:a.name)?(t(),s(w,{key:1},[M!==l.steps.length-1?(t(),s("div",be)):i("",!0),e("span",{onClick:A=>k(n),class:"cursor-pointer group relative flex items-start","aria-current":"step"},[g[1]||(g[1]=e("span",{class:"flex h-9 items-center","aria-hidden":"true"},[e("span",{class:"relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-primary dark:border-dark-body bg-white"},[e("span",{class:"h-2.5 w-2.5 rounded-full bg-primary"})])],-1)),e("span",xe,[e("span",we,u(n.title),1),n.name==((W=h.value)==null?void 0:W.name)?(t(),s(w,{key:0},[n.summary?(t(),s("span",Se,u(n.summary),1)):i("",!0),e("span",Ce,u(n.description),1),e("div",$e,[c(z,{size:"xs",onClick:A=>r(p).push({name:n.route})},{default:x(()=>[P(u(o.$trans("general.proceed")),1)]),_:2},1032,["onClick"])])],64)):i("",!0)])],8,ke)],64)):(t(),s(w,{key:2},[M!==l.steps.length-1?(t(),s("div",ze)):i("",!0),e("span",{onClick:A=>k(n),class:"cursor-pointer group relative flex items-center"},[g[2]||(g[2]=e("span",{class:"flex h-9 items-center","aria-hidden":"true"},[e("span",{class:"relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 border-gray-300 bg-white group-hover:border-gray-400"},[e("span",{class:"h-2.5 w-2.5 rounded-full bg-transparent group-hover:bg-gray-300"})])],-1)),e("span",Be,[e("span",Me,u(n.title),1),n.name==((D=h.value)==null?void 0:D.name)?(t(),s(w,{key:0},[n.summary?(t(),s("span",Pe,u(n.summary),1)):i("",!0),e("span",je,u(n.description),1),e("div",Le,[c(z,{size:"xs",onClick:A=>r(p).push({name:n.route})},{default:x(()=>[P(u(o.$trans("general.proceed")),1)]),_:2},1032,["onClick"])])],64)):i("",!0)])],8,Ae)],64))],2)}),128))])]),C.value?(t(),s("div",Te,[l.completed?(t(),s("p",Ne,u(l.completed),1)):i("",!0),c(z,{block:"",size:"sm",onClick:_,class:"mt-2"},{default:x(()=>[P(u(o.$trans("global.hide",{attribute:o.$trans("config.system.props.setup_wizard")})),1)]),_:1})])):(t(),s("div",We,[l.footer?(t(),s("p",De,u(l.footer),1)):i("",!0)]))])]),_:1},8,["is-loading"])}}}),Ee={class:"hidden lg:flex lg:shrink-0"},Fe={class:"scroller-thin-y scroller-hidden flex h-0 flex-1 flex-col overflow-x-hidden"},He={class:"mt-6 px-0"},Oe={__name:"AppSidebar",setup(B){const p=U(),d=S(()=>p.getters["layout/getSidebarType"]),y=S(()=>d.value==="mini"),C=S(()=>d.value==="full"),h=S(()=>d.value==="pinned");S(()=>C.value||h.value);const b=()=>{h.value||p.dispatch("layout/setSidebarType","full")},l=()=>{h.value||p.dispatch("layout/setSidebarType","mini")};return(k,$)=>{const _=m("MobileSidebar"),o=m("AppNavigation");return t(),s(w,null,[c(_),e("div",Ee,[e("div",{onMouseover:b,onMouseleave:l,class:N(["border-primary bg-primary dark:bg-dark-header flex flex-col border-r pb-4 pt-5 transition-all duration-200 ease-out dark:border-gray-700",{"absolute left-0 top-0 z-40 h-full":!h.value,"w-16":y.value,"w-64":C.value||h.value}])},[e("div",Fe,[e("nav",He,[c(o)])])],34)])],64)}}},Ve={class:"dark:bg-dark-header relative z-10 flex h-16 shrink-0 justify-between bg-black dark:border-b dark:border-gray-700"},qe={class:"flex"},Ze={class:"flex items-center"},Ke=["src"],Qe=["src"],Ie={key:0,class:"far fa-circle h-6 w-6","aria-hidden":"true"},Ge={key:1,class:"fas fa-dot-circle h-6 w-6","aria-hidden":"true"},Je={class:"flex items-center justify-between space-x-4 px-4"},Xe={key:3},Ye={key:4},et={key:5,class:"hidden sm:block"},tt={class:"flex items-center"},st={__name:"AppHeader",setup(B){const p=O(),d=U(),y=v("isImpersonating").value,C=te("config:store"),h=v("assets.logoLight"),b=v("layout.display").value=="dark"?v("assets.iconLight"):v("assets.icon"),l=v("system.mode"),k=v("system.enableMaintenanceMode"),$=S(()=>d.getters["layout/getSidebarType"]==="pinned"),_=v("chat.enableChat").value,o=v("employee.enableQrCodeAttendance").value,g=f=>{d.dispatch("layout/setMobileSidebar",f)},j=f=>{d.dispatch("layout/setUserLayout",f)},z=()=>{let f=$.value?"mini":"pinned";j({sidebar:f})},L=async f=>{await d.dispatch("auth/user/unimpersonate").then(a=>{window.open("/app","_self")}).catch(a=>{})},n=V("keySearch"),M=()=>{n.openSearch()};return(f,a)=>{const W=m("BaseBadge"),D=m("TimesheetClock"),A=m("PeriodSelection"),Q=m("SessionWisePeriodSelection"),I=m("TeamSelection"),G=m("router-link"),J=m("ProfileDropdown"),R=se("tooltip");return t(),s("div",Ve,[e("div",qe,[e("div",Ze,[e("img",{src:r(h),class:"ml-4 mr-2 hidden h-12 sm:block"},null,8,Ke),e("img",{src:r(b),class:"mx-2 h-12 sm:hidden"},null,8,Qe),F((t(),s("button",{type:"button",class:"hidden h-full w-full px-4 text-gray-500 focus:outline-none lg:block",onClick:z},[a[4]||(a[4]=e("span",{class:"sr-only"},"Pinned Sidebar",-1)),$.value?(t(),s("i",Ge)):(t(),s("i",Ie))])),[[R,f.$trans("global.toggle",{attribute:f.$trans("general.sidebar")})]]),r(l)?i("",!0):(t(),T(W,{key:0,class:"hidden sm:block",design:"danger",label:f.$trans("general.demo")},null,8,["label"])),e("button",{type:"button",class:"h-full w-full px-2 text-gray-500 focus:outline-none sm:px-4 lg:hidden",onClick:a[0]||(a[0]=E=>g(!0))},[a[5]||(a[5]=e("span",{class:"sr-only"},"Open Mobile Sidebar",-1)),c(r(ie),{class:"h-6 w-6","aria-hidden":"true"})]),r(o)&&r(K)(["admin","attendance-assistant"],"any")?(t(),s("span",{key:1,onClick:a[2]||(a[2]=E=>r(p).push({name:"AttendanceAssistant"})),class:"cursor-pointer"},[e("i",{class:"far fa-clock text-white",onClick:a[1]||(a[1]=E=>r(p).push({name:"AttendanceAssistant"}))})])):i("",!0)])]),e("div",Je,[c(D),r(v)("academic.periodSelection").value=="period_wise"?(t(),T(A,{key:0})):i("",!0),r(v)("academic.periodSelection").value=="session_wise"?(t(),T(Q,{key:1})):i("",!0),c(I),r(_)?(t(),s("span",{key:2,class:"cursor-pointer",onClick:a[3]||(a[3]=E=>r(p).push({name:"Chat"}))},a[6]||(a[6]=[e("i",{class:"far fa-message text-white"},null,-1)]))):i("",!0),r(C)?(t(),s("div",Xe,[c(G,{to:{name:"Config"}},{default:x(()=>a[7]||(a[7]=[e("i",{class:"fas fa-cog text-white"},null,-1)])),_:1})])):i("",!0),r(y)?(t(),s("div",Ye,[F(e("i",{class:"fas fa-circle text-warning cursor-pointer h-4 w-4",onClick:L},null,512),[[R,f.$trans("user.unimpersonate")]])])):i("",!0),r(k)?F((t(),s("div",et,a[8]||(a[8]=[e("i",{class:"fas fa-circle text-danger h-4 w-4"},null,-1)]))),[[R,f.$trans("general.under_maintenance")]]):i("",!0),e("button",{onClick:M,class:"text-white sm:hidden"},a[9]||(a[9]=[e("i",{class:"fas fa-search"},null,-1)])),e("div",tt,[c(J)])])])}}},nt={class:"dark:bg-dark-body relative flex h-screen overflow-hidden bg-gray-200"},at={key:0,class:"h-screen flex-0 w-1/3 bg-white dark:bg-dark-header"},ot={class:"scroller-thin-y scroller-hidden h-screen overflow-x-hidden"},it={__name:"App",setup(B){const p=U(),d=ne(),y=H(null);re("keySearch",{openSearch:()=>{var _;return(_=y.value)==null?void 0:_.openSearch()},closeSearch:()=>{var _;return(_=y.value)==null?void 0:_.closeSearch()}});const C=v("system.showSetupWizard"),h=S(()=>p.getters["layout/getSidebarType"]==="pinned"),b=S(()=>!d.meta.noPadding),k=`users.${q("uuid").value}`;Z(()=>{le(k,"test.event",_=>{var o=new Audio("/notification.mp3");o.play()})}),ae(()=>{window.Echo.private(k).stopListening("test.event")});const{screenSize:$}=oe();return(_,o)=>{const g=m("NotificationBar"),j=m("router-view"),z=m("FooterCredit"),L=m("ReLogin"),n=m("KeySearch");return t(),s(w,null,[c(g,{type:"app"}),e("div",nt,[c(Oe),e("div",{class:N(["flex w-0 flex-1 flex-col overflow-hidden",{"lg:ml-16":!h.value}])},[c(st),e("main",{class:N(["scroller-thin-y scroller-hidden relative z-0 flex-1 focus:outline-none",{"overflow-y-auto":r(d).query.view!="board","overflow-y-hidden":r(d).query.view=="board"}])},[e("div",{class:N(["space-y-4",{"py-4 lg:px-4":b.value}])},[c(j)],2),c(z)],2)],2),!r($).small&&r(C)&&r(K)("admin")?(t(),s("div",at,[e("div",ot,[c(Re)])])):i("",!0)]),c(L),c(n,{ref_key:"keySearchRef",ref:y},null,512)],64)}}};export{it as default};
