import{u as f,G as D,H as P,l as b,r as d,q as H,o as _,w as V,d as i,e as l,f as s,J as R,a as j,F as q}from"./app-BAwPsakn.js";const A={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-2"},S={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3"},N={class:"grid grid-cols-1"},w={class:"col"},C={name:"TransportVehicleDocumentForm"},G=Object.assign(C,{setup(g){const u=f(),r={vehicle:"",title:"",type:"",startDate:"",endDate:"",description:"",media:[],mediaUpdated:!1,mediaToken:D(),mediaHash:[]},c="transport/vehicle/document/",n=P(c),p=b({vehicles:[],types:[]}),o=b({...r}),v=b({vehicle:"",type:"",isLoaded:!u.params.uuid}),U=a=>{Object.assign(p,a)},y=()=>{o.mediaToken=D(),o.mediaHash=[]},$=a=>{var e,m;Object.assign(r,{...a,type:(e=a.type)==null?void 0:e.uuid,endDate:a.endDate.value||"",startDate:a.startDate.value,vehicle:(m=a.vehicle)==null?void 0:m.uuid}),Object.assign(o,R(r)),v.vehicle=a.vehicle.name,v.type=a.type.name,v.isLoaded=!0};return(a,e)=>{const m=d("BaseSelect"),T=d("BaseInput"),h=d("DatePicker"),k=d("BaseTextarea"),B=d("MediaUpload"),F=d("FormAction");return _(),H(F,{"pre-requisites":!0,onSetPreRequisites:U,"init-url":c,"init-form":r,form:o,"set-form":$,redirect:"TransportVehicleDocument",onResetMediaFiles:y},{default:V(()=>[i("div",A,[i("div",O,[l(m,{modelValue:o.vehicle,"onUpdate:modelValue":e[0]||(e[0]=t=>o.vehicle=t),name:"vehicle",label:a.$trans("transport.vehicle.vehicle"),"label-prop":"nameWithRegistrationNumber","value-prop":"uuid",options:p.vehicles,error:s(n).vehicle,"onUpdate:error":e[1]||(e[1]=t=>s(n).vehicle=t)},null,8,["modelValue","label","options","error"])]),i("div",M,[l(T,{type:"text",modelValue:o.title,"onUpdate:modelValue":e[2]||(e[2]=t=>o.title=t),name:"title",label:a.$trans("transport.vehicle.document.props.title"),error:s(n).title,"onUpdate:error":e[3]||(e[3]=t=>s(n).title=t)},null,8,["modelValue","label","error"])]),i("div",S,[l(m,{modelValue:o.type,"onUpdate:modelValue":e[4]||(e[4]=t=>o.type=t),name:"type",label:a.$trans("transport.vehicle.document.props.type"),"label-prop":"name","value-prop":"uuid",options:p.types,error:s(n).type,"onUpdate:error":e[5]||(e[5]=t=>s(n).type=t)},null,8,["modelValue","label","options","error"])]),i("div",E,[l(h,{modelValue:o.startDate,"onUpdate:modelValue":e[6]||(e[6]=t=>o.startDate=t),name:"startDate",label:a.$trans("transport.vehicle.document.props.start_date"),"no-clear":"",error:s(n).startDate,"onUpdate:error":e[7]||(e[7]=t=>s(n).startDate=t)},null,8,["modelValue","label","error"])]),i("div",I,[l(h,{modelValue:o.endDate,"onUpdate:modelValue":e[8]||(e[8]=t=>o.endDate=t),name:"endDate",label:a.$trans("transport.vehicle.document.props.end_date"),"no-clear":"",error:s(n).endDate,"onUpdate:error":e[9]||(e[9]=t=>s(n).endDate=t)},null,8,["modelValue","label","error"])]),i("div",L,[l(k,{modelValue:o.description,"onUpdate:modelValue":e[10]||(e[10]=t=>o.description=t),name:"description",label:a.$trans("transport.vehicle.document.props.description"),error:s(n).description,"onUpdate:error":e[11]||(e[11]=t=>s(n).description=t)},null,8,["modelValue","label","error"])])]),i("div",N,[i("div",w,[l(B,{multiple:"",label:a.$trans("general.file"),module:"document",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[12]||(e[12]=t=>o.mediaUpdated=!0),onSetHash:e[13]||(e[13]=t=>o.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),J={name:"TransportVehicleDocumentAction"},z=Object.assign(J,{setup(g){const u=f();return(r,c)=>{const n=d("PageHeaderAction"),p=d("PageHeader"),o=d("ParentTransition");return _(),j(q,null,[l(p,{title:r.$trans(s(u).meta.trans,{attribute:r.$trans(s(u).meta.label)}),navs:[{label:r.$trans("transport.transport"),path:"Transport"},{label:r.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:r.$trans("transport.vehicle.document.document"),path:"TransportVehicleDocumentList"}]},{default:V(()=>[l(n,{name:"TransportVehicleDocument",title:r.$trans("transport.vehicle.document.document"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(o,{appear:"",visibility:!0},{default:V(()=>[l(G)]),_:1})],64)}}});export{z as default};
