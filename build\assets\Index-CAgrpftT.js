import{h as A,i as L,m as D,l as j,n as F,r as b,q as T,o as e,w as o,d as t,e as d,a,F as x,v as $,b as _,s as m,t as s,x as M,f as k,z as q,O as H,P as U,Q as E,R as W,A as Q,S as J,B as z,j as K,k as X,g as R,D as Z,c as ee,T as te,u as se,y as V}from"./app-BAwPsakn.js";import{C as ae,a as ne,L as le,B as re,p as oe,b as ie,c as de,d as Y}from"./index-D5FX_aO4.js";import{_ as ce}from"./EditRequestInfo-8GzviTVS.js";import{F as ue}from"./v3-GGBodInF.js";const me={class:"bg-black px-4 pb-16 pt-6"},_e={class:"dark:bg-dark-header rounded-lg bg-white border-b border-gray-200 dark:border-gray-700"},he={class:"flex items-center p-4"},ge={class:"shrink-0"},pe={key:0,class:"text-xl font-semibold leading-none text-gray-900 dark:text-gray-400 sm:text-2xl"},be={key:0,class:"text-2xl sm:text-3xl"},fe={key:1,class:"text-2xl font-semibold leading-none text-gray-900 dark:text-gray-400 sm:text-3xl"},ye={class:"mt-2 text-base font-normal uppercase text-gray-500"},ve={key:0,class:"mt-1 flex items-center gap-2"},xe={class:"text-sm font-semibold leading-none text-gray-500 dark:text-gray-400"},ke={class:"ml-5 flex w-0 flex-1 items-center justify-end font-bold"},$e={class:"px-4 py-2 space-y-2"},we={class:"px-4 py-2 space-y-2"},Se={name:"DashboardStat"},Te=Object.assign(Se,{setup(w){ae.register(ne,le,re,oe,ie,de);const y=A(),g=L(),i=D(!1),f=j({stats:[],attendanceSummary:[],feeSummary:[],studentStrengthChartData:[],transactionChartData:[]});return F(async()=>{i.value=!0,await g.dispatch("dashboard/getStat").then(u=>{i.value=!1,Object.assign(f,u)}).catch(u=>{i.value=!1})}),(u,r)=>{const c=b("BaseFlexCard"),v=b("TextMuted"),l=b("BaseLabel"),n=b("ProgressBar"),p=b("BaseFlexContainer"),B=b("BaseLoader");return e(),T(B,{"is-loading":i.value},{default:o(()=>[t("div",me,[d(p,null,{default:o(()=>[d(c,{class:"md:w-1/3"},{title:o(()=>[m(s(u.$trans("dashboard.statistics")),1)]),default:o(()=>[(e(!0),a(x,null,$(f.stats,h=>(e(),a("div",_e,[t("div",he,[t("div",ge,[t("div",null,[h.subCount?(e(),a("span",pe,[m(s(h.count),1),h.subCount?(e(),a("span",be,"/"+s(h.subCount),1)):_("",!0)])):(e(),a("span",fe,s(h.count),1)),t("h3",ye,s(h.title),1)]),h.subTitle?(e(),a("div",ve,[t("i",{class:M(h.subTitleIcon)},null,2),t("span",xe,s(h.subTitleCount)+" "+s(h.subTitle),1)])):_("",!0)]),t("div",ke,[t("div",{class:M(["rounded-full",[h.color]])},[t("i",{class:M([h.icon,"flex h-12 w-12 items-center justify-center text-white"])},null,2)],2)])])]))),256))]),_:1}),d(c,{class:"md:w-1/3"},{title:o(()=>[m(s(u.$trans("global.summary",{attribute:u.$trans("student.attendance.attendance")})),1)]),action:o(()=>[t("i",{class:"cursor-pointer text-gray-800 dark:text-gray-400 fa-solid fa-up-right-from-square",onClick:r[0]||(r[0]=h=>k(y).push({name:"StudentReportDateWiseAttendance"}))})]),default:o(()=>[t("div",$e,[(e(!0),a(x,null,$(f.attendanceSummary,h=>(e(),a("div",null,[d(l,null,{default:o(()=>[m(s(h.label)+" ",1),d(v,null,{default:o(()=>[m(s(h.value)+" / "+s(h.max),1)]),_:2},1024)]),_:2},1024),d(n,{space:"",height:"h-2",percent:h.percent,max:h.max,color:h.color},null,8,["percent","max","color"])]))),256))])]),_:1}),d(c,{class:"md:w-1/3"},{title:o(()=>[m(s(u.$trans("global.summary",{attribute:u.$trans("student.fee.fee")})),1)]),default:o(()=>[t("div",we,[(e(!0),a(x,null,$(f.feeSummary,h=>(e(),a("div",null,[d(l,null,{default:o(()=>[m(s(h.label)+" ",1),d(v,null,{default:o(()=>[m(s(h.value)+" / "+s(h.max),1)]),_:2},1024)]),_:2},1024),d(n,{space:"",height:"h-2",percent:h.percent,max:h.max,color:h.color},null,8,["percent","max","color"])]))),256))])]),_:1})]),_:1})]),d(p,{class:"-mt-12 px-4"},{default:o(()=>[d(c,{class:"md:w-1/2","is-loading":i.value},{title:o(()=>[m(s(u.$trans("academic.student_strength")),1)]),default:o(()=>[f.studentStrengthChartData.labels?(e(),T(k(Y),{key:0,data:f.studentStrengthChartData,options:{responsive:!0}},null,8,["data"])):_("",!0)]),_:1},8,["is-loading"]),d(c,{class:"md:w-1/2","is-loading":i.value},{title:o(()=>[m(s(u.$trans("finance.receipt_vs_payment")),1)]),default:o(()=>[f.transactionChartData.labels?(e(),T(k(Y),{key:0,data:f.transactionChartData,options:{responsive:!0}},null,8,["data"])):_("",!0)]),_:1},8,["is-loading"])]),_:1})]),_:1},8,["is-loading"])}}}),Ce={class:"lg:flex lg:h-full lg:flex-col"},Be={class:"flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-6 py-4 lg:flex-none"},Me={class:"text-base font-semibold leading-6 text-gray-900 dark:text-gray-300"},De={datetime:"2022-01"},Le={class:"flex items-center"},je={class:"relative flex items-center rounded-md bg-white dark:bg-dark-header shadow-sm md:items-stretch"},Fe={class:"hidden md:ml-4 md:flex md:items-center"},Ne={class:"shadow ring-1 ring-black ring-opacity-5 lg:flex lg:flex-auto lg:flex-col"},Ae={class:"flex bg-gray-200 dark:bg-gray-700 text-xs leading-6 text-gray-700 dark:text-gray-400 lg:flex-auto"},ze=["datetime"],Pe={class:"flex"},Oe={key:0,class:"mt-2"},Re=["href"],Ie={class:"flex-auto truncate font-medium text-gray-900 dark:text-gray-400 group-hover:text-gray-300"},Ge=["datetime"],Ve={key:0,class:"text-gray-500"},Ye=["datetime"],Ee={class:"flex"},qe={class:"sr-only"},He={key:0,class:"-mx-0.5 mt-auto flex flex-wrap-reverse"},Ue={key:0,class:"px-4 py-10 sm:px-6 lg:hidden"},We={class:"divide-y divide-gray-100 overflow-hidden rounded-lg bg-white text-sm shadow ring-1 ring-black ring-opacity-5"},Qe={class:"flex-auto"},Je={class:"font-semibold text-gray-900"},Ke=["datetime"],Xe=["href"],Ze={class:"sr-only"},et={name:"DashboardScheduleCalendar"},tt=Object.assign(et,{props:{calendar:{type:Object,required:!0}},emits:["previousMonth","nextMonth"],setup(w,{emit:y}){const g=y,f=w.calendar.days.find(u=>u.isSelected);return(u,r)=>{var v;const c=q("tooltip");return e(),a("div",Ce,[t("header",Be,[t("h1",Me,[t("time",De,s(w.calendar.month),1)]),t("div",Le,[t("div",je,[t("span",{class:"cursor-pointer flex h-9 w-12 items-center justify-center rounded-l-md border-y border-l border-gray-300 dark:border-gray-500 pr-1 text-gray-400 dark:text-gray-500 hover:text-gray-500 focus:relative md:w-9 md:pr-0 md:hover:bg-gray-50 dark:md:hover:bg-black",onClick:r[0]||(r[0]=l=>g("previousMonth"))},[r[2]||(r[2]=t("span",{class:"sr-only"},"Previous month",-1)),d(k(H),{class:"h-5 w-5","aria-hidden":"true"})]),r[4]||(r[4]=t("span",{class:"relative -mx-px h-5 w-px bg-gray-300 md:hidden"},null,-1)),t("span",{class:"cursor-pointer flex h-9 w-12 items-center justify-center rounded-r-md border-y border-r border-gray-300 dark:border-gray-500 pl-1 text-gray-400 dark:text-gray-500 hover:text-gray-500 focus:relative md:w-9 md:pl-0 md:hover:bg-gray-50 dark:md:hover:bg-black",onClick:r[1]||(r[1]=l=>g("nextMonth"))},[r[3]||(r[3]=t("span",{class:"sr-only"},"Next month",-1)),d(k(U),{class:"h-5 w-5","aria-hidden":"true"})])]),t("div",Fe,[E(u.$slots,"action")])])]),t("div",Ne,[r[5]||(r[5]=W('<div class="grid grid-cols-7 gap-px border-b border-gray-300 dark:border-gray-600 bg-gray-200 dark:bg-gray-700 text-center text-xs font-semibold leading-6 text-gray-700 dark:text-gray-400 lg:flex-none"><div class="bg-white dark:bg-dark-body py-2"> M<span class="sr-only sm:not-sr-only">on</span></div><div class="bg-white dark:bg-dark-body py-2"> T<span class="sr-only sm:not-sr-only">ue</span></div><div class="bg-white dark:bg-dark-body py-2"> W<span class="sr-only sm:not-sr-only">ed</span></div><div class="bg-white dark:bg-dark-body py-2"> T<span class="sr-only sm:not-sr-only">hu</span></div><div class="bg-white dark:bg-dark-body py-2"> F<span class="sr-only sm:not-sr-only">ri</span></div><div class="bg-white dark:bg-dark-body py-2"> S<span class="sr-only sm:not-sr-only">at</span></div><div class="bg-white dark:bg-dark-body py-2"> S<span class="sr-only sm:not-sr-only">un</span></div></div>',1)),t("div",Ae,[t("div",{class:M(["hidden w-full lg:grid lg:grid-cols-7 lg:gap-px",{"lg:grid-rows-4":w.calendar.days.length<=28,"lg:grid-rows-5":w.calendar.days.length>28&&w.calendar.days.length<=35,"lg:grid-rows-6":w.calendar.days.length>35}])},[(e(!0),a(x,null,$(w.calendar.days,l=>(e(),a("div",{key:l.date,class:M([l.isCurrentMonth?"bg-white dark:bg-dark-header":"bg-gray-50 dark:bg-dark-body text-gray-500","relative px-3 py-2"])},[t("time",{datetime:l.date,class:M(l.isToday?"flex h-6 w-6 items-center justify-center rounded-full bg-primary dark:bg-dark-primary font-semibold text-white":void 0)},s(l.date.split("-").pop().replace(/^0/,"")),11,ze),t("div",Pe,[(e(!0),a(x,null,$(l.attendances,n=>(e(),a("span",{key:n.studentName},[t("small",null,[n.code?(e(),a("span",{key:0,class:M(["rounded-full px-2 py-1 text-white",{"bg-primary":n.code==="H","bg-success":n.code==="P","bg-danger":n.code==="A","bg-info":n.code==="L","bg-warning":n.code==="HD"}])},s(l.attendances.length>1?n.idNumber:n.code),3)):_("",!0)])]))),128))]),l.events.length>0?(e(),a("ol",Oe,[(e(!0),a(x,null,$(l.events.slice(0,2),n=>(e(),a("li",{key:n.id},[t("a",{href:n.href,class:"group flex"},[Q((e(),a("p",Ie,[m(s(n.name),1)])),[[c,n.name]]),t("time",{datetime:n.datetime,class:"ml-3 hidden flex-none text-gray-500 group-hover:text-gray-300 xl:block"},s(n.time),9,Ge)],8,Re)]))),128)),l.events.length>2?(e(),a("li",Ve," + "+s(l.events.length-2)+" more ",1)):_("",!0)])):_("",!0)],2))),128))],2),t("div",{class:M(["isolate grid w-full grid-cols-7 gap-px lg:hidden",{"lg:grid-rows-4":w.calendar.days.length<=28,"lg:grid-rows-5":w.calendar.days.length>28&&w.calendar.days.length<=35,"lg:grid-rows-6":w.calendar.days.length>35}])},[(e(!0),a(x,null,$(w.calendar.days,l=>(e(),a("button",{key:l.date,type:"button",class:M([l.isCurrentMonth?"bg-white":"bg-gray-50",(l.isSelected||l.isToday)&&"font-semibold",l.isSelected&&"text-white",!l.isSelected&&l.isToday&&"text-primary",!l.isSelected&&l.isCurrentMonth&&!l.isToday&&"text-gray-900",!l.isSelected&&!l.isCurrentMonth&&!l.isToday&&"text-gray-500","flex h-14 flex-col px-3 py-2 hover:bg-gray-100 focus:z-10"])},[t("time",{datetime:l.date,class:M([l.isSelected&&"flex h-6 w-6 items-center justify-center rounded-full",l.isSelected&&l.isToday&&"bg-primary dark:bg-dark-primary",l.isSelected&&!l.isToday&&"bg-gray-900","ml-auto"])},s(l.date.split("-").pop().replace(/^0/,"")),11,Ye),t("div",Ee,[(e(!0),a(x,null,$(l.attendances,(n,p)=>(e(),a("span",{key:n.studentName},[t("small",null,[n.code?(e(),a("span",{key:0,class:M(["rounded-full px-2 py-1 text-white",{"bg-success":n.code==="P","bg-danger":n.code==="A","bg-info":n.code==="L","bg-warning":n.code==="HD"}])},s(p+1),3)):_("",!0)])]))),128))]),t("span",qe,s(l.events.length)+" events",1),l.events.length>0?(e(),a("span",He,[(e(!0),a(x,null,$(l.events,n=>(e(),a("span",{key:n.id,class:"mx-0.5 mb-1 h-1.5 w-1.5 rounded-full bg-gray-400"}))),128))])):_("",!0)],2))),128))],2)])]),((v=k(f))==null?void 0:v.events.length)>0?(e(),a("div",Ue,[t("ol",We,[(e(!0),a(x,null,$(k(f).events,l=>(e(),a("li",{key:l.id,class:"group flex p-4 pr-6 focus-within:bg-gray-50 hover:bg-gray-50"},[t("div",Qe,[t("p",Je,s(l.name),1),t("time",{datetime:l.datetime,class:"mt-2 flex items-center text-gray-700"},[d(k(J),{class:"mr-2 h-5 w-5 text-gray-400","aria-hidden":"true"}),m(" "+s(l.time),1)],8,Ke)]),t("a",{href:l.href,class:"ml-6 flex-none self-center rounded-md bg-white px-3 py-2 font-semibold text-gray-900 opacity-0 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 focus:opacity-100 group-hover:opacity-100"},[r[6]||(r[6]=m("Edit")),t("span",Ze,", "+s(l.name),1)],8,Xe)]))),128))])])):_("",!0)])}}}),st={class:"scroller-thin-y scroller-hidden max-h-96"},at=["onClick"],nt={class:"flex items-center"},lt={class:"shrink-0"},rt={class:"ml-5 flex w-0 flex-1 items-center"},ot={class:"dark:text-gray-400"},it={key:0,class:"p-2"},dt={__name:"FeedItem",props:{feedItems:{type:Array,default:()=>[]}},setup(w){const y=A(),g=i=>{i.hasDetail&&(i.type=="announcement"?y.push({name:"CommunicationAnnouncementShow",params:{uuid:i.uuid}}):i.type=="event"?y.push({name:"CalendarEventShow",params:{uuid:i.uuid}}):i.type=="exam"?y.push({name:"ExamSchedule"}):i.type=="diary"?y.push({name:"ResourceDiaryShow",params:{uuid:i.uuid}}):i.type=="assignment"?y.push({name:"ResourceAssignmentShow",params:{uuid:i.uuid}}):i.type=="learning_material"?y.push({name:"ResourceLearningMaterialShow",params:{uuid:i.uuid}}):i.type=="meal"&&y.push({name:"MessMealLogShow",params:{uuid:i.uuid}}))};return(i,f)=>{const u=b("TextMuted"),r=b("BaseAlert");return e(),a("div",st,[(e(!0),a(x,null,$(w.feedItems,c=>(e(),a("div",{class:"border-b border-gray-200 p-4 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-neutral-700 cursor-pointer",onClick:v=>g(c)},[t("div",nt,[t("div",lt,[t("div",{class:M(["rounded-full p-2",[c.color]])},[t("i",{class:M([c.icon,"flex h-6 w-6 items-center justify-center text-white"])},null,2)],2)]),t("div",rt,[t("div",null,[t("p",ot,s(c.title),1),c.subTitle?(e(),T(u,{key:0,block:""},{default:o(()=>[m(s(c.subTitle),1)]),_:2},1024)):_("",!0),d(u,{block:""},{default:o(()=>[m(s(c.duration),1)]),_:2},1024)])])])],8,at))),256)),w.feedItems.length==0?(e(),a("div",it,[d(r,{size:"xs",design:"info"},{default:o(()=>[m(s(i.$trans("dashboard.nothing_to_show")),1)]),_:1})])):_("",!0)])}}},ct={class:"scroller-thin-y scroller-hidden max-h-96"},ut={class:"p-2"},mt={key:0,class:"text-center text-sm font-semibold text-gray-800 dark:text-gray-200"},_t={class:"mt-2"},ht={class:"mt-2 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800"},gt={class:"flex justify-between items-center text-sm"},pt={class:"font-medium"},bt={class:"text-xs"},ft={key:0,class:"mt-1 flex justify-between items-center text-xs"},yt={key:0},vt={key:1,class:"mt-1 text-xs"},xt={key:0,class:"p-2"},kt={key:0,class:"mt-2 p-2"},$t={class:"flex justify-between items-center text-sm"},wt={class:"font-medium"},St={class:"text-xs"},Tt={class:"mt-1 flex justify-between items-center text-xs"},Ct={key:0},Bt={key:0},Mt={key:0,class:"mt-1 text-xs"},Dt={key:1,class:"p-2"},Lt={class:"p-2"},jt={__name:"Timetable",setup(w){const y=L(),g=D(!1),i=j({students:[],sessions:[],employee:null}),f=async()=>{g.value=!0,await y.dispatch("dashboard/getTimetable").then(r=>{z(["student","guardian"],"any")?i.students=r:(i.sessions=r.sessions||[],i.employee=r.employee||{}),g.value=!1}).catch(r=>{g.value=!1})},u=()=>{window.open("/app/academic/timetables/teacher/export?action=print")};return F(async()=>{await f()}),(r,c)=>{const v=b("BaseAlert"),l=b("BaseFieldset"),n=b("BaseCard"),p=b("BaseButton");return e(),a("div",ct,[k(z)(["student","guardian"],"any")?(e(),T(n,{key:0,"no-content-padding":"","no-padding":"","is-loading":g.value},{default:o(()=>[(e(!0),a(x,null,$(i.students,(B,h)=>(e(),a("div",ut,[d(l,null,{legend:o(()=>[m(s(B.name)+" - "+s(B.batch),1)]),default:o(()=>[B.room?(e(),a("p",mt,s(B.room),1)):_("",!0),t("div",_t,[B.info?(e(),T(v,{key:0,size:"xs",design:"info"},{default:o(()=>[m(s(B.info),1)]),_:2},1024)):_("",!0),t("div",ht,[(e(!0),a(x,null,$(B.sessions,(C,P)=>(e(),a("div",{key:P,class:M(["",["p-3",P%2===0&&!C.isCurrent?"bg-white dark:bg-dark-body":"bg-gray-100 dark:bg-dark-header",C.isCurrent?"bg-success dark:bg-success text-white":"text-gray-800 dark:text-gray-200"]])},[t("div",gt,[t("span",pt,s(C.name),1),t("span",bt,[m(s(C.startTime.formatted)+" - "+s(C.endTime.formatted)+" ",1),c[0]||(c[0]=t("i",{class:"far fa-clock"},null,-1))])]),(e(!0),a(x,null,$(C.allotments,(N,I)=>{var O,S;return e(),a("div",{key:`allotment-${I}`},[(O=N.subject)!=null&&O.name?(e(),a("div",ft,[t("span",null,s(N.subject.name)+" ("+s(N.subject.code)+")",1),(S=N.employee)!=null&&S.name?(e(),a("span",yt,s(N.employee.name)+" ("+s(N.employee.designation)+") ",1)):_("",!0)])):_("",!0),C.room?(e(),a("div",vt,s(C.room),1)):_("",!0)])}),128))],2))),128))])])]),_:2},1024)]))),256)),i.students.length==0?(e(),a("div",xt,[d(v,{size:"xs",design:"info"},{default:o(()=>[m(s(r.$trans("dashboard.nothing_to_show")),1)]),_:1})])):_("",!0)]),_:1},8,["is-loading"])):(e(),T(n,{key:1,"no-content-padding":"","no-padding":"","is-loading":g.value},{default:o(()=>{var B;return[(B=i.employee)!=null&&B.name?(e(),a("div",kt,[d(l,null,{legend:o(()=>[m(s(i.employee.name)+" ("+s(i.employee.designation)+") ",1)]),default:o(()=>[(e(!0),a(x,null,$(i.sessions,(h,C)=>(e(),a("div",{key:C,class:M(["",["p-3",C%2===0?"bg-white dark:bg-dark-body":"bg-gray-100 dark:bg-dark-header"]])},[t("div",$t,[t("span",wt,s(h.name),1),t("span",St,[m(s(h.startTime.formatted)+" - "+s(h.endTime.formatted)+" ",1),c[1]||(c[1]=t("i",{class:"far fa-clock"},null,-1))])]),t("div",Tt,[h.batch?(e(),a("span",Ct,s(h.batch),1)):_("",!0),t("span",null,[m(s(h.subject.name)+" ",1),h.subject.code?(e(),a("span",Bt,"("+s(h.subject.code)+")",1)):_("",!0)])]),h.room?(e(),a("div",Mt,s(h.room),1)):_("",!0)],2))),128))]),_:1})])):_("",!0),i.sessions.length==0?(e(),a("div",Dt,[d(v,{size:"xs",design:"info"},{default:o(()=>[m(s(r.$trans("dashboard.nothing_to_show")),1)]),_:1})])):_("",!0),t("div",Lt,[d(p,{block:"",design:"primary",onClick:u},{default:o(()=>[m(s(r.$trans("global.print",{attribute:r.$trans("academic.timetable.teacher_timetable")})),1)]),_:1})])]}),_:1},8,["is-loading"]))])}}},Ft={class:"scroller-thin-y scroller-hidden max-h-96"},Nt={class:"border-b border-gray-200 p-4 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-neutral-700 text-gray-700 dark:text-gray-300"},At={class:"text-center text-sm"},zt={key:0,class:"mt-4 border-2 border-primary dark:border-gray-200 rounded-lg px-4 py-2"},Pt={class:"flex text-sm"},Ot={class:"flex-1"},Rt={class:"font-medium"},It={class:"flex-1"},Gt={class:"mt-4 flex justify-between"},Vt={class:"flex-1"},Yt={class:""},Et={class:"mt-1 flex text-xs"},qt={class:"flex-1"},Ht={class:"flex-1"},Ut={class:"flex-1"},Wt={class:"mt-1 flex text-xs"},Qt={class:"flex-1"},Jt={class:"flex-1"},Kt={key:0,class:"p-2"},Xt={__name:"TransportRoute",setup(w){const y=L(),g=D(!1),i=j({transportRoutes:[]}),f=async()=>{g.value=!0,await y.dispatch("dashboard/getTransportRoute").then(u=>{i.transportRoutes=u,g.value=!1}).catch(u=>{g.value=!1})};return F(async()=>{await f()}),(u,r)=>{const c=b("TextMuted"),v=b("BaseAlert"),l=b("BaseLoader");return e(),a("div",Ft,[d(l,{"is-loading":g.value},{default:o(()=>[(e(!0),a(x,null,$(i.transportRoutes,n=>(e(),a("div",Nt,[t("div",At,[m(s(n.vehicle.name)+" ",1),d(c,null,{default:o(()=>[m("("+s(n.vehicle.registrationNumber)+")",1)]),_:2},1024)]),n.employees.length>0?(e(),a("div",zt,[(e(!0),a(x,null,$(n.employees,p=>(e(),a("div",Pt,[t("div",Ot,[t("div",Rt,s(p.name),1)]),t("div",It,[m(s(p.title)+" ",1),d(c,null,{default:o(()=>[m(s(p.contactNumber),1)]),_:2},1024)])]))),256))])):_("",!0),t("div",Gt,[t("div",Vt,[t("div",Yt,[r[0]||(r[0]=t("i",{class:"fas fa-arrow-up"},null,-1)),m(" "+s(u.$trans("transport.circle.directions.arrival")),1)]),(e(!0),a(x,null,$(n.arrivalStoppages,p=>(e(),a("div",Et,[t("div",qt,[t("div",null,s(p.name),1)]),t("div",Ht,s(p.arrivalTime.formatted),1)]))),256))]),t("div",Ut,[t("div",null,[r[1]||(r[1]=t("i",{class:"fas fa-arrow-down"},null,-1)),m(" "+s(u.$trans("transport.circle.directions.departure")),1)]),(e(!0),a(x,null,$(n.departureStoppages,p=>(e(),a("div",Wt,[t("div",Qt,[t("div",null,s(p.name),1)]),t("div",Jt,s(p.arrivalTime.formatted),1)]))),256))])])]))),256)),i.transportRoutes.length==0?(e(),a("div",Kt,[d(v,{size:"xs",design:"info"},{default:o(()=>[m(s(u.$trans("dashboard.nothing_to_show")),1)]),_:1})])):_("",!0)]),_:1},8,["is-loading"])])}}},Zt={class:"scroller-thin-y scroller-hidden max-h-96"},es={class:"border-b border-gray-200 p-4 hover:bg-gray-100 dark:border-gray-700 dark:hover:bg-neutral-700 text-gray-700 dark:text-gray-300"},ts={class:"text-sm"},ss={class:"mt-2"},as={class:"text-sm font-semibold"},ns={class:"mt-1"},ls={key:0,class:"p-2"},rs={__name:"MessSchedule",setup(w){const y=L(),g=D(!1),i=j({messSchedules:[]}),f=async()=>{g.value=!0,await y.dispatch("dashboard/getMessSchedule").then(u=>{i.messSchedules=u,g.value=!1}).catch(u=>{g.value=!1})};return F(async()=>{await f()}),(u,r)=>{const c=b("TextMuted"),v=b("BaseAlert"),l=b("BaseLoader");return e(),a("div",Zt,[d(l,{"is-loading":g.value},{default:o(()=>[(e(!0),a(x,null,$(i.messSchedules,n=>(e(),a("div",es,[t("div",ts,[r[0]||(r[0]=t("i",{class:"fas fa-calendar-days mr-2"},null,-1)),t("span",null,s(u.moment(n.date.value).format("dddd Do, MMMM YYYY")),1)]),(e(!0),a(x,null,$(n.meals,p=>(e(),a("div",ss,[t("div",as,s(p.name),1),t("div",ns,[d(c,{block:""},{default:o(()=>[m(s(p.items),1)]),_:2},1024)])]))),256))]))),256)),i.messSchedules.length==0?(e(),a("div",ls,[d(v,{size:"xs",design:"info"},{default:o(()=>[m(s(u.$trans("dashboard.nothing_to_show")),1)]),_:1})])):_("",!0)]),_:1},8,["is-loading"])])}}},os={class:"scroller-thin-y scroller-hidden max-h-96"},is={__name:"InstituteInfo",setup(w){const y=L(),g=D(!1),i=j({info:[],incharges:[]}),f=async()=>{g.value=!0,await y.dispatch("dashboard/getInstituteInfo").then(u=>{i.info=u.info,i.incharges=u.incharges,g.value=!1}).catch(u=>{g.value=!1})};return F(async()=>{await f()}),(u,r)=>{const c=b("ListItemView"),v=b("ListContainerVertical"),l=b("TextMuted"),n=b("BaseLoader");return e(),a("div",os,[d(n,{"is-loading":g.value},{default:o(()=>[d(v,null,{default:o(()=>[(e(!0),a(x,null,$(i.info,p=>(e(),T(c,{truncate:!1,label:p.label||" "},{default:o(()=>[m(s(p.value),1)]),_:2},1032,["label"]))),256))]),_:1}),d(v,null,{default:o(()=>[(e(!0),a(x,null,$(i.incharges,p=>(e(),T(c,{truncate:!1,label:p.title},{default:o(()=>[m(s(p.name)+" ",1),p.email?(e(),T(l,{key:0,block:""},{default:o(()=>[m(s(p.email),1)]),_:2},1024)):_("",!0),p.contactNumber?(e(),T(l,{key:1,block:""},{default:o(()=>[m(s(p.contactNumber),1)]),_:2},1024)):_("",!0)]),_:2},1032,["label"]))),256))]),_:1})]),_:1},8,["is-loading"])])}}},ds={class:"text-2xl font-semibold"},cs={name:"DashboardFeed"},us=Object.assign(cs,{setup(w){A();const y=L(),g=K("moment"),i=X("profile.name"),f=R("mess.showMessScheduleInDashboard"),u=R("transport.showTransportRouteInDashboard"),r=D(!1),c=j({activeTab:"feed",feedItems:[],calendar:{days:[],lastMonthDate:"",nextMonthDate:""}}),v=async(l="")=>{r.value=!0,await y.dispatch("dashboard/getSchedule",{type:l,lastMonthDate:c.calendar.lastMonthDate,nextMonthDate:c.calendar.nextMonthDate}).then(n=>{r.value=!1,c.feedItems=n.feedItems,c.calendar=n.calendar}).catch(n=>{r.value=!1})};return F(async()=>{await v()}),(l,n)=>{const p=b("TabItem"),B=b("TabGroup"),h=b("BaseFlexCard");return e(),a(x,null,[d(h,{class:"md:w-1/3","is-loading":r.value},{title:o(()=>[m(s(l.$trans("general.greeting_message",{name:k(i)}))+" ",1),t("h3",ds,s(k(g)().format("dddd Do, MMMM YYYY")),1)]),default:o(()=>[d(B,null,{default:o(()=>[d(p,{first:"",label:l.$trans("dashboard.feed"),onClick:n[0]||(n[0]=C=>c.activeTab="feed"),"is-active":c.activeTab=="feed"},null,8,["label","is-active"]),d(p,{label:l.$trans("academic.timetable.timetable"),onClick:n[1]||(n[1]=C=>c.activeTab="timetable"),"is-active":c.activeTab=="timetable"},null,8,["label","is-active"]),k(u)&&k(z)(["student","guardian"],"any")?(e(),T(p,{key:0,label:l.$trans("transport.transport"),onClick:n[2]||(n[2]=C=>c.activeTab="transport"),"is-active":c.activeTab=="transport"},null,8,["label","is-active"])):_("",!0),k(f)?(e(),T(p,{key:1,label:l.$trans("mess.mess"),onClick:n[3]||(n[3]=C=>c.activeTab="mess"),"is-active":c.activeTab=="mess"},null,8,["label","is-active"])):_("",!0),d(p,{last:"",label:l.$trans("general.institute_info"),onClick:n[4]||(n[4]=C=>c.activeTab="instituteInfo"),"is-active":c.activeTab=="instituteInfo"},null,8,["label","is-active"])]),_:1}),c.activeTab=="feed"?(e(),T(dt,{key:0,"feed-items":c.feedItems},null,8,["feed-items"])):_("",!0),c.activeTab=="timetable"?(e(),T(jt,{key:1})):_("",!0),c.activeTab=="instituteInfo"?(e(),T(is,{key:2})):_("",!0),c.activeTab=="transport"&&k(z)(["student","guardian"],"any")?(e(),T(Xt,{key:3})):_("",!0),c.activeTab=="mess"?(e(),T(rs,{key:4})):_("",!0)]),_:1},8,["is-loading"]),d(h,{class:"md:w-2/3","is-loading":r.value},{default:o(()=>[c.calendar.days.length>0?(e(),T(tt,{key:0,calendar:c.calendar,onPreviousMonth:n[5]||(n[5]=C=>v("previous")),onNextMonth:n[6]||(n[6]=C=>v("next"))},null,8,["calendar"])):_("",!0)]),_:1},8,["is-loading"])],64)}}}),ms={class:"relative bg-black sm:rounded-lg"},_s={key:0,class:"absolute right-0 top-0 mt-2 mr-2"},hs={class:"flex"},gs={class:"text-xs"},ps={class:"-mt-12 flex w-full justify-center sm:-mt-16"},bs=["src"],fs={class:"mt-4 text-center text-white"},ys={class:"px-4 py-2 space-y-2"},vs={name:"DashboardStudentList"},xs=Object.assign(vs,{setup(w){const y=A(),g=L(),{screenSize:i}=Z(),f=ee(()=>r.fees.filter(l=>{var n;return l.studentUuid===((n=r.selectedStudent)==null?void 0:n.uuid)})),u=D(!1),r=j({selectedStudent:{},students:[],fees:[]}),c=l=>{r.selectedStudent=l},v=async(l="")=>{u.value=!0,await g.dispatch("dashboard/getStudentList").then(n=>{u.value=!1,r.fees=n.fees,r.students=n.students.map((p,B)=>(p.idNumber=B+1,p)),r.selectedStudent=r.students[0]||{}}).catch(n=>{u.value=!1})};return F(async()=>{await v()}),(l,n)=>{const p=b("BaseButton"),B=b("DropdownItem"),h=b("DropdownMenu"),C=b("TextMuted"),P=b("BaseFlexCard"),N=b("BaseLabel"),I=b("ProgressBar"),O=b("BaseFlexContainer");return e(),a(x,null,[d(ce,{student:r.selectedStudent},null,8,["student"]),d(O,null,{default:o(()=>[d(P,{class:"md:w-1/3 bg-black","is-loading":u.value},{default:o(()=>[t("div",ms,[r.students.length>1?(e(),a("div",_s,[d(h,{"double-top-margin":"","right-margin":!1,"no-width":""},{clickable:o(()=>[d(p,{size:"xs",design:"white"},{default:o(()=>[t("span",hs,[E(l.$slots,"moduleOptionLabel",{},()=>[m(s(r.selectedStudent.name),1)]),d(k(te),{class:M(["ml-1 h-4 w-4"]),"aria-hidden":"true"})])]),_:3})]),default:o(()=>[(e(!0),a(x,null,$(r.students,(S,G)=>(e(),a(x,null,[S.uuid!==r.selectedStudent.uuid?(e(),T(B,{key:0,onClick:Us=>c(S)},{default:o(()=>[t("span",gs,s(S.name)+" ("+s(S.idNumber)+")",1)]),_:2},1032,["onClick"])):_("",!0)],64))),256))]),_:3})])):_("",!0),n[4]||(n[4]=t("img",{class:"h-16 w-full object-cover sm:rounded-tl-lg sm:rounded-tr-lg lg:h-24",src:"/images/user-cover.jpeg",alt:""},null,-1)),t("div",ps,[t("img",{onClick:n[0]||(n[0]=S=>k(y).push({name:"StudentShow",params:{uuid:r.selectedStudent.uuid}})),class:"h-24 w-24 rounded-full ring-4 ring-white sm:h-32 sm:w-32 cursor-pointer",src:r.selectedStudent.photo,alt:""},null,8,bs)]),t("div",fs,[t("h1",{class:"font-bold cursor-pointer",onClick:n[1]||(n[1]=S=>k(y).push({name:"StudentShow",params:{uuid:r.selectedStudent.uuid}}))},s(r.selectedStudent.name)+" ("+s(r.selectedStudent.idNumber)+") ",1),t("p",null,[m(s(r.selectedStudent.courseName)+" "+s(r.selectedStudent.batchName)+" ",1),d(C,{black:"",block:""},{default:o(()=>[m(s(r.selectedStudent.codeNumber),1)]),_:1})]),t("p",null,[n[3]||(n[3]=t("i",{class:"fas fa-mobile-alt"},null,-1)),m(" "+s(r.selectedStudent.contactNumber),1)])])])]),_:3},8,["is-loading"]),d(P,{class:"md:w-2/3","is-loading":u.value},{title:o(()=>[m(s(l.$trans("dashboard.fee_summary")),1)]),action:o(()=>[d(p,{size:"xs",design:"primary",onClick:n[2]||(n[2]=S=>k(y).push({name:"StudentShowFee",params:{uuid:r.selectedStudent.uuid}}))},{default:o(()=>[m(s(l.$trans("global.view",{attribute:l.$trans("student.fee.fee")})),1)]),_:1})]),default:o(()=>[t("div",ys,[(e(!0),a(x,null,$(f.value,S=>(e(),a("div",null,[d(N,null,{default:o(()=>{var G;return[t("span",null,[m(s(S.installmentTitle)+" ",1),d(C,{block:!!k(i).small},{default:o(()=>[m("("+s(S.feeGroupName)+") ",1)]),_:2},1032,["block"]),(G=S.dueDate)!=null&&G.value?(e(),a("span",{key:0,class:M(["text-xs italic block sm:inline",{"text-danger":S.overdueDays>0,"text-success":S.overdueDays<=0}])},[m(s(S.dueOn)+" ",1),S.overdueDays>0?(e(),a("span",{key:0,class:M({block:!!k(i).small})}," ("+s(S.overdueBy)+")",3)):_("",!0)],2)):_("",!0)]),d(C,null,{default:o(()=>[m(s(S.paid.formatted)+" / "+s(S.total.formatted),1)]),_:2},1024)]}),_:2},1024),d(I,{space:"",height:"h-2",percent:S.percent,max:S.total.value,color:S.color},null,8,["percent","max","color"])]))),256))])]),_:1},8,["is-loading"])]),_:3})],64)}}}),ks={key:0},$s={name:"DashboardFormList"},ws=Object.assign($s,{setup(w){se();const y=A(),g=L(),i=D(!1),f=j({forms:[]}),u=async()=>{i.value=!0,await g.dispatch("dashboard/getFormList").then(r=>{f.forms=r,i.value=!1}).catch(r=>{i.value=!1})};return F(async()=>{await u()}),(r,c)=>{const v=b("TextMuted"),l=b("BaseAlert");return f.forms.length>0?(e(),a("div",ks,[(e(!0),a(x,null,$(f.forms,n=>(e(),T(l,{design:"info",size:"xs",key:n.uuid,class:"cursor-pointer",onClick:p=>k(y).push({name:"FormSubmit",params:{uuid:n.uuid}})},{default:o(()=>[m(s(n.name)+" ",1),d(v,{block:"",class:"text-white"},{default:o(()=>[m(s(n.summary),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128))])):_("",!0)}}}),Ss={key:0,class:"mt-4 px-4"},Ts={class:"flex gap-4 scroller-thin-x scroller-hidden scrollbar-track-transparent scrollbar-thumb-body dark:scrollbar-thumb-dm-body"},Cs={class:"inline-block flex-shrink-0 text-center w-32"},Bs={class:"flex justify-center"},Ms=["src"],Ds={class:"mt-2 text-xs text-gray-500 flex flex-col gap-1"},Ls={name:"DashboardCelebration"},js=Object.assign(Ls,{setup(w){A();const y=L(),g=D(!1);D(!1);const i=j({data:[]}),f=async()=>{g.value=!0,await y.dispatch("dashboard/fetchCelebration").then(u=>{g.value=!1,i.data=u}).catch(u=>{g.value=!1})};return F(async()=>{await f()}),(u,r)=>{const c=b("BaseCard"),v=b("ParentTransition");return i.data.length>0?(e(),a("div",Ss,[d(v,{appear:"",visibility:!0},{default:o(()=>[d(c,null,{title:o(()=>[t("h1",null,s(u.$trans("calendar.celebration.celebration")),1)]),default:o(()=>[t("div",Ts,[(e(!0),a(x,null,$(i.data,l=>{var n;return e(),a("div",Cs,[t("div",Bs,[t("img",{src:l.photo,class:"w-24 h-24 rounded-full border-2 border-primary object-cover"},null,8,Ms)]),t("div",Ds,[t("h1",null,s(l.name),1),t("p",null,s(l.detail),1),t("p",null,s((n=l.birthDate.age)==null?void 0:n.short),1)])])}),256))])]),_:1})]),_:1})])):_("",!0)}}}),Fs={key:0},Ns={class:"mt-4 px-4"},As={class:"mt-4 grid grid-cols-3 gap-4"},zs=["onClick"],Ps={class:"relative h-72 flex border border-gray-200 dark:border-gray-700 shadow-lg"},Os={class:"relative overflow-hidden w-full h-full"},Rs=["src"],Is={class:"absolute py-1 bottom-0 w-full bg-black opacity-70 flex items-center justify-center rounded-b-lg"},Gs={class:"px-4 text-center truncate text-gray-50"},Vs={class:"mt-1 block text-gray-50 text-xs"},Ys={name:"DashboardGallery"},Es=Object.assign(Ys,{setup(w){A();const y=L(),g=D(!1),i=D(!1),f=j({images:[],galleries:[]}),u=async(c="")=>{g.value=!0,await y.dispatch("dashboard/fetchGallery").then(v=>{g.value=!1,f.galleries=v}).catch(v=>{g.value=!1})},r=c=>{f.images=c.images.map(v=>v.url),i.value=!i.value};return F(async()=>{await u()}),(c,v)=>{const l=b("ParentTransition");return f.galleries?(e(),a("div",Fs,[t("div",Ns,[d(l,{appear:"",visibility:!0},{default:o(()=>[t("div",As,[(e(!0),a(x,null,$(f.galleries,n=>(e(),a("div",{class:"col-span-3 sm:col-span-1 cursor-pointer",onClick:p=>r(n)},[t("div",Ps,[t("div",Os,[t("img",{src:n.thumbnailUrl,class:"w-full h-full object-cover rounded-lg"},null,8,Rs),t("div",Is,[t("div",Gs,[m(s(n.title)+" ",1),t("span",Vs,s(n.date.formatted),1)])])])])],8,zs))),256))])]),_:1})]),d(k(ue),{toggler:i.value,sources:f.images},null,8,["toggler","sources"])])):_("",!0)}}}),qs={key:1,class:"mt-4 px-4"},Hs={key:2,class:"mt-4 px-4"},Xs={__name:"Index",setup(w){const y=R("calendar.showCelebrationInDashboard"),g=R("gallery.showGalleryInDashboard");return(i,f)=>{const u=b("BaseFlexContainer");return e(),a("div",null,[k(V)("dashboard:stat")?(e(),T(Te,{key:0})):_("",!0),k(V)("form:submit")?(e(),a("div",qs,[d(ws)])):_("",!0),k(z)(["student","guardian"],"any")?(e(),a("div",Hs,[d(xs)])):_("",!0),d(u,{class:"mt-4 px-4"},{default:o(()=>[d(us)]),_:1}),k(y)?(e(),T(js,{key:3})):_("",!0),k(g)&&k(z)(["student","guardian"],"any")?(e(),T(Es,{key:4})):_("",!0)])}}};export{Xs as default};
