import{i as v,H as y,l as C,r as l,q as V,o as u,w as f,e as s,d as r,f as a,a as m,b as c,F as P,s as $,t as S}from"./app-BAwPsakn.js";const k={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},w={class:"mt-4 grid grid-cols-3 gap-6"},F={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},E={key:0,class:"col-span-3"},H={key:1,class:"col-span-3"},T={class:"mt-4 grid grid-cols-3 gap-6"},j={class:"col-span-3"},q={class:"col-span-3 sm:col-span-1"},D={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={name:"ConfigNotification"},R=Object.assign(J,{setup(L){const A=v(),b="config/",i=y(b),g={enableMailNotification:!1,enableMobilePushNotification:!1,enableGuestNotificationBar:!1,enableAppNotificationBar:!1,guestNotificationMessage:"",appNotificationMessage:"",enablePusherNotification:!1,pusherAppId:"",pusherAppKey:"",pusherAppSecret:"",pusherAppCluster:"",type:"notification"},t=C({...g}),h=()=>{A.dispatch("config/testPusherConnection").catch(n=>{})};return(n,e)=>{const B=l("BaseButton"),M=l("CardHeader"),p=l("BaseSwitch"),N=l("BaseTextarea"),d=l("BaseInput"),_=l("FormAction"),U=l("ConfigPage");return u(),V(U,null,{action:f(()=>[t.enablePusherNotification?(u(),V(B,{key:0,design:"primary",onClick:h},{default:f(()=>[$(S(n.$trans("config.notification.test_pusher_connection")),1)]),_:1})):c("",!0)]),default:f(()=>[s(_,{"no-card":"","init-url":b,"data-fetch":"notification","init-form":g,form:t,action:"store","stay-on":"",redirect:"Config"},{default:f(()=>[s(M,{first:"",title:n.$trans("config.notification.notification_config"),description:n.$trans("config.notification.notification_info")},null,8,["title","description"]),r("div",k,[r("div",I,[s(p,{reverse:"",modelValue:t.enableMailNotification,"onUpdate:modelValue":e[0]||(e[0]=o=>t.enableMailNotification=o),name:"enableMailNotification",label:n.$trans("config.notification.props.mail_notification"),error:a(i).enableMailNotification,"onUpdate:error":e[1]||(e[1]=o=>a(i).enableMailNotification=o)},null,8,["modelValue","label","error"])]),r("div",G,[s(p,{reverse:"",modelValue:t.enableMobilePushNotification,"onUpdate:modelValue":e[2]||(e[2]=o=>t.enableMobilePushNotification=o),name:"enableMobilePushNotification",label:n.$trans("config.notification.props.mobile_push_notification"),error:a(i).enableMobilePushNotification,"onUpdate:error":e[3]||(e[3]=o=>a(i).enableMobilePushNotification=o)},null,8,["modelValue","label","error"])])]),r("div",w,[r("div",F,[s(p,{reverse:"",modelValue:t.enableGuestNotificationBar,"onUpdate:modelValue":e[4]||(e[4]=o=>t.enableGuestNotificationBar=o),name:"enableGuestNotificationBar",label:n.$trans("config.notification.props.enable_guest_notification_bar"),error:a(i).enableGuestNotificationBar,"onUpdate:error":e[5]||(e[5]=o=>a(i).enableGuestNotificationBar=o)},null,8,["modelValue","label","error"])]),r("div",K,[s(p,{reverse:"",modelValue:t.enableAppNotificationBar,"onUpdate:modelValue":e[6]||(e[6]=o=>t.enableAppNotificationBar=o),name:"enableAppNotificationBar",label:n.$trans("config.notification.props.enable_app_notification_bar"),error:a(i).enableAppNotificationBar,"onUpdate:error":e[7]||(e[7]=o=>a(i).enableAppNotificationBar=o)},null,8,["modelValue","label","error"])]),t.enableGuestNotificationBar?(u(),m("div",E,[s(N,{rows:2,modelValue:t.guestNotificationMessage,"onUpdate:modelValue":e[8]||(e[8]=o=>t.guestNotificationMessage=o),name:"guestNotificationMessage",placeholder:n.$trans("config.notification.props.guest_notification_message"),error:a(i).guestNotificationMessage,"onUpdate:error":e[9]||(e[9]=o=>a(i).guestNotificationMessage=o)},null,8,["modelValue","placeholder","error"])])):c("",!0),t.enableAppNotificationBar?(u(),m("div",H,[s(N,{rows:2,modelValue:t.appNotificationMessage,"onUpdate:modelValue":e[10]||(e[10]=o=>t.appNotificationMessage=o),name:"appNotificationMessage",placeholder:n.$trans("config.notification.props.app_notification_message"),error:a(i).appNotificationMessage,"onUpdate:error":e[11]||(e[11]=o=>a(i).appNotificationMessage=o)},null,8,["modelValue","placeholder","error"])])):c("",!0)]),r("div",T,[r("div",j,[s(p,{reverse:"",modelValue:t.enablePusherNotification,"onUpdate:modelValue":e[12]||(e[12]=o=>t.enablePusherNotification=o),name:"enablePusherNotification",label:n.$trans("config.notification.props.enable_pusher_notification"),error:a(i).enablePusherNotification,"onUpdate:error":e[13]||(e[13]=o=>a(i).enablePusherNotification=o)},null,8,["modelValue","label","error"])]),t.enablePusherNotification?(u(),m(P,{key:0},[r("div",q,[s(d,{type:"text",modelValue:t.pusherAppId,"onUpdate:modelValue":e[14]||(e[14]=o=>t.pusherAppId=o),name:"pusherAppId",label:n.$trans("config.notification.props.pusher_app_id"),error:a(i).pusherAppId,"onUpdate:error":e[15]||(e[15]=o=>a(i).pusherAppId=o)},null,8,["modelValue","label","error"])]),r("div",D,[s(d,{type:"text",modelValue:t.pusherAppKey,"onUpdate:modelValue":e[16]||(e[16]=o=>t.pusherAppKey=o),name:"pusherAppKey",label:n.$trans("config.notification.props.pusher_app_key"),error:a(i).pusherAppKey,"onUpdate:error":e[17]||(e[17]=o=>a(i).pusherAppKey=o)},null,8,["modelValue","label","error"])]),r("div",O,[s(d,{type:"text",modelValue:t.pusherAppSecret,"onUpdate:modelValue":e[18]||(e[18]=o=>t.pusherAppSecret=o),name:"pusherAppSecret",label:n.$trans("config.notification.props.pusher_app_secret"),error:a(i).pusherAppSecret,"onUpdate:error":e[19]||(e[19]=o=>a(i).pusherAppSecret=o)},null,8,["modelValue","label","error"])]),r("div",z,[s(d,{type:"text",modelValue:t.pusherAppCluster,"onUpdate:modelValue":e[20]||(e[20]=o=>t.pusherAppCluster=o),name:"pusherAppCluster",label:n.$trans("config.notification.props.pusher_app_cluster"),error:a(i).pusherAppCluster,"onUpdate:error":e[21]||(e[21]=o=>a(i).pusherAppCluster=o)},null,8,["modelValue","label","error"])])],64)):c("",!0)])]),_:1},8,["form"])]),_:1})}}});export{R as default};
