import{u as F,j as G,g as d,y as V,r,a as x,o as b,q as s,b as g,e as c,w as t,f as a,d as j,s as u,t as o,F as v}from"./app-BAwPsakn.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},H={name:"GuardianShowBasic"},E=Object.assign(H,{props:{guardian:{type:Object,default(){return{contact:{}}}}},setup(e){const h=F(),n=G("$trans"),f=e,N=d("contact.uniqueIdNumber1Label"),q=d("contact.uniqueIdNumber2Label"),y=d("contact.uniqueIdNumber3Label"),I=d("contact.uniqueIdNumber4Label"),p=d("contact.uniqueIdNumber5Label"),B=d("contact.enableCategoryField"),C=d("contact.enableCasteField");let m=[];return V("guardian:edit")&&(m.push({label:n("general.edit"),path:{name:"GuardianEditBasic",params:{uuid:f.guardian.uuid}}}),m.push({label:n("global.edit",{attribute:n("contact.props.photo")}),path:{name:"GuardianEditPhoto",params:{uuid:f.guardian.uuid}}})),(S,T)=>{const L=r("PageHeaderAction"),P=r("PageHeader"),l=r("BaseDataView"),k=r("BaseCard"),w=r("ParentTransition");return b(),x(v,null,[e.guardian.uuid?(b(),s(P,{key:0,title:a(n)(a(h).meta.label),navs:[{label:a(n)("guardian.guardian"),path:"Guardian"},{label:e.guardian.contact.name,path:{name:"GuardianShow",params:{uuid:e.guardian.uuid}}}]},{default:t(()=>[c(L,{"additional-actions":a(m)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):g("",!0),c(w,{appear:"",visibility:!0},{default:t(()=>[e.guardian.uuid?(b(),s(k,{key:0},{default:t(()=>[j("dl",D,[c(l,{label:a(n)("contact.props.father_name")},{default:t(()=>[u(o(e.guardian.contact.fatherName),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.mother_name")},{default:t(()=>[u(o(e.guardian.contact.motherName),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.birth_date")},{default:t(()=>[u(o(e.guardian.contact.birthDate.formatted),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.gender")},{default:t(()=>[u(o(e.guardian.contact.gender.label),1)]),_:1},8,["label"]),c(l,{label:a(N)},{default:t(()=>[u(o(e.guardian.contact.uniqueIdNumber1),1)]),_:1},8,["label"]),c(l,{label:a(q)},{default:t(()=>[u(o(e.guardian.contact.uniqueIdNumber2),1)]),_:1},8,["label"]),c(l,{label:a(y)},{default:t(()=>[u(o(e.guardian.contact.uniqueIdNumber3),1)]),_:1},8,["label"]),c(l,{label:a(I)},{default:t(()=>[u(o(e.guardian.contact.uniqueIdNumber4),1)]),_:1},8,["label"]),c(l,{label:a(p)},{default:t(()=>[u(o(e.guardian.contact.uniqueIdNumber5),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.occupation")},{default:t(()=>[u(o(e.guardian.contact.occupation),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.annual_income")},{default:t(()=>[u(o(e.guardian.contact.annualIncome),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.organization_name")},{default:t(()=>[u(o(e.guardian.contact.organizationName),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.designation")},{default:t(()=>[u(o(e.guardian.contact.designation),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.birth_place")},{default:t(()=>[u(o(e.guardian.contact.birthPlace),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.nationality")},{default:t(()=>[u(o(e.guardian.contact.nationality),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.mother_tongue")},{default:t(()=>[u(o(e.guardian.contact.motherTongue),1)]),_:1},8,["label"]),c(l,{label:a(n)("contact.props.blood_group")},{default:t(()=>{var i;return[u(o(((i=e.guardian.contact.bloodGroup)==null?void 0:i.label)||"-"),1)]}),_:1},8,["label"]),c(l,{label:a(n)("contact.props.marital_status")},{default:t(()=>{var i;return[u(o(((i=e.guardian.contact.maritalStatus)==null?void 0:i.label)||"-"),1)]}),_:1},8,["label"]),c(l,{label:a(n)("contact.religion.religion")},{default:t(()=>{var i;return[u(o(((i=e.guardian.contact.religion)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"]),a(C)?(b(),s(l,{key:0,label:a(n)("contact.caste.caste")},{default:t(()=>{var i;return[u(o(((i=e.guardian.contact.caste)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"])):g("",!0),a(B)?(b(),s(l,{key:1,label:a(n)("contact.category.category")},{default:t(()=>{var i;return[u(o(((i=e.guardian.contact.category)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"])):g("",!0)])]),_:1})):g("",!0)]),_:1})],64)}}});export{E as default};
