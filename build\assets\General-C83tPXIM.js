import{u as V,j as S,H as B,c as D,l as b,r as n,a as E,o as U,e as a,f as o,w as m,d as i,s as F,t as h,F as j}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3"},I={name:"EmployeePayrollConfigGeneral"},w=Object.assign(I,{setup(T){const y=V(),t=S("$trans"),u="config/",s=B(u),_=D(()=>t("global.placeholder_info",{attribute:p.datePlaceholders})),p=b({datePlaceholders:""}),c={codeNumberPrefix:"",codeNumberDigit:"",codeNumberSuffix:"",type:"payroll"},l=b({...c}),g=f=>{Object.assign(p,{datePlaceholders:f.datePlaceholders.map(e=>e.value).join(", ")})};return(f,e)=>{const N=n("PageHeader"),d=n("BaseInput"),P=n("BaseAlert"),x=n("FormAction"),v=n("ParentTransition");return U(),E(j,null,[a(N,{title:o(t)(o(y).meta.label),navs:[{label:o(t)("employee.employee"),path:"Employee"},{label:o(t)("employee.payroll.payroll"),path:"EmployeePayroll"}]},null,8,["title","navs"]),a(v,{appear:"",visibility:!0},{default:m(()=>[a(x,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:g,"init-url":u,"data-fetch":"payroll",action:"store","init-form":c,form:l,"stay-on":"",redirect:"EmployeePayroll"},{default:m(()=>[i("div",q,[i("div",A,[a(d,{type:"text",modelValue:l.codeNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=r=>l.codeNumberPrefix=r),name:"codeNumberPrefix",label:o(t)("employee.payroll.config.props.number_prefix"),error:o(s).codeNumberPrefix,"onUpdate:error":e[1]||(e[1]=r=>o(s).codeNumberPrefix=r)},null,8,["modelValue","label","error"])]),i("div",R,[a(d,{type:"number",modelValue:l.codeNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=r=>l.codeNumberDigit=r),name:"codeNumberDigit",label:o(t)("employee.payroll.config.props.number_digit"),error:o(s).codeNumberDigit,"onUpdate:error":e[3]||(e[3]=r=>o(s).codeNumberDigit=r)},null,8,["modelValue","label","error"])]),i("div",C,[a(d,{type:"text",modelValue:l.codeNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=r=>l.codeNumberSuffix=r),name:"codeNumberSuffix",label:o(t)("employee.payroll.config.props.number_suffix"),error:o(s).codeNumberSuffix,"onUpdate:error":e[5]||(e[5]=r=>o(s).codeNumberSuffix=r)},null,8,["modelValue","label","error"])]),i("div",H,[a(P,{design:"info"},{default:m(()=>[F(h(_.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{w as default};
