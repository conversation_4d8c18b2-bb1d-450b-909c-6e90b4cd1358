import{u as N,l as I,n as O,r as l,q as m,o as u,w as e,d as w,e as n,b,s,t as o,h as U,j as W,y as $,m as E,f as a,a as F,F as V,v as L}from"./app-BAwPsakn.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(M,{emit:f}){const v=N(),B=f,y={name:"",courses:[]},g=I({...y}),p=I({courses:[],isLoaded:!v.query.courses});return O(async()=>{p.courses=v.query.courses?v.query.courses.split(","):[],p.isLoaded=!0}),(_,h)=>{const i=l("BaseInput"),r=l("BaseSelectSearch"),C=l("FilterForm");return u(),m(C,{"init-form":y,form:g,multiple:["courses"],onHide:h[2]||(h[2]=d=>B("hide"))},{default:e(()=>[w("div",z,[w("div",G,[n(i,{type:"text",modelValue:g.name,"onUpdate:modelValue":h[0]||(h[0]=d=>g.name=d),name:"name",label:_.$trans("academic.batch.props.name")},null,8,["modelValue","label"])]),w("div",J,[p.isLoaded?(u(),m(r,{key:0,multiple:"",name:"courses",label:_.$trans("global.select",{attribute:_.$trans("academic.course.course")}),modelValue:g.courses,"onUpdate:modelValue":h[1]||(h[1]=d=>g.courses=d),"value-prop":"uuid","init-search":p.courses,"search-action":"academic/course/list"},{selectedOption:e(d=>[s(o(d.value.nameWithTerm),1)]),listOption:e(d=>[s(o(d.option.nameWithTerm),1)]),_:1},8,["label","modelValue","init-search"])):b("",!0)])])]),_:1},8,["form"])}}},Q={name:"AcademicBatchList"},Y=Object.assign(Q,{setup(M){const f=U(),v=W("emitter");let B=["filter"];$("batch:create")&&B.unshift("create");let y=[];$("batch:export")&&(y=["print","pdf","excel"]);const g="academic/batch/",p=E(!1),_=I({}),h=i=>{Object.assign(_,i)};return(i,r)=>{const C=l("BaseButton"),d=l("PageHeaderAction"),q=l("PageHeader"),D=l("ParentTransition"),S=l("TextMuted"),k=l("DataCell"),A=l("FloatingMenuItem"),H=l("FloatingMenu"),P=l("DataRow"),R=l("DataTable"),j=l("ListItem");return u(),m(j,{"init-url":g,"additional-query":{details:!0},onSetItems:h},{header:e(()=>[n(q,{title:i.$trans("academic.batch.batch"),navs:[{label:i.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[n(d,{url:"academic/batches/",name:"AcademicBatch",title:i.$trans("academic.batch.batch"),actions:a(B),"dropdown-actions":a(y),"additional-dropdown-actions-query":{details:!0},onToggleFilter:r[1]||(r[1]=t=>p.value=!p.value)},{default:e(()=>[a($)("batch-incharge:read")?(u(),m(C,{key:0,design:"white",onClick:r[0]||(r[0]=t=>a(f).push({name:"AcademicBatchIncharge"}))},{default:e(()=>[s(o(i.$trans("employee.incharge.incharge")),1)]),_:1})):b("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(D,{appear:"",visibility:p.value},{default:e(()=>[n(K,{onRefresh:r[2]||(r[2]=t=>a(v).emit("listItems")),onHide:r[3]||(r[3]=t=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(D,{appear:"",visibility:!0},{default:e(()=>[n(R,{header:_.headers,meta:_.meta,module:"academic.batch",onRefresh:r[5]||(r[5]=t=>a(v).emit("listItems"))},{actionButton:e(()=>[a($)("batch:create")?(u(),m(C,{key:0,onClick:r[4]||(r[4]=t=>a(f).push({name:"AcademicBatchCreate"}))},{default:e(()=>[s(o(i.$trans("global.add",{attribute:i.$trans("academic.batch.batch")})),1)]),_:1})):b("",!0)]),default:e(()=>[(u(!0),F(V,null,L(_.data,t=>(u(),m(P,{key:t.uuid,onDoubleClick:c=>a(f).push({name:"AcademicBatchShow",params:{uuid:t.uuid}})},{default:e(()=>[n(k,{name:"name"},{default:e(()=>[s(o(t.name)+" ",1),t.pgAccount?(u(),m(S,{key:0,block:""},{default:e(()=>[s(o(t.pgAccount),1)]),_:2},1024)):b("",!0)]),_:2},1024),n(k,{name:"course"},{default:e(()=>[s(o(t.course.nameWithTerm),1)]),_:2},1024),n(k,{name:"maxStrength"},{default:e(()=>[s(o(t.currentStrength)+"/"+o(t.maxStrength),1)]),_:2},1024),n(k,{name:"incharge"},{default:e(()=>[(u(!0),F(V,null,L(t.incharges,c=>{var T;return u(),F("div",null,[s(o(((T=c==null?void 0:c.employee)==null?void 0:T.name)||"-")+" ",1),n(S,null,{default:e(()=>[s(o(c==null?void 0:c.period),1)]),_:2},1024)])}),256))]),_:2},1024),n(k,{name:"createdAt"},{default:e(()=>[s(o(t.createdAt.formatted),1)]),_:2},1024),n(k,{name:"action"},{default:e(()=>[n(H,null,{default:e(()=>[n(A,{icon:"fas fa-arrow-circle-right",onClick:c=>a(f).push({name:"AcademicBatchShow",params:{uuid:t.uuid}})},{default:e(()=>[s(o(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),a($)("batch:edit")?(u(),m(A,{key:0,icon:"fas fa-edit",onClick:c=>a(f).push({name:"AcademicBatchEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(o(i.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),a($)("batch:create")?(u(),m(A,{key:1,icon:"fas fa-copy",onClick:c=>a(f).push({name:"AcademicBatchDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[s(o(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),a($)("batch:delete")?(u(),m(A,{key:2,icon:"fas fa-trash",onClick:c=>a(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(o(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
