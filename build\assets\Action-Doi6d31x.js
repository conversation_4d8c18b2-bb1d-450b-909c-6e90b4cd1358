import{H as B,l as v,r as c,z as D,q as F,o as f,w as u,d as l,e as n,f as t,A as P,J as q,u as j,a as y,F as H}from"./app-BAwPsakn.js";const O={class:"grid grid-cols-3 gap-6"},w={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},S={class:"fas fa-question-circle"},z={class:"col-span-3"},C={name:"AcademicDivisionForm"},I=Object.assign(C,{setup(b){const d={name:"",code:"",shortcode:"",program:"",position:"",pgAccount:"",description:""},i="academic/division/",s=B(i),p=v({programs:[]}),a=v({...d}),g=r=>{Object.assign(p,r)},_=r=>{var e;Object.assign(d,{...r,program:((e=r.program)==null?void 0:e.uuid)||""}),Object.assign(a,q(d))};return(r,e)=>{const m=c("BaseInput"),V=c("BaseSelect"),A=c("BaseTextarea"),$=c("FormAction"),U=D("tooltip");return f(),F($,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:g,"init-url":i,"init-form":d,form:a,"set-form":_,redirect:"AcademicDivision"},{default:u(()=>[l("div",O,[l("div",w,[n(m,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=o=>a.name=o),name:"name",label:r.$trans("academic.division.props.name"),error:t(s).name,"onUpdate:error":e[1]||(e[1]=o=>t(s).name=o),autofocus:""},null,8,["modelValue","label","error"])]),l("div",R,[n(m,{type:"text",modelValue:a.code,"onUpdate:modelValue":e[2]||(e[2]=o=>a.code=o),name:"code",label:r.$trans("academic.division.props.code"),error:t(s).code,"onUpdate:error":e[3]||(e[3]=o=>t(s).code=o)},null,8,["modelValue","label","error"])]),l("div",T,[n(m,{type:"text",modelValue:a.shortcode,"onUpdate:modelValue":e[4]||(e[4]=o=>a.shortcode=o),name:"shortcode",label:r.$trans("academic.division.props.shortcode"),error:t(s).shortcode,"onUpdate:error":e[5]||(e[5]=o=>t(s).shortcode=o)},null,8,["modelValue","label","error"])]),l("div",k,[n(V,{modelValue:a.program,"onUpdate:modelValue":e[6]||(e[6]=o=>a.program=o),name:"program",label:r.$trans("academic.program.program"),"label-prop":"nameWithDepartment","value-prop":"uuid",options:p.programs,error:t(s).program,"onUpdate:error":e[7]||(e[7]=o=>t(s).program=o)},null,8,["modelValue","label","options","error"])]),l("div",E,[n(m,{type:"text",modelValue:a.pgAccount,"onUpdate:modelValue":e[8]||(e[8]=o=>a.pgAccount=o),name:"pgAccount",label:r.$trans("finance.config.props.pg_account"),error:t(s).pgAccount,"onUpdate:error":e[9]||(e[9]=o=>t(s).pgAccount=o)},{"additional-label":u(()=>[P(l("i",S,null,512),[[U,r.$trans("finance.config.props.pg_account_info")]])]),_:1},8,["modelValue","label","error"])]),l("div",z,[n(A,{modelValue:a.description,"onUpdate:modelValue":e[10]||(e[10]=o=>a.description=o),name:"description",label:r.$trans("academic.division.props.description"),error:t(s).description,"onUpdate:error":e[11]||(e[11]=o=>t(s).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),N={name:"AcademicDivisionAction"},L=Object.assign(N,{setup(b){const d=j();return(i,s)=>{const p=c("PageHeaderAction"),a=c("PageHeader"),g=c("ParentTransition");return f(),y(H,null,[n(a,{title:i.$trans(t(d).meta.trans,{attribute:i.$trans(t(d).meta.label)}),navs:[{label:i.$trans("academic.academic"),path:"Academic"},{label:i.$trans("academic.division.division"),path:"AcademicDivisionList"}]},{default:u(()=>[n(p,{name:"AcademicDivision",title:i.$trans("academic.division.division"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(g,{appear:"",visibility:!0},{default:u(()=>[n(I)]),_:1})],64)}}});export{L as default};
