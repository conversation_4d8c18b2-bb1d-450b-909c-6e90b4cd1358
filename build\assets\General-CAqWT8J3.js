import{u as V,j as S,H as B,c as D,l as b,r as i,a as R,o as U,e as n,f as t,w as u,d as c,s as F,t as j,F as h}from"./app-BAwPsakn.js";const q={class:"grid grid-cols-3 gap-4"},A={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3"},I={name:"RecruitmentConfigGeneral"},w=Object.assign(I,{setup(T){const v=V(),o=S("$trans"),m="config/",s=B(m),y=D(()=>o("global.placeholder_info",{attribute:d.datePlaceholders})),d=b({datePlaceholders:""}),p={vacancyNumberPrefix:"",vacancyNumberSuffix:"",vacancyNumberDigit:0,type:"recruitment"},r=b({...p}),_=f=>{Object.assign(d,{datePlaceholders:f.datePlaceholders.map(e=>e.value).join(", ")})};return(f,e)=>{const g=i("PageHeader"),l=i("BaseInput"),x=i("BaseAlert"),N=i("FormAction"),P=i("ParentTransition");return U(),R(h,null,[n(g,{title:t(o)(t(v).meta.label),navs:[{label:t(o)("recruitment.recruitment"),path:"Recruitment"}]},null,8,["title","navs"]),n(P,{appear:"",visibility:!0},{default:u(()=>[n(N,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:_,"init-url":m,"data-fetch":"recruitment","init-form":p,form:r,action:"store","stay-on":"",redirect:"Recruitment"},{default:u(()=>[c("div",q,[c("div",A,[n(l,{type:"text",modelValue:r.vacancyNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=a=>r.vacancyNumberPrefix=a),name:"vacancyNumberPrefix",label:t(o)("recruitment.config.props.vacancy_number_prefix"),error:t(s).vacancyNumberPrefix,"onUpdate:error":e[1]||(e[1]=a=>t(s).vacancyNumberPrefix=a)},null,8,["modelValue","label","error"])]),c("div",C,[n(l,{type:"number",modelValue:r.vacancyNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=a=>r.vacancyNumberDigit=a),name:"vacancyNumberDigit",label:t(o)("recruitment.config.props.vacancy_number_digit"),error:t(s).vacancyNumberDigit,"onUpdate:error":e[3]||(e[3]=a=>t(s).vacancyNumberDigit=a)},null,8,["modelValue","label","error"])]),c("div",E,[n(l,{type:"text",modelValue:r.vacancyNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=a=>r.vacancyNumberSuffix=a),name:"vacancyNumberSuffix",label:t(o)("recruitment.config.props.vacancy_number_suffix"),error:t(s).vacancyNumberSuffix,"onUpdate:error":e[5]||(e[5]=a=>t(s).vacancyNumberSuffix=a)},null,8,["modelValue","label","error"])]),c("div",H,[n(x,{size:"xs",design:"info"},{default:u(()=>[F(j(y.value),1)]),_:1})])])]),_:1},8,["form"])]),_:1})],64)}}});export{w as default};
