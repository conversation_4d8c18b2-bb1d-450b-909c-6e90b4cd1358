import{u as U,j as C,H as S,l as M,r as m,a as d,o as y,e as t,f as l,w as V,d as s,b as u,F as b}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-4"},L={class:"col-span-3"},w={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3 sm:col-span-1"},_={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3"},R={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},F={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3"},A={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3"},T={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3"},D={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},h={class:"col-span-3"},ee={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},te={name:"FinanceConfigInvoice"},pe=Object.assign(te,{setup(re){const f=U(),r=C("$trans"),c="config/",o=S(c),v={enablePaypal:"",enableLivePaypalMode:!1,paypalClient:"",paypalSecret:"",enableStripe:"",enableLiveStripeMode:!1,stripeClient:"",stripeSecret:"",enablePayzone:!1,enableLivePayzoneMode:!1,payzoneMerchant:"",payzoneSecretKey:"",payzoneNotificationKey:"",enableRazorpay:"",enableLiveRazorpayMode:!1,razorpayClient:"",razorpaySecret:"",enablePaystack:"",enableLivePaystackMode:!1,paystackClient:"",paystackSecret:"",enableCcavenue:"",enableLiveCcavenueMode:!1,ccavenueMerchant:"",ccavenueClient:"",ccavenueSecret:"",enableBilldesk:"",enableLiveBilldeskMode:!1,billdeskVersion:"",billdeskMerchant:"",billdeskClient:"",billdeskSecret:"",type:"finance"},n=M({...v});return(se,e)=>{const k=m("PageHeader"),p=m("BaseSwitch"),i=m("BaseInput"),g=m("FormAction"),z=m("ParentTransition");return y(),d(b,null,[t(k,{title:l(r)(l(f).meta.label),navs:[{label:l(r)("finance.finance"),path:"Finance"}]},null,8,["title","navs"]),t(z,{appear:"",visibility:!0},{default:V(()=>[t(g,{"init-url":c,"data-fetch":"finance",action:"store","init-form":v,form:n,"stay-on":"",redirect:"Finance"},{default:V(()=>[s("div",P,[s("div",L,[t(p,{reverse:"",modelValue:n.enablePaypal,"onUpdate:modelValue":e[0]||(e[0]=a=>n.enablePaypal=a),name:"enablePaypal",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"PayPal"}),error:l(o).enablePaypal,"onUpdate:error":e[1]||(e[1]=a=>l(o).enablePaypal=a)},null,8,["modelValue","label","error"])]),n.enablePaypal?(y(),d(b,{key:0},[s("div",w,[t(i,{type:"text",modelValue:n.paypalClient,"onUpdate:modelValue":e[2]||(e[2]=a=>n.paypalClient=a),name:"paypalClient",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"PayPal"}),error:l(o).paypalClient,"onUpdate:error":e[3]||(e[3]=a=>l(o).paypalClient=a)},null,8,["modelValue","label","error"])]),s("div",B,[t(i,{type:"text",modelValue:n.paypalSecret,"onUpdate:modelValue":e[4]||(e[4]=a=>n.paypalSecret=a),name:"paypalSecret",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"PayPal"}),error:l(o).paypalSecret,"onUpdate:error":e[5]||(e[5]=a=>l(o).paypalSecret=a)},null,8,["modelValue","label","error"])]),s("div",_,[t(p,{vertical:"",modelValue:n.enableLivePaypalMode,"onUpdate:modelValue":e[6]||(e[6]=a=>n.enableLivePaypalMode=a),name:"enableLivePaypalMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"PayPal"}),error:l(o).enableLivePaypalMode,"onUpdate:error":e[7]||(e[7]=a=>l(o).enableLivePaypalMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0),s("div",x,[t(p,{reverse:"",modelValue:n.enableStripe,"onUpdate:modelValue":e[8]||(e[8]=a=>n.enableStripe=a),name:"enableStripe",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"Stripe"}),error:l(o).enableStripe,"onUpdate:error":e[9]||(e[9]=a=>l(o).enableStripe=a)},null,8,["modelValue","label","error"])]),n.enableStripe?(y(),d(b,{key:1},[s("div",R,[t(i,{type:"text",modelValue:n.stripeClient,"onUpdate:modelValue":e[10]||(e[10]=a=>n.stripeClient=a),name:"stripeClient",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"Stripe"}),error:l(o).stripeClient,"onUpdate:error":e[11]||(e[11]=a=>l(o).stripeClient=a)},null,8,["modelValue","label","error"])]),s("div",K,[t(i,{type:"text",modelValue:n.stripeSecret,"onUpdate:modelValue":e[12]||(e[12]=a=>n.stripeSecret=a),name:"stripeSecret",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"Stripe"}),error:l(o).stripeSecret,"onUpdate:error":e[13]||(e[13]=a=>l(o).stripeSecret=a)},null,8,["modelValue","label","error"])]),s("div",F,[t(p,{vertical:"",modelValue:n.enableLiveStripeMode,"onUpdate:modelValue":e[14]||(e[14]=a=>n.enableLiveStripeMode=a),name:"enableLiveStripeMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"Stripe"}),error:l(o).enableLiveStripeMode,"onUpdate:error":e[15]||(e[15]=a=>l(o).enableLiveStripeMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0),s("div",N,[t(p,{reverse:"",modelValue:n.enablePayzone,"onUpdate:modelValue":e[16]||(e[16]=a=>n.enablePayzone=a),name:"enablePayzone",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"Payzone"}),error:l(o).enablePayzone,"onUpdate:error":e[17]||(e[17]=a=>l(o).enablePayzone=a)},null,8,["modelValue","label","error"])]),n.enablePayzone?(y(),d(b,{key:2},[s("div",A,[t(i,{type:"text",modelValue:n.payzoneMerchant,"onUpdate:modelValue":e[18]||(e[18]=a=>n.payzoneMerchant=a),name:"payzoneMerchant",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"Payzone"}),error:l(o).payzoneMerchant,"onUpdate:error":e[19]||(e[19]=a=>l(o).payzoneMerchant=a)},null,8,["modelValue","label","error"])]),s("div",j,[t(i,{type:"text",modelValue:n.payzoneSecretKey,"onUpdate:modelValue":e[20]||(e[20]=a=>n.payzoneSecretKey=a),name:"payzoneSecretKey",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"Payzone"}),error:l(o).payzoneSecretKey,"onUpdate:error":e[21]||(e[21]=a=>l(o).payzoneSecretKey=a)},null,8,["modelValue","label","error"])]),s("div",E,[t(i,{type:"text",modelValue:n.payzoneNotificationKey,"onUpdate:modelValue":e[22]||(e[22]=a=>n.payzoneNotificationKey=a),name:"payzoneNotificationKey",label:l(r)("finance.config.props.payment_gateway_notification",{attribute:"Payzone"}),error:l(o).payzoneNotificationKey,"onUpdate:error":e[23]||(e[23]=a=>l(o).payzoneNotificationKey=a)},null,8,["modelValue","label","error"])]),s("div",H,[t(p,{vertical:"",modelValue:n.enableLivePayzoneMode,"onUpdate:modelValue":e[24]||(e[24]=a=>n.enableLivePayzoneMode=a),name:"enableLivePayzoneMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"Payzone"}),error:l(o).enableLivePayzoneMode,"onUpdate:error":e[25]||(e[25]=a=>l(o).enableLivePayzoneMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0),s("div",I,[t(p,{reverse:"",modelValue:n.enableRazorpay,"onUpdate:modelValue":e[26]||(e[26]=a=>n.enableRazorpay=a),name:"enableRazorpay",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"Razorpay"}),error:l(o).enableRazorpay,"onUpdate:error":e[27]||(e[27]=a=>l(o).enableRazorpay=a)},null,8,["modelValue","label","error"])]),n.enableRazorpay?(y(),d(b,{key:3},[s("div",T,[t(i,{type:"text",modelValue:n.razorpayClient,"onUpdate:modelValue":e[28]||(e[28]=a=>n.razorpayClient=a),name:"razorpayClient",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"Razorpay"}),error:l(o).razorpayClient,"onUpdate:error":e[29]||(e[29]=a=>l(o).razorpayClient=a)},null,8,["modelValue","label","error"])]),s("div",$,[t(i,{type:"text",modelValue:n.razorpaySecret,"onUpdate:modelValue":e[30]||(e[30]=a=>n.razorpaySecret=a),name:"razorpaySecret",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"Razorpay"}),error:l(o).razorpaySecret,"onUpdate:error":e[31]||(e[31]=a=>l(o).razorpaySecret=a)},null,8,["modelValue","label","error"])]),s("div",O,[t(p,{vertical:"",modelValue:n.enableLiveRazorpayMode,"onUpdate:modelValue":e[32]||(e[32]=a=>n.enableLiveRazorpayMode=a),name:"enableLiveRazorpayMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"Razorpay"}),error:l(o).enableLiveRazorpayMode,"onUpdate:error":e[33]||(e[33]=a=>l(o).enableLiveRazorpayMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0),s("div",q,[t(p,{reverse:"",modelValue:n.enablePaystack,"onUpdate:modelValue":e[34]||(e[34]=a=>n.enablePaystack=a),name:"enablePaystack",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"Paystack"}),error:l(o).enablePaystack,"onUpdate:error":e[35]||(e[35]=a=>l(o).enablePaystack=a)},null,8,["modelValue","label","error"])]),n.enablePaystack?(y(),d(b,{key:4},[s("div",D,[t(i,{type:"text",modelValue:n.paystackClient,"onUpdate:modelValue":e[36]||(e[36]=a=>n.paystackClient=a),name:"paystackClient",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"Paystack"}),error:l(o).paystackClient,"onUpdate:error":e[37]||(e[37]=a=>l(o).paystackClient=a)},null,8,["modelValue","label","error"])]),s("div",G,[t(i,{type:"text",modelValue:n.paystackSecret,"onUpdate:modelValue":e[38]||(e[38]=a=>n.paystackSecret=a),name:"paystackSecret",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"Paystack"}),error:l(o).paystackSecret,"onUpdate:error":e[39]||(e[39]=a=>l(o).paystackSecret=a)},null,8,["modelValue","label","error"])]),s("div",J,[t(p,{vertical:"",modelValue:n.enableLivePaystackMode,"onUpdate:modelValue":e[40]||(e[40]=a=>n.enableLivePaystackMode=a),name:"enableLivePaystackMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"Paystack"}),error:l(o).enableLivePaystackMode,"onUpdate:error":e[41]||(e[41]=a=>l(o).enableLivePaystackMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0),s("div",Q,[t(p,{reverse:"",modelValue:n.enableCcavenue,"onUpdate:modelValue":e[42]||(e[42]=a=>n.enableCcavenue=a),name:"enableCcavenue",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"CCAvenue"}),error:l(o).enableCcavenue,"onUpdate:error":e[43]||(e[43]=a=>l(o).enableCcavenue=a)},null,8,["modelValue","label","error"])]),n.enableCcavenue?(y(),d(b,{key:5},[s("div",W,[t(i,{type:"text",modelValue:n.ccavenueMerchant,"onUpdate:modelValue":e[44]||(e[44]=a=>n.ccavenueMerchant=a),name:"ccavenueMerchant",label:l(r)("finance.config.props.payment_gateway_merchant",{attribute:"CCAvenue"}),error:l(o).ccavenueMerchant,"onUpdate:error":e[45]||(e[45]=a=>l(o).ccavenueMerchant=a)},null,8,["modelValue","label","error"])]),s("div",X,[t(i,{type:"text",modelValue:n.ccavenueClient,"onUpdate:modelValue":e[46]||(e[46]=a=>n.ccavenueClient=a),name:"ccavenueClient",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"CCAvenue"}),error:l(o).ccavenueClient,"onUpdate:error":e[47]||(e[47]=a=>l(o).ccavenueClient=a)},null,8,["modelValue","label","error"])]),s("div",Y,[t(i,{type:"text",modelValue:n.ccavenueSecret,"onUpdate:modelValue":e[48]||(e[48]=a=>n.ccavenueSecret=a),name:"ccavenueSecret",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"CCAvenue"}),error:l(o).ccavenueSecret,"onUpdate:error":e[49]||(e[49]=a=>l(o).ccavenueSecret=a)},null,8,["modelValue","label","error"])]),s("div",Z,[t(p,{vertical:"",modelValue:n.enableLiveCcavenueMode,"onUpdate:modelValue":e[50]||(e[50]=a=>n.enableLiveCcavenueMode=a),name:"enableLiveCcavenueMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"CCAvenue"}),error:l(o).enableLiveCcavenueMode,"onUpdate:error":e[51]||(e[51]=a=>l(o).enableLiveCcavenueMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0),s("div",h,[t(p,{reverse:"",modelValue:n.enableBilldesk,"onUpdate:modelValue":e[52]||(e[52]=a=>n.enableBilldesk=a),name:"enableBilldesk",label:l(r)("finance.config.props.enable_payment_gateway",{attribute:"Billdesk"}),error:l(o).enableBilldesk,"onUpdate:error":e[53]||(e[53]=a=>l(o).enableBilldesk=a)},null,8,["modelValue","label","error"])]),n.enableBilldesk?(y(),d(b,{key:6},[s("div",ee,[t(i,{type:"text",modelValue:n.billdeskVersion,"onUpdate:modelValue":e[54]||(e[54]=a=>n.billdeskVersion=a),name:"billdeskVersion",label:l(r)("finance.config.props.payment_gateway_version",{attribute:"Billdesk"}),error:l(o).billdeskVersion,"onUpdate:error":e[55]||(e[55]=a=>l(o).billdeskVersion=a)},null,8,["modelValue","label","error"])]),s("div",ae,[t(i,{type:"text",modelValue:n.billdeskMerchant,"onUpdate:modelValue":e[56]||(e[56]=a=>n.billdeskMerchant=a),name:"billdeskMerchant",label:l(r)("finance.config.props.payment_gateway_merchant",{attribute:"Billdesk"}),error:l(o).billdeskMerchant,"onUpdate:error":e[57]||(e[57]=a=>l(o).billdeskMerchant=a)},null,8,["modelValue","label","error"])]),s("div",le,[t(i,{type:"text",modelValue:n.billdeskClient,"onUpdate:modelValue":e[58]||(e[58]=a=>n.billdeskClient=a),name:"billdeskClient",label:l(r)("finance.config.props.payment_gateway_client",{attribute:"Billdesk"}),error:l(o).billdeskClient,"onUpdate:error":e[59]||(e[59]=a=>l(o).billdeskClient=a)},null,8,["modelValue","label","error"])]),s("div",ne,[t(i,{type:"text",modelValue:n.billdeskSecret,"onUpdate:modelValue":e[60]||(e[60]=a=>n.billdeskSecret=a),name:"billdeskSecret",label:l(r)("finance.config.props.payment_gateway_secret",{attribute:"Billdesk"}),error:l(o).billdeskSecret,"onUpdate:error":e[61]||(e[61]=a=>l(o).billdeskSecret=a)},null,8,["modelValue","label","error"])]),s("div",oe,[t(p,{vertical:"",modelValue:n.enableLiveBilldeskMode,"onUpdate:modelValue":e[62]||(e[62]=a=>n.enableLiveBilldeskMode=a),name:"enableLiveBilldeskMode",label:l(r)("finance.config.props.payment_gateway_mode",{attribute:"Billdesk"}),error:l(o).enableLiveBilldeskMode,"onUpdate:error":e[63]||(e[63]=a=>l(o).enableLiveBilldeskMode=a)},null,8,["modelValue","label","error"])])],64)):u("",!0)])]),_:1},8,["form"])]),_:1})],64)}}});export{pe as default};
