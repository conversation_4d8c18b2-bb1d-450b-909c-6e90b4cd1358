import{u as A,j as B,y as C,r as d,a as P,o as i,q as m,b as g,e as n,w as c,f as t,d as w,s as l,t as r,F as D}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},V={name:"ContactShowContact"},v=Object.assign(V,{props:{guardian:{type:Object,default(){return{}}}},setup(a){const b=A(),e=B("$trans"),p=a;let u=[];return C("guardian:edit")&&u.push({label:e("general.edit"),path:{name:"GuardianEditContact",params:{uuid:p.guardian.uuid}}}),(k,x)=>{const f=d("PageHeaderAction"),y=d("PageHeader"),s=d("BaseDataView"),h=d("BaseCard"),_=d("ParentTransition");return i(),P(D,null,[a.guardian.uuid?(i(),m(y,{key:0,title:t(e)(t(b).meta.label),navs:[{label:t(e)("guardian.guardian"),path:"Guardian"},{label:a.guardian.contact.name,path:{name:"GuardianShow",params:{uuid:a.guardian.uuid}}}]},{default:c(()=>[n(f,{"additional-actions":t(u)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):g("",!0),n(_,{appear:"",visibility:!0},{default:c(()=>[a.guardian.uuid?(i(),m(h,{key:0},{default:c(()=>[w("dl",N,[n(s,{label:t(e)("contact.props.contact_number")},{default:c(()=>[l(r(a.guardian.contact.contactNumber),1)]),_:1},8,["label"]),n(s,{label:t(e)("global.alternate",{attribute:t(e)("contact.props.contact_number")})},{default:c(()=>{var o;return[l(r((o=a.guardian.contact.alternateRecords)==null?void 0:o.contactNumber),1)]}),_:1},8,["label"]),n(s),n(s,{label:t(e)("contact.props.email")},{default:c(()=>[l(r(a.guardian.contact.email),1)]),_:1},8,["label"]),n(s,{label:t(e)("global.alternate",{attribute:t(e)("contact.props.email")})},{default:c(()=>{var o;return[l(r((o=a.guardian.contact.alternateRecords)==null?void 0:o.email),1)]}),_:1},8,["label"]),n(s),n(s,{class:"col-span-1 sm:col-span-3",label:t(e)("contact.props.present_address")},{default:c(()=>[l(r(a.guardian.contact.presentAddressDisplay),1)]),_:1},8,["label"]),n(s,{class:"col-span-1 sm:col-span-3",label:t(e)("contact.props.permanent_address")},{default:c(()=>[l(r(a.guardian.contact.sameAsPresentAddress?a.guardian.contact.presentAddressDisplay:a.guardian.contact.permanentAddressDisplay),1)]),_:1},8,["label"])])]),_:1})):g("",!0)]),_:1})],64)}}});export{v as default};
