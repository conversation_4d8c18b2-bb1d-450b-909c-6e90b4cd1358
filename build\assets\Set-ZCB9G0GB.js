import{u as w,h as N,i as E,j as L,H as O,l as F,n as T,r as a,a as m,o as d,q as G,b as V,e as r,w as f,f as s,d as u,F as b,s as z,t as D,v as M}from"./app-BAwPsakn.js";const x={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},J={class:"mt-4 grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-2"},W={name:"StudentFeeSet"},Y=Object.assign(W,{props:{student:{type:Object,default(){return{}}}},setup(c){const p=w(),C=N();E();const B=L("emitter"),g=c,v={feeConcession:"",transportCircle:"",direction:"",optedFeeHeads:[],confirm:!1},S="student/fee/",i=O(S),l=F({directions:[],transportCircles:[],feeConcessions:[],frequencies:[],optionalFeeHeads:[]}),o=F({...v}),k=[],y=n=>{Object.assign(l,n)},U=()=>{B.emit("studentUpdated"),C.push({name:"StudentShowFee",params:{uuid:p.params.uuid}})};return T(async()=>{g.student.hasFeeStructureSet&&C.push({name:"StudentShowFee",params:{uuid:g.student.uuid}})}),(n,t)=>{const H=a("PageHeaderAction"),P=a("PageHeader"),_=a("BaseSelect"),h=a("BaseLabel"),q=a("BaseRadioGroup"),A=a("BaseArrayCheckbox"),R=a("FormAction"),$=a("ParentTransition");return d(),m(b,null,[c.student.uuid?(d(),G(P,{key:0,title:n.$trans(s(p).meta.trans,{attribute:n.$trans(s(p).meta.label)}),navs:[{label:n.$trans("student.student"),path:"Student"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}}]},{default:f(()=>[r(H,{"additional-actions":k})]),_:1},8,["title","navs"])):V("",!0),r($,{appear:"",visibility:!0},{default:f(()=>[r(R,{"pre-requisites":{uuid:s(p).params.uuid},onSetPreRequisites:y,"no-data-fetch":"","init-url":S,action:"set","init-form":v,form:o,"keep-adding":!1,"after-submit":U,"validate-unchanged":!1,confirmation:""},{default:f(()=>[u("div",x,[u("div",I,[r(_,{modelValue:o.feeConcession,"onUpdate:modelValue":t[0]||(t[0]=e=>o.feeConcession=e),name:"feeConcession",label:n.$trans("finance.fee_concession.fee_concession"),options:l.feeConcessions,"label-prop":"name","value-prop":"uuid",error:s(i).feeConcession,"onUpdate:error":t[1]||(t[1]=e=>s(i).feeConcession=e)},null,8,["modelValue","label","options","error"])])]),u("div",J,[u("div",K,[r(_,{modelValue:o.transportCircle,"onUpdate:modelValue":t[2]||(t[2]=e=>o.transportCircle=e),name:"transportCircle",label:n.$trans("transport.circle.circle"),options:l.transportCircles,"label-prop":"name","value-prop":"uuid",error:s(i).transportCircle,"onUpdate:error":t[3]||(t[3]=e=>s(i).transportCircle=e)},null,8,["modelValue","label","options","error"])]),u("div",Q,[o.transportCircle?(d(),m(b,{key:0},[r(h,null,{default:f(()=>[z(D(n.$trans("transport.circle.direction")),1)]),_:1}),r(q,{"top-margin":"",options:l.directions,name:"direction",modelValue:o.direction,"onUpdate:modelValue":t[4]||(t[4]=e=>o.direction=e),error:s(i).direction,"onUpdate:error":t[5]||(t[5]=e=>s(i).direction=e),horizontal:""},null,8,["options","modelValue","error"])],64)):V("",!0)]),(d(!0),m(b,null,M(l.optionalFeeHeads,e=>(d(),m("div",{class:"col-span-3",key:e.uuid},[r(A,{items:o.optedFeeHeads,"onUpdate:items":t[6]||(t[6]=j=>o.optedFeeHeads=j),value:e.uuid,label:e.name},null,8,["items","value","label"])]))),128))])]),_:1},8,["pre-requisites","form"])]),_:1})],64)}}});export{Y as default};
