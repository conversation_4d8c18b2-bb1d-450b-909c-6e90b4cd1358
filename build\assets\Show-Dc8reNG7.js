import{i as P,u as A,h as H,l as I,r as u,a as _,o as i,e as l,w as t,f as r,q as c,b as d,d as N,s,t as o,F as f}from"./app-BAwPsakn.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"CustomFieldShow"},j=Object.assign(T,{setup(q){P();const m=A(),b=H(),g={},y="customField/",e=I({...g}),B=a=>{Object.assign(e,a)};return(a,p)=>{const $=u("PageHeaderAction"),v=u("PageHeader"),n=u("BaseDataView"),h=u("BaseBadge"),C=u("BaseButton"),k=u("ShowButton"),F=u("BaseCard"),w=u("ShowItem"),S=u("ParentTransition");return i(),_(f,null,[l(v,{title:a.$trans(r(m).meta.trans,{attribute:a.$trans(r(m).meta.label)}),navs:[{label:a.$trans("custom_field.custom_field"),path:"CustomField"}]},{default:t(()=>[l($,{name:"CustomField",title:a.$trans("custom_field.custom_field"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(S,{appear:"",visibility:!0},{default:t(()=>[l(w,{"init-url":y,uuid:r(m).params.uuid,onSetItem:B,onRedirectTo:p[1]||(p[1]=V=>r(b).push({name:"CustomField"}))},{default:t(()=>[e.uuid?(i(),c(F,{key:0},{title:t(()=>[s(o(e.form.label),1)]),footer:t(()=>[l(k,null,{default:t(()=>[l(C,{design:"primary",onClick:p[0]||(p[0]=V=>r(b).push({name:"CustomFieldEdit",params:{uuid:e.uuid}}))},{default:t(()=>[s(o(a.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[N("dl",R,[l(n,{label:a.$trans("custom_field.props.label")},{default:t(()=>[s(o(e.label),1)]),_:1},8,["label"]),l(n,{label:a.$trans("custom_field.props.type")},{default:t(()=>[s(o(e.type.label)+" ",1),e.isRequired?(i(),c(h,{key:0,design:"primary"},{default:t(()=>[s(o(a.$trans("custom_field.props.is_required")),1)]),_:1})):d("",!0)]),_:1},8,["label"]),l(n,{label:a.$trans("custom_field.props.position")},{default:t(()=>[s(o(e.position),1)]),_:1},8,["label"]),e.type.value=="text_input"||e.type.value=="multi_line_text_input"?(i(),_(f,{key:0},[l(n,{label:a.$trans("custom_field.props.min_length")},{default:t(()=>[s(o(e.minLength),1)]),_:1},8,["label"]),l(n,{label:a.$trans("custom_field.props.max_length")},{default:t(()=>[s(o(e.maxLength),1)]),_:1},8,["label"])],64)):d("",!0),e.type.value=="number_input"||e.type.value=="currency_input"?(i(),_(f,{key:1},[l(n,{label:a.$trans("custom_field.props.min_value")},{default:t(()=>[s(o(e.minValue),1)]),_:1},8,["label"]),l(n,{label:a.$trans("custom_field.props.max_value")},{default:t(()=>[s(o(e.maxValue),1)]),_:1},8,["label"])],64)):d("",!0),e.type.value=="select_input"||e.type.value=="multi_select_input"||e.type.value=="radio_input"||e.type.value=="checkbox_input"?(i(),c(n,{key:2,label:a.$trans("custom_field.props.options")},{default:t(()=>[s(o(e.options),1)]),_:1},8,["label"])):d("",!0),l(n,{label:a.$trans("general.created_at")},{default:t(()=>[s(o(e.createdAt.formatted),1)]),_:1},8,["label"]),l(n,{label:a.$trans("general.updated_at")},{default:t(()=>[s(o(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):d("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
