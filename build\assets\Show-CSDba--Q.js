import{i as H,u as L,h as R,j as w,l as v,H as U,m as j,K as z,n as F,B as G,r as p,a as C,o as u,q as r,b as m,e as d,w as n,f as e,F as O,s,t as o,y as A,d as K}from"./app-BAwPsakn.js";const J={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-4"},W={name:"ExamOnlineExamShow"},Z=Object.assign(W,{setup(X){H();const c=L(),g=R(),a=w("$trans"),b=w("emitter");let B=["list"];const S={},$={},k="exam/onlineExam/";v({}),U(k),v({...$});const f=j(!1),t=v({...S}),I=E=>{Object.assign(t,E)};return z(()=>c.params.uuid,E=>{f.value=!0}),F(()=>{G(["student","guardian"],"any")&&g.push({name:"ExamOnlineExam",params:{uuid:t.uuid}})}),(E,i)=>{const _=p("BaseButton"),h=p("DropdownItem"),P=p("PageHeaderAction"),Q=p("PageHeader"),q=p("BaseTab"),x=p("BaseDataView"),T=p("TextMuted"),D=p("BaseCard"),M=p("router-view"),N=p("ShowItem"),V=p("ParentTransition");return u(),C(O,null,[t.uuid?(u(),r(Q,{key:0,title:e(a)(e(c).meta.trans,{attribute:e(a)(e(c).meta.label)}),navs:[{label:e(a)("exam.online_exam.online_exam"),path:"ExamOnlineExamList"},{label:t.title,path:{name:"ExamOnlineExamShow",params:{uuid:t.uuid}}}]},{default:n(()=>[d(P,{name:"ExamOnlineExam",title:e(a)("exam.online_exam.online_exam"),actions:e(B)},{dropdown:n(()=>[e(A)("online-exam:edit")&&t.isEditable?(u(),r(h,{key:0,icon:"fas fa-pencil",onClick:i[2]||(i[2]=l=>e(g).push({name:"ExamOnlineExamEdit",params:{uuid:t.uuid}}))},{default:n(()=>[s(o(e(a)("global.edit",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0),e(A)("online-exam:delete")&&t.canDelete?(u(),r(h,{key:1,icon:"fas fa-trash",onClick:i[3]||(i[3]=l=>e(b).emit("showDeleteItem",{uuid:t.uuid}))},{default:n(()=>[s(o(e(a)("global.delete",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0)]),default:n(()=>[t.publishedAt.value?m("",!0):(u(),C(O,{key:0},[e(c).name=="ExamOnlineExamQuestionList"&&t.canManageQuestion?(u(),r(_,{key:0,design:"white",onClick:i[0]||(i[0]=l=>e(b).emit("addQuestion"))},{default:n(()=>[s(o(e(a)("global.add",{attribute:e(a)("exam.online_exam.props.question")})),1)]),_:1})):m("",!0),e(c).name=="ExamOnlineExamQuestionList"&&t.canManageQuestion?(u(),r(_,{key:1,design:"white",onClick:i[1]||(i[1]=l=>e(b).emit("reorderQuestion"))},{default:n(()=>[s(o(e(a)("global.reorder",{attribute:e(a)("exam.online_exam.props.question")})),1)]),_:1})):m("",!0)],64))]),_:1},8,["title","actions"])]),_:1},8,["title","navs"])):m("",!0),d(V,{appear:"",visibility:!0},{default:n(()=>[d(N,{"init-url":k,uuid:e(c).params.uuid,onSetItem:I,onRedirectTo:i[9]||(i[9]=l=>e(g).push({name:"ExamOnlineExam"})),refresh:f.value,onRefreshed:i[10]||(i[10]=l=>f.value=!1)},{default:n(()=>[t?(u(),r(q,{key:0,tabs:[{name:"ExamOnlineExamShowGeneral",icon:"fas fa-home",label:e(a)("general.detail"),path:"ExamOnlineExamShowGeneral"},{name:"ExamOnlineExamQuestion",icon:"far fa-circle-question",label:e(a)("exam.online_exam.question.question"),count:t.questionsCount,path:"ExamOnlineExamQuestionList"},{name:"ExamOnlineExamSubmission",icon:"fas fa-list",label:e(a)("exam.online_exam.submission.submission"),count:t.submissionsCount,path:"ExamOnlineExamSubmissionList"}]},null,8,["tabs"])):m("",!0),t.uuid?(u(),r(D,{key:1},{title:n(()=>[s(o(t.title),1)]),action:n(()=>[t.canUpdateStatus&&!t.isCompleted&&!t.publishedAt.value?(u(),r(_,{key:0,size:"xs",design:"success",onClick:i[4]||(i[4]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"publish"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.publish",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0),t.canUpdateStatus&&!t.publishedAt.value&&t.publishedAt.value?(u(),r(_,{key:1,size:"xs",design:"danger",onClick:i[5]||(i[5]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"unpublish"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.unpublish",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0),t.canUpdateStatus&&t.isCompleted&&!t.resultPublishedAt.value?(u(),r(_,{key:2,size:"xs",design:"success",onClick:i[6]||(i[6]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"publish_result"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.publish",{attribute:e(a)("exam.online_exam.result")})),1)]),_:1})):m("",!0),t.canUpdateStatus&&t.isCompleted&&t.resultPublishedAt.value?(u(),r(_,{key:3,size:"xs",design:"danger",onClick:i[7]||(i[7]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"unpublish_result"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.unpublish",{attribute:e(a)("exam.online_exam.result")})),1)]),_:1})):m("",!0)]),default:n(()=>[K("dl",J,[d(x,{label:e(a)("exam.online_exam.props.type")},{default:n(()=>{var l;return[s(o((l=t.type)==null?void 0:l.label),1)]}),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.date")},{default:n(()=>[s(o(t.date.formatted),1)]),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.period")},{default:n(()=>[s(o(t.period),1)]),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.duration")},{default:n(()=>[s(o(t.duration),1)]),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.pass_percentage")},{default:n(()=>[s(o(t.passPercentage.formatted),1)]),_:1},8,["label"]),t.hasNegativeMarking?(u(),r(x,{key:0,label:e(a)("exam.online_exam.props.negative_mark_percent_per_question")},{default:n(()=>[s(o(t.negativeMarkPercentPerQuestion),1)]),_:1},8,["label"])):m("",!0),d(x,{class:"col-span-1 sm:col-span-2",label:e(a)("employee.employee")},{default:n(()=>{var l;return[s(o(((l=t.employee)==null?void 0:l.name)||"-")+" ",1),d(T,{block:""},{default:n(()=>{var y;return[s(o((y=t.employee)==null?void 0:y.codeNumber),1)]}),_:1})]}),_:1},8,["label"]),d(x,{label:e(a)("general.created_at")},{default:n(()=>[s(o(t.createdAt.formatted),1)]),_:1},8,["label"]),d(x,{label:e(a)("general.updated_at")},{default:n(()=>[s(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0),t.uuid?(u(),r(M,{key:2,"online-exam":t,onRefresh:i[8]||(i[8]=l=>f.value=!0)},null,8,["online-exam"])):m("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{Z as default};
