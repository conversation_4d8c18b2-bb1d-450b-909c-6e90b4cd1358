import{i as Y,H as Z,c as ee,m as K,l as P,r as m,z as H,q as w,o as n,w as t,e as l,d as u,a as f,b as V,f as e,F as q,v as J,A as O,x as te,t as d,J as ae,j as Q,s as F,M as ne,u as se,h as le,y as X}from"./app-BAwPsakn.js";const oe={key:0},re={key:1},ue={class:"grid grid-cols-4 gap-2"},ie={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ce={class:"col-span-4 sm:col-span-2 lg:col-span-1"},de={class:"col-span-4 sm:col-span-2 lg:col-span-1"},pe={class:"space-x-2"},me=["onClick"],fe={key:0},_e={key:1},be=["onClick"],ye={key:0},ge={key:1},Fe={key:2},ve={key:0,class:"mt-4 grid grid-cols-4 gap-2"},ke={class:"col-span-4 sm:col-span-2 lg:col-span-1"},he={key:0,class:"col-span-4 sm:col-span-2 lg:col-span-1"},$e={class:"mt-4 grid grid-cols-4 gap-2"},Ve={class:"col-span-4 sm:col-span-2 lg:col-span-1"},Se={class:"col-span-4 sm:col-span-2 lg:col-span-1"},Te={class:"col-span-4 sm:col-span-2 lg:col-span-1"},Be={name:"FeeInstallmentForm"},Ue=Object.assign(Be,{props:{visibility:{type:Boolean,default:!1},feeStructure:{type:Object,default(){return{}}},feeGroup:{type:Object,default(){return{}}},feeInstallment:{type:Object,default(){return{}}},isEditable:{type:Boolean,default:!1}},emits:["close","completed","refresh"],setup(T,{emit:R}){const E=Y(),b=R,r=T,k={feeGroup:"",title:"",dueDate:null,hasLateFee:!1,hasTransportFee:!1,lateFeeFrequency:"daily",lateFeeValue:0,lateFeeType:"amount",heads:[],transportFee:""},v="finance/feeStructure/",y=Z(v),h=ee(()=>{var a;return!!(!(((a=r.feeInstallment)==null?void 0:a.uuid)||"")||r.isEditable)});K(!1),P({paidFeeInstallmentCount:0});const o=P({transportFees:[],feeGroups:[],frequencies:[]}),_=P({...k}),M=c=>{var a,D,S;if(Object.assign(o,c),k.feeGroup=r.feeGroup.uuid,r.feeInstallment.uuid){let p=[];r.feeInstallment.heads.forEach(B=>{p.push({name:B.name,uuid:B.uuid,amount:B.amount.value,isOptional:!!B.isOptional,applicableTo:B.applicableTo})}),Object.assign(k,{...r.feeInstallment,heads:p,dueDate:r.feeInstallment.dueDate.value,transportFee:((a=r.feeInstallment.transportFee)==null?void 0:a.uuid)||"",lateFeeFrequency:((D=r.feeInstallment.lateFeeFrequency)==null?void 0:D.value)||"",lateFeeValue:((S=r.feeInstallment.lateFeeValue)==null?void 0:S.value)||"",lateFeeType:r.feeInstallment.lateFeeType})}else{let p=[];r.feeGroup.heads.forEach(B=>{p.push({...B,amount:0,isOptional:!1,applicableTo:"all"})}),Object.assign(k,{feeGroup:r.feeGroup.uuid,title:"",dueDate:null,hasLateFee:!1,hasTransportFee:!1,lateFeeFrequency:"daily",lateFeeValue:0,lateFeeType:"amount",heads:p,transportFee:""})}Object.assign(_,ae(k))},$=c=>{c.applicableTo==="new"?c.applicableTo="old":c.applicableTo==="old"?c.applicableTo="all":c.applicableTo="new"},i=()=>{E.dispatch(v+"resetFormErrors"),b("close")},N=()=>{b("refresh")};return(c,a)=>{const D=m("BaseInput"),S=m("DatePicker"),p=m("BaseCheckbox"),B=m("BaseSelect"),I=m("FormAction"),z=m("BaseModal"),j=H("tooltip");return n(),w(z,{show:T.visibility,onClose:i,onRefresh:N},{title:t(()=>[T.feeInstallment.uuid?(n(),f("span",oe,d(c.$trans("global.edit",{attribute:c.$trans("finance.fee_structure.installment")})),1)):(n(),f("span",re,d(c.$trans("global.add",{attribute:c.$trans("finance.fee_structure.installment")})),1))]),default:t(()=>[l(I,{"no-card":"","no-data-fetch":"","is-modal":"",uuid:T.feeStructure.uuid,moduleUuid:T.feeInstallment.uuid,"pre-requisites":!0,onSetPreRequisites:M,action:T.feeInstallment.uuid?"updateInstallment":"addInstallment","init-url":v,"init-form":k,form:_,"keep-adding":!1,onRedirectTo:a[12]||(a[12]=s=>b("completed")),onCancelled:i},{default:t(()=>[u("div",ue,[u("div",ie,[l(D,{type:"text",name:"title",modelValue:_.title,"onUpdate:modelValue":a[0]||(a[0]=s=>_.title=s),label:c.$trans("finance.fee_structure.props.title"),error:e(y).title,"onUpdate:error":a[1]||(a[1]=s=>e(y).title=s)},null,8,["modelValue","label","error"])]),u("div",ce,[l(S,{modelValue:_.dueDate,"onUpdate:modelValue":a[2]||(a[2]=s=>_.dueDate=s),name:"dueDate",label:c.$trans("finance.fee_structure.props.due_date"),"no-clear":"",error:e(y).dueDate,"onUpdate:error":a[3]||(a[3]=s=>e(y).dueDate=s)},null,8,["modelValue","label","error"])]),h.value?(n(!0),f(q,{key:0},J(_.heads,(s,G)=>(n(),f("div",de,[l(D,{name:`heads.${G}.amount`,modelValue:s.amount,"onUpdate:modelValue":g=>s.amount=g,label:s.name,currency:"",error:e(y)[`heads.${G}.amount`],"onUpdate:error":g=>e(y)[`heads.${G}.amount`]=g},{"additional-label":t(()=>[u("div",pe,[u("span",{class:"cursor-pointer",onClick:g=>s.isOptional=!s.isOptional},[s.isOptional?O((n(),f("span",_e,a[14]||(a[14]=[u("i",{class:"fas fa-question-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[j,c.$trans("finance.fee_structure.props.is_optional")]]):O((n(),f("span",fe,a[13]||(a[13]=[u("i",{class:"fas fa-check-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[j,c.$trans("finance.fee_structure.props.is_mandatory")]])],8,me),u("span",{class:te({"cursor-pointer":_.isEditable}),onClick:g=>$(s)},[s.applicableTo==="new"?O((n(),f("span",ye,a[15]||(a[15]=[u("i",{class:"fas fa-user-plus text-gray-900 dark:text-gray-300"},null,-1)]))),[[j,c.$trans("finance.fee_structure.props.new_student")]]):s.applicableTo==="old"?O((n(),f("span",ge,a[16]||(a[16]=[u("i",{class:"fas fa-user-lock text-gray-900 dark:text-gray-300"},null,-1)]))),[[j,c.$trans("finance.fee_structure.props.old_student")]]):s.applicableTo==="all"?O((n(),f("span",Fe,a[17]||(a[17]=[u("i",{class:"fas fa-user-graduate text-gray-900 dark:text-gray-300"},null,-1)]))),[[j,c.$trans("finance.fee_structure.props.all_student")]]):V("",!0)],10,be)])]),_:2},1032,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error"])]))),256)):V("",!0)]),h.value?(n(),f("div",ve,[u("div",ke,[l(p,{modelValue:_.hasTransportFee,"onUpdate:modelValue":a[4]||(a[4]=s=>_.hasTransportFee=s),name:"hasTransportFee",label:c.$trans("finance.fee_structure.props.has_transport_fee")},null,8,["modelValue","label"])]),_.hasTransportFee?(n(),f("div",he,[l(B,{modelValue:_.transportFee,"onUpdate:modelValue":a[5]||(a[5]=s=>_.transportFee=s),name:"transportFee",label:c.$trans("transport.fee.fee"),options:o.transportFees,"label-prop":"name","value-prop":"uuid",error:e(y).transportFee,"onUpdate:error":a[6]||(a[6]=s=>e(y).transportFee=s)},null,8,["modelValue","label","options","error"])])):V("",!0)])):V("",!0),u("div",$e,[u("div",Ve,[l(p,{modelValue:_.hasLateFee,"onUpdate:modelValue":a[7]||(a[7]=s=>_.hasLateFee=s),name:"hasLateFee",label:c.$trans("finance.fee_structure.props.has_late_fee")},null,8,["modelValue","label"])]),_.hasLateFee?(n(),f(q,{key:0},[u("div",Se,[l(B,{modelValue:_.lateFeeFrequency,"onUpdate:modelValue":a[8]||(a[8]=s=>_.lateFeeFrequency=s),name:"lateFeeFrequency",label:c.$trans("finance.fee_structure.props.late_fee_frequency"),options:o.frequencies,"label-prop":"label","value-prop":"value",error:e(y).lateFeeFrequency,"onUpdate:error":a[9]||(a[9]=s=>e(y).lateFeeFrequency=s)},null,8,["modelValue","label","options","error"])]),u("div",Te,[l(D,{modelValue:_.lateFeeValue,"onUpdate:modelValue":a[10]||(a[10]=s=>_.lateFeeValue=s),name:"lateFeeValue",label:c.$trans("finance.fee_structure.props.late_fee_value"),currency:"",error:e(y).lateFeeValue,"onUpdate:error":a[11]||(a[11]=s=>e(y).lateFeeValue=s)},null,8,["modelValue","label","error"])])],64)):V("",!0)])]),_:1},8,["uuid","moduleUuid","action","form"])]),_:1},8,["show"])}}}),Ce={class:"flex justify-between"},we={class:"text-right sm:text-left"},Oe={key:0,class:"flex justify-between mr-4"},De={class:"space-x-2"},Ie={key:0},je={key:1},qe={key:2},Re={key:3},Me={key:4},Ae={class:"ml-3"},Ee={name:"FinanceFeeStructureShowGroup"},Le=Object.assign(Ee,{props:{feeStructure:{type:Object,default(){return{}}}},emits:["refresh"],setup(T,{emit:R}){const E=Y(),b=Q("$trans"),r=Q("emitter"),k=T,v=R,y=K(!1),h=K(!1),o=P({}),_=P({}),M="finance/feeStructure/",$=[{key:"title",label:b("finance.fee_structure.props.title"),visibility:!0},{key:"dueDate",label:b("finance.fee_structure.props.due_date"),visibility:!0},{key:"fee",label:b("finance.fee.fee"),visibility:!0},{key:"total",label:b("general.total"),visibility:!0,align:"right"},{key:"action",label:"",visibility:!0}],i=()=>{Object.assign(o,{}),Object.assign(_,{}),h.value=!1},N=()=>{v("refresh"),i()},c=S=>{Object.assign(o,S),Object.assign(_,{uuid:null}),h.value=!0},a=(S,p)=>{Object.assign(o,S),Object.assign(_,p),h.value=!0},D=async S=>{await ne()&&(y.value=!0,await E.dispatch(M+"deleteInstallment",{moduleUuid:S,uuid:k.feeStructure.uuid}).then(()=>{y.value=!1,v("refresh")}).catch(p=>{y.value=!1}))};return(S,p)=>{const B=m("BaseButton"),I=m("DataCell"),z=m("DataRow"),j=m("TextMuted"),s=m("FloatingMenuItem"),G=m("FloatingMenu"),g=m("SimpleTable"),L=m("BaseLoader"),A=H("tooltip");return n(),f(q,null,[l(L,{"is-loading":y.value},{default:t(()=>[l(g,{corner:"sharp",header:$},{default:t(()=>[(n(!0),f(q,null,J(T.feeStructure.feeGroups,x=>(n(),f(q,null,[l(z,{"is-heading":""},{default:t(()=>[l(I,{colspan:100,"is-heading":""},{default:t(()=>[u("div",Ce,[u("span",null,d(x.name),1),O((n(),w(B,{size:"xs",design:"primary",onClick:U=>c(x)},{default:t(()=>p[1]||(p[1]=[u("i",{class:"fas fa-plus"},null,-1)])),_:2},1032,["onClick"])),[[A,e(b)("global.add",{attribute:e(b)("finance.fee_structure.installment")})]])])]),_:2},1024)]),_:2},1024),(n(!0),f(q,null,J(x.installments,U=>(n(),w(z,{key:U.uuid},{default:t(()=>[l(I,{name:"title"},{default:t(()=>{var C;return[F(d(U.title)+" ",1),(C=U.transportFee)!=null&&C.uuid?(n(),w(j,{key:0,block:""},{default:t(()=>{var W;return[F(d((W=U.transportFee)==null?void 0:W.name),1)]}),_:2},1024)):V("",!0)]}),_:2},1024),l(I,{name:"dueDate"},{default:t(()=>[u("div",we,[u("p",null,d(U.dueDate.formatted),1),U.hasLateFee?(n(),w(j,{key:0,block:""},{default:t(()=>[F(d(e(b)("finance.fee_structure.props.late_fee"))+" "+d(U.lateFeeDisplay),1)]),_:2},1024)):V("",!0)])]),_:2},1024),l(I,{name:"fee",table:""},{default:t(()=>[(n(!0),f(q,null,J(U.heads,C=>{var W;return n(),f(q,null,[((W=C.amount)==null?void 0:W.value)>0?(n(),f("div",Oe,[u("div",De,[C.isOptional?O((n(),f("span",Ie,p[2]||(p[2]=[u("i",{class:"fas fa-question-circle"},null,-1)]))),[[A,e(b)("finance.fee_structure.props.is_optional")]]):O((n(),f("span",je,p[3]||(p[3]=[u("i",{class:"fas fa-check-circle"},null,-1)]))),[[A,e(b)("finance.fee_structure.props.is_mandatory")]]),C.applicableTo==="new"?O((n(),f("span",qe,p[4]||(p[4]=[u("i",{class:"fas fa-user-plus"},null,-1)]))),[[A,e(b)("finance.fee_structure.props.new_student")]]):C.applicableTo==="old"?O((n(),f("span",Re,p[5]||(p[5]=[u("i",{class:"fas fa-user-lock"},null,-1)]))),[[A,e(b)("finance.fee_structure.props.old_student")]]):C.applicableTo==="all"?O((n(),f("span",Me,p[6]||(p[6]=[u("i",{class:"fas fa-user-graduate"},null,-1)]))),[[A,e(b)("finance.fee_structure.props.all_student")]]):V("",!0),u("span",Ae,d(C.name),1)]),u("div",null,d(C.amount.formatted),1)])):V("",!0)],64)}),256))]),_:2},1024),l(I,{align:"right",name:"total"},{default:t(()=>[F(d(U.total.formatted),1)]),_:2},1024),l(I,{name:"action"},{default:t(()=>[l(G,null,{default:t(()=>[l(s,{icon:"fas fa-edit",onClick:C=>a(x,U)},{default:t(()=>[F(d(e(b)("general.edit")),1)]),_:2},1032,["onClick"]),l(s,{icon:"fas fa-trash",onClick:C=>D(U.uuid)},{default:t(()=>[F(d(e(b)("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))],64))),256))]),_:1})]),_:1},8,["is-loading"]),l(Ue,{visibility:h.value,"fee-structure":T.feeStructure,"fee-group":o,"fee-installment":_,"is-editable":T.feeStructure.isEditable,onClose:i,onCompleted:N,onRefresh:p[0]||(p[0]=x=>e(r).emit("refresh"))},null,8,["visibility","fee-structure","fee-group","fee-installment","is-editable"])],64)}}}),xe={class:"grid grid-cols-1 gap-4"},Ge={class:"col-span-1"},Pe={key:0,class:"col-span-1"},ze={key:1,class:"col-span-1"},Ne={name:"FeeAllocationForm"},We=Object.assign(Ne,{props:{uuid:{type:String,default:""}},emits:["completed"],setup(T,{emit:R}){const E=R,b={type:"",courses:[],batches:[]},r="finance/feeStructure/",k=Z(r),v=P({...b}),y=()=>{E("completed")};return(h,o)=>{const _=m("BaseRadioGroup"),M=m("BaseSelectSearch"),$=m("FormAction");return n(),w($,{"init-url":r,uuid:T.uuid,"no-data-fetch":"",action:"allocation","init-form":b,form:v,"keep-adding":!1,"after-submit":y},{default:t(()=>[u("div",xe,[u("div",Ge,[l(_,{options:[{label:h.$trans("academic.course.course"),value:"course"},{label:h.$trans("academic.batch.batch"),value:"batch"}],name:"type",modelValue:v.type,"onUpdate:modelValue":o[0]||(o[0]=i=>v.type=i),error:e(k).type,"onUpdate:error":o[1]||(o[1]=i=>e(k).type=i),horizontal:""},null,8,["options","modelValue","error"])]),v.type=="course"?(n(),f("div",Pe,[l(M,{name:"courses",multiple:"",label:h.$trans("academic.course.course"),modelValue:v.courses,"onUpdate:modelValue":o[2]||(o[2]=i=>v.courses=i),error:e(k).courses,"onUpdate:error":o[3]||(o[3]=i=>e(k).courses=i),"value-prop":"uuid","search-action":"academic/course/list"},{selectedOption:t(i=>[F(d(i.value.division.name)+" - "+d(i.value.nameWithTerm),1)]),listOption:t(i=>[F(d(i.option.division.name)+" - "+d(i.option.nameWithTerm),1)]),_:1},8,["label","modelValue","error"])])):V("",!0),v.type=="batch"?(n(),f("div",ze,[l(M,{name:"batches",multiple:"",label:h.$trans("academic.batch.batch"),modelValue:v.batches,"onUpdate:modelValue":o[4]||(o[4]=i=>v.batches=i),error:e(k).batches,"onUpdate:error":o[5]||(o[5]=i=>e(k).batches=i),"search-key":"course_batch","value-prop":"uuid","search-action":"academic/batch/list"},{selectedOption:t(i=>[F(d(i.value.course.name)+" - "+d(i.value.name),1)]),listOption:t(i=>[F(d(i.option.course.nameWithTerm)+" - "+d(i.option.name),1)]),_:1},8,["label","modelValue","error"])])):V("",!0)])]),_:1},8,["uuid","form"])}}}),Je={class:"mt-4 grid grid-cols-1 gap-x-4 gap-y-8 px-4 sm:grid-cols-2 sm:px-6"},Ke={key:0},Qe={key:1},Xe={name:"FinanceFeeStructureShow"},Ze=Object.assign(Xe,{setup(T){Y();const R=se(),E=le(),b=Q("emitter"),r=Q("$trans"),k={},v="finance/feeStructure/",y=[{key:"course",label:r("academic.course.course"),visibility:!0},{key:"batch",label:r("academic.batch.batch"),visibility:!0},{key:"action",label:"",visibility:!0}],h=K(!1),o=P({...k}),_=M=>{Object.assign(o,M)};return(M,$)=>{const i=m("PageHeaderAction"),N=m("PageHeader"),c=m("BaseDataView"),a=m("BaseButton"),D=m("ShowButton"),S=m("BaseCard"),p=m("DataCell"),B=m("FloatingMenuItem"),I=m("FloatingMenu"),z=m("DataRow"),j=m("SimpleTable"),s=m("ShowItem"),G=m("ParentTransition");return n(),f(q,null,[l(N,{title:e(r)(e(R).meta.trans,{attribute:e(r)(e(R).meta.label)}),navs:[{label:e(r)("finance.finance"),path:"Finance"},{label:e(r)("finance.fee_structure.fee_structure"),path:"FinanceFeeStructureList"}]},{default:t(()=>[l(i,{name:"FinanceFeeStructure",title:e(r)("finance.fee_structure.fee_structure"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(G,{appear:"",visibility:!0},{default:t(()=>[l(s,{"init-url":v,uuid:e(R).params.uuid,refresh:h.value,onRefreshed:$[3]||($[3]=g=>h.value=!1),onSetItem:_,onRedirectTo:$[4]||($[4]=g=>e(E).push({name:"FinanceFeeStructure"}))},{default:t(()=>[o.uuid?(n(),w(S,{key:0,"no-padding":"","no-content-padding":""},{title:t(()=>[F(d(o.name),1)]),footer:t(()=>[l(D,null,{default:t(()=>[e(X)("fee-structure:edit")&&o.isEditable?(n(),w(a,{key:0,design:"primary",onClick:$[1]||($[1]=g=>e(E).push({name:"FinanceFeeStructureEdit",params:{uuid:o.uuid}}))},{default:t(()=>[F(d(e(r)("general.edit")),1)]),_:1})):V("",!0)]),_:1})]),default:t(()=>[l(Le,{"fee-structure":o,onRefresh:$[0]||($[0]=g=>h.value=!0)},null,8,["fee-structure"]),u("dl",Je,[l(c,{class:"col-span-1 sm:col-span-2",label:e(r)("finance.fee_structure.props.description")},{default:t(()=>[F(d(o.description),1)]),_:1},8,["label"]),l(c,{label:e(r)("general.created_at")},{default:t(()=>[F(d(o.createdAt.formatted),1)]),_:1},8,["label"]),l(c,{label:e(r)("general.updated_at")},{default:t(()=>[F(d(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0),o.uuid&&o.allocations.length?(n(),w(S,{key:1,class:"mt-4","no-padding":"","no-content-padding":""},{title:t(()=>[F(d(e(r)("finance.fee_structure.allocation")),1)]),default:t(()=>[l(j,{header:y},{default:t(()=>[(n(!0),f(q,null,J(o.allocations,g=>(n(),w(z,{key:g.uuid},{default:t(()=>[l(p,{name:"course"},{default:t(()=>{var L;return[F(d(((L=g.course)==null?void 0:L.name)||"-"),1)]}),_:2},1024),l(p,{name:"batch"},{default:t(()=>{var L,A,x;return[g.batch?(n(),f("span",Ke,d(((A=(L=g.batch)==null?void 0:L.course)==null?void 0:A.name)+" "+((x=g.batch)==null?void 0:x.name)),1)):(n(),f("span",Qe," - "))]}),_:2},1024),l(p,{name:"action"},{default:t(()=>[l(I,null,{default:t(()=>[e(X)("fee-structure:allocate")?(n(),w(B,{key:0,icon:"fas fa-trash",onClick:L=>e(b).emit("showActionItem",{confirmation:!0,action:"removeAllocation",uuid:o.uuid,moduleUuid:g.uuid})},{default:t(()=>[F(d(e(r)("general.delete")),1)]),_:2},1032,["onClick"])):V("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})):V("",!0),e(X)("fee-structure:allocate")&&o.uuid?(n(),w(S,{key:2,class:"mt-4","no-padding":"","no-content-padding":""},{title:t(()=>[F(d(e(r)("finance.fee_structure.allocate")),1)]),default:t(()=>[l(We,{onCompleted:$[2]||($[2]=g=>e(b).emit("refreshItem"))})]),_:1})):V("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{Ze as default};
