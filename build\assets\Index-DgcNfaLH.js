import{u as P,l as k,n as R,r as o,q as b,o as v,w as e,d as F,e as t,h as T,j as V,m as j,f as i,a as S,F as N,v as q,s as u,t as m}from"./app-BAwPsakn.js";const E={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},U={__name:"Filter",emits:["hide"],setup(I,{emit:r}){P();const c=r,f={name:""},p=k({...f}),$=k({isLoaded:!0});return R(async()=>{$.isLoaded=!0}),(d,l)=>{const M=o("BaseInput"),n=o("FilterForm");return v(),b(n,{"init-form":f,form:p,multiple:[],onHide:l[1]||(l[1]=a=>c("hide"))},{default:e(()=>[F("div",E,[F("div",O,[t(M,{type:"text",modelValue:p.name,"onUpdate:modelValue":l[0]||(l[0]=a=>p.name=a),name:"name",label:d.$trans("mess.menu.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"MessMenuItemList"},J=Object.assign(z,{setup(I){const r=T(),c=V("emitter");let f=["create","filter"],p=["print","pdf","excel"];const $="mess/menuItem/",d=j(!1),l=k({}),M=n=>{Object.assign(l,n)};return(n,a)=>{const w=o("PageHeaderAction"),B=o("PageHeader"),h=o("ParentTransition"),C=o("DataCell"),_=o("FloatingMenuItem"),D=o("FloatingMenu"),y=o("DataRow"),A=o("BaseButton"),H=o("DataTable"),L=o("ListItem");return v(),b(L,{"init-url":$,"additional-query":{},onSetItems:M},{header:e(()=>[t(B,{title:n.$trans("mess.menu.item"),navs:[{label:n.$trans("mess.mess"),path:"Mess"}]},{default:e(()=>[t(w,{url:"mess/menu-items/",name:"MessMenuItem",title:n.$trans("mess.menu.item"),actions:i(f),"dropdown-actions":i(p),"config-path":"MessConfig",onToggleFilter:a[0]||(a[0]=s=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(h,{appear:"",visibility:d.value},{default:e(()=>[t(U,{onRefresh:a[1]||(a[1]=s=>i(c).emit("listItems")),onHide:a[2]||(a[2]=s=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(h,{appear:"",visibility:!0},{default:e(()=>[t(H,{header:l.headers,meta:l.meta,module:"mess.menu",onRefresh:a[4]||(a[4]=s=>i(c).emit("listItems"))},{actionButton:e(()=>[t(A,{onClick:a[3]||(a[3]=s=>i(r).push({name:"MessMenuItemCreate"}))},{default:e(()=>[u(m(n.$trans("global.add",{attribute:n.$trans("mess.menu.item")})),1)]),_:1})]),default:e(()=>[(v(!0),S(N,null,q(l.data,s=>(v(),b(y,{key:s.uuid,onDoubleClick:g=>i(r).push({name:"MessMenuItemShow",params:{uuid:s.uuid}})},{default:e(()=>[t(C,{name:"name"},{default:e(()=>[u(m(s.name),1)]),_:2},1024),t(C,{name:"createdAt"},{default:e(()=>[u(m(s.createdAt.formatted),1)]),_:2},1024),t(C,{name:"action"},{default:e(()=>[t(D,null,{default:e(()=>[t(_,{icon:"fas fa-arrow-circle-right",onClick:g=>i(r).push({name:"MessMenuItemShow",params:{uuid:s.uuid}})},{default:e(()=>[u(m(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-edit",onClick:g=>i(r).push({name:"MessMenuItemEdit",params:{uuid:s.uuid}})},{default:e(()=>[u(m(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-copy",onClick:g=>i(r).push({name:"MessMenuItemDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[u(m(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-trash",onClick:g=>i(c).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[u(m(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{J as default};
