import{u as v,j as L,H as _,c as x,l as I,r as a,a as P,o as S,e as i,f as l,w as t,d as n,s as B,t as D,F as E}from"./app-BAwPsakn.js";const F={class:"grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3"},H={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},Q={name:"EmployeeConfigGeneral"},Y=Object.assign(Q,{setup(W){const f=v(),d=L("$trans"),b="config/",o=_(b),U=x(()=>d("global.placeholder_info",{attribute:p.datePlaceholders})),p=I({datePlaceholders:""}),q={codeNumberPrefix:"",codeNumberDigit:"",codeNumberSuffix:"",uniqueIdNumber1Label:"",uniqueIdNumber2Label:"",uniqueIdNumber3Label:"",uniqueIdNumber4Label:"",uniqueIdNumber5Label:"",isUniqueIdNumber1Required:!1,isUniqueIdNumber2Required:!1,isUniqueIdNumber3Required:!1,isUniqueIdNumber4Required:!1,isUniqueIdNumber5Required:!1,type:"employee"},u=I({...q}),V=N=>{Object.assign(p,{datePlaceholders:N.datePlaceholders.map(e=>e.value).join(", ")})};return(N,e)=>{const c=a("PageHeader"),s=a("BaseInput"),g=a("BaseAlert"),m=a("BaseSwitch"),R=a("FormAction"),y=a("ParentTransition");return S(),P(E,null,[i(c,{title:l(d)(l(f).meta.label),navs:[{label:l(d)("employee.employee"),path:"Employee"}]},null,8,["title","navs"]),i(y,{appear:"",visibility:!0},{default:t(()=>[i(R,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:V,"init-url":b,"data-fetch":"employee",action:"store","init-form":q,form:u,"stay-on":"",redirect:"Employee"},{default:t(()=>[n("div",F,[n("div",j,[i(s,{type:"text",modelValue:u.codeNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=r=>u.codeNumberPrefix=r),name:"codeNumberPrefix",label:l(d)("employee.config.props.number_prefix"),error:l(o).codeNumberPrefix,"onUpdate:error":e[1]||(e[1]=r=>l(o).codeNumberPrefix=r)},null,8,["modelValue","label","error"])]),n("div",w,[i(s,{type:"number",modelValue:u.codeNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=r=>u.codeNumberDigit=r),name:"codeNumberDigit",label:l(d)("employee.config.props.number_digit"),error:l(o).codeNumberDigit,"onUpdate:error":e[3]||(e[3]=r=>l(o).codeNumberDigit=r)},null,8,["modelValue","label","error"])]),n("div",A,[i(s,{type:"text",modelValue:u.codeNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=r=>u.codeNumberSuffix=r),name:"codeNumberSuffix",label:l(d)("employee.config.props.number_suffix"),error:l(o).codeNumberSuffix,"onUpdate:error":e[5]||(e[5]=r=>l(o).codeNumberSuffix=r)},null,8,["modelValue","label","error"])]),n("div",C,[i(g,{design:"info"},{default:t(()=>[B(D(U.value),1)]),_:1})]),n("div",H,[i(s,{type:"text",modelValue:u.uniqueIdNumber1Label,"onUpdate:modelValue":e[6]||(e[6]=r=>u.uniqueIdNumber1Label=r),name:"uniqueIdNumber1Label",label:l(d)("employee.config.props.unique_id_number1_label"),error:l(o).uniqueIdNumber1Label,"onUpdate:error":e[7]||(e[7]=r=>l(o).uniqueIdNumber1Label=r)},null,8,["modelValue","label","error"])]),n("div",T,[i(m,{vertical:"",modelValue:u.isUniqueIdNumber1Required,"onUpdate:modelValue":e[8]||(e[8]=r=>u.isUniqueIdNumber1Required=r),name:"uniqueIdNumber1Required",label:l(d)("employee.config.props.unique_id_number1_required"),error:l(o).isUniqueIdNumber1Required,"onUpdate:error":e[9]||(e[9]=r=>l(o).isUniqueIdNumber1Required=r)},null,8,["modelValue","label","error"])]),e[26]||(e[26]=n("div",{class:"col-span-3 sm:col-span-1"},null,-1)),n("div",k,[i(s,{type:"text",modelValue:u.uniqueIdNumber2Label,"onUpdate:modelValue":e[10]||(e[10]=r=>u.uniqueIdNumber2Label=r),name:"uniqueIdNumber2Label",label:l(d)("employee.config.props.unique_id_number2_label"),error:l(o).uniqueIdNumber2Label,"onUpdate:error":e[11]||(e[11]=r=>l(o).uniqueIdNumber2Label=r)},null,8,["modelValue","label","error"])]),n("div",O,[i(m,{vertical:"",modelValue:u.isUniqueIdNumber2Required,"onUpdate:modelValue":e[12]||(e[12]=r=>u.isUniqueIdNumber2Required=r),name:"uniqueIdNumber2Required",label:l(d)("employee.config.props.unique_id_number2_required"),error:l(o).isUniqueIdNumber2Required,"onUpdate:error":e[13]||(e[13]=r=>l(o).isUniqueIdNumber2Required=r)},null,8,["modelValue","label","error"])]),e[27]||(e[27]=n("div",{class:"col-span-3 sm:col-span-1"},null,-1)),n("div",$,[i(s,{type:"text",modelValue:u.uniqueIdNumber3Label,"onUpdate:modelValue":e[14]||(e[14]=r=>u.uniqueIdNumber3Label=r),name:"uniqueIdNumber3Label",label:l(d)("employee.config.props.unique_id_number3_label"),error:l(o).uniqueIdNumber3Label,"onUpdate:error":e[15]||(e[15]=r=>l(o).uniqueIdNumber3Label=r)},null,8,["modelValue","label","error"])]),n("div",G,[i(m,{vertical:"",modelValue:u.isUniqueIdNumber3Required,"onUpdate:modelValue":e[16]||(e[16]=r=>u.isUniqueIdNumber3Required=r),name:"uniqueIdNumber3Required",label:l(d)("employee.config.props.unique_id_number3_required"),error:l(o).isUniqueIdNumber3Required,"onUpdate:error":e[17]||(e[17]=r=>l(o).isUniqueIdNumber3Required=r)},null,8,["modelValue","label","error"])]),e[28]||(e[28]=n("div",{class:"col-span-3 sm:col-span-1"},null,-1)),n("div",z,[i(s,{type:"text",modelValue:u.uniqueIdNumber4Label,"onUpdate:modelValue":e[18]||(e[18]=r=>u.uniqueIdNumber4Label=r),name:"uniqueIdNumber4Label",label:l(d)("employee.config.props.unique_id_number4_label"),error:l(o).uniqueIdNumber4Label,"onUpdate:error":e[19]||(e[19]=r=>l(o).uniqueIdNumber4Label=r)},null,8,["modelValue","label","error"])]),n("div",J,[i(m,{vertical:"",modelValue:u.isUniqueIdNumber4Required,"onUpdate:modelValue":e[20]||(e[20]=r=>u.isUniqueIdNumber4Required=r),name:"uniqueIdNumber4Required",label:l(d)("employee.config.props.unique_id_number4_required"),error:l(o).isUniqueIdNumber4Required,"onUpdate:error":e[21]||(e[21]=r=>l(o).isUniqueIdNumber4Required=r)},null,8,["modelValue","label","error"])]),e[29]||(e[29]=n("div",{class:"col-span-3 sm:col-span-1"},null,-1)),n("div",K,[i(s,{type:"text",modelValue:u.uniqueIdNumber5Label,"onUpdate:modelValue":e[22]||(e[22]=r=>u.uniqueIdNumber5Label=r),name:"uniqueIdNumber5Label",label:l(d)("employee.config.props.unique_id_number5_label"),error:l(o).uniqueIdNumber5Label,"onUpdate:error":e[23]||(e[23]=r=>l(o).uniqueIdNumber5Label=r)},null,8,["modelValue","label","error"])]),n("div",M,[i(m,{vertical:"",modelValue:u.isUniqueIdNumber5Required,"onUpdate:modelValue":e[24]||(e[24]=r=>u.isUniqueIdNumber5Required=r),name:"uniqueIdNumber5Required",label:l(d)("employee.config.props.unique_id_number5_required"),error:l(o).isUniqueIdNumber5Required,"onUpdate:error":e[25]||(e[25]=r=>l(o).isUniqueIdNumber5Required=r)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{Y as default};
