import{u as Z,l as A,n as E,r as m,q as u,o as n,w as t,d as _,e as l,h as ee,j as M,y as w,m as te,z as ae,f as a,B as O,a as k,F,v as T,b as p,s as i,A as R,t as s}from"./app-BAwPsakn.js";const se={class:"grid grid-cols-3 gap-6"},ne={class:"col-span-3 sm:col-span-1"},ie={__name:"Filter",emits:["hide"],setup(U,{emit:g}){Z();const y=g,o={title:""},h=A({...o}),L=A({isLoaded:!0});return E(async()=>{L.isLoaded=!0}),(j,x)=>{const C=m("BaseInput"),f=m("FilterForm");return n(),u(f,{"init-form":o,form:h,multiple:[],onHide:x[1]||(x[1]=B=>y("hide"))},{default:t(()=>[_("div",se,[_("div",ne,[l(C,{type:"text",modelValue:h.title,"onUpdate:modelValue":x[0]||(x[0]=B=>h.title=B),name:"title",label:j.$trans("exam.online_exam.props.title")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},oe={class:"grid grid-cols-1 gap-4 px-4 pt-4 lg:grid-cols-2"},le={class:"text-center dark:text-gray-400"},ue={key:0,class:"text-sm"},re=["onClick"],de=["onClick"],me={class:"text-sm text-center dark:text-gray-500"},ce={class:"text-sm text-center dark:text-gray-500"},_e={class:"text-xl font-semibold"},pe={name:"ExamOnlineExamList"},ge=Object.assign(pe,{setup(U){const g=ee(),y=M("emitter"),o=M("$trans");let h=["filter"];w("online-exam:create")&&h.unshift("create");let L=[];w("online-exam:export")&&(L=["print","pdf","excel"]);const j=[{key:"course",label:o("academic.course.course"),visibility:!0},{key:"subject",label:o("academic.subject.subject"),visibility:!0}],x="exam/onlineExam/",C=te(!1),f=A({}),B=P=>{Object.assign(f,P)};return(P,r)=>{const N=m("PageHeaderAction"),q=m("PageHeader"),I=m("ParentTransition"),v=m("BaseBadge"),b=m("DataCell"),S=m("DataRow"),z=m("SimpleTable"),G=m("CardView"),J=m("Pagination"),K=m("CardList"),$=m("TextMuted"),D=m("FloatingMenuItem"),Q=m("FloatingMenu"),W=m("BaseButton"),X=m("DataTable"),Y=m("ListItem"),V=ae("tooltip");return n(),u(Y,{"init-url":x,onSetItems:B},{header:t(()=>[l(q,{title:a(o)("exam.online_exam.online_exam"),navs:[{label:a(o)("exam.exam"),path:"Exam"}]},{default:t(()=>[l(N,{url:"exam/online-exams/",name:"ExamOnlineExam",title:a(o)("exam.online_exam.online_exam"),actions:a(h),"dropdown-actions":a(L),onToggleFilter:r[0]||(r[0]=e=>C.value=!C.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[l(I,{appear:"",visibility:C.value},{default:t(()=>[l(ie,{onRefresh:r[1]||(r[1]=e=>a(y).emit("listItems")),onHide:r[2]||(r[2]=e=>C.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[a(O)(["student","guardian"],"any")?(n(),u(I,{key:0,appear:"",visibility:!0},{default:t(()=>[l(K,{header:f.headers,meta:f.meta},{content:t(()=>[_("div",_e,s(a(o)("dashboard.nothing_to_show")),1)]),default:t(()=>[_("div",oe,[(n(!0),k(F,null,T(f.data,e=>(n(),u(G,{key:e.uuid,"no-padding":""},{default:t(()=>{var d;return[_("div",le,[i(s(e.title)+" ",1),e.type?(n(),k("span",ue,"("+s((d=e.type)==null?void 0:d.label)+")",1)):p("",!0),a(O)("student")&&e.isLive?R((n(),k("span",{key:1,class:"cursor-pointer ml-2",onClick:c=>a(g).push({name:"ExamOnlineExamSubmit",params:{uuid:e.uuid}})},r[6]||(r[6]=[_("i",{class:"fas fa-arrow-up-right-from-square"},null,-1)]),8,re)),[[V,a(o)("exam.online_exam.submit")]]):p("",!0),a(O)("student")&&e.resultPublishedAt.value?R((n(),k("span",{key:2,class:"cursor-pointer ml-2",onClick:c=>a(g).push({name:"ExamOnlineExamStudentSubmission",params:{uuid:e.uuid}})},r[7]||(r[7]=[_("i",{class:"fas fa-arrow-up-right-from-square"},null,-1)]),8,de)),[[V,a(o)("exam.online_exam.result")]]):p("",!0)]),_("div",me,[e.isUpcoming&&e.timeLeft>e.upcomingThreshold?(n(),u(v,{key:0,design:"info"},{default:t(()=>[i(s(a(o)("exam.online_exam.upcoming")),1)]),_:1})):e.isUpcoming&&e.timeLeft<=e.upcomingThreshold?(n(),u(v,{key:1,design:"info"},{default:t(()=>[i(s(a(o)("exam.online_exam.starting_in",{attribute:e.timeLeft})),1)]),_:2},1024)):e.isLive?(n(),u(v,{key:2,design:"success"},{default:t(()=>[i(s(a(o)("exam.online_exam.live")),1)]),_:1})):e.isCompleted?(n(),u(v,{key:3,design:"primary"},{default:t(()=>[i(s(a(o)("exam.online_exam.completed")),1)]),_:1})):p("",!0)]),_("div",ce,s(e.date.formatted)+" "+s(e.period)+" ("+s(e.duration)+") ",1),r[8]||(r[8]=_("div",{class:"flex justify-between"},null,-1)),e.records.length>0?(n(),u(z,{key:0,header:j},{default:t(()=>[(n(!0),k(F,null,T(e.records,c=>(n(),u(S,{key:c.uuid},{default:t(()=>[l(b,{name:"course"},{default:t(()=>{var H;return[i(s(((H=c.batch.course)==null?void 0:H.name)+" "+c.batch.name),1)]}),_:2},1024),l(b,{name:"subject"},{default:t(()=>[i(s(c.subject.name),1)]),_:2},1024)]),_:2},1024))),128))]),_:2},1024)):p("",!0)]}),_:2},1024))),128))]),_("div",null,[l(J,{"card-view":"",meta:f.meta,onRefresh:r[3]||(r[3]=e=>a(y).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(n(),u(I,{key:1,appear:"",visibility:!0},{default:t(()=>[l(X,{header:f.headers,meta:f.meta,module:"exam.online_exam",onRefresh:r[5]||(r[5]=e=>a(y).emit("listItems"))},{actionButton:t(()=>[a(w)("online-exam:create")?(n(),u(W,{key:0,onClick:r[4]||(r[4]=e=>a(g).push({name:"ExamOnlineExamCreate"}))},{default:t(()=>[i(s(a(o)("global.add",{attribute:a(o)("exam.online_exam.online_exam")})),1)]),_:1})):p("",!0)]),default:t(()=>[(n(!0),k(F,null,T(f.data,e=>(n(),u(S,{key:e.uuid,onDoubleClick:d=>a(g).push({name:"ExamOnlineExamShow",params:{uuid:e.uuid}})},{default:t(()=>[l(b,{name:"title"},{default:t(()=>[i(s(e.title)+" ",1),l($,{block:""},{default:t(()=>{var d;return[i(s((d=e.type)==null?void 0:d.label),1)]}),_:2},1024)]),_:2},1024),l(b,{name:"records"},{default:t(()=>[(n(!0),k(F,null,T(e.records,d=>{var c;return n(),k("div",null,[i(s(((c=d.batch.course)==null?void 0:c.name)+" "+d.batch.name)+" ",1),d.subject?(n(),u($,{key:0},{default:t(()=>[i(s(d.subject.name),1)]),_:2},1024)):p("",!0)])}),256))]),_:2},1024),l(b,{name:"date"},{default:t(()=>[i(s(e.date.formatted)+" ",1),l($,{block:""},{default:t(()=>[i(s(e.endDate.formatted),1)]),_:2},1024),_("div",null,[e.isUpcoming&&e.timeLeft>e.upcomingThreshold?(n(),u(v,{key:0,design:"info"},{default:t(()=>[i(s(a(o)("exam.online_exam.upcoming")),1)]),_:1})):e.isUpcoming&&e.timeLeft<=e.upcomingThreshold?(n(),u(v,{key:1,design:"info"},{default:t(()=>[i(s(a(o)("exam.online_exam.starting_in",{attribute:e.timeLeft})),1)]),_:2},1024)):e.isLive?(n(),u(v,{key:2,design:"success"},{default:t(()=>[i(s(a(o)("exam.online_exam.live")),1)]),_:1})):e.isCompleted?(n(),u(v,{key:3,design:"primary"},{default:t(()=>[i(s(a(o)("exam.online_exam.completed")),1)]),_:1})):p("",!0)])]),_:2},1024),l(b,{name:"time"},{default:t(()=>[i(s(e.period)+" ",1),l($,{block:""},{default:t(()=>[i(s(e.duration),1)]),_:2},1024)]),_:2},1024),l(b,{name:"employee"},{default:t(()=>{var d;return[i(s(((d=e.employee)==null?void 0:d.name)||"-")+" ",1),l($,{block:""},{default:t(()=>{var c;return[i(s((c=e.employee)==null?void 0:c.codeNumber),1)]}),_:2},1024)]}),_:2},1024),l(b,{name:"createdAt"},{default:t(()=>[i(s(e.createdAt.formatted),1)]),_:2},1024),l(b,{name:"action"},{default:t(()=>[l(Q,null,{default:t(()=>[l(D,{icon:"fas fa-arrow-circle-right",onClick:d=>a(g).push({name:"ExamOnlineExamShow",params:{uuid:e.uuid}})},{default:t(()=>[i(s(a(o)("general.show")),1)]),_:2},1032,["onClick"]),a(w)("online-exam:edit")&&e.isEditable?(n(),u(D,{key:0,icon:"fas fa-edit",onClick:d=>a(g).push({name:"ExamOnlineExamEdit",params:{uuid:e.uuid}})},{default:t(()=>[i(s(a(o)("general.edit")),1)]),_:2},1032,["onClick"])):p("",!0),a(w)("online-exam:create")?(n(),u(D,{key:1,icon:"fas fa-copy",onClick:d=>a(g).push({name:"ExamOnlineExamDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[i(s(a(o)("general.duplicate")),1)]),_:2},1032,["onClick"])):p("",!0),a(w)("online-exam:delete")&&e.isDeletable?(n(),u(D,{key:2,icon:"fas fa-trash",onClick:d=>a(y).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[i(s(a(o)("general.delete")),1)]),_:2},1032,["onClick"])):p("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{ge as default};
