import{r as e,q as t,o as a,w as r,e as c}from"./app-BAwPsakn.js";const p={name:"ReceptionConfig"},u=Object.assign(p,{setup(s){const o=[{name:"ReceptionConfigGeneral",icon:"fas fa-chevron-right",label:"config.config"},{name:"ReceptionConfigEnquiryType",icon:"fas fa-chevron-right",label:"reception.enquiry.type.type"},{name:"ReceptionConfigEnquirySource",icon:"fas fa-chevron-right",label:"reception.enquiry.source.source"},{name:"ReceptionConfigCallingPurpose",icon:"fas fa-chevron-right",label:"reception.call_log.purpose.purpose"},{name:"ReceptionConfigVisitingPurpose",icon:"fas fa-chevron-right",label:"reception.visitor_log.purpose.purpose"},{name:"ReceptionConfigComplaintType",icon:"fas fa-chevron-right",label:"reception.complaint.type.type"},{name:"ReceptionConfigGatePassPurpose",icon:"fas fa-chevron-right",label:"reception.gate_pass.purpose.purpose"}];return(f,l)=>{const n=e("router-view"),i=e("ModuleConfig");return a(),t(i,{navigations:o},{default:r(()=>[c(n)]),_:1})}}});export{u as default};
