import{u as w,G as v,H as D,l as T,r as d,q as U,o as g,w as _,d as a,b as H,e as u,f as i,I as q,J as x,a as E,F as I}from"./app-BAwPsakn.js";const j={class:"grid grid-cols-4 gap-6"},O={class:"col-span-4 sm:col-span-3"},L={class:"col-span-4 sm:col-span-1"},M={class:"mt-4 grid grid-cols-4 gap-6"},S={class:"col-span-4 sm:col-span-1"},C={class:"mt-4 grid grid-cols-4 gap-6"},N={class:"col-span-4"},G={class:"mt-4 grid grid-cols-1"},J={class:"col"},z={name:"ResourceDownloadForm"},K=Object.assign(z,{setup(P){const p=w(),r={title:"",studentAudienceType:"",studentAudiences:[],employeeAudienceType:"",employeeAudiences:[],isPublic:!1,expiresAt:"",description:"",media:[],mediaUpdated:!1,mediaToken:v(),mediaHash:[]},f="resource/download/",n=D(f),c=T({studentAudienceTypes:[],employeeAudienceTypes:[]}),t=T({...r}),m=T({studentAudiences:[],employeeAudienceTypes:[],isLoaded:!p.params.uuid}),V=o=>{Object.assign(c,o)},$=()=>{t.mediaToken=v(),t.mediaHash=[]},R=o=>{var y,b;let e=o.audiences.filter(l=>l.type=="student").map(l=>l.uuid),A=o.audiences.filter(l=>l.type=="employee").map(l=>l.uuid);Object.assign(r,{...o,expiresAt:o.expiresAt.at,studentAudienceType:((y=o.studentAudienceType)==null?void 0:y.value)||"",employeeAudienceType:((b=o.employeeAudienceType)==null?void 0:b.value)||"",studentAudiences:e,employeeAudiences:A}),Object.assign(t,x(r)),m.studentAudiences=e,m.employeeAudiences=A,m.isLoaded=!0};return(o,e)=>{const A=d("BaseInput"),y=d("DatePicker"),b=d("BaseSwitch"),l=d("AudienceInput"),k=d("BaseTextarea"),F=d("MediaUpload"),B=d("FormAction");return g(),U(B,{"pre-requisites":!0,onSetPreRequisites:V,"init-url":f,"init-form":r,form:t,setForm:R,redirect:"ResourceDownload",onResetMediaFiles:$},{default:_(()=>[a("div",j,[a("div",O,[u(A,{type:"text",modelValue:t.title,"onUpdate:modelValue":e[0]||(e[0]=s=>t.title=s),name:"title",label:o.$trans("resource.download.props.title"),error:i(n).title,"onUpdate:error":e[1]||(e[1]=s=>i(n).title=s),autofocus:""},null,8,["modelValue","label","error"])]),a("div",L,[u(y,{as:"datetime",modelValue:t.expiresAt,"onUpdate:modelValue":e[2]||(e[2]=s=>t.expiresAt=s),name:"expiresAt",label:o.$trans("resource.download.props.expires_at"),error:i(n).expiresAt,"onUpdate:error":e[3]||(e[3]=s=>i(n).expiresAt=s)},null,8,["modelValue","label","error"])])]),a("div",M,[a("div",S,[u(b,{modelValue:t.isPublic,"onUpdate:modelValue":e[4]||(e[4]=s=>t.isPublic=s),name:"isPublic",label:o.$trans("calendar.event.props.is_public"),error:i(n).isPublic,"onUpdate:error":e[5]||(e[5]=s=>i(n).isPublic=s)},null,8,["modelValue","label","error"])])]),!t.isPublic&&m.isLoaded?(g(),U(l,{key:0,"pre-requisites":c,studentAudienceType:t.studentAudienceType,"onUpdate:studentAudienceType":e[6]||(e[6]=s=>t.studentAudienceType=s),studentAudiences:t.studentAudiences,"onUpdate:studentAudiences":e[7]||(e[7]=s=>t.studentAudiences=s),employeeAudienceType:t.employeeAudienceType,"onUpdate:employeeAudienceType":e[8]||(e[8]=s=>t.employeeAudienceType=s),employeeAudiences:t.employeeAudiences,"onUpdate:employeeAudiences":e[9]||(e[9]=s=>t.employeeAudiences=s),formErrors:i(n),"onUpdate:formErrors":e[10]||(e[10]=s=>q(n)?n.value=s:null)},null,8,["pre-requisites","studentAudienceType","studentAudiences","employeeAudienceType","employeeAudiences","formErrors"])):H("",!0),a("div",C,[a("div",N,[u(k,{modelValue:t.description,"onUpdate:modelValue":e[11]||(e[11]=s=>t.description=s),name:"description",label:o.$trans("resource.download.props.description"),error:i(n).description,"onUpdate:error":e[12]||(e[12]=s=>i(n).description=s)},null,8,["modelValue","label","error"])])]),a("div",G,[a("div",J,[u(F,{multiple:"",label:o.$trans("general.file"),module:"download",media:t.media,"media-token":t.mediaToken,onIsUpdated:e[13]||(e[13]=s=>t.mediaUpdated=!0),onSetHash:e[14]||(e[14]=s=>t.mediaHash.push(s))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),Q={name:"ResourceDownloadAction"},X=Object.assign(Q,{setup(P){const p=w();return(r,f)=>{const n=d("PageHeaderAction"),c=d("PageHeader"),t=d("ParentTransition");return g(),E(I,null,[u(c,{title:r.$trans(i(p).meta.trans,{attribute:r.$trans(i(p).meta.label)}),navs:[{label:r.$trans("resource.resource"),path:"Resource"},{label:r.$trans("resource.download.download"),path:"ResourceDownloadList"}]},{default:_(()=>[u(n,{name:"ResourceDownload",title:r.$trans("resource.download.download"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),u(t,{appear:"",visibility:!0},{default:_(()=>[u(K)]),_:1})],64)}}});export{X as default};
