import{i as C,u as P,h as T,l as V,r as o,a as c,o as d,e as t,w as e,f as i,q as _,b as v,d as A,s as r,t as s,F as f,v as H,y as I}from"./app-BAwPsakn.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},D={name:"ExamObservationShow"},j=Object.assign(D,{setup(M){C();const p=P(),b=T(),g={},B="exam/observation/",n=V({...g}),$=a=>{Object.assign(n,a)};return(a,m)=>{const x=o("PageHeaderAction"),h=o("PageHeader"),l=o("BaseDataView"),w=o("TextMuted"),E=o("BaseButton"),O=o("ShowButton"),S=o("BaseCard"),k=o("ShowItem"),y=o("ParentTransition");return d(),c(f,null,[t(h,{title:a.$trans(i(p).meta.trans,{attribute:a.$trans(i(p).meta.label)}),navs:[{label:a.$trans("exam.exam"),path:"Exam"},{label:a.$trans("exam.observation.observation"),path:"ExamObservationList"}]},{default:e(()=>[t(x,{name:"ExamObservation",title:a.$trans("exam.observation.observation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(y,{appear:"",visibility:!0},{default:e(()=>[t(k,{"init-url":B,uuid:i(p).params.uuid,onSetItem:$,onRedirectTo:m[1]||(m[1]=u=>i(b).push({name:"ExamObservation"}))},{default:e(()=>[n.uuid?(d(),_(S,{key:0},{title:e(()=>[r(s(n.name),1)]),footer:e(()=>[t(O,null,{default:e(()=>[i(I)("exam-observation:edit")?(d(),_(E,{key:0,design:"primary",onClick:m[0]||(m[0]=u=>i(b).push({name:"ExamObservationEdit",params:{uuid:n.uuid}}))},{default:e(()=>[r(s(a.$trans("general.edit")),1)]),_:1})):v("",!0)]),_:1})]),default:e(()=>[A("dl",N,[t(l,{label:a.$trans("exam.observation.props.name")},{default:e(()=>[r(s(n.name),1)]),_:1},8,["label"]),t(l,{label:a.$trans("exam.grade.grade")},{default:e(()=>[r(s(n.grade.name),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:a.$trans("exam.observation.props.records")},{default:e(()=>[(d(!0),c(f,null,H(n.records,u=>(d(),c("div",null,[r(s(u.name)+" "+s(u.code)+" : ",1),t(w,null,{default:e(()=>[r(s(u.maxMark),1)]),_:2},1024)]))),256))]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:a.$trans("exam.observation.props.description")},{default:e(()=>[r(s(n.description),1)]),_:1},8,["label"]),t(l,{label:a.$trans("general.created_at")},{default:e(()=>[r(s(n.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:a.$trans("general.updated_at")},{default:e(()=>[r(s(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):v("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
