import{u as M,h as P,j as y,m as A,l as B,r as n,q as f,o as c,w as e,e as t,f as m,a as R,F as H,v as L,s as o,t as s,b as x}from"./app-BAwPsakn.js";const O={name:"StudentSiblingList"},z=Object.assign(O,{props:{student:{type:Object,default(){return{}}}},setup(l){const g=M(),b=P(),h=y("emitter");let v=[];const S="student/sibling/",_=A(!1),d=B({}),w=u=>{Object.assign(d,u)};return(u,i)=>{const D=n("PageHeaderAction"),k=n("PageHeader"),N=n("BaseAlert"),p=n("TextMuted"),r=n("DataCell"),$=n("FloatingMenuItem"),F=n("FloatingMenu"),T=n("DataRow"),C=n("DataTable"),I=n("ParentTransition"),j=n("ListItem");return c(),f(j,{"init-url":S,uuid:m(g).params.uuid,onSetItems:w},{header:e(()=>[l.student.uuid?(c(),f(k,{key:0,title:u.$trans("student.sibling.sibling"),navs:[{label:u.$trans("student.student"),path:"Student"},{label:l.student.contact.name,path:{name:"StudentShow",params:{uuid:l.student.uuid}}}]},{default:e(()=>[t(D,{url:`students/${l.student.uuid}/siblings/`,name:"StudentSibling",title:u.$trans("student.sibling.sibling"),actions:m(v),"dropdown-actions":["print","pdf","excel"],onToggleFilter:i[0]||(i[0]=a=>_.value=!_.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):x("",!0)]),default:e(()=>[t(I,{appear:"",visibility:!0},{default:e(()=>[t(C,{header:d.headers,meta:d.meta,onRefresh:i[1]||(i[1]=a=>m(h).emit("listItems"))},{alert:e(()=>[t(N,{size:"xs",design:"info"},{default:e(()=>[o(s(u.$trans("general.errors.record_not_found")),1)]),_:1})]),default:e(()=>[(c(!0),R(H,null,L(d.data,a=>(c(),f(T,{key:a.uuid},{default:e(()=>[t(r,{name:"codeNumber"},{default:e(()=>[o(s(a.codeNumber)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.joiningDate.formatted),1)]),_:2},1024)]),_:2},1024),t(r,{name:"name"},{default:e(()=>[o(s(a.name),1)]),_:2},1024),t(r,{name:"course"},{default:e(()=>[o(s(a.courseName)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.batchName),1)]),_:2},1024)]),_:2},1024),t(r,{name:"gender"},{default:e(()=>[o(s(a.gender.label),1)]),_:2},1024),t(r,{name:"birthDate"},{default:e(()=>[o(s(a.birthDate.formatted),1)]),_:2},1024),t(r,{name:"contactNumber"},{default:e(()=>[o(s(a.contactNumber),1)]),_:2},1024),t(r,{name:"action"},{default:e(()=>[t(F,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:V=>m(b).push({name:"StudentShow",params:{uuid:a.uuid}})},{default:e(()=>[o(s(u.$trans("general.show")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{z as default};
