import{u as F,h as O,i as R,m as q,l as U,n as W,r as d,a as o,o as l,e as a,w as e,q as B,b as _,d as r,F as f,v as y,s as i,t as s,f as V,y as z}from"./app-BAwPsakn.js";const G={class:"space-y-2"},J={class:"divide-y divide-gray-200 dark:divide-gray-700"},K={class:"grid grid-cols-6 gap-2 px-4 py-2"},Q={class:"col-span-6 sm:col-span-1"},X={class:"col-span-6 sm:col-span-5"},Y={class:"flex flex-col sm:flex-row gap-2"},Z={class:"w-full"},ee={key:0},te={class:"col-span-6 sm:col-span-1"},ae={key:1,class:"block text-xs"},se={class:"col-span-6 sm:col-span-5"},ne={key:0,class:"flex flex-col sm:flex-row gap-2"},le={class:"w-full"},ie={key:0,class:"flex justify-center"},oe={class:"flex items-center gap-2"},ce={key:0,class:"text-gray-400 text-sm font-semibold"},de={key:1},re={class:"flex justify-center"},ue={name:"AcademicTimetableShow"},be=Object.assign(ue,{setup(me){const T=F(),A=O(),C=R(),L="academic/timetable/",k=q(!1),c=U({}),x=async()=>{k.value=!0,await C.dispatch(L+"get",{uuid:T.params.uuid,params:{detail:!0}}).then(t=>{k.value=!1,Object.assign(c,t)}).catch(t=>{k.value=!1})},D=()=>{var t;return((t=c.days[0])==null?void 0:t.sessions)||[]};return W(async()=>{await x()}),(t,g)=>{const N=d("PageHeaderAction"),P=d("PageHeader"),u=d("ListItemView"),S=d("ListContainerVertical"),w=d("BaseCard"),p=d("BaseDataView"),h=d("TextMuted"),j=d("BaseButton"),H=d("ShowButton"),I=d("DetailLayoutVertical"),M=d("ParentTransition");return l(),o(f,null,[a(P,{title:t.$trans("academic.timetable.timetable"),navs:[{label:t.$trans("academic.academic"),path:"Academic"},{label:t.$trans("academic.timetable.timetable"),path:"AcademicTimetable"}]},{default:e(()=>[a(N,{name:"AcademicTimetable",title:t.$trans("academic.timetable.timetable"),actions:[]},null,8,["title"])]),_:1},8,["title","navs"]),a(M,{appear:"",visibility:!0},{default:e(()=>[c.uuid?(l(),B(I,{key:0},{detail:e(()=>[r("div",G,[a(w,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(s(t.$trans("academic.timetable.timetable")),1)]),action:e(()=>g[1]||(g[1]=[])),default:e(()=>[a(S,null,{default:e(()=>[a(u,{label:t.$trans("academic.batch.batch")},{default:e(()=>{var n,$,m;return[i(s((($=(n=c.batch)==null?void 0:n.course)==null?void 0:$.nameWithTerm)+" "+((m=c.batch)==null?void 0:m.name)),1)]}),_:1},8,["label"]),a(u,{label:t.$trans("asset.building.room.room")},{default:e(()=>{var n;return[i(s((n=c.room)==null?void 0:n.fullName),1)]}),_:1},8,["label"]),a(u,{label:t.$trans("academic.timetable.props.effective_date")},{default:e(()=>[i(s(c.effectiveDate.formatted),1)]),_:1},8,["label"]),a(u,{label:t.$trans("academic.timetable.props.description")},{default:e(()=>[i(s(c.description),1)]),_:1},8,["label"]),a(u,{label:t.$trans("general.created_at")},{default:e(()=>[i(s(c.createdAt.formatted),1)]),_:1},8,["label"]),a(u,{label:t.$trans("general.updated_at")},{default:e(()=>[i(s(c.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[a(w,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(s(t.$trans("global.detail",{attribute:t.$trans("academic.timetable.timetable")})),1)]),footer:e(()=>[a(H,null,{default:e(()=>[V(z)("timetable:edit")?(l(),B(j,{key:0,design:"primary",onClick:g[0]||(g[0]=n=>V(A).push({name:"AcademicTimetableEdit",params:{uuid:c.uuid}}))},{default:e(()=>[i(s(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[r("div",J,[r("div",K,[r("div",Q,[a(p)]),r("div",X,[r("div",Y,[(l(!0),o(f,null,y(D(),n=>(l(),o("div",Z,[a(p,null,{default:e(()=>[i(s(n.name)+" ",1),n.isBreak?(l(),o("span",ee,"("+s(t.$trans("academic.class_timing.break"))+")",1)):_("",!0),a(h,{block:""},{default:e(()=>[i(s(n.duration),1)]),_:2},1024)]),_:2},1024)]))),256))])])]),(l(!0),o(f,null,y(c.days,(n,$)=>(l(),o("div",{class:"grid grid-cols-6 gap-2 px-2 py-2",key:n.value},[r("div",te,[a(p,null,{default:e(()=>[i(s(n.label)+" ",1),n.sessions.length>0?(l(),B(h,{key:0,block:""},{default:e(()=>[i(s(n.period),1)]),_:2},1024)):_("",!0),n.sessions.length>0?(l(),o("span",ae,s(n.duration),1)):_("",!0)]),_:2},1024)]),r("div",se,[n.sessions.length>0?(l(),o("div",ne,[(l(!0),o(f,null,y(n.sessions,(m,_e)=>(l(),o("div",le,[m.isBreak?(l(),o("div",ie,"-")):(l(!0),o(f,{key:1},y(m.allotments,(v,E)=>(l(),o("div",{key:v.id},[r("div",oe,[m.allotments.length>1?(l(),o("span",ce,s(E+1),1)):_("",!0),r("div",null,[a(p,null,{default:e(()=>{var b;return[i(s((b=v.subject)==null?void 0:b.name),1)]}),_:2},1024),a(h,{block:""},{default:e(()=>[i(s(v.roomName),1)]),_:2},1024),a(h,{block:""},{default:e(()=>{var b;return[i(s((b=v.employee)==null?void 0:b.name),1)]}),_:2},1024)])])]))),128))]))),256))])):(l(),o("div",de,[r("div",re,[a(p,null,{default:e(()=>[i(s(t.$trans("academic.timetable.props.holiday")),1)]),_:1})])]))])]))),128))])]),_:1})]),_:1})):_("",!0)]),_:1})],64)}}});export{be as default};
