import{u as A,i as h,m as D,l as E,n as S,r as u,a as _,o as r,e as s,w as e,q as c,b as p,s as t,f as N,t as o,F as b}from"./app-BAwPsakn.js";const T={key:2,class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},U={name:"Product"},L=Object.assign(U,{setup(H){const v=A(),g=h(),m="product/",n=D(!1),a=E({}),k=async()=>{n.value=!0,await g.dispatch(m+"update").then(i=>{location.reload(),n.value=!1}).catch(i=>{n.value=!1})};return S(async()=>{n.value=!0,await g.dispatch(m+"info").then(i=>{Object.assign(a,i),n.value=!1}).catch(i=>{n.value=!1})}),(i,d)=>{const C=u("BaseButton"),P=u("PageHeaderAction"),V=u("PageHeader"),y=u("BaseAlert"),l=u("BaseDataView"),f=u("BaseBadge"),w=u("BaseCard"),x=u("ParentTransition");return r(),_(b,null,[s(V,{title:i.$trans(N(v).meta.title),navs:[]},{default:e(()=>[s(P,null,{default:e(()=>[a.isDownloaded?(r(),c(C,{key:0,design:"primary",onClick:k},{default:e(()=>d[0]||(d[0]=[t("Update")])),_:1})):p("",!0)]),_:1})]),_:1},8,["title"]),s(x,{appear:"",visibility:!0},{default:e(()=>[s(w,{"is-loading":n.value},{default:e(()=>[n.value?(r(),c(y,{key:0,design:"info",size:"xs"},{default:e(()=>[t(o(i.$trans("general.infos.loading")),1)]),_:1})):p("",!0),!n.value&&!a.name?(r(),c(y,{key:1,design:"error",size:"xs"},{default:e(()=>[t(o(i.$trans("general.errors.something_wrong_contact_author")),1)]),_:1})):p("",!0),a.name?(r(),_("dl",T,[s(l,{label:"Name"},{default:e(()=>[t(o(a.name),1)]),_:1}),s(l,{label:"Current Version"},{default:e(()=>[t(o(a.currentVersion),1)]),_:1}),s(l,{label:"Latest Version"},{default:e(()=>[t(o(a.latestVersion)+" ",1),a.isUpdateAvailable?(r(),c(f,{key:0,design:"success"},{default:e(()=>d[1]||(d[1]=[t("Update Available")])),_:1})):(r(),c(f,{key:1,design:"info"},{default:e(()=>d[2]||(d[2]=[t("No Update Available")])),_:1}))]),_:1}),s(l,{label:"Purchase Date"},{default:e(()=>[t(o(a.dateOfPurchase.formatted),1)]),_:1}),s(l,{label:"Date of Support Expiry"},{default:e(()=>{var B;return[a.isSupportExpired?(r(),c(f,{key:0,design:"error"},{default:e(()=>d[3]||(d[3]=[t("Support Expired")])),_:1})):(r(),_(b,{key:1},[t(o(((B=a.dateOfSupportExpiry)==null?void 0:B.formatted)||"-"),1)],64))]}),_:1}),s(l,{label:"License Type"},{default:e(()=>[t(o(a.licenseType),1)]),_:1}),s(l,{label:"Purchase Code"},{default:e(()=>[t(o(a.purchaseCode),1)]),_:1}),s(l,{label:"Access Code"},{default:e(()=>[t(o(a.accessCode),1)]),_:1}),s(l,{label:"Registered Email"},{default:e(()=>[t(o(a.email),1)]),_:1})])):p("",!0)]),_:1},8,["is-loading"])]),_:1})],64)}}});export{L as default};
