import{i as h,H as u,l as C,r as o,q as b,o as v,w as r,e as s,d,f}from"./app-BAwPsakn.js";const B={class:"grid grid-cols-3 gap-6"},V={class:"col-span-3 sm:col-span-1"},w={name:"ConfigChat"},S=Object.assign(w,{setup(F){h();const i="config/",c=u(i),l={enableChat:!1,type:"chat"},t=C({...l});return(a,e)=>{const m=o("CardHeader"),p=o("BaseSwitch"),_=o("FormAction"),g=o("ConfigPage");return v(),b(g,null,{action:r(()=>e[2]||(e[2]=[])),default:r(()=>[s(_,{"no-card":"","init-url":i,"data-fetch":"chat","init-form":l,form:t,action:"store","stay-on":"",redirect:"Config"},{default:r(()=>[s(m,{first:"",title:a.$trans("config.chat.chat_config"),description:a.$trans("config.chat.chat_info")},null,8,["title","description"]),d("div",B,[d("div",V,[s(p,{reverse:"",modelValue:t.enableChat,"onUpdate:modelValue":e[0]||(e[0]=n=>t.enableChat=n),name:"enableChat",label:a.$trans("config.chat.props.enable_chat"),error:f(c).enableChat,"onUpdate:error":e[1]||(e[1]=n=>f(c).enableChat=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})}}});export{S as default};
