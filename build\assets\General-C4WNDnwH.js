import{u as T,j as R,H as v,l as p,r as e,a as D,o as F,e as a,f as t,w as d,d as u,F as I}from"./app-BAwPsakn.js";const P={class:"grid grid-cols-3 gap-4"},B={class:"col-span-3 sm:col-span-1"},V={name:"TransportConfigGeneral"},x=Object.assign(V,{setup(j){const m=T(),o=R("$trans"),i="config/",l=v(i);p({});const c={showTransportRouteInDashboard:!1,type:"transport"},s=p({...c}),_=f=>{};return(f,r)=>{const h=e("PageHeader"),b=e("BaseSwitch"),g=e("FormAction"),w=e("ParentTransition");return F(),D(I,null,[a(h,{title:t(o)(t(m).meta.label),navs:[{label:t(o)("transport.transport"),path:"Transport"}]},null,8,["title","navs"]),a(w,{appear:"",visibility:!0},{default:d(()=>[a(g,{"pre-requisites":!1,onSetPreRequisites:_,"init-url":i,"data-fetch":"transport","init-form":c,form:s,action:"store","stay-on":"",redirect:"Transport"},{default:d(()=>[u("div",P,[u("div",B,[a(b,{vertical:"",modelValue:s.showTransportRouteInDashboard,"onUpdate:modelValue":r[0]||(r[0]=n=>s.showTransportRouteInDashboard=n),name:"showTransportRouteInDashboard",label:t(o)("global.show",{attribute:t(o)("transport.config.props.route_in_dashboard")}),error:t(l).showTransportRouteInDashboard,"onUpdate:error":r[1]||(r[1]=n=>t(l).showTransportRouteInDashboard=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{x as default};
