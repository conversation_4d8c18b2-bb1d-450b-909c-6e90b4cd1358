import{u as L,l as F,n as P,r as i,q as p,o as u,w as e,d as w,e as s,h as R,j as T,y as _,m as M,f as a,a as j,F as N,v as S,s as l,t as m,b as x}from"./app-BAwPsakn.js";const U={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(B,{emit:d}){L();const b=d,g={name:""},f=F({...g}),C=F({isLoaded:!0});return P(async()=>{C.isLoaded=!0}),(v,r)=>{const h=i("BaseInput"),n=i("FilterForm");return u(),p(n,{"init-form":g,form:f,multiple:[],onHide:r[1]||(r[1]=o=>b("hide"))},{default:e(()=>[w("div",U,[w("div",q,[s(h,{type:"text",modelValue:f.name,"onUpdate:modelValue":r[0]||(r[0]=o=>f.name=o),name:"name",label:v.$trans("exam.observation.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={name:"ExamObservationList"},K=Object.assign(G,{setup(B){const d=R(),b=T("emitter");let g=["filter"];_("observation:create")&&g.unshift("create");let f=[];_("observation:export")&&(f=["print","pdf","excel"]);const C="exam/observation/",v=M(!1),r=F({}),h=n=>{Object.assign(r,n)};return(n,o)=>{const D=i("PageHeaderAction"),I=i("PageHeader"),y=i("ParentTransition"),$=i("DataCell"),k=i("FloatingMenuItem"),E=i("FloatingMenu"),O=i("DataRow"),V=i("BaseButton"),A=i("DataTable"),H=i("ListItem");return u(),p(H,{"init-url":C,onSetItems:h},{header:e(()=>[s(I,{title:n.$trans("exam.observation.observation"),navs:[{label:n.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[s(D,{url:"exam/observations/",name:"ExamObservation",title:n.$trans("exam.observation.observation"),actions:a(g),"dropdown-actions":a(f),onToggleFilter:o[0]||(o[0]=t=>v.value=!v.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[s(y,{appear:"",visibility:v.value},{default:e(()=>[s(z,{onRefresh:o[1]||(o[1]=t=>a(b).emit("listItems")),onHide:o[2]||(o[2]=t=>v.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[s(y,{appear:"",visibility:!0},{default:e(()=>[s(A,{header:r.headers,meta:r.meta,module:"exam.observation",onRefresh:o[4]||(o[4]=t=>a(b).emit("listItems"))},{actionButton:e(()=>[a(_)("exam-observation:create")?(u(),p(V,{key:0,onClick:o[3]||(o[3]=t=>a(d).push({name:"ExamObservationCreate"}))},{default:e(()=>[l(m(n.$trans("global.add",{attribute:n.$trans("exam.observation.observation")})),1)]),_:1})):x("",!0)]),default:e(()=>[(u(!0),j(N,null,S(r.data,t=>(u(),p(O,{key:t.uuid,onDoubleClick:c=>a(d).push({name:"ExamObservationShow",params:{uuid:t.uuid}})},{default:e(()=>[s($,{name:"name"},{default:e(()=>[l(m(t.name),1)]),_:2},1024),s($,{name:"grade"},{default:e(()=>{var c;return[l(m(((c=t.grade)==null?void 0:c.name)||"-"),1)]}),_:2},1024),s($,{name:"createdAt"},{default:e(()=>[l(m(t.createdAt.formatted),1)]),_:2},1024),s($,{name:"action"},{default:e(()=>[s(E,null,{default:e(()=>[s(k,{icon:"fas fa-arrow-circle-right",onClick:c=>a(d).push({name:"ExamObservationShow",params:{uuid:t.uuid}})},{default:e(()=>[l(m(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(_)("exam-observation:edit")?(u(),p(k,{key:0,icon:"fas fa-edit",onClick:c=>a(d).push({name:"ExamObservationEdit",params:{uuid:t.uuid}})},{default:e(()=>[l(m(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):x("",!0),a(_)("exam-observation:create")?(u(),p(k,{key:1,icon:"fas fa-copy",onClick:c=>a(d).push({name:"ExamObservationDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[l(m(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):x("",!0),a(_)("exam-observation:delete")?(u(),p(k,{key:2,icon:"fas fa-trash",onClick:c=>a(b).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[l(m(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):x("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
