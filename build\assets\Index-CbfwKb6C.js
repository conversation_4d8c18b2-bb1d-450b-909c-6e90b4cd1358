import{u as H,i as L,m as S,H as P,l as F,n as M,K as j,r as y,q as T,o as c,w as u,a as x,b as g,d as V,e as p,f as o,s as $,t as i,h as A,y as K,F as W}from"./app-BAwPsakn.js";const z={key:0,class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},I={key:0,class:"col-span-3 sm:col-span-1"},J={class:"ml-1"},Q={class:"ml-1"},X={key:1,class:"col-span-3 sm:col-span-1"},Y={key:0,class:"ml-1"},Z={key:0,class:"ml-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ne={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(q,{emit:D}){const r=H();L();const B=D,v=q,k={type:"",term:"",exam:"",attempt:"",batch:"",students:[],resultDate:"",template:""};S(!1),S(!1);const s=P(v.initUrl),a=F({...k}),b=F({selectedType:"",exams:v.preRequisites.exams,students:[]}),m=F({exam:"",batch:"",students:[],isLoaded:!(r.query.exam||r.query.batch||r.query.students)}),l=async n=>{b.students=[],a.students=[],m.students=[]},f=()=>{b.students=[],a.students=[],m.students=[]},w=n=>{b.selectedType=v.preRequisites.types.find(t=>t.value===n)};return M(async()=>{if(_.isEmpty(r.query)){m.isLoaded=!0;return}a.type=r.query.type,m.exam=r.query.exam,a.exam=r.query.exam,a.attempt=r.query.attempt,m.batch=r.query.batch,a.batch=r.query.batch,r.query.batch&&(await l({uuid:r.query.batch}),a.students=r.query.students?r.query.students.split(","):[]),m.isLoaded=!0}),j(v.preRequisites,n=>{n&&(b.selectedType=n.types.find(t=>t.value===r.query.type))},{deep:!0}),(n,t)=>{const h=y("BaseSelect"),E=y("BaseSelectSearch"),R=y("DatePicker"),N=y("FilterForm");return c(),T(N,{"init-form":k,multiple:["students"],form:a,"filter-text":n.$trans("general.process"),onHide:t[14]||(t[14]=O=>B("hide"))},{default:u(()=>{var O,C;return[m.isLoaded?(c(),x("div",z,[V("div",G,[p(h,{modelValue:a.type,"onUpdate:modelValue":t[0]||(t[0]=e=>a.type=e),name:"type",label:n.$trans("exam.marksheet.type"),options:q.preRequisites.types,error:o(s).type,"onUpdate:error":t[1]||(t[1]=e=>o(s).type=e),onChange:w},null,8,["modelValue","label","options","error"])]),(O=b.selectedType)!=null&&O.requiresTerm?(c(),x("div",I,[p(h,{modelValue:a.term,"onUpdate:modelValue":t[2]||(t[2]=e=>a.term=e),name:"term",label:n.$trans("exam.term.term"),options:q.preRequisites.terms,"value-prop":"uuid",error:o(s).term,"onUpdate:error":t[3]||(t[3]=e=>o(s).term=e)},{selectedOption:u(e=>{var d;return[$(i(e.value.name)+" ",1),V("span",J,"("+i(((d=e.value.division)==null?void 0:d.name)||n.$trans("general.all"))+")",1)]}),listOption:u(e=>{var d;return[$(i(e.option.name)+" ",1),V("span",Q,"("+i(((d=e.option.division)==null?void 0:d.name)||n.$trans("general.all"))+")",1)]}),_:1},8,["modelValue","label","options","error"])])):g("",!0),(C=b.selectedType)!=null&&C.requiresExam?(c(),x("div",X,[p(h,{modelValue:a.exam,"onUpdate:modelValue":t[4]||(t[4]=e=>a.exam=e),name:"exam",label:n.$trans("exam.exam"),"value-prop":"uuid",options:q.preRequisites.exams,error:o(s).exam,"onUpdate:error":t[5]||(t[5]=e=>o(s).exam=e)},{selectedOption:u(e=>{var d,U;return[$(i(e.value.name)+" ",1),e.value.term?(c(),x("span",Y,"("+i(((U=(d=e.value.term)==null?void 0:d.division)==null?void 0:U.name)||n.$trans("general.all"))+")",1)):g("",!0)]}),listOption:u(e=>{var d,U;return[$(i(e.option.name)+" ",1),e.option.term?(c(),x("span",Z,"("+i(((U=(d=e.option.term)==null?void 0:d.division)==null?void 0:U.name)||n.$trans("general.all"))+")",1)):g("",!0)]}),_:1},8,["modelValue","label","options","error"])])):g("",!0),V("div",ee,[m.isLoaded?(c(),T(h,{key:0,modelValue:a.attempt,"onUpdate:modelValue":t[6]||(t[6]=e=>a.attempt=e),name:"attempt",label:n.$trans("exam.schedule.props.attempt"),options:q.preRequisites.attempts,error:o(s).attempt,"onUpdate:error":t[7]||(t[7]=e=>o(s).attempt=e)},null,8,["modelValue","label","options","error"])):g("",!0)]),V("div",te,[m.isLoaded?(c(),T(E,{key:0,name:"batch",label:n.$trans("global.select",{attribute:n.$trans("academic.batch.batch")}),modelValue:a.batch,"onUpdate:modelValue":t[8]||(t[8]=e=>a.batch=e),error:o(s).batch,"onUpdate:error":t[9]||(t[9]=e=>o(s).batch=e),"value-prop":"uuid","init-search":m.batch,"search-key":"course_batch","search-action":"academic/batch/list",onSelected:l,onRemoved:f},{selectedOption:u(e=>[$(i(e.value.course.name)+" "+i(e.value.name),1)]),listOption:u(e=>[$(i(e.option.course.nameWithTerm)+" "+i(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):g("",!0)]),V("div",ae,[p(R,{modelValue:a.resultDate,"onUpdate:modelValue":t[10]||(t[10]=e=>a.resultDate=e),name:"resultDate",label:n.$trans("exam.result_date"),"no-clear":"",error:o(s).resultDate,"onUpdate:error":t[11]||(t[11]=e=>o(s).resultDate=e)},null,8,["modelValue","label","error"])]),V("div",se,[p(h,{modelValue:a.template,"onUpdate:modelValue":t[12]||(t[12]=e=>a.template=e),name:"template",label:n.$trans("exam.marksheet.template"),options:q.preRequisites.templates,error:o(s).template,"onUpdate:error":t[13]||(t[13]=e=>o(s).template=e)},null,8,["modelValue","label","options","error"])])])):g("",!0)]}),_:1},8,["form","filter-text"])}}},re={name:"ExamMarksheetProcess"},le=Object.assign(re,{setup(q){const D=H(),r=A(),B=L(),v="exam/marksheet/",k=S(!0),s=S(!1),a=F({types:[],terms:[],exams:[],attempts:[]}),b=async()=>{s.value=!0,await B.dispatch(v+"processPreRequisite").then(l=>{s.value=!1,Object.assign(a,l)}).catch(l=>{s.value=!1})},m=async()=>{s.value=!0,await B.dispatch(v+"process",{params:D.query}).then(l=>{s.value=!1}).catch(l=>{s.value=!1})};return M(async()=>{await b()}),(l,f)=>{const w=y("BaseButton"),n=y("PageHeaderAction"),t=y("PageHeader"),h=y("ParentTransition"),E=y("BaseCard");return c(),x(W,null,[p(t,{title:l.$trans("global.process",{attribute:l.$trans("exam.marksheet.marksheet")}),navs:[{label:l.$trans("exam.exam"),path:"Exam"}]},{default:u(()=>[p(n,{name:"ExamMarksheetProcess",title:l.$trans("exam.marksheet.marksheet"),actions:[],"dropdown-actions":[],onToggleFilter:f[1]||(f[1]=R=>k.value=!k.value)},{default:u(()=>[o(K)("exam-marksheet:access")?(c(),T(w,{key:0,design:"white",onClick:f[0]||(f[0]=R=>o(r).push({name:"ExamMarksheetPrint"}))},{default:u(()=>[$(i(l.$trans("global.print",{attribute:l.$trans("exam.marksheet.marksheet")})),1)]),_:1})):g("",!0)]),_:1},8,["title"])]),_:1},8,["title","navs"]),p(h,{appear:"",visibility:k.value},{default:u(()=>[p(ne,{"is-loading":s.value,onAfterFilter:m,"init-url":v,"pre-requisites":a,onHide:f[2]||(f[2]=R=>k.value=!1)},null,8,["is-loading","pre-requisites"])]),_:1},8,["visibility"]),p(h,{appear:"",visibility:!0},{default:u(()=>[p(E,{"no-padding":"","no-content-padding":"","is-loading":s.value},null,8,["is-loading"])]),_:1})],64)}}});export{le as default};
