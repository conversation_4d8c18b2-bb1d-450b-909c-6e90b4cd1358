import{u as T,i as $,j as C,y as P,c as E,g as F,m as I,r,a as _,o as n,q as c,b,e as o,w as e,f as a,s,t as g,d as w,F as v,v as V}from"./app-BAwPsakn.js";const O={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},q={class:"space-x-1"},z={class:"flex flex-wrap gap-2"},R={name:"GuardianShowLogin"},K=Object.assign(R,{props:{guardian:{type:Object,default(){return{}}}},setup(p){const A=T(),j=$(),t=C("$trans"),G=C("emitter"),f=p;let x=[];P("guardian:edit")&&x.push({label:t("global.edit",{attribute:t("contact.login.login")}),path:{name:"GuardianEditLogin",params:{uuid:f.guardian.uuid}}});const l=E(()=>{var d,i;return(i=(d=f.guardian)==null?void 0:d.contact)==null?void 0:i.user}),H=F("periods"),y=I(!1),L=d=>{var i;d.id!=((i=l.value)==null?void 0:i.currentPeriodId)&&(y.value=!0,j.dispatch("guardian/updateCurrentPeriod",{uuid:f.guardian.uuid,form:{period_id:d.id}}).then(()=>{G.emit("guardianUpdated")}).catch(()=>{}).finally(()=>{y.value=!1}))};return(d,i)=>{const N=r("PageHeaderAction"),S=r("PageHeader"),D=r("BaseAlert"),m=r("BaseDataView"),B=r("BaseBadge"),h=r("BaseCard"),k=r("ParentTransition");return n(),_(v,null,[p.guardian.uuid?(n(),c(S,{key:0,title:a(t)(a(A).meta.label),navs:[{label:a(t)("guardian.guardian"),path:"Guardian"},{label:p.guardian.contact.name,path:{name:"GuardianShow",params:{uuid:p.guardian.uuid}}}]},{default:e(()=>[o(N,{"additional-actions":a(x)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):b("",!0),o(k,{appear:"",visibility:!0},{default:e(()=>[p.guardian.uuid?(n(),c(h,{key:0},{default:e(()=>[l.value?(n(),_("dl",O,[o(m,{label:a(t)("contact.login.props.email")},{default:e(()=>[s(g(l.value.email),1)]),_:1},8,["label"]),o(m,{label:a(t)("contact.login.props.username")},{default:e(()=>[s(g(l.value.username),1)]),_:1},8,["label"]),o(m,{label:a(t)("contact.login.props.password")},{default:e(()=>i[0]||(i[0]=[s(" xxxxxxxxx ")])),_:1},8,["label"]),o(m,{label:a(t)("team.config.role.role")},{default:e(()=>[w("div",q,[(n(!0),_(v,null,V(l.value.roles,u=>(n(),c(B,{design:"primary"},{default:e(()=>[s(g(u.label),1)]),_:2},1024))),256))])]),_:1},8,["label"])])):(n(),c(D,{key:0,design:"error"},{default:e(()=>[s(g(a(t)("contact.login.no_login_found")),1)]),_:1}))]),_:1})):b("",!0)]),_:1}),a(P)("guardian:edit")?(n(),c(k,{key:1,appear:"",visibility:!0},{default:e(()=>[o(h,null,{title:e(()=>[s(g(a(t)("global.update",{attribute:a(t)("academic.period.current_period")})),1)]),default:e(()=>[w("div",z,[(n(!0),_(v,null,V(a(H),u=>(n(),c(B,{key:u.id,size:"md",design:u.id==l.value.currentPeriodId?"success":"info",class:"cursor-pointer",onClick:U=>L(u)},{default:e(()=>[s(g(u.name),1)]),_:2},1032,["design","onClick"]))),128))])]),_:1})]),_:1})):b("",!0)],64)}}});export{K as default};
